<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549590294530" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Accessory:5绕组母线PT带避雷器_0" viewBox="0,0,40,35">
   <use terminal-index="0" type="0" x="22.10905664884498" xlink:href="#terminal" y="34.51612485684674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.91666666666667" x2="34.5" y1="28.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="21.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="22" y1="28.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.46666666666667" x2="35.46666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.46666666666667" x2="33.46666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.13333333333333" x2="18.13333333333333" y1="19.27740325661302" y2="15.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.13333333333333" x2="20.13333333333333" y1="19.27740325661302" y2="18.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.13333333333333" x2="20.13333333333333" y1="15.27740325661302" y2="16.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.5" x2="34.5" y1="28.5" y2="14.5"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,34.5,14.36) scale(1,-1) translate(0,-926.43)" width="7" x="31" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.42943360505483" x2="34.42943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="11.81" cy="17.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="36.45921744067709" x2="32.18240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.62588410734375" x2="33.01573492932658" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.04255077401042" x2="33.59906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.78240159599324" x2="9.382401595993244" y1="17.45662962336982" y2="18.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.78240159599325" x2="14.18240159599324" y1="17.45662962336982" y2="18.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.78240159599324" x2="11.78240159599324" y1="14.98154965466559" y2="17.4566296233698"/>
   <ellipse cx="4.73" cy="17.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.98" cy="10.41" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.949068262659907" x2="5.549068262659912" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.949068262659912" x2="10.34906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.69906826265991" x2="4.69906826265991" y1="15.23154965466559" y2="17.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.699068262659912" x2="7.099068262659907" y1="17.70662962336982" y2="18.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.94906826265991" x2="7.94906826265991" y1="8.148216321332258" y2="10.62329629003646"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.699068262659907" x2="2.299068262659912" y1="17.70662962336982" y2="18.94416960772192"/>
   <ellipse cx="18.81" cy="17.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.48" cy="10.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.44906826265991" x2="17.84906826265991" y1="10.53996295670315" y2="11.77750294105526"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.4490682626599" x2="13.04906826265991" y1="10.53996295670315" y2="11.77750294105526"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.44906826265991" x2="15.44906826265991" y1="8.064882987998928" y2="10.53996295670313"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="PowerTransformer2:接地可调两卷变_0" viewBox="0,0,22,30">
   <use terminal-index="0" type="1" x="11.00731595793324" xlink:href="#terminal" y="1.08736282578875"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.02194787379973" x2="6.955871323769093" y1="7.640146319158664" y2="3.944602328551218"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.23333333333333" x2="11.01463191586648" y1="3.694602328551216" y2="7.640146319158664"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.00935070873343" x2="11.00935070873343" y1="7.654778235025146" y2="11.68728637061797"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="21.53783721993598" x2="20.53783721993598" y1="2.175582990397805" y2="5.175582990397805"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.5394375857338822" x2="21.53943758573388" y1="13.09064929126657" y2="2.173982624599901"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="18.53052126200274" x2="21.53052126200274" y1="1.175582990397805" y2="2.175582990397805"/>
   <ellipse cx="11.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="11" xlink:href="#terminal" y="7.7"/>
  </symbol>
  <symbol id="PowerTransformer2:接地可调两卷变_1" viewBox="0,0,22,30">
   <use terminal-index="1" type="1" x="11" xlink:href="#terminal" y="29.05121170553269"/>
   <path d="M 6.75 25.8333 L 15.8333 25.8333 L 11.0833 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Compensator:无功补偿20210816_0" viewBox="0,0,12,13">
   <use terminal-index="0" type="0" x="6.1" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.4" x2="4" y1="3.141666666666663" y2="3.141666666666663"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.141666666666663" x2="6.141666666666663" y1="0.04999999999999893" y2="3.141666666666661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.141666666666663" x2="6.141666666666663" y1="5.15" y2="8.09166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.629535048371164" x2="2.363367518766924" y1="11.72804518360739" y2="7.956509060518093"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.02953748863437" x2="7.38155138676536" y1="8.002747606646338" y2="11.51674385085443"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.116482967203315" x2="3.466407395865956" y1="8.199949647924862" y2="9.792275696188446"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.4" x2="4" y1="5.141666666666663" y2="5.141666666666663"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.201323776119781" x2="8.670438561349325" y1="7.945721353591398" y2="9.806332800169811"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.915200446966938" x2="0.6490329173626979" y1="12.7581213334275" y2="8.986585210338204"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.62680850872896" x2="8.978822406859944" y1="9.206377652950431" y2="12.72037389715852"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="EnergyConsumer:Y-Y站用_0" viewBox="0,0,17,26">
   <use terminal-index="0" type="0" x="8.666666666666666" xlink:href="#terminal" y="0.1666666666666714"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.597025948103791" x2="8.597025948103791" y1="1.79293756914176" y2="0.2382945839350814"/>
   <ellipse cx="8.42" cy="8.35" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.5" cy="18.92" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.611718118722354" x2="8.611718118722354" y1="16.35874008086165" y2="18.95772818087859"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.2052656081938" x2="8.611718118722351" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.018170629250881" x2="8.611718118722335" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.611718118722354" x2="8.611718118722354" y1="5.503560677018262" y2="8.102548777035198"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.2052656081938" x2="8.611718118722351" y1="10.70153687705211" y2="8.102548777035178"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.018170629250881" x2="8.611718118722335" y1="10.70153687705211" y2="8.102548777035178"/>
  </symbol>
  <symbol id="Accessory:熔断器_0" viewBox="0,0,10,18">
   <use terminal-index="0" type="0" x="5.016666666666667" xlink:href="#terminal" y="1.083333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1" y2="17"/>
   <rect fill-opacity="0" height="16.08" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.06,9.06) scale(1,1) translate(0,0)" width="9.58" x="0.27" y="1.02"/>
  </symbol>
  <symbol id="EnergyConsumer:Y-d厂用变_0" viewBox="0,0,20,35">
   <use terminal-index="0" type="0" x="10.16666666666667" xlink:href="#terminal" y="1.166666666666671"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="13" y1="15.25" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="7" y1="20.25" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="34.5" y2="31.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="24.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="7" y1="15.25" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="9" y1="34.5" y2="31.5"/>
   <ellipse cx="9.92" cy="7.6" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10" cy="18.17" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.11171811872235" x2="10.11171811872235" y1="4.753560677018264" y2="7.352548777035198"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.7052656081938" x2="10.11171811872235" y1="9.951536877052112" y2="7.352548777035178"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.518170629250881" x2="10.11171811872233" y1="9.951536877052112" y2="7.352548777035178"/>
  </symbol>
  <symbol id=":110kV线路带PT避雷器_0" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="22.5" xlink:href="#terminal" y="44.60000000000001"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="33" y1="29.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="16" y1="40.5" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="27.41094883543335" x2="32.88823024054981" y1="18.42541949757221" y2="18.42541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.66022336769755" x2="22.66022336769755" y1="44.33333333333334" y2="0.2499999999999964"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="13.5" y1="37.5" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36" x2="36" y1="31.25" y2="36.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36" x2="39" y1="31.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="15" y1="41.5" y2="41.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="14" y1="42.5" y2="42.5"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.54,30.42) scale(1,1) translate(0,0)" width="6.08" x="10.5" y="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36" x2="39" y1="36.25" y2="34.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.96058610156545" x2="22.65436235204273" y1="18.42541949757221" y2="18.42541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="12.5" y1="31.25" y2="25.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="14.5" y1="31.25" y2="25.25"/>
   <path d="M 22.5 18.5 L 13.5 18.5 L 13.5 30.5 L 13.5 30.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="30" y1="26.25" y2="29.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.00545055364641" x2="30.00545055364641" y1="18.54138864447597" y2="24.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="27" y1="29.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.96383161512026" x2="39.91666666666666" y1="18.49758812251703" y2="18.49758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.85940244368076" x2="32.85940244368076" y1="18.49758812251703" y2="18.49758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="39.86598892707138" x2="39.86598892707138" y1="15.25" y2="21.74517624503405"/>
   <ellipse cx="30.01" cy="29.32" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="37.05" cy="33.58" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="30.26" cy="37.58" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="32.85940244368077" x2="32.85940244368077" y1="15.98325293741726" y2="21.08409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="41.01910080183274" x2="41.01910080183274" y1="16.51295093653441" y2="20.84306843322376"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="41.88393470790376" x2="41.88393470790376" y1="17.41505874834466" y2="19.39969593432727"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="25.07589728904158" x2="25.07589728904158" y1="18.49758812251703" y2="18.49758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="24.99256395570825" x2="24.99256395570825" y1="15.83333333333333" y2="20.93417232847779"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="35.02148720885832" x2="35.02148720885832" y1="15.98325293741726" y2="21.08409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="27.23798205421914" x2="27.23798205421914" y1="15.98325293741726" y2="21.08409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.08333333333334" x2="33.08333333333334" y1="38" y2="40"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.08333333333334" x2="27.08333333333334" y1="38" y2="40"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.08333333333334" x2="30.08333333333334" y1="35" y2="38"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV海西硅厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="66.95999999999999" xlink:href="logo.png" y="45.64"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.589,75.6429) scale(1,1) translate(0,0)" writing-mode="lr" x="201.59" xml:space="preserve" y="79.14" zvalue="10493"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,203.298,75.3332) scale(1,1) translate(9.43293e-15,0)" writing-mode="lr" x="203.3" xml:space="preserve" y="84.33" zvalue="10494">110kV海西硅厂</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="16" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" width="72.88" x="71.94" y="327" zvalue="10496"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" writing-mode="lr" x="108.38" xml:space="preserve" y="343.5" zvalue="10496">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,570.076,432.091) scale(1,1) translate(0,0)" writing-mode="lr" x="570.08" xml:space="preserve" y="436.59" zvalue="7577">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,898.98,397.52) scale(1,1) translate(-1.96172e-13,0)" writing-mode="lr" x="898.98" xml:space="preserve" y="402.02" zvalue="8016">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,969.607,369.394) scale(1,1) translate(1.06815e-13,0)" writing-mode="lr" x="969.61" xml:space="preserve" y="373.89" zvalue="8030">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,975.607,421.644) scale(1,1) translate(0,0)" writing-mode="lr" x="975.61" xml:space="preserve" y="426.14" zvalue="8035">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,694.155,365.645) scale(1,1) translate(0,0)" writing-mode="lr" x="694.15" xml:space="preserve" y="370.14" zvalue="9968">151</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,637.526,234.63) scale(1,1) translate(0,0)" writing-mode="lr" x="637.53" xml:space="preserve" y="239.13" zvalue="9972">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,693.425,420.549) scale(1,1) translate(0,0)" writing-mode="lr" x="693.42" xml:space="preserve" y="425.05" zvalue="9975">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,638.276,322.065) scale(1,1) translate(0,0)" writing-mode="lr" x="638.28" xml:space="preserve" y="326.56" zvalue="10028">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,692.925,272.088) scale(1,1) translate(0,-3.49171e-13)" writing-mode="lr" x="692.92" xml:space="preserve" y="276.59" zvalue="10032">6</text>
  <line fill="none" id="174" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="152.8704926140824" y2="152.8704926140824" zvalue="10076"/>
  <line fill="none" id="173" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="380" x2="380" y1="11" y2="1041" zvalue="10077"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="260.75" y2="283.5"/>
  <line fill="none" id="171" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="622.8704926140824" y2="622.8704926140824" zvalue="10079"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1005.0816" y2="1033"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,958) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="964" zvalue="10083">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,992) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="998" zvalue="10084">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237,992) scale(1,1) translate(0,0)" writing-mode="lr" x="237" xml:space="preserve" y="998" zvalue="10085">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1026" zvalue="10086">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="1026" zvalue="10087">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5,652.5) scale(1,1) translate(0,0)" writing-mode="lr" x="78.5" xml:space="preserve" y="657" zvalue="10090">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,212.399,338.071) scale(1,1) translate(0,0)" writing-mode="lr" x="212.4" xml:space="preserve" y="342.57" zvalue="10091">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,317.399,338.071) scale(1,1) translate(0,0)" writing-mode="lr" x="317.4" xml:space="preserve" y="342.57" zvalue="10092">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.054,960) scale(1,1) translate(0,0)" writing-mode="lr" x="237.05" xml:space="preserve" y="966" zvalue="10100">HaiXi-01-2012</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52,179) scale(1,1) translate(0,0)" writing-mode="lr" x="52" xml:space="preserve" y="183.5" zvalue="10103">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232,179) scale(1,1) translate(0,0)" writing-mode="lr" x="232" xml:space="preserve" y="183.5" zvalue="10104">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,203.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="207.75" zvalue="10105">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.1875,251.409) scale(1,1) translate(0,0)" writing-mode="lr" x="54.19" xml:space="preserve" y="255.91" zvalue="10107">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.1875,272.591) scale(1,1) translate(0,-3.49841e-13)" writing-mode="lr" x="54.19" xml:space="preserve" y="277.09" zvalue="10109">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.1875,227.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.19" xml:space="preserve" y="231.75" zvalue="10110">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,666.08,150.875) scale(1,1) translate(0,0)" writing-mode="lr" x="666.08" xml:space="preserve" y="155.38" zvalue="10140">110kV傣海T线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,644.196,414.545) scale(1,1) translate(0,0)" writing-mode="lr" x="644.2" xml:space="preserve" y="419.05" zvalue="10259">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,979.155,535.821) scale(1,1) translate(1.07431e-13,0)" writing-mode="lr" x="979.15" xml:space="preserve" y="540.3200000000001" zvalue="10435">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,970.425,484.917) scale(1,1) translate(0,0)" writing-mode="lr" x="970.42" xml:space="preserve" y="489.42" zvalue="10442">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,904.196,502.92) scale(1,1) translate(0,0)" writing-mode="lr" x="904.2" xml:space="preserve" y="507.42" zvalue="10458">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" x="1022.625" xml:space="preserve" y="704.140625" zvalue="10504">#1炉变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1022.625" xml:space="preserve" y="720.140625" zvalue="10504">12500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,848,726) scale(1,1) translate(0,0)" writing-mode="lr" x="848" xml:space="preserve" y="730.5" zvalue="10506">1010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,951.735,905.5) scale(1,1) translate(0,0)" writing-mode="lr" x="951.73" xml:space="preserve" y="910" zvalue="10508">#1电炉</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" x="1020.75" xml:space="preserve" y="897.25" zvalue="10510">#1电容器组</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1020.75" xml:space="preserve" y="913.25" zvalue="10510">5MVar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,899.418,565.16) scale(1,1) translate(0,0)" writing-mode="lr" x="899.42" xml:space="preserve" y="569.66" zvalue="10540">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,972.388,588.498) scale(1,1) translate(0,0)" writing-mode="lr" x="972.39" xml:space="preserve" y="593" zvalue="10545">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,900.529,619.382) scale(1,1) translate(0,0)" writing-mode="lr" x="900.53" xml:space="preserve" y="623.88" zvalue="10549">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1836.78,524.091) scale(1,1) translate(0,0)" writing-mode="lr" x="1836.78" xml:space="preserve" y="528.59" zvalue="10556">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1428.11,473.02) scale(1,1) translate(0,0)" writing-mode="lr" x="1428.11" xml:space="preserve" y="477.52" zvalue="10557">351</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1408.28,254.194) scale(1,1) translate(0,0)" writing-mode="lr" x="1408.28" xml:space="preserve" y="258.69" zvalue="10559">35kV槟巨线海西T线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1355.97,438.212) scale(1,1) translate(0,0)" writing-mode="lr" x="1355.97" xml:space="preserve" y="442.71" zvalue="10563">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1394.1,396.421) scale(1,1) translate(0,0)" writing-mode="lr" x="1394.1" xml:space="preserve" y="400.92" zvalue="10564">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" x="1588.6875" xml:space="preserve" y="891.572914229499" zvalue="10568">#1环保变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1588.6875" xml:space="preserve" y="907.572914229499" zvalue="10568">1250kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1561.13,701.977) scale(1,1) translate(0,0)" writing-mode="lr" x="1561.13" xml:space="preserve" y="706.48" zvalue="10571">3016</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="189" stroke="rgb(255,255,255)" text-anchor="middle" x="1764.796875" xml:space="preserve" y="891.572914229499" zvalue="10579">#2环保变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="189" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1764.796875" xml:space="preserve" y="907.572914229499" zvalue="10579">1250kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1737.13,701.977) scale(1,1) translate(0,0)" writing-mode="lr" x="1737.13" xml:space="preserve" y="706.48" zvalue="10581">3026</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,675.155,535.821) scale(1,1) translate(0,0)" writing-mode="lr" x="675.15" xml:space="preserve" y="540.3200000000001" zvalue="10592">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,666.425,484.917) scale(1,1) translate(0,0)" writing-mode="lr" x="666.42" xml:space="preserve" y="489.42" zvalue="10594">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,600.196,502.92) scale(1,1) translate(0,0)" writing-mode="lr" x="600.2" xml:space="preserve" y="507.42" zvalue="10598">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" x="718.640625" xml:space="preserve" y="704.140625" zvalue="10601">#2炉变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="718.640625" xml:space="preserve" y="720.140625" zvalue="10601">12500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,544,726) scale(1,1) translate(0,0)" writing-mode="lr" x="544" xml:space="preserve" y="730.5" zvalue="10603">1020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,647.735,905.5) scale(1,1) translate(0,0)" writing-mode="lr" x="647.73" xml:space="preserve" y="910" zvalue="10605">#2电炉</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" x="716.75" xml:space="preserve" y="897.25" zvalue="10608">#2电容器组</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="716.75" xml:space="preserve" y="913.25" zvalue="10608">5MVar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,595.418,565.16) scale(1,1) translate(0,0)" writing-mode="lr" x="595.42" xml:space="preserve" y="569.66" zvalue="10611">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,668.388,588.498) scale(1,1) translate(0,0)" writing-mode="lr" x="668.39" xml:space="preserve" y="593" zvalue="10616">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,596.529,619.382) scale(1,1) translate(0,0)" writing-mode="lr" x="596.53" xml:space="preserve" y="623.88" zvalue="10620">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1627.42,728.861) scale(1,1) translate(0,0)" writing-mode="lr" x="1627.42" xml:space="preserve" y="733.36" zvalue="10633">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1799.42,727.861) scale(1,1) translate(0,0)" writing-mode="lr" x="1799.42" xml:space="preserve" y="732.36" zvalue="10635">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1658.63,567.977) scale(1,1) translate(0,0)" writing-mode="lr" x="1658.63" xml:space="preserve" y="572.48" zvalue="10639">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1702.42,592.861) scale(1,1) translate(0,0)" writing-mode="lr" x="1702.42" xml:space="preserve" y="597.36" zvalue="10643">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="206" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1355,788) scale(1,1) translate(0,0)" writing-mode="lr" x="1355" xml:space="preserve" y="792.5" zvalue="10645">厂用变200kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1327.07,588.977) scale(1,1) translate(0,0)" writing-mode="lr" x="1327.07" xml:space="preserve" y="593.48" zvalue="10648">3021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1378.42,615.861) scale(1,1) translate(0,0)" writing-mode="lr" x="1378.42" xml:space="preserve" y="620.36" zvalue="10651">17</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="71.94" y="327" zvalue="10496"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="48">
   <path class="kv110" d="M 538 449.09 L 1059.11 449.09" stroke-width="6" zvalue="7576"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674249932804" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674249932804"/></metadata>
  <path d="M 538 449.09 L 1059.11 449.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv35" d="M 1282 525.09 L 1803.11 525.09" stroke-width="6" zvalue="10555"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674249998340" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674249998340"/></metadata>
  <path d="M 1282 525.09 L 1803.11 525.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="1453">
   <use class="kv110" height="30" transform="rotate(180,918.01,396.828) scale(0.947693,-0.6712) translate(50.2765,-992.981)" width="15" x="910.9024010422277" xlink:href="#Disconnector:刀闸_0" y="386.7597602301333" zvalue="8015"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449926266886" ObjectName="110kV母线PT1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449926266886"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,918.01,396.828) scale(0.947693,-0.6712) translate(50.2765,-992.981)" width="15" x="910.9024010422277" y="386.7597602301333"/></g>
  <g id="96">
   <use class="kv110" height="30" transform="rotate(180,668.392,419.857) scale(0.947693,-0.6712) translate(36.499,-1050.32)" width="15" x="661.2841334259955" xlink:href="#Disconnector:刀闸_0" y="409.7885305077181" zvalue="9974"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449925545989" ObjectName="110kV傣海T线1511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449925545989"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,668.392,419.857) scale(0.947693,-0.6712) translate(36.499,-1050.32)" width="15" x="661.2841334259955" y="409.7885305077181"/></g>
  <g id="32">
   <use class="kv110" height="30" transform="rotate(0,668.211,278.088) scale(-0.947693,0.6712) translate(-1373.7,131.294)" width="15" x="661.103492892157" xlink:href="#Disconnector:刀闸_0" y="268.0197927208583" zvalue="10031"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449925873670" ObjectName="110kV傣海T线1516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449925873670"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,668.211,278.088) scale(-0.947693,0.6712) translate(-1373.7,131.294)" width="15" x="661.103492892157" y="268.0197927208583"/></g>
  <g id="53">
   <use class="kv110" height="30" transform="rotate(180,951.392,480.609) scale(0.947693,0.6712) translate(52.1189,230.503)" width="15" x="944.2841334259955" xlink:href="#Disconnector:刀闸_0" y="470.5413658803764" zvalue="10441"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449926660102" ObjectName="#1炉变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449926660102"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,951.392,480.609) scale(0.947693,0.6712) translate(52.1189,230.503)" width="15" x="944.2841334259955" y="470.5413658803764"/></g>
  <g id="13">
   <use class="kv110" height="30" transform="rotate(180,951.392,589.498) scale(0.947693,0.6712) translate(52.1189,283.844)" width="15" x="944.2841334259955" xlink:href="#Disconnector:刀闸_0" y="579.4302547692652" zvalue="10544"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449927184390" ObjectName="#1炉变110kV侧1016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449927184390"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,951.392,589.498) scale(0.947693,0.6712) translate(52.1189,283.844)" width="15" x="944.2841334259955" y="579.4302547692652"/></g>
  <g id="79">
   <use class="kv35" height="30" transform="rotate(0,1407.1,397.421) scale(-0.947693,0.6712) translate(-2892.26,189.752)" width="15" x="1399.992381781046" xlink:href="#Disconnector:刀闸_0" y="387.3531260541916" zvalue="10563"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449927512070" ObjectName="35kV槟巨线海西T线3516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449927512070"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1407.1,397.421) scale(-0.947693,0.6712) translate(-2892.26,189.752)" width="15" x="1399.992381781046" y="387.3531260541916"/></g>
  <g id="136">
   <use class="kv35" height="30" transform="rotate(0,1589.23,702.977) scale(0.947693,0.6712) translate(87.3241,339.434)" width="15" x="1582.12690638291" xlink:href="#Disconnector:刀闸_0" y="692.9086816097472" zvalue="10570"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449927774214" ObjectName="#1环保变3016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449927774214"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1589.23,702.977) scale(0.947693,0.6712) translate(87.3241,339.434)" width="15" x="1582.12690638291" y="692.9086816097472"/></g>
  <g id="194">
   <use class="kv35" height="30" transform="rotate(0,1765.23,702.977) scale(0.947693,0.6712) translate(97.0383,339.434)" width="15" x="1758.12690638291" xlink:href="#Disconnector:刀闸_0" y="692.9086816097472" zvalue="10580"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449927839750" ObjectName="#2环保变3026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449927839750"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1765.23,702.977) scale(0.947693,0.6712) translate(97.0383,339.434)" width="15" x="1758.12690638291" y="692.9086816097472"/></g>
  <g id="155">
   <use class="kv110" height="30" transform="rotate(180,647.392,480.609) scale(0.947693,0.6712) translate(35.3399,230.503)" width="15" x="640.2841334259955" xlink:href="#Disconnector:刀闸_0" y="470.5413658803764" zvalue="10593"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449928822790" ObjectName="#2炉变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449928822790"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,647.392,480.609) scale(0.947693,0.6712) translate(35.3399,230.503)" width="15" x="640.2841334259955" y="470.5413658803764"/></g>
  <g id="124">
   <use class="kv110" height="30" transform="rotate(180,647.392,589.498) scale(0.947693,0.6712) translate(35.3399,283.844)" width="15" x="640.2841334259955" xlink:href="#Disconnector:刀闸_0" y="579.4302547692652" zvalue="10614"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449928167430" ObjectName="#2炉变110kV侧1026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449928167430"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,647.392,589.498) scale(0.947693,0.6712) translate(35.3399,283.844)" width="15" x="640.2841334259955" y="579.4302547692652"/></g>
  <g id="198">
   <use class="kv35" height="30" transform="rotate(0,1681.23,568.977) scale(0.947693,0.6712) translate(92.402,273.791)" width="15" x="1674.12690638291" xlink:href="#Disconnector:刀闸_0" y="558.9086816097472" zvalue="10638"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449929150470" ObjectName="#2环保变3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449929150470"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1681.23,568.977) scale(0.947693,0.6712) translate(92.402,273.791)" width="15" x="1674.12690638291" y="558.9086816097472"/></g>
  <g id="212">
   <use class="kv35" height="30" transform="rotate(0,1354.24,591.977) scale(0.947693,0.6712) translate(74.3539,285.058)" width="15" x="1347.134338023149" xlink:href="#Disconnector:刀闸_0" y="581.9086816097472" zvalue="10647"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449929543686" ObjectName="厂用变3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449929543686"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1354.24,591.977) scale(0.947693,0.6712) translate(74.3539,285.058)" width="15" x="1347.134338023149" y="581.9086816097472"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="150">
   <use class="kv110" height="20" transform="rotate(90,944.374,369.393) scale(1.24619,-1.0068) translate(-185.332,-736.222)" width="10" x="938.1432423998623" xlink:href="#GroundDisconnector:地刀_0" y="359.3248086033777" zvalue="8029"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449926201350" ObjectName="110kV母线PT19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449926201350"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,944.374,369.393) scale(1.24619,-1.0068) translate(-185.332,-736.222)" width="10" x="938.1432423998623" y="359.3248086033777"/></g>
  <g id="170">
   <use class="kv110" height="20" transform="rotate(90,948.374,422.643) scale(1.24619,-1.0068) translate(-186.122,-842.363)" width="10" x="942.1432423998623" xlink:href="#GroundDisconnector:地刀_0" y="412.5748086033776" zvalue="8034"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449926070278" ObjectName="110kV母线PT19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449926070278"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,948.374,422.643) scale(1.24619,-1.0068) translate(-186.122,-842.363)" width="10" x="942.1432423998623" y="412.5748086033776"/></g>
  <g id="98">
   <use class="kv110" height="20" transform="rotate(270,638.196,253.315) scale(-1.24619,-1.0068) translate(-1149.08,-504.85)" width="10" x="631.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="243.246507813326" zvalue="9971"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449925677062" ObjectName="110kV傣海T线15167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449925677062"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,638.196,253.315) scale(-1.24619,-1.0068) translate(-1149.08,-504.85)" width="10" x="631.9650768138584" y="243.246507813326"/></g>
  <g id="29">
   <use class="kv110" height="20" transform="rotate(270,638.196,305.315) scale(-1.24619,-1.0068) translate(-1149.08,-608.499)" width="10" x="631.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="295.246507813326" zvalue="10027"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449925808134" ObjectName="110kV傣海T线15160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449925808134"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,638.196,305.315) scale(-1.24619,-1.0068) translate(-1149.08,-608.499)" width="10" x="631.9650768138584" y="295.246507813326"/></g>
  <g id="114">
   <use class="kv110" height="20" transform="rotate(270,644.196,397.315) scale(-1.24619,-1.0068) translate(-1159.9,-791.877)" width="10" x="637.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="387.246507813326" zvalue="10258"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449926463494" ObjectName="110kV傣海T线15117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449926463494"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,644.196,397.315) scale(-1.24619,-1.0068) translate(-1159.9,-791.877)" width="10" x="637.9650768138584" y="387.246507813326"/></g>
  <g id="28">
   <use class="kv110" height="20" transform="rotate(90,927.196,503.151) scale(-1.24619,1.0068) translate(-1669.99,-3.33063)" width="10" x="920.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="493.0833885747684" zvalue="10456"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449926594566" ObjectName="#1炉变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449926594566"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,927.196,503.151) scale(-1.24619,1.0068) translate(-1669.99,-3.33063)" width="10" x="920.9650768138584" y="493.0833885747684"/></g>
  <g id="104">
   <use class="kv110" height="40" transform="rotate(0,884,727) scale(1,-1) translate(0,-1454)" width="40" x="864" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="707" zvalue="10505"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449926791174" ObjectName="#1炉变110kV侧中性点1010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449926791174"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,884,727) scale(1,-1) translate(0,-1454)" width="40" x="864" y="707"/></g>
  <g id="5">
   <use class="kv110" height="20" transform="rotate(90,927.196,567.151) scale(-1.24619,1.0068) translate(-1669.99,-3.76293)" width="10" x="920.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="557.0833885747684" zvalue="10539"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449927118854" ObjectName="#1炉变110kV侧10160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449927118854"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,927.196,567.151) scale(-1.24619,1.0068) translate(-1669.99,-3.76293)" width="10" x="920.9650768138584" y="557.0833885747684"/></g>
  <g id="22">
   <use class="kv110" height="20" transform="rotate(90,927.196,619.151) scale(-1.24619,1.0068) translate(-1669.99,-4.11417)" width="10" x="920.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="609.0833885747684" zvalue="10548"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449927315462" ObjectName="#1炉变110kV侧10167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449927315462"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,927.196,619.151) scale(-1.24619,1.0068) translate(-1669.99,-4.11417)" width="10" x="920.9650768138584" y="609.0833885747684"/></g>
  <g id="80">
   <use class="kv35" height="20" transform="rotate(270,1380.42,439.092) scale(-1.24619,-1.0068) translate(-2486.9,-875.151)" width="10" x="1374.187299036081" xlink:href="#GroundDisconnector:地刀_0" y="429.0242855911038" zvalue="10562"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449927643142" ObjectName="35kV槟巨线海西T线35167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449927643142"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1380.42,439.092) scale(-1.24619,-1.0068) translate(-2486.9,-875.151)" width="10" x="1374.187299036081" y="429.0242855911038"/></g>
  <g id="152">
   <use class="kv110" height="20" transform="rotate(90,623.196,503.151) scale(-1.24619,1.0068) translate(-1122.05,-3.33063)" width="10" x="616.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="493.0833885747684" zvalue="10596"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449928757254" ObjectName="#2炉变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449928757254"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,623.196,503.151) scale(-1.24619,1.0068) translate(-1122.05,-3.33063)" width="10" x="616.9650768138584" y="493.0833885747684"/></g>
  <g id="139">
   <use class="kv110" height="40" transform="rotate(0,580,727) scale(1,-1) translate(0,-1454)" width="40" x="560" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="707" zvalue="10602"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449928626182" ObjectName="#2炉变110kV侧中性点1020接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449928626182"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,580,727) scale(1,-1) translate(0,-1454)" width="40" x="560" y="707"/></g>
  <g id="129">
   <use class="kv110" height="20" transform="rotate(90,623.196,567.151) scale(-1.24619,1.0068) translate(-1122.05,-3.76293)" width="10" x="616.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="557.0833885747684" zvalue="10610"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449928298502" ObjectName="#2炉变110kV侧10260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449928298502"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,623.196,567.151) scale(-1.24619,1.0068) translate(-1122.05,-3.76293)" width="10" x="616.9650768138584" y="557.0833885747684"/></g>
  <g id="120">
   <use class="kv110" height="20" transform="rotate(90,623.196,619.151) scale(-1.24619,1.0068) translate(-1122.05,-4.11417)" width="10" x="616.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="609.0833885747684" zvalue="10618"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449928101894" ObjectName="#2炉变110kV侧10267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449928101894"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,623.196,619.151) scale(-1.24619,1.0068) translate(-1122.05,-4.11417)" width="10" x="616.9650768138584" y="609.0833885747684"/></g>
  <g id="180">
   <use class="kv35" height="20" transform="rotate(90,1625.42,747.092) scale(1.24619,-1.0068) translate(-319.874,-1489.07)" width="10" x="1619.187299036081" xlink:href="#GroundDisconnector:地刀_0" y="737.0242855911039" zvalue="10632"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449928953862" ObjectName="#1环保变30167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449928953862"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1625.42,747.092) scale(1.24619,-1.0068) translate(-319.874,-1489.07)" width="10" x="1619.187299036081" y="737.0242855911039"/></g>
  <g id="187">
   <use class="kv35" height="20" transform="rotate(90,1797.42,746.092) scale(1.24619,-1.0068) translate(-353.853,-1487.08)" width="10" x="1791.187299036081" xlink:href="#GroundDisconnector:地刀_0" y="736.0242855911039" zvalue="10634"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449929084934" ObjectName="#2环保变30267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449929084934"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1797.42,746.092) scale(1.24619,-1.0068) translate(-353.853,-1487.08)" width="10" x="1791.187299036081" y="736.0242855911039"/></g>
  <g id="202">
   <use class="kv35" height="20" transform="rotate(90,1703.42,610.092) scale(1.24619,-1.0068) translate(-335.283,-1216)" width="10" x="1697.187299036081" xlink:href="#GroundDisconnector:地刀_0" y="600.0242855911038" zvalue="10642"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449929281542" ObjectName="#2环保变30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449929281542"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1703.42,610.092) scale(1.24619,-1.0068) translate(-335.283,-1216)" width="10" x="1697.187299036081" y="600.0242855911038"/></g>
  <g id="211">
   <use class="kv35" height="20" transform="rotate(90,1379.42,633.092) scale(1.24619,-1.0068) translate(-271.276,-1261.84)" width="10" x="1373.187299036081" xlink:href="#GroundDisconnector:地刀_0" y="623.0242855911039" zvalue="10649"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449929478150" ObjectName="厂用变30217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449929478150"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1379.42,633.092) scale(1.24619,-1.0068) translate(-271.276,-1261.84)" width="10" x="1373.187299036081" y="623.0242855911039"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="151">
   <path class="kv110" d="M 917.95 406.72 L 917.95 449.09" stroke-width="1" zvalue="8031"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@1" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 917.95 406.72 L 917.95 449.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv110" d="M 917.93 387.09 L 917.93 344.57" stroke-width="1" zvalue="8038"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@0" LinkObjectIDznd="218@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 917.93 387.09 L 917.93 344.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv110" d="M 668.31 378.95 L 668.31 410.12" stroke-width="1" zvalue="9978"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@1" LinkObjectIDznd="96@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 668.31 378.95 L 668.31 410.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv110" d="M 668.33 429.75 L 668.33 449.09" stroke-width="1" zvalue="9981"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 668.33 429.75 L 668.33 449.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv110" d="M 668.13 216.31 L 668.13 268.35" stroke-width="1" zvalue="10040"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="32@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 668.13 216.31 L 668.13 268.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv110" d="M 648.01 305.38 L 668.15 305.38" stroke-width="1" zvalue="10141"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="29@0" LinkObjectIDznd="73" MaxPinNum="2"/>
   </metadata>
  <path d="M 648.01 305.38 L 668.15 305.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv110" d="M 648.01 253.38 L 668.13 253.38" stroke-width="1" zvalue="10255"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="41" MaxPinNum="2"/>
   </metadata>
  <path d="M 648.01 253.38 L 668.13 253.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv110" d="M 654.01 397.38 L 668.31 397.38" stroke-width="1" zvalue="10259"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="93" MaxPinNum="2"/>
   </metadata>
  <path d="M 654.01 397.38 L 668.31 397.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv110" d="M 951.31 521.51 L 951.31 490.34" stroke-width="1" zvalue="10443"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@1" LinkObjectIDznd="53@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 951.31 521.51 L 951.31 490.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv110" d="M 937.01 503.09 L 951.31 503.09" stroke-width="1" zvalue="10457"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="52" MaxPinNum="2"/>
   </metadata>
  <path d="M 937.01 503.09 L 951.31 503.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv110" d="M 951.33 470.71 L 951.33 449.09" stroke-width="1" zvalue="10459"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="48@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 951.33 470.71 L 951.33 449.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv110" d="M 668.15 287.98 L 668.15 353.06" stroke-width="1" zvalue="10463"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@1" LinkObjectIDznd="100@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 668.15 287.98 L 668.15 353.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="kv110" d="M 938.56 422.71 L 917.95 422.71" stroke-width="1" zvalue="10466"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="151" MaxPinNum="2"/>
   </metadata>
  <path d="M 938.56 422.71 L 917.95 422.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv110" d="M 934.56 369.46 L 917.93 369.46" stroke-width="1" zvalue="10467"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 934.56 369.46 L 917.93 369.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 951.13 873.05 L 951.13 755.62" stroke-width="1" zvalue="10508"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="97@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 951.13 873.05 L 951.13 755.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="kv110" d="M 886.6 714.8 L 886.89 683.33 L 951.13 683.8" stroke-width="1" zvalue="10541"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="97@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 886.6 714.8 L 886.89 683.33 L 951.13 683.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv110" d="M 937.01 567.09 L 951.33 567.09" stroke-width="1" zvalue="10542"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 937.01 567.09 L 951.33 567.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv110" d="M 951.15 661.56 L 951.15 599.23" stroke-width="1" zvalue="10545"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="13@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 951.15 661.56 L 951.15 599.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv110" d="M 951.33 579.6 L 951.33 547.4" stroke-width="1" zvalue="10546"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="13@1" LinkObjectIDznd="61@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 951.33 579.6 L 951.33 547.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv110" d="M 937.01 619.09 L 951.15 619.09" stroke-width="1" zvalue="10549"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@0" LinkObjectIDznd="17" MaxPinNum="2"/>
   </metadata>
  <path d="M 937.01 619.09 L 951.15 619.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv35" d="M 1408.44 486.95 L 1408.44 525.09" stroke-width="1" zvalue="10560"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@1" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1408.44 486.95 L 1408.44 525.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv35" d="M 1408.28 322.47 L 1408.28 387.69" stroke-width="1" zvalue="10564"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="79@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1408.28 322.47 L 1408.28 387.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv35" d="M 1407.04 407.32 L 1407.04 461.06" stroke-width="1" zvalue="10565"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@1" LinkObjectIDznd="39@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1407.04 407.32 L 1407.04 461.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv35" d="M 1390.23 439.15 L 1407.04 439.15" stroke-width="1" zvalue="10566"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="92" MaxPinNum="2"/>
   </metadata>
  <path d="M 1390.23 439.15 L 1407.04 439.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv10" d="M 1020.28 838.21 L 1020.28 792 L 951.13 792" stroke-width="1" zvalue="10587"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="109" MaxPinNum="2"/>
   </metadata>
  <path d="M 1020.28 838.21 L 1020.28 792 L 951.13 792" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv10" d="M 1019.52 800.33 L 1020.28 800.33" stroke-width="1" zvalue="10588"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31@0" LinkObjectIDznd="38" MaxPinNum="2"/>
   </metadata>
  <path d="M 1019.52 800.33 L 1020.28 800.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv110" d="M 980.63 644.97 L 951.15 644.97" stroke-width="1" zvalue="10589"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="17" MaxPinNum="2"/>
   </metadata>
  <path d="M 980.63 644.97 L 951.15 644.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv110" d="M 647.31 521.51 L 647.31 490.34" stroke-width="1" zvalue="10595"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@1" LinkObjectIDznd="155@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 647.31 521.51 L 647.31 490.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv110" d="M 633.01 503.09 L 647.31 503.09" stroke-width="1" zvalue="10597"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="153" MaxPinNum="2"/>
   </metadata>
  <path d="M 633.01 503.09 L 647.31 503.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv110" d="M 647.33 470.71 L 647.33 449.09" stroke-width="1" zvalue="10599"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@1" LinkObjectIDznd="48@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 647.33 470.71 L 647.33 449.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv10" d="M 647.13 873.05 L 647.13 755.62" stroke-width="1" zvalue="10606"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@0" LinkObjectIDznd="141@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 647.13 873.05 L 647.13 755.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv110" d="M 582.6 714.8 L 582.89 683.33 L 647.13 683.8" stroke-width="1" zvalue="10612"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@0" LinkObjectIDznd="141@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 582.6 714.8 L 582.89 683.33 L 647.13 683.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv110" d="M 633.01 567.09 L 647.33 567.09" stroke-width="1" zvalue="10613"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="121" MaxPinNum="2"/>
   </metadata>
  <path d="M 633.01 567.09 L 647.33 567.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv110" d="M 647.15 661.56 L 647.15 599.23" stroke-width="1" zvalue="10615"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@0" LinkObjectIDznd="124@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 647.15 661.56 L 647.15 599.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv110" d="M 647.33 579.6 L 647.33 547.4" stroke-width="1" zvalue="10617"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@1" LinkObjectIDznd="157@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 647.33 579.6 L 647.33 547.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv110" d="M 633.01 619.09 L 647.15 619.09" stroke-width="1" zvalue="10619"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="123" MaxPinNum="2"/>
   </metadata>
  <path d="M 633.01 619.09 L 647.15 619.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv10" d="M 716.28 838.21 L 716.28 792 L 647.13 792" stroke-width="1" zvalue="10622"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="133" MaxPinNum="2"/>
   </metadata>
  <path d="M 716.28 838.21 L 716.28 792 L 647.13 792" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv10" d="M 715.52 800.33 L 716.28 800.33" stroke-width="1" zvalue="10623"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="115" MaxPinNum="2"/>
   </metadata>
  <path d="M 715.52 800.33 L 716.28 800.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv110" d="M 676.63 644.97 L 647.15 644.97" stroke-width="1" zvalue="10624"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="123" MaxPinNum="2"/>
   </metadata>
  <path d="M 676.63 644.97 L 647.15 644.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv35" d="M 1589.29 781.46 L 1589.29 712.87" stroke-width="1" zvalue="10627"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="136@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1589.29 781.46 L 1589.29 712.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="kv35" d="M 1589.32 693.24 L 1589.32 656 L 1765.32 656 L 1765.32 693.24" stroke-width="1" zvalue="10628"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136@0" LinkObjectIDznd="194@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1589.32 693.24 L 1589.32 656 L 1765.32 656 L 1765.32 693.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv35" d="M 1765.29 712.87 L 1765.29 781.46" stroke-width="1" zvalue="10629"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@1" LinkObjectIDznd="195@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1765.29 712.87 L 1765.29 781.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv35" d="M 1615.6 747.15 L 1589.29 747.15" stroke-width="1" zvalue="10635"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@0" LinkObjectIDznd="169" MaxPinNum="2"/>
   </metadata>
  <path d="M 1615.6 747.15 L 1589.29 747.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv35" d="M 1787.6 746.15 L 1765.29 746.15" stroke-width="1" zvalue="10636"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@0" LinkObjectIDznd="177" MaxPinNum="2"/>
   </metadata>
  <path d="M 1787.6 746.15 L 1765.29 746.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="kv35" d="M 1681.29 656 L 1681.29 578.87" stroke-width="1" zvalue="10639"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176" LinkObjectIDznd="198@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1681.29 656 L 1681.29 578.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv35" d="M 1681.32 559.24 L 1681.32 525.09" stroke-width="1" zvalue="10640"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@0" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1681.32 559.24 L 1681.32 525.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="kv35" d="M 1693.6 610.15 L 1681.29 610.15" stroke-width="1" zvalue="10643"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="200" MaxPinNum="2"/>
   </metadata>
  <path d="M 1693.6 610.15 L 1681.29 610.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="kv35" d="M 1369.6 633.15 L 1354.3 633.15" stroke-width="1" zvalue="10650"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@0" LinkObjectIDznd="213" MaxPinNum="2"/>
   </metadata>
  <path d="M 1369.6 633.15 L 1354.3 633.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv35" d="M 1354.3 707.6 L 1354.3 601.87" stroke-width="1" zvalue="10652"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="212@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1354.3 707.6 L 1354.3 601.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv35" d="M 1354.33 582.24 L 1354.33 525.09" stroke-width="1" zvalue="10653"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="36@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1354.33 582.24 L 1354.33 525.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="218">
   <use class="kv110" height="35" transform="rotate(0,915.95,326.341) scale(0.9375,1.07143) translate(59.8133,-20.5061)" width="40" x="897.199671629758" xlink:href="#Accessory:5绕组母线PT带避雷器_0" y="307.5909090909091" zvalue="9702"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449925939206" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,915.95,326.341) scale(0.9375,1.07143) translate(59.8133,-20.5061)" width="40" x="897.199671629758" y="307.5909090909091"/></g>
  <g id="184">
   <use class="kv110" height="26" transform="rotate(270,993,645) scale(1,1) translate(0,0)" width="12" x="987" xlink:href="#Accessory:避雷器1_0" y="632" zvalue="10533"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449926987782" ObjectName="#1炉变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,993,645) scale(1,1) translate(0,0)" width="12" x="987" y="632"/></g>
  <g id="31">
   <use class="kv10" height="18" transform="rotate(0,1019.5,812.242) scale(1.3,1.50468) translate(-233.769,-267.889)" width="10" x="1013" xlink:href="#Accessory:熔断器_0" y="798.7001980746802" zvalue="10551"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449927380998" ObjectName="#1电容器组熔断器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1019.5,812.242) scale(1.3,1.50468) translate(-233.769,-267.889)" width="10" x="1013" y="798.7001980746802"/></g>
  <g id="131">
   <use class="kv110" height="26" transform="rotate(270,689,645) scale(1,1) translate(0,0)" width="12" x="683" xlink:href="#Accessory:避雷器1_0" y="632" zvalue="10609"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449928364038" ObjectName="#2炉变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,689,645) scale(1,1) translate(0,0)" width="12" x="683" y="632"/></g>
  <g id="116">
   <use class="kv10" height="18" transform="rotate(0,715.5,812.242) scale(1.3,1.50468) translate(-163.615,-267.889)" width="10" x="709" xlink:href="#Accessory:熔断器_0" y="798.7001980746802" zvalue="10621"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449927970822" ObjectName="#2电容器组熔断器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,715.5,812.242) scale(1.3,1.50468) translate(-163.615,-267.889)" width="10" x="709" y="798.7001980746802"/></g>
 </g>
 <g id="BreakerClass">
  <g id="100">
   <use class="kv110" height="20" transform="rotate(0,668.205,366.02) scale(1.5542,1.35421) translate(-235.499,-92.195)" width="10" x="660.4340345345652" xlink:href="#Breaker:开关_0" y="352.4779758524579" zvalue="9966"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924540039173" ObjectName="110kV傣海T线151断路器"/>
   <cge:TPSR_Ref TObjectID="6473924540039173"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,668.205,366.02) scale(1.5542,1.35421) translate(-235.499,-92.195)" width="10" x="660.4340345345652" y="352.4779758524579"/></g>
  <g id="61">
   <use class="kv110" height="20" transform="rotate(0,951.205,534.446) scale(1.5542,-1.35421) translate(-336.411,-925.559)" width="10" x="943.4340345345652" xlink:href="#Breaker:开关_0" y="520.9037238148987" zvalue="10434"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924540104709" ObjectName="#1炉变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473924540104709"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,951.205,534.446) scale(1.5542,-1.35421) translate(-336.411,-925.559)" width="10" x="943.4340345345652" y="520.9037238148987"/></g>
  <g id="39">
   <use class="kv35" height="20" transform="rotate(0,1408.34,474.02) scale(1.5542,1.35421) translate(-499.416,-120.444)" width="10" x="1400.565063936659" xlink:href="#Breaker:开关_0" y="460.477975852458" zvalue="10556"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924540170245" ObjectName="35kV槟巨线海西T线351断路器"/>
   <cge:TPSR_Ref TObjectID="6473924540170245"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1408.34,474.02) scale(1.5542,1.35421) translate(-499.416,-120.444)" width="10" x="1400.565063936659" y="460.477975852458"/></g>
  <g id="157">
   <use class="kv110" height="20" transform="rotate(0,647.205,534.446) scale(1.5542,-1.35421) translate(-228.011,-925.559)" width="10" x="639.4340345345652" xlink:href="#Breaker:开关_0" y="520.9037238148987" zvalue="10591"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924540235781" ObjectName="#2炉变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473924540235781"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,647.205,534.446) scale(1.5542,-1.35421) translate(-228.011,-925.559)" width="10" x="639.4340345345652" y="520.9037238148987"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,521.25) scale(1,1) translate(1.24653e-14,0)" writing-mode="lr" x="137.75" xml:space="preserve" y="526.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126081527815" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="67">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,544.25) scale(1,1) translate(1.24653e-14,5.96467e-14)" writing-mode="lr" x="137.75" xml:space="preserve" y="549.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126081593351" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,567.25) scale(1,1) translate(1.24653e-14,-1.244e-13)" writing-mode="lr" x="137.75" xml:space="preserve" y="572.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126081658887" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,495.75) scale(1,1) translate(1.24653e-14,-1.08524e-13)" writing-mode="lr" x="137.75" xml:space="preserve" y="500.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126081789959" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,142,203.111) scale(1,1) translate(0,0)" writing-mode="lr" x="142.15" xml:space="preserve" y="209.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126081921031" ObjectName="F"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,521.25) scale(1,1) translate(2.57217e-14,0)" writing-mode="lr" x="257.15" xml:space="preserve" y="526.02" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="58">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,544.25) scale(1,1) translate(2.57217e-14,5.96467e-14)" writing-mode="lr" x="257.15" xml:space="preserve" y="549.02" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,567.25) scale(1,1) translate(2.57217e-14,-1.244e-13)" writing-mode="lr" x="257.15" xml:space="preserve" y="572.02" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,495.75) scale(1,1) translate(2.57217e-14,-1.08524e-13)" writing-mode="lr" x="257.15" xml:space="preserve" y="500.52" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="54" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,142,228) scale(1,1) translate(0,0)" writing-mode="lr" x="142.15" xml:space="preserve" y="234.27" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,148.091,250.104) scale(1,1) translate(0,0)" writing-mode="lr" x="148.24" xml:space="preserve" y="256.38" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,148.091,273.104) scale(1,1) translate(0,-3.54521e-13)" writing-mode="lr" x="148.24" xml:space="preserve" y="279.38" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,594.25) scale(1,1) translate(1.24653e-14,1.30396e-13)" writing-mode="lr" x="137.75" xml:space="preserve" y="599.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126081986566" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,594.25) scale(1,1) translate(2.57217e-14,1.30396e-13)" writing-mode="lr" x="257.15" xml:space="preserve" y="599.02" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,665.83,75.25) scale(1,1) translate(0,0)" writing-mode="lr" x="666.01" xml:space="preserve" y="80.12" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126076284933" ObjectName="P"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="18" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,665.83,96.25) scale(1,1) translate(0,0)" writing-mode="lr" x="666.01" xml:space="preserve" y="101.12" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126076350469" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,665.83,117.25) scale(1,1) translate(0,0)" writing-mode="lr" x="666.01" xml:space="preserve" y="122.12" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126076416005" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="8" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1398.28,154.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1397.82" xml:space="preserve" y="159.25" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126082576390" ObjectName="P"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="33" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1398.28,175.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1397.82" xml:space="preserve" y="180.25" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126082641926" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="34">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="34" prefix="Ib:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1398.28,196.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1397.82" xml:space="preserve" y="201.25" zvalue="1">Ib:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126082969604" ObjectName="Ib"/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="35" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1411.03,214.972) scale(1,1) translate(0,0)" writing-mode="lr" x="1410.48" xml:space="preserve" y="221.25" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126083362820" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="140" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,914,229.591) scale(1,1) translate(0,0)" writing-mode="lr" x="913.53" xml:space="preserve" y="234.37" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126075236359" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,914,248.424) scale(1,1) translate(0,0)" writing-mode="lr" x="913.53" xml:space="preserve" y="253.2" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126075301892" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="145" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,914,267.258) scale(1,1) translate(0,0)" writing-mode="lr" x="913.53" xml:space="preserve" y="272.04" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126075367431" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="146">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="146" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,914,286.091) scale(1,1) translate(0,-2.38557e-13)" writing-mode="lr" x="913.53" xml:space="preserve" y="290.87" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126075498503" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="164">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="164" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,751.129,507.4) scale(1,1) translate(0,0)" writing-mode="lr" x="750.58" xml:space="preserve" y="513.6799999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126086967300" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="166">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="166" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,751.129,532.4) scale(1,1) translate(0,0)" writing-mode="lr" x="750.58" xml:space="preserve" y="538.6799999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126087032836" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="178">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="178" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,751.129,557.4) scale(1,1) translate(0,0)" writing-mode="lr" x="750.58" xml:space="preserve" y="563.6799999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126087229447" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="186">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="186" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1051.13,505.4) scale(1,1) translate(0,0)" writing-mode="lr" x="1050.58" xml:space="preserve" y="511.68" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126077857797" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="190">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="190" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1051.13,530.4) scale(1,1) translate(0,0)" writing-mode="lr" x="1050.58" xml:space="preserve" y="536.6799999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126077923333" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="191">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="191" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1051.13,555.4) scale(1,1) translate(0,0)" writing-mode="lr" x="1050.58" xml:space="preserve" y="561.6799999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126078119941" ObjectName="HIa"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="1056">
   <use height="30" transform="rotate(0,265.286,338.071) scale(0.708333,0.665547) translate(104.86,164.872)" width="30" x="254.66" xlink:href="#State:红绿圆(方形)_0" y="328.09" zvalue="10247"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562951492272132" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,265.286,338.071) scale(0.708333,0.665547) translate(104.86,164.872)" width="30" x="254.66" y="328.09"/></g>
  <g id="1057">
   <use height="30" transform="rotate(0,352.911,338.071) scale(0.708333,0.665547) translate(140.941,164.872)" width="30" x="342.29" xlink:href="#State:红绿圆(方形)_0" y="328.09" zvalue="10249"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374891417604" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,352.911,338.071) scale(0.708333,0.665547) translate(140.941,164.872)" width="30" x="342.29" y="328.09"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="97">
   <g id="970">
    <use class="kv110" height="30" transform="rotate(0,951.129,708.355) scale(3.36364,3.36364) translate(-642.361,-462.308)" width="22" x="914.13" xlink:href="#PowerTransformer2:接地可调两卷变_0" y="657.9" zvalue="10503"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874446667779" ObjectName="110"/>
    </metadata>
   </g>
   <g id="971">
    <use class="kv10" height="30" transform="rotate(0,951.129,708.355) scale(3.36364,3.36364) translate(-642.361,-462.308)" width="22" x="914.13" xlink:href="#PowerTransformer2:接地可调两卷变_1" y="657.9" zvalue="10503"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874446733315" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399455473667" ObjectName="#1炉变"/>
   <cge:TPSR_Ref TObjectID="6755399455473667"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,951.129,708.355) scale(3.36364,3.36364) translate(-642.361,-462.308)" width="22" x="914.13" y="657.9"/></g>
  <g id="141">
   <g id="1410">
    <use class="kv110" height="30" transform="rotate(0,647.129,708.355) scale(3.36364,3.36364) translate(-428.739,-462.308)" width="22" x="610.13" xlink:href="#PowerTransformer2:接地可调两卷变_0" y="657.9" zvalue="10600"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874446798851" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1411">
    <use class="kv10" height="30" transform="rotate(0,647.129,708.355) scale(3.36364,3.36364) translate(-428.739,-462.308)" width="22" x="610.13" xlink:href="#PowerTransformer2:接地可调两卷变_1" y="657.9" zvalue="10600"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874446864387" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399455539203" ObjectName="#2炉变"/>
   <cge:TPSR_Ref TObjectID="6755399455539203"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,647.129,708.355) scale(3.36364,3.36364) translate(-428.739,-462.308)" width="22" x="610.13" y="657.9"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="107">
   <use class="kv10" height="30" transform="rotate(0,951.129,882.5) scale(1.98234,-0.7) translate(-465.433,-2147.71)" width="12" x="939.2345913622861" xlink:href="#EnergyConsumer:负荷_0" y="872" zvalue="10507"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449926856710" ObjectName="#1电炉"/>
   <cge:TPSR_Ref TObjectID="6192449926856710"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,951.129,882.5) scale(1.98234,-0.7) translate(-465.433,-2147.71)" width="12" x="939.2345913622861" y="872"/></g>
  <g id="40">
   <use class="kv35" height="30" transform="rotate(0,1408.28,299.972) scale(3.125,1.66667) translate(-944.883,-109.989)" width="12" x="1389.534254087979" xlink:href="#EnergyConsumer:负荷_0" y="274.9722222222222" zvalue="10558"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449927446534" ObjectName="35kV槟巨线海西T线"/>
   <cge:TPSR_Ref TObjectID="6192449927446534"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1408.28,299.972) scale(3.125,1.66667) translate(-944.883,-109.989)" width="12" x="1389.534254087979" y="274.9722222222222"/></g>
  <g id="126">
   <use class="kv35" height="26" transform="rotate(0,1588.72,825.333) scale(3.4188,3.4188) translate(-1103.46,-552.479)" width="17" x="1559.66293873013" xlink:href="#EnergyConsumer:Y-Y站用_0" y="780.8888888888889" zvalue="10567"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449927708678" ObjectName="#1环保变"/>
   <cge:TPSR_Ref TObjectID="6192449927708678"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1588.72,825.333) scale(3.4188,3.4188) translate(-1103.46,-552.479)" width="17" x="1559.66293873013" y="780.8888888888889"/></g>
  <g id="195">
   <use class="kv35" height="26" transform="rotate(0,1764.72,825.333) scale(3.4188,3.4188) translate(-1227.98,-552.479)" width="17" x="1735.66293873013" xlink:href="#EnergyConsumer:Y-Y站用_0" y="780.8888888888889" zvalue="10578"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449927905286" ObjectName="#2环保变"/>
   <cge:TPSR_Ref TObjectID="6192449927905286"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1764.72,825.333) scale(3.4188,3.4188) translate(-1227.98,-552.479)" width="17" x="1735.66293873013" y="780.8888888888889"/></g>
  <g id="135">
   <use class="kv10" height="30" transform="rotate(0,647.129,882.5) scale(1.98234,-0.7) translate(-314.787,-2147.71)" width="12" x="635.2345913622862" xlink:href="#EnergyConsumer:负荷_0" y="872" zvalue="10604"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449928495110" ObjectName="#2电炉"/>
   <cge:TPSR_Ref TObjectID="6192449928495110"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,647.129,882.5) scale(1.98234,-0.7) translate(-314.787,-2147.71)" width="12" x="635.2345913622862" y="872"/></g>
  <g id="205">
   <use class="kv35" height="35" transform="rotate(0,1354,737) scale(1.8,1.8) translate(-593.778,-313.556)" width="20" x="1336" xlink:href="#EnergyConsumer:Y-d厂用变_0" y="705.5" zvalue="10644"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449929347078" ObjectName="厂用变"/>
   <cge:TPSR_Ref TObjectID="6192449929347078"/></metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1354,737) scale(1.8,1.8) translate(-593.778,-313.556)" width="20" x="1336" y="705.5"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="110">
   <use class="kv10" height="13" transform="rotate(0,1020,855.917) scale(2.83333,2.83333) translate(-649,-541.912)" width="12" x="1003" xlink:href="#Compensator:无功补偿20210816_0" y="837.5" zvalue="10509"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449926922246" ObjectName="#1电容器组"/>
   <cge:TPSR_Ref TObjectID="6192449926922246"/></metadata>
  <rect fill="white" height="13" opacity="0" stroke="white" transform="rotate(0,1020,855.917) scale(2.83333,2.83333) translate(-649,-541.912)" width="12" x="1003" y="837.5"/></g>
  <g id="132">
   <use class="kv10" height="13" transform="rotate(0,716,855.917) scale(2.83333,2.83333) translate(-452.294,-541.912)" width="12" x="699" xlink:href="#Compensator:无功补偿20210816_0" y="837.5" zvalue="10607"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449928429574" ObjectName="#2电容器组"/>
   <cge:TPSR_Ref TObjectID="6192449928429574"/></metadata>
  <rect fill="white" height="13" opacity="0" stroke="white" transform="rotate(0,716,855.917) scale(2.83333,2.83333) translate(-452.294,-541.912)" width="12" x="699" y="837.5"/></g>
 </g>
</svg>