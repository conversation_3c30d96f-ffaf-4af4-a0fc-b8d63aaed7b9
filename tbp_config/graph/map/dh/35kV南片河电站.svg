<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549593767938" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1754.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2334.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:五卷PT带熔断_0" viewBox="0,0,70,75">
   <use terminal-index="0" type="0" x="33.8" xlink:href="#terminal" y="70.3"/>
   <path d="M 55 21.1 L 55 25.9 L 49.4 25.9" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 47.8 19.5 L 47.8 17.9 L 62.2 6.7 L 62.2 5.1" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.2 43.5 L 8.2 65.9 L 33.8 65.9" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="42.41333333333334" x2="45.61333333333334" y1="31.14384521058083" y2="29.54384521058083"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="42.41333333333334" x2="42.41333333333334" y1="31.14384521058083" y2="24.74384521058083"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="42.41333333333334" x2="45.61333333333334" y1="24.74384521058083" y2="26.34384521058083"/>
   <rect fill-opacity="0" height="14.4" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,33.8,51.5) scale(1,1) translate(0,0)" width="8" x="29.8" y="44.3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.8" x2="33.8" y1="40.3" y2="69.5"/>
   <rect fill-opacity="0" height="16.53" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,8.2,50.83) scale(1,-1) translate(0,-1389.33)" width="11.2" x="2.6" y="42.57"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.27999999999999" x2="11.26666666666667" y1="46.34384521058083" y2="54.3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.146666666666651" x2="5.399999999999995" y1="46.34384521058083" y2="54.3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.087093768087719" x2="8.087093768087719" y1="42.47717854391416" y2="37.44003242978255"/>
   <ellipse cx="33.89" cy="33.89" fill-opacity="0" rx="6.67" ry="6.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.33474790508334" x2="4.491842553589187" y1="37.33137877295937" y2="37.33137877295937"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.00141457175" x2="5.825175886922519" y1="35.33137877295938" y2="35.33137877295938"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.068081238416674" x2="6.758509220255849" y1="33.33137877295938" y2="33.33137877295938"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.8518425535892" x2="37.69184255358919" y1="34.23060739739171" y2="36.21067137235507"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.85184255358918" x2="30.01184255358919" y1="34.23060739739171" y2="36.21067137235507"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.85184255358919" x2="33.85184255358919" y1="30.27047944746495" y2="34.23060739739167"/>
   <ellipse cx="33.36" cy="22.56" fill-opacity="0" rx="6.67" ry="6.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="23.36" cy="28.29" fill-opacity="0" rx="6.67" ry="6.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.31850922025586" x2="33.31850922025586" y1="18.93714611413161" y2="22.89727406405834"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.31850922025585" x2="19.47850922025586" y1="28.63060739739171" y2="30.61067137235507"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.31850922025586" x2="27.15850922025585" y1="28.63060739739171" y2="30.61067137235507"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.31850922025586" x2="23.31850922025586" y1="24.67047944746495" y2="28.63060739739167"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.31850922025585" x2="29.47850922025586" y1="22.89727406405837" y2="24.87733803902174"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.31850922025586" x2="37.15850922025585" y1="22.89727406405837" y2="24.87733803902174"/>
   <ellipse cx="43.23" cy="28.02" fill-opacity="0" rx="6.67" ry="6.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <rect fill-opacity="0" height="14.21" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,54.93,14.03) scale(1,1) translate(0,0)" width="8.4" x="50.73" y="6.92"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="54.44865365939912" x2="54.44865365939912" y1="4.566666666666663" y2="6.966666666666651"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="56.0125128711384" x2="52.09076330619942" y1="4.75335271024386" y2="4.75335271024386"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="55.24836385090142" x2="52.85491232643641" y1="3.499576240599325" y2="3.499576240599325"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="54.71345953673553" x2="53.38981664060229" y1="2.24579977095479" y2="2.24579977095479"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV南片河电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="50.21" xlink:href="logo.png" y="47.57"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.214,79.5714) scale(1,1) translate(0,0)" writing-mode="lr" x="199.21" xml:space="preserve" y="83.06999999999999" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,201.714,77.2618) scale(1,1) translate(0,0)" writing-mode="lr" x="201.71" xml:space="preserve" y="86.26000000000001" zvalue="3">35kV南片河电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="56" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,82.9688,199) scale(1,1) translate(0,0)" width="73.56" x="46.19" y="187" zvalue="219"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.9688,199) scale(1,1) translate(0,0)" writing-mode="lr" x="82.97" xml:space="preserve" y="203.5" zvalue="219">信号一览</text>
  <line fill="none" id="83" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384.2142857142856" x2="384.2142857142856" y1="15.57142857142867" y2="1045.571428571429" zvalue="4"/>
  <line fill="none" id="81" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="151.441921185511" y2="151.441921185511" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,892.319,358.73) scale(1,1) translate(0,0)" writing-mode="lr" x="892.3200000000001" xml:space="preserve" y="363.23" zvalue="8">301</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,780.393,455.551) scale(1,1) translate(0,0)" writing-mode="lr" x="780.39" xml:space="preserve" y="460.05" zvalue="10">#1主变6.3MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,493.34,601.841) scale(1,1) translate(0,0)" writing-mode="lr" x="493.34" xml:space="preserve" y="606.34" zvalue="12">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,628.456,921.51) scale(1,1) translate(0,3.0001e-13)" writing-mode="lr" x="628.4563443455019" xml:space="preserve" y="926.0096849061007" zvalue="14">#1发电机2.5MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,935.497,858.175) scale(1,1) translate(0,0)" writing-mode="lr" x="935.5" xml:space="preserve" y="862.67" zvalue="16">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,943.222,288.889) scale(1,1) translate(0,0)" writing-mode="lr" x="943.22" xml:space="preserve" y="293.39" zvalue="20">35117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,888.932,263.675) scale(1,1) translate(0,0)" writing-mode="lr" x="888.9299999999999" xml:space="preserve" y="268.17" zvalue="22">3511</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,863.53,127.25) scale(1,1) translate(0,0)" writing-mode="lr" x="863.53" xml:space="preserve" y="131.75" zvalue="30">35kV允南线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,834.046,263.675) scale(1,1) translate(0,0)" writing-mode="lr" x="834.05" xml:space="preserve" y="268.17" zvalue="37">3519</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,738.778,212.778) scale(1,1) translate(0,0)" writing-mode="lr" x="738.78" xml:space="preserve" y="217.28" zvalue="41">35190</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,739.889,321.778) scale(1,1) translate(0,0)" writing-mode="lr" x="739.89" xml:space="preserve" y="326.28" zvalue="43">35197</text>
  <line fill="none" id="48" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="95"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="97">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="98">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="99">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="100">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="101">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="103">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,206.054,954) scale(1,1) translate(0,0)" writing-mode="lr" x="206.05" xml:space="preserve" y="960" zvalue="105">NanPianHe-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,612.5,370.25) scale(1,1) translate(0,0)" writing-mode="lr" x="612.5" xml:space="preserve" y="374.75" zvalue="120">6.3kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,663.34,547.425) scale(1,1) translate(1.43592e-13,0)" writing-mode="lr" x="663.34" xml:space="preserve" y="551.92" zvalue="124">6901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,892.43,543.675) scale(1,1) translate(0,0)" writing-mode="lr" x="892.4299999999999" xml:space="preserve" y="548.17" zvalue="130">6011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,734.18,827.925) scale(1,1) translate(0,0)" writing-mode="lr" x="734.1799999999999" xml:space="preserve" y="832.42" zvalue="137">6913</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,582.18,827.925) scale(1,1) translate(0,0)" writing-mode="lr" x="582.1799999999999" xml:space="preserve" y="832.42" zvalue="139">6912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,532.93,827.925) scale(1,1) translate(0,0)" writing-mode="lr" x="532.9299999999999" xml:space="preserve" y="832.42" zvalue="141">6911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,657.002,736.23) scale(1,1) translate(-1.43164e-13,0)" writing-mode="lr" x="657" xml:space="preserve" y="740.73" zvalue="150">651</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,658.659,689.925) scale(1,1) translate(0,0)" writing-mode="lr" x="658.66" xml:space="preserve" y="694.42" zvalue="154">3511</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,944.409,698.175) scale(1,1) translate(0,0)" writing-mode="lr" x="944.41" xml:space="preserve" y="702.67" zvalue="158">6811</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1288.46,921.51) scale(1,1) translate(0,3.0001e-13)" writing-mode="lr" x="1288.456344345502" xml:space="preserve" y="926.0096849061007" zvalue="162">#2发电机2.5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1394.18,827.925) scale(1,1) translate(0,0)" writing-mode="lr" x="1394.18" xml:space="preserve" y="832.42" zvalue="167">6923</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1242.18,827.925) scale(1,1) translate(0,0)" writing-mode="lr" x="1242.18" xml:space="preserve" y="832.42" zvalue="169">6922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1192.93,827.925) scale(1,1) translate(0,0)" writing-mode="lr" x="1192.93" xml:space="preserve" y="832.42" zvalue="172">6921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1317,736.23) scale(1,1) translate(-2.89714e-13,0)" writing-mode="lr" x="1317" xml:space="preserve" y="740.73" zvalue="179">652</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1318.66,689.925) scale(1,1) translate(0,0)" writing-mode="lr" x="1318.66" xml:space="preserve" y="694.42" zvalue="183">3511</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1348.39,456.801) scale(1,1) translate(0,0)" writing-mode="lr" x="1348.39" xml:space="preserve" y="461.3" zvalue="187">坝区变0.1MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1462.18,549.925) scale(1,1) translate(2.73138e-12,0)" writing-mode="lr" x="1462.18" xml:space="preserve" y="554.42" zvalue="190">6021</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1608.25,377) scale(1,1) translate(0,0)" writing-mode="lr" x="1608.25" xml:space="preserve" y="381.5" zvalue="194">10kV母线</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="286.75" y2="286.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="260.75" y2="286.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="260.75" y2="286.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="286.75" y2="286.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="260.75" y2="286.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="260.75" y2="286.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="286.75" y2="286.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="311" y2="311"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="286.75" y2="311"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="286.75" y2="311"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="286.75" y2="286.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="311" y2="311"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="286.75" y2="311"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="286.75" y2="311"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="311" y2="311"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="333.75" y2="333.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="311" y2="333.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="311" y2="333.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="311" y2="311"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="333.75" y2="333.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="311" y2="333.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="311" y2="333.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="333.75" y2="333.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="356.5" y2="356.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="333.75" y2="356.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="333.75" y2="356.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="333.75" y2="333.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="356.5" y2="356.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="333.75" y2="356.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="333.75" y2="356.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="356.5" y2="356.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="379.25" y2="379.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="356.5" y2="379.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="356.5" y2="379.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="356.5" y2="356.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="379.25" y2="379.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="356.5" y2="379.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="356.5" y2="379.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="379.25" y2="379.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="402" y2="402"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="379.25" y2="402"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="379.25" y2="402"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="379.25" y2="379.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="402" y2="402"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="379.25" y2="402"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="379.25" y2="402"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="402" y2="402"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="424.75" y2="424.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="402" y2="424.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="402" y2="424.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="402" y2="402"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="424.75" y2="424.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="402" y2="424.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="402" y2="424.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,198.399,199.591) scale(1,1) translate(0,0)" writing-mode="lr" x="198.4" xml:space="preserve" y="204.09" zvalue="210">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,303.399,199.591) scale(1,1) translate(0,0)" writing-mode="lr" x="303.4" xml:space="preserve" y="204.09" zvalue="211">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,55.5,273.75) scale(1,1) translate(0,0)" writing-mode="lr" x="13" xml:space="preserve" y="278.25" zvalue="212">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,236,273.75) scale(1,1) translate(0,0)" writing-mode="lr" x="193.5" xml:space="preserve" y="278.25" zvalue="213">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.5,299.75) scale(1,1) translate(0,0)" writing-mode="lr" x="12" xml:space="preserve" y="304.25" zvalue="220">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235,299.75) scale(1,1) translate(0,0)" writing-mode="lr" x="192.5" xml:space="preserve" y="304.25" zvalue="221">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,392) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="396.5" zvalue="224">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,223.688,392) scale(1,1) translate(0,0)" writing-mode="lr" x="223.69" xml:space="preserve" y="396.5" zvalue="226">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,416) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="420.5" zvalue="228">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,223.688,415) scale(1,1) translate(0,0)" writing-mode="lr" x="223.69" xml:space="preserve" y="419.5" zvalue="229">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.5,322.75) scale(1,1) translate(0,0)" writing-mode="lr" x="12" xml:space="preserve" y="327.25" zvalue="230">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,234.5,321.75) scale(1,1) translate(0,0)" writing-mode="lr" x="192" xml:space="preserve" y="326.25" zvalue="232">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,238.688,347) scale(1,1) translate(0,0)" writing-mode="lr" x="238.69" xml:space="preserve" y="351.5" zvalue="236">6.3kV母线频率</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="73.56" x="46.19" y="187" zvalue="219"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BreakerClass">
  <g id="418">
   <use class="kv35" height="20" transform="rotate(0,863.541,359.73) scale(1.22222,1.11111) translate(-155.896,-34.8619)" width="10" x="857.4303734218488" xlink:href="#Breaker:开关_0" y="348.6190476190475" zvalue="7"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924563763204" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924563763204"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,863.541,359.73) scale(1.22222,1.11111) translate(-155.896,-34.8619)" width="10" x="857.4303734218488" y="348.6190476190475"/></g>
  <g id="113">
   <use class="v6300" height="20" transform="rotate(0,626.394,737.23) scale(1.22222,1.11111) translate(-112.779,-72.6119)" width="10" x="620.2826217389002" xlink:href="#Breaker:开关_0" y="726.1190476190475" zvalue="149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924563828740" ObjectName="#1发电机651断路器"/>
   <cge:TPSR_Ref TObjectID="6473924563828740"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,626.394,737.23) scale(1.22222,1.11111) translate(-112.779,-72.6119)" width="10" x="620.2826217389002" y="726.1190476190475"/></g>
  <g id="151">
   <use class="v6300" height="20" transform="rotate(0,1286.39,737.23) scale(1.22222,1.11111) translate(-232.779,-72.6119)" width="10" x="1280.2826217389" xlink:href="#Breaker:开关_0" y="726.1190476190475" zvalue="178"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924563894276" ObjectName="#2发电机652断路器"/>
   <cge:TPSR_Ref TObjectID="6473924563894276"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1286.39,737.23) scale(1.22222,1.11111) translate(-232.779,-72.6119)" width="10" x="1280.2826217389" y="726.1190476190475"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="414">
   <g id="4140">
    <use class="kv35" height="60" transform="rotate(0,863.53,448.077) scale(1.6125,1.54462) translate(-315.757,-141.65)" width="40" x="831.28" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="401.74" zvalue="9"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874458726403" ObjectName="35"/>
    </metadata>
   </g>
   <g id="4141">
    <use class="v6300" height="60" transform="rotate(0,863.53,448.077) scale(1.6125,1.54462) translate(-315.757,-141.65)" width="40" x="831.28" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="401.74" zvalue="9"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874458791939" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399461240835" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399461240835"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,863.53,448.077) scale(1.6125,1.54462) translate(-315.757,-141.65)" width="40" x="831.28" y="401.74"/></g>
  <g id="178">
   <g id="1780">
    <use class="kv10" height="60" transform="rotate(0,1431.53,449.327) scale(1.6125,1.54462) translate(-531.509,-142.091)" width="40" x="1399.28" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="402.99" zvalue="186"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874458857475" ObjectName="10"/>
    </metadata>
   </g>
   <g id="1781">
    <use class="v6300" height="60" transform="rotate(0,1431.53,449.327) scale(1.6125,1.54462) translate(-531.509,-142.091)" width="40" x="1399.28" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="402.99" zvalue="186"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874458923011" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399461306371" ObjectName="坝区变"/>
   <cge:TPSR_Ref TObjectID="6755399461306371"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1431.53,449.327) scale(1.6125,1.54462) translate(-531.509,-142.091)" width="40" x="1399.28" y="402.99"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="410">
   <path class="v6300" d="M 460.26 633.54 L 1693.26 633.54" stroke-width="6" zvalue="11"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674256486404" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674256486404"/></metadata>
  <path d="M 460.26 633.54 L 1693.26 633.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv10" d="M 1282.5 346.75 L 1597.5 346.75" stroke-width="6" zvalue="193"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674256551940" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674256551940"/></metadata>
  <path d="M 1282.5 346.75 L 1597.5 346.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="212">
   <use class="v6300" height="30" transform="rotate(0,626.475,875.307) scale(1.58467,1.58467) translate(-222.371,-314.179)" width="30" x="602.7051208787044" xlink:href="#Generator:发电机_0" y="851.5367361713461" zvalue="13"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450063302661" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192450063302661"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,626.475,875.307) scale(1.58467,1.58467) translate(-222.371,-314.179)" width="30" x="602.7051208787044" y="851.5367361713461"/></g>
  <g id="171">
   <use class="v6300" height="30" transform="rotate(0,1286.48,875.307) scale(1.58467,1.58467) translate(-465.881,-314.179)" width="30" x="1262.705120878704" xlink:href="#Generator:发电机_0" y="851.5367361713461" zvalue="161"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450064416773" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192450064416773"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1286.48,875.307) scale(1.58467,1.58467) translate(-465.881,-314.179)" width="30" x="1262.705120878704" y="851.5367361713461"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="399">
   <use class="v6300" height="30" transform="rotate(0,919.622,790.675) scale(1.69643,1.70833) translate(-367.779,-317.216)" width="28" x="895.8719224784917" xlink:href="#EnergyConsumer:站用变DY接地_0" y="765.0496031746031" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450063237125" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,919.622,790.675) scale(1.69643,1.70833) translate(-367.779,-317.216)" width="28" x="895.8719224784917" y="765.0496031746031"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="207">
   <use class="kv35" height="20" transform="rotate(270,947.889,311) scale(-1.11111,1.66667) translate(-1800.43,-117.733)" width="10" x="942.3333333333334" xlink:href="#GroundDisconnector:地刀_0" y="294.3333333333334" zvalue="19"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450063171589" ObjectName="35kV允南线35117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450063171589"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,947.889,311) scale(-1.11111,1.66667) translate(-1800.43,-117.733)" width="10" x="942.3333333333334" y="294.3333333333334"/></g>
  <g id="125">
   <use class="kv35" height="20" transform="rotate(90,741,234.333) scale(1.11111,1.66667) translate(-73.5444,-87.0667)" width="10" x="735.4444444444445" xlink:href="#GroundDisconnector:地刀_0" y="217.6666666666667" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450062778373" ObjectName="35kV允南线35190接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450062778373"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,741,234.333) scale(1.11111,1.66667) translate(-73.5444,-87.0667)" width="10" x="735.4444444444445" y="217.6666666666667"/></g>
  <g id="126">
   <use class="kv35" height="20" transform="rotate(90,739.889,294.444) scale(1.11111,1.66667) translate(-73.4333,-111.111)" width="10" x="734.3333333333334" xlink:href="#GroundDisconnector:地刀_0" y="277.7777777777777" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450062647301" ObjectName="35kV允南线35197接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450062647301"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,739.889,294.444) scale(1.11111,1.66667) translate(-73.4333,-111.111)" width="10" x="734.3333333333334" y="277.7777777777777"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="206">
   <use class="kv35" height="30" transform="rotate(0,863.598,264.675) scale(-1.11111,-0.814815) translate(-1640,-592.28)" width="15" x="855.2649405153977" xlink:href="#Disconnector:刀闸_0" y="252.4523946217128" zvalue="21"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450063040517" ObjectName="35kV允南线3511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450063040517"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,863.598,264.675) scale(-1.11111,-0.814815) translate(-1640,-592.28)" width="15" x="855.2649405153977" y="252.4523946217128"/></g>
  <g id="119">
   <use class="kv35" height="30" transform="rotate(0,804.712,264.675) scale(-1.11111,-0.814815) translate(-1528.12,-592.28)" width="15" x="796.3790115379934" xlink:href="#Disconnector:刀闸_0" y="252.4523946217128" zvalue="35"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450062909445" ObjectName="35kV允南线3519隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450062909445"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,804.712,264.675) scale(-1.11111,-0.814815) translate(-1528.12,-592.28)" width="15" x="796.3790115379934" y="252.4523946217128"/></g>
  <g id="90">
   <use class="v6300" height="30" transform="rotate(0,621.972,542.175) scale(-1.11111,-0.814815) translate(-1180.91,-1210.35)" width="15" x="613.6381985664667" xlink:href="#Disconnector:刀闸_0" y="529.9523946217128" zvalue="123"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450063433733" ObjectName="6.3kV母线电压互感器6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450063433733"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,621.972,542.175) scale(-1.11111,-0.814815) translate(-1180.91,-1210.35)" width="15" x="613.6381985664667" y="529.9523946217128"/></g>
  <g id="96">
   <use class="v6300" height="30" transform="rotate(0,862.472,544.675) scale(-1.11111,-0.814815) translate(-1637.86,-1215.92)" width="15" x="854.1381985664667" xlink:href="#Disconnector:刀闸_0" y="532.4523946217128" zvalue="129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450064678917" ObjectName="#1主变6.3kV侧6011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450064678917"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,862.472,544.675) scale(-1.11111,-0.814815) translate(-1637.86,-1215.92)" width="15" x="854.1381985664667" y="532.4523946217128"/></g>
  <g id="103">
   <use class="v6300" height="30" transform="rotate(0,706.972,828.925) scale(-1.11111,-0.814815) translate(-1342.41,-1849.02)" width="15" x="698.6381985664668" xlink:href="#Disconnector:刀闸_0" y="816.7023946217128" zvalue="136"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450063630341" ObjectName="#1发电机6913隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450063630341"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,706.972,828.925) scale(-1.11111,-0.814815) translate(-1342.41,-1849.02)" width="15" x="698.6381985664668" y="816.7023946217128"/></g>
  <g id="104">
   <use class="v6300" height="30" transform="rotate(0,554.972,828.925) scale(-1.11111,-0.814815) translate(-1053.61,-1849.02)" width="15" x="546.6381985664667" xlink:href="#Disconnector:刀闸_0" y="816.7023946217128" zvalue="138"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450063695877" ObjectName="#1发电机6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450063695877"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,554.972,828.925) scale(-1.11111,-0.814815) translate(-1053.61,-1849.02)" width="15" x="546.6381985664667" y="816.7023946217128"/></g>
  <g id="105">
   <use class="v6300" height="30" transform="rotate(0,506.972,828.925) scale(-1.11111,-0.814815) translate(-962.413,-1849.02)" width="15" x="498.6381985664667" xlink:href="#Disconnector:刀闸_0" y="816.7023946217128" zvalue="140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450063761413" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450063761413"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,506.972,828.925) scale(-1.11111,-0.814815) translate(-962.413,-1849.02)" width="15" x="498.6381985664667" y="816.7023946217128"/></g>
  <g id="116">
   <use class="v6300" height="30" transform="rotate(0,626.451,687.175) scale(-1.11111,-0.814815) translate(-1189.42,-1533.3)" width="15" x="618.1171888324493" xlink:href="#Disconnector:刀闸_0" y="674.9523946217128" zvalue="153"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450063826949" ObjectName="#1发电机3511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450063826949"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,626.451,687.175) scale(-1.11111,-0.814815) translate(-1189.42,-1533.3)" width="15" x="618.1171888324493" y="674.9523946217128"/></g>
  <g id="131">
   <use class="v6300" height="30" transform="rotate(0,918.451,699.175) scale(-1.11111,-0.814815) translate(-1744.22,-1560.03)" width="15" x="910.1171888324492" xlink:href="#Disconnector:刀闸_0" y="686.9523946217128" zvalue="157"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450063892485" ObjectName="#1站用变6811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450063892485"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,918.451,699.175) scale(-1.11111,-0.814815) translate(-1744.22,-1560.03)" width="15" x="910.1171888324492" y="686.9523946217128"/></g>
  <g id="167">
   <use class="v6300" height="30" transform="rotate(0,1366.97,828.925) scale(-1.11111,-0.814815) translate(-2596.41,-1849.02)" width="15" x="1358.638198566467" xlink:href="#Disconnector:刀闸_0" y="816.7023946217128" zvalue="166"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450064154629" ObjectName="#2发电机6923隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450064154629"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1366.97,828.925) scale(-1.11111,-0.814815) translate(-2596.41,-1849.02)" width="15" x="1358.638198566467" y="816.7023946217128"/></g>
  <g id="166">
   <use class="v6300" height="30" transform="rotate(0,1214.97,828.925) scale(-1.11111,-0.814815) translate(-2307.61,-1849.02)" width="15" x="1206.638198566467" xlink:href="#Disconnector:刀闸_0" y="816.7023946217128" zvalue="168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450064089093" ObjectName="#2发电机6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450064089093"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1214.97,828.925) scale(-1.11111,-0.814815) translate(-2307.61,-1849.02)" width="15" x="1206.638198566467" y="816.7023946217128"/></g>
  <g id="165">
   <use class="v6300" height="30" transform="rotate(0,1166.97,828.925) scale(-1.11111,-0.814815) translate(-2216.41,-1849.02)" width="15" x="1158.638198566467" xlink:href="#Disconnector:刀闸_0" y="816.7023946217128" zvalue="170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450064023557" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450064023557"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1166.97,828.925) scale(-1.11111,-0.814815) translate(-2216.41,-1849.02)" width="15" x="1158.638198566467" y="816.7023946217128"/></g>
  <g id="146">
   <use class="v6300" height="30" transform="rotate(0,1286.45,687.175) scale(-1.11111,-0.814815) translate(-2443.42,-1533.3)" width="15" x="1278.117188832449" xlink:href="#Disconnector:刀闸_0" y="674.9523946217128" zvalue="181"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450063958021" ObjectName="#2发电机3511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450063958021"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1286.45,687.175) scale(-1.11111,-0.814815) translate(-2443.42,-1533.3)" width="15" x="1278.117188832449" y="674.9523946217128"/></g>
  <g id="176">
   <use class="v6300" height="30" transform="rotate(0,1432.22,550.925) scale(-1.11111,-0.814815) translate(-2720.39,-1229.84)" width="15" x="1423.888198566467" xlink:href="#Disconnector:刀闸_0" y="538.7023946217128" zvalue="189"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450064482309" ObjectName="坝区变6.3kV侧6021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450064482309"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1432.22,550.925) scale(-1.11111,-0.814815) translate(-2720.39,-1229.84)" width="15" x="1423.888198566467" y="538.7023946217128"/></g>
  <g id="182">
   <use class="kv10" height="30" transform="rotate(0,1343.6,264.675) scale(-1.11111,-0.814815) translate(-2552,-592.28)" width="15" x="1335.264940515398" xlink:href="#Disconnector:刀闸_0" y="252.4523946217128" zvalue="196"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450064547845" ObjectName="0511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450064547845"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1343.6,264.675) scale(-1.11111,-0.814815) translate(-2552,-592.28)" width="15" x="1335.264940515398" y="252.4523946217128"/></g>
  <g id="183">
   <use class="kv10" height="30" transform="rotate(0,1487.6,263.425) scale(-1.11111,-0.814815) translate(-2825.6,-589.496)" width="15" x="1479.264940515397" xlink:href="#Disconnector:刀闸_0" y="251.2023946217127" zvalue="198"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450064613381" ObjectName="0521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450064613381"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1487.6,263.425) scale(-1.11111,-0.814815) translate(-2825.6,-589.496)" width="15" x="1479.264940515397" y="251.2023946217127"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="77">
   <path class="kv35" d="M 863.5 349.1 L 863.5 276.49" stroke-width="1" zvalue="32"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="418@0" LinkObjectIDznd="206@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 863.5 349.1 L 863.5 276.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv35" d="M 863.53 252.66 L 863.53 188.52" stroke-width="1" zvalue="33"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@1" LinkObjectIDznd="156@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 863.53 252.66 L 863.53 188.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv35" d="M 931.64 311.06 L 863.5 311.06" stroke-width="1" zvalue="34"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="77" MaxPinNum="2"/>
   </metadata>
  <path d="M 931.64 311.06 L 863.5 311.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv35" d="M 804.61 308.33 L 804.61 276.49" stroke-width="1" zvalue="38"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="119@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 804.61 308.33 L 804.61 276.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv35" d="M 756.14 294.5 L 804.61 294.5" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="74" MaxPinNum="2"/>
   </metadata>
  <path d="M 756.14 294.5 L 804.61 294.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv35" d="M 863.59 402.58 L 863.62 370.34" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@0" LinkObjectIDznd="418@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 863.59 402.58 L 863.62 370.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv35" d="M 863.53 215.5 L 804.64 215.5 L 804.64 252.66" stroke-width="1" zvalue="91"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76" LinkObjectIDznd="119@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 863.53 215.5 L 804.64 215.5 L 804.64 252.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv35" d="M 757.25 234.39 L 804.64 234.39" stroke-width="1" zvalue="92"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="52" MaxPinNum="2"/>
   </metadata>
  <path d="M 757.25 234.39 L 804.64 234.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="v6300" d="M 621.9 472.93 L 621.9 530.16" stroke-width="1" zvalue="125"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="90@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 621.9 472.93 L 621.9 530.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="v6300" d="M 621.87 553.99 L 621.87 633.54" stroke-width="1" zvalue="126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="410@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 621.87 553.99 L 621.87 633.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="v6300" d="M 708.69 887.69 L 708.69 840.74" stroke-width="1" zvalue="141"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="103@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 708.69 887.69 L 708.69 840.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="v6300" d="M 706.9 816.91 L 706.9 783.5 L 626.48 783.5" stroke-width="1" zvalue="142"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@1" LinkObjectIDznd="115" MaxPinNum="2"/>
   </metadata>
  <path d="M 706.9 816.91 L 706.9 783.5 L 626.48 783.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="v6300" d="M 626.48 783.5 L 506.9 783.5 L 506.9 816.91" stroke-width="1" zvalue="143"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107" LinkObjectIDznd="105@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.48 783.5 L 506.9 783.5 L 506.9 816.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="v6300" d="M 506.87 840.74 L 506.87 883.69" stroke-width="1" zvalue="144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="133@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 506.87 840.74 L 506.87 883.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="v6300" d="M 554.9 816.91 L 554.9 783.5" stroke-width="1" zvalue="145"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@1" LinkObjectIDznd="108" MaxPinNum="2"/>
   </metadata>
  <path d="M 554.9 816.91 L 554.9 783.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="v6300" d="M 556.14 887.69 L 556.14 840.74" stroke-width="1" zvalue="146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136@0" LinkObjectIDznd="104@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 556.14 887.69 L 556.14 840.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="v6300" d="M 626.48 747.84 L 626.48 851.93" stroke-width="1" zvalue="151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@1" LinkObjectIDznd="212@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.48 747.84 L 626.48 851.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="v6300" d="M 626.38 633.54 L 626.38 675.16" stroke-width="1" zvalue="154"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="410@1" LinkObjectIDznd="116@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.38 633.54 L 626.38 675.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="v6300" d="M 626.35 698.99 L 626.35 726.6" stroke-width="1" zvalue="155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="113@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.35 698.99 L 626.35 726.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="v6300" d="M 919.78 766.07 L 919.78 710.99" stroke-width="1" zvalue="158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="399@0" LinkObjectIDznd="131@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.78 766.07 L 919.78 710.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="v6300" d="M 918.38 687.16 L 918.38 633.54" stroke-width="1" zvalue="159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@1" LinkObjectIDznd="410@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 918.38 687.16 L 918.38 633.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="v6300" d="M 1368.69 887.69 L 1368.69 840.74" stroke-width="1" zvalue="171"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@0" LinkObjectIDznd="167@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1368.69 887.69 L 1368.69 840.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="v6300" d="M 1366.9 816.91 L 1366.9 783.5 L 1286.48 783.5" stroke-width="1" zvalue="173"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="167@1" LinkObjectIDznd="149" MaxPinNum="2"/>
   </metadata>
  <path d="M 1366.9 816.91 L 1366.9 783.5 L 1286.48 783.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="v6300" d="M 1286.48 783.5 L 1166.9 783.5 L 1166.9 816.91" stroke-width="1" zvalue="174"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="149" LinkObjectIDznd="165@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1286.48 783.5 L 1166.9 783.5 L 1166.9 816.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="v6300" d="M 1166.87 840.74 L 1166.87 883.69" stroke-width="1" zvalue="175"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="170@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1166.87 840.74 L 1166.87 883.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="v6300" d="M 1214.9 816.91 L 1214.9 783.5" stroke-width="1" zvalue="176"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@1" LinkObjectIDznd="160" MaxPinNum="2"/>
   </metadata>
  <path d="M 1214.9 816.91 L 1214.9 783.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="v6300" d="M 1216.14 887.69 L 1216.14 840.74" stroke-width="1" zvalue="177"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@0" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1216.14 887.69 L 1216.14 840.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="v6300" d="M 1286.48 747.84 L 1286.48 851.93" stroke-width="1" zvalue="180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@1" LinkObjectIDznd="171@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1286.48 747.84 L 1286.48 851.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="v6300" d="M 1286.38 633.54 L 1286.38 675.16" stroke-width="1" zvalue="182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="410@3" LinkObjectIDznd="146@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1286.38 633.54 L 1286.38 675.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="v6300" d="M 1286.35 698.99 L 1286.35 726.6" stroke-width="1" zvalue="184"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="151@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1286.35 698.99 L 1286.35 726.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="kv10" d="M 1431.59 403.83 L 1431.59 346.75" stroke-width="1" zvalue="194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="179@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1431.59 403.83 L 1431.59 346.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="184">
   <path class="kv10" d="M 1343.5 276.49 L 1343.5 346.75" stroke-width="1" zvalue="199"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="179@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1343.5 276.49 L 1343.5 346.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv10" d="M 1487.5 275.24 L 1487.5 346.75" stroke-width="1" zvalue="200"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@0" LinkObjectIDznd="179@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1487.5 275.24 L 1487.5 346.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv10" d="M 1343.53 252.66 L 1343.53 190.5" stroke-width="1" zvalue="201"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@1" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 1343.53 252.66 L 1343.53 190.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv10" d="M 1487.53 251.41 L 1487.53 200.5" stroke-width="1" zvalue="202"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@1" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 1487.53 251.41 L 1487.53 200.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="v6300" d="M 863.53 493.76 L 863.53 532.66" stroke-width="1" zvalue="204"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@1" LinkObjectIDznd="96@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 863.53 493.76 L 863.53 532.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="v6300" d="M 862.37 556.49 L 862.37 633.54" stroke-width="1" zvalue="205"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="410@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 862.37 556.49 L 862.37 633.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="v6300" d="M 1431.53 495.01 L 1431.53 538.91" stroke-width="1" zvalue="206"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@1" LinkObjectIDznd="176@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1431.53 495.01 L 1431.53 538.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="v6300" d="M 1432.12 562.74 L 1432.12 633.54" stroke-width="1" zvalue="207"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@0" LinkObjectIDznd="410@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1432.12 562.74 L 1432.12 633.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="120">
   <use class="kv35" height="30" transform="rotate(0,806.236,330) scale(1.43056,1.55556) translate(-236.195,-109.524)" width="30" x="784.7777777777778" xlink:href="#Accessory:避雷器PT带熔断器_0" y="306.6666666666666" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450062843909" ObjectName="35kV南片河电站线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,806.236,330) scale(1.43056,1.55556) translate(-236.195,-109.524)" width="30" x="784.7777777777778" y="306.6666666666666"/></g>
  <g id="133">
   <use class="v6300" height="29" transform="rotate(0,506.5,901.5) scale(1.25,-1.25) translate(-97.55,-1619.08)" width="30" x="487.75" xlink:href="#Accessory:PT12321_0" y="883.375" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450062516229" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,506.5,901.5) scale(1.25,-1.25) translate(-97.55,-1619.08)" width="30" x="487.75" y="883.375"/></g>
  <g id="136">
   <use class="v6300" height="29" transform="rotate(0,556.141,905.5) scale(1.25,-1.25) translate(-107.478,-1626.28)" width="30" x="537.3910256410256" xlink:href="#Accessory:PT12321_0" y="887.375" zvalue="70"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450063499269" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,556.141,905.5) scale(1.25,-1.25) translate(-107.478,-1626.28)" width="30" x="537.3910256410256" y="887.375"/></g>
  <g id="86">
   <use class="v6300" height="75" transform="rotate(0,623.125,439.545) scale(1.01786,1.01786) translate(-10.307,-7.04167)" width="70" x="587.5" xlink:href="#Accessory:五卷PT带熔断_0" y="401.375" zvalue="119"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450063368197" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="75" opacity="0" stroke="white" transform="rotate(0,623.125,439.545) scale(1.01786,1.01786) translate(-10.307,-7.04167)" width="70" x="587.5" y="401.375"/></g>
  <g id="102">
   <use class="v6300" height="30" transform="rotate(0,708.75,906) scale(1.25,1.25) translate(-138,-177.45)" width="30" x="690" xlink:href="#Accessory:PT789_0" y="887.25" zvalue="134"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450063564805" ObjectName="#1发电机PT3"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,708.75,906) scale(1.25,1.25) translate(-138,-177.45)" width="30" x="690" y="887.25"/></g>
  <g id="170">
   <use class="v6300" height="29" transform="rotate(0,1166.5,901.5) scale(1.25,-1.25) translate(-229.55,-1619.08)" width="30" x="1147.75" xlink:href="#Accessory:PT12321_0" y="883.375" zvalue="163"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450064351237" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1166.5,901.5) scale(1.25,-1.25) translate(-229.55,-1619.08)" width="30" x="1147.75" y="883.375"/></g>
  <g id="169">
   <use class="v6300" height="29" transform="rotate(0,1216.14,905.5) scale(1.25,-1.25) translate(-239.478,-1626.28)" width="30" x="1197.391025641026" xlink:href="#Accessory:PT12321_0" y="887.375" zvalue="164"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450064285701" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1216.14,905.5) scale(1.25,-1.25) translate(-239.478,-1626.28)" width="30" x="1197.391025641026" y="887.375"/></g>
  <g id="168">
   <use class="v6300" height="30" transform="rotate(0,1368.75,906) scale(1.25,1.25) translate(-270,-177.45)" width="30" x="1350" xlink:href="#Accessory:PT789_0" y="887.25" zvalue="165"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450064220165" ObjectName="#2发电机PT3"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1368.75,906) scale(1.25,1.25) translate(-270,-177.45)" width="30" x="1350" y="887.25"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="222">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="222" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,155.611,273.917) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="278.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126916915204" ObjectName="GEN_PSUM"/>
   </metadata>
  </g>
  <g id="221">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="221" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,333.222,274.917) scale(1,1) translate(0,0)" writing-mode="lr" x="333.38" xml:space="preserve" y="279.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126916980740" ObjectName="GEN_QSUM"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="53" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,155.611,298.917) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="303.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126916784132" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="50">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,333.222,299.917) scale(1,1) translate(0,0)" writing-mode="lr" x="333.38" xml:space="preserve" y="304.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126916849668" ObjectName="LOAD_QSUM"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.611,391.139) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="396.05" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,333.611,391.139) scale(1,1) translate(0,0)" writing-mode="lr" x="333.77" xml:space="preserve" y="396.05" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="雨量采集"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,155.611,322.917) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="327.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126916784132" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,335.611,321.917) scale(1,1) translate(0,0)" writing-mode="lr" x="335.77" xml:space="preserve" y="326.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126916784132" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="19" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.611,415.139) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="420.05" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="坝下水位"/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,335.611,345.917) scale(1,1) translate(0,0)" writing-mode="lr" x="335.77" xml:space="preserve" y="350.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126909837316" ObjectName="F"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="63" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,863.53,60.625) scale(1,1) translate(0,0)" writing-mode="lr" x="863.0599999999999" xml:space="preserve" y="65.40000000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126907150340" ObjectName="P"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="64" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,863.53,84.625) scale(1,1) translate(0,0)" writing-mode="lr" x="863.0599999999999" xml:space="preserve" y="89.40000000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126907215876" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="65" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,864.53,108.625) scale(1,1) translate(0,0)" writing-mode="lr" x="864.0599999999999" xml:space="preserve" y="113.4" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126907281412" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="66" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,967.53,358.238) scale(1,1) translate(0,0)" writing-mode="lr" x="966.98" xml:space="preserve" y="362.52" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126909968388" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="68" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,970.53,385.238) scale(1,1) translate(0,0)" writing-mode="lr" x="969.98" xml:space="preserve" y="389.52" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126910033924" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="69">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="69" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,970.53,522.916) scale(1,1) translate(0,0)" writing-mode="lr" x="969.98" xml:space="preserve" y="527.1900000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126910099460" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="70">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="70" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,970.53,551.916) scale(1,1) translate(0,0)" writing-mode="lr" x="969.98" xml:space="preserve" y="556.1900000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126910164996" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="71">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="71" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,970.53,412.238) scale(1,1) translate(0,-1.75299e-13)" writing-mode="lr" x="969.98" xml:space="preserve" y="416.52" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126910230532" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="73" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,973.53,583.916) scale(1,1) translate(0,0)" writing-mode="lr" x="972.98" xml:space="preserve" y="588.1900000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126910558214" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="78" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,623.475,948.577) scale(1,1) translate(0,0)" writing-mode="lr" x="622.92" xml:space="preserve" y="952.85" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126908788742" ObjectName="P"/>
   </metadata>
  </g>
  <g id="79">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="79" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,623.475,976.577) scale(1,1) translate(0,0)" writing-mode="lr" x="622.92" xml:space="preserve" y="980.85" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126908854276" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="80" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,626.475,1007.58) scale(1,1) translate(0,0)" writing-mode="lr" x="625.92" xml:space="preserve" y="1011.85" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126908919812" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="88" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1278.48,952.577) scale(1,1) translate(0,0)" writing-mode="lr" x="1277.92" xml:space="preserve" y="956.85" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126913572868" ObjectName="P"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="89" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1278.48,980.577) scale(1,1) translate(0,0)" writing-mode="lr" x="1277.92" xml:space="preserve" y="984.85" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126913638404" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="91" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1278.48,1008.58) scale(1,1) translate(0,0)" writing-mode="lr" x="1277.92" xml:space="preserve" y="1012.85" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126913703940" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="95" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,613.256,282.036) scale(1,1) translate(0,-2.34955e-13)" writing-mode="lr" x="612.79" xml:space="preserve" y="286.81" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126909444102" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="97" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,613.256,310.036) scale(1,1) translate(0,0)" writing-mode="lr" x="612.79" xml:space="preserve" y="314.81" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126909509640" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="98" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,614.256,335.036) scale(1,1) translate(0,0)" writing-mode="lr" x="613.79" xml:space="preserve" y="339.81" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126909575174" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="100" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,478.256,657.536) scale(1,1) translate(2.88607e-13,-1.42116e-13)" writing-mode="lr" x="477.79" xml:space="preserve" y="662.3099999999999" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126909640710" ObjectName="Uca"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="218">
   <use height="30" transform="rotate(0,330.673,200.107) scale(0.708333,0.665547) translate(131.784,95.5419)" width="30" x="320.05" xlink:href="#State:红绿圆(方形)_0" y="190.12" zvalue="217"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374892400643" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,330.673,200.107) scale(0.708333,0.665547) translate(131.784,95.5419)" width="30" x="320.05" y="190.12"/></g>
  <g id="215">
   <use height="30" transform="rotate(0,235.048,200.107) scale(0.708333,0.665547) translate(92.4093,95.5419)" width="30" x="224.42" xlink:href="#State:红绿圆(方形)_0" y="190.12" zvalue="218"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,235.048,200.107) scale(0.708333,0.665547) translate(92.4093,95.5419)" width="30" x="224.42" y="190.12"/></g>
 </g>
</svg>