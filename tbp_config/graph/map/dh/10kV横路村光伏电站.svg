<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549594488834" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:杆或塔_0" viewBox="0,0,15,15">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="7.5"/>
   <ellipse cx="7.46" cy="7.68" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0.25" y2="25.75"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_0" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="3.333333333333332" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="24.08333333333333" y2="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="7" y1="19.5" y2="24.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_1" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="18.25" y2="32.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_2" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="4" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="10" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="25.83333333333333" y2="32.41666666666667"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2027.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id=":光伏模组_0" viewBox="0,0,80,80">
   <use terminal-index="0" type="0" x="23.86666666666666" xlink:href="#terminal" y="70.93333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="50.8" x2="57.2" y1="10.13333333333333" y2="10.13333333333333"/>
   <path d="M 36.7489 56.5767 L 38.0132 59.1405 L 39.2774 56.5767" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.6" x2="50.66666666666667" y1="21.33333333333333" y2="11.33333333333333"/>
   <path d="M 52.4 16.5333 L 49.2 16.5333 L 49.2 27.7333 L 52.4 27.7333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(170,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.2" x2="55.2" y1="32.53333333333333" y2="43.73333333333333"/>
   <path d="M 38 45.3333 L 38 35.7333 L 66.8 35.7333 L 66.8 30.9333" fill="none" stroke="rgb(170,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="73.59999999999999" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,40.13) scale(1,1) translate(0,0)" width="68.8" x="5.6" y="3.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="66.8" x2="66.8" y1="60.13333333333333" y2="64.93333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="5" x1="14" x2="33.2" y1="70.93333333333332" y2="70.93333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="62" x2="66.8" y1="60.13333333333333" y2="60.13333333333333"/>
   <path d="M 53.7333 10.4 L 53.7333 5.6 L 23.3333 5.6 L 23.7333 71.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="6 2" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.2" x2="55.2" y1="68.13333333333334" y2="79.20000000000002"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="23.6" y1="27.73333333333334" y2="27.73333333333334"/>
   <rect fill-opacity="0" height="6.77" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,37.91,50.21) scale(1,1) translate(0,0)" width="3.37" x="36.22" y="46.82"/>
   <path d="M 36.7489 61.0634 L 38.0132 63.6272 L 39.2774 61.0634" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="65.07886701035602" x2="68.00570387390415" y1="28.93345174664175" y2="28.93345174664175"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="37.9078029848685" x2="37.9078029848685" y1="55.40157383031868" y2="45.46666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.20202391636546" x2="14.96424980017045" y1="38.63150148536967" y2="35.9076739581179"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.20202391636535" x2="14.20202391636535" y1="27.59999999999994" y2="38.17753023082763"/>
   <rect fill-opacity="0" height="8.44" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.27,38.31) scale(1,1) translate(0,0)" width="5.34" x="11.6" y="34.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.20202391636544" x2="13.43979803256045" y1="38.63150148536967" y2="35.9076739581179"/>
   <ellipse cx="55.25" cy="50.35" fill-opacity="0" rx="6.61" ry="6.88" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="63.2906587876924" x2="70.00000000000001" y1="17.85532825265332" y2="17.85532825265332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="64.40888232307701" x2="68.88177646461541" y1="16.86538557734767" y2="16.86538557734767"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="65.40515432248043" x2="67.64160139324963" y1="15.89299537279264" y2="15.89299537279264"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.21175520726185" x2="14.21175520726185" y1="42.19334811824088" y2="44.59300008617765"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="66.58435362585561" x2="66.58435362585561" y1="17.91050011524912" y2="21.03483990886042"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="63.06666666666666" x2="66.60326121012066" y1="27.83642232472767" y2="21.07872108573694"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="66.5726562972967" x2="66.5726562972967" y1="28.99881464929833" y2="31.03687217788674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="52.85048888888888" x2="54.58955061728395" y1="51.30912086720867" y2="47.91573333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="58.06767407407408" x2="56.32861234567901" y1="51.30912086720867" y2="47.91573333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="52.85048888888888" x2="58.06767407407408" y1="51.30912086720867" y2="51.30912086720867"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.29388998943305" x2="12.96402824408577" y1="44.28768815766198" y2="44.28768815766198"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.83991873489108" x2="13.41799949862773" y1="44.96864503947492" y2="44.96864503947492"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.52213885671171" x2="13.73577937680711" y1="45.64960192128786" y2="45.64960192128786"/>
   <ellipse cx="55.29" cy="61.19" fill-opacity="0" rx="6.67" ry="6.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.25184255358918" x2="51.41184255358919" y1="60.85264603444045" y2="58.87258205947708"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.25184255358919" x2="55.25184255358919" y1="64.81277398436721" y2="60.85264603444048"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.2518425535892" x2="59.09184255358919" y1="60.85264603444045" y2="58.87258205947708"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="70.20141457174999" x2="63.35850922025583" y1="64.84471210629269" y2="64.84471210629269"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="68.86808123841665" x2="64.69184255358917" y1="66.84471210629269" y2="66.84471210629269"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="67.93474790508333" x2="65.62517588692251" y1="68.84471210629269" y2="68.84471210629269"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.06666666666667" x2="55.06666666666667" y1="32.26666666666668" y2="19.86666666666667"/>
   <rect fill-opacity="0" height="8.449999999999999" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,54.93,25.92) scale(1,1) translate(0,0)" width="4.27" x="52.8" y="21.69"/>
   <ellipse cx="37.99" cy="57.88" fill-opacity="0" rx="2.94" ry="2.98" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="37.99" cy="62.37" fill-opacity="0" rx="2.94" ry="2.98" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="54.24" cy="11.23" fill-opacity="0" rx="1.1" ry="1.1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV横路村光伏电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="42.43" xlink:href="logo.png" y="42.86"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,191.429,74.8571) scale(1,1) translate(0,0)" writing-mode="lr" x="191.43" xml:space="preserve" y="78.36" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,193.929,72.5475) scale(1,1) translate(0,0)" writing-mode="lr" x="193.93" xml:space="preserve" y="81.55" zvalue="5">10kV横路村光伏电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="77" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,82.9286,313.857) scale(1,1) translate(0,0)" width="97" x="34.43" y="301.86" zvalue="10"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.9286,313.857) scale(1,1) translate(0,0)" writing-mode="lr" x="82.93000000000001" xml:space="preserve" y="318.36" zvalue="10">全站公用</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54.65093316591856" x2="102.9956331659187" y1="440.5237863630023" y2="440.5237863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54.65093316591856" x2="102.9956331659187" y1="478.0137863630023" y2="478.0137863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54.65093316591856" x2="54.65093316591856" y1="440.5237863630023" y2="478.0137863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9956331659187" x2="102.9956331659187" y1="440.5237863630023" y2="478.0137863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9959331659186" x2="165.1042331659187" y1="440.5237863630023" y2="440.5237863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9959331659186" x2="165.1042331659187" y1="478.0137863630023" y2="478.0137863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9959331659186" x2="102.9959331659186" y1="440.5237863630023" y2="478.0137863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1042331659187" x2="165.1042331659187" y1="440.5237863630023" y2="478.0137863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1037331659186" x2="228.4285331659187" y1="440.5237863630023" y2="440.5237863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1037331659186" x2="228.4285331659187" y1="478.0137863630023" y2="478.0137863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1037331659186" x2="165.1037331659186" y1="440.5237863630023" y2="478.0137863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4285331659187" x2="228.4285331659187" y1="440.5237863630023" y2="478.0137863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4284331659186" x2="290.5367331659186" y1="440.5237863630023" y2="440.5237863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4284331659186" x2="290.5367331659186" y1="478.0137863630023" y2="478.0137863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4284331659186" x2="228.4284331659186" y1="440.5237863630023" y2="478.0137863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="290.5367331659186" y1="440.5237863630023" y2="478.0137863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="352.6450331659187" y1="440.5237863630023" y2="440.5237863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="352.6450331659187" y1="478.0137863630023" y2="478.0137863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="290.5367331659186" y1="440.5237863630023" y2="478.0137863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="352.6450331659187" x2="352.6450331659187" y1="440.5237863630023" y2="478.0137863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54.65093316591856" x2="102.9956331659187" y1="478.0138863630023" y2="478.0138863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54.65093316591856" x2="102.9956331659187" y1="502.1824863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54.65093316591856" x2="54.65093316591856" y1="478.0138863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9956331659187" x2="102.9956331659187" y1="478.0138863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9959331659186" x2="165.1042331659187" y1="478.0138863630023" y2="478.0138863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9959331659186" x2="165.1042331659187" y1="502.1824863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9959331659186" x2="102.9959331659186" y1="478.0138863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1042331659187" x2="165.1042331659187" y1="478.0138863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1037331659186" x2="228.4285331659187" y1="478.0138863630023" y2="478.0138863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1037331659186" x2="228.4285331659187" y1="502.1824863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1037331659186" x2="165.1037331659186" y1="478.0138863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4285331659187" x2="228.4285331659187" y1="478.0138863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4284331659186" x2="290.5367331659186" y1="478.0138863630023" y2="478.0138863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4284331659186" x2="290.5367331659186" y1="502.1824863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4284331659186" x2="228.4284331659186" y1="478.0138863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="290.5367331659186" y1="478.0138863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="352.6450331659187" y1="478.0138863630023" y2="478.0138863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="352.6450331659187" y1="502.1824863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="290.5367331659186" y1="478.0138863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="352.6450331659187" x2="352.6450331659187" y1="478.0138863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54.65093316591856" x2="102.9956331659187" y1="502.1824863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54.65093316591856" x2="102.9956331659187" y1="526.3510863630023" y2="526.3510863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54.65093316591856" x2="54.65093316591856" y1="502.1824863630023" y2="526.3510863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9956331659187" x2="102.9956331659187" y1="502.1824863630023" y2="526.3510863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9959331659186" x2="165.1042331659187" y1="502.1824863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9959331659186" x2="165.1042331659187" y1="526.3510863630023" y2="526.3510863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9959331659186" x2="102.9959331659186" y1="502.1824863630023" y2="526.3510863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1042331659187" x2="165.1042331659187" y1="502.1824863630023" y2="526.3510863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1037331659186" x2="228.4285331659187" y1="502.1824863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1037331659186" x2="228.4285331659187" y1="526.3510863630023" y2="526.3510863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1037331659186" x2="165.1037331659186" y1="502.1824863630023" y2="526.3510863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4285331659187" x2="228.4285331659187" y1="502.1824863630023" y2="526.3510863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4284331659186" x2="290.5367331659186" y1="502.1824863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4284331659186" x2="290.5367331659186" y1="526.3510863630023" y2="526.3510863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4284331659186" x2="228.4284331659186" y1="502.1824863630023" y2="526.3510863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="290.5367331659186" y1="502.1824863630023" y2="526.3510863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="352.6450331659187" y1="502.1824863630023" y2="502.1824863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="352.6450331659187" y1="526.3510863630023" y2="526.3510863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="290.5367331659186" y1="502.1824863630023" y2="526.3510863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="352.6450331659187" x2="352.6450331659187" y1="502.1824863630023" y2="526.3510863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54.65093316591856" x2="102.9956331659187" y1="526.3511263630023" y2="526.3511263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54.65093316591856" x2="102.9956331659187" y1="550.5197263630023" y2="550.5197263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54.65093316591856" x2="54.65093316591856" y1="526.3511263630023" y2="550.5197263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9956331659187" x2="102.9956331659187" y1="526.3511263630023" y2="550.5197263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9959331659186" x2="165.1042331659187" y1="526.3511263630023" y2="526.3511263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9959331659186" x2="165.1042331659187" y1="550.5197263630023" y2="550.5197263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9959331659186" x2="102.9959331659186" y1="526.3511263630023" y2="550.5197263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1042331659187" x2="165.1042331659187" y1="526.3511263630023" y2="550.5197263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1037331659186" x2="228.4285331659187" y1="526.3511263630023" y2="526.3511263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1037331659186" x2="228.4285331659187" y1="550.5197263630023" y2="550.5197263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1037331659186" x2="165.1037331659186" y1="526.3511263630023" y2="550.5197263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4285331659187" x2="228.4285331659187" y1="526.3511263630023" y2="550.5197263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4284331659186" x2="290.5367331659186" y1="526.3511263630023" y2="526.3511263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4284331659186" x2="290.5367331659186" y1="550.5197263630023" y2="550.5197263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4284331659186" x2="228.4284331659186" y1="526.3511263630023" y2="550.5197263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="290.5367331659186" y1="526.3511263630023" y2="550.5197263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="352.6450331659187" y1="526.3511263630023" y2="526.3511263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="352.6450331659187" y1="550.5197263630023" y2="550.5197263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="290.5367331659186" y1="526.3511263630023" y2="550.5197263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="352.6450331659187" x2="352.6450331659187" y1="526.3511263630023" y2="550.5197263630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54.65093316591856" x2="102.9956331659187" y1="550.5198863630023" y2="550.5198863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54.65093316591856" x2="102.9956331659187" y1="574.6884863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54.65093316591856" x2="54.65093316591856" y1="550.5198863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9956331659187" x2="102.9956331659187" y1="550.5198863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9959331659186" x2="165.1042331659187" y1="550.5198863630023" y2="550.5198863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9959331659186" x2="165.1042331659187" y1="574.6884863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9959331659186" x2="102.9959331659186" y1="550.5198863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1042331659187" x2="165.1042331659187" y1="550.5198863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1037331659186" x2="228.4285331659187" y1="550.5198863630023" y2="550.5198863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1037331659186" x2="228.4285331659187" y1="574.6884863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1037331659186" x2="165.1037331659186" y1="550.5198863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4285331659187" x2="228.4285331659187" y1="550.5198863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4284331659186" x2="290.5367331659186" y1="550.5198863630023" y2="550.5198863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4284331659186" x2="290.5367331659186" y1="574.6884863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4284331659186" x2="228.4284331659186" y1="550.5198863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="290.5367331659186" y1="550.5198863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="352.6450331659187" y1="550.5198863630023" y2="550.5198863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="352.6450331659187" y1="574.6884863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="290.5367331659186" y1="550.5198863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="352.6450331659187" x2="352.6450331659187" y1="550.5198863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54.65093316591856" x2="102.9956331659187" y1="574.6884863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54.65093316591856" x2="102.9956331659187" y1="598.8570863630023" y2="598.8570863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54.65093316591856" x2="54.65093316591856" y1="574.6884863630023" y2="598.8570863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9956331659187" x2="102.9956331659187" y1="574.6884863630023" y2="598.8570863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9959331659186" x2="165.1042331659187" y1="574.6884863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9959331659186" x2="165.1042331659187" y1="598.8570863630023" y2="598.8570863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="102.9959331659186" x2="102.9959331659186" y1="574.6884863630023" y2="598.8570863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1042331659187" x2="165.1042331659187" y1="574.6884863630023" y2="598.8570863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1037331659186" x2="228.4285331659187" y1="574.6884863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1037331659186" x2="228.4285331659187" y1="598.8570863630023" y2="598.8570863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.1037331659186" x2="165.1037331659186" y1="574.6884863630023" y2="598.8570863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4285331659187" x2="228.4285331659187" y1="574.6884863630023" y2="598.8570863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4284331659186" x2="290.5367331659186" y1="574.6884863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4284331659186" x2="290.5367331659186" y1="598.8570863630023" y2="598.8570863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="228.4284331659186" x2="228.4284331659186" y1="574.6884863630023" y2="598.8570863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="290.5367331659186" y1="574.6884863630023" y2="598.8570863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="352.6450331659187" y1="574.6884863630023" y2="574.6884863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="352.6450331659187" y1="598.8570863630023" y2="598.8570863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.5367331659186" x2="290.5367331659186" y1="574.6884863630023" y2="598.8570863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="352.6450331659187" x2="352.6450331659187" y1="574.6884863630023" y2="598.8570863630023"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571786" x2="184.4285714285718" y1="158.8571428571428" y2="158.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571786" x2="184.4285714285718" y1="184.8571428571428" y2="184.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571786" x2="3.428571428571786" y1="158.8571428571428" y2="184.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="184.4285714285718" y1="158.8571428571428" y2="184.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="365.4285714285718" y1="158.8571428571428" y2="158.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="365.4285714285718" y1="184.8571428571428" y2="184.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="184.4285714285718" y1="158.8571428571428" y2="184.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285718" x2="365.4285714285718" y1="158.8571428571428" y2="184.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571786" x2="184.4285714285718" y1="184.8571428571428" y2="184.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571786" x2="184.4285714285718" y1="209.1071428571428" y2="209.1071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571786" x2="3.428571428571786" y1="184.8571428571428" y2="209.1071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="184.4285714285718" y1="184.8571428571428" y2="209.1071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="365.4285714285718" y1="184.8571428571428" y2="184.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="365.4285714285718" y1="209.1071428571428" y2="209.1071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="184.4285714285718" y1="184.8571428571428" y2="209.1071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285718" x2="365.4285714285718" y1="184.8571428571428" y2="209.1071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571786" x2="184.4285714285718" y1="209.1071428571428" y2="209.1071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571786" x2="184.4285714285718" y1="231.8571428571428" y2="231.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571786" x2="3.428571428571786" y1="209.1071428571428" y2="231.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="184.4285714285718" y1="209.1071428571428" y2="231.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="365.4285714285718" y1="209.1071428571428" y2="209.1071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="365.4285714285718" y1="231.8571428571428" y2="231.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="184.4285714285718" y1="209.1071428571428" y2="231.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285718" x2="365.4285714285718" y1="209.1071428571428" y2="231.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571786" x2="184.4285714285718" y1="231.8571428571428" y2="231.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571786" x2="184.4285714285718" y1="254.6071428571428" y2="254.6071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571786" x2="3.428571428571786" y1="231.8571428571428" y2="254.6071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="184.4285714285718" y1="231.8571428571428" y2="254.6071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="365.4285714285718" y1="231.8571428571428" y2="231.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="365.4285714285718" y1="254.6071428571428" y2="254.6071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="184.4285714285718" y1="231.8571428571428" y2="254.6071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285718" x2="365.4285714285718" y1="231.8571428571428" y2="254.6071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571786" x2="184.4285714285718" y1="254.6071428571428" y2="254.6071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571786" x2="184.4285714285718" y1="277.3571428571428" y2="277.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571786" x2="3.428571428571786" y1="254.6071428571428" y2="277.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="184.4285714285718" y1="254.6071428571428" y2="277.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="365.4285714285718" y1="254.6071428571428" y2="254.6071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="365.4285714285718" y1="277.3571428571428" y2="277.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285718" x2="184.4285714285718" y1="254.6071428571428" y2="277.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285718" x2="365.4285714285718" y1="254.6071428571428" y2="277.3571428571428"/>
  <line fill="none" id="81" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="376.4285714285716" x2="376.4285714285716" y1="10.85714285714289" y2="1040.857142857143" zvalue="6"/>
  <line fill="none" id="79" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.428571428572468" x2="369.428571428572" y1="146.7276354712253" y2="146.7276354712253" zvalue="8"/>
  <line fill="none" id="78" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.428571428572468" x2="369.428571428572" y1="616.7276354712253" y2="616.7276354712253" zvalue="9"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571786" x2="92.42857142857179" y1="931.8571428571429" y2="931.8571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571786" x2="92.42857142857179" y1="971.0204428571428" y2="971.0204428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571786" x2="2.428571428571786" y1="931.8571428571429" y2="971.0204428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857179" x2="92.42857142857179" y1="931.8571428571429" y2="971.0204428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857179" x2="362.4285714285718" y1="931.8571428571429" y2="931.8571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857179" x2="362.4285714285718" y1="971.0204428571428" y2="971.0204428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857179" x2="92.42857142857179" y1="931.8571428571429" y2="971.0204428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362.4285714285718" x2="362.4285714285718" y1="931.8571428571429" y2="971.0204428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571786" x2="92.42857142857179" y1="971.0204128571429" y2="971.0204128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571786" x2="92.42857142857179" y1="998.9388128571429" y2="998.9388128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571786" x2="2.428571428571786" y1="971.0204128571429" y2="998.9388128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857179" x2="92.42857142857179" y1="971.0204128571429" y2="998.9388128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857179" x2="182.4285714285718" y1="971.0204128571429" y2="971.0204128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857179" x2="182.4285714285718" y1="998.9388128571429" y2="998.9388128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857179" x2="92.42857142857179" y1="971.0204128571429" y2="998.9388128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="182.4285714285718" y1="971.0204128571429" y2="998.9388128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285719" x2="272.4285714285719" y1="971.0204128571429" y2="971.0204128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285719" x2="272.4285714285719" y1="998.9388128571429" y2="998.9388128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285719" x2="182.4285714285719" y1="971.0204128571429" y2="998.9388128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285719" x2="272.4285714285719" y1="971.0204128571429" y2="998.9388128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285718" x2="362.4285714285718" y1="971.0204128571429" y2="971.0204128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285718" x2="362.4285714285718" y1="998.9388128571429" y2="998.9388128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285718" x2="272.4285714285718" y1="971.0204128571429" y2="998.9388128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362.4285714285718" x2="362.4285714285718" y1="971.0204128571429" y2="998.9388128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571786" x2="92.42857142857179" y1="998.9387428571429" y2="998.9387428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571786" x2="92.42857142857179" y1="1026.857142857143" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571786" x2="2.428571428571786" y1="998.9387428571429" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857179" x2="92.42857142857179" y1="998.9387428571429" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857179" x2="182.4285714285718" y1="998.9387428571429" y2="998.9387428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857179" x2="182.4285714285718" y1="1026.857142857143" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857179" x2="92.42857142857179" y1="998.9387428571429" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="182.4285714285718" y1="998.9387428571429" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285719" x2="272.4285714285719" y1="998.9387428571429" y2="998.9387428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285719" x2="272.4285714285719" y1="1026.857142857143" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285719" x2="182.4285714285719" y1="998.9387428571429" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285719" x2="272.4285714285719" y1="998.9387428571429" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285718" x2="362.4285714285718" y1="998.9387428571429" y2="998.9387428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285718" x2="362.4285714285718" y1="1026.857142857143" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285718" x2="272.4285714285718" y1="998.9387428571429" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362.4285714285718" x2="362.4285714285718" y1="998.9387428571429" y2="1026.857142857143"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.4286,951.857) scale(1,1) translate(0,0)" writing-mode="lr" x="47.43" xml:space="preserve" y="957.86" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.4286,985.857) scale(1,1) translate(0,0)" writing-mode="lr" x="44.43" xml:space="preserve" y="991.86" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.429,985.857) scale(1,1) translate(0,0)" writing-mode="lr" x="226.43" xml:space="preserve" y="991.86" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43.4286,1013.86) scale(1,1) translate(0,0)" writing-mode="lr" x="43.43" xml:space="preserve" y="1019.86" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225.429,1013.86) scale(1,1) translate(0,0)" writing-mode="lr" x="225.43" xml:space="preserve" y="1019.86" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" x="130.4375" xml:space="preserve" y="453.859375" zvalue="17">110kV  母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="130.4375" xml:space="preserve" y="469.859375" zvalue="17">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.9286,646.357) scale(1,1) translate(0,0)" writing-mode="lr" x="67.92857142857179" xml:space="preserve" y="650.8571428571428" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,200.827,313.699) scale(1,1) translate(0,0)" writing-mode="lr" x="200.83" xml:space="preserve" y="318.2" zvalue="20">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,305.827,313.699) scale(1,1) translate(0,0)" writing-mode="lr" x="305.83" xml:space="preserve" y="318.2" zvalue="21">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.4286,489.357) scale(1,1) translate(0,0)" writing-mode="lr" x="79.42857142857179" xml:space="preserve" y="493.8571428571429" zvalue="22">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.4286,514.857) scale(1,1) translate(0,0)" writing-mode="lr" x="79.42857142857179" xml:space="preserve" y="519.3571428571429" zvalue="23">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.4286,540.357) scale(1,1) translate(0,0)" writing-mode="lr" x="79.42857142857179" xml:space="preserve" y="544.857142857143" zvalue="24">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.4286,564.857) scale(1,1) translate(0,0)" writing-mode="lr" x="78.42857142857179" xml:space="preserve" y="569.3571428571429" zvalue="25">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.4286,591.357) scale(1,1) translate(0,0)" writing-mode="lr" x="79.42857142857179" xml:space="preserve" y="595.8571428571429" zvalue="26">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.483,953.857) scale(1,1) translate(0,0)" writing-mode="lr" x="227.48" xml:space="preserve" y="959.86" zvalue="27">HLCGF-01-2018</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,41.4286,172.857) scale(1,1) translate(0,0)" writing-mode="lr" x="41.43" xml:space="preserve" y="178.36" zvalue="28">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,221.429,172.857) scale(1,1) translate(0,0)" writing-mode="lr" x="221.43" xml:space="preserve" y="178.36" zvalue="29">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,512.804,537.357) scale(1,1) translate(0,0)" writing-mode="lr" x="512.8" xml:space="preserve" y="541.86" zvalue="32">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,818.179,820.268) scale(1,1) translate(0,0)" writing-mode="lr" x="818.1799999999999" xml:space="preserve" y="824.77" zvalue="33">10kV#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1056.43,817.232) scale(1,1) translate(0,0)" writing-mode="lr" x="1056.43" xml:space="preserve" y="821.73" zvalue="35">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,626.554,678.607) scale(1,1) translate(0,0)" writing-mode="lr" x="626.55" xml:space="preserve" y="683.11" zvalue="37">013</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,847.179,489.857) scale(1,1) translate(0,0)" writing-mode="lr" x="847.1799999999999" xml:space="preserve" y="494.36" zvalue="40">011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,846.554,647.357) scale(1,1) translate(0,0)" writing-mode="lr" x="846.55" xml:space="preserve" y="651.86" zvalue="44">0121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1017.55,652.357) scale(1,1) translate(-2.22612e-13,-5.7186e-13)" writing-mode="lr" x="1017.55" xml:space="preserve" y="656.86" zvalue="47">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,788.179,360.107) scale(1,1) translate(0,0)" writing-mode="lr" x="788.1799999999999" xml:space="preserve" y="364.61" zvalue="51">A012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,844.679,311.357) scale(1,1) translate(0,0)" writing-mode="lr" x="844.6799999999999" xml:space="preserve" y="315.86" zvalue="53">A01</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,665.429,743.92) scale(1,1) translate(0,0)" writing-mode="lr" x="665.4299999999999" xml:space="preserve" y="748.42" zvalue="56">01367</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1445.18,258.482) scale(1,1) translate(0,0)" writing-mode="lr" x="1445.18" xml:space="preserve" y="262.98" zvalue="60">#1箱变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,795.025,217.466) scale(1,1) translate(0,0)" writing-mode="lr" x="795.02" xml:space="preserve" y="221.97" zvalue="65">#2杆</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,787.554,158.357) scale(1,1) translate(0,0)" writing-mode="lr" x="787.55" xml:space="preserve" y="162.86" zvalue="68">A0R1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,820.598,52.2129) scale(1,1) translate(0,0)" writing-mode="lr" x="820.6" xml:space="preserve" y="56.71" zvalue="77">#44杆</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,701.714,387.232) scale(1,1) translate(-1.37002e-13,0)" writing-mode="lr" x="701.71" xml:space="preserve" y="391.73" zvalue="84">10kV横路村光伏电站线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1033.93,68.9821) scale(1,1) translate(0,0)" writing-mode="lr" x="1033.93" xml:space="preserve" y="73.48" zvalue="85">35kV沙坝变/10kV沙横线#44杆</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,995.286,1018.21) scale(1,1) translate(0,-2.22917e-13)" writing-mode="lr" x="995.29" xml:space="preserve" y="1022.71" zvalue="86">10kV光伏集成线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" x="1466.9140625" xml:space="preserve" y="505.7924107142858" zvalue="88">#1光阵(横路</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1466.9140625" xml:space="preserve" y="521.7924107142858" zvalue="88">村)0.252MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1526.5,452.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1526.5" xml:space="preserve" y="457" zvalue="91">315kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1472.5,451.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1472.5" xml:space="preserve" y="456" zvalue="92">0.38kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,789.5,263.75) scale(1,1) translate(0,0)" writing-mode="lr" x="789.5" xml:space="preserve" y="268.25" zvalue="95">A011</text>
 </g>
 <g id="ButtonClass">
  <g href="500kV德宏变_全站公用.svg"><rect fill-opacity="0" height="24" width="97" x="34.43" y="301.86" zvalue="10"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="48">
   <path class="kv10" d="M 503.93 559.61 L 1255.18 559.61" stroke-width="6" zvalue="30"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674257928196" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674257928196"/></metadata>
  <path d="M 503.93 559.61 L 1255.18 559.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="47">
   <use class="kv10" height="30" transform="rotate(0,818.304,772.518) scale(2.63393,2.65241) translate(-484.751,-456.481)" width="28" x="781.4285714285716" xlink:href="#EnergyConsumer:站用变DY接地_0" y="732.732142857143" zvalue="31"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450096988165" ObjectName="10kV#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,818.304,772.518) scale(2.63393,2.65241) translate(-484.751,-456.481)" width="28" x="781.4285714285716" y="732.732142857143"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="45">
   <use class="kv10" height="42" transform="rotate(0,1056.43,777.107) scale(1.25,-1.25) translate(-207.536,-1393.54)" width="30" x="1037.678571428572" xlink:href="#Accessory:4卷PT带容断器_0" y="750.8571428571429" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450096922629" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1056.43,777.107) scale(1.25,-1.25) translate(-207.536,-1393.54)" width="30" x="1037.678571428572" y="750.8571428571429"/></g>
  <g id="63">
   <use class="kv10" height="26" transform="rotate(0,545.179,795.857) scale(1.25,1.25) translate(-107.536,-155.921)" width="12" x="537.6785714285716" xlink:href="#Accessory:避雷器1_0" y="779.6071428571429" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450096529413" ObjectName="013侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,545.179,795.857) scale(1.25,1.25) translate(-107.536,-155.921)" width="12" x="537.6785714285716" y="779.6071428571429"/></g>
  <g id="65">
   <use class="kv10" height="26" transform="rotate(0,776.429,417.107) scale(1.25,-1.25) translate(-153.786,-747.543)" width="12" x="768.9285714285716" xlink:href="#Accessory:避雷器1_0" y="400.8571428571429" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450096463877" ObjectName="011侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,776.429,417.107) scale(1.25,-1.25) translate(-153.786,-747.543)" width="12" x="768.9285714285716" y="400.8571428571429"/></g>
  <g id="75">
   <use class="kv10" height="15" transform="rotate(0,819.025,219.049) scale(1.25,1.25) translate(-161.93,-41.9349)" width="15" x="809.6497252747255" xlink:href="#Accessory:杆或塔_0" y="209.6744505494506" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450096332805" ObjectName="#2杆"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,819.025,219.049) scale(1.25,1.25) translate(-161.93,-41.9349)" width="15" x="809.6497252747255" y="209.6744505494506"/></g>
  <g id="55">
   <use class="kv10" height="15" transform="rotate(0,819.265,66.5879) scale(1.25,1.25) translate(-161.978,-11.4426)" width="15" x="809.8901098901101" xlink:href="#Accessory:杆或塔_0" y="57.21291208791229" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450096201733" ObjectName="#44杆"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,819.265,66.5879) scale(1.25,1.25) translate(-161.978,-11.4426)" width="15" x="809.8901098901101" y="57.21291208791229"/></g>
 </g>
 <g id="BreakerClass">
  <g id="44">
   <use class="kv10" height="20" transform="rotate(0,597.679,679.607) scale(2.75,2.75) translate(-371.591,-414.977)" width="10" x="583.9285714285716" xlink:href="#Breaker:小车断路器_0" y="652.1071428571429" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924567564293" ObjectName="013"/>
   <cge:TPSR_Ref TObjectID="6473924567564293"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,597.679,679.607) scale(2.75,2.75) translate(-371.591,-414.977)" width="10" x="583.9285714285716" y="652.1071428571429"/></g>
  <g id="40">
   <use class="kv10" height="20" transform="rotate(0,818.929,490.857) scale(2.75,2.75) translate(-512.386,-294.864)" width="10" x="805.1785714305029" xlink:href="#Breaker:小车断路器_0" y="463.3571428571429" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924567498757" ObjectName="011"/>
   <cge:TPSR_Ref TObjectID="6473924567498757"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,818.929,490.857) scale(2.75,2.75) translate(-512.386,-294.864)" width="10" x="805.1785714305029" y="463.3571428571429"/></g>
  <g id="52">
   <use class="kv10" height="20" transform="rotate(0,818.929,312.357) scale(1.875,1.6875) translate(-377.792,-120.382)" width="10" x="809.5535714285716" xlink:href="#Breaker:开关_0" y="295.4821428571429" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924567433221" ObjectName="A01"/>
   <cge:TPSR_Ref TObjectID="6473924567433221"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,818.929,312.357) scale(1.875,1.6875) translate(-377.792,-120.382)" width="10" x="809.5535714285716" y="295.4821428571429"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="41">
   <path class="kv10" d="M 597.68 654.17 L 597.68 559.61" stroke-width="1" zvalue="38"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 597.68 654.17 L 597.68 559.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv10" d="M 818.93 515.61 L 818.93 559.61" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.93 515.61 L 818.93 559.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv10" d="M 818.55 734.31 L 818.55 668.73" stroke-width="1" zvalue="43"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="43@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.55 734.31 L 818.55 668.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv10" d="M 818.93 628.36 L 818.93 559.61" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@1" LinkObjectIDznd="39" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.93 628.36 L 818.93 559.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv10" d="M 1044.53 751.64 L 1044.53 669.57" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="46@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1044.53 751.64 L 1044.53 669.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv10" d="M 1045.18 637.2 L 1045.18 559.61" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@1" LinkObjectIDznd="48@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1045.18 637.2 L 1045.18 559.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv10" d="M 819.05 328.47 L 819.04 347.81" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@1" LinkObjectIDznd="50@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 819.05 328.47 L 819.04 347.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv10" d="M 818.93 465.42 L 819.01 374.62" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="50@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.93 465.42 L 819.01 374.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv10" d="M 819.02 219.05 L 819.02 224.98" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@0" LinkObjectIDznd="87" MaxPinNum="2"/>
   </metadata>
  <path d="M 819.02 219.05 L 819.02 224.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv10" d="M 819.03 142.79 L 819.03 66.59" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="27" MaxPinNum="2"/>
   </metadata>
  <path d="M 819.03 142.79 L 819.03 66.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv10" d="M 819.07 121.98 L 819.03 121.98" stroke-width="1" zvalue="73"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 819.07 121.98 L 819.03 121.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv115" d="M 706.04 66.59 L 926.04 66.59" stroke-width="1" zvalue="74"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 706.04 66.59 L 926.04 66.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv10" d="M 819.03 66.59 L 819.03 66.59" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="29" LinkObjectIDznd="27" MaxPinNum="2"/>
   </metadata>
  <path d="M 819.03 66.59 L 819.03 66.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv10" d="M 1398.01 443.49 L 1398.01 995.86 L 597.68 995.86 L 597.68 704.36" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="44@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1398.01 443.49 L 1398.01 995.86 L 597.68 995.86 L 597.68 704.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv10" d="M 653.24 762.11 L 597.68 762.11" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="24" MaxPinNum="2"/>
   </metadata>
  <path d="M 653.24 762.11 L 597.68 762.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv10" d="M 545.22 780.4 L 545.22 760.86 L 597.68 760.86" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="24" MaxPinNum="2"/>
   </metadata>
  <path d="M 545.22 780.4 L 545.22 760.86 L 597.68 760.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv10" d="M 776.47 432.57 L 776.47 445.49 L 818.95 445.49" stroke-width="1" zvalue="82"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="31" MaxPinNum="2"/>
   </metadata>
  <path d="M 776.47 432.57 L 776.47 445.49 L 818.95 445.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv10" d="M 819.27 66.59 L 819.03 66.59" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 819.27 66.59 L 819.03 66.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv10" d="M 818.87 296.21 L 818.87 277.26" stroke-width="1" zvalue="95"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="71@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.87 296.21 L 818.87 277.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv10" d="M 818.48 250.45 L 818.48 174.67" stroke-width="1" zvalue="96"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="32@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.48 250.45 L 818.48 174.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="43">
   <use class="kv10" height="33" transform="rotate(0,818.929,648.357) scale(1.25,-1.25) translate(-162.036,-1162.92)" width="14" x="810.1785714305026" xlink:href="#Disconnector:手车隔离开关13_0" y="627.7321428571429" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450096857093" ObjectName="10kV#1站用变0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450096857093"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,818.929,648.357) scale(1.25,-1.25) translate(-162.036,-1162.92)" width="14" x="810.1785714305026" y="627.7321428571429"/></g>
  <g id="46">
   <use class="kv10" height="26" transform="rotate(0,1045.18,653.357) scale(1.25,1.25) translate(-207.536,-127.421)" width="12" x="1037.678571428572" xlink:href="#Disconnector:小车隔刀熔断器_0" y="637.1071428571429" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450096791557" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450096791557"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1045.18,653.357) scale(1.25,1.25) translate(-207.536,-127.421)" width="12" x="1037.678571428572" y="637.1071428571429"/></g>
  <g id="50">
   <use class="kv10" height="30" transform="rotate(0,818.929,361.107) scale(1.25,0.916667) translate(-161.911,31.5779)" width="15" x="809.5535714285716" xlink:href="#Disconnector:刀闸_0" y="347.3571428571429" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450096726021" ObjectName="A012"/>
   <cge:TPSR_Ref TObjectID="6192450096726021"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,818.929,361.107) scale(1.25,0.916667) translate(-161.911,31.5779)" width="15" x="809.5535714285716" y="347.3571428571429"/></g>
  <g id="32">
   <use class="kv10" height="30" transform="rotate(0,818.929,159.357) scale(1.25,1.25) translate(-161.911,-28.1214)" width="15" x="809.5535714285716" xlink:href="#Disconnector:令克_0" y="140.6071428571429" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450096267269" ObjectName="A0R1"/>
   <cge:TPSR_Ref TObjectID="6192450096267269"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,818.929,159.357) scale(1.25,1.25) translate(-161.911,-28.1214)" width="15" x="809.5535714285716" y="140.6071428571429"/></g>
  <g id="71">
   <use class="kv10" height="30" transform="rotate(0,818.375,263.75) scale(1.25,0.916667) translate(-161.8,22.7273)" width="15" x="809" xlink:href="#Disconnector:刀闸_0" y="250" zvalue="94"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450307096581" ObjectName="A011"/>
   <cge:TPSR_Ref TObjectID="6192450307096581"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,818.375,263.75) scale(1.25,0.916667) translate(-161.8,22.7273)" width="15" x="809" y="250"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="61">
   <use class="kv10" height="20" transform="rotate(270,665.429,762.17) scale(1.25,1.25) translate(-131.836,-149.934)" width="10" x="659.1785714285716" xlink:href="#GroundDisconnector:地刀_0" y="749.6696428571429" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450096660485" ObjectName="01367"/>
   <cge:TPSR_Ref TObjectID="6192450096660485"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,665.429,762.17) scale(1.25,1.25) translate(-131.836,-149.934)" width="10" x="659.1785714285716" y="749.6696428571429"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="89">
   <text Format="f5.1" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="89" prefix="Ia:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,927.429,331.982) scale(1,1) translate(0,0)" writing-mode="lr" x="872.3200000000001" xml:space="preserve" y="336.34" zvalue="1">Ia:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127049822212" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="91" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,924.429,297.982) scale(1,1) translate(0,0)" writing-mode="lr" x="876.03" xml:space="preserve" y="302.34" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127050215430" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="5" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,915.5,268.5) scale(1,1) translate(0,0)" writing-mode="lr" x="867.1" xml:space="preserve" y="272.86" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127062798342" ObjectName="GEN_PSUM"/>
   </metadata>
  </g>
 </g>
</svg>