<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549682307073" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="ACLineSegment:带避雷器线路PT的线路_0" viewBox="0,0,28,25">
   <use terminal-index="0" type="0" x="7.889690721649489" xlink:href="#terminal" y="23.4778860569715"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.705760715003304" x2="3.705760715003304" y1="17.98165807560139" y2="20.28165807560139"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.1" x2="7.9" y1="20.35" y2="20.35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.99493209648661" x2="15.73801133176259" y1="13.42856966348774" y2="13.42856966348774"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.05708746256016" x2="12.05708746256016" y1="18.21462834614074" y2="20.31394252867895"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.05708746256016" x2="12.05708746256016" y1="15.83326413498526" y2="10.847392951457"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.881013745704481" x2="7.881013745704481" y1="23.33200899550225" y2="0.6833333333333229"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.0715036948084" x2="12.0715036948084" y1="8.626739580957119" y2="6.39621826201027"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.520172840926069" x2="14.92116229358282" y1="6.262853550392695" y2="6.262853550392695"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.11740027094565" x2="12.11740027094565" y1="10.74142612107711" y2="10.74142612107711"/>
   <ellipse cx="23.44" cy="13.33" fill-opacity="0" rx="3.84" ry="3.87" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.45" cy="8.369999999999999" fill-opacity="0" rx="3.84" ry="3.87" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.43" cy="13.38" fill-opacity="0" rx="3.84" ry="3.87" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.49620888348457" x2="14.79890446679221" y1="10.7414261210771" y2="10.7414261210771"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.39664267427136" x2="14.13972190954734" y1="5.17713898780255" y2="5.17713898780255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.18422026904639" x2="12.89979825188121" y1="4.36285306585993" y2="4.36285306585993"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.11740027094565" x2="12.11740027094565" y1="18.06999941856068" y2="18.06999941856068"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.49620888348457" x2="14.79890446679221" y1="18.06999941856068" y2="18.06999941856068"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.496208883484574" x2="14.79890446679222" y1="8.705711316220558" y2="8.705711316220558"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.49620888348457" x2="14.79890446679221" y1="16.03428461370413" y2="16.03428461370413"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(90,3.74,12.62) scale(1,1) translate(0,0)" width="10.7" x="-1.61" y="10.12"/>
   <path d="M 5.09221 10.9572 L 2.32976 10.9572 L 3.734 14.8326 L 5.09221 10.9572" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.786782704008788" x2="1.643104543089253" y1="5.14716501266186" y2="5.14716501266186"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.71494362354902" x2="3.71494362354902" y1="14.69035020469595" y2="5.122943730448588"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.981067506052216" x2="2.218615398772524" y1="4.178313724130515" y2="4.178313724130515"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.405556650368943" x2="3.139432767865758" y1="3.451675257731946" y2="3.451675257731946"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.85" x2="3.65" y1="20.35" y2="20.35"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:母联开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill="rgb(170,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.333333333333333" x2="9.75" y1="0.4166666666666696" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.666666666666668" x2="0.4166666666666661" y1="0.4999999999999982" y2="19.66666666666667"/>
   <rect fill-opacity="0" height="19.58" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.04,10.04) scale(1,1) translate(0,0)" width="9.58" x="0.25" y="0.25"/>
  </symbol>
  <symbol id="Accessory:避雷器PT1_0" viewBox="0,0,38,38">
   <use terminal-index="0" type="0" x="18.77349690599886" xlink:href="#terminal" y="36.79316976066781"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.7559822394171" x2="18.7559822394171" y1="29.08333333333334" y2="36.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.88529270218527" x2="24.41666666666667" y1="21.56930997848709" y2="21.56930997848709"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.7559822394171" x2="18.7559822394171" y1="25.91666666666667" y2="17.4423061423378"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.83645328312235" x2="18.83645328312235" y1="17.48357618069931" y2="17.48357618069931"/>
   <ellipse cx="28.59" cy="21.55" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.4566694475001" x2="22.29670816245006" y1="17.4835761806993" y2="17.4835761806993"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.66978661645568" x2="18.66978661645568" y1="29.04315320496906" y2="29.04315320496906"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.45666944750009" x2="22.29670816245006" y1="14.38832330358733" y2="14.38832330358733"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.29000278083343" x2="22.1300414957834" y1="29.04315320496904" y2="29.04315320496904"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.29000278083343" x2="22.1300414957834" y1="25.94790032785706" y2="25.94790032785706"/>
   <ellipse cx="32.84" cy="24.05" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="32.84" cy="19.05" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.82811977618069" x2="18.82811977618069" y1="14.41666666666667" y2="7.039733668812222"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.92895180590147" x2="16.65213596121762" y1="6.971825133297724" y2="6.971825133297724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.09561847256814" x2="17.48546929455096" y1="5.596825133297724" y2="5.596825133297724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.25" x2="18.3310877671191" y1="4.221825133297724" y2="4.221825133297724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.883333333333329" x2="4.883333333333329" y1="7.049999999999994" y2="3.449999999999994"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.133333333333331" x2="6.333333333333336" y1="11.79999999999999" y2="15.58333333333334"/>
   <rect fill-opacity="0" height="12.12" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.12,13.11) scale(1,-1) translate(0,-820.33)" width="6.08" x="2.08" y="7.05"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.133333333333331" x2="3.750000000000004" y1="11.79999999999999" y2="15.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.133333333333329" x2="5.133333333333329" y1="21.5" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.218626035518611" x2="18.58333333333333" y1="21.48597664515375" y2="21.48597664515375"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.012285139234802" x2="2.735469294550956" y1="3.30515846663106" y2="3.30515846663106"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.178951805901468" x2="3.56880262788429" y1="1.93015846663106" y2="1.93015846663106"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.333333333333332" x2="4.414421100452426" y1="0.5551584666310596" y2="0.5551584666310596"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Accessory:线路PT带避雷器0904_0" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="12.91666666666667" y1="23.75" y2="23.75"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,11) scale(1,1) translate(0,0)" width="6" x="7" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.988213005145578" x2="9.988213005145578" y1="1.029523490692871" y2="18.75"/>
   <ellipse cx="9.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="12.91666666666667" y1="32.91666666666667" y2="32.91666666666667"/>
  </symbol>
  <symbol id="ACLineSegment:线路带壁雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="14.875" xlink:href="#terminal" y="39.83880854456296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.25" x2="6.25" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="5.75" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.25" x2="7.25" y1="32.25" y2="32.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="8.25" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="6.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="4.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.91022336769755" x2="14.91022336769755" y1="39.75" y2="0.8333333333333321"/>
   <path d="M 14.75 9.25 L 5.75 9.25 L 5.75 21.25 L 5.75 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.79,21.17) scale(1,1) translate(0,0)" width="6.08" x="2.75" y="14"/>
  </symbol>
  <symbol id="Accessory:5卷PT带壁雷器_0" viewBox="0,0,30,35">
   <use terminal-index="0" type="0" x="25.27572331551165" xlink:href="#terminal" y="34.09945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.25" x2="25.0990682626599" y1="27.2315496546656" y2="27.2315496546656"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666666" x2="25.16666666666666" y1="34.02740325661302" y2="14.61073658994635"/>
   <path d="M 10.5 13.9441 L 4.5 13.9441 L 4.5 6.94407" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25.25,14.36) scale(1,-1) translate(0,-930.43)" width="7" x="21.75" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="22.19406992327969" y2="18.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666666" x2="26.16666666666666" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="5.5" y1="18.19406992327969" y2="19.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666666" x2="24.16666666666666" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="5.5" y1="22.19406992327969" y2="21.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.17943360505483" x2="25.17943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="13.98" cy="27.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.39" cy="13.83" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.20921744067709" x2="22.93240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.37588410734375" x2="23.76573492932657" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.79255077401042" x2="24.34906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="16.34906826265991" y1="27.45662962336982" y2="28.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="13.94906826265991" y1="24.98154965466559" y2="27.4566296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="11.54906826265991" y1="27.45662962336982" y2="28.69416960772192"/>
   <ellipse cx="13.98" cy="20.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.98" cy="27.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.94906826265991" x2="6.94906826265991" y1="24.98154965466559" y2="27.4566296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.949068262659907" x2="4.549068262659912" y1="27.45662962336982" y2="28.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="16.34906826265991" y1="20.45662962336982" y2="21.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="11.54906826265991" y1="20.45662962336982" y2="21.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.949068262659912" x2="9.349068262659905" y1="27.45662962336982" y2="28.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="13.94906826265991" y1="17.9815496546656" y2="20.4566296233698"/>
   <ellipse cx="6.98" cy="20.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.48240159599324" x2="12.88240159599324" y1="13.92329629003649" y2="15.16083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.48240159599324" x2="8.082401595993243" y1="13.92329629003649" y2="15.16083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.48240159599324" x2="10.48240159599324" y1="11.44821632133226" y2="13.92329629003646"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.542550774010419" x2="2.265734929326571" y1="6.8946117330996" y2="6.8946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.709217440677083" x2="3.099068262659904" y1="5.644611733099605" y2="5.644611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.125884107343751" x2="3.682401595993237" y1="4.394611733099605" y2="4.394611733099605"/>
  </symbol>
  <symbol id="Accessory:母线电压互感器11_0" viewBox="0,0,45,48">
   <use terminal-index="0" type="0" x="25.25" xlink:href="#terminal" y="47.78457520218115"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.5,30.95) scale(1,-1) translate(0,-1334.05)" width="6" x="10.5" y="23.95"/>
   <path d="M 8.64167 11.4096 L 4.64167 11.4096 L 6.50709 7.52624 L 7.67376 9.52624 L 8.64167 11.4096" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="13.5" y1="19.78457520218114" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.25" x2="25.25" y1="40.5" y2="47.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="37.25" x2="13.58333333333333" y1="40.5" y2="40.5"/>
   <ellipse cx="13.56" cy="5.42" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799663" x2="13.56620079799663" y1="3.155388266900378" y2="5.82957386674455"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799661" x2="10.88240159599323" y1="5.829573866744575" y2="7.166666666666657"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799662" x2="16.25" y1="5.829573866744575" y2="7.166666666666657"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,36.96,25.94) scale(1,-1) translate(0,-1133.72)" width="7.58" x="33.17" y="17.87"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="34.01790853551448" y2="24.11790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="40.43457520218114" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.7861111111111" x2="39.3" y1="11.60124186884782" y2="11.60124186884782"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.82777777777777" x2="39.93888888888888" y1="12.98282081621623" y2="12.98282081621623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.55" x2="41.21666666666666" y1="14.36439976358464" y2="14.36439976358464"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="34.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="18.01790853551448" y2="14.41790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="40.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <ellipse cx="13.59" cy="15.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.51" cy="10.09" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.67" cy="10.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490864" x2="10.91228003290526" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="13.59607923490865" y1="12.74178419246191" y2="15.41596979230608"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="23.19654510357869" y1="10.49930312563944" y2="11.83639592556152"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="20.51274590157532" y1="7.825117525795246" y2="10.49930312563942"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="16.27987843691203" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5127459015753" x2="17.82894669957193" y1="10.49930312563944" y2="11.83639592556152"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷带壁雷器_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="28.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11.75" y1="17.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="14.75" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="12" y1="11.75" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="9" y1="12.75" y2="12.75"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.04,18.25) scale(1,1) translate(0,0)" width="3.25" x="9.42" y="14.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.5" y1="10.83333333333333" y2="10.83333333333333"/>
   <path d="M 5.025 2.775 L 4.16667 9.5 L 5.025 7.60833 L 5.91667 9.5 L 5.025 2.775" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 5 23.75 L 11 23.75 L 11 17.75 L 10.25 20.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="7.627914951989029" y2="28.23902606310013"/>
  </symbol>
  <symbol id="Accessory:10kV母线PT带消谐装置_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="17.56245852479325" xlink:href="#terminal" y="28.12373692455963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.61245852479333" x2="34.61245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.86245852479331" x2="30.86245852479331" y1="22.68299618381883" y2="19.84040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.6124585247933" x2="26.6124585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.61245852479332" x2="34.61245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666667" x2="31" y1="22.69225544307809" y2="22.69225544307809"/>
   <rect fill-opacity="0" height="3.55" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,30.81,15.38) scale(1,1) translate(0,0)" width="8.92" x="26.34" y="13.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.647721108666" x2="30.647721108666" y1="10.83674821859629" y2="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.71438777533266" x2="28.71438777533266" y1="7.51612768565195" y2="7.51612768565195"/>
   <ellipse cx="14.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.05048569403981" x2="22.05048569403981" y1="34.53084700683308" y2="34.53084700683308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.7608382776216" x2="28.48402243293775" y1="7.754208141873304" y2="7.754208141873304"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.01083827762157" x2="29.40068909960439" y1="6.50420814187332" y2="6.50420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.51083827762159" x2="30.06735576627107" y1="5.004208141873296" y2="5.004208141873296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.89410730945214" x2="23.0877735452272" y1="24.15299989806297" y2="21.53208583485582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58713830216066" x2="22.81883560169719" y1="21.54040359122622" y2="21.54040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51457106407813" x2="19.32090482830307" y1="24.05654513693459" y2="21.43563107372743"/>
   <ellipse cx="14.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.3644003886642" x2="22.3644003886642" y1="30.19213090500035" y2="30.19213090500035"/>
   <ellipse cx="21.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.19220382559271" x2="17.19220382559271" y1="23.07076893362116" y2="23.07076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.4422038255927" x2="16.4422038255927" y1="16.82076893362115" y2="16.82076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.92264294445647" x2="20.87303257583197" y1="16.99992879670395" y2="15.47611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.873032575832" x2="20.37063985073817" y1="15.47611741162252" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.87303257583199" x2="23.32581493230132" y1="15.47611741162254" y2="16.60543752773795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.94220382559271" x2="16.94220382559271" y1="16.32076893362115" y2="16.32076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="16.07581493230131" y1="22.72611741162255" y2="23.85543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="13.12063985073816" y1="22.72611741162255" y2="20.07298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583197" y1="24.24992879670398" y2="22.72611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="16.07581493230131" y1="15.47611741162254" y2="16.60543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="13.12063985073816" y1="15.47611741162253" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583196" y1="16.99992879670396" y2="15.47611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.59103363843511" x2="17.59103363843511" y1="25.15822158129307" y2="28.16666666666667"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2027.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_0" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="3.333333333333332" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="24.08333333333333" y2="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="7" y1="19.5" y2="24.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_1" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="18.25" y2="32.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_2" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="4" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="10" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="25.83333333333333" y2="32.41666666666667"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Breaker:小车母联_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="17.58333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车母联_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="0.8333333333333304" y2="5.749999999999999"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车母联_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="14" y2="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.666666666666666" x2="7.25" y1="5.833333333333333" y2="14.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_0" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="3" y1="19" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="4.75" y2="8.75"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="18.75" y2="21.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_1" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="2" y2="24"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_2" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.08333333333333" x2="3.995614035087721" y1="9.083333333333332" y2="17.19627192982456"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.717927631578948" x2="10.16666666666667" y1="9.172423245614038" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="2" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="21.25" y2="24.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
  </symbol>
  <symbol id="Ground:大地_0" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6" x2="6" y1="13.08333333333334" y2="0.1666666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.5833333333333313" x2="11.25" y1="12.99453511141348" y2="12.99453511141348"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.083333333333333" x2="8" y1="17.63116790988687" y2="17.63116790988687"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666667" x2="10.33333333333333" y1="15.40451817731685" y2="15.40451817731685"/>
  </symbol>
  <symbol id="Accessory:附属接地变_0" viewBox="0,0,15,15">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="1.5"/>
   <ellipse cx="7.57" cy="7.91" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.561718118722353" x2="7.561718118722353" y1="4.914465958746223" y2="7.513454058763156"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.1552656081938" x2="7.561718118722348" y1="10.11244215878007" y2="7.513454058763136"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.96817062925088" x2="7.561718118722333" y1="10.11244215878007" y2="7.513454058763136"/>
  </symbol>
  <symbol id="PowerTransformer3:可调三卷变Y-D-Y_0" viewBox="0,0,50,50">
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.08333333333333" x2="15.17203932327389" y1="8.166666666666668" y2="11.6754686785551"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.33333333333333" x2="15.15740740740741" y1="8.083333333333332" y2="11.6754686785551"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.16666666666666" x2="15.16666666666666" y1="11.66666666666666" y2="16.41666666666666"/>
   <ellipse cx="14.93" cy="15" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.91666666666666" x2="0.5833333333333286" y1="1.749999999999989" y2="24.5"/>
   <use terminal-index="0" type="1" x="15.08687700045725" xlink:href="#terminal" y="0.1329446730681312"/>
   <use terminal-index="4" type="2" x="15.1368541380887" xlink:href="#terminal" y="11.65974508459076"/>
   <path d="M 28.6667 0.5 L 25.1667 1.75 L 26.6667 3.33333 z" fill="rgb(255,0,0)" fill-opacity="1" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer3:可调三卷变Y-D-Y_1" viewBox="0,0,50,50">
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.91666666666666" x2="14.91666666666666" y1="34.83333333333334" y2="39.13203017832647"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.91598079561043" x2="10" y1="39.13203017832647" y2="43.58333333333333"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.92329675354367" x2="19.5" y1="39.13934613625972" y2="43.66666666666667"/>
   <ellipse cx="15.03" cy="35.08" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="1" x="15.29117584228516" xlink:href="#terminal" y="49.91666666666666"/>
   <use terminal-index="3" type="2" x="14.92046753543667" xlink:href="#terminal" y="39.13212162780064"/>
  </symbol>
  <symbol id="PowerTransformer3:可调三卷变Y-D-Y_2" viewBox="0,0,50,50">
   <path d="M 35.9167 21 L 30.8333 29 L 40.9167 29 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="34.87" cy="25.17" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="1" x="49.67268518518519" xlink:href="#terminal" y="24.89444444444445"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id=":线路带避雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15.16666666666667" xlink:href="#terminal" y="39.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.16022336769755" x2="15.16022336769755" y1="39.75" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="6.5" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="8.5" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="7.5" y1="32.25" y2="32.25"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6.04,21.17) scale(1,1) translate(0,0)" width="6.08" x="3" y="14"/>
   <path d="M 15 9.25 L 6 9.25 L 6 21.25 L 6 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="7" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="5" y1="22" y2="16"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变无融断_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <path d="M 10 9 L 10 0" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="26.83333333333333" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="10.8868007916835" y2="12.55855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="14.23031840279781" y2="12.55855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
  <symbol id="Coil:消弧线圈带壁雷器_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="14.75" xlink:href="#terminal" y="0.5"/>
   <path d="M 14.75 5 L 3.75 5 L 3.75 13 L 4.75 10" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.75" x2="2.75" y1="13" y2="10"/>
   <rect fill-opacity="0" height="8.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,3.79,11.96) scale(1,1) translate(0,0)" width="4.08" x="1.75" y="7.92"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="14.75" y1="6.249999999999993" y2="0.9166666666666572"/>
   <path d="M 14.791 16.3509 A 2.54583 4.29167 270 0 0 14.791 11.2593" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.75" x2="14.75" y1="21.52777777777779" y2="25.42283950617285"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.77191358024691" x2="17.77191358024692" y1="27.35901008120442" y2="27.35901008120442"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.98024691358025" x2="16.56358024691358" y1="29.29396199879663" y2="29.29396199879663"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.896913580246911" x2="19.64691358024692" y1="25.42405816361222" y2="25.42405816361222"/>
   <path d="M 14.784 11.2785 A 2.53125 3.83333 270 0 0 14.784 6.21605" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 14.7716 21.5361 A 2.5875 4.5 270 0 0 14.7716 16.3611" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.666666666666665" x2="3.666666666666665" y1="15.94444444444445" y2="19.83950617283951"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="1.957098765432102" x2="5.439814814814817" y1="21.23401008120442" y2="21.23401008120442"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.73024691358025" x2="4.666666666666669" y1="22.54396199879661" y2="22.54396199879661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.6685185185185158" x2="6.728395061728393" y1="19.92405816361222" y2="19.92405816361222"/>
  </symbol>
  <symbol id=":220kV等值机_0" viewBox="0,0,35,40">
   <use terminal-index="0" type="0" x="14.91666666666667" xlink:href="#terminal" y="38.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.25000000000001" x2="31.25000000000001" y1="25.58333333333334" y2="27.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25000000000001" x2="22.25000000000001" y1="20.58333333333334" y2="23.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="4.75" y1="25.58333333333334" y2="19.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25000000000001" x2="25.25000000000001" y1="23.58333333333334" y2="25.58333333333334"/>
   <path d="M 14.75 12.8333 L 5.75 12.8333 L 5.75 24.8333 L 5.75 24.8333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25000000000001" x2="19.25" y1="23.58333333333334" y2="25.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.25000000000001" x2="28.25000000000001" y1="25.58333333333334" y2="30.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.25" x2="7.25" y1="35.83333333333333" y2="35.83333333333333"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.79,24.75) scale(1,1) translate(0,0)" width="6.08" x="2.75" y="17.58"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.25" x2="6.25" y1="36.83333333333333" y2="36.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="5.75" y1="31.83333333333333" y2="34.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.25000000000001" x2="31.25000000000001" y1="30.58333333333334" y2="28.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="8.25" y1="34.83333333333333" y2="34.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="6.75" y1="25.58333333333334" y2="19.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.91022336769756" x2="14.91022336769756" y1="38.66666666666667" y2="0.6666666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.21058610156545" x2="14.90436235204273" y1="12.75875283090555" y2="12.75875283090555"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.2554505536464" x2="22.2554505536464" y1="12.8747219778093" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.66094883543335" x2="25.13823024054981" y1="12.75875283090555" y2="12.75875283090555"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.21383161512025" x2="32.16666666666666" y1="12.83092145585036" y2="12.83092145585036"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="25.10940244368076" x2="25.10940244368076" y1="12.83092145585036" y2="12.83092145585036"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.11598892707139" x2="32.11598892707139" y1="9.583333333333332" y2="16.07850957836739"/>
   <ellipse cx="22.26" cy="23.66" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="29.3" cy="27.91" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="22.51" cy="31.92" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="25.10940244368077" x2="25.10940244368077" y1="10.31658627075059" y2="15.41742526589504"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="33.26910080183274" x2="33.26910080183274" y1="10.84628426986774" y2="15.1764017665571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="34.13393470790376" x2="34.13393470790376" y1="11.74839208167799" y2="13.73302926766061"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.32589728904158" x2="17.32589728904158" y1="12.83092145585036" y2="12.83092145585036"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="27.27148720885832" x2="27.27148720885832" y1="10.31658627075059" y2="15.41742526589504"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="17.24256395570825" x2="17.24256395570825" y1="10.16666666666667" y2="15.26750566181112"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="19.48798205421914" x2="19.48798205421914" y1="10.31658627075059" y2="15.41742526589504"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.33333333333334" x2="19.33333333333334" y1="32.33333333333333" y2="34.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.33333333333334" x2="22.33333333333334" y1="29.33333333333334" y2="32.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.33333333333334" x2="25.33333333333334" y1="32.33333333333333" y2="34.33333333333333"/>
  </symbol>
  <symbol id=":110kV线路带PT避雷器_0" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="22.5" xlink:href="#terminal" y="44.60000000000001"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="33" y1="29.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="16" y1="40.5" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="27.41094883543335" x2="32.88823024054981" y1="18.42541949757221" y2="18.42541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.66022336769755" x2="22.66022336769755" y1="44.33333333333334" y2="0.2499999999999964"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="13.5" y1="37.5" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36" x2="36" y1="31.25" y2="36.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36" x2="39" y1="31.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="15" y1="41.5" y2="41.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="14" y1="42.5" y2="42.5"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.54,30.42) scale(1,1) translate(0,0)" width="6.08" x="10.5" y="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36" x2="39" y1="36.25" y2="34.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.96058610156545" x2="22.65436235204273" y1="18.42541949757221" y2="18.42541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="12.5" y1="31.25" y2="25.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="14.5" y1="31.25" y2="25.25"/>
   <path d="M 22.5 18.5 L 13.5 18.5 L 13.5 30.5 L 13.5 30.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="30" y1="26.25" y2="29.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.00545055364641" x2="30.00545055364641" y1="18.54138864447597" y2="24.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="27" y1="29.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.96383161512026" x2="39.91666666666666" y1="18.49758812251703" y2="18.49758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.85940244368076" x2="32.85940244368076" y1="18.49758812251703" y2="18.49758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="39.86598892707138" x2="39.86598892707138" y1="15.25" y2="21.74517624503405"/>
   <ellipse cx="30.01" cy="29.32" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="37.05" cy="33.58" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="30.26" cy="37.58" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="32.85940244368077" x2="32.85940244368077" y1="15.98325293741726" y2="21.08409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="41.01910080183274" x2="41.01910080183274" y1="16.51295093653441" y2="20.84306843322376"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="41.88393470790376" x2="41.88393470790376" y1="17.41505874834466" y2="19.39969593432727"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="25.07589728904158" x2="25.07589728904158" y1="18.49758812251703" y2="18.49758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="24.99256395570825" x2="24.99256395570825" y1="15.83333333333333" y2="20.93417232847779"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="35.02148720885832" x2="35.02148720885832" y1="15.98325293741726" y2="21.08409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="27.23798205421914" x2="27.23798205421914" y1="15.98325293741726" y2="21.08409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.08333333333334" x2="33.08333333333334" y1="38" y2="40"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.08333333333334" x2="27.08333333333334" y1="38" y2="40"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.08333333333334" x2="30.08333333333334" y1="35" y2="38"/>
  </symbol>
  <symbol id="ACLineSegment:220kV线路_0" viewBox="0,0,35,40">
   <use terminal-index="0" type="0" x="14.20833333333333" xlink:href="#terminal" y="39.00547521122963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.5" x2="30.5" y1="30.75" y2="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.5" x2="27.5" y1="25.75" y2="30.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.50545055364641" x2="21.50545055364641" y1="13.04138864447597" y2="19.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.5" x2="24.5" y1="23.75" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="5.5" y1="37" y2="37"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.91094883543335" x2="24.38823024054981" y1="12.92541949757221" y2="12.92541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.5" x2="18.5" y1="23.75" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.5" x2="30.5" y1="25.75" y2="27.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="6.5" y1="36" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="7.5" y1="35" y2="35"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.04,24.92) scale(1,1) translate(0,0)" width="6.08" x="2" y="17.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="32" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="4" y1="25.75" y2="19.75"/>
   <path d="M 14 13 L 5 13 L 5 25 L 5 25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="6" y1="25.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.5" x2="21.5" y1="20.75" y2="23.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.46058610156545" x2="14.15436235204273" y1="12.92541949757221" y2="12.92541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16022336769755" x2="14.16022336769755" y1="38.83333333333334" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.46383161512025" x2="31.41666666666666" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.35940244368076" x2="24.35940244368076" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.36598892707138" x2="31.36598892707138" y1="9.749999999999996" y2="16.24517624503405"/>
   <ellipse cx="21.51" cy="23.82" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="28.55" cy="28.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.76" cy="32.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="24.35940244368077" x2="24.35940244368077" y1="10.48325293741726" y2="15.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.51910080183274" x2="32.51910080183274" y1="11.01295093653441" y2="15.34306843322376"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="33.38393470790376" x2="33.38393470790376" y1="11.91505874834466" y2="13.89969593432727"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.57589728904158" x2="16.57589728904158" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="26.52148720885832" x2="26.52148720885832" y1="10.48325293741726" y2="15.5840919325617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="16.49256395570825" x2="16.49256395570825" y1="10.33333333333333" y2="15.43417232847779"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="18.73798205421914" x2="18.73798205421914" y1="10.48325293741726" y2="15.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.58333333333333" x2="18.58333333333333" y1="32.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.58333333333334" x2="24.58333333333334" y1="32.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.58333333333333" x2="21.58333333333333" y1="29.5" y2="32.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV盏西变" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="64.45" xlink:href="logo.png" y="36.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="370" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,189.296,77.7136) scale(1,1) translate(-1.43107e-14,0)" writing-mode="lr" x="189.3" xml:space="preserve" y="81.20999999999999" zvalue="9527"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,218.143,77.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="218.14" xml:space="preserve" y="86.69" zvalue="9528">110kV盏西变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="807" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,257.438,404.25) scale(1,1) translate(0,-2.61291e-13)" width="72.88" x="221" y="392.25" zvalue="10397"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,257.438,404.25) scale(1,1) translate(0,-2.61291e-13)" writing-mode="lr" x="257.44" xml:space="preserve" y="408.75" zvalue="10397">小电流接地</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="806" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,155.531,404.25) scale(1,1) translate(0,0)" width="72.88" x="119.09" y="392.25" zvalue="10399"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,155.531,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="155.53" xml:space="preserve" y="408.75" zvalue="10399">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="805" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,55.625,404.25) scale(1,1) translate(0,0)" width="72.88" x="19.19" y="392.25" zvalue="10400"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.625,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="55.63" xml:space="preserve" y="408.75" zvalue="10400">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="798" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,55.625,367.75) scale(1,1) translate(0,0)" width="72.88" x="19.19" y="355.75" zvalue="10401"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.625,367.75) scale(1,1) translate(0,0)" writing-mode="lr" x="55.63" xml:space="preserve" y="372.25" zvalue="10401">信号一览</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="759" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,55.625,329.25) scale(1,1) translate(0,0)" width="72.88" x="19.19" y="317.25" zvalue="10427"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.625,329.25) scale(1,1) translate(0,0)" writing-mode="lr" x="55.63" xml:space="preserve" y="333.75" zvalue="10427">全站公用</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,425.361,338.948) scale(1,1) translate(0,0)" writing-mode="lr" x="425.36" xml:space="preserve" y="343.45" zvalue="7572">110kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,425.79,395.896) scale(1,1) translate(0,0)" writing-mode="lr" x="425.79" xml:space="preserve" y="400.4" zvalue="7574">110kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="393" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,637.73,557.362) scale(1,1) translate(0,1.21539e-13)" writing-mode="lr" x="637.73" xml:space="preserve" y="561.86" zvalue="7579">1010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" x="630.7578125" xml:space="preserve" y="585.3917470270245" zvalue="7585">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="630.7578125" xml:space="preserve" y="602.3917470270245" zvalue="7585">10MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="403" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1197.29,539.7) scale(1,1) translate(0,0)" writing-mode="lr" x="1197.29" xml:space="preserve" y="544.2" zvalue="7948">1020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,483.286,251.357) scale(1,1) translate(0,0)" writing-mode="lr" x="483.29" xml:space="preserve" y="255.86" zvalue="8763">171</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,476.786,186.857) scale(1,1) translate(0,0)" writing-mode="lr" x="476.79" xml:space="preserve" y="191.36" zvalue="8764">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,530.786,318.857) scale(1,1) translate(0,0)" writing-mode="lr" x="530.79" xml:space="preserve" y="323.36" zvalue="8766">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,483.286,318.857) scale(1,1) translate(0,0)" writing-mode="lr" x="483.29" xml:space="preserve" y="323.36" zvalue="8768">2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" x="458.1484375" xml:space="preserve" y="66.93080145091932" zvalue="8769">110kV傣盏</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="458.1484375" xml:space="preserve" y="83.93080145091932" zvalue="8769">Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,530.286,280.857) scale(1,1) translate(0,0)" writing-mode="lr" x="530.29" xml:space="preserve" y="285.36" zvalue="8770">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,510.286,226.857) scale(1,1) translate(0,0)" writing-mode="lr" x="510.29" xml:space="preserve" y="231.36" zvalue="8771">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,505.286,164.857) scale(1,1) translate(0,0)" writing-mode="lr" x="505.29" xml:space="preserve" y="169.36" zvalue="8773">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="232" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,926.893,257.929) scale(1,1) translate(0,0)" writing-mode="lr" x="926.8928571428571" xml:space="preserve" y="262.4285714285714" zvalue="8951">110kV母联112</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="235" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,901.786,318.857) scale(1,1) translate(0,0)" writing-mode="lr" x="901.79" xml:space="preserve" y="323.36" zvalue="8953">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,988.286,318.857) scale(1,1) translate(0,0)" writing-mode="lr" x="988.29" xml:space="preserve" y="323.36" zvalue="8955">2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="249" stroke="rgb(255,255,255)" text-anchor="middle" x="1430.03125" xml:space="preserve" y="159.359375" zvalue="8966">110kVⅠ母电压互</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="249" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1430.03125" xml:space="preserve" y="176.359375" zvalue="8966">感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1459.79,318.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1459.79" xml:space="preserve" y="323.36" zvalue="8968">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1471.29,279.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1471.29" xml:space="preserve" y="284.36" zvalue="8970">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="middle" x="498.640625" xml:space="preserve" y="525.796875" zvalue="8975">110kVⅡ母电压互</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="498.640625" xml:space="preserve" y="542.78125" zvalue="8975">感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,516.286,411.857) scale(1,1) translate(0,0)" writing-mode="lr" x="516.29" xml:space="preserve" y="416.36" zvalue="8977">1902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="268" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,530.786,456.857) scale(1,1) translate(0,0)" writing-mode="lr" x="530.79" xml:space="preserve" y="461.36" zvalue="8981">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,721.786,475.857) scale(1,1) translate(0,0)" writing-mode="lr" x="721.79" xml:space="preserve" y="480.36" zvalue="8984">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,720.286,421.857) scale(1,1) translate(0,0)" writing-mode="lr" x="720.29" xml:space="preserve" y="426.36" zvalue="8987">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,672.786,421.857) scale(1,1) translate(0,0)" writing-mode="lr" x="672.79" xml:space="preserve" y="426.36" zvalue="8989">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,752.786,457.907) scale(1,1) translate(0,0)" writing-mode="lr" x="752.79" xml:space="preserve" y="462.41" zvalue="8991">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,752.286,517.019) scale(1,1) translate(0,0)" writing-mode="lr" x="752.29" xml:space="preserve" y="521.52" zvalue="8993">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,723.286,528.857) scale(1,1) translate(0,0)" writing-mode="lr" x="723.29" xml:space="preserve" y="533.36" zvalue="8995">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="431" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1278.79,475.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1278.79" xml:space="preserve" y="480.36" zvalue="9147">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="430" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1277.29,421.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1277.29" xml:space="preserve" y="426.36" zvalue="9149">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="416" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1229.79,421.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1229.79" xml:space="preserve" y="426.36" zvalue="9151">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="415" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1309.79,457.929) scale(1,1) translate(0,0)" writing-mode="lr" x="1309.79" xml:space="preserve" y="462.43" zvalue="9153">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="410" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1315,488.286) scale(1,1) translate(0,0)" writing-mode="lr" x="1315" xml:space="preserve" y="492.79" zvalue="9155">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="402" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1278.86,519.429) scale(1,1) translate(0,0)" writing-mode="lr" x="1278.86" xml:space="preserve" y="523.9299999999999" zvalue="9157">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,991.286,261.857) scale(1,1) translate(0,0)" writing-mode="lr" x="991.29" xml:space="preserve" y="266.36" zvalue="9340">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,867.286,261.857) scale(1,1) translate(0,0)" writing-mode="lr" x="867.29" xml:space="preserve" y="266.36" zvalue="9349">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1035.29,294.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1035.29" xml:space="preserve" y="299.36" zvalue="9357">11017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1036.29,443.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1036.29" xml:space="preserve" y="448.36" zvalue="9361">11027</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="213" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,585.286,251.357) scale(1,1) translate(0,0)" writing-mode="lr" x="585.29" xml:space="preserve" y="255.86" zvalue="9366">172</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,578.786,186.857) scale(1,1) translate(0,0)" writing-mode="lr" x="578.79" xml:space="preserve" y="191.36" zvalue="9367">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="166" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,628.786,318.857) scale(1,1) translate(0,0)" writing-mode="lr" x="628.79" xml:space="preserve" y="323.36" zvalue="9369">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,585.286,318.857) scale(1,1) translate(0,0)" writing-mode="lr" x="585.29" xml:space="preserve" y="323.36" zvalue="9372">2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" x="562.4375" xml:space="preserve" y="66.93080145091932" zvalue="9373">110kV傣盏</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="562.4375" xml:space="preserve" y="83.93080145091932" zvalue="9373">Ⅱ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,632.286,280.857) scale(1,1) translate(0,0)" writing-mode="lr" x="632.29" xml:space="preserve" y="285.36" zvalue="9376">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,612.286,226.857) scale(1,1) translate(0,0)" writing-mode="lr" x="612.29" xml:space="preserve" y="231.36" zvalue="9377">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,607.286,164.857) scale(1,1) translate(0,0)" writing-mode="lr" x="607.29" xml:space="preserve" y="169.36" zvalue="9379">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="280" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,703.286,251.357) scale(1,1) translate(0,0)" writing-mode="lr" x="703.29" xml:space="preserve" y="255.86" zvalue="9393">173</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="279" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,696.786,186.857) scale(1,1) translate(0,0)" writing-mode="lr" x="696.79" xml:space="preserve" y="191.36" zvalue="9394">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="324" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,750.036,322.857) scale(1,1) translate(0,0)" writing-mode="lr" x="750.04" xml:space="preserve" y="327.36" zvalue="9396">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="278" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,694.036,321.607) scale(1,1) translate(0,0)" writing-mode="lr" x="694.04" xml:space="preserve" y="326.11" zvalue="9398">2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="277" stroke="rgb(255,255,255)" text-anchor="middle" x="683.609375" xml:space="preserve" y="66.93080145091932" zvalue="9399">110kV香柏河二</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="277" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="683.609375" xml:space="preserve" y="83.93080145091932" zvalue="9399">级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="800" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,741.286,282.857) scale(1,1) translate(0,0)" writing-mode="lr" x="741.29" xml:space="preserve" y="287.36" zvalue="9401">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="276" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,730.286,226.857) scale(1,1) translate(0,0)" writing-mode="lr" x="730.29" xml:space="preserve" y="231.36" zvalue="9402">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="275" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,725.286,164.857) scale(1,1) translate(0,0)" writing-mode="lr" x="725.29" xml:space="preserve" y="169.36" zvalue="9404">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="305" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,809.286,251.357) scale(1,1) translate(0,0)" writing-mode="lr" x="809.29" xml:space="preserve" y="255.86" zvalue="9418">174</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="304" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,802.786,186.857) scale(1,1) translate(0,0)" writing-mode="lr" x="802.79" xml:space="preserve" y="191.36" zvalue="9419">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="325" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,852.661,318.857) scale(1,1) translate(0,0)" writing-mode="lr" x="852.66" xml:space="preserve" y="323.36" zvalue="9421">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="303" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,809.286,318.857) scale(1,1) translate(0,0)" writing-mode="lr" x="809.29" xml:space="preserve" y="323.36" zvalue="9423">2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="302" stroke="rgb(255,255,255)" text-anchor="middle" x="788.9375" xml:space="preserve" y="66.93080145091932" zvalue="9424">110kV支</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="302" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="788.9375" xml:space="preserve" y="83.93080145091932" zvalue="9424">盏T线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="808" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,844.286,282.857) scale(1,1) translate(0,0)" writing-mode="lr" x="844.29" xml:space="preserve" y="287.36" zvalue="9426">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,836.286,226.857) scale(1,1) translate(0,0)" writing-mode="lr" x="836.29" xml:space="preserve" y="231.36" zvalue="9427">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="299" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,831.286,164.857) scale(1,1) translate(0,0)" writing-mode="lr" x="831.29" xml:space="preserve" y="169.36" zvalue="9429">67</text>
  <line fill="none" id="367" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.14285714285722" x2="380.1428571428568" y1="154.8704926140825" y2="154.8704926140825" zvalue="9530"/>
  <line fill="none" id="366" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="381.142857142857" x2="381.142857142857" y1="13" y2="1043" zvalue="9531"/>
  <line fill="none" id="365" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39285714285722" x2="381.3928571428568" y1="613.8704926140824" y2="613.8704926140824" zvalue="9532"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.142857142857" x2="63.99995714285706" y1="427.0000000000001" y2="427.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.142857142857" x2="63.99995714285706" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.142857142857" x2="12.142857142857" y1="427.0000000000001" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99995714285706" x2="63.99995714285706" y1="427.0000000000001" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99985714285697" x2="115.856957142857" y1="427.0000000000001" y2="427.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99985714285697" x2="115.856957142857" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99985714285697" x2="63.99985714285697" y1="427.0000000000001" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.856957142857" x2="115.856957142857" y1="427.0000000000001" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.857157142857" x2="167.714257142857" y1="427.0000000000001" y2="427.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.857157142857" x2="167.714257142857" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.857157142857" x2="115.857157142857" y1="427.0000000000001" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="167.714257142857" y1="427.0000000000001" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="219.571357142857" y1="427.0000000000001" y2="427.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="219.571357142857" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="167.714257142857" y1="427.0000000000001" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="219.571357142857" y1="427.0000000000001" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="271.428457142857" y1="427.0000000000001" y2="427.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="271.428457142857" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="219.571357142857" y1="427.0000000000001" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="271.428457142857" y1="427.0000000000001" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="323.285557142857" y1="427.0000000000001" y2="427.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="323.285557142857" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="271.428457142857" y1="427.0000000000001" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285557142857" x2="323.285557142857" y1="427.0000000000001" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285857142857" x2="375.142957142857" y1="427.0000000000001" y2="427.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285857142857" x2="375.142957142857" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285857142857" x2="323.285857142857" y1="427.0000000000001" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375.142957142857" x2="375.142957142857" y1="427.0000000000001" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.142857142857" x2="63.99995714285706" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.142857142857" x2="63.99995714285706" y1="492.5834" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.142857142857" x2="12.142857142857" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99995714285706" x2="63.99995714285706" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99985714285697" x2="115.856957142857" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99985714285697" x2="115.856957142857" y1="492.5834" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99985714285697" x2="63.99985714285697" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.856957142857" x2="115.856957142857" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.857157142857" x2="167.714257142857" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.857157142857" x2="167.714257142857" y1="492.5834" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.857157142857" x2="115.857157142857" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="167.714257142857" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="219.571357142857" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="219.571357142857" y1="492.5834" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="167.714257142857" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="219.571357142857" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="271.428457142857" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="271.428457142857" y1="492.5834" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="219.571357142857" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="271.428457142857" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="323.285557142857" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="323.285557142857" y1="492.5834" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="271.428457142857" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285557142857" x2="323.285557142857" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285857142857" x2="375.142957142857" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285857142857" x2="375.142957142857" y1="492.5834" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285857142857" x2="323.285857142857" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375.142957142857" x2="375.142957142857" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.142857142857" x2="63.99995714285706" y1="492.5833000000001" y2="492.5833000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.142857142857" x2="63.99995714285706" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.142857142857" x2="12.142857142857" y1="492.5833000000001" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99995714285706" x2="63.99995714285706" y1="492.5833000000001" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99985714285697" x2="115.856957142857" y1="492.5833000000001" y2="492.5833000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99985714285697" x2="115.856957142857" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99985714285697" x2="63.99985714285697" y1="492.5833000000001" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.856957142857" x2="115.856957142857" y1="492.5833000000001" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.857157142857" x2="167.714257142857" y1="492.5833000000001" y2="492.5833000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.857157142857" x2="167.714257142857" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.857157142857" x2="115.857157142857" y1="492.5833000000001" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="167.714257142857" y1="492.5833000000001" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="219.571357142857" y1="492.5833000000001" y2="492.5833000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="219.571357142857" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="167.714257142857" y1="492.5833000000001" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="219.571357142857" y1="492.5833000000001" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="271.428457142857" y1="492.5833000000001" y2="492.5833000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="271.428457142857" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="219.571357142857" y1="492.5833000000001" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="271.428457142857" y1="492.5833000000001" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="323.285557142857" y1="492.5833000000001" y2="492.5833000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="323.285557142857" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="271.428457142857" y1="492.5833000000001" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285557142857" x2="323.285557142857" y1="492.5833000000001" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285857142857" x2="375.142957142857" y1="492.5833000000001" y2="492.5833000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285857142857" x2="375.142957142857" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285857142857" x2="323.285857142857" y1="492.5833000000001" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375.142957142857" x2="375.142957142857" y1="492.5833000000001" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.142857142857" x2="63.99995714285706" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.142857142857" x2="63.99995714285706" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.142857142857" x2="12.142857142857" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99995714285706" x2="63.99995714285706" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99985714285697" x2="115.856957142857" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99985714285697" x2="115.856957142857" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99985714285697" x2="63.99985714285697" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.856957142857" x2="115.856957142857" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.857157142857" x2="167.714257142857" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.857157142857" x2="167.714257142857" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.857157142857" x2="115.857157142857" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="167.714257142857" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="219.571357142857" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="219.571357142857" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="167.714257142857" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="219.571357142857" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="271.428457142857" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="271.428457142857" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="219.571357142857" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="271.428457142857" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="323.285557142857" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="323.285557142857" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="271.428457142857" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285557142857" x2="323.285557142857" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285857142857" x2="375.142957142857" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285857142857" x2="375.142957142857" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285857142857" x2="323.285857142857" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375.142957142857" x2="375.142957142857" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.142857142857" x2="63.99995714285706" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.142857142857" x2="63.99995714285706" y1="572.5834" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.142857142857" x2="12.142857142857" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99995714285706" x2="63.99995714285706" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99985714285697" x2="115.856957142857" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99985714285697" x2="115.856957142857" y1="572.5834" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99985714285697" x2="63.99985714285697" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.856957142857" x2="115.856957142857" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.857157142857" x2="167.714257142857" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.857157142857" x2="167.714257142857" y1="572.5834" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.857157142857" x2="115.857157142857" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="167.714257142857" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="219.571357142857" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="219.571357142857" y1="572.5834" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="167.714257142857" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="219.571357142857" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="271.428457142857" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="271.428457142857" y1="572.5834" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="219.571357142857" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="271.428457142857" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="323.285557142857" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="323.285557142857" y1="572.5834" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="271.428457142857" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285557142857" x2="323.285557142857" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285857142857" x2="375.142957142857" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285857142857" x2="375.142957142857" y1="572.5834" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285857142857" x2="323.285857142857" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375.142957142857" x2="375.142957142857" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.142857142857" x2="63.99995714285706" y1="572.5833" y2="572.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.142857142857" x2="63.99995714285706" y1="599.25" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.142857142857" x2="12.142857142857" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99995714285706" x2="63.99995714285706" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99985714285697" x2="115.856957142857" y1="572.5833" y2="572.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99985714285697" x2="115.856957142857" y1="599.25" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99985714285697" x2="63.99985714285697" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.856957142857" x2="115.856957142857" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.857157142857" x2="167.714257142857" y1="572.5833" y2="572.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.857157142857" x2="167.714257142857" y1="599.25" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.857157142857" x2="115.857157142857" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="167.714257142857" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="219.571357142857" y1="572.5833" y2="572.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="219.571357142857" y1="599.25" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.714257142857" x2="167.714257142857" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="219.571357142857" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="271.428457142857" y1="572.5833" y2="572.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="271.428457142857" y1="599.25" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.571357142857" x2="219.571357142857" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="271.428457142857" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="323.285557142857" y1="572.5833" y2="572.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="323.285557142857" y1="599.25" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.428457142857" x2="271.428457142857" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285557142857" x2="323.285557142857" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285857142857" x2="375.142957142857" y1="572.5833" y2="572.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285857142857" x2="375.142957142857" y1="599.25" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="323.285857142857" x2="323.285857142857" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375.142957142857" x2="375.142957142857" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.392857142857" x2="100.392857142857" y1="927.0000000000002" y2="927.0000000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.392857142857" x2="100.392857142857" y1="979.1633000000002" y2="979.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.392857142857" x2="10.392857142857" y1="927.0000000000002" y2="979.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.392857142857" x2="100.392857142857" y1="927.0000000000002" y2="979.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.392857142857" x2="370.392857142857" y1="927.0000000000002" y2="927.0000000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.392857142857" x2="370.392857142857" y1="979.1633000000002" y2="979.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.392857142857" x2="100.392857142857" y1="927.0000000000002" y2="979.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.392857142857" x2="370.392857142857" y1="927.0000000000002" y2="979.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.392857142857" x2="100.392857142857" y1="979.1632700000002" y2="979.1632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.392857142857" x2="100.392857142857" y1="1007.08167" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.392857142857" x2="10.392857142857" y1="979.1632700000002" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.392857142857" x2="100.392857142857" y1="979.1632700000002" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.392857142857" x2="190.392857142857" y1="979.1632700000002" y2="979.1632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.392857142857" x2="190.392857142857" y1="1007.08167" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.392857142857" x2="100.392857142857" y1="979.1632700000002" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.392857142857" x2="190.392857142857" y1="979.1632700000002" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.3928571428571" x2="280.3928571428571" y1="979.1632700000002" y2="979.1632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.3928571428571" x2="280.3928571428571" y1="1007.08167" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.3928571428571" x2="190.3928571428571" y1="979.1632700000002" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.3928571428571" x2="280.3928571428571" y1="979.1632700000002" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.392857142857" x2="370.392857142857" y1="979.1632700000002" y2="979.1632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.392857142857" x2="370.392857142857" y1="1007.08167" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.392857142857" x2="280.392857142857" y1="979.1632700000002" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.392857142857" x2="370.392857142857" y1="979.1632700000002" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.392857142857" x2="100.392857142857" y1="1007.0816" y2="1007.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.392857142857" x2="100.392857142857" y1="1035" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.392857142857" x2="10.392857142857" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.392857142857" x2="100.392857142857" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.392857142857" x2="190.392857142857" y1="1007.0816" y2="1007.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.392857142857" x2="190.392857142857" y1="1035" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.392857142857" x2="100.392857142857" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.392857142857" x2="190.392857142857" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.3928571428571" x2="280.3928571428571" y1="1007.0816" y2="1007.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.3928571428571" x2="280.3928571428571" y1="1035" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.3928571428571" x2="190.3928571428571" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.3928571428571" x2="280.3928571428571" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.392857142857" x2="370.392857142857" y1="1007.0816" y2="1007.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.392857142857" x2="370.392857142857" y1="1035" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.392857142857" x2="280.392857142857" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.392857142857" x2="370.392857142857" y1="1007.0816" y2="1035"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="361" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,60.6964,955.25) scale(1,1) translate(0,0)" writing-mode="lr" x="27.39" xml:space="preserve" y="961.25" zvalue="9536">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="360" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,60.1964,994) scale(1,1) translate(0,0)" writing-mode="lr" x="39.39" xml:space="preserve" y="1000" zvalue="9537">制图              </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="359" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,281.571,994) scale(1,1) translate(4.39966e-14,0)" writing-mode="lr" x="198.14" xml:space="preserve" y="1000" zvalue="9538">绘制日期     20200716</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="358" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.1429,1022) scale(1,1) translate(0,0)" writing-mode="lr" x="54.14" xml:space="preserve" y="1028" zvalue="9539">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="357" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.143,1022) scale(1,1) translate(0,0)" writing-mode="lr" x="232.14" xml:space="preserve" y="1028" zvalue="9540">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="356" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,193.567,446.125) scale(1,1) translate(0,-9.52571e-14)" writing-mode="lr" x="193.5666666666665" xml:space="preserve" y="450.6250000000001" zvalue="9541">35kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="355" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,245.314,446.125) scale(1,1) translate(0,-9.52571e-14)" writing-mode="lr" x="245.3142857142855" xml:space="preserve" y="450.6250000000001" zvalue="9542">35kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="354" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,38.1429,482.75) scale(1,1) translate(0,0)" writing-mode="lr" x="38.142857142857" xml:space="preserve" y="487.2500000000001" zvalue="9543">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="353" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,38.1429,508.25) scale(1,1) translate(0,5.48173e-14)" writing-mode="lr" x="38.142857142857" xml:space="preserve" y="512.7500000000001" zvalue="9544">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="352" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,38.1429,533.75) scale(1,1) translate(0,0)" writing-mode="lr" x="38.142857142857" xml:space="preserve" y="538.2500000000001" zvalue="9545">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="351" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,38.1429,559.25) scale(1,1) translate(0,-1.20959e-13)" writing-mode="lr" x="38.142857142857" xml:space="preserve" y="563.7500000000001" zvalue="9546">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="350" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,38.1429,584.75) scale(1,1) translate(0,0)" writing-mode="lr" x="38.142857142857" xml:space="preserve" y="589.25" zvalue="9547">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="349" stroke="rgb(255,255,255)" text-anchor="middle" x="90.0703125" xml:space="preserve" y="443.1250000000001" zvalue="9548">110kVⅠ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="349" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="90.0703125" xml:space="preserve" y="460.1250000000001" zvalue="9548">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="348" stroke="rgb(255,255,255)" text-anchor="middle" x="141.8203125" xml:space="preserve" y="443.1406250000001" zvalue="9549">110kVⅡ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="348" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="141.8203125" xml:space="preserve" y="460.1406250000001" zvalue="9549">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="347" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,297.062,446.125) scale(1,1) translate(0,-9.52571e-14)" writing-mode="lr" x="297.0619047619045" xml:space="preserve" y="450.6250000000001" zvalue="9550">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="346" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,348.81,446.125) scale(1,1) translate(0,-9.52571e-14)" writing-mode="lr" x="348.8095238095235" xml:space="preserve" y="450.6250000000001" zvalue="9551">10kVⅡ母</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.142857142857" x2="195.142857142857" y1="164.0000000000001" y2="164.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.142857142857" x2="195.142857142857" y1="190.0000000000001" y2="190.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.142857142857" x2="14.142857142857" y1="164.0000000000001" y2="190.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="195.142857142857" y1="164.0000000000001" y2="190.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="376.142857142857" y1="164.0000000000001" y2="164.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="376.142857142857" y1="190.0000000000001" y2="190.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="195.142857142857" y1="164.0000000000001" y2="190.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376.142857142857" x2="376.142857142857" y1="164.0000000000001" y2="190.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.142857142857" x2="195.142857142857" y1="190.0000000000001" y2="190.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.142857142857" x2="195.142857142857" y1="214.2500000000001" y2="214.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.142857142857" x2="14.142857142857" y1="190.0000000000001" y2="214.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="195.142857142857" y1="190.0000000000001" y2="214.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="376.142857142857" y1="190.0000000000001" y2="190.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="376.142857142857" y1="214.2500000000001" y2="214.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="195.142857142857" y1="190.0000000000001" y2="214.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376.142857142857" x2="376.142857142857" y1="190.0000000000001" y2="214.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.142857142857" x2="195.142857142857" y1="214.2500000000001" y2="214.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.142857142857" x2="195.142857142857" y1="238.5000000000001" y2="238.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.142857142857" x2="14.142857142857" y1="214.2500000000001" y2="238.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="195.142857142857" y1="214.2500000000001" y2="238.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="376.142857142857" y1="214.2500000000001" y2="214.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="376.142857142857" y1="238.5000000000001" y2="238.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="195.142857142857" y1="214.2500000000001" y2="238.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376.142857142857" x2="376.142857142857" y1="214.2500000000001" y2="238.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.142857142857" x2="195.142857142857" y1="238.5000000000001" y2="238.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.142857142857" x2="195.142857142857" y1="261.2500000000001" y2="261.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.142857142857" x2="14.142857142857" y1="238.5000000000001" y2="261.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="195.142857142857" y1="238.5000000000001" y2="261.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="376.142857142857" y1="238.5000000000001" y2="238.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="376.142857142857" y1="261.2500000000001" y2="261.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="195.142857142857" y1="238.5000000000001" y2="261.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376.142857142857" x2="376.142857142857" y1="238.5000000000001" y2="261.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.142857142857" x2="195.142857142857" y1="261.2500000000001" y2="261.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.142857142857" x2="195.142857142857" y1="284.0000000000001" y2="284.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.142857142857" x2="14.142857142857" y1="261.2500000000001" y2="284.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="195.142857142857" y1="261.2500000000001" y2="284.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="376.142857142857" y1="261.2500000000001" y2="261.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="376.142857142857" y1="284.0000000000001" y2="284.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="195.142857142857" y1="261.2500000000001" y2="284.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376.142857142857" x2="376.142857142857" y1="261.2500000000001" y2="284.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.142857142857" x2="195.142857142857" y1="284.0000000000001" y2="284.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.142857142857" x2="195.142857142857" y1="306.7500000000001" y2="306.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.142857142857" x2="14.142857142857" y1="284.0000000000001" y2="306.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="195.142857142857" y1="284.0000000000001" y2="306.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="376.142857142857" y1="284.0000000000001" y2="284.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="376.142857142857" y1="306.7500000000001" y2="306.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.142857142857" x2="195.142857142857" y1="284.0000000000001" y2="306.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376.142857142857" x2="376.142857142857" y1="284.0000000000001" y2="306.7500000000001"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="344" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.1429,178) scale(1,1) translate(0,0)" writing-mode="lr" x="52.14" xml:space="preserve" y="182.5" zvalue="9553">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="343" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.143,178) scale(1,1) translate(0,0)" writing-mode="lr" x="232.14" xml:space="preserve" y="182.5" zvalue="9554">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="342" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,64.0804,203.25) scale(1,1) translate(0,0)" writing-mode="lr" x="64.08" xml:space="preserve" y="207.75" zvalue="9555">110kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="340" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,245.893,203) scale(1,1) translate(0,0)" writing-mode="lr" x="245.89" xml:space="preserve" y="207.5" zvalue="9556">110kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="339" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,72.0804,274) scale(1,1) translate(0,0)" writing-mode="lr" x="72.08" xml:space="preserve" y="278.5" zvalue="9557">110kV#1主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="338" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,253.393,273.5) scale(1,1) translate(0,0)" writing-mode="lr" x="253.39" xml:space="preserve" y="278" zvalue="9558">110kV#2主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="337" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,72.0804,297) scale(1,1) translate(0,0)" writing-mode="lr" x="72.08" xml:space="preserve" y="301.5" zvalue="9559">110kV#1主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="336" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,253.393,296.5) scale(1,1) translate(0,0)" writing-mode="lr" x="253.39" xml:space="preserve" y="301" zvalue="9560">110kV#2主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="335" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.0804,228.25) scale(1,1) translate(0,0)" writing-mode="lr" x="60.08" xml:space="preserve" y="232.75" zvalue="9561">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="334" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241.893,228) scale(1,1) translate(0,0)" writing-mode="lr" x="241.89" xml:space="preserve" y="232.5" zvalue="9562">35kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="333" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.0804,250.75) scale(1,1) translate(0,0)" writing-mode="lr" x="60.08" xml:space="preserve" y="255.25" zvalue="9563">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="332" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241.893,251.75) scale(1,1) translate(0,0)" writing-mode="lr" x="241.89" xml:space="preserve" y="256.25" zvalue="9564">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="331" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,194.541,341.341) scale(1,1) translate(0,0)" writing-mode="lr" x="194.54" xml:space="preserve" y="345.84" zvalue="9565">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="330" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,299.541,341.341) scale(1,1) translate(0,0)" writing-mode="lr" x="299.54" xml:space="preserve" y="345.84" zvalue="9566">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="328" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.3929,635.25) scale(1,1) translate(0,0)" writing-mode="lr" x="80.392857142857" xml:space="preserve" y="639.7500000000001" zvalue="9568">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="379" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1103.29,251.357) scale(1,1) translate(0,0)" writing-mode="lr" x="1103.29" xml:space="preserve" y="255.86" zvalue="9620">175</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="378" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1096.79,186.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1096.79" xml:space="preserve" y="191.36" zvalue="9621">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="404" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1152.77,318.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1152.77" xml:space="preserve" y="323.36" zvalue="9623">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="377" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1103.29,318.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1103.29" xml:space="preserve" y="323.36" zvalue="9625">2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="399" stroke="rgb(255,255,255)" text-anchor="middle" x="1085.84375" xml:space="preserve" y="66.93080145091932" zvalue="9625">110kV勐乃电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="399" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1085.84375" xml:space="preserve" y="83.93080145091932" zvalue="9625">站线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="809" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1115.29,281.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1115.29" xml:space="preserve" y="286.36" zvalue="9627">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="376" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1130.29,226.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1130.29" xml:space="preserve" y="231.36" zvalue="9628">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="375" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1125.29,164.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1125.29" xml:space="preserve" y="169.36" zvalue="9630">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="414" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1209.29,251.357) scale(1,1) translate(1.46218e-12,0)" writing-mode="lr" x="1209.29" xml:space="preserve" y="255.86" zvalue="9643">177</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="413" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1202.79,186.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1202.79" xml:space="preserve" y="191.36" zvalue="9644">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="411" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1258.77,318.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1258.77" xml:space="preserve" y="323.36" zvalue="9647">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="409" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1209.29,318.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1209.29" xml:space="preserve" y="323.36" zvalue="9649">2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="407" stroke="rgb(255,255,255)" text-anchor="middle" x="1189.4609375" xml:space="preserve" y="66.93080145091932" zvalue="9650">110kV松坡电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="407" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1189.4609375" xml:space="preserve" y="83.93080145091932" zvalue="9650">站线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="810" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1221.29,281.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1221.29" xml:space="preserve" y="286.36" zvalue="9652">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="406" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1236.29,226.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1236.29" xml:space="preserve" y="231.36" zvalue="9653">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="405" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1231.29,164.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1231.29" xml:space="preserve" y="169.36" zvalue="9655">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="457" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1327.29,251.357) scale(1,1) translate(0,0)" writing-mode="lr" x="1327.29" xml:space="preserve" y="255.86" zvalue="9668">178</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="456" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1320.79,186.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1320.79" xml:space="preserve" y="191.36" zvalue="9669">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="454" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1376.77,318.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1376.77" xml:space="preserve" y="323.36" zvalue="9672">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="453" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1327.29,318.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1327.29" xml:space="preserve" y="323.36" zvalue="9674">2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="452" stroke="rgb(255,255,255)" text-anchor="middle" x="1307.09375" xml:space="preserve" y="66.93080145091932" zvalue="9675">110kV狮子山电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="452" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1307.09375" xml:space="preserve" y="83.93080145091932" zvalue="9675">站线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="811" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1339.29,281.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1339.29" xml:space="preserve" y="286.36" zvalue="9677">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="451" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1354.29,226.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1354.29" xml:space="preserve" y="231.36" zvalue="9678">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="450" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1349.29,164.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1349.29" xml:space="preserve" y="169.36" zvalue="9680">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="476" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,898.429,681.286) scale(1,1) translate(0,0)" writing-mode="lr" x="898.4299999999999" xml:space="preserve" y="685.79" zvalue="9691">35kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="479" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1440,687.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1440" xml:space="preserve" y="692.36" zvalue="9695">35kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="481" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,467.145,586.794) scale(1,1) translate(0,0)" writing-mode="lr" x="467.15" xml:space="preserve" y="591.29" zvalue="9697">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="480" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,443.937,674.016) scale(1,1) translate(0,0)" writing-mode="lr" x="443.94" xml:space="preserve" y="678.52" zvalue="9699">3711</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="572" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,512.159,731.159) scale(1,1) translate(0,0)" writing-mode="lr" x="512.16" xml:space="preserve" y="735.66" zvalue="9766">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="589" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,516.855,774.163) scale(1,1) translate(0,0)" writing-mode="lr" x="516.86" xml:space="preserve" y="778.66" zvalue="9770">372</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="573" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,510.717,827.889) scale(1,1) translate(0,0)" writing-mode="lr" x="510.72" xml:space="preserve" y="832.39" zvalue="9773">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="580" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,485.247,965.319) scale(1,1) translate(0,0)" writing-mode="lr" x="485.25" xml:space="preserve" y="969.8200000000001" zvalue="9776">35kV西那线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="571" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,486.829,890.031) scale(1,1) translate(0,0)" writing-mode="lr" x="486.83" xml:space="preserve" y="894.53" zvalue="9778">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="577" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,463.985,733.52) scale(1,1) translate(0,0)" writing-mode="lr" x="463.99" xml:space="preserve" y="738.02" zvalue="9802">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="578" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,463.096,784.123) scale(1,1) translate(0,0)" writing-mode="lr" x="463.1" xml:space="preserve" y="788.62" zvalue="9805">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="579" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,461.414,841.71) scale(1,1) translate(0,0)" writing-mode="lr" x="461.41" xml:space="preserve" y="846.21" zvalue="9808">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="597" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,594.159,731.159) scale(1,1) translate(0,0)" writing-mode="lr" x="594.16" xml:space="preserve" y="735.66" zvalue="9821">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="596" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,598.855,774.163) scale(1,1) translate(0,0)" writing-mode="lr" x="598.86" xml:space="preserve" y="778.66" zvalue="9823">373</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="595" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,592.717,827.889) scale(1,1) translate(0,0)" writing-mode="lr" x="592.72" xml:space="preserve" y="832.39" zvalue="9827">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="594" stroke="rgb(255,255,255)" text-anchor="middle" x="577.84375" xml:space="preserve" y="971.9027784589738" zvalue="9830">35kV昆润采选</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="594" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="577.84375" xml:space="preserve" y="988.9027784589738" zvalue="9830">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="593" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,568.829,890.031) scale(1,1) translate(0,0)" writing-mode="lr" x="568.83" xml:space="preserve" y="894.53" zvalue="9832">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="592" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,545.985,733.52) scale(1,1) translate(0,0)" writing-mode="lr" x="545.99" xml:space="preserve" y="738.02" zvalue="9838">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="591" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,545.096,784.123) scale(1,1) translate(0,0)" writing-mode="lr" x="545.1" xml:space="preserve" y="788.62" zvalue="9840">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="590" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,543.414,841.71) scale(1,1) translate(0,0)" writing-mode="lr" x="543.41" xml:space="preserve" y="846.21" zvalue="9844">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="622" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,676.159,731.159) scale(1,1) translate(0,0)" writing-mode="lr" x="676.16" xml:space="preserve" y="735.66" zvalue="9848">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="621" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,680.855,774.163) scale(1,1) translate(0,0)" writing-mode="lr" x="680.86" xml:space="preserve" y="778.66" zvalue="9850">374</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="620" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,674.717,827.889) scale(1,1) translate(0,0)" writing-mode="lr" x="674.72" xml:space="preserve" y="832.39" zvalue="9854">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="619" stroke="rgb(255,255,255)" text-anchor="middle" x="664.46875" xml:space="preserve" y="971.9027784589738" zvalue="9857">35kV曼悠河一</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="619" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="664.46875" xml:space="preserve" y="988.9027784589738" zvalue="9857">级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="632" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,638.829,902.031) scale(1,1) translate(0,0)" writing-mode="lr" x="638.83" xml:space="preserve" y="906.53" zvalue="9858">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="618" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,627.985,733.52) scale(1,1) translate(0,0)" writing-mode="lr" x="627.99" xml:space="preserve" y="738.02" zvalue="9864">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="617" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,627.096,784.123) scale(1,1) translate(0,0)" writing-mode="lr" x="627.1" xml:space="preserve" y="788.62" zvalue="9866">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="616" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,625.414,841.71) scale(1,1) translate(0,0)" writing-mode="lr" x="625.41" xml:space="preserve" y="846.21" zvalue="9870">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="675" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,849.714,752.964) scale(1,1) translate(0,0)" writing-mode="lr" x="849.71" xml:space="preserve" y="757.46" zvalue="9901">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="674" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,860.214,787.714) scale(1,1) translate(0,0)" writing-mode="lr" x="860.21" xml:space="preserve" y="792.21" zvalue="9905">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="687" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,865.714,737.893) scale(1,1) translate(0,0)" writing-mode="lr" x="865.71" xml:space="preserve" y="742.39" zvalue="9909">10</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="708" stroke="rgb(255,255,255)" text-anchor="middle" x="1355" xml:space="preserve" y="848.6450892857143" zvalue="9912">35kVⅡ段母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="708" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1355" xml:space="preserve" y="865.6450892857143" zvalue="9912">互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="707" stroke="rgb(255,255,255)" text-anchor="middle" x="815.5" xml:space="preserve" y="877.5625" zvalue="9915">35kVⅠ段母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="707" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="815.5" xml:space="preserve" y="894.5625" zvalue="9915">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="706" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1326.66,744.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1326.66" xml:space="preserve" y="748.64" zvalue="9917">3902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="723" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,728.802,790.413) scale(1,1) translate(0,0)" writing-mode="lr" x="728.8" xml:space="preserve" y="794.91" zvalue="9928">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="725" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,721.542,850.175) scale(1,1) translate(0,0)" writing-mode="lr" x="721.54" xml:space="preserve" y="854.67" zvalue="9936">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="724" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,722.85,743.603) scale(1,1) translate(0,0)" writing-mode="lr" x="722.85" xml:space="preserve" y="748.1" zvalue="9938">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="726" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,781.414,745.591) scale(1,1) translate(0,0)" writing-mode="lr" x="781.41" xml:space="preserve" y="750.09" zvalue="9944">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="727" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,781.699,798.734) scale(1,1) translate(0,0)" writing-mode="lr" x="781.7" xml:space="preserve" y="803.23" zvalue="9947">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1030.79,755.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1030.785714285714" xml:space="preserve" y="760.0714285714284" zvalue="9951">35kV分段312</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,915.429,747.571) scale(1,1) translate(0,0)" writing-mode="lr" x="915.4299999999999" xml:space="preserve" y="752.0700000000001" zvalue="9953">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1082.71,742.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1082.71" xml:space="preserve" y="747.21" zvalue="9955">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,974.929,800.071) scale(1,1) translate(-2.15589e-13,0)" writing-mode="lr" x="974.9299999999999" xml:space="preserve" y="804.5700000000001" zvalue="9957">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,932.632,814.302) scale(1,1) translate(0,0)" writing-mode="lr" x="932.63" xml:space="preserve" y="818.8" zvalue="9967">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1228.8,790.413) scale(1,1) translate(0,0)" writing-mode="lr" x="1228.8" xml:space="preserve" y="794.91" zvalue="9971">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1228.69,848.746) scale(1,1) translate(0,0)" writing-mode="lr" x="1228.69" xml:space="preserve" y="853.25" zvalue="9973">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1224.28,745.032) scale(1,1) translate(0,0)" writing-mode="lr" x="1224.28" xml:space="preserve" y="749.53" zvalue="9975">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1163.41,805.591) scale(1,1) translate(0,0)" writing-mode="lr" x="1163.41" xml:space="preserve" y="810.09" zvalue="9980">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1163.7,870.734) scale(1,1) translate(0,0)" writing-mode="lr" x="1163.7" xml:space="preserve" y="875.23" zvalue="9984">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1510.43,76.7143) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.43" xml:space="preserve" y="81.20999999999999" zvalue="10001">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1519.43,974.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1519.43" xml:space="preserve" y="979.36" zvalue="10002">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1593.43,125.327) scale(1,1) translate(0,0)" writing-mode="lr" x="1593.43" xml:space="preserve" y="129.83" zvalue="10003">071</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1674.28,153.707) scale(1,1) translate(0,0)" writing-mode="lr" x="1674.28" xml:space="preserve" y="158.21" zvalue="10006">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1674.5,126.476) scale(1,1) translate(0,0)" writing-mode="lr" x="1674.5" xml:space="preserve" y="130.98" zvalue="10012">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1756,111.53) scale(1,1) translate(0,0)" writing-mode="lr" x="1756" xml:space="preserve" y="116.03" zvalue="10017">10kV盏西乡专线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1595.43,222.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1595.43" xml:space="preserve" y="227.36" zvalue="10028">072</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1679.5,257.412) scale(1,1) translate(0,0)" writing-mode="lr" x="1679.5" xml:space="preserve" y="261.91" zvalue="10031">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1676.86,226) scale(1,1) translate(0,0)" writing-mode="lr" x="1676.86" xml:space="preserve" y="230.5" zvalue="10034">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="171" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1765.14,210.101) scale(1,1) translate(0,0)" writing-mode="lr" x="1765.14" xml:space="preserve" y="214.6" zvalue="10037">10kV支那乡专线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="192" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1594.43,312.607) scale(1,1) translate(0,0)" writing-mode="lr" x="1594.43" xml:space="preserve" y="317.11" zvalue="10042">073</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="191" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1679.5,346.162) scale(1,1) translate(0,0)" writing-mode="lr" x="1679.5" xml:space="preserve" y="350.66" zvalue="10045">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1676.86,314.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1676.86" xml:space="preserve" y="319.25" zvalue="10049">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="189" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1766.57,294.566) scale(1,1) translate(0,0)" writing-mode="lr" x="1766.57" xml:space="preserve" y="299.07" zvalue="10052">10kV东洪坝线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1715.06,456.579) scale(1,1) translate(0,0)" writing-mode="lr" x="1715.06" xml:space="preserve" y="461.08" zvalue="10057">10kVⅠ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1625.78,403.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1625.78" xml:space="preserve" y="408.06" zvalue="10061">0801</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="227" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1624.89,440.479) scale(1,1) translate(0,0)" writing-mode="lr" x="1624.89" xml:space="preserve" y="444.98" zvalue="10063">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1597.18,663.607) scale(1,1) translate(0,0)" writing-mode="lr" x="1597.18" xml:space="preserve" y="668.11" zvalue="10072">082</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1682.25,697.162) scale(1,1) translate(0,0)" writing-mode="lr" x="1682.25" xml:space="preserve" y="701.66" zvalue="10075">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1679.61,665.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1679.61" xml:space="preserve" y="670.25" zvalue="10079">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1767.89,646.994) scale(1,1) translate(0,4.95432e-13)" writing-mode="lr" x="1767.89" xml:space="preserve" y="651.49" zvalue="10082">10kV盏西街道线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1597.18,758.607) scale(1,1) translate(0,0)" writing-mode="lr" x="1597.18" xml:space="preserve" y="763.11" zvalue="10087">083</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1734.25,792.162) scale(1,1) translate(0,0)" writing-mode="lr" x="1734.25" xml:space="preserve" y="796.66" zvalue="10090">0020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1679.61,760.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1679.61" xml:space="preserve" y="765.25" zvalue="10094">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="729" stroke="rgb(255,255,255)" text-anchor="middle" x="1768.8984375" xml:space="preserve" y="746.6741068703788" zvalue="10102">10kV2号接地变及消</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="729" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1768.8984375" xml:space="preserve" y="763.6741068703788" zvalue="10102">弧线圈</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="503" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1732.05,869.338) scale(1,1) translate(0,0)" writing-mode="lr" x="1732.05" xml:space="preserve" y="873.84" zvalue="10106">10kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="504" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1684.03,961.045) scale(1,1) translate(0,0)" writing-mode="lr" x="1684.03" xml:space="preserve" y="965.55" zvalue="10108">10kVⅡ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="498" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1585.89,929.813) scale(1,1) translate(0,0)" writing-mode="lr" x="1585.89" xml:space="preserve" y="934.3099999999999" zvalue="10112">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="502" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1585,879) scale(1,1) translate(0,0)" writing-mode="lr" x="1585" xml:space="preserve" y="883.5" zvalue="10115">0812</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1410.32,412.179) scale(1,1) translate(0,0)" writing-mode="lr" x="1410.32" xml:space="preserve" y="416.68" zvalue="10119">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="510" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1449.43,622.538) scale(1,1) translate(0,0)" writing-mode="lr" x="1449.43" xml:space="preserve" y="627.04" zvalue="10126">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="516" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1316.71,529.442) scale(1,1) translate(0,0)" writing-mode="lr" x="1316.71" xml:space="preserve" y="533.9400000000001" zvalue="10138">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="522" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1670.68,589.143) scale(1,1) translate(0,6.37585e-14)" writing-mode="lr" x="1670.678571428571" xml:space="preserve" y="593.6428583690096" zvalue="10143">10kV分段012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="526" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1572,509.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1572" xml:space="preserve" y="514.21" zvalue="10146">0121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="539" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1370.82,682.591) scale(1,1) translate(0,0)" writing-mode="lr" x="1370.82" xml:space="preserve" y="687.09" zvalue="10159">3020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" x="1191.90625" xml:space="preserve" y="594.5161068531397" zvalue="10277">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1191.90625" xml:space="preserve" y="611.5161068531397" zvalue="10277">31.5MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,500.699,638.734) scale(1,1) translate(0,0)" writing-mode="lr" x="500.7" xml:space="preserve" y="643.23" zvalue="10282">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="106" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,230.196,953.25) scale(1,1) translate(3.65025e-14,0)" writing-mode="lr" x="164.39" xml:space="preserve" y="959.25" zvalue="10405">ZhanXi-01-2018</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="107" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,145.196,992) scale(1,1) translate(0,0)" writing-mode="lr" x="124.39" xml:space="preserve" y="998" zvalue="10407">段勇</text>
  <ellipse cx="926.66" cy="313.66" fill="rgb(255,0,0)" fill-opacity="1" id="812" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10492"/>
  <ellipse cx="769.66" cy="248.66" fill="rgb(255,0,0)" fill-opacity="1" id="813" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10494"/>
  <ellipse cx="1065.66" cy="249.66" fill="rgb(255,0,0)" fill-opacity="1" id="814" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10496"/>
  <ellipse cx="685.66" cy="473.66" fill="rgb(255,0,0)" fill-opacity="1" id="815" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10498"/>
  <ellipse cx="1241.66" cy="473.66" fill="rgb(255,0,0)" fill-opacity="1" id="825" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10500"/>
  <ellipse cx="1169.66" cy="249.66" fill="rgb(255,0,0)" fill-opacity="1" id="826" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10502"/>
  <ellipse cx="1284.66" cy="249.66" fill="rgb(255,0,0)" fill-opacity="1" id="827" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10504"/>
  <ellipse cx="439.66" cy="248.66" fill="rgb(255,0,0)" fill-opacity="1" id="828" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10506"/>
  <ellipse cx="542.66" cy="248.66" fill="rgb(255,0,0)" fill-opacity="1" id="829" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10508"/>
  <ellipse cx="663.66" cy="248.66" fill="rgb(255,0,0)" fill-opacity="1" id="830" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10510"/>
  <ellipse cx="444.66" cy="316.66" fill="rgb(255,0,0)" fill-opacity="1" id="832" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10514"/>
  <ellipse cx="495.66" cy="316.66" fill="rgb(255,0,0)" fill-opacity="1" id="833" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10516"/>
  <ellipse cx="549.66" cy="316.66" fill="rgb(255,0,0)" fill-opacity="1" id="834" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10518"/>
  <ellipse cx="600.66" cy="316.66" fill="rgb(255,0,0)" fill-opacity="1" id="835" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10519"/>
  <ellipse cx="666.66" cy="316.66" fill="rgb(255,0,0)" fill-opacity="1" id="836" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10521"/>
  <ellipse cx="717.66" cy="316.66" fill="rgb(255,0,0)" fill-opacity="1" id="837" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10522"/>
  <ellipse cx="771.66" cy="316.66" fill="rgb(255,0,0)" fill-opacity="1" id="838" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10524"/>
  <ellipse cx="822.66" cy="316.66" fill="rgb(255,0,0)" fill-opacity="1" id="839" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10525"/>
  <ellipse cx="868.66" cy="316.66" fill="rgb(255,0,0)" fill-opacity="1" id="840" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10527"/>
  <ellipse cx="955.66" cy="316.66" fill="rgb(255,0,0)" fill-opacity="1" id="841" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10528"/>
  <ellipse cx="1065.66" cy="316.66" fill="rgb(255,0,0)" fill-opacity="1" id="842" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10530"/>
  <ellipse cx="1116.66" cy="316.66" fill="rgb(255,0,0)" fill-opacity="1" id="843" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10531"/>
  <ellipse cx="1170.66" cy="316.66" fill="rgb(255,0,0)" fill-opacity="1" id="844" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10533"/>
  <ellipse cx="1221.66" cy="316.66" fill="rgb(255,0,0)" fill-opacity="1" id="845" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10534"/>
  <ellipse cx="1291.66" cy="316.66" fill="rgb(255,0,0)" fill-opacity="1" id="846" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10536"/>
  <ellipse cx="1342.66" cy="316.66" fill="rgb(255,0,0)" fill-opacity="1" id="847" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10537"/>
  <ellipse cx="686.66" cy="421.66" fill="rgb(255,0,0)" fill-opacity="1" id="848" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10539"/>
  <ellipse cx="639.66" cy="421.66" fill="rgb(255,0,0)" fill-opacity="1" id="849" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10541"/>
  <ellipse cx="1243.66" cy="421.66" fill="rgb(255,0,0)" fill-opacity="1" id="851" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10543"/>
  <ellipse cx="1196.66" cy="421.66" fill="rgb(255,0,0)" fill-opacity="1" id="850" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10544"/>
  <ellipse cx="472.66" cy="413.66" fill="rgb(255,0,0)" fill-opacity="1" id="820" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10551"/>
  <ellipse cx="643.66" cy="773.66" fill="rgb(255,0,0)" fill-opacity="1" id="819" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10553"/>
 </g>
 <g id="ButtonClass">
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="221" y="392.25" zvalue="10397"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="119.09" y="392.25" zvalue="10399"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="19.19" y="392.25" zvalue="10400"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="19.19" y="355.75" zvalue="10401"/></g>
  <g href="全站公用_遥控.svg"><rect fill-opacity="0" height="24" width="72.88" x="19.19" y="317.25" zvalue="10427"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="48">
   <path class="kv110" d="M 435.95 352.95 L 1475.29 352.95" stroke-width="6" zvalue="7571"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674413379587" ObjectName="110kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674413379587"/></metadata>
  <path d="M 435.95 352.95 L 1475.29 352.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv110" d="M 439.38 379.71 L 1476.29 379.71" stroke-width="6" zvalue="7573"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674413314051" ObjectName="110kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674413314051"/></metadata>
  <path d="M 439.38 379.71 L 1476.29 379.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="475">
   <path class="kv35" d="M 447.14 703.71 L 925.71 703.71" stroke-width="6" zvalue="9690"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674413445123" ObjectName="35kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674413445123"/></metadata>
  <path d="M 447.14 703.71 L 925.71 703.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="478">
   <path class="kv35" d="M 1072.86 701.71 L 1455.71 701.71" stroke-width="6" zvalue="9694"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674413510659" ObjectName="35kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674413510659"/></metadata>
  <path d="M 1072.86 701.71 L 1455.71 701.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv10" d="M 1526.29 92.29 L 1526.29 500.57" stroke-width="6" zvalue="10000"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674413576195" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674413576195"/></metadata>
  <path d="M 1526.29 92.29 L 1526.29 500.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="kv10" d="M 1530.29 576.75 L 1530.29 958" stroke-width="6" zvalue="10001"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674413641731" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674413641731"/></metadata>
  <path d="M 1530.29 576.75 L 1530.29 958" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="14">
   <use class="kv110" height="40" transform="rotate(0,597.73,553.362) scale(-0.727779,1.1) translate(-1424.48,-48.3056)" width="40" x="583.1745624513943" xlink:href="#GroundDisconnector:中性点地刀12_0" y="531.361616565419" zvalue="7578"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454444515331" ObjectName="#1主变110kV侧中性点1010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454444515331"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,597.73,553.362) scale(-0.727779,1.1) translate(-1424.48,-48.3056)" width="40" x="583.1745624513943" y="531.361616565419"/></g>
  <g id="412">
   <use class="kv110" height="40" transform="rotate(0,1161.73,542.362) scale(-0.727779,1.1) translate(-2763.44,-47.3056)" width="40" x="1147.174562451394" xlink:href="#GroundDisconnector:中性点地刀12_0" y="520.361616565419" zvalue="7947"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454444646403" ObjectName="#2主变110kV侧中性点1020接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454444646403"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1161.73,542.362) scale(-0.727779,1.1) translate(-2763.44,-47.3056)" width="40" x="1147.174562451394" y="520.361616565419"/></g>
  <g id="16">
   <use class="kv110" height="20" transform="rotate(0,508.286,282.857) scale(1,-1) translate(0,-565.714)" width="10" x="503.2857142857142" xlink:href="#GroundDisconnector:地刀_0" y="272.8571428571429" zvalue="8769"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454445039619" ObjectName="110kV傣盏Ⅰ回线17117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454445039619"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,508.286,282.857) scale(1,-1) translate(0,-565.714)" width="10" x="503.2857142857142" y="272.8571428571429"/></g>
  <g id="18">
   <use class="kv110" height="20" transform="rotate(270,507.286,213.857) scale(1,1) translate(0,0)" width="10" x="502.2857142857142" xlink:href="#GroundDisconnector:地刀_0" y="203.8571428571429" zvalue="8770"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454445170691" ObjectName="110kV傣盏Ⅰ回线17160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454445170691"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,507.286,213.857) scale(1,1) translate(0,0)" width="10" x="502.2857142857142" y="203.8571428571429"/></g>
  <g id="20">
   <use class="kv110" height="20" transform="rotate(270,507.286,147.857) scale(1,1) translate(0,0)" width="10" x="502.2857142857142" xlink:href="#GroundDisconnector:地刀_0" y="137.8571428571429" zvalue="8772"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454445301763" ObjectName="110kV傣盏Ⅰ回线17167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454445301763"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,507.286,147.857) scale(1,1) translate(0,0)" width="10" x="502.2857142857142" y="137.8571428571429"/></g>
  <g id="252">
   <use class="kv110" height="20" transform="rotate(270,1469.29,262.857) scale(1,1) translate(0,0)" width="10" x="1464.285714285714" xlink:href="#GroundDisconnector:地刀_0" y="252.8571428571429" zvalue="8969"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454445694979" ObjectName="110kVⅠ母电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454445694979"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1469.29,262.857) scale(1,1) translate(0,0)" width="10" x="1464.285714285714" y="252.8571428571429"/></g>
  <g id="267">
   <use class="kv110" height="20" transform="rotate(270,532.286,441.857) scale(1,1) translate(0,0)" width="10" x="527.2857142857142" xlink:href="#GroundDisconnector:地刀_0" y="431.8571428571429" zvalue="8980"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454445891587" ObjectName="110kVⅡ母电压互感器19027接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454445891587"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,532.286,441.857) scale(1,1) translate(0,0)" width="10" x="527.2857142857142" y="431.8571428571429"/></g>
  <g id="45">
   <use class="kv110" height="20" transform="rotate(270,750.286,442.907) scale(1,1) translate(0,0)" width="10" x="745.2857142857143" xlink:href="#GroundDisconnector:地刀_0" y="432.9071428571428" zvalue="8990"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454446153731" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454446153731"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,750.286,442.907) scale(1,1) translate(0,0)" width="10" x="745.2857142857143" y="432.9071428571428"/></g>
  <g id="51">
   <use class="kv110" height="20" transform="rotate(270,750.286,500.019) scale(1,1) translate(0,0)" width="10" x="745.2857142857143" xlink:href="#GroundDisconnector:地刀_0" y="490.0194001008064" zvalue="8992"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454446284803" ObjectName="#1主变110kV侧10167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454446284803"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,750.286,500.019) scale(1,1) translate(0,0)" width="10" x="745.2857142857143" y="490.0194001008064"/></g>
  <g id="439">
   <use class="kv110" height="20" transform="rotate(270,1307.29,442.929) scale(1,1) translate(0,0)" width="10" x="1302.285714285714" xlink:href="#GroundDisconnector:地刀_0" y="432.9285856101629" zvalue="9152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454446678019" ObjectName="#2主变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454446678019"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1307.29,442.929) scale(1,1) translate(0,0)" width="10" x="1302.285714285714" y="432.9285856101629"/></g>
  <g id="438">
   <use class="kv110" height="20" transform="rotate(270,1307.29,499.857) scale(1,1) translate(0,0)" width="10" x="1302.285714285714" xlink:href="#GroundDisconnector:地刀_0" y="489.8571428571428" zvalue="9154"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454446546947" ObjectName="#2主变110kV侧10260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454446546947"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1307.29,499.857) scale(1,1) translate(0,0)" width="10" x="1302.285714285714" y="489.8571428571428"/></g>
  <g id="86">
   <use class="kv110" height="20" transform="rotate(0,969.286,263.857) scale(1,-1) translate(0,-527.714)" width="10" x="964.2857142857143" xlink:href="#GroundDisconnector:地刀_0" y="253.8571428571429" zvalue="9339"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454446940163" ObjectName="110kV母联11217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454446940163"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,969.286,263.857) scale(1,-1) translate(0,-527.714)" width="10" x="964.2857142857143" y="253.8571428571429"/></g>
  <g id="140">
   <use class="kv110" height="20" transform="rotate(0,883.286,263.857) scale(1,-1) translate(0,-527.714)" width="10" x="878.2857142857142" xlink:href="#GroundDisconnector:地刀_0" y="253.8571428571429" zvalue="9348"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454447071235" ObjectName="110kV母联11227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454447071235"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,883.286,263.857) scale(1,-1) translate(0,-527.714)" width="10" x="878.2857142857142" y="253.8571428571429"/></g>
  <g id="137">
   <use class="kv110" height="20" transform="rotate(0,1035.29,321.857) scale(1,-1) translate(0,-643.714)" width="10" x="1030.285714285714" xlink:href="#GroundDisconnector:地刀_0" y="311.8571428571429" zvalue="9356"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454447202307" ObjectName="110kVⅠ母11017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454447202307"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1035.29,321.857) scale(1,-1) translate(0,-643.714)" width="10" x="1030.285714285714" y="311.8571428571429"/></g>
  <g id="212">
   <use class="kv110" height="20" transform="rotate(0,1037.29,418.857) scale(1,1) translate(0,0)" width="10" x="1032.285714285714" xlink:href="#GroundDisconnector:地刀_0" y="408.8571428571429" zvalue="9360"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454447333379" ObjectName="110kVⅡ母11027接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454447333379"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1037.29,418.857) scale(1,1) translate(0,0)" width="10" x="1032.285714285714" y="408.8571428571429"/></g>
  <g id="255">
   <use class="kv110" height="20" transform="rotate(0,610.286,282.857) scale(1,-1) translate(0,-565.714)" width="10" x="605.2857142857142" xlink:href="#GroundDisconnector:地刀_0" y="272.8571428571429" zvalue="9374"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454447726595" ObjectName="110kV傣盏Ⅱ回线17217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454447726595"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,610.286,282.857) scale(1,-1) translate(0,-565.714)" width="10" x="605.2857142857142" y="272.8571428571429"/></g>
  <g id="247">
   <use class="kv110" height="20" transform="rotate(270,609.286,213.857) scale(1,1) translate(0,0)" width="10" x="604.2857142857142" xlink:href="#GroundDisconnector:地刀_0" y="203.8571428571429" zvalue="9375"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454447595523" ObjectName="110kV傣盏Ⅱ回线17260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454447595523"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,609.286,213.857) scale(1,1) translate(0,0)" width="10" x="604.2857142857142" y="203.8571428571429"/></g>
  <g id="246">
   <use class="kv110" height="20" transform="rotate(270,609.286,147.857) scale(1,1) translate(0,0)" width="10" x="604.2857142857142" xlink:href="#GroundDisconnector:地刀_0" y="137.8571428571429" zvalue="9378"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454447464451" ObjectName="110kV傣盏Ⅱ回线17267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454447464451"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,609.286,147.857) scale(1,1) translate(0,0)" width="10" x="604.2857142857142" y="137.8571428571429"/></g>
  <g id="292">
   <use class="kv110" height="20" transform="rotate(0,728.286,282.857) scale(1,-1) translate(0,-565.714)" width="10" x="723.2857142857142" xlink:href="#GroundDisconnector:地刀_0" y="272.8571428571429" zvalue="9400"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454448381955" ObjectName="110kV香柏河二级线17317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454448381955"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,728.286,282.857) scale(1,-1) translate(0,-565.714)" width="10" x="723.2857142857142" y="272.8571428571429"/></g>
  <g id="291">
   <use class="kv110" height="20" transform="rotate(270,727.286,213.857) scale(1,1) translate(0,0)" width="10" x="722.2857142857142" xlink:href="#GroundDisconnector:地刀_0" y="203.8571428571429" zvalue="9401"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454448250883" ObjectName="110kV香柏河二级线17360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454448250883"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,727.286,213.857) scale(1,1) translate(0,0)" width="10" x="722.2857142857142" y="203.8571428571429"/></g>
  <g id="290">
   <use class="kv110" height="20" transform="rotate(270,727.286,147.857) scale(1,1) translate(0,0)" width="10" x="722.2857142857142" xlink:href="#GroundDisconnector:地刀_0" y="137.8571428571429" zvalue="9403"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454448119811" ObjectName="110kV香柏河二级线17367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454448119811"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,727.286,147.857) scale(1,1) translate(0,0)" width="10" x="722.2857142857142" y="137.8571428571429"/></g>
  <g id="317">
   <use class="kv110" height="20" transform="rotate(0,834.286,282.857) scale(1,-1) translate(0,-565.714)" width="10" x="829.2857142857143" xlink:href="#GroundDisconnector:地刀_0" y="272.8571428571429" zvalue="9425"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454449037315" ObjectName="110kV支盏T线17417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454449037315"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,834.286,282.857) scale(1,-1) translate(0,-565.714)" width="10" x="829.2857142857143" y="272.8571428571429"/></g>
  <g id="316">
   <use class="kv110" height="20" transform="rotate(270,833.286,213.857) scale(1,1) translate(0,0)" width="10" x="828.2857142857143" xlink:href="#GroundDisconnector:地刀_0" y="203.8571428571429" zvalue="9426"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454448906243" ObjectName="110kV支盏T线17460接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454448906243"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,833.286,213.857) scale(1,1) translate(0,0)" width="10" x="828.2857142857143" y="203.8571428571429"/></g>
  <g id="315">
   <use class="kv110" height="20" transform="rotate(270,833.286,147.857) scale(1,1) translate(0,0)" width="10" x="828.2857142857143" xlink:href="#GroundDisconnector:地刀_0" y="137.8571428571429" zvalue="9428"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454448775171" ObjectName="110kV支盏T线17467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454448775171"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,833.286,147.857) scale(1,1) translate(0,0)" width="10" x="828.2857142857143" y="137.8571428571429"/></g>
  <g id="392">
   <use class="kv110" height="20" transform="rotate(0,1128.29,282.857) scale(1,-1) translate(0,-565.714)" width="10" x="1123.285714285714" xlink:href="#GroundDisconnector:地刀_0" y="272.8571428571429" zvalue="9626"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454449758211" ObjectName="110kV勐乃电站线17517接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454449758211"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1128.29,282.857) scale(1,-1) translate(0,-565.714)" width="10" x="1123.285714285714" y="272.8571428571429"/></g>
  <g id="391">
   <use class="kv110" height="20" transform="rotate(270,1127.29,213.857) scale(1,1) translate(0,0)" width="10" x="1122.285714285714" xlink:href="#GroundDisconnector:地刀_0" y="203.8571428571429" zvalue="9627"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454449627139" ObjectName="110kV勐乃电站线17560接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454449627139"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1127.29,213.857) scale(1,1) translate(0,0)" width="10" x="1122.285714285714" y="203.8571428571429"/></g>
  <g id="390">
   <use class="kv110" height="20" transform="rotate(270,1127.29,147.857) scale(1,1) translate(0,0)" width="10" x="1122.285714285714" xlink:href="#GroundDisconnector:地刀_0" y="137.8571428571429" zvalue="9629"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454449496067" ObjectName="110kV勐乃电站线17567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454449496067"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1127.29,147.857) scale(1,1) translate(0,0)" width="10" x="1122.285714285714" y="137.8571428571429"/></g>
  <g id="428">
   <use class="kv110" height="20" transform="rotate(0,1234.29,282.857) scale(1,-1) translate(0,-565.714)" width="10" x="1229.285714285714" xlink:href="#GroundDisconnector:地刀_0" y="272.8571428571429" zvalue="9651"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454450413571" ObjectName="110kV松坡电站线17717接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454450413571"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1234.29,282.857) scale(1,-1) translate(0,-565.714)" width="10" x="1229.285714285714" y="272.8571428571429"/></g>
  <g id="427">
   <use class="kv110" height="20" transform="rotate(270,1233.29,213.857) scale(1,1) translate(0,0)" width="10" x="1228.285714285714" xlink:href="#GroundDisconnector:地刀_0" y="203.8571428571429" zvalue="9652"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454450282499" ObjectName="110kV松坡电站线17760接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454450282499"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1233.29,213.857) scale(1,1) translate(0,0)" width="10" x="1228.285714285714" y="203.8571428571429"/></g>
  <g id="426">
   <use class="kv110" height="20" transform="rotate(270,1233.29,147.857) scale(1,1) translate(0,0)" width="10" x="1228.285714285714" xlink:href="#GroundDisconnector:地刀_0" y="137.8571428571429" zvalue="9654"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454450151427" ObjectName="110kV松坡电站线17767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454450151427"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1233.29,147.857) scale(1,1) translate(0,0)" width="10" x="1228.285714285714" y="137.8571428571429"/></g>
  <g id="469">
   <use class="kv110" height="20" transform="rotate(0,1352.29,282.857) scale(1,-1) translate(0,-565.714)" width="10" x="1347.285714285714" xlink:href="#GroundDisconnector:地刀_0" y="272.8571428571429" zvalue="9676"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454451068931" ObjectName="110kV狮子山电站线17817接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454451068931"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1352.29,282.857) scale(1,-1) translate(0,-565.714)" width="10" x="1347.285714285714" y="272.8571428571429"/></g>
  <g id="468">
   <use class="kv110" height="20" transform="rotate(270,1351.29,213.857) scale(1,1) translate(0,0)" width="10" x="1346.285714285714" xlink:href="#GroundDisconnector:地刀_0" y="203.8571428571429" zvalue="9677"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454450937859" ObjectName="110kV狮子山电站线17860接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454450937859"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1351.29,213.857) scale(1,1) translate(0,0)" width="10" x="1346.285714285714" y="203.8571428571429"/></g>
  <g id="467">
   <use class="kv110" height="20" transform="rotate(270,1351.29,147.857) scale(1,1) translate(0,0)" width="10" x="1346.285714285714" xlink:href="#GroundDisconnector:地刀_0" y="137.8571428571429" zvalue="9679"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454450806787" ObjectName="110kV狮子山电站线17867接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454450806787"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1351.29,147.857) scale(1,1) translate(0,0)" width="10" x="1346.285714285714" y="137.8571428571429"/></g>
  <g id="567">
   <use class="kv35" height="20" transform="rotate(90,468.271,749.556) scale(1.25,1.25) translate(-92.4042,-147.411)" width="10" x="462.0208333333335" xlink:href="#GroundDisconnector:地刀_0" y="737.0555555555553" zvalue="9801"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454451920899" ObjectName="35kV西那线37217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454451920899"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,468.271,749.556) scale(1.25,1.25) translate(-92.4042,-147.411)" width="10" x="462.0208333333335" y="737.0555555555553"/></g>
  <g id="569">
   <use class="kv35" height="20" transform="rotate(90,467.382,800.444) scale(1.25,1.25) translate(-92.2264,-157.589)" width="10" x="461.1319444444446" xlink:href="#GroundDisconnector:地刀_0" y="787.9444444444441" zvalue="9804"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454452051971" ObjectName="35kV西那线37260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454452051971"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,467.382,800.444) scale(1.25,1.25) translate(-92.2264,-157.589)" width="10" x="461.1319444444446" y="787.9444444444441"/></g>
  <g id="574">
   <use class="kv35" height="20" transform="rotate(90,468.271,854.889) scale(1.25,1.25) translate(-92.4042,-168.478)" width="10" x="462.0208333333335" xlink:href="#GroundDisconnector:地刀_0" y="842.3888888888887" zvalue="9807"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454452183043" ObjectName="35kV西那线37267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454452183043"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,468.271,854.889) scale(1.25,1.25) translate(-92.4042,-168.478)" width="10" x="462.0208333333335" y="842.3888888888887"/></g>
  <g id="603">
   <use class="kv35" height="20" transform="rotate(90,550.271,749.556) scale(1.25,1.25) translate(-108.804,-147.411)" width="10" x="544.0208333333335" xlink:href="#GroundDisconnector:地刀_0" y="737.0555555555553" zvalue="9836"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454452576259" ObjectName="35kV昆润采选线37317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454452576259"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,550.271,749.556) scale(1.25,1.25) translate(-108.804,-147.411)" width="10" x="544.0208333333335" y="737.0555555555553"/></g>
  <g id="601">
   <use class="kv35" height="20" transform="rotate(90,549.382,800.444) scale(1.25,1.25) translate(-108.626,-157.589)" width="10" x="543.1319444444446" xlink:href="#GroundDisconnector:地刀_0" y="787.9444444444441" zvalue="9839"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454452445187" ObjectName="35kV昆润采选线37360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454452445187"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,549.382,800.444) scale(1.25,1.25) translate(-108.626,-157.589)" width="10" x="543.1319444444446" y="787.9444444444441"/></g>
  <g id="599">
   <use class="kv35" height="20" transform="rotate(90,550.271,854.889) scale(1.25,1.25) translate(-108.804,-168.478)" width="10" x="544.0208333333335" xlink:href="#GroundDisconnector:地刀_0" y="842.3888888888887" zvalue="9842"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454452314115" ObjectName="35kV昆润采选线37367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454452314115"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,550.271,854.889) scale(1.25,1.25) translate(-108.804,-168.478)" width="10" x="544.0208333333335" y="842.3888888888887"/></g>
  <g id="628">
   <use class="kv35" height="20" transform="rotate(90,632.271,749.556) scale(1.25,1.25) translate(-125.204,-147.411)" width="10" x="626.0208333333335" xlink:href="#GroundDisconnector:地刀_0" y="737.0555555555553" zvalue="9862"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454453297155" ObjectName="35kV曼悠河一级线37417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454453297155"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,632.271,749.556) scale(1.25,1.25) translate(-125.204,-147.411)" width="10" x="626.0208333333335" y="737.0555555555553"/></g>
  <g id="626">
   <use class="kv35" height="20" transform="rotate(90,631.382,800.444) scale(1.25,1.25) translate(-125.026,-157.589)" width="10" x="625.1319444444446" xlink:href="#GroundDisconnector:地刀_0" y="787.9444444444441" zvalue="9865"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454453166083" ObjectName="35kV曼悠河一级线37460接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454453166083"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,631.382,800.444) scale(1.25,1.25) translate(-125.026,-157.589)" width="10" x="625.1319444444446" y="787.9444444444441"/></g>
  <g id="624">
   <use class="kv35" height="20" transform="rotate(90,632.271,854.889) scale(1.25,1.25) translate(-125.204,-168.478)" width="10" x="626.0208333333335" xlink:href="#GroundDisconnector:地刀_0" y="842.3888888888887" zvalue="9868"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454453035011" ObjectName="35kV曼悠河一级线37467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454453035011"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,632.271,854.889) scale(1.25,1.25) translate(-125.204,-168.478)" width="10" x="626.0208333333335" y="842.3888888888887"/></g>
  <g id="681">
   <use class="kv35" height="20" transform="rotate(270,861.714,772.714) scale(1,1) translate(0,0)" width="10" x="856.7142857142857" xlink:href="#GroundDisconnector:地刀_0" y="762.7142857142856" zvalue="9903"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454453690371" ObjectName="35kVⅠ段母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454453690371"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,861.714,772.714) scale(1,1) translate(0,0)" width="10" x="856.7142857142857" y="762.7142857142856"/></g>
  <g id="685">
   <use class="kv35" height="20" transform="rotate(270,864.286,725.571) scale(1,1) translate(0,0)" width="10" x="859.2857142857142" xlink:href="#GroundDisconnector:地刀_0" y="715.5714285714284" zvalue="9908"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454453886979" ObjectName="35kVⅠ段母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454453886979"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,864.286,725.571) scale(1,1) translate(0,0)" width="10" x="859.2857142857142" y="715.5714285714284"/></g>
  <g id="719">
   <use class="kv35" height="20" transform="rotate(270,781.414,764.127) scale(-1.25,1.25) translate(-1405.29,-150.325)" width="10" x="775.1636904761906" xlink:href="#GroundDisconnector:地刀_0" y="751.6269841269839" zvalue="9943"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454454411267" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454454411267"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,781.414,764.127) scale(-1.25,1.25) translate(-1405.29,-150.325)" width="10" x="775.1636904761906" y="751.6269841269839"/></g>
  <g id="721">
   <use class="kv35" height="20" transform="rotate(270,781.699,821.27) scale(-1.25,1.25) translate(-1405.81,-161.754)" width="10" x="775.4494047619049" xlink:href="#GroundDisconnector:地刀_0" y="808.769841269841" zvalue="9946"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454454542339" ObjectName="#1主变35kV侧30167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454454542339"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,781.699,821.27) scale(-1.25,1.25) translate(-1405.81,-161.754)" width="10" x="775.4494047619049" y="808.769841269841"/></g>
  <g id="54">
   <use class="kv35" height="20" transform="rotate(0,915.096,815.302) scale(1.25,1.25) translate(-181.769,-160.56)" width="10" x="908.8462301587304" xlink:href="#GroundDisconnector:地刀_0" y="802.801587301587" zvalue="9966"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454454935555" ObjectName="35kV分段31217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454454935555"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,915.096,815.302) scale(1.25,1.25) translate(-181.769,-160.56)" width="10" x="908.8462301587304" y="802.801587301587"/></g>
  <g id="70">
   <use class="kv35" height="20" transform="rotate(90,1163.41,824.127) scale(1.25,1.25) translate(-231.433,-162.325)" width="10" x="1157.163690476191" xlink:href="#GroundDisconnector:地刀_0" y="811.6269841269839" zvalue="9979"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454455197699" ObjectName="#2主变35kV侧30260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454455197699"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1163.41,824.127) scale(1.25,1.25) translate(-231.433,-162.325)" width="10" x="1157.163690476191" y="811.6269841269839"/></g>
  <g id="68">
   <use class="kv35" height="20" transform="rotate(90,1163.7,893.27) scale(1.25,1.25) translate(-231.49,-176.154)" width="10" x="1157.449404761905" xlink:href="#GroundDisconnector:地刀_0" y="880.769841269841" zvalue="9982"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454455066627" ObjectName="#2主变35kV侧30267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454455066627"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1163.7,893.27) scale(1.25,1.25) translate(-231.49,-176.154)" width="10" x="1157.449404761905" y="880.769841269841"/></g>
  <g id="149">
   <use class="kv10" height="20" transform="rotate(270,1678,111.429) scale(1.6,1.6) translate(-626.25,-35.7857)" width="10" x="1670" xlink:href="#GroundDisconnector:地刀_0" y="95.42857142857144" zvalue="10011"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454455656451" ObjectName="10kV盏西乡专线07167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454455656451"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1678,111.429) scale(1.6,1.6) translate(-626.25,-35.7857)" width="10" x="1670" y="95.42857142857144"/></g>
  <g id="178">
   <use class="kv10" height="20" transform="rotate(270,1680,211.429) scale(1.6,1.6) translate(-627,-73.2857)" width="10" x="1672" xlink:href="#GroundDisconnector:地刀_0" y="195.4285714285714" zvalue="10033"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454456049667" ObjectName="10kV支那乡专线07267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454456049667"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1680,211.429) scale(1.6,1.6) translate(-627,-73.2857)" width="10" x="1672" y="195.4285714285714"/></g>
  <g id="197">
   <use class="kv10" height="20" transform="rotate(270,1680,300.179) scale(1.6,1.6) translate(-627,-106.567)" width="10" x="1672" xlink:href="#GroundDisconnector:地刀_0" y="284.1785714285714" zvalue="10048"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454456377347" ObjectName="10kV东洪坝线07367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454456377347"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1680,300.179) scale(1.6,1.6) translate(-627,-106.567)" width="10" x="1672" y="284.1785714285714"/></g>
  <g id="129">
   <use class="kv10" height="20" transform="rotate(270,1682.75,651.179) scale(1.6,1.6) translate(-628.031,-238.192)" width="10" x="1674.75" xlink:href="#GroundDisconnector:地刀_0" y="635.1785714285714" zvalue="10078"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454457032707" ObjectName="10kV盏西街道线08267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454457032707"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1682.75,651.179) scale(1.6,1.6) translate(-628.031,-238.192)" width="10" x="1674.75" y="635.1785714285714"/></g>
  <g id="259">
   <use class="kv10" height="20" transform="rotate(270,1682.75,746.179) scale(1.6,1.6) translate(-628.031,-273.817)" width="10" x="1674.75" xlink:href="#GroundDisconnector:地刀_0" y="730.1785714285714" zvalue="10093"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454457294851" ObjectName="10kV2号接地变及消弧线圈08367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454457294851"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1682.75,746.179) scale(1.6,1.6) translate(-628.031,-273.817)" width="10" x="1674.75" y="730.1785714285714"/></g>
  <g id="514">
   <use class="kv110" height="20" transform="rotate(270,1308.14,541.584) scale(0.942857,0.942857) translate(78.9957,32.2519)" width="10" x="1303.428571428571" xlink:href="#GroundDisconnector:地刀_0" y="532.1558016854508" zvalue="10137"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454458146819" ObjectName="#2主变110kV侧10267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454458146819"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1308.14,541.584) scale(0.942857,0.942857) translate(78.9957,32.2519)" width="10" x="1303.428571428571" y="532.1558016854508"/></g>
  <g id="113">
   <use class="kv35" height="20" transform="rotate(270,498.842,654.127) scale(-1.25,1.25) translate(-896.666,-128.325)" width="10" x="492.592261904762" xlink:href="#GroundDisconnector:地刀_0" y="641.6269841269839" zvalue="10281"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454458736643" ObjectName="35kV1号站用变37117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454458736643"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,498.842,654.127) scale(-1.25,1.25) translate(-896.666,-128.325)" width="10" x="492.592261904762" y="641.6269841269839"/></g>
 </g>
 <g id="PowerTransformer3Class">
  <g id="388">
   <g id="3880">
    <use class="kv110" height="50" transform="rotate(0,720.554,590.469) scale(1.78626,1.77563) translate(-297.51,-238.538)" width="50" x="675.9" xlink:href="#PowerTransformer3:可调三卷变Y-D-Y_0" y="546.08" zvalue="7584"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874582392834" ObjectName="110"/>
    </metadata>
   </g>
   <g id="3881">
    <use class="kv35" height="50" transform="rotate(0,720.554,590.469) scale(1.78626,1.77563) translate(-297.51,-238.538)" width="50" x="675.9" xlink:href="#PowerTransformer3:可调三卷变Y-D-Y_1" y="546.08" zvalue="7584"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874582458370" ObjectName="35"/>
    </metadata>
   </g>
   <g id="3882">
    <use class="kv10" height="50" transform="rotate(0,720.554,590.469) scale(1.78626,1.77563) translate(-297.51,-238.538)" width="50" x="675.9" xlink:href="#PowerTransformer3:可调三卷变Y-D-Y_2" y="546.08" zvalue="7584"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874582523906" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399529332738" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399529332738"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,720.554,590.469) scale(1.78626,1.77563) translate(-297.51,-238.538)" width="50" x="675.9" y="546.08"/></g>
  <g id="112">
   <g id="1120">
    <use class="kv110" height="50" transform="rotate(0,1276.13,596.419) scale(1.78626,1.77563) translate(-542.056,-241.137)" width="50" x="1231.47" xlink:href="#PowerTransformer3:可调三卷变Y-D-Y_0" y="552.03" zvalue="10276"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874582589442" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1121">
    <use class="kv35" height="50" transform="rotate(0,1276.13,596.419) scale(1.78626,1.77563) translate(-542.056,-241.137)" width="50" x="1231.47" xlink:href="#PowerTransformer3:可调三卷变Y-D-Y_1" y="552.03" zvalue="10276"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874582654978" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1122">
    <use class="kv10" height="50" transform="rotate(0,1276.13,596.419) scale(1.78626,1.77563) translate(-542.056,-241.137)" width="50" x="1231.47" xlink:href="#PowerTransformer3:可调三卷变Y-D-Y_2" y="552.03" zvalue="10276"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874582720514" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399529398274" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399529398274"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1276.13,596.419) scale(1.78626,1.77563) translate(-542.056,-241.137)" width="50" x="1231.47" y="552.03"/></g>
 </g>
 <g id="BreakerClass">
  <g id="1">
   <use class="kv110" height="20" transform="rotate(0,460.397,248.857) scale(1.5,1.35) translate(-150.966,-61.0185)" width="10" x="452.8968787098202" xlink:href="#Breaker:开关_0" y="235.3571428571429" zvalue="8762"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925172068356" ObjectName="110kV傣盏Ⅰ回线171断路器"/>
   <cge:TPSR_Ref TObjectID="6473925172068356"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,460.397,248.857) scale(1.5,1.35) translate(-150.966,-61.0185)" width="10" x="452.8968787098202" y="235.3571428571429"/></g>
  <g id="231">
   <use class="kv110" height="20" transform="rotate(90,927.81,291.357) scale(1.9,1.40238) translate(-434.989,-79.5744)" width="10" x="918.3095238095239" xlink:href="#Breaker:母联开关_0" y="277.3333333333334" zvalue="8950"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925172133891" ObjectName="110kV母联112断路器"/>
   <cge:TPSR_Ref TObjectID="6473925172133891"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,927.81,291.357) scale(1.9,1.40238) translate(-434.989,-79.5744)" width="10" x="918.3095238095239" y="277.3333333333334"/></g>
  <g id="13">
   <use class="kv110" height="20" transform="rotate(0,702.786,477.357) scale(1.5,1.35) translate(-231.762,-120.259)" width="10" x="695.2857142857142" xlink:href="#Breaker:开关_0" y="463.8571428571428" zvalue="8983"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925172199427" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925172199427"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,702.786,477.357) scale(1.5,1.35) translate(-231.762,-120.259)" width="10" x="695.2857142857142" y="463.8571428571428"/></g>
  <g id="442">
   <use class="kv110" height="20" transform="rotate(0,1259.79,477.357) scale(1.5,1.35) translate(-417.429,-120.259)" width="10" x="1252.285714285714" xlink:href="#Breaker:开关_0" y="463.8571428571428" zvalue="9146"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925172264963" ObjectName="#2主变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473925172264963"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1259.79,477.357) scale(1.5,1.35) translate(-417.429,-120.259)" width="10" x="1252.285714285714" y="463.8571428571428"/></g>
  <g id="274">
   <use class="kv110" height="20" transform="rotate(0,562.397,248.857) scale(1.5,1.35) translate(-184.966,-61.0185)" width="10" x="554.8968787098202" xlink:href="#Breaker:开关_0" y="235.3571428571429" zvalue="9364"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925172330499" ObjectName="110kV傣盏Ⅱ回线172断路器"/>
   <cge:TPSR_Ref TObjectID="6473925172330499"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,562.397,248.857) scale(1.5,1.35) translate(-184.966,-61.0185)" width="10" x="554.8968787098202" y="235.3571428571429"/></g>
  <g id="297">
   <use class="kv110" height="20" transform="rotate(0,680.397,248.857) scale(1.5,1.35) translate(-224.299,-61.0185)" width="10" x="672.8968787098202" xlink:href="#Breaker:开关_0" y="235.3571428571429" zvalue="9391"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925172396035" ObjectName="110kV香柏河二级线173断路器"/>
   <cge:TPSR_Ref TObjectID="6473925172396035"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,680.397,248.857) scale(1.5,1.35) translate(-224.299,-61.0185)" width="10" x="672.8968787098202" y="235.3571428571429"/></g>
  <g id="322">
   <use class="kv110" height="20" transform="rotate(0,786.397,248.857) scale(1.5,1.35) translate(-259.632,-61.0185)" width="10" x="778.8968787098202" xlink:href="#Breaker:开关_0" y="235.3571428571429" zvalue="9416"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925172461571" ObjectName="110kV支盏T线174断路器"/>
   <cge:TPSR_Ref TObjectID="6473925172461571"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,786.397,248.857) scale(1.5,1.35) translate(-259.632,-61.0185)" width="10" x="778.8968787098202" y="235.3571428571429"/></g>
  <g id="398">
   <use class="kv110" height="20" transform="rotate(0,1080.4,248.857) scale(1.5,1.35) translate(-357.632,-61.0185)" width="10" x="1072.89687870982" xlink:href="#Breaker:开关_0" y="235.3571428571429" zvalue="9618"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925172527107" ObjectName="110kV勐乃电站线175断路器"/>
   <cge:TPSR_Ref TObjectID="6473925172527107"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1080.4,248.857) scale(1.5,1.35) translate(-357.632,-61.0185)" width="10" x="1072.89687870982" y="235.3571428571429"/></g>
  <g id="449">
   <use class="kv110" height="20" transform="rotate(0,1186.4,248.857) scale(1.5,1.35) translate(-392.966,-61.0185)" width="10" x="1178.89687870982" xlink:href="#Breaker:开关_0" y="235.3571428571429" zvalue="9641"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925172592643" ObjectName="110kV松坡电站线177断路器"/>
   <cge:TPSR_Ref TObjectID="6473925172592643"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1186.4,248.857) scale(1.5,1.35) translate(-392.966,-61.0185)" width="10" x="1178.89687870982" y="235.3571428571429"/></g>
  <g id="474">
   <use class="kv110" height="20" transform="rotate(0,1304.4,248.857) scale(1.5,1.35) translate(-432.299,-61.0185)" width="10" x="1296.89687870982" xlink:href="#Breaker:开关_0" y="235.3571428571429" zvalue="9666"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925172658179" ObjectName="110kV狮子山电站线178断路器"/>
   <cge:TPSR_Ref TObjectID="6473925172658179"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1304.4,248.857) scale(1.5,1.35) translate(-432.299,-61.0185)" width="10" x="1296.89687870982" y="235.3571428571429"/></g>
  <g id="540">
   <use class="kv35" height="20" transform="rotate(0,495.73,776.413) scale(1.5,1.35) translate(-162.743,-197.792)" width="10" x="488.2302120431535" xlink:href="#Breaker:开关_0" y="762.9126984126985" zvalue="9769"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925172723715" ObjectName="35kV西那线372断路器"/>
   <cge:TPSR_Ref TObjectID="6473925172723715"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,495.73,776.413) scale(1.5,1.35) translate(-162.743,-197.792)" width="10" x="488.2302120431535" y="762.9126984126985"/></g>
  <g id="613">
   <use class="kv35" height="20" transform="rotate(0,577.73,776.413) scale(1.5,1.35) translate(-190.077,-197.792)" width="10" x="570.2302120431534" xlink:href="#Breaker:开关_0" y="762.9126984126985" zvalue="9822"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925172789251" ObjectName="35kV昆润采选线373断路器"/>
   <cge:TPSR_Ref TObjectID="6473925172789251"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,577.73,776.413) scale(1.5,1.35) translate(-190.077,-197.792)" width="10" x="570.2302120431534" y="762.9126984126985"/></g>
  <g id="637">
   <use class="kv35" height="20" transform="rotate(0,659.73,776.413) scale(1.5,1.35) translate(-217.41,-197.792)" width="10" x="652.2302120431534" xlink:href="#Breaker:开关_0" y="762.9126984126985" zvalue="9849"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925172854787" ObjectName="35kV曼悠河一级线374断路器"/>
   <cge:TPSR_Ref TObjectID="6473925172854787"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,659.73,776.413) scale(1.5,1.35) translate(-217.41,-197.792)" width="10" x="652.2302120431534" y="762.9126984126985"/></g>
  <g id="705">
   <use class="kv35" height="20" transform="rotate(0,752.587,791.413) scale(1.5,1.35) translate(-248.362,-201.681)" width="10" x="745.0873549002964" xlink:href="#Breaker:开关_0" y="777.9127044677737" zvalue="9927"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925172920323" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925172920323"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,752.587,791.413) scale(1.5,1.35) translate(-248.362,-201.681)" width="10" x="745.0873549002964" y="777.9127044677737"/></g>
  <g id="36">
   <use class="kv35" height="20" transform="rotate(270,1032.64,781.857) scale(1.5,1.35) translate(-341.714,-199.204)" width="10" x="1025.142857142857" xlink:href="#Breaker:母联开关_0" y="768.3571428571428" zvalue="9950"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925172985859" ObjectName="35kV分段312断路器"/>
   <cge:TPSR_Ref TObjectID="6473925172985859"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1032.64,781.857) scale(1.5,1.35) translate(-341.714,-199.204)" width="10" x="1025.142857142857" y="768.3571428571428"/></g>
  <g id="80">
   <use class="kv35" height="20" transform="rotate(0,1202.59,791.413) scale(1.5,1.35) translate(-398.362,-201.681)" width="10" x="1195.087354900296" xlink:href="#Breaker:开关_0" y="777.9127044677737" zvalue="9970"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925173051395" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925173051395"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1202.59,791.413) scale(1.5,1.35) translate(-398.362,-201.681)" width="10" x="1195.087354900296" y="777.9127044677737"/></g>
  <g id="135">
   <use class="kv10" height="20" transform="rotate(270,1590.41,145.033) scale(2.3125,2.3125) translate(-896.103,-69.1909)" width="10" x="1578.848214285714" xlink:href="#Breaker:小车断路器_0" y="121.9076969588645" zvalue="10002"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925173116931" ObjectName="10kV盏西乡专线071断路器"/>
   <cge:TPSR_Ref TObjectID="6473925173116931"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1590.41,145.033) scale(2.3125,2.3125) translate(-896.103,-69.1909)" width="10" x="1578.848214285714" y="121.9076969588645"/></g>
  <g id="183">
   <use class="kv10" height="20" transform="rotate(270,1595,243.857) scale(2.3125,2.3125) translate(-898.708,-125.28)" width="10" x="1583.4375" xlink:href="#Breaker:小车断路器_0" y="220.7321428571429" zvalue="10027"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925173182467" ObjectName="10kV支那乡专线072断路器"/>
   <cge:TPSR_Ref TObjectID="6473925173182467"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1595,243.857) scale(2.3125,2.3125) translate(-898.708,-125.28)" width="10" x="1583.4375" y="220.7321428571429"/></g>
  <g id="202">
   <use class="kv10" height="20" transform="rotate(270,1594,333.607) scale(2.3125,2.3125) translate(-898.14,-176.22)" width="10" x="1582.4375" xlink:href="#Breaker:小车断路器_0" y="310.4821428571429" zvalue="10041"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925173248002" ObjectName="10kV东洪坝线073断路器"/>
   <cge:TPSR_Ref TObjectID="6473925173248002"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1594,333.607) scale(2.3125,2.3125) translate(-898.14,-176.22)" width="10" x="1582.4375" y="310.4821428571429"/></g>
  <g id="158">
   <use class="kv10" height="20" transform="rotate(270,1596.75,684.607) scale(2.3125,2.3125) translate(-899.701,-375.436)" width="10" x="1585.1875" xlink:href="#Breaker:小车断路器_0" y="661.4821428571429" zvalue="10071"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925173313538" ObjectName="10kV盏西街道线082断路器"/>
   <cge:TPSR_Ref TObjectID="6473925173313538"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1596.75,684.607) scale(2.3125,2.3125) translate(-899.701,-375.436)" width="10" x="1585.1875" y="661.4821428571429"/></g>
  <g id="477">
   <use class="kv10" height="20" transform="rotate(270,1596.75,779.607) scale(2.3125,2.3125) translate(-899.701,-429.355)" width="10" x="1585.1875" xlink:href="#Breaker:小车断路器_0" y="756.4821428571429" zvalue="10086"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925173379074" ObjectName="10kV2号接地变及消弧线圈083断路器"/>
   <cge:TPSR_Ref TObjectID="6473925173379074"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1596.75,779.607) scale(2.3125,2.3125) translate(-899.701,-429.355)" width="10" x="1585.1875" y="756.4821428571429"/></g>
  <g id="159">
   <use class="kv10" height="20" transform="rotate(270,1449.43,398.732) scale(2.3125,2.3125) translate(-816.086,-213.182)" width="10" x="1437.86608014788" xlink:href="#Breaker:小车断路器_0" y="375.6071428571429" zvalue="10118"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925173444610" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925173444610"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1449.43,398.732) scale(2.3125,2.3125) translate(-816.086,-213.182)" width="10" x="1437.86608014788" y="375.6071428571429"/></g>
  <g id="505">
   <use class="kv10" height="20" transform="rotate(270,1449.43,596.538) scale(2.3125,2.3125) translate(-816.086,-325.45)" width="10" x="1437.86608014788" xlink:href="#Breaker:小车断路器_0" y="573.412725952476" zvalue="10125"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925173510146" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473925173510146"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1449.43,596.538) scale(2.3125,2.3125) translate(-816.086,-325.45)" width="10" x="1437.86608014788" y="573.412725952476"/></g>
  <g id="519">
   <use class="kv10" height="20" transform="rotate(0,1621.71,584.571) scale(2.3125,2.3125) translate(-913.87,-318.659)" width="10" x="1610.151785714286" xlink:href="#Breaker:小车母联_0" y="561.4464285714283" zvalue="10142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925173575682" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473925173575682"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1621.71,584.571) scale(2.3125,2.3125) translate(-913.87,-318.659)" width="10" x="1610.151785714286" y="561.4464285714283"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="5">
   <use class="kv110" height="30" transform="rotate(0,460.286,187.857) scale(1,0.733333) translate(0,64.3117)" width="15" x="452.7857142857144" xlink:href="#Disconnector:刀闸_0" y="176.8571428571429" zvalue="8763"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454444711939" ObjectName="110kV傣盏Ⅰ回线1716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454444711939"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,460.286,187.857) scale(1,0.733333) translate(0,64.3117)" width="15" x="452.7857142857144" y="176.8571428571429"/></g>
  <g id="7">
   <use class="kv110" height="30" transform="rotate(0,508.286,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="500.7857142857142" xlink:href="#Disconnector:刀闸_0" y="308.8571428571429" zvalue="8765"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454444777475" ObjectName="110kV傣盏Ⅰ回线1711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454444777475"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,508.286,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="500.7857142857142" y="308.8571428571429"/></g>
  <g id="9">
   <use class="kv110" height="30" transform="rotate(0,460.286,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="452.7857142857144" xlink:href="#Disconnector:刀闸_0" y="308.8571428571429" zvalue="8767"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454444843011" ObjectName="110kV傣盏Ⅰ回线1712隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454444843011"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,460.286,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="452.7857142857144" y="308.8571428571429"/></g>
  <g id="233">
   <use class="kv110" height="30" transform="rotate(0,883.786,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="876.2857142857142" xlink:href="#Disconnector:刀闸_0" y="308.8571428571429" zvalue="8952"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454445367299" ObjectName="110kV母联1121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454445367299"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,883.786,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="876.2857142857142" y="308.8571428571429"/></g>
  <g id="234">
   <use class="kv110" height="30" transform="rotate(0,969.786,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="962.2857142857142" xlink:href="#Disconnector:刀闸_0" y="308.8571428571429" zvalue="8954"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454445432835" ObjectName="110kV母联1122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454445432835"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,969.786,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="962.2857142857142" y="308.8571428571429"/></g>
  <g id="250">
   <use class="kv110" height="30" transform="rotate(0,1436.79,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="1429.285714285714" xlink:href="#Disconnector:刀闸_0" y="308.8571428571429" zvalue="8967"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454445563907" ObjectName="110kVⅠ母电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454445563907"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1436.79,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="1429.285714285714" y="308.8571428571429"/></g>
  <g id="263">
   <use class="kv110" height="30" transform="rotate(0,499.786,412.857) scale(1,0.733333) translate(0,146.13)" width="15" x="492.2857142857142" xlink:href="#Disconnector:刀闸_0" y="401.8571428571429" zvalue="8976"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454445760515" ObjectName="110kVⅡ母电压互感器1902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454445760515"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,499.786,412.857) scale(1,0.733333) translate(0,146.13)" width="15" x="492.2857142857142" y="401.8571428571429"/></g>
  <g id="22">
   <use class="kv110" height="30" transform="rotate(0,702.786,422.857) scale(1,0.733333) translate(0,149.766)" width="15" x="695.2857142857142" xlink:href="#Disconnector:刀闸_0" y="411.8571428571429" zvalue="8986"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454445957123" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454445957123"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,702.786,422.857) scale(1,0.733333) translate(0,149.766)" width="15" x="695.2857142857142" y="411.8571428571429"/></g>
  <g id="28">
   <use class="kv110" height="30" transform="rotate(0,655.786,422.857) scale(1,0.733333) translate(0,149.766)" width="15" x="648.2857142857142" xlink:href="#Disconnector:刀闸_0" y="411.8571428571429" zvalue="8988"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454446022659" ObjectName="#1主变110kV侧1012隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454446022659"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,655.786,422.857) scale(1,0.733333) translate(0,149.766)" width="15" x="648.2857142857142" y="411.8571428571429"/></g>
  <g id="120">
   <use class="kv110" height="30" transform="rotate(0,702.786,529.857) scale(1,0.733333) translate(0,188.675)" width="15" x="695.2857142857142" xlink:href="#Disconnector:刀闸_0" y="518.8571428571429" zvalue="8994"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454446350339" ObjectName="#1主变110kV侧1016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454446350339"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,702.786,529.857) scale(1,0.733333) translate(0,188.675)" width="15" x="695.2857142857142" y="518.8571428571429"/></g>
  <g id="441">
   <use class="kv110" height="30" transform="rotate(0,1259.79,422.857) scale(1,0.733333) translate(0,149.766)" width="15" x="1252.285714285714" xlink:href="#Disconnector:刀闸_0" y="411.8571428571429" zvalue="9148"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454446809091" ObjectName="#2主变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454446809091"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1259.79,422.857) scale(1,0.733333) translate(0,149.766)" width="15" x="1252.285714285714" y="411.8571428571429"/></g>
  <g id="440">
   <use class="kv110" height="30" transform="rotate(0,1212.79,422.857) scale(1,0.733333) translate(0,149.766)" width="15" x="1205.285714285714" xlink:href="#Disconnector:刀闸_0" y="411.8571428571429" zvalue="9150"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454446743555" ObjectName="#2主变110kV侧1022隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454446743555"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1212.79,422.857) scale(1,0.733333) translate(0,149.766)" width="15" x="1205.285714285714" y="411.8571428571429"/></g>
  <g id="437">
   <use class="kv110" height="30" transform="rotate(0,1258.36,520.429) scale(1,0.733333) translate(0,185.247)" width="15" x="1250.857142857143" xlink:href="#Disconnector:刀闸_0" y="509.4285714285714" zvalue="9156"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454446415875" ObjectName="#2主变110kV侧1026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454446415875"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1258.36,520.429) scale(1,0.733333) translate(0,185.247)" width="15" x="1250.857142857143" y="509.4285714285714"/></g>
  <g id="273">
   <use class="kv110" height="30" transform="rotate(0,562.286,187.857) scale(1,0.733333) translate(0,64.3117)" width="15" x="554.7857142857142" xlink:href="#Disconnector:刀闸_0" y="176.8571428571429" zvalue="9365"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454447988739" ObjectName="110kV傣盏Ⅱ回线1726隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454447988739"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,562.286,187.857) scale(1,0.733333) translate(0,64.3117)" width="15" x="554.7857142857142" y="176.8571428571429"/></g>
  <g id="258">
   <use class="kv110" height="30" transform="rotate(0,610.286,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="602.7857142857142" xlink:href="#Disconnector:刀闸_0" y="308.8571428571429" zvalue="9368"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454447923203" ObjectName="110kV傣盏Ⅱ回线1721隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454447923203"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,610.286,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="602.7857142857142" y="308.8571428571429"/></g>
  <g id="257">
   <use class="kv110" height="30" transform="rotate(0,562.286,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="554.7857142857142" xlink:href="#Disconnector:刀闸_0" y="308.8571428571429" zvalue="9370"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454447857667" ObjectName="110kV傣盏Ⅱ回线1722隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454447857667"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,562.286,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="554.7857142857142" y="308.8571428571429"/></g>
  <g id="296">
   <use class="kv110" height="30" transform="rotate(0,680.286,187.857) scale(1,0.733333) translate(0,64.3117)" width="15" x="672.7857142857142" xlink:href="#Disconnector:刀闸_0" y="176.8571428571429" zvalue="9392"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454448644099" ObjectName="110kV香柏河二级线1736隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454448644099"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,680.286,187.857) scale(1,0.733333) translate(0,64.3117)" width="15" x="672.7857142857142" y="176.8571428571429"/></g>
  <g id="295">
   <use class="kv110" height="30" transform="rotate(0,728.286,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="720.7857142857142" xlink:href="#Disconnector:刀闸_0" y="308.8571428571429" zvalue="9395"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454448578563" ObjectName="110kV香柏河二级线1731隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454448578563"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,728.286,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="720.7857142857142" y="308.8571428571429"/></g>
  <g id="294">
   <use class="kv110" height="30" transform="rotate(0,680.286,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="672.7857142857142" xlink:href="#Disconnector:刀闸_0" y="308.8571428571429" zvalue="9396"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454448513027" ObjectName="110kV香柏河二级线1732隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454448513027"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,680.286,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="672.7857142857142" y="308.8571428571429"/></g>
  <g id="321">
   <use class="kv110" height="30" transform="rotate(0,786.286,187.857) scale(1,0.733333) translate(0,64.3117)" width="15" x="778.7857142857143" xlink:href="#Disconnector:刀闸_0" y="176.8571428571429" zvalue="9417"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454449299459" ObjectName="110kV支盏T线1746隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454449299459"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,786.286,187.857) scale(1,0.733333) translate(0,64.3117)" width="15" x="778.7857142857143" y="176.8571428571429"/></g>
  <g id="320">
   <use class="kv110" height="30" transform="rotate(0,834.286,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="826.7857142857143" xlink:href="#Disconnector:刀闸_0" y="308.8571428571429" zvalue="9420"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454449233923" ObjectName="110kV支盏T线1741隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454449233923"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,834.286,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="826.7857142857143" y="308.8571428571429"/></g>
  <g id="319">
   <use class="kv110" height="30" transform="rotate(0,786.286,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="778.7857142857143" xlink:href="#Disconnector:刀闸_0" y="308.8571428571429" zvalue="9421"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454449168387" ObjectName="110kV支盏T线1742隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454449168387"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,786.286,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="778.7857142857143" y="308.8571428571429"/></g>
  <g id="397">
   <use class="kv110" height="30" transform="rotate(0,1080.29,187.857) scale(1,0.733333) translate(0,64.3117)" width="15" x="1072.785714285714" xlink:href="#Disconnector:刀闸_0" y="176.8571428571429" zvalue="9619"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454450020355" ObjectName="110kV勐乃电站线1756隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454450020355"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1080.29,187.857) scale(1,0.733333) translate(0,64.3117)" width="15" x="1072.785714285714" y="176.8571428571429"/></g>
  <g id="396">
   <use class="kv110" height="30" transform="rotate(0,1128.27,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="1120.774549861608" xlink:href="#Disconnector:刀闸_0" y="308.8571428571429" zvalue="9622"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454449954819" ObjectName="110kV勐乃电站线1751隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454449954819"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1128.27,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="1120.774549861608" y="308.8571428571429"/></g>
  <g id="395">
   <use class="kv110" height="30" transform="rotate(0,1080.29,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="1072.785714285714" xlink:href="#Disconnector:刀闸_0" y="308.8571428571429" zvalue="9623"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454449889283" ObjectName="110kV勐乃电站线1752隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454449889283"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1080.29,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="1072.785714285714" y="308.8571428571429"/></g>
  <g id="448">
   <use class="kv110" height="30" transform="rotate(0,1186.29,187.857) scale(1,0.733333) translate(0,64.3117)" width="15" x="1178.785714285714" xlink:href="#Disconnector:刀闸_0" y="176.8571428571429" zvalue="9642"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454450675715" ObjectName="110kV松坡电站线1776隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454450675715"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1186.29,187.857) scale(1,0.733333) translate(0,64.3117)" width="15" x="1178.785714285714" y="176.8571428571429"/></g>
  <g id="447">
   <use class="kv110" height="30" transform="rotate(0,1234.27,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="1226.774549861608" xlink:href="#Disconnector:刀闸_0" y="308.8571428571429" zvalue="9645"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454450610179" ObjectName="110kV松坡电站线1771隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454450610179"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1234.27,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="1226.774549861608" y="308.8571428571429"/></g>
  <g id="443">
   <use class="kv110" height="30" transform="rotate(0,1186.29,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="1178.785714285714" xlink:href="#Disconnector:刀闸_0" y="308.8571428571429" zvalue="9646"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454450544643" ObjectName="110kV松坡电站线1772隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454450544643"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1186.29,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="1178.785714285714" y="308.8571428571429"/></g>
  <g id="473">
   <use class="kv110" height="30" transform="rotate(0,1304.29,187.857) scale(1,0.733333) translate(0,64.3117)" width="15" x="1296.785714285714" xlink:href="#Disconnector:刀闸_0" y="176.8571428571429" zvalue="9667"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454451331075" ObjectName="110kV狮子山电站线1786隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454451331075"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1304.29,187.857) scale(1,0.733333) translate(0,64.3117)" width="15" x="1296.785714285714" y="176.8571428571429"/></g>
  <g id="472">
   <use class="kv110" height="30" transform="rotate(0,1352.27,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="1344.774549861608" xlink:href="#Disconnector:刀闸_0" y="308.8571428571429" zvalue="9670"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454451265539" ObjectName="110kV狮子山电站线1781隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454451265539"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1352.27,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="1344.774549861608" y="308.8571428571429"/></g>
  <g id="471">
   <use class="kv110" height="30" transform="rotate(0,1304.29,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="1296.785714285714" xlink:href="#Disconnector:刀闸_0" y="308.8571428571429" zvalue="9671"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454451200003" ObjectName="110kV狮子山电站线1782隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454451200003"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1304.29,319.857) scale(1,-0.733333) translate(0,-760.026)" width="15" x="1296.785714285714" y="308.8571428571429"/></g>
  <g id="484">
   <use class="kv35" height="30" transform="rotate(0,470.381,675.016) scale(1.11111,0.814815) translate(-46.2048,150.635)" width="15" x="462.0476190476192" xlink:href="#Disconnector:刀闸_0" y="662.7936507936508" zvalue="9698"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454451396611" ObjectName="35kV1号站用变3711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454451396611"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,470.381,675.016) scale(1.11111,0.814815) translate(-46.2048,150.635)" width="15" x="462.0476190476192" y="662.7936507936508"/></g>
  <g id="537">
   <use class="kv35" height="30" transform="rotate(0,497.492,729.016) scale(1.11111,0.814815) translate(-48.9159,162.908)" width="15" x="489.1587301587301" xlink:href="#Disconnector:刀闸_0" y="716.7936507936508" zvalue="9765"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454451527683" ObjectName="35kV西那线3721隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454451527683"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,497.492,729.016) scale(1.11111,0.814815) translate(-48.9159,162.908)" width="15" x="489.1587301587301" y="716.7936507936508"/></g>
  <g id="542">
   <use class="kv35" height="30" transform="rotate(0,495.733,827.46) scale(1.11111,0.814815) translate(-48.7399,185.281)" width="15" x="487.3993486533082" xlink:href="#Disconnector:刀闸_0" y="815.2380952380953" zvalue="9772"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454451593219" ObjectName="35kV西那线3726隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454451593219"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,495.733,827.46) scale(1.11111,0.814815) translate(-48.7399,185.281)" width="15" x="487.3993486533082" y="815.2380952380953"/></g>
  <g id="546">
   <use class="kv35" height="30" transform="rotate(0,469.21,892.222) scale(1.11111,0.814815) translate(-46.0876,200)" width="15" x="460.876483973216" xlink:href="#Disconnector:刀闸_0" y="879.9994998462314" zvalue="9777"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454451658755" ObjectName="35kV西那线3729隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454451658755"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,469.21,892.222) scale(1.11111,0.814815) translate(-46.0876,200)" width="15" x="460.876483973216" y="879.9994998462314"/></g>
  <g id="615">
   <use class="kv35" height="30" transform="rotate(0,579.492,729.016) scale(1.11111,0.814815) translate(-57.1159,162.908)" width="15" x="571.1587301587301" xlink:href="#Disconnector:刀闸_0" y="716.7936507936508" zvalue="9819"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454452903939" ObjectName="35kV昆润采选线3731隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454452903939"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,579.492,729.016) scale(1.11111,0.814815) translate(-57.1159,162.908)" width="15" x="571.1587301587301" y="716.7936507936508"/></g>
  <g id="611">
   <use class="kv35" height="30" transform="rotate(0,577.733,827.46) scale(1.11111,0.814815) translate(-56.9399,185.281)" width="15" x="569.3993486533082" xlink:href="#Disconnector:刀闸_0" y="815.2380952380953" zvalue="9825"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454452838403" ObjectName="35kV昆润采选线3736隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454452838403"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,577.733,827.46) scale(1.11111,0.814815) translate(-56.9399,185.281)" width="15" x="569.3993486533082" y="815.2380952380953"/></g>
  <g id="607">
   <use class="kv35" height="30" transform="rotate(0,551.21,892.222) scale(1.11111,0.814815) translate(-54.2876,200)" width="15" x="542.876483973216" xlink:href="#Disconnector:刀闸_0" y="879.9994998462314" zvalue="9831"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454452641795" ObjectName="35kV昆润采选线3739隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454452641795"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,551.21,892.222) scale(1.11111,0.814815) translate(-54.2876,200)" width="15" x="542.876483973216" y="879.9994998462314"/></g>
  <g id="639">
   <use class="kv35" height="30" transform="rotate(0,661.492,729.016) scale(1.11111,0.814815) translate(-65.3159,162.908)" width="15" x="653.1587301587301" xlink:href="#Disconnector:刀闸_0" y="716.7936507936508" zvalue="9846"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454453493763" ObjectName="35kV曼悠河一级线3741隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454453493763"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,661.492,729.016) scale(1.11111,0.814815) translate(-65.3159,162.908)" width="15" x="653.1587301587301" y="716.7936507936508"/></g>
  <g id="635">
   <use class="kv35" height="30" transform="rotate(0,659.733,827.46) scale(1.11111,0.814815) translate(-65.1399,185.281)" width="15" x="651.3993486533082" xlink:href="#Disconnector:刀闸_0" y="815.2380952380953" zvalue="9852"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454453428227" ObjectName="35kV曼悠河一级线3746隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454453428227"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,659.733,827.46) scale(1.11111,0.814815) translate(-65.1399,185.281)" width="15" x="651.3993486533082" y="815.2380952380953"/></g>
  <g id="683">
   <use class="kv35" height="30" transform="rotate(0,829.214,743.714) scale(1,0.733333) translate(0,266.442)" width="15" x="821.7142857142857" xlink:href="#Disconnector:刀闸_0" y="732.7142857142857" zvalue="9900"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454453755907" ObjectName="35kVⅠ段母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454453755907"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,829.214,743.714) scale(1,0.733333) translate(0,266.442)" width="15" x="821.7142857142857" y="732.7142857142857"/></g>
  <g id="704">
   <use class="kv35" height="30" transform="rotate(0,1354.29,745.143) scale(1,0.733333) translate(0,266.961)" width="15" x="1346.785714285714" xlink:href="#Disconnector:刀闸_0" y="734.1428571428571" zvalue="9916"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454454083587" ObjectName="35kVⅡ段母线电压互感器3902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454454083587"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1354.29,745.143) scale(1,0.733333) translate(0,266.961)" width="15" x="1346.785714285714" y="734.1428571428571"/></g>
  <g id="712">
   <use class="kv35" height="30" transform="rotate(0,632.46,893.472) scale(1.11111,0.814815) translate(-62.4126,200.284)" width="15" x="624.126483973216" xlink:href="#Disconnector:刀闸_0" y="881.2494998462314" zvalue="9933"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454454149123" ObjectName="35kV曼悠河一级线3749隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454454149123"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,632.46,893.472) scale(1.11111,0.814815) translate(-62.4126,200.284)" width="15" x="624.126483973216" y="881.2494998462314"/></g>
  <g id="713">
   <use class="kv35" height="30" transform="rotate(0,752.59,851.175) scale(1.11111,0.814815) translate(-74.4256,190.671)" width="15" x="744.256491510451" xlink:href="#Disconnector:刀闸_0" y="838.9523809523808" zvalue="9935"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454454214659" ObjectName="#1主变35kV侧3016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454454214659"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,752.59,851.175) scale(1.11111,0.814815) translate(-74.4256,190.671)" width="15" x="744.256491510451" y="838.9523809523808"/></g>
  <g id="714">
   <use class="kv35" height="30" transform="rotate(0,752.469,744.603) scale(1.11111,0.814815) translate(-74.4136,166.45)" width="15" x="744.1360610957344" xlink:href="#Disconnector:刀闸_0" y="732.3809523809524" zvalue="9937"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454454280195" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454454280195"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,752.469,744.603) scale(1.11111,0.814815) translate(-74.4136,166.45)" width="15" x="744.1360610957344" y="732.3809523809524"/></g>
  <g id="38">
   <use class="kv35" height="30" transform="rotate(0,907.786,748.571) scale(1,0.733333) translate(0,268.208)" width="15" x="900.2857142857142" xlink:href="#Disconnector:刀闸_0" y="737.5714285714284" zvalue="9952"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454454607875" ObjectName="35kV分段3121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454454607875"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,907.786,748.571) scale(1,0.733333) translate(0,268.208)" width="15" x="900.2857142857142" y="737.5714285714284"/></g>
  <g id="39">
   <use class="kv35" height="30" transform="rotate(0,1097.21,743.714) scale(1,0.733333) translate(0,266.442)" width="15" x="1089.714285714286" xlink:href="#Disconnector:刀闸_0" y="732.7142857142857" zvalue="9954"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454454673411" ObjectName="35kV分段3122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454454673411"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1097.21,743.714) scale(1,0.733333) translate(0,266.442)" width="15" x="1089.714285714286" y="732.7142857142857"/></g>
  <g id="40">
   <use class="kv35" height="30" transform="rotate(270,974.929,782.286) scale(1,0.733333) translate(0,280.468)" width="15" x="967.4285714285714" xlink:href="#Disconnector:刀闸_0" y="771.2857142857143" zvalue="9956"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454454738947" ObjectName="35kV分段3126隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454454738947"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,974.929,782.286) scale(1,0.733333) translate(0,280.468)" width="15" x="967.4285714285714" y="771.2857142857143"/></g>
  <g id="79">
   <use class="kv35" height="30" transform="rotate(0,1202.59,851.175) scale(1.11111,0.814815) translate(-119.426,190.671)" width="15" x="1194.256491510451" xlink:href="#Disconnector:刀闸_0" y="838.9523809523808" zvalue="9972"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454455328771" ObjectName="#2主变35kV侧3026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454455328771"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1202.59,851.175) scale(1.11111,0.814815) translate(-119.426,190.671)" width="15" x="1194.256491510451" y="838.9523809523808"/></g>
  <g id="78">
   <use class="kv35" height="30" transform="rotate(0,1202.47,744.603) scale(1.11111,0.814815) translate(-119.414,166.45)" width="15" x="1194.136061095734" xlink:href="#Disconnector:刀闸_0" y="732.3809523809524" zvalue="9974"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454455263235" ObjectName="#2主变35kV侧3022隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454455263235"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1202.47,744.603) scale(1.11111,0.814815) translate(-119.414,166.45)" width="15" x="1194.136061095734" y="732.3809523809524"/></g>
  <g id="142">
   <use class="kv10" height="30" transform="rotate(90,1676.07,144.945) scale(1,-0.733333) translate(0,-346.597)" width="15" x="1668.568155842973" xlink:href="#Disconnector:刀闸_0" y="133.9449199080037" zvalue="10005"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454455525379" ObjectName="10kV盏西乡专线0716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454455525379"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1676.07,144.945) scale(1,-0.733333) translate(0,-346.597)" width="15" x="1668.568155842973" y="133.9449199080037"/></g>
  <g id="181">
   <use class="kv10" height="30" transform="rotate(90,1678.07,244.769) scale(1,-0.733333) translate(0,-582.546)" width="15" x="1670.568155842973" xlink:href="#Disconnector:刀闸_0" y="233.7693658062822" zvalue="10030"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454456115203" ObjectName="10kV支那乡专线0726隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454456115203"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1678.07,244.769) scale(1,-0.733333) translate(0,-582.546)" width="15" x="1670.568155842973" y="233.7693658062822"/></g>
  <g id="200">
   <use class="kv10" height="30" transform="rotate(90,1678.07,333.519) scale(1,-0.733333) translate(0,-792.319)" width="15" x="1670.568155842973" xlink:href="#Disconnector:刀闸_0" y="322.5193658062822" zvalue="10044"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454456442883" ObjectName="10kV东洪坝线0736隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454456442883"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1678.07,333.519) scale(1,-0.733333) translate(0,-792.319)" width="15" x="1670.568155842973" y="322.5193658062822"/></g>
  <g id="206">
   <use class="kv10" height="26" transform="rotate(90,1625.78,390.222) scale(1.11111,-1.11111) translate(-161.911,-739.978)" width="12" x="1619.111111111111" xlink:href="#Disconnector:单手车刀闸1212_0" y="375.7777777777777" zvalue="10060"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454456639491" ObjectName="10kVⅠ段母线电压互感器0801隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454456639491"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1625.78,390.222) scale(1.11111,-1.11111) translate(-161.911,-739.978)" width="12" x="1619.111111111111" y="375.7777777777777"/></g>
  <g id="207">
   <use class="kv10" height="26" transform="rotate(90,1624.89,423.146) scale(1.11111,-1.11111) translate(-161.822,-802.533)" width="12" x="1618.222222222222" xlink:href="#Disconnector:单手车刀闸1212_0" y="408.7014893583928" zvalue="10062"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454456705027" ObjectName="10kVⅠ段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454456705027"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1624.89,423.146) scale(1.11111,-1.11111) translate(-161.822,-802.533)" width="12" x="1618.222222222222" y="408.7014893583928"/></g>
  <g id="146">
   <use class="kv10" height="30" transform="rotate(90,1680.82,684.519) scale(1,-0.733333) translate(0,-1621.95)" width="15" x="1673.318155842973" xlink:href="#Disconnector:刀闸_0" y="673.5193658062822" zvalue="10074"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454457098243" ObjectName="10kV盏西街道线0826隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454457098243"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1680.82,684.519) scale(1,-0.733333) translate(0,-1621.95)" width="15" x="1673.318155842973" y="673.5193658062822"/></g>
  <g id="371">
   <use class="kv10" height="30" transform="rotate(90,1732.82,779.519) scale(1,-0.733333) translate(0,-1846.5)" width="15" x="1725.318155842973" xlink:href="#Disconnector:刀闸_0" y="768.5193658062822" zvalue="10089"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454457360387" ObjectName="10kV2号接地变及消弧线圈0020隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454457360387"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1732.82,779.519) scale(1,-0.733333) translate(0,-1846.5)" width="15" x="1725.318155842973" y="768.5193658062822"/></g>
  <g id="495">
   <use class="kv10" height="26" transform="rotate(90,1585.89,915.146) scale(1.11111,-1.11111) translate(-157.922,-1737.33)" width="12" x="1579.222222222222" xlink:href="#Disconnector:单手车刀闸1212_0" y="900.7014893583928" zvalue="10111"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454457688067" ObjectName="10kVⅡ段母线电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454457688067"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1585.89,915.146) scale(1.11111,-1.11111) translate(-157.922,-1737.33)" width="12" x="1579.222222222222" y="900.7014893583928"/></g>
  <g id="499">
   <use class="kv10" height="33" transform="rotate(90,1585,864) scale(1,1) translate(0,0)" width="14" x="1578" xlink:href="#Disconnector:手车隔离开关13_0" y="847.5" zvalue="10114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454457753603" ObjectName="10kV2号站用变0812隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454457753603"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(90,1585,864) scale(1,1) translate(0,0)" width="14" x="1578" y="847.5"/></g>
  <g id="523">
   <use class="kv10" height="26" transform="rotate(270,1577.21,480.121) scale(1.46684,1.57967) translate(-498.698,-168.648)" width="14" x="1566.946428571428" xlink:href="#Disconnector:联体手车刀闸_0" y="459.585164835165" zvalue="10145"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454458212355" ObjectName="10kV分段0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454458212355"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1577.21,480.121) scale(1.46684,1.57967) translate(-498.698,-168.648)" width="14" x="1566.946428571428" y="459.585164835165"/></g>
  <g id="534">
   <use class="kv35" height="30" transform="rotate(90,1370.82,668.805) scale(1,-0.733333) translate(0,-1584.81)" width="15" x="1363.318155842973" xlink:href="#Disconnector:刀闸_0" y="657.8050800919965" zvalue="10158"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454458474499" ObjectName="#2主变35kV侧3020隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454458474499"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1370.82,668.805) scale(1,-0.733333) translate(0,-1584.81)" width="15" x="1363.318155842973" y="657.8050800919965"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="11">
   <use class="kv110" height="25" transform="rotate(0,469.117,112.998) scale(1.59511,1.56571) translate(-166.688,-33.7562)" width="28" x="446.7857142857142" xlink:href="#ACLineSegment:带避雷器线路PT的线路_0" y="93.42617797851562" zvalue="8768"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249326878722" ObjectName="110kV傣盏Ⅰ回线"/>
   <cge:TPSR_Ref TObjectID="8444249326878722_5066549682307073"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,469.117,112.998) scale(1.59511,1.56571) translate(-166.688,-33.7562)" width="28" x="446.7857142857142" y="93.42617797851562"/></g>
  <g id="256">
   <use class="kv110" height="25" transform="rotate(0,572.12,112.998) scale(1.59511,1.56571) translate(-205.117,-33.7562)" width="28" x="549.7885882793967" xlink:href="#ACLineSegment:带避雷器线路PT的线路_0" y="93.4261779785154" zvalue="9371"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249326944258" ObjectName="110kV傣盏Ⅱ回线"/>
   <cge:TPSR_Ref TObjectID="8444249326944258_5066549682307073"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,572.12,112.998) scale(1.59511,1.56571) translate(-205.117,-33.7562)" width="28" x="549.7885882793967" y="93.4261779785154"/></g>
  <g id="429">
   <use class="kv110" height="40" transform="rotate(0,1190.57,112.998) scale(1.27609,0.978571) translate(-252.754,2.04583)" width="35" x="1168.242438659094" xlink:href="#ACLineSegment:220kV线路_0" y="93.42617797851528" zvalue="9648"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249310953478" ObjectName="110kV松坡电站线"/>
   <cge:TPSR_Ref TObjectID="8444249310953478_5066549682307073"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1190.57,112.998) scale(1.27609,0.978571) translate(-252.754,2.04583)" width="35" x="1168.242438659094" y="93.42617797851528"/></g>
  <g id="548">
   <use class="kv35" height="40" transform="rotate(180,495.676,933.891) scale(1,1) translate(0,0)" width="30" x="480.6756424578703" xlink:href="#ACLineSegment:线路带壁雷器_0" y="913.8908730158728" zvalue="9775"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249327009796" ObjectName="35kV西那线"/>
   <cge:TPSR_Ref TObjectID="8444249327009796_5066549682307073"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,495.676,933.891) scale(1,1) translate(0,0)" width="30" x="480.6756424578703" y="913.8908730158728"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="23">
   <path class="kv110" d="M 508.37 330.49 L 508.37 352.95" stroke-width="1" zvalue="8774"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="7@0" LinkObjectIDznd="48@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 508.37 330.49 L 508.37 352.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv110" d="M 460.37 330.49 L 460.37 379.71" stroke-width="1" zvalue="8775"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="49@11" MaxPinNum="2"/>
   </metadata>
  <path d="M 460.37 330.49 L 460.37 379.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv110" d="M 460.35 309.05 L 460.35 261.75" stroke-width="1" zvalue="8776"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@1" LinkObjectIDznd="1@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 460.35 309.05 L 460.35 261.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv110" d="M 460.35 235.94 L 460.35 198.67" stroke-width="1" zvalue="8777"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="5@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 460.35 235.94 L 460.35 198.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv110" d="M 460.37 177.22 L 460.37 130.19" stroke-width="1" zvalue="8778"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@0" LinkObjectIDznd="11@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 460.37 177.22 L 460.37 130.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv110" d="M 508.35 309.05 L 508.34 292.61" stroke-width="1" zvalue="8781"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="7@1" LinkObjectIDznd="16@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 508.35 309.05 L 508.34 292.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv110" d="M 460.35 296.86 L 508.34 296.86" stroke-width="1" zvalue="8782"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="25" LinkObjectIDznd="30" MaxPinNum="2"/>
   </metadata>
  <path d="M 460.35 296.86 L 508.34 296.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv110" d="M 497.54 147.81 L 460.37 147.81" stroke-width="1" zvalue="8784"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@0" LinkObjectIDznd="27" MaxPinNum="2"/>
   </metadata>
  <path d="M 497.54 147.81 L 460.37 147.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv110" d="M 497.54 213.81 L 460.35 213.81" stroke-width="1" zvalue="8785"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@0" LinkObjectIDznd="26" MaxPinNum="2"/>
   </metadata>
  <path d="M 497.54 213.81 L 460.35 213.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="237">
   <path class="kv110" d="M 883.85 309.05 L 883.85 291.54 L 913.93 291.54" stroke-width="1" zvalue="8955"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@1" LinkObjectIDznd="231@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 883.85 309.05 L 883.85 291.54 L 913.93 291.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="238">
   <path class="kv110" d="M 941.58 291.46 L 969.85 291.46 L 969.85 309.05" stroke-width="1" zvalue="8956"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@1" LinkObjectIDznd="234@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 941.58 291.46 L 969.85 291.46 L 969.85 309.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="kv110" d="M 883.87 330.49 L 883.87 352.95" stroke-width="1" zvalue="8957"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@0" LinkObjectIDznd="48@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 883.87 330.49 L 883.87 352.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="241">
   <path class="kv110" d="M 969.87 330.49 L 969.87 379.71" stroke-width="1" zvalue="8958"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@0" LinkObjectIDznd="49@10" MaxPinNum="2"/>
   </metadata>
  <path d="M 969.87 330.49 L 969.87 379.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="254">
   <path class="kv110" d="M 1436.87 330.49 L 1436.87 352.95" stroke-width="1" zvalue="8970"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@0" LinkObjectIDznd="48@11" MaxPinNum="2"/>
   </metadata>
  <path d="M 1436.87 330.49 L 1436.87 352.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="260">
   <path class="kv110" d="M 1459.54 262.81 L 1436.91 262.81" stroke-width="1" zvalue="8972"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252@0" LinkObjectIDznd="373" MaxPinNum="2"/>
   </metadata>
  <path d="M 1459.54 262.81 L 1436.91 262.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="265">
   <path class="kv110" d="M 499.87 402.22 L 499.87 379.71" stroke-width="1" zvalue="8977"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@0" LinkObjectIDznd="49@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 499.87 402.22 L 499.87 379.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv110" d="M 522.54 441.81 L 499.85 441.81" stroke-width="1" zvalue="8981"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@0" LinkObjectIDznd="374" MaxPinNum="2"/>
   </metadata>
  <path d="M 522.54 441.81 L 499.85 441.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="kv110" d="M 702.87 412.22 L 702.87 352.95" stroke-width="1" zvalue="8995"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@0" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 702.87 412.22 L 702.87 352.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv110" d="M 655.87 412.22 L 655.87 379.71" stroke-width="1" zvalue="8996"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="49@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 655.87 412.22 L 655.87 379.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="kv110" d="M 702.85 433.67 L 702.85 464.44" stroke-width="1" zvalue="8997"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@1" LinkObjectIDznd="13@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 702.85 433.67 L 702.85 464.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="219">
   <path class="kv110" d="M 702.89 490.25 L 702.87 519.22" stroke-width="1" zvalue="8998"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="13@1" LinkObjectIDznd="120@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 702.89 490.25 L 702.87 519.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="220">
   <path class="kv110" d="M 702.85 540.67 L 702.85 546.31" stroke-width="1" zvalue="8999"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@1" LinkObjectIDznd="388@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 702.85 540.67 L 702.85 546.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv110" d="M 655.85 433.67 L 655.85 442.86 L 702.85 442.86" stroke-width="1" zvalue="9000"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@1" LinkObjectIDznd="218" MaxPinNum="2"/>
   </metadata>
  <path d="M 655.85 433.67 L 655.85 442.86 L 702.85 442.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="271">
   <path class="kv110" d="M 740.54 442.86 L 702.85 442.86" stroke-width="1" zvalue="9001"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="218" MaxPinNum="2"/>
   </metadata>
  <path d="M 740.54 442.86 L 702.85 442.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="272">
   <path class="kv110" d="M 740.54 499.97 L 702.88 499.97" stroke-width="1" zvalue="9002"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="219" MaxPinNum="2"/>
   </metadata>
  <path d="M 740.54 499.97 L 702.88 499.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="400">
   <path class="kv110" d="M 702.94 566.78 L 607.48 566.78" stroke-width="1" zvalue="9144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="388@4" LinkObjectIDznd="14@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 702.94 566.78 L 607.48 566.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="436">
   <path class="kv110" d="M 1259.85 433.67 L 1259.85 464.44" stroke-width="1" zvalue="9158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="441@1" LinkObjectIDznd="442@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1259.85 433.67 L 1259.85 464.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="435">
   <path class="kv110" d="M 1259.89 490.25 L 1259.89 509.79" stroke-width="1" zvalue="9159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="442@1" LinkObjectIDznd="437@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1259.89 490.25 L 1259.89 509.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="434">
   <path class="kv110" d="M 1212.85 433.67 L 1212.85 442.86 L 1259.85 442.86" stroke-width="1" zvalue="9160"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="440@1" LinkObjectIDznd="436" MaxPinNum="2"/>
   </metadata>
  <path d="M 1212.85 433.67 L 1212.85 442.86 L 1259.85 442.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="433">
   <path class="kv110" d="M 1297.54 442.88 L 1259.85 442.86" stroke-width="1" zvalue="9161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="439@0" LinkObjectIDznd="434" MaxPinNum="2"/>
   </metadata>
  <path d="M 1297.54 442.88 L 1259.85 442.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="432">
   <path class="kv110" d="M 1297.54 499.81 L 1259.89 499.81" stroke-width="1" zvalue="9162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="438@0" LinkObjectIDznd="435" MaxPinNum="2"/>
   </metadata>
  <path d="M 1297.54 499.81 L 1259.89 499.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="444">
   <path class="kv110" d="M 1259.87 412.22 L 1259.87 352.95" stroke-width="1" zvalue="9164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="441@0" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1259.87 412.22 L 1259.87 352.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="445">
   <path class="kv110" d="M 1212.87 412.22 L 1212.87 379.71" stroke-width="1" zvalue="9165"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="440@0" LinkObjectIDznd="49@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1212.87 412.22 L 1212.87 379.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="446">
   <path class="kv110" d="M 1258.42 552.26 L 1258.42 531.24" stroke-width="1" zvalue="9166"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="437@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1258.42 552.26 L 1258.42 531.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="455">
   <path class="kv110" d="M 1258.51 572.73 L 1171.48 572.73 L 1171.48 555.78" stroke-width="1" zvalue="9167"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@4" LinkObjectIDznd="412@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1258.51 572.73 L 1171.48 572.73 L 1171.48 555.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv110" d="M 969.85 292.05 L 969.85 273.61" stroke-width="1" zvalue="9342"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="238" LinkObjectIDznd="86@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 969.85 292.05 L 969.85 273.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv110" d="M 883.85 292.05 L 883.85 273.61" stroke-width="1" zvalue="9350"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237" LinkObjectIDznd="140@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 883.85 292.05 L 883.85 273.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv110" d="M 1035.34 352.95 L 1035.34 331.61" stroke-width="1" zvalue="9358"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@4" LinkObjectIDznd="137@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1035.34 352.95 L 1035.34 331.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv110" d="M 1037.34 409.11 L 1037.34 379.71" stroke-width="1" zvalue="9362"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="49@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.34 409.11 L 1037.34 379.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="245">
   <path class="kv110" d="M 610.37 330.49 L 610.37 352.95" stroke-width="1" zvalue="9380"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="258@0" LinkObjectIDznd="48@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 610.37 330.49 L 610.37 352.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv110" d="M 562.37 330.49 L 562.37 379.71" stroke-width="1" zvalue="9381"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="257@0" LinkObjectIDznd="49@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 562.37 330.49 L 562.37 379.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="243">
   <path class="kv110" d="M 562.35 309.05 L 562.35 261.75" stroke-width="1" zvalue="9382"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="257@1" LinkObjectIDznd="274@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 562.35 309.05 L 562.35 261.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="kv110" d="M 562.35 235.94 L 562.35 198.67" stroke-width="1" zvalue="9383"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="273@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 562.35 235.94 L 562.35 198.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv110" d="M 562.37 177.22 L 562.37 130.19" stroke-width="1" zvalue="9384"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@0" LinkObjectIDznd="256@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 562.37 177.22 L 562.37 130.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv110" d="M 610.35 309.05 L 610.34 292.61" stroke-width="1" zvalue="9385"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="258@1" LinkObjectIDznd="255@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 610.35 309.05 L 610.34 292.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="kv110" d="M 562.35 296.86 L 610.34 296.86" stroke-width="1" zvalue="9386"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243" LinkObjectIDznd="225" MaxPinNum="2"/>
   </metadata>
  <path d="M 562.35 296.86 L 610.34 296.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="223">
   <path class="kv110" d="M 599.54 147.81 L 562.37 147.81" stroke-width="1" zvalue="9387"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@0" LinkObjectIDznd="226" MaxPinNum="2"/>
   </metadata>
  <path d="M 599.54 147.81 L 562.37 147.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv110" d="M 599.54 213.81 L 562.35 213.81" stroke-width="1" zvalue="9388"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247@0" LinkObjectIDznd="242" MaxPinNum="2"/>
   </metadata>
  <path d="M 599.54 213.81 L 562.35 213.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="289">
   <path class="kv110" d="M 728.37 330.49 L 728.37 352.95" stroke-width="1" zvalue="9405"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="295@0" LinkObjectIDznd="48@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 728.37 330.49 L 728.37 352.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="288">
   <path class="kv110" d="M 680.37 330.49 L 680.37 379.71" stroke-width="1" zvalue="9406"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="294@0" LinkObjectIDznd="49@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 680.37 330.49 L 680.37 379.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="287">
   <path class="kv110" d="M 680.35 309.05 L 680.35 261.75" stroke-width="1" zvalue="9407"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="294@1" LinkObjectIDznd="297@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 680.35 309.05 L 680.35 261.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="286">
   <path class="kv110" d="M 680.35 235.94 L 680.35 198.67" stroke-width="1" zvalue="9408"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="297@0" LinkObjectIDznd="296@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 680.35 235.94 L 680.35 198.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="285">
   <path class="kv110" d="M 680.37 177.22 L 680.37 131.51" stroke-width="1" zvalue="9409"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="296@0" LinkObjectIDznd="293@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 680.37 177.22 L 680.37 131.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="284">
   <path class="kv110" d="M 728.35 309.05 L 728.34 292.61" stroke-width="1" zvalue="9410"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="295@1" LinkObjectIDznd="292@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 728.35 309.05 L 728.34 292.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="283">
   <path class="kv110" d="M 680.35 296.86 L 728.34 296.86" stroke-width="1" zvalue="9411"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="287" LinkObjectIDznd="284" MaxPinNum="2"/>
   </metadata>
  <path d="M 680.35 296.86 L 728.34 296.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="282">
   <path class="kv110" d="M 717.54 147.81 L 680.37 147.81" stroke-width="1" zvalue="9412"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="290@0" LinkObjectIDznd="285" MaxPinNum="2"/>
   </metadata>
  <path d="M 717.54 147.81 L 680.37 147.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="281">
   <path class="kv110" d="M 717.54 213.81 L 680.35 213.81" stroke-width="1" zvalue="9413"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="291@0" LinkObjectIDznd="286" MaxPinNum="2"/>
   </metadata>
  <path d="M 717.54 213.81 L 680.35 213.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="314">
   <path class="kv110" d="M 834.37 330.49 L 834.37 352.95" stroke-width="1" zvalue="9430"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@0" LinkObjectIDznd="48@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 834.37 330.49 L 834.37 352.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="313">
   <path class="kv110" d="M 786.37 330.49 L 786.37 379.71" stroke-width="1" zvalue="9431"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="319@0" LinkObjectIDznd="49@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 786.37 330.49 L 786.37 379.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="kv110" d="M 786.35 309.05 L 786.35 261.75" stroke-width="1" zvalue="9432"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="319@1" LinkObjectIDznd="322@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 786.35 309.05 L 786.35 261.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv110" d="M 786.35 235.94 L 786.35 198.67" stroke-width="1" zvalue="9433"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="322@0" LinkObjectIDznd="321@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 786.35 235.94 L 786.35 198.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="310">
   <path class="kv110" d="M 786.37 177.22 L 786.37 132.22" stroke-width="1" zvalue="9434"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="321@0" LinkObjectIDznd="318@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 786.37 177.22 L 786.37 132.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="309">
   <path class="kv110" d="M 834.35 309.05 L 834.34 292.61" stroke-width="1" zvalue="9435"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@1" LinkObjectIDznd="317@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 834.35 309.05 L 834.34 292.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="308">
   <path class="kv110" d="M 786.35 296.86 L 834.34 296.86" stroke-width="1" zvalue="9436"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="312" LinkObjectIDznd="309" MaxPinNum="2"/>
   </metadata>
  <path d="M 786.35 296.86 L 834.34 296.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="307">
   <path class="kv110" d="M 823.54 147.81 L 786.37 147.81" stroke-width="1" zvalue="9437"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="315@0" LinkObjectIDznd="310" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.54 147.81 L 786.37 147.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="306">
   <path class="kv110" d="M 823.54 213.81 L 786.35 213.81" stroke-width="1" zvalue="9438"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="316@0" LinkObjectIDznd="311" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.54 213.81 L 786.35 213.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="373">
   <path class="kv110" d="M 1436.93 247.95 L 1436.85 309.05" stroke-width="1" zvalue="9615"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@0" LinkObjectIDznd="250@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1436.93 247.95 L 1436.85 309.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="374">
   <path class="kv110" d="M 499.85 423.67 L 499.85 457.76" stroke-width="1" zvalue="9616"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@1" LinkObjectIDznd="261@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 499.85 423.67 L 499.85 457.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="389">
   <path class="kv110" d="M 1128.36 330.49 L 1128.36 352.95" stroke-width="1" zvalue="9631"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="396@0" LinkObjectIDznd="48@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1128.36 330.49 L 1128.36 352.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="387">
   <path class="kv110" d="M 1080.37 330.49 L 1080.37 379.71" stroke-width="1" zvalue="9632"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="395@0" LinkObjectIDznd="49@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.37 330.49 L 1080.37 379.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="386">
   <path class="kv110" d="M 1080.35 309.05 L 1080.35 261.75" stroke-width="1" zvalue="9633"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="395@1" LinkObjectIDznd="398@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.35 309.05 L 1080.35 261.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="385">
   <path class="kv110" d="M 1080.35 235.94 L 1080.35 198.67" stroke-width="1" zvalue="9634"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="398@0" LinkObjectIDznd="397@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.35 235.94 L 1080.35 198.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="384">
   <path class="kv110" d="M 1080.37 177.22 L 1080.37 131.51" stroke-width="1" zvalue="9635"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="397@0" LinkObjectIDznd="394@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.37 177.22 L 1080.37 131.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="383">
   <path class="kv110" d="M 1128.34 309.05 L 1128.34 292.61" stroke-width="1" zvalue="9636"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="396@1" LinkObjectIDznd="392@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1128.34 309.05 L 1128.34 292.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="382">
   <path class="kv110" d="M 1080.35 296.86 L 1128.34 296.86" stroke-width="1" zvalue="9637"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="386" LinkObjectIDznd="383" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.35 296.86 L 1128.34 296.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="381">
   <path class="kv110" d="M 1117.54 147.81 L 1080.37 147.81" stroke-width="1" zvalue="9638"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="390@0" LinkObjectIDznd="384" MaxPinNum="2"/>
   </metadata>
  <path d="M 1117.54 147.81 L 1080.37 147.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="380">
   <path class="kv110" d="M 1117.54 213.81 L 1080.35 213.81" stroke-width="1" zvalue="9639"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="391@0" LinkObjectIDznd="385" MaxPinNum="2"/>
   </metadata>
  <path d="M 1117.54 213.81 L 1080.35 213.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="425">
   <path class="kv110" d="M 1234.36 330.49 L 1234.36 352.95" stroke-width="1" zvalue="9656"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="447@0" LinkObjectIDznd="48@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.36 330.49 L 1234.36 352.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="424">
   <path class="kv110" d="M 1186.37 330.49 L 1186.37 379.71" stroke-width="1" zvalue="9657"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="443@0" LinkObjectIDznd="49@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1186.37 330.49 L 1186.37 379.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="423">
   <path class="kv110" d="M 1186.35 309.05 L 1186.35 261.75" stroke-width="1" zvalue="9658"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="443@1" LinkObjectIDznd="449@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1186.35 309.05 L 1186.35 261.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="422">
   <path class="kv110" d="M 1186.35 235.94 L 1186.35 198.67" stroke-width="1" zvalue="9659"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="449@0" LinkObjectIDznd="448@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1186.35 235.94 L 1186.35 198.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="421">
   <path class="kv110" d="M 1186.37 177.22 L 1186.37 131.6" stroke-width="1" zvalue="9660"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="448@0" LinkObjectIDznd="429@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1186.37 177.22 L 1186.37 131.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="420">
   <path class="kv110" d="M 1234.34 309.05 L 1234.34 292.61" stroke-width="1" zvalue="9661"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="447@1" LinkObjectIDznd="428@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.34 309.05 L 1234.34 292.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="419">
   <path class="kv110" d="M 1186.35 296.86 L 1234.34 296.86" stroke-width="1" zvalue="9662"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="423" LinkObjectIDznd="420" MaxPinNum="2"/>
   </metadata>
  <path d="M 1186.35 296.86 L 1234.34 296.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="418">
   <path class="kv110" d="M 1223.54 147.81 L 1186.37 147.81" stroke-width="1" zvalue="9663"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="426@0" LinkObjectIDznd="421" MaxPinNum="2"/>
   </metadata>
  <path d="M 1223.54 147.81 L 1186.37 147.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="417">
   <path class="kv110" d="M 1223.54 213.81 L 1186.35 213.81" stroke-width="1" zvalue="9664"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="427@0" LinkObjectIDznd="422" MaxPinNum="2"/>
   </metadata>
  <path d="M 1223.54 213.81 L 1186.35 213.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="466">
   <path class="kv110" d="M 1352.36 330.49 L 1352.36 352.95" stroke-width="1" zvalue="9681"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="472@0" LinkObjectIDznd="48@10" MaxPinNum="2"/>
   </metadata>
  <path d="M 1352.36 330.49 L 1352.36 352.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="465">
   <path class="kv110" d="M 1304.37 330.49 L 1304.37 379.71" stroke-width="1" zvalue="9682"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="471@0" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1304.37 330.49 L 1304.37 379.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="464">
   <path class="kv110" d="M 1304.35 309.05 L 1304.35 261.75" stroke-width="1" zvalue="9683"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="471@1" LinkObjectIDznd="474@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1304.35 309.05 L 1304.35 261.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="463">
   <path class="kv110" d="M 1304.35 235.94 L 1304.35 198.67" stroke-width="1" zvalue="9684"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="474@0" LinkObjectIDznd="473@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1304.35 235.94 L 1304.35 198.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="462">
   <path class="kv110" d="M 1304.37 177.22 L 1304.37 131.51" stroke-width="1" zvalue="9685"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="473@0" LinkObjectIDznd="470@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1304.37 177.22 L 1304.37 131.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="461">
   <path class="kv110" d="M 1352.34 309.05 L 1352.34 292.61" stroke-width="1" zvalue="9686"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="472@1" LinkObjectIDznd="469@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1352.34 309.05 L 1352.34 292.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="460">
   <path class="kv110" d="M 1304.35 296.86 L 1352.34 296.86" stroke-width="1" zvalue="9687"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="464" LinkObjectIDznd="461" MaxPinNum="2"/>
   </metadata>
  <path d="M 1304.35 296.86 L 1352.34 296.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="459">
   <path class="kv110" d="M 1341.54 147.81 L 1304.37 147.81" stroke-width="1" zvalue="9688"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="467@0" LinkObjectIDznd="462" MaxPinNum="2"/>
   </metadata>
  <path d="M 1341.54 147.81 L 1304.37 147.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="458">
   <path class="kv110" d="M 1341.54 213.81 L 1304.35 213.81" stroke-width="1" zvalue="9689"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="468@0" LinkObjectIDznd="463" MaxPinNum="2"/>
   </metadata>
  <path d="M 1341.54 213.81 L 1304.35 213.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="483">
   <path class="kv35" d="M 470.48 644.38 L 470.48 663.2" stroke-width="1" zvalue="9700"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="485@0" LinkObjectIDznd="484@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 470.48 644.38 L 470.48 663.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="482">
   <path class="kv35" d="M 470.45 687.03 L 470.45 703.71" stroke-width="1" zvalue="9701"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="484@1" LinkObjectIDznd="475@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 470.45 687.03 L 470.45 703.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="538">
   <path class="kv35" d="M 497.59 717.2 L 497.59 703.71" stroke-width="1" zvalue="9766"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="537@0" LinkObjectIDznd="475@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 497.59 717.2 L 497.59 703.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="541">
   <path class="kv35" d="M 497.56 741.03 L 497.56 763.5" stroke-width="1" zvalue="9770"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="537@1" LinkObjectIDznd="540@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 497.56 741.03 L 497.56 763.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="543">
   <path class="kv35" d="M 495.83 789.31 L 495.83 815.64" stroke-width="1" zvalue="9773"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="540@1" LinkObjectIDznd="542@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 495.83 789.31 L 495.83 815.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="551">
   <path class="kv35" d="M 495.8 839.47 L 495.8 914.05" stroke-width="1" zvalue="9782"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="542@1" LinkObjectIDznd="548@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 495.8 839.47 L 495.8 914.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="564">
   <path class="kv35" d="M 469.28 910.81 L 469.28 904.23" stroke-width="1" zvalue="9795"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="547@0" LinkObjectIDznd="546@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 469.28 910.81 L 469.28 904.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="565">
   <path class="kv35" d="M 469.31 880.4 L 469.31 871.56 L 495.8 871.56" stroke-width="1" zvalue="9796"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="546@0" LinkObjectIDznd="551" MaxPinNum="2"/>
   </metadata>
  <path d="M 469.31 880.4 L 469.31 871.56 L 495.8 871.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="568">
   <path class="kv35" d="M 480.46 749.62 L 497.56 749.62" stroke-width="1" zvalue="9802"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="567@0" LinkObjectIDznd="541" MaxPinNum="2"/>
   </metadata>
  <path d="M 480.46 749.62 L 497.56 749.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="570">
   <path class="kv35" d="M 479.57 800.51 L 495.83 800.51" stroke-width="1" zvalue="9805"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="569@0" LinkObjectIDznd="543" MaxPinNum="2"/>
   </metadata>
  <path d="M 479.57 800.51 L 495.83 800.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="575">
   <path class="kv35" d="M 480.46 854.95 L 495.8 854.95" stroke-width="1" zvalue="9808"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="574@0" LinkObjectIDznd="551" MaxPinNum="2"/>
   </metadata>
  <path d="M 480.46 854.95 L 495.8 854.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="614">
   <path class="kv35" d="M 579.59 717.2 L 579.59 703.71" stroke-width="1" zvalue="9820"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="615@0" LinkObjectIDznd="475@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 579.59 717.2 L 579.59 703.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="612">
   <path class="kv35" d="M 579.56 741.03 L 579.56 763.5" stroke-width="1" zvalue="9824"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="615@1" LinkObjectIDznd="613@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 579.56 741.03 L 579.56 763.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="610">
   <path class="kv35" d="M 577.83 789.31 L 577.83 815.64" stroke-width="1" zvalue="9826"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="613@1" LinkObjectIDznd="611@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 577.83 789.31 L 577.83 815.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="606">
   <path class="kv35" d="M 577.8 839.47 L 577.8 914.22" stroke-width="1" zvalue="9833"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="611@1" LinkObjectIDznd="609@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 577.8 839.47 L 577.8 914.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="605">
   <path class="kv35" d="M 551.28 910.81 L 551.28 904.23" stroke-width="1" zvalue="9834"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="608@0" LinkObjectIDznd="607@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 551.28 910.81 L 551.28 904.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="604">
   <path class="kv35" d="M 551.31 880.4 L 551.31 871.56 L 577.8 871.56" stroke-width="1" zvalue="9835"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="607@0" LinkObjectIDznd="606" MaxPinNum="2"/>
   </metadata>
  <path d="M 551.31 880.4 L 551.31 871.56 L 577.8 871.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="602">
   <path class="kv35" d="M 562.46 749.62 L 579.56 749.62" stroke-width="1" zvalue="9837"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="603@0" LinkObjectIDznd="612" MaxPinNum="2"/>
   </metadata>
  <path d="M 562.46 749.62 L 579.56 749.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="600">
   <path class="kv35" d="M 561.57 800.51 L 577.83 800.51" stroke-width="1" zvalue="9841"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="601@0" LinkObjectIDznd="610" MaxPinNum="2"/>
   </metadata>
  <path d="M 561.57 800.51 L 577.83 800.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="598">
   <path class="kv35" d="M 562.46 854.95 L 577.8 854.95" stroke-width="1" zvalue="9843"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="599@0" LinkObjectIDznd="606" MaxPinNum="2"/>
   </metadata>
  <path d="M 562.46 854.95 L 577.8 854.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="638">
   <path class="kv35" d="M 661.59 717.2 L 661.59 703.71" stroke-width="1" zvalue="9847"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="639@0" LinkObjectIDznd="475@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 661.59 717.2 L 661.59 703.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="636">
   <path class="kv35" d="M 661.56 741.03 L 661.56 763.5" stroke-width="1" zvalue="9851"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="639@1" LinkObjectIDznd="637@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 661.56 741.03 L 661.56 763.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="634">
   <path class="kv35" d="M 659.83 789.31 L 659.83 815.64" stroke-width="1" zvalue="9853"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="637@1" LinkObjectIDznd="635@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 659.83 789.31 L 659.83 815.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="631">
   <path class="kv35" d="M 659.8 839.47 L 659.8 911.13" stroke-width="1" zvalue="9859"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="635@1" LinkObjectIDznd="633@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 659.8 839.47 L 659.8 911.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="630">
   <path class="kv35" d="M 633.28 910.81 L 633.28 905.48" stroke-width="1" zvalue="9860"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="640@0" LinkObjectIDznd="712@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 633.28 910.81 L 633.28 905.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="629">
   <path class="kv35" d="M 632.56 881.65 L 632.56 871.56 L 659.8 871.56" stroke-width="1" zvalue="9861"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="712@0" LinkObjectIDznd="631" MaxPinNum="2"/>
   </metadata>
  <path d="M 632.56 881.65 L 632.56 871.56 L 659.8 871.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="627">
   <path class="kv35" d="M 644.46 749.62 L 661.56 749.62" stroke-width="1" zvalue="9863"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="628@0" LinkObjectIDznd="636" MaxPinNum="2"/>
   </metadata>
  <path d="M 644.46 749.62 L 661.56 749.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="625">
   <path class="kv35" d="M 643.57 800.51 L 659.83 800.51" stroke-width="1" zvalue="9867"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="626@0" LinkObjectIDznd="634" MaxPinNum="2"/>
   </metadata>
  <path d="M 643.57 800.51 L 659.83 800.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="623">
   <path class="kv35" d="M 644.46 854.95 L 659.8 854.95" stroke-width="1" zvalue="9869"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="624@0" LinkObjectIDznd="631" MaxPinNum="2"/>
   </metadata>
  <path d="M 644.46 854.95 L 659.8 854.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="682">
   <path class="kv35" d="M 829.3 733.08 L 829.3 703.71" stroke-width="1" zvalue="9902"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="683@0" LinkObjectIDznd="475@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.3 733.08 L 829.3 703.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="680">
   <path class="kv35" d="M 851.96 772.66 L 829.28 772.66" stroke-width="1" zvalue="9904"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="681@0" LinkObjectIDznd="678" MaxPinNum="2"/>
   </metadata>
  <path d="M 851.96 772.66 L 829.28 772.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="678">
   <path class="kv35" d="M 829.28 754.53 L 829.28 798.02" stroke-width="1" zvalue="9906"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="683@1" LinkObjectIDznd="693@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.28 754.53 L 829.28 798.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="686">
   <path class="kv35" d="M 854.54 725.52 L 829.3 725.52" stroke-width="1" zvalue="9909"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="685@0" LinkObjectIDznd="682" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.54 725.52 L 829.3 725.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="703">
   <path class="kv35" d="M 1354.37 734.51 L 1354.37 701.71" stroke-width="1" zvalue="9917"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="704@0" LinkObjectIDznd="478@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1354.37 734.51 L 1354.37 701.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="700">
   <path class="kv35" d="M 1354.35 755.95 L 1354.35 775.22" stroke-width="1" zvalue="9921"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="704@1" LinkObjectIDznd="689@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1354.35 755.95 L 1354.35 775.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="715">
   <path class="kv35" d="M 703.21 634.71 L 703.21 917.14 L 752.66 917.14 L 752.66 863.19" stroke-width="1" zvalue="9938"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="388@1" LinkObjectIDznd="713@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 703.21 634.71 L 703.21 917.14 L 752.66 917.14 L 752.66 863.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="716">
   <path class="kv35" d="M 752.69 839.36 L 752.69 804.31" stroke-width="1" zvalue="9939"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="713@0" LinkObjectIDznd="705@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 752.69 839.36 L 752.69 804.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="717">
   <path class="kv35" d="M 752.54 778.5 L 752.54 756.62" stroke-width="1" zvalue="9940"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="705@0" LinkObjectIDznd="714@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 752.54 778.5 L 752.54 756.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="718">
   <path class="kv35" d="M 752.57 732.79 L 752.57 703.71" stroke-width="1" zvalue="9941"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="714@0" LinkObjectIDznd="475@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 752.57 732.79 L 752.57 703.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="720">
   <path class="kv35" d="M 769.23 764.19 L 752.54 764.19" stroke-width="1" zvalue="9944"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="719@0" LinkObjectIDznd="717" MaxPinNum="2"/>
   </metadata>
  <path d="M 769.23 764.19 L 752.54 764.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="722">
   <path class="kv35" d="M 769.51 821.33 L 752.69 821.33" stroke-width="1" zvalue="9947"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="721@0" LinkObjectIDznd="716" MaxPinNum="2"/>
   </metadata>
  <path d="M 769.51 821.33 L 752.69 821.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv35" d="M 907.87 737.94 L 907.87 703.71" stroke-width="1" zvalue="9957"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="475@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 907.87 737.94 L 907.87 703.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv35" d="M 1046.01 781.71 L 1097.28 781.64 L 1097.28 754.53" stroke-width="1" zvalue="9959"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="39@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1046.01 781.71 L 1097.28 781.64 L 1097.28 754.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv35" d="M 1097.3 733.08 L 1097.3 701.71" stroke-width="1" zvalue="9960"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@0" LinkObjectIDznd="478@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.3 733.08 L 1097.3 701.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv35" d="M 907.85 759.38 L 907.85 782.2 L 964.29 782.2" stroke-width="1" zvalue="9961"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@1" LinkObjectIDznd="40@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 907.85 759.38 L 907.85 782.2 L 964.29 782.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv35" d="M 985.74 782.22 L 1019.39 782.22" stroke-width="1" zvalue="9962"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@1" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 985.74 782.22 L 1019.39 782.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv35" d="M 938.52 771.62 L 938.52 782.2" stroke-width="1" zvalue="9964"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="46" MaxPinNum="2"/>
   </metadata>
  <path d="M 938.52 771.62 L 938.52 782.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv35" d="M 915.16 803.11 L 915.16 782.2" stroke-width="1" zvalue="9968"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@0" LinkObjectIDznd="46" MaxPinNum="2"/>
   </metadata>
  <path d="M 915.16 803.11 L 915.16 782.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv35" d="M 1202.69 839.36 L 1202.69 804.31" stroke-width="1" zvalue="9976"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="80@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1202.69 839.36 L 1202.69 804.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv35" d="M 1202.54 778.5 L 1202.54 756.62" stroke-width="1" zvalue="9977"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="78@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1202.54 778.5 L 1202.54 756.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv35" d="M 1202.57 732.79 L 1202.57 701.71" stroke-width="1" zvalue="9978"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="478@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1202.57 732.79 L 1202.57 701.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv35" d="M 1175.6 824.19 L 1202.69 824.19" stroke-width="1" zvalue="9981"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="77" MaxPinNum="2"/>
   </metadata>
  <path d="M 1175.6 824.19 L 1202.69 824.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv35" d="M 1175.89 893.33 L 1202.66 893.33" stroke-width="1" zvalue="9986"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="130" MaxPinNum="2"/>
   </metadata>
  <path d="M 1175.89 893.33 L 1202.66 893.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv35" d="M 688.84 660.11 L 703.21 660.11" stroke-width="1" zvalue="9989"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@0" LinkObjectIDznd="715" MaxPinNum="2"/>
   </metadata>
  <path d="M 688.84 660.11 L 703.21 660.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv110" d="M 687.84 499.97 L 702.88 499.97" stroke-width="1" zvalue="9996"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="219" MaxPinNum="2"/>
   </metadata>
  <path d="M 687.84 499.97 L 702.88 499.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="kv35" d="M 1258.78 640.66 L 1258.78 910 L 1202.66 910 L 1202.66 863.19" stroke-width="1" zvalue="9999"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@1" LinkObjectIDznd="79@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1258.78 640.66 L 1258.78 910 L 1202.66 910 L 1202.66 863.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="kv10" d="M 1569.02 145.03 L 1526.29 145.03" stroke-width="1" zvalue="10003"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@0" LinkObjectIDznd="131@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1569.02 145.03 L 1526.29 145.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv10" d="M 1665.43 145.03 L 1611.22 145.03" stroke-width="1" zvalue="10006"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@0" LinkObjectIDznd="135@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1665.43 145.03 L 1611.22 145.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv10" d="M 1686.88 145.01 L 1710.08 145.01" stroke-width="1" zvalue="10009"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@1" LinkObjectIDznd="152@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1686.88 145.01 L 1710.08 145.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv10" d="M 1662.4 111.35 L 1635.43 111.35 L 1635.43 145.03" stroke-width="1" zvalue="10017"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="149@0" LinkObjectIDznd="143" MaxPinNum="2"/>
   </metadata>
  <path d="M 1662.4 111.35 L 1635.43 111.35 L 1635.43 145.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv10" d="M 1667.45 174.32 L 1635.43 174.32 L 1635.43 141.59" stroke-width="1" zvalue="10018"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="154" MaxPinNum="2"/>
   </metadata>
  <path d="M 1667.45 174.32 L 1635.43 174.32 L 1635.43 141.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv10" d="M 1573.61 243.86 L 1526.29 243.86" stroke-width="1" zvalue="10029"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@0" LinkObjectIDznd="131@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1573.61 243.86 L 1526.29 243.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv10" d="M 1667.43 244.86 L 1615.81 244.86" stroke-width="1" zvalue="10031"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="183@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1667.43 244.86 L 1615.81 244.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv10" d="M 1688.88 244.83 L 1712.08 244.83" stroke-width="1" zvalue="10032"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@1" LinkObjectIDznd="176@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1688.88 244.83 L 1712.08 244.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv10" d="M 1664.4 211.35 L 1637.43 211.35 L 1637.43 244.86" stroke-width="1" zvalue="10038"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="180" MaxPinNum="2"/>
   </metadata>
  <path d="M 1664.4 211.35 L 1637.43 211.35 L 1637.43 244.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="kv10" d="M 1669.45 274.32 L 1637.43 274.32 L 1637.43 241.43" stroke-width="1" zvalue="10039"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@0" LinkObjectIDznd="175" MaxPinNum="2"/>
   </metadata>
  <path d="M 1669.45 274.32 L 1637.43 274.32 L 1637.43 241.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv10" d="M 1572.61 333.61 L 1526.29 333.61" stroke-width="1" zvalue="10043"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="131@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1572.61 333.61 L 1526.29 333.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="kv10" d="M 1667.43 333.61 L 1614.81 333.61" stroke-width="1" zvalue="10046"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="200@0" LinkObjectIDznd="202@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1667.43 333.61 L 1614.81 333.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="kv10" d="M 1688.88 333.58 L 1712.08 333.58" stroke-width="1" zvalue="10047"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="200@1" LinkObjectIDznd="195@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1688.88 333.58 L 1712.08 333.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 1664.4 300.1 L 1637.43 300.1 L 1637.43 333.61" stroke-width="1" zvalue="10053"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="199" MaxPinNum="2"/>
   </metadata>
  <path d="M 1664.4 300.1 L 1637.43 300.1 L 1637.43 333.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv10" d="M 1669.45 363.07 L 1637.43 363.07 L 1637.43 330.34" stroke-width="1" zvalue="10054"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="194" MaxPinNum="2"/>
   </metadata>
  <path d="M 1669.45 363.07 L 1637.43 363.07 L 1637.43 330.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv10" d="M 1696.88 423.24 L 1639.3 423.24" stroke-width="1" zvalue="10065"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="204@0" LinkObjectIDznd="207@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1696.88 423.24 L 1639.3 423.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv10" d="M 1664.1 390.73 L 1640.19 390.32" stroke-width="1" zvalue="10068"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="216@0" LinkObjectIDznd="206@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1664.1 390.73 L 1640.19 390.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="kv10" d="M 1611.42 390.22 L 1593.02 390.22 L 1593.02 423.15" stroke-width="1" zvalue="10069"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@1" LinkObjectIDznd="148" MaxPinNum="2"/>
   </metadata>
  <path d="M 1611.42 390.22 L 1593.02 390.22 L 1593.02 423.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv10" d="M 1575.36 684.61 L 1530.29 684.61" stroke-width="1" zvalue="10073"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="132@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1575.36 684.61 L 1530.29 684.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv10" d="M 1670.18 684.61 L 1617.56 684.61" stroke-width="1" zvalue="10076"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="158@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1670.18 684.61 L 1617.56 684.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv10" d="M 1691.63 684.58 L 1714.83 684.58" stroke-width="1" zvalue="10077"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@1" LinkObjectIDznd="90@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1691.63 684.58 L 1714.83 684.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv10" d="M 1667.15 651.1 L 1640.18 651.1 L 1640.18 684.61" stroke-width="1" zvalue="10083"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="145" MaxPinNum="2"/>
   </metadata>
  <path d="M 1667.15 651.1 L 1640.18 651.1 L 1640.18 684.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv10" d="M 1672.2 714.07 L 1640.18 714.07 L 1640.18 681.34" stroke-width="1" zvalue="10084"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@0" LinkObjectIDznd="85" MaxPinNum="2"/>
   </metadata>
  <path d="M 1672.2 714.07 L 1640.18 714.07 L 1640.18 681.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="372">
   <path class="kv10" d="M 1575.36 779.61 L 1530.29 779.61" stroke-width="1" zvalue="10088"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="477@0" LinkObjectIDznd="132@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1575.36 779.61 L 1530.29 779.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="341">
   <path class="kv10" d="M 1722.18 779.61 L 1617.56 779.61" stroke-width="1" zvalue="10091"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="371@0" LinkObjectIDznd="477@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1722.18 779.61 L 1617.56 779.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="kv10" d="M 1743.63 779.58 L 1759.25 779.58" stroke-width="1" zvalue="10092"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="371@1" LinkObjectIDznd="487@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1743.63 779.58 L 1759.25 779.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="203">
   <path class="kv10" d="M 1667.15 746.1 L 1640.18 746.1 L 1640.18 779.61" stroke-width="1" zvalue="10098"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@0" LinkObjectIDznd="341" MaxPinNum="2"/>
   </metadata>
  <path d="M 1667.15 746.1 L 1640.18 746.1 L 1640.18 779.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv10" d="M 1672.2 809.07 L 1640.18 809.07 L 1640.18 776.34" stroke-width="1" zvalue="10099"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="203" MaxPinNum="2"/>
   </metadata>
  <path d="M 1672.2 809.07 L 1640.18 809.07 L 1640.18 776.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="496">
   <path class="kv10" d="M 1642.84 914.42 L 1600.3 914.42" stroke-width="1" zvalue="10112"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="492@0" LinkObjectIDznd="495@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1642.84 914.42 L 1600.3 914.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="497">
   <path class="kv10" d="M 1571.53 915.15 L 1530.29 915.15" stroke-width="1" zvalue="10113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="495@1" LinkObjectIDznd="132@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1571.53 915.15 L 1530.29 915.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="500">
   <path class="kv10" d="M 1627.49 863.99 L 1601.3 863.99" stroke-width="1" zvalue="10115"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="490@0" LinkObjectIDznd="499@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1627.49 863.99 L 1601.3 863.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="501">
   <path class="kv10" d="M 1569 864 L 1530.29 864" stroke-width="1" zvalue="10116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="499@1" LinkObjectIDznd="132@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1569 864 L 1530.29 864" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="507">
   <path class="kv10" d="M 1320.2 596.23 L 1428.04 596.23" stroke-width="1" zvalue="10127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@2" LinkObjectIDznd="505@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1320.2 596.23 L 1428.04 596.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="509">
   <path class="kv10" d="M 1361.11 611.31 L 1361.11 596.23" stroke-width="1" zvalue="10130"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="508@0" LinkObjectIDznd="507" MaxPinNum="2"/>
   </metadata>
  <path d="M 1361.11 611.31 L 1361.11 596.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="512">
   <path class="kv35" d="M 1253.1 666.83 L 1258.78 666.83" stroke-width="1" zvalue="10133"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="511@0" LinkObjectIDznd="130" MaxPinNum="2"/>
   </metadata>
  <path d="M 1253.1 666.83 L 1258.78 666.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="515">
   <path class="kv110" d="M 1298.95 541.54 L 1258.42 541.54" stroke-width="1" zvalue="10138"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="514@0" LinkObjectIDznd="446" MaxPinNum="2"/>
   </metadata>
  <path d="M 1298.95 541.54 L 1258.42 541.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="517">
   <path class="kv110" d="M 1242.06 499.81 L 1259.89 499.81" stroke-width="1" zvalue="10139"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="513@0" LinkObjectIDznd="435" MaxPinNum="2"/>
   </metadata>
  <path d="M 1242.06 499.81 L 1259.89 499.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="521">
   <path class="kv10" d="M 1621.71 604.81 L 1621.71 631.43 L 1530.29 631.43" stroke-width="1" zvalue="10144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="519@1" LinkObjectIDznd="132@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1621.71 604.81 L 1621.71 631.43 L 1530.29 631.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="524">
   <path class="kv10" d="M 1526.29 480.14 L 1559.36 480.14" stroke-width="1" zvalue="10146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@3" LinkObjectIDznd="523@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1526.29 480.14 L 1559.36 480.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="525">
   <path class="kv10" d="M 1595.09 480.09 L 1621.71 480.09 L 1621.71 563.76" stroke-width="1" zvalue="10147"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="523@1" LinkObjectIDznd="519@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1595.09 480.09 L 1621.71 480.09 L 1621.71 563.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="528">
   <path class="kv10" d="M 1644.74 510.89 L 1621.71 510.89" stroke-width="1" zvalue="10150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="527@0" LinkObjectIDznd="525" MaxPinNum="2"/>
   </metadata>
  <path d="M 1644.74 510.89 L 1621.71 510.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="530">
   <path class="kv10" d="M 1645.31 542.32 L 1621.71 542.32" stroke-width="1" zvalue="10153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="529@0" LinkObjectIDznd="525" MaxPinNum="2"/>
   </metadata>
  <path d="M 1645.31 542.32 L 1621.71 542.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="531">
   <path class="kv10" d="M 764.63 590.28 L 1069 590.28 L 1069 398.73 L 1428.04 398.73" stroke-width="1" zvalue="10154"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="388@2" LinkObjectIDznd="159@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 764.63 590.28 L 1069 590.28 L 1069 398.73 L 1428.04 398.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="536">
   <path class="kv35" d="M 1381.63 668.87 L 1407.5 668.87" stroke-width="1" zvalue="10160"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="534@1" LinkObjectIDznd="532@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1381.63 668.87 L 1407.5 668.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="520">
   <path class="kv10" d="M 1710.7 779.5 L 1710.7 779.61" stroke-width="1" zvalue="10168"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="491@0" LinkObjectIDznd="341" MaxPinNum="2"/>
   </metadata>
  <path d="M 1710.7 779.5 L 1710.7 779.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="696">
   <path class="kv10" d="M 789.11 582.12 L 789.11 590.28" stroke-width="1" zvalue="10251"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="493@0" LinkObjectIDznd="531" MaxPinNum="2"/>
   </metadata>
  <path d="M 789.11 582.12 L 789.11 590.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv35" d="M 1258.12 621.51 L 1272 621.51 L 1272 668.89 L 1360.18 668.89" stroke-width="1" zvalue="10278"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@3" LinkObjectIDznd="534@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1258.12 621.51 L 1272 621.51 L 1272 668.89 L 1360.18 668.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv35" d="M 1324.54 654.69 L 1324.54 668.89" stroke-width="1" zvalue="10279"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="544@0" LinkObjectIDznd="114" MaxPinNum="2"/>
   </metadata>
  <path d="M 1324.54 654.69 L 1324.54 668.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv35" d="M 486.65 654.19 L 470.48 654.19" stroke-width="1" zvalue="10282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="483" MaxPinNum="2"/>
   </metadata>
  <path d="M 486.65 654.19 L 470.48 654.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv10" d="M 1610.53 423.15 L 1526.29 423.15" stroke-width="1" zvalue="10283"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@1" LinkObjectIDznd="131@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1610.53 423.15 L 1526.29 423.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="205">
   <path class="kv10" d="M 1470.24 596.54 L 1530.29 596.54" stroke-width="1" zvalue="10284"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="505@1" LinkObjectIDznd="132@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1470.24 596.54 L 1530.29 596.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv10" d="M 1470.24 398.73 L 1526.29 398.73" stroke-width="1" zvalue="10285"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@1" LinkObjectIDznd="131@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1470.24 398.73 L 1526.29 398.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="248">
   <use class="kv110" height="38" transform="rotate(0,1437.29,219.857) scale(1.57895,1.57895) translate(-516.005,-69.6143)" width="38" x="1407.285714285714" xlink:href="#Accessory:避雷器PT1_0" y="189.8571428571429" zvalue="8965"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454445498371" ObjectName="110kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,1437.29,219.857) scale(1.57895,1.57895) translate(-516.005,-69.6143)" width="38" x="1407.285714285714" y="189.8571428571429"/></g>
  <g id="261">
   <use class="kv110" height="38" transform="rotate(0,500.205,485.857) scale(1.57895,-1.57895) translate(-172.408,-782.567)" width="38" x="470.2045151740323" xlink:href="#Accessory:避雷器PT1_0" y="455.8571428571428" zvalue="8974"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454449364995" ObjectName="110kVⅡ母电压互感器"/>
   </metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,500.205,485.857) scale(1.57895,-1.57895) translate(-172.408,-782.567)" width="38" x="470.2045151740323" y="455.8571428571428"/></g>
  <g id="547">
   <use class="kv35" height="40" transform="rotate(0,469.278,929.889) scale(-1,1) translate(-938.556,-2.02036e-13)" width="20" x="459.2777777777781" xlink:href="#Accessory:线路PT带避雷器0904_0" y="909.8888888888887" zvalue="9776"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454451724291" ObjectName="35kV西那线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,469.278,929.889) scale(-1,1) translate(-938.556,-2.02036e-13)" width="20" x="459.2777777777781" y="909.8888888888887"/></g>
  <g id="608">
   <use class="kv35" height="40" transform="rotate(0,551.278,929.889) scale(-1,1) translate(-1102.56,-2.02036e-13)" width="20" x="541.2777777777781" xlink:href="#Accessory:线路PT带避雷器0904_0" y="909.8888888888887" zvalue="9829"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454452707331" ObjectName="35kV昆润采选线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,551.278,929.889) scale(-1,1) translate(-1102.56,-2.02036e-13)" width="20" x="541.2777777777781" y="909.8888888888887"/></g>
  <g id="640">
   <use class="kv35" height="40" transform="rotate(0,633.278,929.889) scale(-1,1) translate(-1266.56,-2.02036e-13)" width="20" x="623.2777777777781" xlink:href="#Accessory:线路PT带避雷器0904_0" y="909.8888888888887" zvalue="9856"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454453559299" ObjectName="35kV曼悠河一级线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,633.278,929.889) scale(-1,1) translate(-1266.56,-2.02036e-13)" width="20" x="623.2777777777781" y="909.8888888888887"/></g>
  <g id="689">
   <use class="kv35" height="35" transform="rotate(0,1369.03,798.929) scale(-1.42857,-1.42857) translate(-2320.92,-1350.68)" width="30" x="1347.597912017694" xlink:href="#Accessory:5卷PT带壁雷器_0" y="773.9285714285713" zvalue="9911"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454453952515" ObjectName="35kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1369.03,798.929) scale(-1.42857,-1.42857) translate(-2320.92,-1350.68)" width="30" x="1347.597912017694" y="773.9285714285713"/></g>
  <g id="693">
   <use class="kv35" height="48" transform="rotate(0,825.5,827.75) scale(1.25,-1.25) translate(-159.475,-1483.95)" width="45" x="797.375" xlink:href="#Accessory:母线电压互感器11_0" y="797.75" zvalue="9914"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454454018051" ObjectName="35kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,825.5,827.75) scale(1.25,-1.25) translate(-159.475,-1483.95)" width="45" x="797.375" y="797.75"/></g>
  <g id="50">
   <use class="kv35" height="26" transform="rotate(0,938.492,758.643) scale(0.917997,-1.04945) translate(83.3418,-1480.9)" width="12" x="932.9840359157987" xlink:href="#Accessory:避雷器1_0" y="745" zvalue="9963"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454454804483" ObjectName="35kV分段避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,938.492,758.643) scale(0.917997,-1.04945) translate(83.3418,-1480.9)" width="12" x="932.9840359157987" y="745"/></g>
  <g id="83">
   <use class="kv35" height="26" transform="rotate(270,675.857,660.143) scale(0.917997,-1.04945) translate(59.8811,-1288.54)" width="12" x="670.3491608150423" xlink:href="#Accessory:避雷器1_0" y="646.5" zvalue="9988"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454455394307" ObjectName="#1主变35kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,675.857,660.143) scale(0.917997,-1.04945) translate(59.8811,-1288.54)" width="12" x="670.3491608150423" y="646.5"/></g>
  <g id="91">
   <use class="kv110" height="26" transform="rotate(270,674.857,500) scale(0.917997,-1.04945) translate(59.7917,-975.797)" width="12" x="669.3491608150423" xlink:href="#Accessory:避雷器1_0" y="486.3571428571428" zvalue="9995"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454455459843" ObjectName="#1主变110kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,674.857,500) scale(0.917997,-1.04945) translate(59.7917,-975.797)" width="12" x="669.3491608150423" y="486.3571428571428"/></g>
  <g id="150">
   <use class="kv10" height="26" transform="rotate(90,1680.43,174.352) scale(-0.917997,-1.04945) translate(-3511.46,-339.845)" width="12" x="1674.920589386471" xlink:href="#Accessory:避雷器1_0" y="160.7087912087914" zvalue="10013"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454455721987" ObjectName="10kV盏西乡专线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1680.43,174.352) scale(-0.917997,-1.04945) translate(-3511.46,-339.845)" width="12" x="1674.920589386471" y="160.7087912087914"/></g>
  <g id="177">
   <use class="kv10" height="26" transform="rotate(90,1682.43,274.352) scale(-0.917997,-1.04945) translate(-3515.64,-535.133)" width="12" x="1676.920589386471" xlink:href="#Accessory:避雷器1_0" y="260.7087912087914" zvalue="10035"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454455918595" ObjectName="10kV支那乡专线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1682.43,274.352) scale(-0.917997,-1.04945) translate(-3515.64,-535.133)" width="12" x="1676.920589386471" y="260.7087912087914"/></g>
  <g id="196">
   <use class="kv10" height="26" transform="rotate(90,1682.43,363.102) scale(-0.917997,-1.04945) translate(-3515.64,-708.451)" width="12" x="1676.920589386471" xlink:href="#Accessory:避雷器1_0" y="349.4587912087914" zvalue="10050"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454456246275" ObjectName="10kV东洪坝线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1682.43,363.102) scale(-0.917997,-1.04945) translate(-3515.64,-708.451)" width="12" x="1676.920589386471" y="349.4587912087914"/></g>
  <g id="204">
   <use class="kv10" height="30" transform="rotate(270,1717,423.319) scale(1.26977,-1.53308) translate(-360.066,-691.445)" width="35" x="1694.782828282828" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="400.3224376142469" zvalue="10056"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454456508419" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1717,423.319) scale(1.26977,-1.53308) translate(-360.066,-691.445)" width="35" x="1694.782828282828" y="400.3224376142469"/></g>
  <g id="216">
   <use class="kv10" height="26" transform="rotate(90,1677.08,390.698) scale(0.917997,-1.04945) translate(149.318,-762.344)" width="12" x="1671.571383037265" xlink:href="#Accessory:避雷器1_0" y="377.0556010897197" zvalue="10067"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454456770563" ObjectName="10kVⅠ段母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1677.08,390.698) scale(0.917997,-1.04945) translate(149.318,-762.344)" width="12" x="1671.571383037265" y="377.0556010897197"/></g>
  <g id="128">
   <use class="kv10" height="26" transform="rotate(90,1685.18,714.102) scale(-0.917997,-1.04945) translate(-3521.38,-1393.91)" width="12" x="1679.670589386471" xlink:href="#Accessory:避雷器1_0" y="700.4587912087914" zvalue="10080"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454456901635" ObjectName="10kV盏西街道线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1685.18,714.102) scale(-0.917997,-1.04945) translate(-3521.38,-1393.91)" width="12" x="1679.670589386471" y="700.4587912087914"/></g>
  <g id="230">
   <use class="kv10" height="26" transform="rotate(90,1685.18,809.102) scale(-0.917997,-1.04945) translate(-3521.38,-1579.44)" width="12" x="1679.670589386471" xlink:href="#Accessory:避雷器1_0" y="795.4587912087914" zvalue="10095"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454457163779" ObjectName="10kV2号接地变及消弧线圈避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1685.18,809.102) scale(-0.917997,-1.04945) translate(-3521.38,-1579.44)" width="12" x="1679.670589386471" y="795.4587912087914"/></g>
  <g id="492">
   <use class="kv10" height="42" transform="rotate(90,1670,927.116) scale(1.33333,1.33333) translate(-412.5,-224.779)" width="30" x="1650" xlink:href="#Accessory:4卷PT带容断器_0" y="899.1164790805124" zvalue="10107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454457622531" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(90,1670,927.116) scale(1.33333,1.33333) translate(-412.5,-224.779)" width="30" x="1650" y="899.1164790805124"/></g>
  <g id="493">
   <use class="kv10" height="26" transform="rotate(180,789.143,569.143) scale(0.917997,1.04945) translate(70.0007,-26.1754)" width="12" x="783.6348751007565" xlink:href="#Accessory:避雷器1_0" y="555.5" zvalue="10122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454457819139" ObjectName="#1主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,789.143,569.143) scale(0.917997,1.04945) translate(70.0007,-26.1754)" width="12" x="783.6348751007565" y="555.5"/></g>
  <g id="508">
   <use class="kv10" height="26" transform="rotate(180,1361.14,624.286) scale(0.917997,-1.04945) translate(121.096,-1218.51)" width="12" x="1355.634875100757" xlink:href="#Accessory:避雷器1_0" y="610.6428571428571" zvalue="10129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454457884675" ObjectName="#2主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1361.14,624.286) scale(0.917997,-1.04945) translate(121.096,-1218.51)" width="12" x="1355.634875100757" y="610.6428571428571"/></g>
  <g id="511">
   <use class="kv35" height="26" transform="rotate(270,1235.43,666.857) scale(0.931232,-1.42857) translate(90.819,-1128.09)" width="12" x="1229.841178772941" xlink:href="#Accessory:避雷器1_0" y="648.2857142857142" zvalue="10132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454457950211" ObjectName="#2主变35kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1235.43,666.857) scale(0.931232,-1.42857) translate(90.819,-1128.09)" width="12" x="1229.841178772941" y="648.2857142857142"/></g>
  <g id="513">
   <use class="kv110" height="26" transform="rotate(270,1229.08,499.838) scale(0.917997,-1.04945) translate(109.299,-975.48)" width="12" x="1223.571383037264" xlink:href="#Accessory:避雷器1_0" y="486.1948856145196" zvalue="10135"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454458015747" ObjectName="#2主变110kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1229.08,499.838) scale(0.917997,-1.04945) translate(109.299,-975.48)" width="12" x="1223.571383037264" y="486.1948856145196"/></g>
  <g id="527">
   <use class="kv10" height="26" transform="rotate(90,1657.71,510.857) scale(0.917997,-1.04945) translate(147.589,-997)" width="12" x="1652.206303672185" xlink:href="#Accessory:避雷器1_0" y="497.2142857142857" zvalue="10149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454458277891" ObjectName="10kV分段避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1657.71,510.857) scale(0.917997,-1.04945) translate(147.589,-997)" width="12" x="1652.206303672185" y="497.2142857142857"/></g>
  <g id="529">
   <use class="kv10" height="26" transform="rotate(90,1658.29,542.286) scale(0.917997,-1.04945) translate(147.64,-1058.38)" width="12" x="1652.777732243614" xlink:href="#Accessory:避雷器1_0" y="528.6428571428571" zvalue="10152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454458343427" ObjectName="10kV分段避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1658.29,542.286) scale(0.917997,-1.04945) translate(147.64,-1058.38)" width="12" x="1652.777732243614" y="528.6428571428571"/></g>
  <g id="544">
   <use class="kv35" height="26" transform="rotate(0,1324.57,641.714) scale(-0.917997,-1.04945) translate(-2767.96,-1252.55)" width="12" x="1319.063446529328" xlink:href="#Accessory:避雷器1_0" y="628.0714285714287" zvalue="10162"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454458540035" ObjectName="#2主变35kV侧避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1324.57,641.714) scale(-0.917997,-1.04945) translate(-2767.96,-1252.55)" width="12" x="1319.063446529328" y="628.0714285714287"/></g>
  <g id="491">
   <use class="kv10" height="15" transform="rotate(90,1695.84,779.5) scale(-2.46667,2.47619) translate(-2372.35,-453.63)" width="15" x="1677.34188034188" xlink:href="#Accessory:附属接地变_0" y="760.9285714285714" zvalue="10166"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454458605571" ObjectName="10kV2号接地变"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(90,1695.84,779.5) scale(-2.46667,2.47619) translate(-2372.35,-453.63)" width="15" x="1677.34188034188" y="760.9285714285714"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="327">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="327" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,90.5,480) scale(1,1) translate(0,0)" writing-mode="lr" x="90.23999999999999" xml:space="preserve" y="484.69" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135271444482" ObjectName=""/>
   </metadata>
  </g>
  <g id="326">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="326" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,90.6429,507.062) scale(1,1) translate(7.34333e-15,0)" writing-mode="lr" x="90.39" xml:space="preserve" y="511.75" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135271182338" ObjectName=""/>
   </metadata>
  </g>
  <g id="323">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="323" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,90.6429,534.125) scale(1,1) translate(7.34333e-15,0)" writing-mode="lr" x="90.39" xml:space="preserve" y="538.8200000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135271247874" ObjectName=""/>
   </metadata>
  </g>
  <g id="300">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="300" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,90.6429,561.188) scale(1,1) translate(7.34333e-15,0)" writing-mode="lr" x="90.39" xml:space="preserve" y="565.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135271313410" ObjectName=""/>
   </metadata>
  </g>
  <g id="298">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="298" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,90.6429,588.25) scale(1,1) translate(7.34333e-15,0)" writing-mode="lr" x="90.39" xml:space="preserve" y="592.9400000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135271641090" ObjectName=""/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="240" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,196.643,480) scale(1,1) translate(1.91117e-14,0)" writing-mode="lr" x="196.39" xml:space="preserve" y="484.69" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135284944898" ObjectName=""/>
   </metadata>
  </g>
  <g id="188">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,196.643,507.063) scale(1,1) translate(1.91117e-14,5.4963e-14)" writing-mode="lr" x="196.39" xml:space="preserve" y="511.75" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135284682754" ObjectName=""/>
   </metadata>
  </g>
  <g id="187">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="187" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,196.643,534.125) scale(1,1) translate(1.91117e-14,0)" writing-mode="lr" x="196.39" xml:space="preserve" y="538.8200000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135284748290" ObjectName=""/>
   </metadata>
  </g>
  <g id="186">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="186" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,196.643,561.188) scale(1,1) translate(1.91117e-14,0)" writing-mode="lr" x="196.39" xml:space="preserve" y="565.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135284813826" ObjectName=""/>
   </metadata>
  </g>
  <g id="156">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="156" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,196.643,588.25) scale(1,1) translate(1.91117e-14,0)" writing-mode="lr" x="196.39" xml:space="preserve" y="592.9400000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135285141506" ObjectName=""/>
   </metadata>
  </g>
  <g id="122">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,142.643,480) scale(1,1) translate(1.31165e-14,0)" writing-mode="lr" x="142.39" xml:space="preserve" y="484.69" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135270920194" ObjectName=""/>
   </metadata>
  </g>
  <g id="119">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,142.643,507.063) scale(1,1) translate(1.31165e-14,5.4963e-14)" writing-mode="lr" x="142.39" xml:space="preserve" y="511.75" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135270658050" ObjectName=""/>
   </metadata>
  </g>
  <g id="118">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="118" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,142.643,534.125) scale(1,1) translate(1.31165e-14,0)" writing-mode="lr" x="142.39" xml:space="preserve" y="538.8200000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135270723586" ObjectName=""/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="117" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,142.643,561.188) scale(1,1) translate(1.31165e-14,0)" writing-mode="lr" x="142.39" xml:space="preserve" y="565.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135270789122" ObjectName=""/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,142.643,588.25) scale(1,1) translate(1.31165e-14,0)" writing-mode="lr" x="142.39" xml:space="preserve" y="592.9400000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135271116802" ObjectName=""/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="105" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,158.143,177) scale(1,1) translate(0,0)" writing-mode="lr" x="157.78" xml:space="preserve" y="182.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135306702850" ObjectName=""/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="104" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,337.143,179) scale(1,1) translate(0,0)" writing-mode="lr" x="336.78" xml:space="preserve" y="184.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135306768386" ObjectName=""/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="15" id="103" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,158.143,203) scale(1,1) translate(0,0)" writing-mode="lr" x="157.78" xml:space="preserve" y="208.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135271575554" ObjectName=""/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="15" id="102" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,338.143,204) scale(1,1) translate(0,0)" writing-mode="lr" x="337.78" xml:space="preserve" y="209.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135271051266" ObjectName=""/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="101" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,159.143,299) scale(1,1) translate(0,0)" writing-mode="lr" x="158.78" xml:space="preserve" y="304.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135268757506" ObjectName=""/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="100" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,338.143,299) scale(1,1) translate(0,0)" writing-mode="lr" x="337.78" xml:space="preserve" y="304.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135304802306" ObjectName=""/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="15" id="99" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,159.143,229) scale(1,1) translate(0,0)" writing-mode="lr" x="158.78" xml:space="preserve" y="234.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135285075970" ObjectName=""/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="15" id="98" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,338.143,229) scale(1,1) translate(0,0)" writing-mode="lr" x="337.78" xml:space="preserve" y="234.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135285075970" ObjectName=""/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="15" id="97" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,160.393,275.5) scale(1,1) translate(0,0)" writing-mode="lr" x="160.03" xml:space="preserve" y="281.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135269019650" ObjectName=""/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="15" id="96" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,339.393,275.5) scale(1,1) translate(0,0)" writing-mode="lr" x="339.03" xml:space="preserve" y="281.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135305064450" ObjectName=""/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="15" id="95" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,159.143,251) scale(1,1) translate(0,0)" writing-mode="lr" x="158.78" xml:space="preserve" y="256.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135293726722" ObjectName=""/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="15" id="94" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,338.143,251) scale(1,1) translate(0,0)" writing-mode="lr" x="337.78" xml:space="preserve" y="256.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135294251010" ObjectName=""/>
   </metadata>
  </g>
  <g id="582">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="582" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,468.036,15.3571) scale(1,1) translate(2.81798e-13,0)" writing-mode="lr" x="467.57" xml:space="preserve" y="20.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135272230914" ObjectName="P"/>
   </metadata>
  </g>
  <g id="583">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="583" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,466.925,28.5794) scale(1,1) translate(2.81058e-13,0)" writing-mode="lr" x="466.46" xml:space="preserve" y="33.25" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135272296450" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="584">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="584" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,468.036,41.3571) scale(1,1) translate(2.81798e-13,0)" writing-mode="lr" x="467.57" xml:space="preserve" y="46.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135272361986" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="585">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="585" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,479.426,1005.14) scale(1,1) translate(2.89386e-13,0)" writing-mode="lr" x="478.96" xml:space="preserve" y="1009.81" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135287173122" ObjectName="P"/>
   </metadata>
  </g>
  <g id="586">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="586" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,479.676,1021.39) scale(1,1) translate(2.89552e-13,2.58132e-12)" writing-mode="lr" x="479.21" xml:space="preserve" y="1026.06" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135287238658" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="587">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="587" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,479.676,1036.39) scale(1,1) translate(2.89552e-13,0)" writing-mode="lr" x="479.21" xml:space="preserve" y="1041.06" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135287304194" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="562">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="562" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,298.5,480) scale(1,1) translate(3.04201e-14,0)" writing-mode="lr" x="298.24" xml:space="preserve" y="484.69" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135293595650" ObjectName=""/>
   </metadata>
  </g>
  <g id="561">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="561" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,298.5,507.062) scale(1,1) translate(3.04201e-14,0)" writing-mode="lr" x="298.24" xml:space="preserve" y="511.75" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135293333506" ObjectName=""/>
   </metadata>
  </g>
  <g id="560">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="560" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,298.5,534.125) scale(1,1) translate(3.04201e-14,0)" writing-mode="lr" x="298.24" xml:space="preserve" y="538.8200000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135293399042" ObjectName=""/>
   </metadata>
  </g>
  <g id="559">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="559" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,298.5,561.188) scale(1,1) translate(3.04201e-14,0)" writing-mode="lr" x="298.24" xml:space="preserve" y="565.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135293464578" ObjectName=""/>
   </metadata>
  </g>
  <g id="558">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="558" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,298.5,588.25) scale(1,1) translate(3.04201e-14,0)" writing-mode="lr" x="298.24" xml:space="preserve" y="592.9400000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135293792258" ObjectName=""/>
   </metadata>
  </g>
  <g id="557">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="557" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,247,480) scale(1,1) translate(2.47025e-14,0)" writing-mode="lr" x="246.74" xml:space="preserve" y="484.69" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135285469186" ObjectName=""/>
   </metadata>
  </g>
  <g id="556">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="556" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,247,507.062) scale(1,1) translate(2.47025e-14,0)" writing-mode="lr" x="246.74" xml:space="preserve" y="511.75" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135285207042" ObjectName=""/>
   </metadata>
  </g>
  <g id="555">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="555" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,247,534.125) scale(1,1) translate(2.47025e-14,0)" writing-mode="lr" x="246.74" xml:space="preserve" y="538.8200000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135285272578" ObjectName=""/>
   </metadata>
  </g>
  <g id="554">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="554" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,247,561.188) scale(1,1) translate(2.47025e-14,0)" writing-mode="lr" x="246.74" xml:space="preserve" y="565.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135285338114" ObjectName=""/>
   </metadata>
  </g>
  <g id="553">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="553" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,247,588.25) scale(1,1) translate(2.47025e-14,0)" writing-mode="lr" x="246.74" xml:space="preserve" y="592.9400000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135285665794" ObjectName=""/>
   </metadata>
  </g>
  <g id="552">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="552" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,351.5,480) scale(1,1) translate(3.63043e-14,0)" writing-mode="lr" x="351.24" xml:space="preserve" y="484.69" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135294119938" ObjectName=""/>
   </metadata>
  </g>
  <g id="550">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="550" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,351.5,507.062) scale(1,1) translate(3.63043e-14,0)" writing-mode="lr" x="351.24" xml:space="preserve" y="511.75" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135293857794" ObjectName=""/>
   </metadata>
  </g>
  <g id="545">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="545" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,351.5,534.125) scale(1,1) translate(3.63043e-14,0)" writing-mode="lr" x="351.24" xml:space="preserve" y="538.8200000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135293923330" ObjectName=""/>
   </metadata>
  </g>
  <g id="533">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="533" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,351.5,561.188) scale(1,1) translate(3.63043e-14,0)" writing-mode="lr" x="351.24" xml:space="preserve" y="565.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135293988866" ObjectName=""/>
   </metadata>
  </g>
  <g id="518">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="518" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,351.5,588.25) scale(1,1) translate(3.63043e-14,0)" writing-mode="lr" x="351.24" xml:space="preserve" y="592.9400000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135294316546" ObjectName=""/>
   </metadata>
  </g>
  <g id="563">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="563" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,570.692,15.3571) scale(1,1) translate(0,0)" writing-mode="lr" x="570.22" xml:space="preserve" y="20.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135275245570" ObjectName="P"/>
   </metadata>
  </g>
  <g id="566">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="566" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,683.67,15.3571) scale(1,1) translate(0,0)" writing-mode="lr" x="683.2" xml:space="preserve" y="20.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135276818434" ObjectName="P"/>
   </metadata>
  </g>
  <g id="576">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="576" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,786.373,15.3571) scale(1,1) translate(0,0)" writing-mode="lr" x="785.9" xml:space="preserve" y="20.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135278391298" ObjectName="P"/>
   </metadata>
  </g>
  <g id="581">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="581" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1083.67,15.3571) scale(1,1) translate(0,0)" writing-mode="lr" x="1083.2" xml:space="preserve" y="20.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135279964162" ObjectName="P"/>
   </metadata>
  </g>
  <g id="588">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="588" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1190.57,15.3571) scale(1,1) translate(0,0)" writing-mode="lr" x="1190.11" xml:space="preserve" y="20.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135281537026" ObjectName="P"/>
   </metadata>
  </g>
  <g id="641">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="641" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1307.67,15.3571) scale(1,1) translate(0,0)" writing-mode="lr" x="1307.2" xml:space="preserve" y="20.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135283109890" ObjectName="P"/>
   </metadata>
  </g>
  <g id="642">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="642" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,571.009,28.5794) scale(1,1) translate(0,0)" writing-mode="lr" x="570.54" xml:space="preserve" y="33.25" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135275311106" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="643">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="643" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,682.559,28.5794) scale(1,1) translate(0,0)" writing-mode="lr" x="682.09" xml:space="preserve" y="33.25" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135276883970" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="644">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="644" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,785.262,28.5794) scale(1,1) translate(0,0)" writing-mode="lr" x="784.79" xml:space="preserve" y="33.25" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135278456834" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="645">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="645" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1082.56,28.5794) scale(1,1) translate(1.15192e-13,0)" writing-mode="lr" x="1082.09" xml:space="preserve" y="33.25" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135280029698" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="646">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="646" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1189.46,28.5794) scale(1,1) translate(-2.54122e-13,0)" writing-mode="lr" x="1188.99" xml:space="preserve" y="33.25" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135281602562" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="647">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="647" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1306.56,28.5794) scale(1,1) translate(0,0)" writing-mode="lr" x="1306.09" xml:space="preserve" y="33.25" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135283175426" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="648">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="648" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,572.12,41.3571) scale(1,1) translate(0,0)" writing-mode="lr" x="571.65" xml:space="preserve" y="46.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135275376642" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="649">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="649" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,683.67,41.3571) scale(1,1) translate(0,0)" writing-mode="lr" x="683.2" xml:space="preserve" y="46.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135276949506" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="650">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="650" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,786.373,41.3571) scale(1,1) translate(0,0)" writing-mode="lr" x="785.9" xml:space="preserve" y="46.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135278522370" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="651">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="651" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1083.67,41.3571) scale(1,1) translate(0,0)" writing-mode="lr" x="1083.2" xml:space="preserve" y="46.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135280095234" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="652">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="652" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1190.57,41.3571) scale(1,1) translate(0,0)" writing-mode="lr" x="1190.11" xml:space="preserve" y="46.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135281668098" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="653">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="653" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1307.67,41.3571) scale(1,1) translate(0,0)" writing-mode="lr" x="1307.2" xml:space="preserve" y="46.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135283240962" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="654">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="654" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,561.634,1006.39) scale(1,1) translate(0,0)" writing-mode="lr" x="561.17" xml:space="preserve" y="1011.06" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135288221698" ObjectName="P"/>
   </metadata>
  </g>
  <g id="655">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="655" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,649.894,1003.39) scale(1,1) translate(0,0)" writing-mode="lr" x="649.4299999999999" xml:space="preserve" y="1008.06" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135289794562" ObjectName="P"/>
   </metadata>
  </g>
  <g id="656">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="656" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,561.634,1021.39) scale(1,1) translate(0,0)" writing-mode="lr" x="561.17" xml:space="preserve" y="1026.06" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135288287234" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="657">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="657" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,649.894,1018.39) scale(1,1) translate(0,0)" writing-mode="lr" x="649.4299999999999" xml:space="preserve" y="1023.06" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135289860098" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="658">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="658" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,560.205,1037.82) scale(1,1) translate(0,2.73733e-12)" writing-mode="lr" x="559.74" xml:space="preserve" y="1042.49" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135288352770" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="659">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="659" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,651.894,1034.39) scale(1,1) translate(0,0)" writing-mode="lr" x="651.4299999999999" xml:space="preserve" y="1039.06" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135289925634" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="660">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="660" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1871,119.173) scale(1,1) translate(0,0)" writing-mode="lr" x="1870.53" xml:space="preserve" y="123.84" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135294906370" ObjectName="P"/>
   </metadata>
  </g>
  <g id="661">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="661" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1873,219.173) scale(1,1) translate(0,0)" writing-mode="lr" x="1872.53" xml:space="preserve" y="223.84" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135295823874" ObjectName="P"/>
   </metadata>
  </g>
  <g id="662">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="662" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1873,307.923) scale(1,1) translate(0,0)" writing-mode="lr" x="1872.53" xml:space="preserve" y="312.59" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135297265666" ObjectName="P"/>
   </metadata>
  </g>
  <g id="663">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="663" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1875.75,658.923) scale(1,1) translate(0,0)" writing-mode="lr" x="1875.28" xml:space="preserve" y="663.59" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135298707458" ObjectName="P"/>
   </metadata>
  </g>
  <g id="664">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="664" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1871,138.173) scale(1,1) translate(0,0)" writing-mode="lr" x="1870.53" xml:space="preserve" y="142.84" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135294971906" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="665">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="665" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1873,238.173) scale(1,1) translate(0,0)" writing-mode="lr" x="1872.53" xml:space="preserve" y="242.84" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135295889410" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="666">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="666" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1873,326.923) scale(1,1) translate(0,0)" writing-mode="lr" x="1872.53" xml:space="preserve" y="331.59" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135297331202" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="667">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="667" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1875.75,677.923) scale(1,1) translate(0,0)" writing-mode="lr" x="1875.28" xml:space="preserve" y="682.59" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135298772994" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="668">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="668" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1871,157.173) scale(1,1) translate(0,0)" writing-mode="lr" x="1870.53" xml:space="preserve" y="161.84" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135295037442" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="669">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="669" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1873,257.173) scale(1,1) translate(0,0)" writing-mode="lr" x="1872.53" xml:space="preserve" y="261.84" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135295954946" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="670">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="670" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1873,345.923) scale(1,1) translate(0,0)" writing-mode="lr" x="1872.53" xml:space="preserve" y="350.59" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135297396738" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="671">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="671" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1875.75,696.923) scale(1,1) translate(0,0)" writing-mode="lr" x="1875.28" xml:space="preserve" y="701.59" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135298838530" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="672">
   <text Format="" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="672" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1864.59,804.232) scale(1,1) translate(0,0)" writing-mode="lr" x="1864.01" xml:space="preserve" y="810.4299999999999" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135300804610" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="673">
   <text Format="" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="673" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1864.59,766.232) scale(1,1) translate(0,0)" writing-mode="lr" x="1864.01" xml:space="preserve" y="772.4299999999999" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135301001218" ObjectName="P"/>
   </metadata>
  </g>
  <g id="676">
   <text Format="" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="676" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1864.59,785.232) scale(1,1) translate(0,0)" writing-mode="lr" x="1864.01" xml:space="preserve" y="791.4299999999999" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135301066754" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="677">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="677" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,609.483,459.292) scale(1,1) translate(0,0)" writing-mode="lr" x="609.01" xml:space="preserve" y="463.96" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135268036610" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="679">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="679" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,609.483,474.292) scale(1,1) translate(0,0)" writing-mode="lr" x="609.01" xml:space="preserve" y="478.96" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135268102146" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="684">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="684" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,609.483,489.292) scale(1,1) translate(0,0)" writing-mode="lr" x="609.01" xml:space="preserve" y="493.96" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135268167682" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="688">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="688" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,751.854,936.826) scale(1,1) translate(0,0)" writing-mode="lr" x="751.38" xml:space="preserve" y="941.49" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135268495362" ObjectName="MP"/>
   </metadata>
  </g>
  <g id="690">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="690" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,751.854,951.826) scale(1,1) translate(0,0)" writing-mode="lr" x="751.38" xml:space="preserve" y="956.49" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135268560898" ObjectName="MQ"/>
   </metadata>
  </g>
  <g id="691">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="691" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,751.854,966.826) scale(1,1) translate(0,0)" writing-mode="lr" x="751.38" xml:space="preserve" y="971.49" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135268823042" ObjectName="MIa"/>
   </metadata>
  </g>
  <g id="692">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="692" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1456.41,458.074) scale(1,1) translate(-9.40189e-13,0)" writing-mode="lr" x="1455.94" xml:space="preserve" y="462.74" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135268298754" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="694">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="694" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1456.41,428.074) scale(1,1) translate(-9.40189e-13,2.318e-13)" writing-mode="lr" x="1455.94" xml:space="preserve" y="432.74" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135268626434" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="695">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="695" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1456.41,443.074) scale(1,1) translate(-9.40189e-13,0)" writing-mode="lr" x="1455.94" xml:space="preserve" y="447.74" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135268691970" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="730">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="730" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,927.81,193.357) scale(1,1) translate(0,0)" writing-mode="lr" x="927.34" xml:space="preserve" y="198.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135273279490" ObjectName="P"/>
   </metadata>
  </g>
  <g id="731">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="731" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,927.81,208.357) scale(1,1) translate(0,0)" writing-mode="lr" x="927.34" xml:space="preserve" y="213.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135273345026" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="732">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="732" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,926.381,225.929) scale(1,1) translate(0,0)" writing-mode="lr" x="925.91" xml:space="preserve" y="230.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135273410562" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="733">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="733" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1578.71,558.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1578.25" xml:space="preserve" y="563.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135303163906" ObjectName="P"/>
   </metadata>
  </g>
  <g id="734">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="734" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1578.71,577.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1578.25" xml:space="preserve" y="582.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135303229442" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="735">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="735" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1576.43,599.071) scale(1,1) translate(0,0)" writing-mode="lr" x="1575.96" xml:space="preserve" y="603.74" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135303294978" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="736">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="736" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1033.07,807.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1032.6" xml:space="preserve" y="812.38" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135291891714" ObjectName="P"/>
   </metadata>
  </g>
  <g id="737">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="737" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1033.07,824.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1032.6" xml:space="preserve" y="828.8099999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135291957250" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="738">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="738" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1034.21,838.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1033.75" xml:space="preserve" y="843.24" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135292022786" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="210">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="210" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1157.38,453.278) scale(1,1) translate(0,0)" writing-mode="lr" x="1156.91" xml:space="preserve" y="457.94" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135304081410" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="222">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="222" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1157.38,468.278) scale(1,1) translate(0,0)" writing-mode="lr" x="1156.91" xml:space="preserve" y="472.94" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135304146946" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="401">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="401" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1157.38,483.278) scale(1,1) translate(0,0)" writing-mode="lr" x="1156.91" xml:space="preserve" y="487.94" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135304212482" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="408">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="408" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1447.88,562.06) scale(1,1) translate(0,0)" writing-mode="lr" x="1447.41" xml:space="preserve" y="566.73" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135304343554" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="486">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="486" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1207.53,931.419) scale(1,1) translate(3.87201e-13,0)" writing-mode="lr" x="1207.06" xml:space="preserve" y="936.09" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135304540162" ObjectName="MP"/>
   </metadata>
  </g>
  <g id="488">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="488" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1207.53,946.419) scale(1,1) translate(3.87201e-13,0)" writing-mode="lr" x="1207.06" xml:space="preserve" y="951.09" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135304605698" ObjectName="MQ"/>
   </metadata>
  </g>
  <g id="489">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="489" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1447.88,532.06) scale(1,1) translate(0,0)" writing-mode="lr" x="1447.41" xml:space="preserve" y="536.73" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135304671234" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="494">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="494" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1447.88,547.06) scale(1,1) translate(0,0)" writing-mode="lr" x="1447.41" xml:space="preserve" y="551.73" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135304736770" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="506">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="506" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1207.53,961.419) scale(1,1) translate(3.87201e-13,0)" writing-mode="lr" x="1207.06" xml:space="preserve" y="966.09" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135304867842" ObjectName="MIa"/>
   </metadata>
  </g>
  <g id="757">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="757" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,450.478,552.294) scale(1,1) translate(2.70103e-13,0)" writing-mode="lr" x="450.01" xml:space="preserve" y="556.96" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135285731330" ObjectName="P"/>
   </metadata>
  </g>
  <g id="758">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="758" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,450.478,569.294) scale(1,1) translate(2.70103e-13,0)" writing-mode="lr" x="450.01" xml:space="preserve" y="573.96" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135285796866" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="111" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,417.5,319) scale(1,1) translate(0,0)" writing-mode="lr" x="417.24" xml:space="preserve" y="323.69" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135271444482" ObjectName=""/>
   </metadata>
  </g>
  <g id="535">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="535" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,417.5,417) scale(1,1) translate(4.36318e-14,0)" writing-mode="lr" x="417.24" xml:space="preserve" y="421.69" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135270920194" ObjectName=""/>
   </metadata>
  </g>
  <g id="779">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="779" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,813.5,911) scale(1,1) translate(8.75966e-14,0)" writing-mode="lr" x="813.24" xml:space="preserve" y="915.6900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135284944898" ObjectName=""/>
   </metadata>
  </g>
  <g id="780">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="780" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1357.5,879) scale(1,1) translate(1.47993e-13,0)" writing-mode="lr" x="1357.24" xml:space="preserve" y="883.6900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135285469186" ObjectName=""/>
   </metadata>
  </g>
  <g id="788">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="788" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1760.5,437) scale(1,1) translate(1.92735e-13,0)" writing-mode="lr" x="1760.24" xml:space="preserve" y="441.69" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135293595650" ObjectName=""/>
   </metadata>
  </g>
  <g id="797">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="797" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1727.5,937) scale(1,1) translate(1.89071e-13,0)" writing-mode="lr" x="1727.24" xml:space="preserve" y="941.6900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135294119938" ObjectName=""/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="93">
   <use height="30" transform="rotate(0,329.768,341.341) scale(0.708333,0.665547) translate(131.412,166.516)" width="30" x="319.14" xlink:href="#State:红绿圆(方形)_0" y="331.36" zvalue="9611"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374925889537" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.768,341.341) scale(0.708333,0.665547) translate(131.412,166.516)" width="30" x="319.14" y="331.36"/></g>
  <g id="92">
   <use height="30" transform="rotate(0,234.143,341.341) scale(0.708333,0.665547) translate(92.0368,166.516)" width="30" x="223.52" xlink:href="#State:红绿圆(方形)_0" y="331.36" zvalue="9612"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562960719085570" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,234.143,341.341) scale(0.708333,0.665547) translate(92.0368,166.516)" width="30" x="223.52" y="331.36"/></g>
  <g id="364">
   <use height="30" transform="rotate(0,329.812,132.464) scale(1.22222,1.03092) translate(-49.9659,-3.50934)" width="90" x="274.81" xlink:href="#State:全站检修_0" y="117" zvalue="10403"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549682307073" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.812,132.464) scale(1.22222,1.03092) translate(-49.9659,-3.50934)" width="90" x="274.81" y="117"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="485">
   <use class="kv35" height="30" transform="rotate(0,470.478,620.794) scale(1.66667,-1.66667) translate(-181.525,-983.27)" width="20" x="453.8118157707979" xlink:href="#EnergyConsumer:站用变无融断_0" y="595.7936507936507" zvalue="9696"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454451462147" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,470.478,620.794) scale(1.66667,-1.66667) translate(-181.525,-983.27)" width="20" x="453.8118157707979" y="595.7936507936507"/></g>
  <g id="152">
   <use class="kv10" height="30" transform="rotate(270,1741,139.173) scale(2.33333,-2.33333) translate(-984.857,-178.818)" width="15" x="1723.5" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="104.1727509987763" zvalue="10016"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454455787523" ObjectName="10kV盏西乡专线"/>
   <cge:TPSR_Ref TObjectID="6192454455787523"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1741,139.173) scale(2.33333,-2.33333) translate(-984.857,-178.818)" width="15" x="1723.5" y="104.1727509987763"/></g>
  <g id="176">
   <use class="kv10" height="30" transform="rotate(270,1743,239.173) scale(2.33333,-2.33333) translate(-986,-321.675)" width="15" x="1725.5" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="204.1727509987763" zvalue="10036"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454455853059" ObjectName="10kV支那乡专线"/>
   <cge:TPSR_Ref TObjectID="6192454455853059"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1743,239.173) scale(2.33333,-2.33333) translate(-986,-321.675)" width="15" x="1725.5" y="204.1727509987763"/></g>
  <g id="195">
   <use class="kv10" height="30" transform="rotate(270,1743,327.923) scale(2.33333,-2.33333) translate(-986,-448.461)" width="15" x="1725.5" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="292.9227509987762" zvalue="10051"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454456180739" ObjectName="10kV东洪坝线"/>
   <cge:TPSR_Ref TObjectID="6192454456180739"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1743,327.923) scale(2.33333,-2.33333) translate(-986,-448.461)" width="15" x="1725.5" y="292.9227509987762"/></g>
  <g id="90">
   <use class="kv10" height="30" transform="rotate(270,1745.75,678.923) scale(2.33333,-2.33333) translate(-987.571,-949.89)" width="15" x="1728.25" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="643.9227509987761" zvalue="10081"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454456836099" ObjectName="10kV盏西街道线"/>
   <cge:TPSR_Ref TObjectID="6192454456836099"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1745.75,678.923) scale(2.33333,-2.33333) translate(-987.571,-949.89)" width="15" x="1728.25" y="643.9227509987761"/></g>
  <g id="490">
   <use class="kv10" height="30" transform="rotate(90,1657.12,863.988) scale(2.09365,-2.09365) translate(-854.685,-1260.25)" width="20" x="1636.18253968254" xlink:href="#EnergyConsumer:站用变无融断_0" y="832.583582122745" zvalue="10105"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454457556995" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1657.12,863.988) scale(2.09365,-2.09365) translate(-854.685,-1260.25)" width="20" x="1636.18253968254" y="832.583582122745"/></g>
 </g>
 <g id="CoilClass">
  <g id="487">
   <use class="kv10" height="30" transform="rotate(270,1788.38,785.518) scale(1.25,2.00833) translate(-355.175,-379.264)" width="20" x="1775.875" xlink:href="#Coil:消弧线圈带壁雷器_0" y="755.3930302303879" zvalue="10101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454457491459" ObjectName="10kV2号接地变及消弧线圈"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1788.38,785.518) scale(1.25,2.00833) translate(-355.175,-379.264)" width="20" x="1775.875" y="755.3930302303879"/></g>
 </g>
 <g id="GroundClass">
  <g id="532">
   <use class="kv35" height="18" transform="rotate(270,1420,669.286) scale(1.30952,1.42857) translate(-333.779,-196.929)" width="12" x="1412.142857142857" xlink:href="#Ground:大地_0" y="656.4285714285713" zvalue="10155"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454458408963" ObjectName="#2主变35kV侧接地"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(270,1420,669.286) scale(1.30952,1.42857) translate(-333.779,-196.929)" width="12" x="1412.142857142857" y="656.4285714285713"/></g>
 </g>
</svg>