<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1000" id="thSvg" source="NR-PCS9000" viewBox="0 0 1000 1000" width="1000">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.45181617405996" x2="1.714850492606708" y1="5.276370517142858" y2="14.05696281619048"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.583333333333333" x2="8.583333333333334" y1="5.166666666666667" y2="14.16666666666667"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="LY" InitShowingPlane="" fill="rgb(0,0,0)" height="1000" width="1000" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="PowerTransformer2Class">
  <g id="1">
   <g id="10">
    <use class="kv35" height="30" transform="rotate(0,541.059,502) scale(3.75,3.66667) translate(-363.776,-325.091)" width="24" x="496.06" xlink:href="#PowerTransformer2:可调两卷变_0" y="447" zvalue="1"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="35"/>
    </metadata>
   </g>
   <g id="11">
    <use class="kv10" height="30" transform="rotate(0,541.059,502) scale(3.75,3.66667) translate(-363.776,-325.091)" width="24" x="496.06" xlink:href="#PowerTransformer2:可调两卷变_1" y="447" zvalue="1"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,541.059,502) scale(3.75,3.66667) translate(-363.776,-325.091)" width="24" x="496.06" y="447"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="2">
   <path class="kv35" d="M 300 272 L 826 272" stroke-width="6" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kVI母"/>
   </metadata>
  <path d="M 300 272 L 826 272" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv10" d="M 300 715 L 804 715" stroke-width="6" zvalue="3"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kVI母"/>
   </metadata>
  <path d="M 300 715 L 804 715" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv10" d="M 300 905 L 790 905" stroke-width="5" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV旁母"/>
   </metadata>
  <path d="M 300 905 L 790 905" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,474.559,501) scale(1,1) translate(0,0)" writing-mode="lr" x="474.56" xml:space="preserve" y="505.5" zvalue="2">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,851.5,271) scale(1,1) translate(0,0)" writing-mode="lr" x="851.5" xml:space="preserve" y="275.5" zvalue="3">35kVI母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,829.5,714) scale(1,1) translate(0,0)" writing-mode="lr" x="829.5" xml:space="preserve" y="718.5" zvalue="4">10kVI母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,528,16.5) scale(1,1) translate(0,0)" writing-mode="lr" x="528" xml:space="preserve" y="21" zvalue="9">031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,557.5,175) scale(1,1) translate(0,0)" writing-mode="lr" x="557.5" xml:space="preserve" y="179.5" zvalue="11">031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,504.012,217) scale(1,1) translate(5.44578e-14,0)" writing-mode="lr" x="504.01" xml:space="preserve" y="221.5" zvalue="18">0311</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,490,175) scale(1,1) translate(0,0)" writing-mode="lr" x="490" xml:space="preserve" y="179.5" zvalue="22">03167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,491,229) scale(1,1) translate(0,0)" writing-mode="lr" x="491" xml:space="preserve" y="233.5" zvalue="26">03160</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,487,280.75) scale(1,1) translate(0,0)" writing-mode="lr" x="487" xml:space="preserve" y="285.25" zvalue="29">03117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,559.5,380) scale(1,1) translate(0,0)" writing-mode="lr" x="559.5" xml:space="preserve" y="384.5" zvalue="32">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,515.912,314) scale(1,1) translate(0,0)" writing-mode="lr" x="515.91" xml:space="preserve" y="318.5" zvalue="39">3011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,362.973,960) scale(1,1) translate(0,0)" writing-mode="lr" x="362.97" xml:space="preserve" y="964.5" zvalue="43">051负荷</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,561.164,647) scale(1,1) translate(0,0)" writing-mode="lr" x="561.16" xml:space="preserve" y="651.5" zvalue="45">050</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,850,909.5) scale(1,1) translate(0,0)" writing-mode="lr" x="850" xml:space="preserve" y="914" zvalue="48">10kV旁母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,383.164,770) scale(1,1) translate(0,0)" writing-mode="lr" x="383.16" xml:space="preserve" y="774.5" zvalue="50">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,338.412,828) scale(1,1) translate(0,0)" writing-mode="lr" x="338.41" xml:space="preserve" y="832.5" zvalue="54">0516</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,370.412,875) scale(1,1) translate(0,0)" writing-mode="lr" x="370.41" xml:space="preserve" y="879.5" zvalue="58">0511</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,495.973,960) scale(1,1) translate(0,0)" writing-mode="lr" x="495.97" xml:space="preserve" y="964.5" zvalue="62">052负荷</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,516.164,770) scale(1,1) translate(0,0)" writing-mode="lr" x="516.16" xml:space="preserve" y="774.5" zvalue="64">052</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,471.412,828) scale(1,1) translate(0,0)" writing-mode="lr" x="471.41" xml:space="preserve" y="832.5" zvalue="67">0526</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,503.412,875) scale(1,1) translate(0,0)" writing-mode="lr" x="503.41" xml:space="preserve" y="879.5" zvalue="71">0521</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,622.973,963) scale(1,1) translate(0,0)" writing-mode="lr" x="622.97" xml:space="preserve" y="967.5" zvalue="76">053负荷</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,643.164,773) scale(1,1) translate(0,0)" writing-mode="lr" x="643.16" xml:space="preserve" y="777.5" zvalue="78">053</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,598.412,831) scale(1,1) translate(0,0)" writing-mode="lr" x="598.41" xml:space="preserve" y="835.5" zvalue="81">0536</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,630.412,878) scale(1,1) translate(0,0)" writing-mode="lr" x="630.41" xml:space="preserve" y="882.5" zvalue="85">0531</text>
 </g>
 <g id="ACLineSegmentClass">
  <g id="8">
   <use class="kv35" height="30" transform="rotate(0,528,36) scale(1.85714,0.5) translate(-240.692,28.5)" width="7" x="521.5" xlink:href="#ACLineSegment:线路_0" y="28.5" zvalue="8"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="031"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,528,36) scale(1.85714,0.5) translate(-240.692,28.5)" width="7" x="521.5" y="28.5"/></g>
 </g>
 <g id="BreakerClass">
  <g id="10">
   <use class="kv35" height="20" transform="rotate(0,527,173) scale(1.5,1.35) translate(-173.167,-41.3519)" width="10" x="519.5" xlink:href="#Breaker:开关_0" y="159.5" zvalue="10"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="031"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,527,173) scale(1.5,1.35) translate(-173.167,-41.3519)" width="10" x="519.5" y="159.5"/></g>
  <g id="27">
   <use class="kv35" height="20" transform="rotate(0,540,381) scale(1.5,1.35) translate(-177.5,-95.2778)" width="10" x="532.5" xlink:href="#Breaker:开关_0" y="367.5" zvalue="31"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="301"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,540,381) scale(1.5,1.35) translate(-177.5,-95.2778)" width="10" x="532.5" y="367.5"/></g>
  <g id="38">
   <use class="kv10" height="20" transform="rotate(0,540.082,648) scale(1.51639,2.5) translate(-181.338,-373.8)" width="10" x="532.5" xlink:href="#Breaker:手车开关_0" y="623" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="050"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,540.082,648) scale(1.51639,2.5) translate(-181.338,-373.8)" width="10" x="532.5" y="623"/></g>
  <g id="42">
   <use class="kv10" height="20" transform="rotate(0,363.082,771) scale(1.51639,2.5) translate(-121.062,-447.6)" width="10" x="355.5000000000001" xlink:href="#Breaker:手车开关_0" y="746" zvalue="49"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="051"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,363.082,771) scale(1.51639,2.5) translate(-121.062,-447.6)" width="10" x="355.5000000000001" y="746"/></g>
  <g id="79">
   <use class="kv10" height="20" transform="rotate(0,496.082,771) scale(1.51639,2.5) translate(-166.354,-447.6)" width="10" x="488.5" xlink:href="#Breaker:手车开关_0" y="746" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="052"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,496.082,771) scale(1.51639,2.5) translate(-166.354,-447.6)" width="10" x="488.5" y="746"/></g>
  <g id="92">
   <use class="kv10" height="20" transform="rotate(0,623.082,774) scale(1.51639,2.5) translate(-209.603,-449.4)" width="10" x="615.5" xlink:href="#Breaker:手车开关_0" y="749" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="053"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,623.082,774) scale(1.51639,2.5) translate(-209.603,-449.4)" width="10" x="615.5" y="749"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="13">
   <use class="kv35" height="30" transform="rotate(0,527.912,121) scale(1,0.733333) translate(0,40)" width="15" x="520.4122229491392" xlink:href="#Disconnector:刀闸_0" y="110" zvalue="13"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,527.912,121) scale(1,0.733333) translate(0,40)" width="15" x="520.4122229491392" y="110"/></g>
  <g id="16">
   <use class="kv35" height="30" transform="rotate(0,527.012,218) scale(1,0.733333) translate(0,75.2727)" width="15" x="519.5122229491392" xlink:href="#Disconnector:刀闸_0" y="207" zvalue="17"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0311"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,527.012,218) scale(1,0.733333) translate(0,75.2727)" width="15" x="519.5122229491392" y="207"/></g>
  <g id="32">
   <use class="kv35" height="30" transform="rotate(0,538.912,315) scale(1,0.733333) translate(0,110.545)" width="15" x="531.4122229491393" xlink:href="#Disconnector:刀闸_0" y="304" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,538.912,315) scale(1,0.733333) translate(0,110.545)" width="15" x="531.4122229491393" y="304"/></g>
  <g id="45">
   <use class="kv10" height="30" transform="rotate(0,362.912,829) scale(1,0.733333) translate(0,297.455)" width="15" x="355.4122229491392" xlink:href="#Disconnector:刀闸_0" y="818" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0516"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,362.912,829) scale(1,0.733333) translate(0,297.455)" width="15" x="355.4122229491392" y="818"/></g>
  <g id="48">
   <use class="kv10" height="30" transform="rotate(0,393.912,876) scale(1,0.733333) translate(0,314.545)" width="15" x="386.4122229491392" xlink:href="#Disconnector:刀闸_0" y="865" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0511"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,393.912,876) scale(1,0.733333) translate(0,314.545)" width="15" x="386.4122229491392" y="865"/></g>
  <g id="77">
   <use class="kv10" height="30" transform="rotate(0,495.912,829) scale(1,0.733333) translate(0,297.455)" width="15" x="488.4122229491392" xlink:href="#Disconnector:刀闸_0" y="818" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0526"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,495.912,829) scale(1,0.733333) translate(0,297.455)" width="15" x="488.4122229491392" y="818"/></g>
  <g id="74">
   <use class="kv10" height="30" transform="rotate(0,526.912,876) scale(1,0.733333) translate(0,314.545)" width="15" x="519.4122229491393" xlink:href="#Disconnector:刀闸_0" y="865" zvalue="70"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0521"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,526.912,876) scale(1,0.733333) translate(0,314.545)" width="15" x="519.4122229491393" y="865"/></g>
  <g id="90">
   <use class="kv10" height="30" transform="rotate(0,622.912,832) scale(1,0.733333) translate(0,298.545)" width="15" x="615.4122229491392" xlink:href="#Disconnector:刀闸_0" y="821" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0536"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,622.912,832) scale(1,0.733333) translate(0,298.545)" width="15" x="615.4122229491392" y="821"/></g>
  <g id="87">
   <use class="kv10" height="30" transform="rotate(0,653.912,879) scale(1,0.733333) translate(0,315.636)" width="15" x="646.4122229491392" xlink:href="#Disconnector:刀闸_0" y="868" zvalue="84"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0531"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,653.912,879) scale(1,0.733333) translate(0,315.636)" width="15" x="646.4122229491392" y="868"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="14">
   <path class="kv35" d="M 528 43.43 L 528 110.36" stroke-width="1" zvalue="14"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8@0" LinkObjectIDznd="13@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 528 43.43 L 528 110.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv35" d="M 527.97 131.81 L 527.97 160.09" stroke-width="1" zvalue="15"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="13@1" LinkObjectIDznd="10@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 527.97 131.81 L 527.97 160.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv35" d="M 527.1 185.89 L 527.1 207.36" stroke-width="1" zvalue="18"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@1" LinkObjectIDznd="16@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 527.1 185.89 L 527.1 207.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv35" d="M 527.07 228.81 L 527.07 272" stroke-width="1" zvalue="19"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="16@1" LinkObjectIDznd="2@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 527.07 228.81 L 527.07 272" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv35" d="M 499.75 142.05 L 527.97 142.05" stroke-width="1" zvalue="22"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@0" LinkObjectIDznd="15" MaxPinNum="2"/>
   </metadata>
  <path d="M 499.75 142.05 L 527.97 142.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv35" d="M 584.63 83.05 L 528 83.05" stroke-width="1" zvalue="23"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="19@0" LinkObjectIDznd="14" MaxPinNum="2"/>
   </metadata>
  <path d="M 584.63 83.05 L 528 83.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv35" d="M 500.75 196.05 L 527.1 196.05" stroke-width="1" zvalue="26"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="23@0" LinkObjectIDznd="17" MaxPinNum="2"/>
   </metadata>
  <path d="M 500.75 196.05 L 527.1 196.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv35" d="M 496.75 250.03 L 527.07 250.03" stroke-width="1" zvalue="29"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="25@0" LinkObjectIDznd="18" MaxPinNum="2"/>
   </metadata>
  <path d="M 496.75 250.03 L 527.07 250.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv35" d="M 541.1 450.95 L 541.1 393.89" stroke-width="1" zvalue="32"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="27@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 541.1 450.95 L 541.1 393.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv35" d="M 497.75 349.05 L 539.95 349.05" stroke-width="1" zvalue="36"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="30@0" LinkObjectIDznd="33" MaxPinNum="2"/>
   </metadata>
  <path d="M 497.75 349.05 L 539.95 349.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv35" d="M 539.95 368.09 L 539.95 325.81" stroke-width="1" zvalue="39"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="27@0" LinkObjectIDznd="32@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 539.95 368.09 L 539.95 325.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv35" d="M 539 304.36 L 539 272" stroke-width="1" zvalue="40"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="2@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 539 304.36 L 539 272" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv10" d="M 541.06 553.5 L 541.06 624.88" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@1" LinkObjectIDznd="38@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 541.06 553.5 L 541.06 624.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv10" d="M 540.08 670.5 L 540.08 715" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@1" LinkObjectIDznd="3@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 540.08 670.5 L 540.08 715" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv10" d="M 363.08 747.88 L 363.08 715" stroke-width="1" zvalue="51"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@0" LinkObjectIDznd="3@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 363.08 747.88 L 363.08 715" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv10" d="M 362.97 913.35 L 362.97 839.81" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="45@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 362.97 913.35 L 362.97 839.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv10" d="M 363 818.36 L 363.08 793.5" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="42@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 363 818.36 L 363.08 793.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv10" d="M 393.97 886.81 L 393.97 905" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@1" LinkObjectIDznd="41@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 393.97 886.81 L 393.97 905" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv10" d="M 394 865.36 L 394 850 L 362.97 850" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@0" LinkObjectIDznd="46" MaxPinNum="2"/>
   </metadata>
  <path d="M 394 865.36 L 394 850 L 362.97 850" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv10" d="M 496.08 747.88 L 496.08 715" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="3@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 496.08 747.88 L 496.08 715" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv10" d="M 495.97 913.35 L 495.97 839.81" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="77@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 495.97 913.35 L 495.97 839.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv10" d="M 496 818.36 L 496.08 793.5" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@0" LinkObjectIDznd="79@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 496 818.36 L 496.08 793.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv10" d="M 526.97 886.81 L 526.97 905" stroke-width="1" zvalue="72"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@1" LinkObjectIDznd="41@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 526.97 886.81 L 526.97 905" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv10" d="M 527 865.36 L 527 850 L 495.97 850" stroke-width="1" zvalue="73"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="76" MaxPinNum="2"/>
   </metadata>
  <path d="M 527 865.36 L 527 850 L 495.97 850" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv10" d="M 623.08 750.88 L 623.08 715" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="3@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 623.08 750.88 L 623.08 715" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv10" d="M 622.97 916.35 L 622.97 842.81" stroke-width="1" zvalue="82"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@0" LinkObjectIDznd="90@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 622.97 916.35 L 622.97 842.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv10" d="M 623 821.36 L 623.08 796.5" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="92@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 623 821.36 L 623.08 796.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv10" d="M 653.97 889.81 L 653.97 905" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@1" LinkObjectIDznd="41@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 653.97 889.81 L 653.97 905" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv10" d="M 654 868.36 L 654 853 L 622.97 853" stroke-width="1" zvalue="87"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@0" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 654 868.36 L 654 853 L 622.97 853" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="19">
   <use class="kv35" height="26" transform="rotate(270,597,83.0833) scale(1,1) translate(0,0)" width="12" x="591" xlink:href="#Accessory:避雷器_0" y="70.08333333333343" zvalue="20"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,597,83.0833) scale(1,1) translate(0,0)" width="12" x="591" y="70.08333333333343"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="20">
   <use class="kv35" height="20" transform="rotate(90,490,142) scale(1,1) translate(0,0)" width="10" x="485" xlink:href="#GroundDisconnector:地刀_0" y="132" zvalue="21"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="03167"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,490,142) scale(1,1) translate(0,0)" width="10" x="485" y="132"/></g>
  <g id="23">
   <use class="kv35" height="20" transform="rotate(90,491,196) scale(1,1) translate(0,0)" width="10" x="486" xlink:href="#GroundDisconnector:地刀_0" y="186" zvalue="25"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="03160"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,491,196) scale(1,1) translate(0,0)" width="10" x="486" y="186"/></g>
  <g id="25">
   <use class="kv35" height="20" transform="rotate(90,487,250) scale(0.55,1) translate(396.205,0)" width="10" x="484.25" xlink:href="#GroundDisconnector:地刀_0" y="240" zvalue="28"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="03117"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,487,250) scale(0.55,1) translate(396.205,0)" width="10" x="484.25" y="240"/></g>
  <g id="30">
   <use class="kv35" height="20" transform="rotate(90,488,349) scale(1,1) translate(0,0)" width="10" x="483" xlink:href="#GroundDisconnector:地刀_0" y="339" zvalue="35"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,488,349) scale(1,1) translate(0,0)" width="10" x="483" y="339"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="36">
   <use class="kv10" height="30" transform="rotate(180,362.973,930) scale(1.25,1.23333) translate(-71.0947,-172.446)" width="12" x="355.473387373245" xlink:href="#EnergyConsumer:负荷_0" y="911.5" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="051负荷"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,362.973,930) scale(1.25,1.23333) translate(-71.0947,-172.446)" width="12" x="355.473387373245" y="911.5"/></g>
  <g id="80">
   <use class="kv10" height="30" transform="rotate(180,495.973,930) scale(1.25,1.23333) translate(-97.6947,-172.446)" width="12" x="488.473387373245" xlink:href="#EnergyConsumer:负荷_0" y="911.5" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="052负荷"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,495.973,930) scale(1.25,1.23333) translate(-97.6947,-172.446)" width="12" x="488.473387373245" y="911.5"/></g>
  <g id="93">
   <use class="kv10" height="30" transform="rotate(180,622.973,933) scale(1.25,1.23333) translate(-123.095,-173.014)" width="12" x="615.473387373245" xlink:href="#EnergyConsumer:负荷_0" y="914.5" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="053负荷"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,622.973,933) scale(1.25,1.23333) translate(-123.095,-173.014)" width="12" x="615.473387373245" y="914.5"/></g>
 </g>
</svg>