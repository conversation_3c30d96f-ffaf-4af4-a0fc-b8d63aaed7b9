<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549592850434" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_0" viewBox="0,0,30,50">
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01666666666667" x2="15.01666666666667" y1="37.50000000000001" y2="32.91666666666667"/>
   <use terminal-index="1" type="1" x="15.00325153685922" xlink:href="#terminal" y="49.86055225321343"/>
   <use terminal-index="2" type="2" x="15" xlink:href="#terminal" y="37.75085860895189"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.25" x2="15" y1="43.08333333333334" y2="37.75000000000001"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15" x2="20" y1="37.74761215261902" y2="42.91666666666667"/>
   <ellipse cx="15" cy="35.25" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_1" viewBox="0,0,30,50">
   <path d="M 10 16 L 20.1667 16 L 15 7.83333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="0" type="1" x="15.00731595793324" xlink:href="#terminal" y="0.3902606310013752"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV芒法电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="43.5" xlink:href="logo.png" y="45"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,178.125,75) scale(1,1) translate(0,0)" writing-mode="lr" x="178.13" xml:space="preserve" y="78.5" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,179.833,74.6903) scale(1,1) translate(6.82787e-15,0)" writing-mode="lr" x="179.83" xml:space="preserve" y="83.69" zvalue="3">10kV芒法电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="28" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,84,316) scale(1,1) translate(0,0)" width="97" x="35.5" y="304" zvalue="9"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84,316) scale(1,1) translate(0,0)" writing-mode="lr" x="84" xml:space="preserve" y="320.5" zvalue="9">全站公用</text>
  <line fill="none" id="33" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.5" x2="377.5" y1="13" y2="1043" zvalue="4"/>
  <line fill="none" id="31" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.500000000000227" x2="370.4999999999998" y1="148.8704926140824" y2="148.8704926140824" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="161" y2="161"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="187" y2="187"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="4.5" y1="161" y2="187"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="161" y2="187"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="161" y2="161"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="187" y2="187"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="161" y2="187"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.5" x2="366.5" y1="161" y2="187"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="187" y2="187"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="211.25" y2="211.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="4.5" y1="187" y2="211.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="187" y2="211.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="187" y2="187"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="211.25" y2="211.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="187" y2="211.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.5" x2="366.5" y1="187" y2="211.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="211.25" y2="211.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="234" y2="234"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="4.5" y1="211.25" y2="234"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="211.25" y2="234"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="211.25" y2="211.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="234" y2="234"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="211.25" y2="234"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.5" x2="366.5" y1="211.25" y2="234"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="234" y2="234"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="256.75" y2="256.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="4.5" y1="234" y2="256.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="234" y2="256.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="234" y2="234"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="256.75" y2="256.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="234" y2="256.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.5" x2="366.5" y1="234" y2="256.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="256.75" y2="256.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="279.5" y2="279.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="4.5" y1="256.75" y2="279.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="256.75" y2="279.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="256.75" y2="256.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="279.5" y2="279.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="256.75" y2="279.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.5" x2="366.5" y1="256.75" y2="279.5"/>
  <line fill="none" id="29" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.500000000000227" x2="370.4999999999998" y1="618.8704926140824" y2="618.8704926140824" zvalue="8"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="441.6666435058594" y2="441.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="479.1566435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="56.72236173734677" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0670617373469" x2="105.0670617373469" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="441.6666435058594" y2="441.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="479.1566435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="105.0673617373468" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1756617373469" x2="167.1756617373469" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="441.6666435058594" y2="441.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="479.1566435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="167.1751617373468" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4999617373469" x2="230.4999617373469" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="441.6666435058594" y2="441.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="479.1566435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="230.4998617373468" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="441.6666435058594" y2="441.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="479.1566435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.7164617373469" x2="354.7164617373469" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="479.1567435058594" y2="479.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="56.72236173734677" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0670617373469" x2="105.0670617373469" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="479.1567435058594" y2="479.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="105.0673617373468" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1756617373469" x2="167.1756617373469" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="479.1567435058594" y2="479.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="167.1751617373468" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4999617373469" x2="230.4999617373469" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="479.1567435058594" y2="479.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="230.4998617373468" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="479.1567435058594" y2="479.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.7164617373469" x2="354.7164617373469" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="527.4939435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="56.72236173734677" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0670617373469" x2="105.0670617373469" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="527.4939435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="105.0673617373468" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1756617373469" x2="167.1756617373469" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="527.4939435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="167.1751617373468" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4999617373469" x2="230.4999617373469" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="527.4939435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="230.4998617373468" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="527.4939435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.7164617373469" x2="354.7164617373469" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="527.4939835058593" y2="527.4939835058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="551.6625835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="56.72236173734677" y1="527.4939835058593" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0670617373469" x2="105.0670617373469" y1="527.4939835058593" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="527.4939835058593" y2="527.4939835058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="551.6625835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="105.0673617373468" y1="527.4939835058593" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1756617373469" x2="167.1756617373469" y1="527.4939835058593" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="527.4939835058593" y2="527.4939835058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="551.6625835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="167.1751617373468" y1="527.4939835058593" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4999617373469" x2="230.4999617373469" y1="527.4939835058593" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="527.4939835058593" y2="527.4939835058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="551.6625835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="230.4998617373468" y1="527.4939835058593" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="527.4939835058593" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="527.4939835058593" y2="527.4939835058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="551.6625835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="527.4939835058593" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.7164617373469" x2="354.7164617373469" y1="527.4939835058593" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="551.6627435058593" y2="551.6627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="56.72236173734677" y1="551.6627435058593" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0670617373469" x2="105.0670617373469" y1="551.6627435058593" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="551.6627435058593" y2="551.6627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="105.0673617373468" y1="551.6627435058593" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1756617373469" x2="167.1756617373469" y1="551.6627435058593" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="551.6627435058593" y2="551.6627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="167.1751617373468" y1="551.6627435058593" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4999617373469" x2="230.4999617373469" y1="551.6627435058593" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="551.6627435058593" y2="551.6627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="230.4998617373468" y1="551.6627435058593" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="551.6627435058593" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="551.6627435058593" y2="551.6627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="551.6627435058593" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.7164617373469" x2="354.7164617373469" y1="551.6627435058593" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="599.9999435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="56.72236173734677" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0670617373469" x2="105.0670617373469" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="599.9999435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="105.0673617373468" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1756617373469" x2="167.1756617373469" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="599.9999435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="167.1751617373468" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4999617373469" x2="230.4999617373469" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="599.9999435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="230.4998617373468" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="599.9999435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.7164617373469" x2="354.7164617373469" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="93.5" y1="934.0000000000002" y2="934.0000000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="93.5" y1="973.1633000000002" y2="973.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="3.5" y1="934.0000000000002" y2="973.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="93.5" y1="934.0000000000002" y2="973.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="363.5" y1="934.0000000000002" y2="934.0000000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="363.5" y1="973.1633000000002" y2="973.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="93.5" y1="934.0000000000002" y2="973.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.5" x2="363.5" y1="934.0000000000002" y2="973.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="93.5" y1="973.1632700000002" y2="973.1632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="93.5" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="3.5" y1="973.1632700000002" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="93.5" y1="973.1632700000002" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="183.5" y1="973.1632700000002" y2="973.1632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="183.5" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="93.5" y1="973.1632700000002" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5" x2="183.5" y1="973.1632700000002" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5000000000001" x2="273.5000000000001" y1="973.1632700000002" y2="973.1632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5000000000001" x2="273.5000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5000000000001" x2="183.5000000000001" y1="973.1632700000002" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5000000000001" x2="273.5000000000001" y1="973.1632700000002" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5" x2="363.5" y1="973.1632700000002" y2="973.1632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5" x2="363.5" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5" x2="273.5" y1="973.1632700000002" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.5" x2="363.5" y1="973.1632700000002" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="93.5" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="93.5" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="3.5" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="93.5" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="183.5" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="183.5" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="93.5" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5" x2="183.5" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5000000000001" x2="273.5000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5000000000001" x2="273.5000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5000000000001" x2="183.5000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5000000000001" x2="273.5000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5" x2="363.5" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5" x2="363.5" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5" x2="273.5" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.5" x2="363.5" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.5,954) scale(1,1) translate(0,0)" writing-mode="lr" x="48.5" xml:space="preserve" y="960" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.5,988) scale(1,1) translate(0,0)" writing-mode="lr" x="45.5" xml:space="preserve" y="994" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.5,988) scale(1,1) translate(0,0)" writing-mode="lr" x="227.5" xml:space="preserve" y="994" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.5,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="44.5" xml:space="preserve" y="1022" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.5,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="226.5" xml:space="preserve" y="1022" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69,648.5) scale(1,1) translate(0,0)" writing-mode="lr" x="69" xml:space="preserve" y="653" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.899,315.841) scale(1,1) translate(0,0)" writing-mode="lr" x="201.9" xml:space="preserve" y="320.34" zvalue="19">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,306.899,315.841) scale(1,1) translate(0,0)" writing-mode="lr" x="306.9" xml:space="preserve" y="320.34" zvalue="20">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.5,491.5) scale(1,1) translate(0,0)" writing-mode="lr" x="80.5" xml:space="preserve" y="496" zvalue="21">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.5,517) scale(1,1) translate(0,0)" writing-mode="lr" x="80.5" xml:space="preserve" y="521.5" zvalue="22">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.5,542.5) scale(1,1) translate(0,0)" writing-mode="lr" x="80.5" xml:space="preserve" y="547" zvalue="23">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.5,567) scale(1,1) translate(0,0)" writing-mode="lr" x="79.5" xml:space="preserve" y="571.5" zvalue="24">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.5,593.5) scale(1,1) translate(0,0)" writing-mode="lr" x="80.5" xml:space="preserve" y="598" zvalue="25">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.554,956) scale(1,1) translate(0,0)" writing-mode="lr" x="228.55" xml:space="preserve" y="962" zvalue="26">DHDDBH-MangFa-01-2024</text>
  
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,318.554,988) scale(1,1) translate(0,0)" writing-mode="lr" x="318.55" xml:space="preserve" y="994" zvalue="28">20240614</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,42.5,175) scale(1,1) translate(0,0)" writing-mode="lr" x="42.5" xml:space="preserve" y="180.5" zvalue="29">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.5,175) scale(1,1) translate(0,0)" writing-mode="lr" x="222.5" xml:space="preserve" y="180.5" zvalue="30">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.6875,247) scale(1,1) translate(0,0)" writing-mode="lr" x="49.69" xml:space="preserve" y="251.5" zvalue="31">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.583,201.694) scale(1,1) translate(0,0)" writing-mode="lr" x="234.58" xml:space="preserve" y="206.19" zvalue="32">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" x="255.875" xml:space="preserve" y="459.75" zvalue="33">0.4kV  母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="255.875" xml:space="preserve" y="475.75" zvalue="33">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.1429,202.5) scale(1,1) translate(0,0)" writing-mode="lr" x="55.14" xml:space="preserve" y="207" zvalue="34">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.071,248.214) scale(1,1) translate(0,0)" writing-mode="lr" x="234.07" xml:space="preserve" y="252.71" zvalue="35">10kV#2变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" x="133.796875" xml:space="preserve" y="459.296875" zvalue="36">10kV  母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="133.796875" xml:space="preserve" y="475.296875" zvalue="36">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1803.88,445.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1803.88" xml:space="preserve" y="450.25" zvalue="38">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,819.639,1016.75) scale(1,1) translate(0,0)" writing-mode="lr" x="819.64" xml:space="preserve" y="1021.25" zvalue="39">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,761.75,704.812) scale(1,1) translate(0,0)" writing-mode="lr" x="761.75" xml:space="preserve" y="709.3099999999999" zvalue="40">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,782.375,575.75) scale(1,1) translate(0,0)" writing-mode="lr" x="782.38" xml:space="preserve" y="580.25" zvalue="43">0021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,797.375,833.25) scale(1,1) translate(0,0)" writing-mode="lr" x="797.38" xml:space="preserve" y="837.75" zvalue="47">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,837.764,887) scale(1,1) translate(0,0)" writing-mode="lr" x="837.76" xml:space="preserve" y="891.5" zvalue="50">402</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,698.75,33.75) scale(1,1) translate(0,0)" writing-mode="lr" x="698.75" xml:space="preserve" y="38.25" zvalue="53">10kV河茂线T红茂支线#93</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1523.64,1016.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1523.64" xml:space="preserve" y="1021.25" zvalue="57">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1465.75,704.812) scale(1,1) translate(0,0)" writing-mode="lr" x="1465.75" xml:space="preserve" y="709.3099999999999" zvalue="58">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1486.38,575.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1486.38" xml:space="preserve" y="580.25" zvalue="61">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1501.38,833.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1501.38" xml:space="preserve" y="837.75" zvalue="64">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1541.76,887) scale(1,1) translate(0,0)" writing-mode="lr" x="1541.76" xml:space="preserve" y="891.5" zvalue="67">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,763,729.5) scale(1,1) translate(0,0)" writing-mode="lr" x="763" xml:space="preserve" y="734" zvalue="78">250kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1465,727.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1465" xml:space="preserve" y="732" zvalue="79">320kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,818.5,1036) scale(1,1) translate(0,0)" writing-mode="lr" x="818.5" xml:space="preserve" y="1040.5" zvalue="80">200kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1524.5,1035) scale(1,1) translate(0,0)" writing-mode="lr" x="1524.5" xml:space="preserve" y="1039.5" zvalue="82">200kW</text>
 </g>
 <g id="ButtonClass">
  <g href="500kV德宏变_全站公用.svg"><rect fill-opacity="0" height="24" width="97" x="35.5" y="304" zvalue="9"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="36">
   <path class="kv10" d="M 515 446.75 L 1766.25 446.75" stroke-width="6" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674254192644" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674254192644"/></metadata>
  <path d="M 515 446.75 L 1766.25 446.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="37">
   <use class="v400" height="30" transform="rotate(0,813.389,973.5) scale(1.875,1.875) translate(-366.457,-441.175)" width="30" x="785.2639555301323" xlink:href="#Generator:发电机_0" y="945.375" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450021818373" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192450021818373"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,813.389,973.5) scale(1.875,1.875) translate(-366.457,-441.175)" width="30" x="785.2639555301323" y="945.375"/></g>
  <g id="72">
   <use class="v400" height="30" transform="rotate(0,1517.39,973.5) scale(1.875,1.875) translate(-694.99,-441.175)" width="30" x="1489.263955530132" xlink:href="#Generator:发电机_0" y="945.375" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450022211589" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192450022211589"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1517.39,973.5) scale(1.875,1.875) translate(-694.99,-441.175)" width="30" x="1489.263955530132" y="945.375"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="38">
   <g id="380">
    <use class="v400" height="50" transform="rotate(0,812.688,705.812) scale(1.6375,1.6375) translate(-306.827,-258.844)" width="30" x="788.13" xlink:href="#PowerTransformer2:D-Y_0" y="664.88" zvalue="39"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874455384067" ObjectName="0.4"/>
    </metadata>
   </g>
   <g id="381">
    <use class="kv10" height="50" transform="rotate(0,812.688,705.812) scale(1.6375,1.6375) translate(-306.827,-258.844)" width="30" x="788.13" xlink:href="#PowerTransformer2:D-Y_1" y="664.88" zvalue="39"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874455318531" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399459536899" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399459536899"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,812.688,705.812) scale(1.6375,1.6375) translate(-306.827,-258.844)" width="30" x="788.13" y="664.88"/></g>
  <g id="71">
   <g id="710">
    <use class="v400" height="50" transform="rotate(0,1516.69,705.812) scale(1.6375,1.6375) translate(-580.904,-258.844)" width="30" x="1492.13" xlink:href="#PowerTransformer2:D-Y_0" y="664.88" zvalue="56"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874455515139" ObjectName="0.4"/>
    </metadata>
   </g>
   <g id="711">
    <use class="kv10" height="50" transform="rotate(0,1516.69,705.812) scale(1.6375,1.6375) translate(-580.904,-258.844)" width="30" x="1492.13" xlink:href="#PowerTransformer2:D-Y_1" y="664.88" zvalue="56"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874455449603" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399459602435" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399459602435"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1516.69,705.812) scale(1.6375,1.6375) translate(-580.904,-258.844)" width="30" x="1492.13" y="664.88"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="41">
   <use class="kv10" height="30" transform="rotate(0,811.25,576.75) scale(1.25,0.916667) translate(-160.375,51.1818)" width="15" x="801.875" xlink:href="#Disconnector:刀闸_0" y="563" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450021883909" ObjectName="#2主变0021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450021883909"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,811.25,576.75) scale(1.25,0.916667) translate(-160.375,51.1818)" width="15" x="801.875" y="563"/></g>
  <g id="44">
   <use class="v400" height="30" transform="rotate(0,813.125,834.25) scale(1.25,0.916667) translate(-160.75,74.5909)" width="15" x="803.75" xlink:href="#Disconnector:刀闸_0" y="820.5" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450021949445" ObjectName="#2主变4026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450021949445"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,813.125,834.25) scale(1.25,0.916667) translate(-160.75,74.5909)" width="15" x="803.75" y="820.5"/></g>
  <g id="70">
   <use class="kv10" height="30" transform="rotate(0,1515.25,576.75) scale(1.25,0.916667) translate(-301.175,51.1818)" width="15" x="1505.875" xlink:href="#Disconnector:刀闸_0" y="563" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450022146053" ObjectName="#1主变0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450022146053"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1515.25,576.75) scale(1.25,0.916667) translate(-301.175,51.1818)" width="15" x="1505.875" y="563"/></g>
  <g id="67">
   <use class="v400" height="30" transform="rotate(0,1517.12,834.25) scale(1.25,0.916667) translate(-301.55,74.5909)" width="15" x="1507.75" xlink:href="#Disconnector:刀闸_0" y="820.5" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450022080517" ObjectName="#1主变4016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450022080517"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1517.12,834.25) scale(1.25,0.916667) translate(-301.55,74.5909)" width="15" x="1507.75" y="820.5"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="42">
   <path class="kv10" d="M 812.7 665.51 L 812.7 590.26" stroke-width="1" zvalue="43"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="41@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 812.7 665.51 L 812.7 590.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv10" d="M 811.36 563.45 L 811.36 446.75" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 811.36 563.45 L 811.36 446.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="v400" d="M 813.23 820.95 L 813.23 746.52" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="38@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 813.23 820.95 L 813.23 746.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="v400" d="M 813.39 945.84 L 813.39 904.12" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="47@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 813.39 945.84 L 813.39 904.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="v400" d="M 813.2 871.86 L 813.2 847.76" stroke-width="1" zvalue="51"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="44@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 813.2 871.86 L 813.2 847.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv10" d="M 692.5 97.14 L 692.5 446.75" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 692.5 97.14 L 692.5 446.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv10" d="M 1516.7 665.51 L 1516.7 590.26" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="70@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1516.7 665.51 L 1516.7 590.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv10" d="M 1515.36 563.45 L 1515.36 446.75" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="36@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1515.36 563.45 L 1515.36 446.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="v400" d="M 1517.23 820.95 L 1517.23 746.52" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="71@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.23 820.95 L 1517.23 746.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="v400" d="M 1517.39 945.84 L 1517.39 904.12" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@0" LinkObjectIDznd="65@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.39 945.84 L 1517.39 904.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="v400" d="M 1517.2 871.86 L 1517.2 847.76" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="67@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.2 871.86 L 1517.2 847.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="47">
   <use class="v400" height="20" transform="rotate(0,813.264,888) scale(1.875,1.6875) translate(-375.148,-354.903)" width="10" x="803.8889555301323" xlink:href="#Breaker:开关_0" y="871.125" zvalue="49"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924558061573" ObjectName="#2主变402断路器"/>
   <cge:TPSR_Ref TObjectID="6473924558061573"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,813.264,888) scale(1.875,1.6875) translate(-375.148,-354.903)" width="10" x="803.8889555301323" y="871.125"/></g>
  <g id="65">
   <use class="v400" height="20" transform="rotate(0,1517.26,888) scale(1.875,1.6875) translate(-703.682,-354.903)" width="10" x="1507.888955530132" xlink:href="#Breaker:开关_0" y="871.125" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924558127109" ObjectName="#1主变401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924558127109"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1517.26,888) scale(1.875,1.6875) translate(-703.682,-354.903)" width="10" x="1507.888955530132" y="871.125"/></g>
 </g>
</svg>