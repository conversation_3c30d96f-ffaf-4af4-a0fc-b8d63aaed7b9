<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549595406338" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Breaker:手车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.45181617405996" x2="1.714850492606708" y1="5.276370517142858" y2="14.05696281619048"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.583333333333333" x2="8.583333333333334" y1="5.166666666666667" y2="14.16666666666667"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:母联开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill="rgb(170,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.333333333333333" x2="9.75" y1="0.4166666666666696" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.666666666666668" x2="0.4166666666666661" y1="0.4999999999999982" y2="19.66666666666667"/>
   <rect fill-opacity="0" height="19.58" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.04,10.04) scale(1,1) translate(0,0)" width="9.58" x="0.25" y="0.25"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Accessory:10kV避雷器PT_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="4"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.75" x2="8.75" y1="18" y2="14"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75" x2="22.75" y1="23" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.75" x2="20.75" y1="11" y2="21"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.66666666666667" x2="23.75" y1="21" y2="21"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.75" x2="21.75" y1="25" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.75" x2="21.5" y1="11" y2="13.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.75" x2="20.75" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.75" x2="8.75" y1="9" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.75" x2="20.75" y1="9" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="4" y2="5"/>
   <rect fill="rgb(0,0,0)" fill-opacity="1" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,8.83,11.58) scale(1,1) translate(0,0)" width="3" x="7.33" y="9.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.79263465688835" x2="8.79263465688835" y1="19.45930132355502" y2="20.45930132355502"/>
   <ellipse cx="8.789999999999999" cy="20.46" fill-opacity="0" rx="2.29" ry="2.29" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20.75,11.58) scale(1,1) translate(0,0)" width="2.33" x="19.58" y="9.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.79263465688835" x2="9.79263465688835" y1="20.45930132355502" y2="19.45930132355502"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.79263465688835" x2="8.79263465688835" y1="20.45930132355502" y2="21.45930132355502"/>
   <ellipse cx="8.81" cy="25.04" fill-opacity="0" rx="2.29" ry="2.29" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="11.21" cy="22.96" fill-opacity="0" rx="2.29" ry="2.29" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="6.54" cy="22.79" fill-opacity="0" rx="2.29" ry="2.29" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.29263465688835" x2="11.29263465688835" y1="23.00930132355501" y2="24.00930132355501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.29263465688835" x2="11.29263465688835" y1="22.00930132355501" y2="23.00930132355501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.29263465688835" x2="12.29263465688835" y1="23.00930132355501" y2="22.00930132355501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.29263465688835" x2="7.29263465688835" y1="23.00930132355501" y2="22.00930132355501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.29263465688835" x2="6.29263465688835" y1="23.00930132355501" y2="24.00930132355501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.29263465688835" x2="6.29263465688835" y1="22.00930132355501" y2="23.00930132355501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.79263465688835" x2="8.79263465688835" y1="25.50930132355501" y2="26.50930132355501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.79263465688835" x2="9.79263465688835" y1="25.50930132355501" y2="24.50930132355501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.79263465688835" x2="8.79263465688835" y1="24.50930132355501" y2="25.50930132355501"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸1_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="35.06751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="9" y1="9" y2="9"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.084670781893005" x2="1.626337448559672" y1="4.853614126578682" y2="9.663958954164888"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.084670781893004" x2="12.54300411522634" y1="4.853614126578682" y2="9.663958954164888"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.27315435646374" y2="26.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="31.27315435646374" y2="26.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="7.00133744855967" y1="4.916666666666663" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="1.833333333333333" x2="7.001337448559672" y1="10.75" y2="27"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="7.00133744855967" x2="7.00133744855967" y1="26.75" y2="31.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸1_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="35.06751120764343"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="7.00133744855967" y1="0.7499999999999964" y2="35.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="9" y1="9" y2="9"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸1_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="35.06751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="2.083333333333333" y1="11.16666666666666" y2="25.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.133446357018218" x2="11.79254048508705" y1="11.12564285751284" y2="24.83570582669768"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.27315435646374" y2="27.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.27315435646374" y2="27.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="35.07918883922238" y2="30.26884401163617"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.998992968246015" x2="4.998992968246015" y1="14.41666666666666" y2="17.66666666666666"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="1.08333333333333" y2="5.749999999999999"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="6" y2="14"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="14" y2="6"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷带壁雷器_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="28.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11.75" y1="17.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="14.75" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="12" y1="11.75" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="9" y1="12.75" y2="12.75"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.04,18.25) scale(1,1) translate(0,0)" width="3.25" x="9.42" y="14.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.5" y1="10.83333333333333" y2="10.83333333333333"/>
   <path d="M 5.025 2.775 L 4.16667 9.5 L 5.025 7.60833 L 5.91667 9.5 L 5.025 2.775" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 5 23.75 L 11 23.75 L 11 17.75 L 10.25 20.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="7.627914951989029" y2="28.23902606310013"/>
  </symbol>
  <symbol id="Compensator:电容器0922_0" viewBox="0,0,30,60">
   <use terminal-index="0" type="0" x="18.20000012207032" xlink:href="#terminal" y="0.4999988746642643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.599999679565418" x2="6.599999679565418" y1="37.20000027465822" y2="50.40000077819828"/>
   <path d="M 18.4 54.9 L 18.4 58.5 L 1.6 58.5 L 1.6 6" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.499999485015849" x2="18.30000012588501" y1="5.99999908447262" y2="5.99999908447262"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.7000004463196" x2="27.90000049209597" y1="38.70000033187868" y2="35.10000019454957"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.50000040054323" x2="27.90000049209597" y1="46.70000063705447" y2="46.70000063705447"/>
   <rect fill-opacity="0" height="10.6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,26.7,37.7) scale(1,1) translate(0,0)" width="4.8" x="24.3" y="32.4"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.10000042343141" x2="27.30000046920778" y1="47.90000068283084" y2="47.90000068283084"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.7000004463196" x2="26.7000004463196" y1="45.5000005912781" y2="43.10000049972537"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.90000037765504" x2="28.50000051498415" y1="45.5000005912781" y2="45.5000005912781"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.7000004463196" x2="25.50000040054323" y1="38.70000033187868" y2="35.10000019454957"/>
   <rect fill-opacity="0" height="4.8" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,18.4,38.8) scale(1,1) translate(0,0)" width="2.4" x="17.2" y="36.4"/>
   <path d="M 18.3 28.5 L 26.7 28.5 L 26.7 38.7" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.43000013084412" x2="18.43000013084412" y1="48.13000069160465" y2="52.35222307489187"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.73000048561097" x2="8.749086898476051" y1="55.23000096244816" y2="55.23000096244816"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.849086902290754" x2="8.849086902290754" y1="55.33000096626286" y2="53.70000090408329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.18555541004604" x2="11.18555541004604" y1="49.66333408343001" y2="52.35222307489187"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.22999985618591" x2="11.22999985618591" y1="36.73000025672914" y2="34.08555571140714"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.83000048942568" x2="27.83000048942568" y1="53.60000090026859" y2="55.33000096626286"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.43000013084412" x2="18.43000013084412" y1="52.43000085563664" y2="55.40000096893315"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.30000012588501" x2="11.1999998550415" y1="52.33000085182194" y2="52.33000085182194"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.43000013084412" x2="18.43000013084412" y1="25.79999983978271" y2="46.93000064582828"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.1999998550415" x2="18.43000013084412" y1="34.03000015373231" y2="34.03000015373231"/>
   <path d="M 11.23 41.0486 A 3.5567 2.18096 -180 0 1 11.23 36.6867" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 11.23 45.4105 A 3.5567 2.18096 -180 0 1 11.23 41.0486" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 11.23 49.6525 A 3.5567 2.18096 -180 0 1 11.23 45.2905" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.23000019950868" x2="16.63000006217957" y1="46.93000064582827" y2="46.93000064582827"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.23000019950868" x2="16.63000006217957" y1="48.13000069160464" y2="48.13000069160464"/>
   <path d="M 27 17.69 A 8.21 8.52716 -270 1 0 18.4728 25.9" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 26.9457 17.6988 L 18.3649 17.6988 L 18.3649 0.499999" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:带电显示/避雷器_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10.08333333333333" xlink:href="#terminal" y="1.416666666666664"/>
   <rect fill-opacity="0" height="9.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,10.46) scale(1,1) translate(0,0)" width="4" x="13" y="5.92"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.08333333333334" x2="15.08333333333334" y1="1.416666666666668" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.833333333333336" x2="15.16666666666667" y1="1.5" y2="1.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.08333333333333" x2="14.08333333333333" y1="11.33333333333333" y2="9.333333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.08333333333334" x2="16.08333333333334" y1="11.41666666666667" y2="9.333333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.333333333333334" x2="6.333333333333335" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.333333333333334" x2="6.333333333333335" y1="6.5" y2="6.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.833333333333334" x2="4.833333333333335" y1="19.5" y2="19.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.833333333333335" x2="3.833333333333335" y1="1.5" y2="6.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.333333333333334" x2="5.333333333333335" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.833333333333334" x2="5.833333333333335" y1="17.5" y2="17.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.833333333333335" x2="3.833333333333335" y1="17.5" y2="15.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.833333333333335" x2="3.833333333333335" y1="8.5" y2="10.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.41666666666667" x2="5.41666666666667" y1="11.08333333333333" y2="14.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.16666666666667" x2="2.500000000000002" y1="11.08333333333333" y2="14.5"/>
   <ellipse cx="3.83" cy="12.75" fill-opacity="0" rx="2.24" ry="2.24" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="16.5" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="17.5" y2="15.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="17" y1="17.5" y2="17.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="16" y1="19.5" y2="19.5"/>
  </symbol>
  <symbol id="Accessory:中间电缆_0" viewBox="0,0,8,21">
   <use terminal-index="0" type="0" x="4" xlink:href="#terminal" y="10.5"/>
   <path d="M 1.08333 0.5 L 7 0.5 L 4 7.13889 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4" x2="4" y1="20.83333333333333" y2="0.5000000000000036"/>
   <path d="M 1.08333 20.6389 L 7 20.6389 L 4 14 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:电缆1_0" viewBox="0,0,12,7">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.25"/>
   <path d="M 1.16667 0.25 L 10.8333 0.25 L 6 6.88889 L 1.16667 0.25 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.750000000000004" y2="4.166666666666668"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="32"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="1" y1="27" y2="11"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="27" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Accessory:接地变20201012_0" viewBox="0,0,23,35">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.05" x2="21.05" y1="24.5" y2="24.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="20.5" y1="17.5" y2="17.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.5" x2="21.5" y1="23.5" y2="23.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5" x2="20.5" y1="17.5" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="16.5" y1="17.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="7.5" y1="25.5" y2="29.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.2" x2="7.2" y1="24.25" y2="32.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.5" x2="22.5" y1="22.5" y2="22.5"/>
   <path d="M 6.25 29.75 L 8.25 29.75 L 7.25 31.75 L 6.25 29.75 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="7.07" cy="6.66" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.061718118722352" x2="7.061718118722352" y1="3.664465958746224" y2="6.263454058763156"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.655265608193801" x2="7.061718118722348" y1="8.862442158780071" y2="6.263454058763138"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.468170629250878" x2="7.061718118722331" y1="8.862442158780071" y2="6.263454058763138"/>
   <ellipse cx="7.07" cy="17.66" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.061718118722352" x2="7.061718118722352" y1="14.66446595874622" y2="17.26345405876316"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.655265608193801" x2="7.061718118722348" y1="19.86244215878007" y2="17.26345405876314"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.468170629250878" x2="7.061718118722331" y1="19.86244215878007" y2="17.26345405876314"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Accessory:回贤变PT_0" viewBox="0,0,12,32">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.6666666666666661"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,8) scale(1,1) translate(0,0)" width="4" x="4" y="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="13.66666666666667" y2="1"/>
   <ellipse cx="5.9" cy="18.6" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.9" cy="26.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:回贤变站用变_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="9" y1="24" y2="25.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="25.75" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.16666666666667" x2="11.25" y1="24" y2="25.83333333333333"/>
   <rect fill-opacity="0" height="4.32" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.06,5.25) scale(1,1) translate(0,0)" width="3.43" x="8.34" y="3.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0235180220435" x2="10.0235180220435" y1="0.5833333333333091" y2="9.290410445610181"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="29.16666666666667" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="10.8868007916835" y2="12.55855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="14.23031840279781" y2="12.55855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV回贤变" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="57" xlink:href="logo.png" y="21"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,185,51) scale(1,1) translate(0,0)" writing-mode="lr" x="185" xml:space="preserve" y="54.5" zvalue="267"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,185.5,50.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="185.5" xml:space="preserve" y="59.69" zvalue="268">35kV回贤变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="11" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,78.625,283.75) scale(1,1) translate(0,0)" width="72.88" x="42.19" y="271.75" zvalue="297"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.625,283.75) scale(1,1) translate(0,0)" writing-mode="lr" x="78.63" xml:space="preserve" y="288.25" zvalue="297">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,730.625,305) scale(1,1) translate(0,0)" writing-mode="lr" x="730.63" xml:space="preserve" y="309.5" zvalue="2">35kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1388.38,317) scale(1,1) translate(0,0)" writing-mode="lr" x="1388.38" xml:space="preserve" y="321.5" zvalue="3">35kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,673.625,712) scale(1,1) translate(0,0)" writing-mode="lr" x="673.63" xml:space="preserve" y="716.5" zvalue="4">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1388.51,708.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1388.51" xml:space="preserve" y="712.75" zvalue="5">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" x="858.0625" xml:space="preserve" y="510" zvalue="7">2号主变   </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="858.0625" xml:space="preserve" y="526" zvalue="7">10000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,806.542,676.833) scale(1,1) translate(0,0)" writing-mode="lr" x="806.54" xml:space="preserve" y="681.33" zvalue="16">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,800.273,438.273) scale(1,1) translate(0,0)" writing-mode="lr" x="800.27" xml:space="preserve" y="442.77" zvalue="21">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,803.091,363.727) scale(1,1) translate(0,0)" writing-mode="lr" x="803.09" xml:space="preserve" y="368.23" zvalue="24">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,741.636,411) scale(1,1) translate(0,0)" writing-mode="lr" x="741.64" xml:space="preserve" y="415.5" zvalue="27">27</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" x="982.296875" xml:space="preserve" y="421.9147735942494" zvalue="29">312     35kV</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="982.296875" xml:space="preserve" y="437.9147735942494" zvalue="29">分段</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1031,367) scale(1,1) translate(0,0)" writing-mode="lr" x="1031" xml:space="preserve" y="371.5" zvalue="32">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,883,365) scale(1,1) translate(0,0)" writing-mode="lr" x="883" xml:space="preserve" y="369.5" zvalue="35">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,881.667,420.333) scale(1,1) translate(0,0)" writing-mode="lr" x="881.67" xml:space="preserve" y="424.83" zvalue="40">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1030.69,417.213) scale(1,1) translate(0,0)" writing-mode="lr" x="1030.69" xml:space="preserve" y="421.71" zvalue="42">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" x="1182.0625" xml:space="preserve" y="513.34375" zvalue="47">1号主变   </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1182.0625" xml:space="preserve" y="529.34375" zvalue="47">10000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1260.77,677.667) scale(1,1) translate(4.14779e-13,0)" writing-mode="lr" x="1260.77" xml:space="preserve" y="682.17" zvalue="51">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1249.27,438.273) scale(1,1) translate(0,0)" writing-mode="lr" x="1249.27" xml:space="preserve" y="442.77" zvalue="57">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1252.09,363.727) scale(1,1) translate(0,0)" writing-mode="lr" x="1252.09" xml:space="preserve" y="368.23" zvalue="61">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1190.64,411) scale(1,1) translate(0,0)" writing-mode="lr" x="1190.64" xml:space="preserve" y="415.5" zvalue="65">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,571.091,466.636) scale(1,1) translate(0,0)" writing-mode="lr" x="571.09" xml:space="preserve" y="471.14" zvalue="71">35kVⅡ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,551.422,377.578) scale(1,1) translate(0,0)" writing-mode="lr" x="551.42" xml:space="preserve" y="382.08" zvalue="73">3902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,643.333,352.667) scale(1,1) translate(0,0)" writing-mode="lr" x="643.33" xml:space="preserve" y="357.17" zvalue="76">39020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,643.833,403.167) scale(1,1) translate(0,3.50534e-13)" writing-mode="lr" x="643.83" xml:space="preserve" y="407.67" zvalue="78">39027</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1544.09,466.636) scale(1,1) translate(0,0)" writing-mode="lr" x="1544.09" xml:space="preserve" y="471.14" zvalue="82">35kVⅠ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1524.42,377.578) scale(1,1) translate(0,0)" writing-mode="lr" x="1524.42" xml:space="preserve" y="382.08" zvalue="84">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1616.33,352.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1616.33" xml:space="preserve" y="357.17" zvalue="88">39010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1616.83,403.167) scale(1,1) translate(0,3.50534e-13)" writing-mode="lr" x="1616.83" xml:space="preserve" y="407.67" zvalue="91">39017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1323.5,233) scale(1,1) translate(0,0)" writing-mode="lr" x="1323.5" xml:space="preserve" y="237.5" zvalue="94">351</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1299,35) scale(1,1) translate(0,0)" writing-mode="lr" x="1299" xml:space="preserve" y="39.5" zvalue="95">35kV弄回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1284.11,294.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1284.11" xml:space="preserve" y="298.61" zvalue="99">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1282,168) scale(1,1) translate(0,0)" writing-mode="lr" x="1282" xml:space="preserve" y="172.5" zvalue="102">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1264,274) scale(1,1) translate(0,0)" writing-mode="lr" x="1264" xml:space="preserve" y="278.5" zvalue="105">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1228,144) scale(1,1) translate(0,0)" writing-mode="lr" x="1228" xml:space="preserve" y="148.5" zvalue="108">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1261,219) scale(1,1) translate(0,0)" writing-mode="lr" x="1261" xml:space="preserve" y="223.5" zvalue="110">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1329,121.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1329" xml:space="preserve" y="126" zvalue="116">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1513.09,286) scale(1,1) translate(0,0)" writing-mode="lr" x="1513.09" xml:space="preserve" y="290.5" zvalue="123">3531</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1578.43,278.429) scale(1,1) translate(0,0)" writing-mode="lr" x="1578.43" xml:space="preserve" y="282.93" zvalue="128">35317</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,571.444,569.333) scale(1,1) translate(0,0)" writing-mode="lr" x="571.4400000000001" xml:space="preserve" y="573.83" zvalue="131">10kVⅡ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,600.544,684.294) scale(1,1) translate(0,0)" writing-mode="lr" x="600.54" xml:space="preserve" y="688.79" zvalue="136">0902</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1552.44,569.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1552.44" xml:space="preserve" y="573.83" zvalue="141">10kVⅠ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1586.49,680.658) scale(1,1) translate(2.61336e-12,0)" writing-mode="lr" x="1586.49" xml:space="preserve" y="685.16" zvalue="143">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" x="955.8984375" xml:space="preserve" y="835.335227619518" zvalue="147">012       </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="955.8984375" xml:space="preserve" y="851.335227619518" zvalue="147">10kV分段</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,761.75,788.045) scale(1,1) translate(0,0)" writing-mode="lr" x="761.75" xml:space="preserve" y="792.55" zvalue="155">054</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,761.955,889.545) scale(1,1) translate(0,0)" writing-mode="lr" x="761.95" xml:space="preserve" y="894.05" zvalue="157">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,734.136,848.242) scale(1,1) translate(0,0)" writing-mode="lr" x="734.14" xml:space="preserve" y="852.74" zvalue="161">27</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,780.412,977.676) scale(1,1) translate(0,0)" writing-mode="lr" x="780.41" xml:space="preserve" y="982.1799999999999" zvalue="166">10kV芒弄隧道线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,510.295,788.045) scale(1,1) translate(0,0)" writing-mode="lr" x="510.3" xml:space="preserve" y="792.55" zvalue="170">056</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,510.955,885.545) scale(1,1) translate(0,0)" writing-mode="lr" x="510.95" xml:space="preserve" y="890.05" zvalue="174">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="192" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,478.636,852.485) scale(1,1) translate(0,0)" writing-mode="lr" x="478.64" xml:space="preserve" y="856.98" zvalue="179">27</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,529.857,977.5) scale(1,1) translate(0,0)" writing-mode="lr" x="529.86" xml:space="preserve" y="982" zvalue="185">10kV2号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,630.5,783.955) scale(1,1) translate(0,-1.37748e-12)" writing-mode="lr" x="630.5" xml:space="preserve" y="788.45" zvalue="187">055</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="215" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1106.75,788.045) scale(1,1) translate(0,0)" writing-mode="lr" x="1106.75" xml:space="preserve" y="792.55" zvalue="191">053</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="214" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1098.95,885.545) scale(1,1) translate(0,0)" writing-mode="lr" x="1098.95" xml:space="preserve" y="890.05" zvalue="195">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="213" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1079.14,848.242) scale(1,1) translate(0,0)" writing-mode="lr" x="1079.14" xml:space="preserve" y="852.74" zvalue="200">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="212" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1125.41,977.676) scale(1,1) translate(0,0)" writing-mode="lr" x="1125.41" xml:space="preserve" y="982.1799999999999" zvalue="203">10kV老寨河大桥线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1451.96,789.157) scale(1,1) translate(0,0)" writing-mode="lr" x="1451.96" xml:space="preserve" y="793.66" zvalue="206">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1444.62,886.657) scale(1,1) translate(0,0)" writing-mode="lr" x="1444.62" xml:space="preserve" y="891.16" zvalue="210">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="227" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1420.3,853.596) scale(1,1) translate(0,0)" writing-mode="lr" x="1420.3" xml:space="preserve" y="858.1" zvalue="215">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1471.52,978.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1471.52" xml:space="preserve" y="983.11" zvalue="218">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1014.79,771.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1014.79" xml:space="preserve" y="776.33" zvalue="240">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="270" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,662.568,969.691) scale(1,1) translate(0,0)" writing-mode="lr" x="662.5700000000001" xml:space="preserve" y="974.1900000000001" zvalue="250">10kV2号接地变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,608.583,919.363) scale(1,1) translate(0,0)" writing-mode="lr" x="608.58" xml:space="preserve" y="923.86" zvalue="253">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="272" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1270.84,785.066) scale(1,1) translate(0,0)" writing-mode="lr" x="1270.84" xml:space="preserve" y="789.5700000000001" zvalue="258">052</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="271" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1305.46,967.802) scale(1,1) translate(0,0)" writing-mode="lr" x="1305.46" xml:space="preserve" y="972.3" zvalue="262">10kV1号接地变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1243.14,912.141) scale(1,1) translate(0,0)" writing-mode="lr" x="1243.14" xml:space="preserve" y="916.64" zvalue="265">67</text>
  <line fill="none" id="166" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="391" x2="391" y1="13" y2="1043" zvalue="269"/>
  <line fill="none" id="152" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00000000000045" x2="384" y1="124.8704926140824" y2="124.8704926140824" zvalue="271"/>
  <line fill="none" id="151" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00000000000045" x2="384" y1="594.8704926140824" y2="594.8704926140824" zvalue="272"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="107" y1="910" y2="910"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="107" y1="949.1632999999999" y2="949.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="17" y1="910" y2="949.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="107" y1="910" y2="949.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="377" y1="910" y2="910"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="377" y1="949.1632999999999" y2="949.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="107" y1="910" y2="949.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="377" x2="377" y1="910" y2="949.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="107" y1="949.16327" y2="949.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="107" y1="977.08167" y2="977.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="17" y1="949.16327" y2="977.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="107" y1="949.16327" y2="977.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="197" y1="949.16327" y2="949.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="197" y1="977.08167" y2="977.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="107" y1="949.16327" y2="977.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197" x2="197" y1="949.16327" y2="977.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.0000000000001" x2="287.0000000000001" y1="949.16327" y2="949.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.0000000000001" x2="287.0000000000001" y1="977.08167" y2="977.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.0000000000001" x2="197.0000000000001" y1="949.16327" y2="977.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287.0000000000001" x2="287.0000000000001" y1="949.16327" y2="977.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287" x2="377" y1="949.16327" y2="949.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287" x2="377" y1="977.08167" y2="977.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287" x2="287" y1="949.16327" y2="977.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="377" x2="377" y1="949.16327" y2="977.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="107" y1="977.0816" y2="977.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="107" y1="1005" y2="1005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17" x2="17" y1="977.0816" y2="1005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="107" y1="977.0816" y2="1005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="197" y1="977.0816" y2="977.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="197" y1="1005" y2="1005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107" x2="107" y1="977.0816" y2="1005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197" x2="197" y1="977.0816" y2="1005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.0000000000001" x2="287.0000000000001" y1="977.0816" y2="977.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.0000000000001" x2="287.0000000000001" y1="1005" y2="1005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.0000000000001" x2="197.0000000000001" y1="977.0816" y2="1005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287.0000000000001" x2="287.0000000000001" y1="977.0816" y2="1005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287" x2="377" y1="977.0816" y2="977.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287" x2="377" y1="1005" y2="1005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287" x2="287" y1="977.0816" y2="1005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="377" x2="377" y1="977.0816" y2="1005"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62,930) scale(1,1) translate(0,0)" writing-mode="lr" x="62" xml:space="preserve" y="936" zvalue="274">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59,964) scale(1,1) translate(0,0)" writing-mode="lr" x="59" xml:space="preserve" y="970" zvalue="275">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241,964) scale(1,1) translate(0,0)" writing-mode="lr" x="241" xml:space="preserve" y="970" zvalue="276">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58,992) scale(1,1) translate(0,0)" writing-mode="lr" x="58" xml:space="preserve" y="998" zvalue="277">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240,992) scale(1,1) translate(0,0)" writing-mode="lr" x="240" xml:space="preserve" y="998" zvalue="278">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.5,624.5) scale(1,1) translate(0,0)" writing-mode="lr" x="82.5" xml:space="preserve" y="629" zvalue="280">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,242.054,932) scale(1,1) translate(0,0)" writing-mode="lr" x="242.05" xml:space="preserve" y="938" zvalue="281">HuiXian-01-2022</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,152.054,964) scale(1,1) translate(0,0)" writing-mode="lr" x="152.05" xml:space="preserve" y="970" zvalue="282">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,332.054,964) scale(1,1) translate(0,0)" writing-mode="lr" x="332.05" xml:space="preserve" y="970" zvalue="283">20220516</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.67857142857133" x2="191.6785714285713" y1="128" y2="128"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.67857142857133" x2="191.6785714285713" y1="154" y2="154"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.67857142857133" x2="10.67857142857133" y1="128" y2="154"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="191.6785714285713" y1="128" y2="154"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="372.6785714285713" y1="128" y2="128"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="372.6785714285713" y1="154" y2="154"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="191.6785714285713" y1="128" y2="154"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.6785714285713" x2="372.6785714285713" y1="128" y2="154"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.67857142857133" x2="191.6785714285713" y1="154" y2="154"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.67857142857133" x2="191.6785714285713" y1="177.5" y2="177.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.67857142857133" x2="10.67857142857133" y1="154" y2="177.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="191.6785714285713" y1="154" y2="177.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="372.6785714285713" y1="154" y2="154"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="372.6785714285713" y1="177.5" y2="177.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="191.6785714285713" y1="154" y2="177.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.6785714285713" x2="372.6785714285713" y1="154" y2="177.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.67857142857133" x2="191.6785714285713" y1="177.5" y2="177.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.67857142857133" x2="191.6785714285713" y1="200.25" y2="200.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.67857142857133" x2="10.67857142857133" y1="177.5" y2="200.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="191.6785714285713" y1="177.5" y2="200.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="372.6785714285713" y1="177.5" y2="177.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="372.6785714285713" y1="200.25" y2="200.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="191.6785714285713" y1="177.5" y2="200.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.6785714285713" x2="372.6785714285713" y1="177.5" y2="200.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.67857142857133" x2="191.6785714285713" y1="200.25" y2="200.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.67857142857133" x2="191.6785714285713" y1="223" y2="223"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.67857142857133" x2="10.67857142857133" y1="200.25" y2="223"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="191.6785714285713" y1="200.25" y2="223"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="372.6785714285713" y1="200.25" y2="200.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="372.6785714285713" y1="223" y2="223"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="191.6785714285713" y1="200.25" y2="223"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.6785714285713" x2="372.6785714285713" y1="200.25" y2="223"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.67857142857133" x2="191.6785714285713" y1="223" y2="223"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.67857142857133" x2="191.6785714285713" y1="245.75" y2="245.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.67857142857133" x2="10.67857142857133" y1="223" y2="245.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="191.6785714285713" y1="223" y2="245.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="372.6785714285713" y1="223" y2="223"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="372.6785714285713" y1="245.75" y2="245.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.6785714285713" x2="191.6785714285713" y1="223" y2="245.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.6785714285713" x2="372.6785714285713" y1="223" y2="245.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.4009331659181" x2="93.74563316591821" y1="408.9166435058594" y2="408.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.4009331659181" x2="93.74563316591821" y1="446.4066435058594" y2="446.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.4009331659181" x2="45.4009331659181" y1="408.9166435058594" y2="446.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74563316591821" x2="93.74563316591821" y1="408.9166435058594" y2="446.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74593316591813" x2="155.8542331659182" y1="408.9166435058594" y2="408.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74593316591813" x2="155.8542331659182" y1="446.4066435058594" y2="446.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74593316591813" x2="93.74593316591813" y1="408.9166435058594" y2="446.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8542331659182" x2="155.8542331659182" y1="408.9166435058594" y2="446.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8537331659181" x2="219.1785331659182" y1="408.9166435058594" y2="408.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8537331659181" x2="219.1785331659182" y1="446.4066435058594" y2="446.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8537331659181" x2="155.8537331659181" y1="408.9166435058594" y2="446.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1785331659182" x2="219.1785331659182" y1="408.9166435058594" y2="446.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1784331659181" x2="281.2867331659181" y1="408.9166435058594" y2="408.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1784331659181" x2="281.2867331659181" y1="446.4066435058594" y2="446.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1784331659181" x2="219.1784331659181" y1="408.9166435058594" y2="446.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="281.2867331659181" y1="408.9166435058594" y2="446.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="343.3950331659182" y1="408.9166435058594" y2="408.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="343.3950331659182" y1="446.4066435058594" y2="446.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="281.2867331659181" y1="408.9166435058594" y2="446.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="343.3950331659182" x2="343.3950331659182" y1="408.9166435058594" y2="446.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.4009331659181" x2="93.74563316591821" y1="446.4067435058594" y2="446.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.4009331659181" x2="93.74563316591821" y1="470.5753435058595" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.4009331659181" x2="45.4009331659181" y1="446.4067435058594" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74563316591821" x2="93.74563316591821" y1="446.4067435058594" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74593316591813" x2="155.8542331659182" y1="446.4067435058594" y2="446.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74593316591813" x2="155.8542331659182" y1="470.5753435058595" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74593316591813" x2="93.74593316591813" y1="446.4067435058594" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8542331659182" x2="155.8542331659182" y1="446.4067435058594" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8537331659181" x2="219.1785331659182" y1="446.4067435058594" y2="446.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8537331659181" x2="219.1785331659182" y1="470.5753435058595" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8537331659181" x2="155.8537331659181" y1="446.4067435058594" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1785331659182" x2="219.1785331659182" y1="446.4067435058594" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1784331659181" x2="281.2867331659181" y1="446.4067435058594" y2="446.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1784331659181" x2="281.2867331659181" y1="470.5753435058595" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1784331659181" x2="219.1784331659181" y1="446.4067435058594" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="281.2867331659181" y1="446.4067435058594" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="343.3950331659182" y1="446.4067435058594" y2="446.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="343.3950331659182" y1="470.5753435058595" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="281.2867331659181" y1="446.4067435058594" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="343.3950331659182" x2="343.3950331659182" y1="446.4067435058594" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.4009331659181" x2="93.74563316591821" y1="470.5753435058595" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.4009331659181" x2="93.74563316591821" y1="494.7439435058594" y2="494.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.4009331659181" x2="45.4009331659181" y1="470.5753435058595" y2="494.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74563316591821" x2="93.74563316591821" y1="470.5753435058595" y2="494.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74593316591813" x2="155.8542331659182" y1="470.5753435058595" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74593316591813" x2="155.8542331659182" y1="494.7439435058594" y2="494.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74593316591813" x2="93.74593316591813" y1="470.5753435058595" y2="494.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8542331659182" x2="155.8542331659182" y1="470.5753435058595" y2="494.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8537331659181" x2="219.1785331659182" y1="470.5753435058595" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8537331659181" x2="219.1785331659182" y1="494.7439435058594" y2="494.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8537331659181" x2="155.8537331659181" y1="470.5753435058595" y2="494.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1785331659182" x2="219.1785331659182" y1="470.5753435058595" y2="494.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1784331659181" x2="281.2867331659181" y1="470.5753435058595" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1784331659181" x2="281.2867331659181" y1="494.7439435058594" y2="494.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1784331659181" x2="219.1784331659181" y1="470.5753435058595" y2="494.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="281.2867331659181" y1="470.5753435058595" y2="494.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="343.3950331659182" y1="470.5753435058595" y2="470.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="343.3950331659182" y1="494.7439435058594" y2="494.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="281.2867331659181" y1="470.5753435058595" y2="494.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="343.3950331659182" x2="343.3950331659182" y1="470.5753435058595" y2="494.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.4009331659181" x2="93.74563316591821" y1="494.7439835058594" y2="494.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.4009331659181" x2="93.74563316591821" y1="518.9125835058594" y2="518.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.4009331659181" x2="45.4009331659181" y1="494.7439835058594" y2="518.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74563316591821" x2="93.74563316591821" y1="494.7439835058594" y2="518.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74593316591813" x2="155.8542331659182" y1="494.7439835058594" y2="494.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74593316591813" x2="155.8542331659182" y1="518.9125835058594" y2="518.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74593316591813" x2="93.74593316591813" y1="494.7439835058594" y2="518.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8542331659182" x2="155.8542331659182" y1="494.7439835058594" y2="518.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8537331659181" x2="219.1785331659182" y1="494.7439835058594" y2="494.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8537331659181" x2="219.1785331659182" y1="518.9125835058594" y2="518.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8537331659181" x2="155.8537331659181" y1="494.7439835058594" y2="518.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1785331659182" x2="219.1785331659182" y1="494.7439835058594" y2="518.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1784331659181" x2="281.2867331659181" y1="494.7439835058594" y2="494.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1784331659181" x2="281.2867331659181" y1="518.9125835058594" y2="518.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1784331659181" x2="219.1784331659181" y1="494.7439835058594" y2="518.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="281.2867331659181" y1="494.7439835058594" y2="518.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="343.3950331659182" y1="494.7439835058594" y2="494.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="343.3950331659182" y1="518.9125835058594" y2="518.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="281.2867331659181" y1="494.7439835058594" y2="518.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="343.3950331659182" x2="343.3950331659182" y1="494.7439835058594" y2="518.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.4009331659181" x2="93.74563316591821" y1="518.9127435058595" y2="518.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.4009331659181" x2="93.74563316591821" y1="543.0813435058594" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.4009331659181" x2="45.4009331659181" y1="518.9127435058595" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74563316591821" x2="93.74563316591821" y1="518.9127435058595" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74593316591813" x2="155.8542331659182" y1="518.9127435058595" y2="518.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74593316591813" x2="155.8542331659182" y1="543.0813435058594" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74593316591813" x2="93.74593316591813" y1="518.9127435058595" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8542331659182" x2="155.8542331659182" y1="518.9127435058595" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8537331659181" x2="219.1785331659182" y1="518.9127435058595" y2="518.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8537331659181" x2="219.1785331659182" y1="543.0813435058594" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8537331659181" x2="155.8537331659181" y1="518.9127435058595" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1785331659182" x2="219.1785331659182" y1="518.9127435058595" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1784331659181" x2="281.2867331659181" y1="518.9127435058595" y2="518.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1784331659181" x2="281.2867331659181" y1="543.0813435058594" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1784331659181" x2="219.1784331659181" y1="518.9127435058595" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="281.2867331659181" y1="518.9127435058595" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="343.3950331659182" y1="518.9127435058595" y2="518.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="343.3950331659182" y1="543.0813435058594" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="281.2867331659181" y1="518.9127435058595" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="343.3950331659182" x2="343.3950331659182" y1="518.9127435058595" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.4009331659181" x2="93.74563316591821" y1="543.0813435058594" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.4009331659181" x2="93.74563316591821" y1="567.2499435058594" y2="567.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.4009331659181" x2="45.4009331659181" y1="543.0813435058594" y2="567.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74563316591821" x2="93.74563316591821" y1="543.0813435058594" y2="567.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74593316591813" x2="155.8542331659182" y1="543.0813435058594" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74593316591813" x2="155.8542331659182" y1="567.2499435058594" y2="567.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.74593316591813" x2="93.74593316591813" y1="543.0813435058594" y2="567.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8542331659182" x2="155.8542331659182" y1="543.0813435058594" y2="567.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8537331659181" x2="219.1785331659182" y1="543.0813435058594" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8537331659181" x2="219.1785331659182" y1="567.2499435058594" y2="567.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="155.8537331659181" x2="155.8537331659181" y1="543.0813435058594" y2="567.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1785331659182" x2="219.1785331659182" y1="543.0813435058594" y2="567.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1784331659181" x2="281.2867331659181" y1="543.0813435058594" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1784331659181" x2="281.2867331659181" y1="567.2499435058594" y2="567.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1784331659181" x2="219.1784331659181" y1="543.0813435058594" y2="567.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="281.2867331659181" y1="543.0813435058594" y2="567.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="343.3950331659182" y1="543.0813435058594" y2="543.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="343.3950331659182" y1="567.2499435058594" y2="567.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2867331659181" x2="281.2867331659181" y1="543.0813435058594" y2="567.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="343.3950331659182" x2="343.3950331659182" y1="543.0813435058594" y2="567.2499435058594"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,166.827,281.841) scale(1,1) translate(0,3.61499e-13)" writing-mode="lr" x="166.83" xml:space="preserve" y="286.34" zvalue="286">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,271.827,281.841) scale(1,1) translate(0,-3.61499e-13)" writing-mode="lr" x="271.83" xml:space="preserve" y="286.34" zvalue="287">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" x="183.609375" xml:space="preserve" y="426.765625" zvalue="288">35kVⅡ段</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="183.609375" xml:space="preserve" y="443.765625" zvalue="288">母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,65.4286,457.5) scale(1,1) translate(0,0)" writing-mode="lr" x="65.42857142857133" xml:space="preserve" y="462.0000000000001" zvalue="289">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,65.4286,483) scale(1,1) translate(0,0)" writing-mode="lr" x="65.42857142857133" xml:space="preserve" y="487.5" zvalue="290">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,65.4286,508.5) scale(1,1) translate(0,0)" writing-mode="lr" x="65.42857142857133" xml:space="preserve" y="513" zvalue="291">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,64.4286,533) scale(1,1) translate(0,0)" writing-mode="lr" x="64.42857142857133" xml:space="preserve" y="537.5" zvalue="292">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,65.4286,559.5) scale(1,1) translate(0,0)" writing-mode="lr" x="65.42857142857133" xml:space="preserve" y="564" zvalue="293">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.4286,141) scale(1,1) translate(0,0)" writing-mode="lr" x="47.43" xml:space="preserve" y="146.5" zvalue="294">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.429,141) scale(1,1) translate(0,0)" writing-mode="lr" x="227.43" xml:space="preserve" y="146.5" zvalue="295">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,64.5536,166.5) scale(1,1) translate(0,0)" writing-mode="lr" x="64.55" xml:space="preserve" y="171" zvalue="296">35kVⅠ段母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="170" stroke="rgb(255,255,255)" text-anchor="middle" x="124.609375" xml:space="preserve" y="426.765625" zvalue="299">35kVⅠ段</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="170" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="124.609375" xml:space="preserve" y="443.765625" zvalue="299">母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="171" stroke="rgb(255,255,255)" text-anchor="middle" x="248.609375" xml:space="preserve" y="426.765625" zvalue="301">10kVⅠ段</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="171" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="248.609375" xml:space="preserve" y="443.765625" zvalue="301">母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="178" stroke="rgb(255,255,255)" text-anchor="middle" x="309.609375" xml:space="preserve" y="426.765625" zvalue="303">10kVⅡ段</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="178" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="309.609375" xml:space="preserve" y="443.765625" zvalue="303">母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.0764,211.889) scale(1,1) translate(0,0)" writing-mode="lr" x="59.08" xml:space="preserve" y="216.39" zvalue="306">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.0764,234.889) scale(1,1) translate(0,0)" writing-mode="lr" x="59.08" xml:space="preserve" y="239.39" zvalue="307">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="191" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,64.5764,188.139) scale(1,1) translate(0,0)" writing-mode="lr" x="64.58" xml:space="preserve" y="192.64" zvalue="308">10kVⅠ段母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236.076,211.889) scale(1,1) translate(0,0)" writing-mode="lr" x="236.08" xml:space="preserve" y="216.39" zvalue="318">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="245" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236.076,234.889) scale(1,1) translate(0,0)" writing-mode="lr" x="236.08" xml:space="preserve" y="239.39" zvalue="319">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="268" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,249.554,166.5) scale(1,1) translate(0,0)" writing-mode="lr" x="249.55" xml:space="preserve" y="171" zvalue="323">35kVⅡ段母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,245.576,188.139) scale(1,1) translate(0,0)" writing-mode="lr" x="245.58" xml:space="preserve" y="192.64" zvalue="326">10kVⅡ段母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="310" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1549.07,167.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1549.07" xml:space="preserve" y="172.21" zvalue="362">35kV1号站用变  50kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="314" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1611.93,872) scale(1,1) translate(0,0)" writing-mode="lr" x="1611.93" xml:space="preserve" y="876.5" zvalue="365">10kV2号站用变  50kVA</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="42.19" y="271.75" zvalue="297"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="1">
   <path class="kv35" d="M 504.29 333.5 L 945.71 333.5" stroke-width="6" zvalue="1"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674259763204" ObjectName="35kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674259763204"/></metadata>
  <path d="M 504.29 333.5 L 945.71 333.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="kv35" d="M 1032 335.5 L 1779.5 335.5" stroke-width="6" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674259828740" ObjectName="35kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674259828740"/></metadata>
  <path d="M 1032 335.5 L 1779.5 335.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv10" d="M 494.75 736.75 L 932.86 736.75" stroke-width="6" zvalue="3"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674259894276" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674259894276"/></metadata>
  <path d="M 494.75 736.75 L 932.86 736.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv10" d="M 1017 736.75 L 1747 736.75" stroke-width="6" zvalue="4"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674259959812" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674259959812"/></metadata>
  <path d="M 1017 736.75 L 1747 736.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="10">
   <g id="100">
    <use class="kv35" height="30" transform="rotate(0,782.25,532) scale(2.08333,2) translate(-393.77,-251)" width="24" x="757.25" xlink:href="#PowerTransformer2:可调两卷变_0" y="502" zvalue="6"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874462658563" ObjectName="35"/>
    </metadata>
   </g>
   <g id="101">
    <use class="kv10" height="30" transform="rotate(0,782.25,532) scale(2.08333,2) translate(-393.77,-251)" width="24" x="757.25" xlink:href="#PowerTransformer2:可调两卷变_1" y="502" zvalue="6"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874462724099" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399463075843" ObjectName="2号主变"/>
   <cge:TPSR_Ref TObjectID="6755399463075843"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,782.25,532) scale(2.08333,2) translate(-393.77,-251)" width="24" x="757.25" y="502"/></g>
  <g id="70">
   <g id="700">
    <use class="kv35" height="30" transform="rotate(0,1231.25,532) scale(2.08333,2) translate(-627.25,-251)" width="24" x="1206.25" xlink:href="#PowerTransformer2:可调两卷变_0" y="502" zvalue="46"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874462789635" ObjectName="35"/>
    </metadata>
   </g>
   <g id="701">
    <use class="kv10" height="30" transform="rotate(0,1231.25,532) scale(2.08333,2) translate(-627.25,-251)" width="24" x="1206.25" xlink:href="#PowerTransformer2:可调两卷变_1" y="502" zvalue="46"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874462855171" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399463141379" ObjectName="1号主变"/>
   <cge:TPSR_Ref TObjectID="6755399463141379"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1231.25,532) scale(2.08333,2) translate(-627.25,-251)" width="24" x="1206.25" y="502"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="17">
   <use class="kv35" height="26" transform="rotate(0,824,548.25) scale(1.25,1.25) translate(-163.3,-106.4)" width="12" x="816.5" xlink:href="#Accessory:避雷器1_0" y="532" zvalue="12"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450143518725" ObjectName="2号主变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,824,548.25) scale(1.25,1.25) translate(-163.3,-106.4)" width="12" x="816.5" y="532"/></g>
  <g id="69">
   <use class="kv35" height="26" transform="rotate(0,1273,548.25) scale(1.25,1.25) translate(-253.1,-106.4)" width="12" x="1265.5" xlink:href="#Accessory:避雷器1_0" y="532" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450144370693" ObjectName="1号主变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1273,548.25) scale(1.25,1.25) translate(-253.1,-106.4)" width="12" x="1265.5" y="532"/></g>
  <g id="75">
   <use class="kv35" height="18" transform="rotate(0,572,444.182) scale(1.33333,1.55556) translate(-140.5,-153.636)" width="15" x="561.9999999999999" xlink:href="#Accessory:PT8_0" y="430.1818181818181" zvalue="70"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450144436229" ObjectName="35kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,572,444.182) scale(1.33333,1.55556) translate(-140.5,-153.636)" width="15" x="561.9999999999999" y="430.1818181818181"/></g>
  <g id="99">
   <use class="kv35" height="18" transform="rotate(0,1545,444.182) scale(1.33333,1.55556) translate(-383.75,-153.636)" width="15" x="1535" xlink:href="#Accessory:PT8_0" y="430.1818181818181" zvalue="81"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450145157125" ObjectName="35kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1545,444.182) scale(1.33333,1.55556) translate(-383.75,-153.636)" width="15" x="1535" y="430.1818181818181"/></g>
  <g id="125">
   <use class="kv35" height="26" transform="rotate(90,1261,118.5) scale(0.916667,1) translate(114.136,0)" width="12" x="1255.5" xlink:href="#Accessory:避雷器1_0" y="105.5" zvalue="113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450145812485" ObjectName="35kV弄回线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1261,118.5) scale(0.916667,1) translate(114.136,0)" width="12" x="1255.5" y="105.5"/></g>
  <g id="146">
   <use class="kv10" height="30" transform="rotate(0,575.333,608.444) scale(1.66667,-1.66667) translate(-220.133,-963.511)" width="30" x="550.3333333333333" xlink:href="#Accessory:10kV避雷器PT_0" y="583.4444444444445" zvalue="130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450146140165" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,575.333,608.444) scale(1.66667,-1.66667) translate(-220.133,-963.511)" width="30" x="550.3333333333333" y="583.4444444444445"/></g>
  <g id="163">
   <use class="kv10" height="30" transform="rotate(0,1556.33,608.444) scale(1.66667,-1.66667) translate(-612.533,-963.511)" width="30" x="1531.333333333333" xlink:href="#Accessory:10kV避雷器PT_0" y="583.4444444444445" zvalue="140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450146336773" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1556.33,608.444) scale(1.66667,-1.66667) translate(-612.533,-963.511)" width="30" x="1531.333333333333" y="583.4444444444445"/></g>
  <g id="211">
   <use class="kv10" height="20" transform="rotate(0,701.226,915.135) scale(1.5,1.5) translate(-228.742,-300.045)" width="20" x="686.2261904761908" xlink:href="#Accessory:带电显示/避雷器_0" y="900.1349206349204" zvalue="188"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450146926597" ObjectName="10kV2号接地变避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,701.226,915.135) scale(1.5,1.5) translate(-228.742,-300.045)" width="20" x="686.2261904761908" y="900.1349206349204"/></g>
  <g id="996">
   <use class="kv10" height="21" transform="rotate(0,782.567,605.323) scale(1.875,1.66667) translate(-361.698,-235.129)" width="8" x="775.0665445665447" xlink:href="#Accessory:中间电缆_0" y="587.8229548229547" zvalue="227"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450147516421" ObjectName="2号主变电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,782.567,605.323) scale(1.875,1.66667) translate(-361.698,-235.129)" width="8" x="775.0665445665447" y="587.8229548229547"/></g>
  <g id="246">
   <use class="kv10" height="21" transform="rotate(0,1231.39,603.759) scale(1.875,1.66667) translate(-571.149,-234.504)" width="8" x="1223.890073978309" xlink:href="#Accessory:中间电缆_0" y="586.2592592592592" zvalue="229"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450147581957" ObjectName="1号主变电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,1231.39,603.759) scale(1.875,1.66667) translate(-571.149,-234.504)" width="8" x="1223.890073978309" y="586.2592592592592"/></g>
  <g id="248">
   <use class="kv10" height="7" transform="rotate(0,530.889,849.381) scale(1.5,1.71429) translate(-173.963,-351.409)" width="12" x="521.8888888888888" xlink:href="#Accessory:电缆1_0" y="843.3809523809524" zvalue="231"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450147647493" ObjectName="10kV2号电容器电缆"/>
   </metadata>
  <rect fill="white" height="7" opacity="0" stroke="white" transform="rotate(0,530.889,849.381) scale(1.5,1.71429) translate(-173.963,-351.409)" width="12" x="521.8888888888888" y="843.3809523809524"/></g>
  <g id="249">
   <use class="kv10" height="7" transform="rotate(0,782.317,847.238) scale(1.5,1.71429) translate(-257.772,-350.516)" width="12" x="773.3174603174602" xlink:href="#Accessory:电缆1_0" y="841.2380952380952" zvalue="233"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450147713029" ObjectName="10kV芒弄隧道线电缆"/>
   </metadata>
  <rect fill="white" height="7" opacity="0" stroke="white" transform="rotate(0,782.317,847.238) scale(1.5,1.71429) translate(-257.772,-350.516)" width="12" x="773.3174603174602" y="841.2380952380952"/></g>
  <g id="250">
   <use class="kv10" height="7" transform="rotate(0,1127.32,851.643) scale(1.5,1.71429) translate(-372.772,-352.351)" width="12" x="1118.31746031746" xlink:href="#Accessory:电缆1_0" y="845.6428571428573" zvalue="235"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450147778565" ObjectName="10kV老寨河大桥线电缆"/>
   </metadata>
  <rect fill="white" height="7" opacity="0" stroke="white" transform="rotate(0,1127.32,851.643) scale(1.5,1.71429) translate(-372.772,-352.351)" width="12" x="1118.31746031746" y="845.6428571428573"/></g>
  <g id="252">
   <use class="kv10" height="7" transform="rotate(0,1472.32,847.238) scale(1.5,1.71429) translate(-487.772,-350.516)" width="12" x="1463.31746031746" xlink:href="#Accessory:电缆1_0" y="841.2380952380952" zvalue="238"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450147844101" ObjectName="10kV1号电容器电缆"/>
   </metadata>
  <rect fill="white" height="7" opacity="0" stroke="white" transform="rotate(0,1472.32,847.238) scale(1.5,1.71429) translate(-487.772,-350.516)" width="12" x="1463.31746031746" y="841.2380952380952"/></g>
  <g id="264">
   <use class="kv10" height="35" transform="rotate(0,663.429,927.714) scale(1.73913,1.71429) translate(-273.457,-374.048)" width="23" x="643.4285714285713" xlink:href="#Accessory:接地变20201012_0" y="897.7142857142856" zvalue="249"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450147975173" ObjectName="10kV2号接地变"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,663.429,927.714) scale(1.73913,1.71429) translate(-273.457,-374.048)" width="23" x="643.4285714285713" y="897.7142857142856"/></g>
  <g id="278">
   <use class="kv10" height="20" transform="rotate(0,1345.12,916.246) scale(1.5,1.5) translate(-443.372,-300.415)" width="20" x="1330.11507936508" xlink:href="#Accessory:带电显示/避雷器_0" y="901.2460317460316" zvalue="259"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450148368389" ObjectName="10kV1号接地变避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1345.12,916.246) scale(1.5,1.5) translate(-443.372,-300.415)" width="20" x="1330.11507936508" y="901.2460317460316"/></g>
  <g id="277">
   <use class="kv10" height="35" transform="rotate(0,1307.32,928.825) scale(1.73913,1.71429) translate(-547.11,-374.511)" width="23" x="1287.31746031746" xlink:href="#Accessory:接地变20201012_0" y="898.8253968253965" zvalue="260"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450148302853" ObjectName="10kV1号接地变"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1307.32,928.825) scale(1.73913,1.71429) translate(-547.11,-374.511)" width="23" x="1287.31746031746" y="898.8253968253965"/></g>
  <g id="303">
   <use class="kv35" height="32" transform="rotate(270,1368.61,104.944) scale(-0.833333,0.833333) translate(-3011.94,18.3222)" width="12" x="1363.611111111111" xlink:href="#Accessory:回贤变PT_0" y="91.61111111111109" zvalue="356"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450148433925" ObjectName="35kV弄回线PT"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(270,1368.61,104.944) scale(-0.833333,0.833333) translate(-3011.94,18.3222)" width="12" x="1363.611111111111" y="91.61111111111109"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="18">
   <path class="kv35" d="M 782.26 517.82 L 824.04 517.82 L 824.04 532.79" stroke-width="1" zvalue="13"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@2" LinkObjectIDznd="17@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 782.26 517.82 L 824.04 517.82 L 824.04 532.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv10" d="M 782.25 560.09 L 782.25 656" stroke-width="1" zvalue="16"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@1" LinkObjectIDznd="20@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 782.25 560.09 L 782.25 656" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv10" d="M 782.25 692.5 L 782.25 736.75" stroke-width="1" zvalue="17"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@1" LinkObjectIDznd="3@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 782.25 692.5 L 782.25 736.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv35" d="M 782.27 504.15 L 782.27 450.99" stroke-width="1" zvalue="21"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@0" LinkObjectIDznd="26@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 782.27 504.15 L 782.27 450.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv35" d="M 782.32 427.53 L 782.32 370.92" stroke-width="1" zvalue="24"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@0" LinkObjectIDznd="30@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 782.32 427.53 L 782.32 370.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="kv35" d="M 782.44 351.42 L 782.44 333.5" stroke-width="1" zvalue="25"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="30@0" LinkObjectIDznd="1@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 782.44 351.42 L 782.44 333.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv35" d="M 748.5 396.59 L 782.32 396.59" stroke-width="1" zvalue="27"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@0" LinkObjectIDznd="31" MaxPinNum="2"/>
   </metadata>
  <path d="M 748.5 396.59 L 782.32 396.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv35" d="M 1053.09 357.36 L 1053.09 335.5" stroke-width="1" zvalue="33"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="2@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1053.09 357.36 L 1053.09 335.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv35" d="M 906.09 355.36 L 906.09 333.5" stroke-width="1" zvalue="36"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@0" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 906.09 355.36 L 906.09 333.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv35" d="M 990 397.81 L 1053.06 397.81 L 1053.06 378.81" stroke-width="1" zvalue="37"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="9@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 990 397.81 L 1053.06 397.81 L 1053.06 378.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv35" d="M 972.25 397.87 L 906.06 397.87 L 906.06 376.81" stroke-width="1" zvalue="38"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@1" LinkObjectIDznd="15@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 972.25 397.87 L 906.06 397.87 L 906.06 376.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv35" d="M 1231.26 517.82 L 1273.04 517.82 L 1273.04 532.79" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@2" LinkObjectIDznd="69@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1231.26 517.82 L 1273.04 517.82 L 1273.04 532.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv10" d="M 1231.25 560.09 L 1231.25 656" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@1" LinkObjectIDznd="67@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1231.25 560.09 L 1231.25 656" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv10" d="M 1231.25 692.5 L 1231.25 736.75" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@1" LinkObjectIDznd="4@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1231.25 692.5 L 1231.25 736.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv35" d="M 1231.27 504.15 L 1231.27 450.99" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="62@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1231.27 504.15 L 1231.27 450.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv35" d="M 1231.32 427.53 L 1231.32 370.92" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="60@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1231.32 427.53 L 1231.32 370.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv35" d="M 1231.44 351.42 L 1231.44 335.5" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="2@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1231.44 351.42 L 1231.44 335.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv35" d="M 1197.5 396.59 L 1231.32 396.59" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="59" MaxPinNum="2"/>
   </metadata>
  <path d="M 1197.5 396.59 L 1231.32 396.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv35" d="M 906.05 413.25 L 906.06 397.86" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="42" MaxPinNum="2"/>
   </metadata>
  <path d="M 906.05 413.25 L 906.06 397.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv35" d="M 1052.85 411.8 L 1052.85 397.81" stroke-width="1" zvalue="67"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="41" MaxPinNum="2"/>
   </metadata>
  <path d="M 1052.85 411.8 L 1052.85 397.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv10" d="M 782.57 605.32 L 782.25 605.32" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="996@0" LinkObjectIDznd="22" MaxPinNum="2"/>
   </metadata>
  <path d="M 782.57 605.32 L 782.25 605.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv35" d="M 570.76 431.18 L 570.76 387.59" stroke-width="1" zvalue="73"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@0" LinkObjectIDznd="78@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 570.76 431.18 L 570.76 387.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv35" d="M 570.99 369.71 L 570.99 333.5" stroke-width="1" zvalue="74"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="1@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 570.99 369.71 L 570.99 333.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv35" d="M 604.87 352.79 L 570.99 352.79" stroke-width="1" zvalue="78"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="80" MaxPinNum="2"/>
   </metadata>
  <path d="M 604.87 352.79 L 570.99 352.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv35" d="M 604.87 400.79 L 570.76 400.79" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="79" MaxPinNum="2"/>
   </metadata>
  <path d="M 604.87 400.79 L 570.76 400.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv35" d="M 1543.76 431.18 L 1543.76 387.59" stroke-width="1" zvalue="85"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="98@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1543.76 431.18 L 1543.76 387.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv35" d="M 1543.99 369.71 L 1543.99 335.5" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="2@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1543.99 369.71 L 1543.99 335.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv35" d="M 1577.87 352.79 L 1543.99 352.79" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 1577.87 352.79 L 1543.99 352.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv35" d="M 1577.87 400.79 L 1543.76 400.79" stroke-width="1" zvalue="92"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="97" MaxPinNum="2"/>
   </metadata>
  <path d="M 1577.87 400.79 L 1543.76 400.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv35" d="M 1301.1 246.89 L 1301.1 288.47" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@1" LinkObjectIDznd="107@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1301.1 246.89 L 1301.1 288.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv35" d="M 1302.17 309.92 L 1302.17 335.5" stroke-width="1" zvalue="100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@1" LinkObjectIDznd="2@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1302.17 309.92 L 1302.17 335.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv35" d="M 1300 86.32 L 1300 158.36" stroke-width="1" zvalue="102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="111@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1300 86.32 L 1300 158.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv35" d="M 1301.06 179.81 L 1300.95 221.09" stroke-width="1" zvalue="103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@1" LinkObjectIDznd="100@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1301.06 179.81 L 1300.95 221.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv35" d="M 1271.75 264.05 L 1301.1 264.05" stroke-width="1" zvalue="105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="109" MaxPinNum="2"/>
   </metadata>
  <path d="M 1271.75 264.05 L 1301.1 264.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv35" d="M 1268.75 141.05 L 1300 141.05" stroke-width="1" zvalue="111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@0" LinkObjectIDznd="113" MaxPinNum="2"/>
   </metadata>
  <path d="M 1268.75 141.05 L 1300 141.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv35" d="M 1273.37 118.53 L 1300 118.53" stroke-width="1" zvalue="114"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="113" MaxPinNum="2"/>
   </metadata>
  <path d="M 1273.37 118.53 L 1300 118.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv35" d="M 1317.36 105.91 L 1300 105.91" stroke-width="1" zvalue="116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="113" MaxPinNum="2"/>
   </metadata>
  <path d="M 1317.36 105.91 L 1300 105.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="kv35" d="M 1543.99 297.81 L 1543.99 335.5" stroke-width="1" zvalue="125"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@1" LinkObjectIDznd="2@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1543.99 297.81 L 1543.99 335.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv35" d="M 1269.75 200.05 L 1301.01 200.05" stroke-width="1" zvalue="129"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="114" MaxPinNum="2"/>
   </metadata>
  <path d="M 1269.75 200.05 L 1301.01 200.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv10" d="M 575.33 626.78 L 575.33 663.41" stroke-width="1" zvalue="137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="153@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 575.33 626.78 L 575.33 663.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv10" d="M 574.96 701.32 L 574.96 736.75" stroke-width="1" zvalue="138"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@1" LinkObjectIDznd="3@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 574.96 701.32 L 574.96 736.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="kv10" d="M 1556.33 626.78 L 1556.33 663.41" stroke-width="1" zvalue="144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@0" LinkObjectIDznd="162@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1556.33 626.78 L 1556.33 663.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv10" d="M 1555.96 701.32 L 1555.96 736.75" stroke-width="1" zvalue="145"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162@1" LinkObjectIDznd="4@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1555.96 701.32 L 1555.96 736.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="kv10" d="M 935.72 805.38 L 880.18 805.38 L 880.18 736.75" stroke-width="1" zvalue="147"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@0" LinkObjectIDznd="3@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 935.72 805.38 L 880.18 805.38 L 880.18 736.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="kv10" d="M 782.25 771.45 L 782.25 736.75" stroke-width="1" zvalue="155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@0" LinkObjectIDznd="3@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 782.25 771.45 L 782.25 736.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv10" d="M 782.25 807.95 L 782.25 876.88" stroke-width="1" zvalue="157"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@1" LinkObjectIDznd="175@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 782.25 807.95 L 782.25 876.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv10" d="M 782.32 841.67 L 782.25 841.67" stroke-width="1" zvalue="159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@0" LinkObjectIDznd="177" MaxPinNum="2"/>
   </metadata>
  <path d="M 782.32 841.67 L 782.25 841.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv10" d="M 755.21 844.29 L 755.21 829.08 L 782.25 829.08" stroke-width="1" zvalue="161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@0" LinkObjectIDznd="177" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.21 844.29 L 755.21 829.08 L 782.25 829.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv10" d="M 781.56 896.37 L 781.56 928.51" stroke-width="1" zvalue="166"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@1" LinkObjectIDznd="187@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.56 896.37 L 781.56 928.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="203">
   <path class="kv10" d="M 530.8 771.45 L 530.8 736.75" stroke-width="1" zvalue="171"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="204@0" LinkObjectIDznd="3@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 530.8 771.45 L 530.8 736.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv10" d="M 530.8 807.95 L 530.8 876.88" stroke-width="1" zvalue="173"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="204@1" LinkObjectIDznd="202@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 530.8 807.95 L 530.8 876.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="kv10" d="M 530.89 843.81 L 530.8 843.81" stroke-width="1" zvalue="176"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@0" LinkObjectIDznd="201" MaxPinNum="2"/>
   </metadata>
  <path d="M 530.89 843.81 L 530.8 843.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv10" d="M 504.21 844.29 L 504.21 829.08 L 530.8 829.08" stroke-width="1" zvalue="178"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@0" LinkObjectIDznd="201" MaxPinNum="2"/>
   </metadata>
  <path d="M 504.21 844.29 L 504.21 829.08 L 530.8 829.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv10" d="M 530.56 896.37 L 530.56 924.07" stroke-width="1" zvalue="181"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@1" LinkObjectIDznd="206@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 530.56 896.37 L 530.56 924.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="kv10" d="M 656.08 766.85 L 656.08 736.75" stroke-width="1" zvalue="187"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@0" LinkObjectIDznd="3@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 656.08 766.85 L 656.08 736.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="kv10" d="M 1127.25 771.45 L 1127.25 736.75" stroke-width="1" zvalue="192"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="225@0" LinkObjectIDznd="4@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1127.25 771.45 L 1127.25 736.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="kv10" d="M 1127.25 807.95 L 1127.25 876.88" stroke-width="1" zvalue="194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="225@1" LinkObjectIDznd="223@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1127.25 807.95 L 1127.25 876.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="kv10" d="M 1100.21 844.29 L 1100.21 829.08 L 1127.25 829.08" stroke-width="1" zvalue="199"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@0" LinkObjectIDznd="222" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100.21 844.29 L 1100.21 829.08 L 1127.25 829.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv10" d="M 1126.56 896.37 L 1126.56 928.51" stroke-width="1" zvalue="202"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@1" LinkObjectIDznd="217@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.56 896.37 L 1126.56 928.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="238">
   <path class="kv10" d="M 1472.46 772.57 L 1472.46 736.75" stroke-width="1" zvalue="207"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="239@0" LinkObjectIDznd="4@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472.46 772.57 L 1472.46 736.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="kv10" d="M 1472.46 809.07 L 1472.46 877.99" stroke-width="1" zvalue="209"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="239@1" LinkObjectIDznd="237@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472.46 809.07 L 1472.46 877.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="234">
   <path class="kv10" d="M 1472.32 841.67 L 1472.46 841.67" stroke-width="1" zvalue="212"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252@0" LinkObjectIDznd="236" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472.32 841.67 L 1472.46 841.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="kv10" d="M 1445.87 845.4 L 1445.87 830.19 L 1472.46 830.19" stroke-width="1" zvalue="214"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@0" LinkObjectIDznd="236" MaxPinNum="2"/>
   </metadata>
  <path d="M 1445.87 845.4 L 1445.87 830.19 L 1472.46 830.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="kv10" d="M 1472.22 897.49 L 1472.22 925.18" stroke-width="1" zvalue="216"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@1" LinkObjectIDznd="230@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472.22 897.49 L 1472.22 925.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="247">
   <path class="kv10" d="M 1231.39 603.76 L 1231.25 603.76" stroke-width="1" zvalue="230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@0" LinkObjectIDznd="66" MaxPinNum="2"/>
   </metadata>
  <path d="M 1231.39 603.76 L 1231.25 603.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv10" d="M 1127.32 846.07 L 1127.25 846.07" stroke-width="1" zvalue="236"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@0" LinkObjectIDznd="222" MaxPinNum="2"/>
   </metadata>
  <path d="M 1127.32 846.07 L 1127.25 846.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="254">
   <path class="kv10" d="M 971.82 805.45 L 1038.25 805.45 L 1038.25 789.78" stroke-width="1" zvalue="240"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@1" LinkObjectIDznd="253@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 971.82 805.45 L 1038.25 805.45 L 1038.25 789.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="255">
   <path class="kv10" d="M 1038.25 756.72 L 1038.25 736.75" stroke-width="1" zvalue="241"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@0" LinkObjectIDznd="4@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1038.25 756.72 L 1038.25 736.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="265">
   <path class="kv10" d="M 656 802.95 L 656 898.14" stroke-width="1" zvalue="250"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@1" LinkObjectIDznd="264@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 656 802.95 L 656 898.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="kv10" d="M 656 882 L 701.35 882 L 701.35 902.26" stroke-width="1" zvalue="251"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265" LinkObjectIDznd="211@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 656 882 L 701.35 882 L 701.35 902.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv10" d="M 656 882 L 624.93 882 L 624.93 903.1" stroke-width="1" zvalue="254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266" LinkObjectIDznd="267@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 656 882 L 624.93 882 L 624.93 903.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="279">
   <path class="kv10" d="M 1299.96 767.97 L 1299.96 736.75" stroke-width="1" zvalue="257"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@0" LinkObjectIDznd="4@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1299.96 767.97 L 1299.96 736.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="276">
   <path class="kv10" d="M 1299.89 804.07 L 1299.89 899.25" stroke-width="1" zvalue="261"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@1" LinkObjectIDznd="277@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1299.89 804.07 L 1299.89 899.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="275">
   <path class="kv10" d="M 1299.89 883.11 L 1345.24 883.11 L 1345.24 903.37" stroke-width="1" zvalue="263"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276" LinkObjectIDznd="278@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1299.89 883.11 L 1345.24 883.11 L 1345.24 903.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="273">
   <path class="kv10" d="M 1299.89 883.11 L 1268.82 883.11 L 1268.82 904.21" stroke-width="1" zvalue="265"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="275" LinkObjectIDznd="274@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1299.89 883.11 L 1268.82 883.11 L 1268.82 904.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="304">
   <path class="kv35" d="M 1355.83 104.94 L 1338.81 104.94" stroke-width="1" zvalue="357"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="303@0" LinkObjectIDznd="127@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1355.83 104.94 L 1338.81 104.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv35" d="M 1545 242.01 L 1545 276.36" stroke-width="1" zvalue="362"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="309@0" LinkObjectIDznd="135@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1545 242.01 L 1545 276.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="kv35" d="M 1571.68 262.38 L 1545 262.38" stroke-width="1" zvalue="363"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@0" LinkObjectIDznd="311" MaxPinNum="2"/>
   </metadata>
  <path d="M 1571.68 262.38 L 1545 262.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="315">
   <path class="kv10" d="M 1600 798.33 L 1600 736.75" stroke-width="1" zvalue="365"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="313@0" LinkObjectIDznd="4@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1600 798.33 L 1600 736.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="20">
   <use class="kv10" height="20" transform="rotate(0,782.25,674.5) scale(1.5,2) translate(-258.25,-327.25)" width="10" x="774.75" xlink:href="#Breaker:手车开关_0" y="654.5" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924573265924" ObjectName="2号主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473924573265924"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,782.25,674.5) scale(1.5,2) translate(-258.25,-327.25)" width="10" x="774.75" y="654.5"/></g>
  <g id="26">
   <use class="kv35" height="20" transform="rotate(0,782.364,439.273) scale(1.36364,1.22727) translate(-206.812,-79.0741)" width="10" x="775.5454545454543" xlink:href="#Breaker:开关_0" y="426.9999999999999" zvalue="20"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924573331460" ObjectName="2号主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473924573331460"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,782.364,439.273) scale(1.36364,1.22727) translate(-206.812,-79.0741)" width="10" x="775.5454545454543" y="426.9999999999999"/></g>
  <g id="37">
   <use class="kv35" height="20" transform="rotate(90,981.091,397.93) scale(-1.2,-0.9) translate(-1797.67,-841.075)" width="10" x="975.090909090909" xlink:href="#Breaker:母联开关_0" y="388.9300506372838" zvalue="28"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924573396996" ObjectName="35kV分段312断路器"/>
   <cge:TPSR_Ref TObjectID="6473924573396996"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,981.091,397.93) scale(-1.2,-0.9) translate(-1797.67,-841.075)" width="10" x="975.090909090909" y="388.9300506372838"/></g>
  <g id="67">
   <use class="kv10" height="20" transform="rotate(0,1231.25,674.5) scale(1.5,2) translate(-407.917,-327.25)" width="10" x="1223.75" xlink:href="#Breaker:手车开关_0" y="654.5" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924573528068" ObjectName="1号主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924573528068"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1231.25,674.5) scale(1.5,2) translate(-407.917,-327.25)" width="10" x="1223.75" y="654.5"/></g>
  <g id="62">
   <use class="kv35" height="20" transform="rotate(0,1231.36,439.273) scale(1.36364,1.22727) translate(-326.545,-79.0741)" width="10" x="1224.545454545454" xlink:href="#Breaker:开关_0" y="426.9999999999999" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924573462532" ObjectName="1号主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924573462532"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1231.36,439.273) scale(1.36364,1.22727) translate(-326.545,-79.0741)" width="10" x="1224.545454545454" y="426.9999999999999"/></g>
  <g id="100">
   <use class="kv35" height="20" transform="rotate(0,1301,234) scale(1.5,1.35) translate(-431.167,-57.1667)" width="10" x="1293.5" xlink:href="#Breaker:开关_0" y="220.5" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924573593604" ObjectName="35kV弄回线351断路器"/>
   <cge:TPSR_Ref TObjectID="6473924573593604"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1301,234) scale(1.5,1.35) translate(-431.167,-57.1667)" width="10" x="1293.5" y="220.5"/></g>
  <g id="164">
   <use class="kv10" height="20" transform="rotate(90,953.818,805.455) scale(-1.5,-2) translate(-1587.2,-1198.18)" width="10" x="946.3181818181818" xlink:href="#Breaker:母联小车开关_0" y="785.4545454545456" zvalue="146"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924573659140" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473924573659140"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,953.818,805.455) scale(-1.5,-2) translate(-1587.2,-1198.18)" width="10" x="946.3181818181818" y="785.4545454545456"/></g>
  <g id="172">
   <use class="kv10" height="20" transform="rotate(0,782.25,789.955) scale(1.5,2) translate(-258.25,-384.977)" width="10" x="774.75" xlink:href="#Breaker:手车开关_0" y="769.9545454545454" zvalue="154"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924573724676" ObjectName="10kV芒弄隧道线054断路器"/>
   <cge:TPSR_Ref TObjectID="6473924573724676"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,782.25,789.955) scale(1.5,2) translate(-258.25,-384.977)" width="10" x="774.75" y="769.9545454545454"/></g>
  <g id="204">
   <use class="kv10" height="20" transform="rotate(0,530.795,789.955) scale(1.5,2) translate(-174.432,-384.977)" width="10" x="523.2954545454545" xlink:href="#Breaker:手车开关_0" y="769.9545454545454" zvalue="169"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924573790212" ObjectName="10kV2号电容器056断路器"/>
   <cge:TPSR_Ref TObjectID="6473924573790212"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,530.795,789.955) scale(1.5,2) translate(-174.432,-384.977)" width="10" x="523.2954545454545" y="769.9545454545454"/></g>
  <g id="208">
   <use class="kv10" height="20" transform="rotate(0,656,784.955) scale(1.5,2) translate(-216.167,-382.477)" width="10" x="648.5000000000001" xlink:href="#Breaker:母联小车开关_0" y="764.9545454545454" zvalue="186"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924573855748" ObjectName="10kV2号接地变055断路器"/>
   <cge:TPSR_Ref TObjectID="6473924573855748"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,656,784.955) scale(1.5,2) translate(-216.167,-382.477)" width="10" x="648.5000000000001" y="764.9545454545454"/></g>
  <g id="225">
   <use class="kv10" height="20" transform="rotate(0,1127.25,789.955) scale(1.5,2) translate(-373.25,-384.977)" width="10" x="1119.75" xlink:href="#Breaker:手车开关_0" y="769.9545454545454" zvalue="190"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924573921284" ObjectName="10kV老寨河大桥线053断路器"/>
   <cge:TPSR_Ref TObjectID="6473924573921284"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1127.25,789.955) scale(1.5,2) translate(-373.25,-384.977)" width="10" x="1119.75" y="769.9545454545454"/></g>
  <g id="239">
   <use class="kv10" height="20" transform="rotate(0,1472.46,791.066) scale(1.5,2) translate(-488.321,-385.533)" width="10" x="1464.962121212121" xlink:href="#Breaker:手车开关_0" y="771.0656565656566" zvalue="205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924573986820" ObjectName="10kV1号电容器051断路器"/>
   <cge:TPSR_Ref TObjectID="6473924573986820"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1472.46,791.066) scale(1.5,2) translate(-488.321,-385.533)" width="10" x="1464.962121212121" y="771.0656565656566"/></g>
  <g id="280">
   <use class="kv10" height="20" transform="rotate(0,1299.89,786.066) scale(1.5,2) translate(-430.796,-383.033)" width="10" x="1292.388888888889" xlink:href="#Breaker:母联小车开关_0" y="766.0656565656566" zvalue="256"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924574052356" ObjectName="10kV1号接地变052断路器"/>
   <cge:TPSR_Ref TObjectID="6473924574052356"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1299.89,786.066) scale(1.5,2) translate(-430.796,-383.033)" width="10" x="1292.388888888889" y="766.0656565656566"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="30">
   <use class="kv35" height="30" transform="rotate(0,782.364,361.091) scale(0.909091,0.666667) translate(77.5545,175.545)" width="15" x="775.5454545454543" xlink:href="#Disconnector:刀闸_0" y="351.090909090909" zvalue="23"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450143584261" ObjectName="2号主变35kV侧3022隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450143584261"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,782.364,361.091) scale(0.909091,0.666667) translate(77.5545,175.545)" width="15" x="775.5454545454543" y="351.090909090909"/></g>
  <g id="9">
   <use class="kv35" height="30" transform="rotate(0,1053,368) scale(1,0.733333) translate(0,129.818)" width="15" x="1045.5" xlink:href="#Disconnector:刀闸_0" y="357" zvalue="31"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450143780869" ObjectName="35kV分段3121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450143780869"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1053,368) scale(1,0.733333) translate(0,129.818)" width="15" x="1045.5" y="357"/></g>
  <g id="15">
   <use class="kv35" height="30" transform="rotate(0,906,366) scale(1,0.733333) translate(0,129.091)" width="15" x="898.5" xlink:href="#Disconnector:刀闸_0" y="355" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450143846405" ObjectName="35kV分段3122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450143846405"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,906,366) scale(1,0.733333) translate(0,129.091)" width="15" x="898.5" y="355"/></g>
  <g id="60">
   <use class="kv35" height="30" transform="rotate(0,1231.36,361.091) scale(0.909091,0.666667) translate(122.455,175.545)" width="15" x="1224.545454545454" xlink:href="#Disconnector:刀闸_0" y="351.090909090909" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450144305157" ObjectName="1号主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450144305157"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1231.36,361.091) scale(0.909091,0.666667) translate(122.455,175.545)" width="15" x="1224.545454545454" y="351.090909090909"/></g>
  <g id="78">
   <use class="kv35" height="30" transform="rotate(0,570.922,378.578) scale(0.833333,0.611111) translate(112.934,235.08)" width="15" x="564.6715686274508" xlink:href="#Disconnector:刀闸_0" y="369.4117647058822" zvalue="72"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450144501765" ObjectName="35kVⅡ段母线电压互感器3902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450144501765"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,570.922,378.578) scale(0.833333,0.611111) translate(112.934,235.08)" width="15" x="564.6715686274508" y="369.4117647058822"/></g>
  <g id="98">
   <use class="kv35" height="30" transform="rotate(0,1543.92,378.578) scale(0.833333,0.611111) translate(307.534,235.08)" width="15" x="1537.671568627451" xlink:href="#Disconnector:刀闸_0" y="369.4117647058822" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450145091589" ObjectName="35kVⅠ段母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450145091589"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1543.92,378.578) scale(0.833333,0.611111) translate(307.534,235.08)" width="15" x="1537.671568627451" y="369.4117647058822"/></g>
  <g id="107">
   <use class="kv35" height="30" transform="rotate(0,1302.11,299.111) scale(1,0.733333) translate(0,104.768)" width="15" x="1294.611111111111" xlink:href="#Disconnector:刀闸_0" y="288.1111111111111" zvalue="98"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450145288197" ObjectName="35kV弄回线3511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450145288197"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1302.11,299.111) scale(1,0.733333) translate(0,104.768)" width="15" x="1294.611111111111" y="288.1111111111111"/></g>
  <g id="111">
   <use class="kv35" height="30" transform="rotate(0,1301,169) scale(1,0.733333) translate(0,57.4545)" width="15" x="1293.5" xlink:href="#Disconnector:刀闸_0" y="158" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450145353733" ObjectName="35kV弄回线3516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450145353733"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1301,169) scale(1,0.733333) translate(0,57.4545)" width="15" x="1293.5" y="158"/></g>
  <g id="127">
   <use class="kv35" height="30" transform="rotate(270,1328,106) scale(1,0.733333) translate(0,34.5455)" width="15" x="1320.5" xlink:href="#Disconnector:刀闸_0" y="95" zvalue="115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450145878021" ObjectName="35kV弄回线3519隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450145878021"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1328,106) scale(1,0.733333) translate(0,34.5455)" width="15" x="1320.5" y="95"/></g>
  <g id="135">
   <use class="kv35" height="30" transform="rotate(0,1543.93,287) scale(1,0.733333) translate(0,100.364)" width="15" x="1536.433551745729" xlink:href="#Disconnector:刀闸_0" y="276" zvalue="122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450145943557" ObjectName="35kV1号站用变3531隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450145943557"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1543.93,287) scale(1,0.733333) translate(0,100.364)" width="15" x="1536.433551745729" y="276"/></g>
  <g id="153">
   <use class="kv10" height="36" transform="rotate(0,574.941,682.353) scale(1.07143,1.11111) translate(-37.8294,-66.2353)" width="14" x="567.4411764705883" xlink:href="#Disconnector:联体手车刀闸1_0" y="662.3529411764705" zvalue="135"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450146205701" ObjectName="10kVⅡ段母线电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450146205701"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,574.941,682.353) scale(1.07143,1.11111) translate(-37.8294,-66.2353)" width="14" x="567.4411764705883" y="662.3529411764705"/></g>
  <g id="162">
   <use class="kv10" height="36" transform="rotate(0,1555.94,682.353) scale(1.07143,1.11111) translate(-103.229,-66.2353)" width="14" x="1548.441176470588" xlink:href="#Disconnector:联体手车刀闸1_0" y="662.3529411764705" zvalue="142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450146271237" ObjectName="10kVⅠ段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450146271237"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1555.94,682.353) scale(1.07143,1.11111) translate(-103.229,-66.2353)" width="14" x="1548.441176470588" y="662.3529411764705"/></g>
  <g id="175">
   <use class="kv10" height="30" transform="rotate(0,781.5,886.545) scale(0.909091,0.666667) translate(77.4682,438.273)" width="15" x="774.681818181818" xlink:href="#Disconnector:刀闸_0" y="876.5454545454547" zvalue="156"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450146402309" ObjectName="10kV芒弄隧道线0546隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450146402309"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,781.5,886.545) scale(0.909091,0.666667) translate(77.4682,438.273)" width="15" x="774.681818181818" y="876.5454545454547"/></g>
  <g id="202">
   <use class="kv10" height="30" transform="rotate(0,530.5,886.545) scale(0.909091,0.666667) translate(52.3682,438.273)" width="15" x="523.6818181818181" xlink:href="#Disconnector:刀闸_0" y="876.5454545454547" zvalue="172"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450146795525" ObjectName="10kV2号电容器0566隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450146795525"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,530.5,886.545) scale(0.909091,0.666667) translate(52.3682,438.273)" width="15" x="523.6818181818181" y="876.5454545454547"/></g>
  <g id="223">
   <use class="kv10" height="30" transform="rotate(0,1126.5,886.545) scale(0.909091,0.666667) translate(111.968,438.273)" width="15" x="1119.681818181818" xlink:href="#Disconnector:刀闸_0" y="876.5454545454547" zvalue="193"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450147188741" ObjectName="10kV老寨河大桥线0536隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450147188741"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1126.5,886.545) scale(0.909091,0.666667) translate(111.968,438.273)" width="15" x="1119.681818181818" y="876.5454545454547"/></g>
  <g id="237">
   <use class="kv10" height="30" transform="rotate(0,1472.17,887.657) scale(0.909091,0.666667) translate(146.535,438.828)" width="15" x="1465.348484848485" xlink:href="#Disconnector:刀闸_0" y="877.6565656565658" zvalue="208"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450147450885" ObjectName="10kV1号电容器0516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450147450885"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1472.17,887.657) scale(0.909091,0.666667) translate(146.535,438.828)" width="15" x="1465.348484848485" y="877.6565656565658"/></g>
  <g id="253">
   <use class="kv10" height="36" transform="rotate(0,1038.25,773.25) scale(1.07143,0.972222) translate(-68.7167,21.5929)" width="14" x="1030.75" xlink:href="#Disconnector:手车刀闸_0" y="755.75" zvalue="239"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450147909637" ObjectName="10kV分段0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450147909637"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1038.25,773.25) scale(1.07143,0.972222) translate(-68.7167,21.5929)" width="14" x="1030.75" y="755.75"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="34">
   <use class="kv35" height="20" transform="rotate(90,739.636,396.545) scale(0.909091,0.909091) translate(73.5091,38.7455)" width="10" x="735.0909090909088" xlink:href="#GroundDisconnector:地刀_0" y="387.4545454545454" zvalue="26"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450143715333" ObjectName="2号主变35kV侧30227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450143715333"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,739.636,396.545) scale(0.909091,0.909091) translate(73.5091,38.7455)" width="10" x="735.0909090909088" y="387.4545454545454"/></g>
  <g id="43">
   <use class="kv35" height="20" transform="rotate(0,906,423) scale(1,1) translate(0,0)" width="10" x="901" xlink:href="#GroundDisconnector:地刀_0" y="413" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450143977477" ObjectName="35kV分段31227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450143977477"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,906,423) scale(1,1) translate(0,0)" width="10" x="901" y="413"/></g>
  <g id="46">
   <use class="kv35" height="20" transform="rotate(0,1052.8,421.546) scale(1,1) translate(0,0)" width="10" x="1047.797101449275" xlink:href="#GroundDisconnector:地刀_0" y="411.5458937198068" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450144108549" ObjectName="35kV分段31217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450144108549"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1052.8,421.546) scale(1,1) translate(0,0)" width="10" x="1047.797101449275" y="411.5458937198068"/></g>
  <g id="57">
   <use class="kv35" height="20" transform="rotate(90,1188.64,396.545) scale(0.909091,0.909091) translate(118.409,38.7455)" width="10" x="1184.090909090909" xlink:href="#GroundDisconnector:地刀_0" y="387.4545454545454" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450144239621" ObjectName="1号主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450144239621"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1188.64,396.545) scale(0.909091,0.909091) translate(118.409,38.7455)" width="10" x="1184.090909090909" y="387.4545454545454"/></g>
  <g id="82">
   <use class="kv35" height="20" transform="rotate(270,613,352.833) scale(0.833333,0.833333) translate(121.767,68.9)" width="10" x="608.8333333333331" xlink:href="#GroundDisconnector:地刀_0" y="344.4999999999999" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450144632837" ObjectName="35kVⅡ段母线电压互感器39020接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450144632837"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,613,352.833) scale(0.833333,0.833333) translate(121.767,68.9)" width="10" x="608.8333333333331" y="344.4999999999999"/></g>
  <g id="84">
   <use class="kv35" height="20" transform="rotate(270,613,400.833) scale(0.833333,0.833333) translate(121.767,78.5)" width="10" x="608.8333333333331" xlink:href="#GroundDisconnector:地刀_0" y="392.5" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450144763909" ObjectName="35kVⅡ段母线电压互感器39027接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450144763909"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,613,400.833) scale(0.833333,0.833333) translate(121.767,78.5)" width="10" x="608.8333333333331" y="392.5"/></g>
  <g id="95">
   <use class="kv35" height="20" transform="rotate(270,1586,352.833) scale(0.833333,0.833333) translate(316.367,68.9)" width="10" x="1581.833333333333" xlink:href="#GroundDisconnector:地刀_0" y="344.4999999999999" zvalue="87"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450145026053" ObjectName="35kVⅠ段母线电压互感器39010接地刀闸"/>
   <cge:TPSR_Ref TObjectID="6192450145026053"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1586,352.833) scale(0.833333,0.833333) translate(316.367,68.9)" width="10" x="1581.833333333333" y="344.4999999999999"/></g>
  <g id="94">
   <use class="kv35" height="20" transform="rotate(270,1586,400.833) scale(0.833333,0.833333) translate(316.367,78.5)" width="10" x="1581.833333333333" xlink:href="#GroundDisconnector:地刀_0" y="392.5" zvalue="89"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450144894981" ObjectName="35kVⅠ段母线电压互感器39017接地刀闸"/>
   <cge:TPSR_Ref TObjectID="6192450144894981"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1586,400.833) scale(0.833333,0.833333) translate(316.367,78.5)" width="10" x="1581.833333333333" y="392.5"/></g>
  <g id="115">
   <use class="kv35" height="20" transform="rotate(90,1262,264) scale(1,1) translate(0,0)" width="10" x="1257" xlink:href="#GroundDisconnector:地刀_0" y="254" zvalue="104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450145484805" ObjectName="35kV弄回线35117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450145484805"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1262,264) scale(1,1) translate(0,0)" width="10" x="1257" y="254"/></g>
  <g id="118">
   <use class="kv35" height="20" transform="rotate(90,1259,141) scale(1,1) translate(0,0)" width="10" x="1254" xlink:href="#GroundDisconnector:地刀_0" y="131" zvalue="107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450145615877" ObjectName="35kV弄回线35167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450145615877"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1259,141) scale(1,1) translate(0,0)" width="10" x="1254" y="131"/></g>
  <g id="119">
   <use class="kv35" height="20" transform="rotate(90,1260,200) scale(1,1) translate(0,0)" width="10" x="1255" xlink:href="#GroundDisconnector:地刀_0" y="190" zvalue="109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450145746949" ObjectName="35kV弄回线35110接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450145746949"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1260,200) scale(1,1) translate(0,0)" width="10" x="1255" y="190"/></g>
  <g id="142">
   <use class="kv35" height="20" transform="rotate(270,1581.43,262.429) scale(1,1) translate(0,-1.12101e-13)" width="10" x="1576.428571428572" xlink:href="#GroundDisconnector:地刀_0" y="252.4285714285715" zvalue="127"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450146074629" ObjectName="35kV1号站用变35317接地刀闸"/>
   <cge:TPSR_Ref TObjectID="6192450146074629"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1581.43,262.429) scale(1,1) translate(0,-1.12101e-13)" width="10" x="1576.428571428572" y="252.4285714285715"/></g>
  <g id="180">
   <use class="kv10" height="20" transform="rotate(0,755.167,852.417) scale(0.833,0.8335) translate(150.561,168.614)" width="10" x="751.0016666666668" xlink:href="#GroundDisconnector:地刀_0" y="844.0816666666665" zvalue="160"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450146533381" ObjectName="10kV芒弄隧道线05427接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450146533381"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,755.167,852.417) scale(0.833,0.8335) translate(150.561,168.614)" width="10" x="751.0016666666668" y="844.0816666666665"/></g>
  <g id="198">
   <use class="kv10" height="20" transform="rotate(0,504.167,852.417) scale(0.833,0.8335) translate(100.24,168.614)" width="10" x="500.0016666666667" xlink:href="#GroundDisconnector:地刀_0" y="844.0816666666665" zvalue="177"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450146729989" ObjectName="10kV2号电容器05627接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450146729989"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,504.167,852.417) scale(0.833,0.8335) translate(100.24,168.614)" width="10" x="500.0016666666667" y="844.0816666666665"/></g>
  <g id="219">
   <use class="kv10" height="20" transform="rotate(0,1100.17,852.417) scale(0.833,0.8335) translate(219.727,168.614)" width="10" x="1096.001666666667" xlink:href="#GroundDisconnector:地刀_0" y="844.0816666666665" zvalue="198"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450147123205" ObjectName="10kV老寨河大桥线05317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450147123205"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1100.17,852.417) scale(0.833,0.8335) translate(219.727,168.614)" width="10" x="1096.001666666667" y="844.0816666666665"/></g>
  <g id="233">
   <use class="kv10" height="20" transform="rotate(0,1445.83,853.528) scale(0.833,0.8335) translate(289.026,168.836)" width="10" x="1441.668333333334" xlink:href="#GroundDisconnector:地刀_0" y="845.1927777777778" zvalue="213"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450147385349" ObjectName="10kV1号电容器05117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450147385349"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1445.83,853.528) scale(0.833,0.8335) translate(289.026,168.836)" width="10" x="1441.668333333334" y="845.1927777777778"/></g>
  <g id="267">
   <use class="kv10" height="20" transform="rotate(0,624.857,917.03) scale(1.42857,1.42857) translate(-185.314,-270.823)" width="10" x="617.7142857142859" xlink:href="#GroundDisconnector:地刀_0" y="902.7441860465115" zvalue="252"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450148106245" ObjectName="10kV2号接地变05567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450148106245"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,624.857,917.03) scale(1.42857,1.42857) translate(-185.314,-270.823)" width="10" x="617.7142857142859" y="902.7441860465115"/></g>
  <g id="274">
   <use class="kv10" height="20" transform="rotate(0,1268.75,918.141) scale(1.42857,1.42857) translate(-378.481,-271.157)" width="10" x="1261.603174603175" xlink:href="#GroundDisconnector:地刀_0" y="903.8552971576225" zvalue="264"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450148237317" ObjectName="10kV1号接地变05267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450148237317"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1268.75,918.141) scale(1.42857,1.42857) translate(-378.481,-271.157)" width="10" x="1261.603174603175" y="903.8552971576225"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="187">
   <use class="kv10" height="30" transform="rotate(0,778.412,946.176) scale(-1.33333,-1.33333) translate(-1359.72,-1650.81)" width="15" x="768.4117647058823" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="926.1764705882351" zvalue="165"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450146598917" ObjectName="10kV芒弄隧道线"/>
   <cge:TPSR_Ref TObjectID="6192450146598917"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,778.412,946.176) scale(-1.33333,-1.33333) translate(-1359.72,-1650.81)" width="15" x="768.4117647058823" y="926.1764705882351"/></g>
  <g id="217">
   <use class="kv10" height="30" transform="rotate(0,1123.41,946.176) scale(-1.33333,-1.33333) translate(-1963.47,-1650.81)" width="15" x="1113.411764705882" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="926.1764705882351" zvalue="201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450146992133" ObjectName="10kV老寨河大桥线"/>
   <cge:TPSR_Ref TObjectID="6192450146992133"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1123.41,946.176) scale(-1.33333,-1.33333) translate(-1963.47,-1650.81)" width="15" x="1113.411764705882" y="926.1764705882351"/></g>
  <g id="309">
   <use class="kv35" height="30" transform="rotate(0,1545,213.714) scale(2,-2) translate(-762.5,-305.571)" width="20" x="1525" xlink:href="#EnergyConsumer:回贤变站用变_0" y="183.7142857142858" zvalue="361"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450148499461" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1545,213.714) scale(2,-2) translate(-762.5,-305.571)" width="20" x="1525" y="183.7142857142858"/></g>
  <g id="313">
   <use class="kv10" height="30" transform="rotate(0,1600,827.643) scale(2.07143,2.07143) translate(-816.872,-412.02)" width="20" x="1579.285714285714" xlink:href="#EnergyConsumer:回贤变站用变_0" y="796.5714285714286" zvalue="364"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450148564997" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1600,827.643) scale(2.07143,2.07143) translate(-816.872,-412.02)" width="20" x="1579.285714285714" y="796.5714285714286"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="206">
   <use class="kv10" height="60" transform="rotate(0,527.714,945.143) scale(0.714286,0.714286) translate(206.8,369.486)" width="30" x="517.0000000000001" xlink:href="#Compensator:电容器0922_0" y="923.7142857142857" zvalue="184"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450146861061" ObjectName="10kV2号电容器"/>
   <cge:TPSR_Ref TObjectID="6192450146861061"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,527.714,945.143) scale(0.714286,0.714286) translate(206.8,369.486)" width="30" x="517.0000000000001" y="923.7142857142857"/></g>
  <g id="230">
   <use class="kv10" height="60" transform="rotate(0,1469.38,946.254) scale(0.714286,0.714286) translate(583.467,369.93)" width="30" x="1458.666666666667" xlink:href="#Compensator:电容器0922_0" y="924.8253968253968" zvalue="217"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450147254277" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192450147254277"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1469.38,946.254) scale(0.714286,0.714286) translate(583.467,369.93)" width="30" x="1458.666666666667" y="924.8253968253968"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="200">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="200" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,158,166.111) scale(1,1) translate(0,0)" writing-mode="lr" x="158.2" xml:space="preserve" y="172.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127376846852" ObjectName="F"/>
   </metadata>
  </g>
  <g id="190">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="190" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,149.778,187.778) scale(1,1) translate(0,0)" writing-mode="lr" x="149.97" xml:space="preserve" y="194.19" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127377895428" ObjectName="F"/>
   </metadata>
  </g>
  <g id="186">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="186" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,142.889,211.083) scale(1,1) translate(-2.00774e-14,0)" writing-mode="lr" x="143.12" xml:space="preserve" y="217.57" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127383990276" ObjectName="油温"/>
   </metadata>
  </g>
  <g id="183">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,142.889,234.083) scale(1,1) translate(0,0)" writing-mode="lr" x="143.09" xml:space="preserve" y="240.57" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127383597060" ObjectName="Tap"/>
   </metadata>
  </g>
  <g id="205">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="205" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,144,139) scale(1,1) translate(0,0)" writing-mode="lr" x="144.15" xml:space="preserve" y="145.39" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127395065860" ObjectName=""/>
   </metadata>
  </g>
  <g id="220">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="220" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,321,141) scale(1,1) translate(0,0)" writing-mode="lr" x="321.15" xml:space="preserve" y="147.39" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127395131396" ObjectName=""/>
   </metadata>
  </g>
  <g id="262">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,319.889,211.083) scale(1,1) translate(-5.93793e-14,0)" writing-mode="lr" x="320.12" xml:space="preserve" y="217.57" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127378944004" ObjectName="油温"/>
   </metadata>
  </g>
  <g id="235">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="235" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,319.889,234.083) scale(1,1) translate(0,0)" writing-mode="lr" x="320.09" xml:space="preserve" y="240.57" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127378550788" ObjectName="Tap"/>
   </metadata>
  </g>
  <g id="281">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="281" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,341,165.111) scale(1,1) translate(0,0)" writing-mode="lr" x="341.2" xml:space="preserve" y="171.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127376322564" ObjectName="F"/>
   </metadata>
  </g>
  <g id="282">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="282" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,330.778,187.778) scale(1,1) translate(0,0)" writing-mode="lr" x="330.97" xml:space="preserve" y="194.19" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127377371140" ObjectName="F"/>
   </metadata>
  </g>
  <g id="1182">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1182" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,124.135,503.964) scale(1,1) translate(-2.19384e-14,-1.09571e-13)" writing-mode="lr" x="124.24" xml:space="preserve" y="508.87" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127376519172" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="1181">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="1181" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,124.135,458.409) scale(1,1) translate(-2.19384e-14,0)" writing-mode="lr" x="124.24" xml:space="preserve" y="463.32" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127376715780" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1180">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1180" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,124.135,481.742) scale(1,1) translate(-2.19384e-14,0)" writing-mode="lr" x="124.24" xml:space="preserve" y="486.65" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127376453636" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="1179">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="1179" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,124.135,550.492) scale(1,1) translate(-2.19384e-14,0)" writing-mode="lr" x="124.24" xml:space="preserve" y="555.4" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127376912388" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="1178">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="1178" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,124.135,528.548) scale(1,1) translate(-2.19384e-14,0)" writing-mode="lr" x="124.24" xml:space="preserve" y="533.46" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127376584708" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="285">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="285" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,185.135,503.964) scale(1,1) translate(-3.54831e-14,-1.09571e-13)" writing-mode="lr" x="185.24" xml:space="preserve" y="508.87" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127375994884" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="196">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="196" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,185.135,458.409) scale(1,1) translate(-3.54831e-14,0)" writing-mode="lr" x="185.24" xml:space="preserve" y="463.32" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127376191492" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="284">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="284" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,185.135,481.742) scale(1,1) translate(-3.54831e-14,0)" writing-mode="lr" x="185.24" xml:space="preserve" y="486.65" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127375929348" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="287">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="287" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,185.135,550.492) scale(1,1) translate(-3.54831e-14,0)" writing-mode="lr" x="185.24" xml:space="preserve" y="555.4" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127376388100" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="286">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="286" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,185.135,528.548) scale(1,1) translate(-3.54831e-14,0)" writing-mode="lr" x="185.24" xml:space="preserve" y="533.46" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127376060420" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="290">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="290" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,250.135,503.964) scale(1,1) translate(-4.9916e-14,-1.09571e-13)" writing-mode="lr" x="250.24" xml:space="preserve" y="508.87" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127377567748" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="288">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="288" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,250.135,458.409) scale(1,1) translate(-4.9916e-14,0)" writing-mode="lr" x="250.24" xml:space="preserve" y="463.32" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127377764356" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="289">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="289" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,250.135,481.742) scale(1,1) translate(-4.9916e-14,0)" writing-mode="lr" x="250.24" xml:space="preserve" y="486.65" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127377502212" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="292">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="292" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,250.135,550.492) scale(1,1) translate(-4.9916e-14,0)" writing-mode="lr" x="250.24" xml:space="preserve" y="555.4" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127377960964" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="291">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="291" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,250.135,528.548) scale(1,1) translate(-4.9916e-14,0)" writing-mode="lr" x="250.24" xml:space="preserve" y="533.46" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127377633284" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="295">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="295" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,311.135,503.964) scale(1,1) translate(-6.34607e-14,-1.09571e-13)" writing-mode="lr" x="311.24" xml:space="preserve" y="508.87" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127377043460" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="293">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="293" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,311.135,458.409) scale(1,1) translate(-6.34607e-14,0)" writing-mode="lr" x="311.24" xml:space="preserve" y="463.32" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127377240068" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="294">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="294" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,311.135,481.742) scale(1,1) translate(-6.34607e-14,0)" writing-mode="lr" x="311.24" xml:space="preserve" y="486.65" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127376977924" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="297">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="297" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,311.135,550.492) scale(1,1) translate(-6.34607e-14,0)" writing-mode="lr" x="311.24" xml:space="preserve" y="555.4" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127377436676" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="296">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="296" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,311.135,528.548) scale(1,1) translate(-6.34607e-14,0)" writing-mode="lr" x="311.24" xml:space="preserve" y="533.46" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127377108996" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="133">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="133" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,704.25,443.5) scale(1,1) translate(0,0)" writing-mode="lr" x="703.7" xml:space="preserve" y="449.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127378026500" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="134" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,706.25,465.5) scale(1,1) translate(0,0)" writing-mode="lr" x="705.7" xml:space="preserve" y="471.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127378092036" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="137" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,705.25,589.5) scale(1,1) translate(0,0)" writing-mode="lr" x="704.7" xml:space="preserve" y="595.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127378157572" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="138" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,709.25,620.5) scale(1,1) translate(0,0)" writing-mode="lr" x="708.7" xml:space="preserve" y="626.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127378223108" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="141" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,707.25,493.5) scale(1,1) translate(0,0)" writing-mode="lr" x="706.7" xml:space="preserve" y="499.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127378288644" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="144" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,718.25,660.5) scale(1,1) translate(0,0)" writing-mode="lr" x="717.7" xml:space="preserve" y="666.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127378616324" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="240" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1306.25,383.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1305.7" xml:space="preserve" y="389.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127383072772" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="241" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1307.25,417.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1306.7" xml:space="preserve" y="423.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127383138308" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="242">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="242" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1298.25,589.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1297.7" xml:space="preserve" y="595.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127383203844" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="243">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="243" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1301.25,623.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1300.7" xml:space="preserve" y="629.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127383269380" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="244" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1312.25,455.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1311.7" xml:space="preserve" y="461.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127383334916" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="257" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1304.25,657.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1303.7" xml:space="preserve" y="663.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127383662596" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="258">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="258" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,468.75,713.75) scale(1,1) translate(0,0)" writing-mode="lr" x="468.28" xml:space="preserve" y="718.53" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127377174532" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="260">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="260" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,461.286,364.5) scale(1,1) translate(2.77302e-13,0)" writing-mode="lr" x="460.82" xml:space="preserve" y="369.28" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127376125956" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="261">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="261" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1743,312.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1742.53" xml:space="preserve" y="317.28" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127376650244" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="263">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="263" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1714,710.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1713.53" xml:space="preserve" y="715.53" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127377698820" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="298">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="298" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1201,38) scale(1,1) translate(0,0)" writing-mode="lr" x="1200.53" xml:space="preserve" y="42.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127385628676" ObjectName="P"/>
   </metadata>
  </g>
  <g id="299">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="299" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1199,67) scale(1,1) translate(0,0)" writing-mode="lr" x="1198.53" xml:space="preserve" y="71.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127385694212" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="300">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="300" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1202,98) scale(1,1) translate(0,0)" writing-mode="lr" x="1201.53" xml:space="preserve" y="102.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127385759748" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="302">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="302" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1119.41,996.676) scale(1,1) translate(0,0)" writing-mode="lr" x="1118.94" xml:space="preserve" y="1001.45" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127390674948" ObjectName="P"/>
   </metadata>
  </g>
  <g id="305">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="305" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1120.41,1018.68) scale(1,1) translate(0,0)" writing-mode="lr" x="1119.94" xml:space="preserve" y="1023.45" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127390740484" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="306">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="306" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1124.41,1039.68) scale(1,1) translate(0,1.70227e-12)" writing-mode="lr" x="1123.94" xml:space="preserve" y="1044.45" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127390806020" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="307">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="307" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,780.412,994.676) scale(1,1) translate(0,0)" writing-mode="lr" x="779.9400000000001" xml:space="preserve" y="999.45" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127387987972" ObjectName="P"/>
   </metadata>
  </g>
  <g id="308">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="308" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,780.412,1018.68) scale(1,1) translate(0,0)" writing-mode="lr" x="779.9400000000001" xml:space="preserve" y="1023.45" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127388053508" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="316">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="316" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,782.412,1036.68) scale(1,1) translate(0,1.69727e-12)" writing-mode="lr" x="781.9400000000001" xml:space="preserve" y="1041.45" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127388119044" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="317">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="317" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,974.091,444.43) scale(1,1) translate(0,0)" writing-mode="lr" x="973.62" xml:space="preserve" y="449.21" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127381106692" ObjectName="P"/>
   </metadata>
  </g>
  <g id="318">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="318" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,974.091,468.43) scale(1,1) translate(0,-1.00127e-13)" writing-mode="lr" x="973.62" xml:space="preserve" y="473.21" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127381172228" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="319">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="319" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,977.091,494.43) scale(1,1) translate(0,0)" writing-mode="lr" x="976.62" xml:space="preserve" y="499.21" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127381237764" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="320">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="320" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,953.818,860.455) scale(1,1) translate(0,0)" writing-mode="lr" x="953.35" xml:space="preserve" y="865.23" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127386546180" ObjectName="P"/>
   </metadata>
  </g>
  <g id="321">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="321" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,950.818,890.455) scale(1,1) translate(0,0)" writing-mode="lr" x="950.35" xml:space="preserve" y="895.23" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127386611716" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="322">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="322" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,950.818,911.455) scale(1,1) translate(0,0)" writing-mode="lr" x="950.35" xml:space="preserve" y="916.23" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127386677252" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="323">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="323" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,521.964,997.071) scale(1,1) translate(0,0)" writing-mode="lr" x="521.5" xml:space="preserve" y="1001.85" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127389429764" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="324">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="324" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,522.714,1020.07) scale(1,1) translate(0,0)" writing-mode="lr" x="522.25" xml:space="preserve" y="1024.85" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127389495300" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="325">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="325" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1465.13,1000.18) scale(1,1) translate(0,0)" writing-mode="lr" x="1464.66" xml:space="preserve" y="1004.96" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127392116740" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="326">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="326" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1468.13,1026.18) scale(1,1) translate(0,-1.56781e-12)" writing-mode="lr" x="1467.66" xml:space="preserve" y="1030.96" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127392182276" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="327">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="327" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1293.64,985.566) scale(1,1) translate(0,0)" writing-mode="lr" x="1293.17" xml:space="preserve" y="990.34" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127393558532" ObjectName="P"/>
   </metadata>
  </g>
  <g id="328">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="328" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1296.14,1008.82) scale(1,1) translate(0,0)" writing-mode="lr" x="1295.67" xml:space="preserve" y="1013.59" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127393624068" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="329">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="329" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1297.39,1024.32) scale(1,1) translate(0,0)" writing-mode="lr" x="1296.92" xml:space="preserve" y="1029.09" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127393361924" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="330">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="330" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,647.25,984.455) scale(1,1) translate(0,0)" writing-mode="lr" x="646.78" xml:space="preserve" y="989.23" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127390347268" ObjectName="P"/>
   </metadata>
  </g>
  <g id="331">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="331" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,647.25,1003.95) scale(1,1) translate(0,0)" writing-mode="lr" x="646.78" xml:space="preserve" y="1008.73" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127390412804" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="332">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="332" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,649.75,1024.45) scale(1,1) translate(0,0)" writing-mode="lr" x="649.28" xml:space="preserve" y="1029.23" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127390150660" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,318.625,282.5) scale(0.708333,0.665547) translate(126.824,136.946)" width="30" x="308" xlink:href="#State:红绿圆(方形)_0" y="272.52" zvalue="313"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374893645827" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,318.625,282.5) scale(0.708333,0.665547) translate(126.824,136.946)" width="30" x="308" y="272.52"/></g>
  <g id="221">
   <use height="30" transform="rotate(0,203,282.5) scale(0.708333,0.665547) translate(79.2132,136.946)" width="30" x="192.38" xlink:href="#State:红绿圆(方形)_0" y="272.52" zvalue="314"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562952899198983" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,203,282.5) scale(0.708333,0.665547) translate(79.2132,136.946)" width="30" x="192.38" y="272.52"/></g>
 </g>
</svg>