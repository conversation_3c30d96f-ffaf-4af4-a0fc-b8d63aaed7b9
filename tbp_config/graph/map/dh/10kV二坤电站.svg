<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549592260610" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV二坤电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="50.21" xlink:href="logo.png" y="47.57"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.214,79.5714) scale(1,1) translate(0,0)" writing-mode="lr" x="199.21" xml:space="preserve" y="83.06999999999999" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,201.714,77.2618) scale(1,1) translate(0,0)" writing-mode="lr" x="201.71" xml:space="preserve" y="86.26000000000001" zvalue="5">10kV二坤电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="19" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,81.625,204) scale(1,1) translate(0,0)" width="72.88" x="45.19" y="192" zvalue="100"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,81.625,204) scale(1,1) translate(0,0)" writing-mode="lr" x="81.63" xml:space="preserve" y="208.5" zvalue="100">信号一览</text>
  <line fill="none" id="53" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384.2142857142856" x2="384.2142857142856" y1="15.57142857142867" y2="1045.571428571429" zvalue="6"/>
  <line fill="none" id="51" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="151.441921185511" y2="151.441921185511" zvalue="8"/>
  <line fill="none" id="50" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="621.441921185511" y2="621.441921185511" zvalue="9"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="936.5714285714287" y2="936.5714285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="975.7347285714286" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="370.2142857142856" y1="936.5714285714287" y2="936.5714285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="370.2142857142856" y1="975.7347285714286" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142856" x2="190.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="190.2142857142857" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142857" x2="280.2142857142857" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="280.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142856" x2="190.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="190.2142857142857" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142857" x2="280.2142857142857" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="280.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.2143,956.571) scale(1,1) translate(0,0)" writing-mode="lr" x="55.21" xml:space="preserve" y="962.5700000000001" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.2143,990.571) scale(1,1) translate(0,0)" writing-mode="lr" x="52.21" xml:space="preserve" y="996.5700000000001" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.214,990.571) scale(1,1) translate(0,0)" writing-mode="lr" x="234.21" xml:space="preserve" y="996.5700000000001" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.2143,1018.57) scale(1,1) translate(0,0)" writing-mode="lr" x="51.21" xml:space="preserve" y="1024.57" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.214,1018.57) scale(1,1) translate(0,0)" writing-mode="lr" x="233.21" xml:space="preserve" y="1024.57" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,75.7143,651.071) scale(1,1) translate(0,2.10522e-13)" writing-mode="lr" x="75.71428571428555" xml:space="preserve" y="655.5714285714286" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.268,958.571) scale(1,1) translate(0,0)" writing-mode="lr" x="235.27" xml:space="preserve" y="964.5700000000001" zvalue="27">ErKun-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,794.092,434.019) scale(1,1) translate(0,0)" writing-mode="lr" x="794.09" xml:space="preserve" y="438.52" zvalue="31">#1主变315kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,756.061,867.99) scale(1,1) translate(0,2.82185e-13)" writing-mode="lr" x="756.0606713030318" xml:space="preserve" y="872.49045413687" zvalue="33">#1发电机200kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,765.524,629.425) scale(1,1) translate(0,0)" writing-mode="lr" x="765.52" xml:space="preserve" y="633.92" zvalue="36">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,772.271,716.814) scale(1,1) translate(0,0)" writing-mode="lr" x="772.27" xml:space="preserve" y="721.3099999999999" zvalue="38">4011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1174.22,573.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1174.22" xml:space="preserve" y="578.17" zvalue="42">0.4kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1012.06,871.99) scale(1,1) translate(0,2.83517e-13)" writing-mode="lr" x="1012.060671303032" xml:space="preserve" y="876.49045413687" zvalue="49">#2发电机200kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1025.64,641.202) scale(1,1) translate(0,0)" writing-mode="lr" x="1025.64" xml:space="preserve" y="645.7" zvalue="52">402</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1028.39,722.591) scale(1,1) translate(0,0)" writing-mode="lr" x="1028.39" xml:space="preserve" y="727.09" zvalue="54">4021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,900.094,339.258) scale(1,1) translate(-1.96197e-13,0)" writing-mode="lr" x="900.09" xml:space="preserve" y="343.76" zvalue="64">0716</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,866.26,240.444) scale(1,1) translate(0,0)" writing-mode="lr" x="866.26" xml:space="preserve" y="244.94" zvalue="66">10kV允罕开发区线</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="265.75" y2="265.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="291.75" y2="291.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="265.75" y2="291.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="265.75" y2="291.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="265.75" y2="265.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="291.75" y2="291.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="265.75" y2="291.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="265.75" y2="291.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="291.75" y2="291.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="316" y2="316"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="291.75" y2="316"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="291.75" y2="316"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="291.75" y2="291.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="316" y2="316"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="291.75" y2="316"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="291.75" y2="316"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="316" y2="316"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="338.75" y2="338.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="316" y2="338.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="316" y2="338.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="316" y2="316"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="338.75" y2="338.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="316" y2="338.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="316" y2="338.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="338.75" y2="338.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="361.5" y2="361.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="338.75" y2="361.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="338.75" y2="361.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="338.75" y2="338.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="361.5" y2="361.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="338.75" y2="361.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="338.75" y2="361.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="361.5" y2="361.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="384.25" y2="384.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="361.5" y2="384.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="361.5" y2="384.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="361.5" y2="361.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="384.25" y2="384.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="361.5" y2="384.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="361.5" y2="384.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="384.25" y2="384.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="407" y2="407"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="384.25" y2="407"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="384.25" y2="407"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="384.25" y2="384.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="407" y2="407"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="384.25" y2="407"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="384.25" y2="407"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="407" y2="407"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="429.75" y2="429.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="407" y2="429.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="407" y2="429.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="407" y2="407"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="429.75" y2="429.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="407" y2="429.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="407" y2="429.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,197.399,204.591) scale(1,1) translate(0,0)" writing-mode="lr" x="197.4" xml:space="preserve" y="209.09" zvalue="91">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,302.399,204.591) scale(1,1) translate(0,0)" writing-mode="lr" x="302.4" xml:space="preserve" y="209.09" zvalue="92">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.5,278.75) scale(1,1) translate(0,0)" writing-mode="lr" x="12" xml:space="preserve" y="283.25" zvalue="93">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235,278.75) scale(1,1) translate(0,0)" writing-mode="lr" x="192.5" xml:space="preserve" y="283.25" zvalue="94">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.6875,352) scale(1,1) translate(0,0)" writing-mode="lr" x="57.69" xml:space="preserve" y="356.5" zvalue="95">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,53.5,304.75) scale(1,1) translate(0,0)" writing-mode="lr" x="11" xml:space="preserve" y="309.25" zvalue="101">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,234,304.75) scale(1,1) translate(0,0)" writing-mode="lr" x="191.5" xml:space="preserve" y="309.25" zvalue="102">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.6875,398) scale(1,1) translate(0,0)" writing-mode="lr" x="54.69" xml:space="preserve" y="402.5" zvalue="105">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.688,397) scale(1,1) translate(0,0)" writing-mode="lr" x="222.69" xml:space="preserve" y="401.5" zvalue="106">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.6875,421) scale(1,1) translate(0,0)" writing-mode="lr" x="54.69" xml:space="preserve" y="425.5" zvalue="107">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.688,420) scale(1,1) translate(0,0)" writing-mode="lr" x="222.69" xml:space="preserve" y="424.5" zvalue="108">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,53.5,327.75) scale(1,1) translate(0,0)" writing-mode="lr" x="11" xml:space="preserve" y="332.25" zvalue="109">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,233.5,326.75) scale(1,1) translate(0,0)" writing-mode="lr" x="191" xml:space="preserve" y="331.25" zvalue="111">厂用电率</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="45.19" y="192" zvalue="100"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="PowerTransformer2Class">
  <g id="414">
   <g id="4140">
    <use class="kv10" height="60" transform="rotate(0,864.557,417.771) scale(1.16106,1.11219) translate(-116.71,-38.776)" width="40" x="841.34" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="384.41" zvalue="30"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874453614595" ObjectName="10"/>
    </metadata>
   </g>
   <g id="4141">
    <use class="v400" height="60" transform="rotate(0,864.557,417.771) scale(1.16106,1.11219) translate(-116.71,-38.776)" width="40" x="841.34" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="384.41" zvalue="30"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874453680131" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399458750467" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399458750467"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,864.557,417.771) scale(1.16106,1.11219) translate(-116.71,-38.776)" width="40" x="841.34" y="384.41"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="212">
   <use class="v400" height="30" transform="rotate(0,746.215,805.616) scale(1.52192,1.52192) translate(-248.076,-268.446)" width="30" x="723.3863709131574" xlink:href="#Generator:发电机_0" y="782.7867361713461" zvalue="32"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450010742789" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192450010742789"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,746.215,805.616) scale(1.52192,1.52192) translate(-248.076,-268.446)" width="30" x="723.3863709131574" y="782.7867361713461"/></g>
  <g id="62">
   <use class="v400" height="30" transform="rotate(0,1001.25,808.649) scale(1.45749,1.45749) translate(-307.419,-246.964)" width="30" x="979.3863709131574" xlink:href="#Generator:发电机_0" y="786.7867361713459" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450010611717" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192450010611717"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1001.25,808.649) scale(1.45749,1.45749) translate(-307.419,-246.964)" width="30" x="979.3863709131574" y="786.7867361713459"/></g>
 </g>
 <g id="BreakerClass">
  <g id="108">
   <use class="v400" height="20" transform="rotate(0,746.163,630.425) scale(1.22222,1.11111) translate(-134.555,-61.9313)" width="10" x="740.0522020423972" xlink:href="#Breaker:开关_0" y="619.3134920634919" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924555112452" ObjectName="#1发电机401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924555112452"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,746.163,630.425) scale(1.22222,1.11111) translate(-134.555,-61.9313)" width="10" x="740.0522020423972" y="619.3134920634919"/></g>
  <g id="61">
   <use class="v400" height="20" transform="rotate(0,1002.28,642.202) scale(1.22222,1.11111) translate(-181.121,-63.1091)" width="10" x="996.1672595306528" xlink:href="#Breaker:开关_0" y="631.0912698412696" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924555046916" ObjectName="#2发电机402断路器"/>
   <cge:TPSR_Ref TObjectID="6473924555046916"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1002.28,642.202) scale(1.22222,1.11111) translate(-181.121,-63.1091)" width="10" x="996.1672595306528" y="631.0912698412696"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="109">
   <use class="v400" height="30" transform="rotate(0,746.313,717.814) scale(-1.11111,-0.814815) translate(-1417.16,-1601.54)" width="15" x="737.9794217728852" xlink:href="#Disconnector:刀闸_0" y="705.5912835106017" zvalue="35"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450010677253" ObjectName="#1发电机4011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450010677253"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,746.313,717.814) scale(-1.11111,-0.814815) translate(-1417.16,-1601.54)" width="15" x="737.9794217728852" y="705.5912835106017"/></g>
  <g id="60">
   <use class="v400" height="30" transform="rotate(0,1002.43,723.591) scale(-1.11111,-0.814815) translate(-1903.78,-1614.41)" width="15" x="994.0944792611407" xlink:href="#Disconnector:刀闸_0" y="711.3690612883794" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450010546181" ObjectName="#2发电机4021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450010546181"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1002.43,723.591) scale(-1.11111,-0.814815) translate(-1903.78,-1614.41)" width="15" x="994.0944792611407" y="711.3690612883794"/></g>
  <g id="67">
   <use class="kv10" height="30" transform="rotate(0,866.358,335.814) scale(-1.11111,-0.814815) translate(-1645.25,-750.726)" width="15" x="858.0243096775781" xlink:href="#Disconnector:刀闸_0" y="323.5912835106016" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450010480645" ObjectName="10kV允罕开发区线线0716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450010480645"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,866.358,335.814) scale(-1.11111,-0.814815) translate(-1645.25,-750.726)" width="15" x="858.0243096775781" y="323.5912835106016"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="43">
   <path class="v400" d="M 614.44 543.56 L 1201.11 543.56" stroke-width="6" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674253668356" ObjectName="0.4kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674253668356"/></metadata>
  <path d="M 614.44 543.56 L 1201.11 543.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="65">
   <path class="v400" d="M 864.56 450.67 L 864.56 543.56" stroke-width="1" zvalue="73"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@1" LinkObjectIDznd="43@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 864.56 450.67 L 864.56 543.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv10" d="M 866.26 347.63 L 866.26 385.01" stroke-width="1" zvalue="74"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="414@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.26 347.63 L 866.26 385.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv10" d="M 866.29 323.8 L 866.26 286.51" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@1" LinkObjectIDznd="69@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.29 323.8 L 866.26 286.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="v400" d="M 746.22 783.17 L 746.22 729.63" stroke-width="1" zvalue="82"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="109@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 746.22 783.17 L 746.22 729.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="v400" d="M 746.24 705.8 L 746.24 641.04" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@1" LinkObjectIDznd="108@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 746.24 705.8 L 746.24 641.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="v400" d="M 746.12 619.79 L 746.12 543.56" stroke-width="1" zvalue="85"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="43@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 746.12 619.79 L 746.12 543.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="v400" d="M 1002.24 631.57 L 1002.24 543.56" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="43@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1002.24 631.57 L 1002.24 543.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="v400" d="M 1002.36 652.81 L 1002.36 711.58" stroke-width="1" zvalue="87"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@1" LinkObjectIDznd="60@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1002.36 652.81 L 1002.36 711.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="v400" d="M 1002.33 735.41 L 1002.33 787.15" stroke-width="1" zvalue="88"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="62@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1002.33 735.41 L 1002.33 787.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="MeasurementClass">
  <g id="274">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="274" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,154.611,350.917) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="355.83" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="273">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="273" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,154.611,278.917) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="283.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126607126532" ObjectName="F"/>
   </metadata>
  </g>
  <g id="272">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="272" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,332.222,279.917) scale(1,1) translate(0,0)" writing-mode="lr" x="332.38" xml:space="preserve" y="284.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126607192068" ObjectName="F"/>
   </metadata>
  </g>
  <g id="260">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="260" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,154.611,303.917) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="308.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126606995460" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,332.222,304.917) scale(1,1) translate(0,0)" writing-mode="lr" x="332.38" xml:space="preserve" y="309.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126607060996" ObjectName="F"/>
   </metadata>
  </g>
  <g id="209">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="209" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,154.611,327.917) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="332.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126606995460" ObjectName="F"/>
   </metadata>
  </g>
  <g id="198">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,334.611,326.917) scale(1,1) translate(0,0)" writing-mode="lr" x="334.77" xml:space="preserve" y="331.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126606995460" ObjectName="F"/>
   </metadata>
  </g>
  <g id="287">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="287" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,154.611,394.917) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="399.83" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="288">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="288" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,330.611,394.917) scale(1,1) translate(0,0)" writing-mode="lr" x="330.77" xml:space="preserve" y="399.83" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="雨量采集"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="271">
   <use height="30" transform="rotate(0,329.673,205.107) scale(0.708333,0.665547) translate(131.373,98.0545)" width="30" x="319.05" xlink:href="#State:红绿圆(方形)_0" y="195.12" zvalue="99"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.673,205.107) scale(0.708333,0.665547) translate(131.373,98.0545)" width="30" x="319.05" y="195.12"/></g>
  <g id="286">
   <use height="30" transform="rotate(0,234.048,205.107) scale(0.708333,0.665547) translate(91.9975,98.0545)" width="30" x="223.42" xlink:href="#State:红绿圆(方形)_0" y="195.12" zvalue="113"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,234.048,205.107) scale(0.708333,0.665547) translate(91.9975,98.0545)" width="30" x="223.42" y="195.12"/></g>
 </g>
</svg>