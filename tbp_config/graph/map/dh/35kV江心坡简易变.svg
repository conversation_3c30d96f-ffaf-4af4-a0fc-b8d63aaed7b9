<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549597372418" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="PowerTransformer2:Vertical_0" viewBox="0,0,50,85">
   <use terminal-index="0" type="1" x="25" xlink:href="#terminal" y="1.5"/>
   <ellipse cx="25" cy="25" fill-opacity="0" rx="24" ry="24" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Vertical_1" viewBox="0,0,50,85">
   <use terminal-index="1" type="1" x="25.66666666666666" xlink:href="#terminal" y="84.83333333333333"/>
   <ellipse cx="25" cy="60" fill-opacity="0" rx="24" ry="24" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV江心坡简易变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="137.81" y="381" zvalue="1587"/></g>
  <g href="全站公用20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="38" y="308" zvalue="1588"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="38" y="381" zvalue="1589"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="38" y="343.5" zvalue="1591"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="141" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,174.25,393) scale(1,1) translate(0,0)" width="72.88" x="137.81" y="381" zvalue="1587"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,174.25,393) scale(1,1) translate(0,0)" writing-mode="lr" x="174.25" xml:space="preserve" y="397.5" zvalue="1587">小电流接地</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="140" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,74.4375,320) scale(1,1) translate(0,0)" width="72.88" x="38" y="308" zvalue="1588"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.4375,320) scale(1,1) translate(0,0)" writing-mode="lr" x="74.44" xml:space="preserve" y="324.5" zvalue="1588">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="139" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,74.4375,393) scale(1,1) translate(0,0)" width="72.88" x="38" y="381" zvalue="1589"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.4375,393) scale(1,1) translate(0,0)" writing-mode="lr" x="74.44" xml:space="preserve" y="397.5" zvalue="1589">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="137" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,74.4375,355.5) scale(1,1) translate(0,0)" width="72.88" x="38" y="343.5" zvalue="1591"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.4375,355.5) scale(1,1) translate(0,0)" writing-mode="lr" x="74.44" xml:space="preserve" y="360" zvalue="1591">信号一览</text>
  <line fill="none" id="67" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382" x2="382" y1="11" y2="1041" zvalue="58"/>
  <line fill="none" id="666" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="146.8704926140824" y2="146.8704926140824" zvalue="1056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="159" y2="159"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="159" y2="159"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="277.5" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="254.75" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="254.75" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="277.5" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="254.75" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="254.75" y2="277.5"/>
  <line fill="none" id="613" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="616.8704926140824" y2="616.8704926140824" zvalue="1058"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="999.0816" y2="1027"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="609" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,952) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="958" zvalue="1062">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="608" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50,986) scale(1,1) translate(0,0)" writing-mode="lr" x="50" xml:space="preserve" y="992" zvalue="1063">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="607" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232,986) scale(1,1) translate(0,0)" writing-mode="lr" x="232" xml:space="preserve" y="992" zvalue="1064">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="606" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="49" xml:space="preserve" y="1020" zvalue="1065">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="605" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="231" xml:space="preserve" y="1020" zvalue="1066">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="602" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.5,646.5) scale(1,1) translate(0,0)" writing-mode="lr" x="73.5" xml:space="preserve" y="651" zvalue="1069">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="599" stroke="rgb(255,255,255)" text-anchor="middle" x="203.3359375" xml:space="preserve" y="454.5" zvalue="1072">6.3kV  </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="599" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="203.3359375" xml:space="preserve" y="471.5" zvalue="1072">母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="598" stroke="rgb(255,255,255)" text-anchor="middle" x="327.34375" xml:space="preserve" y="455.5" zvalue="1073">10kV    母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="598" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="327.34375" xml:space="preserve" y="472.5" zvalue="1073">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="596" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85,489.5) scale(1,1) translate(0,0)" writing-mode="lr" x="85" xml:space="preserve" y="494" zvalue="1075">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="595" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85,515) scale(1,1) translate(0,0)" writing-mode="lr" x="85" xml:space="preserve" y="519.5" zvalue="1076">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="594" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85,540.5) scale(1,1) translate(0,0)" writing-mode="lr" x="85" xml:space="preserve" y="545" zvalue="1077">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="593" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84,565) scale(1,1) translate(0,0)" writing-mode="lr" x="84" xml:space="preserve" y="569.5" zvalue="1078">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="592" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85,591.5) scale(1,1) translate(0,0)" writing-mode="lr" x="85" xml:space="preserve" y="596" zvalue="1079">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.054,954) scale(1,1) translate(0,0)" writing-mode="lr" x="233.05" xml:space="preserve" y="960" zvalue="1080">JiangXinPoJianYi-01-2024</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,143.054,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="143.05" xml:space="preserve" y="1020" zvalue="1081">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,323.054,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="323.05" xml:space="preserve" y="1020" zvalue="1082">20240730</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47,173) scale(1,1) translate(0,0)" writing-mode="lr" x="47" xml:space="preserve" y="177.5" zvalue="1083">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227,173) scale(1,1) translate(0,0)" writing-mode="lr" x="227" xml:space="preserve" y="177.5" zvalue="1084">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.6875,196.25) scale(1,1) translate(0,0)" writing-mode="lr" x="54.69" xml:space="preserve" y="200.75" zvalue="1085">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.125,197.25) scale(1,1) translate(0,0)" writing-mode="lr" x="234.13" xml:space="preserve" y="201.75" zvalue="1086">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.1875,245) scale(1,1) translate(0,0)" writing-mode="lr" x="54.19" xml:space="preserve" y="249.5" zvalue="1087">10kV#1变油温</text>
  <image height="81" id="5" preserveAspectRatio="xMidYMid slice" width="266" x="53" xlink:href="logo.png" y="38"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,186,78.5) scale(1,1) translate(0,0)" writing-mode="lr" x="186" xml:space="preserve" y="83" zvalue="1439"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,190.961,320.591) scale(1,1) translate(0,0)" writing-mode="lr" x="190.96" xml:space="preserve" y="325.09" zvalue="1583">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,295.961,320.591) scale(1,1) translate(0,0)" writing-mode="lr" x="295.96" xml:space="preserve" y="325.09" zvalue="1584">通道</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="19" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,215.5,76.25) scale(1,1) translate(0,0)" writing-mode="lr" x="215.5" xml:space="preserve" y="82.75" zvalue="1710">35kV江心坡简易变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1152,387.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1152" xml:space="preserve" y="392.38" zvalue="1714">3016</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1208.81,529) scale(1,1) translate(0,0)" writing-mode="lr" x="1208.8125" xml:space="preserve" y="533.5" zvalue="1715">1号主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1053.62,696.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1053.63" xml:space="preserve" y="701.25" zvalue="1716">10kV江心线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1171.5,247) scale(1,1) translate(0,0)" writing-mode="lr" x="1171.5" xml:space="preserve" y="251.5" zvalue="1725">35kV象旧线#27</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1083,207) scale(1,1) translate(0,0)" writing-mode="lr" x="1083" xml:space="preserve" y="211.5" zvalue="1725">35kV象旧线江心坡简易变T线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1212,551.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1212" xml:space="preserve" y="556" zvalue="1736">S9-2000/35</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1135.38,697.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1135.38" xml:space="preserve" y="702.25" zvalue="1736">10kV江平线</text>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="235">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="235" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,205.222,489.667) scale(1,1) translate(-3.9598e-14,0)" writing-mode="lr" x="205.34" xml:space="preserve" y="494.58" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,146.222,172.167) scale(1,1) translate(0,0)" writing-mode="lr" x="146.42" xml:space="preserve" y="177.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128376532996" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,325.222,173.167) scale(1,1) translate(0,0)" writing-mode="lr" x="325.42" xml:space="preserve" y="178.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128376598532" ObjectName="LOAD_QSUM"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="138" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,145,196.5) scale(1,1) translate(0,0)" writing-mode="lr" x="145.2" xml:space="preserve" y="201.41" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,325,197.5) scale(1,1) translate(0,0)" writing-mode="lr" x="325.2" xml:space="preserve" y="202.41" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="145" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,145,242.5) scale(1,1) translate(0,0)" writing-mode="lr" x="145.2" xml:space="preserve" y="247.41" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="YW1"/>
   </metadata>
  </g>
  <g id="146">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="146" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,205.222,513.5) scale(1,1) translate(-3.9598e-14,0)" writing-mode="lr" x="205.34" xml:space="preserve" y="518.41" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="147">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="147" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,205.222,537.5) scale(1,1) translate(-3.9598e-14,0)" writing-mode="lr" x="205.34" xml:space="preserve" y="542.41" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="148" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,205.222,561.5) scale(1,1) translate(-3.9598e-14,0)" writing-mode="lr" x="205.34" xml:space="preserve" y="566.41" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="149">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,205.222,586.5) scale(1,1) translate(-3.9598e-14,0)" writing-mode="lr" x="205.34" xml:space="preserve" y="591.41" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="154">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,329.222,489.667) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="494.58" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="153">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,329.222,513.5) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="518.41" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="152">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="152" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,329.222,537.5) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="542.41" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="151">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="151" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,329.222,561.5) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="566.41" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="150" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,329.222,586.5) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="591.41" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,329.188,321.25) scale(0.708333,0.665547) translate(131.173,156.419)" width="30" x="318.56" xlink:href="#State:红绿圆(方形)_0" y="311.27" zvalue="1585"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.188,321.25) scale(0.708333,0.665547) translate(131.173,156.419)" width="30" x="318.56" y="311.27"/></g>
  <g id="1035">
   <use height="30" transform="rotate(0,233.562,321.25) scale(0.708333,0.665547) translate(91.7978,156.419)" width="30" x="222.94" xlink:href="#State:红绿圆(方形)_0" y="311.27" zvalue="1586"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,233.562,321.25) scale(0.708333,0.665547) translate(91.7978,156.419)" width="30" x="222.94" y="311.27"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="22">
   <use class="kv35" height="30" transform="rotate(0,1096.31,382.875) scale(-1.79167,-1.79167) translate(-1702.27,-584.698)" width="15" x="1082.875" xlink:href="#Disconnector:令克_0" y="356" zvalue="1713"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450386526213" ObjectName="35kV江心坡简易变3016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450386526213"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1096.31,382.875) scale(-1.79167,-1.79167) translate(-1702.27,-584.698)" width="15" x="1082.875" y="356"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="32">
   <g id="320">
    <use class="kv35" height="85" transform="rotate(0,1096.62,544.688) scale(1.225,1.08088) translate(-195.796,-37.3214)" width="50" x="1066" xlink:href="#PowerTransformer2:Vertical_0" y="498.75" zvalue="1714"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874468425731" ObjectName="35"/>
    </metadata>
   </g>
   <g id="321">
    <use class="kv10" height="85" transform="rotate(0,1096.62,544.688) scale(1.225,1.08088) translate(-195.796,-37.3214)" width="50" x="1066" xlink:href="#PowerTransformer2:Vertical_1" y="498.75" zvalue="1714"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874468491267" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399465631747" ObjectName="35kV江心坡简易变1号主变"/>
   <cge:TPSR_Ref TObjectID="6755399465631747"/></metadata>
  <rect fill="white" height="85" opacity="0" stroke="white" transform="rotate(0,1096.62,544.688) scale(1.225,1.08088) translate(-195.796,-37.3214)" width="50" x="1066" y="498.75"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="44">
   <use class="kv10" height="30" transform="rotate(0,1085.62,656.25) scale(1.5625,-1.54167) translate(-387.45,-1073.8)" width="12" x="1076.25" xlink:href="#EnergyConsumer:负荷_0" y="633.125" zvalue="1715"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450386591749" ObjectName="10kV江心线"/>
   <cge:TPSR_Ref TObjectID="6192450386591749"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1085.62,656.25) scale(1.5625,-1.54167) translate(-387.45,-1073.8)" width="12" x="1076.25" y="633.125"/></g>
  <g id="2">
   <use class="kv35" height="30" transform="rotate(0,1096,257) scale(1.25,1.23333) translate(-217.7,-45.1216)" width="12" x="1088.5" xlink:href="#EnergyConsumer:负荷_0" y="238.5" zvalue="1724"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450386657285" ObjectName="35kV象旧线江心坡简易变T线"/>
   <cge:TPSR_Ref TObjectID="6192450386657285"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1096,257) scale(1.25,1.23333) translate(-217.7,-45.1216)" width="12" x="1088.5" y="238.5"/></g>
  <g id="17">
   <use class="kv10" height="30" transform="rotate(0,1109.38,657.125) scale(1.5625,-1.54167) translate(-396,-1075.24)" width="12" x="1100" xlink:href="#EnergyConsumer:负荷_0" y="634" zvalue="1735"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450386722821" ObjectName="10kV江平线"/>
   <cge:TPSR_Ref TObjectID="6192450386722821"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1109.38,657.125) scale(1.5625,-1.54167) translate(-396,-1075.24)" width="12" x="1100" y="634"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="75">
   <path class="kv35" d="M 1096.16 406.61 L 1096.16 500.37" stroke-width="1" zvalue="1721"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@0" LinkObjectIDznd="32@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1096.16 406.61 L 1096.16 500.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv35" d="M 1096 273.65 L 1096 360.93" stroke-width="1" zvalue="1727"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@0" LinkObjectIDznd="22@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1096 273.65 L 1096 360.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv10" d="M 1097.44 590.44 L 1109.38 590.44 L 1109.38 636.31" stroke-width="1" zvalue="1732"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@1" LinkObjectIDznd="17@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.44 590.44 L 1109.38 590.44 L 1109.38 636.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv10" d="M 1097.44 590.44 L 1085.63 590.44 L 1085.63 635.44" stroke-width="1" zvalue="1733"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15" LinkObjectIDznd="44@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.44 590.44 L 1085.63 590.44 L 1085.63 635.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
</svg>