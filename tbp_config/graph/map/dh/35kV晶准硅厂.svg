<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549584330754" height="1045" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1045" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Compensator:西郊变电容_0" viewBox="0,0,24,40">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="2.449999999999999"/>
   <rect fill-opacity="0" height="5.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12,23) scale(1,1) translate(0,0)" width="3" x="10.5" y="20.17"/>
   <path d="M 12 17.85 L 17.85 17.85 L 17.85 22.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="6.5" y1="33.10833333333333" y2="33.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.471296296296289" x2="6.471296296296289" y1="30.88611111111111" y2="33.12685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.50833333333333" x2="6.50833333333333" y1="20.10833333333333" y2="17.90462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="29.60833333333333" y2="33.12685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="22.60833333333333" y2="28.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="17.79351851851851" y2="22.525"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.94090594744122" x2="6.94090594744122" y1="36.94166666666666" y2="34.94166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.416666666666666" x2="12.00833333333334" y1="17.85833333333333" y2="17.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="33.10833333333333" y2="37.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.09166666666667" x2="17.09166666666667" y1="34.94166666666666" y2="36.94166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.758333333333324" x2="2.758333333333324" y1="19.85833333333333" y2="31.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.08333333333333" x2="7.000000000000002" y1="37.10833333333333" y2="37.10833333333333"/>
   <path d="M 6.50833 23.7072 A 2.96392 1.81747 180 0 1 6.50833 20.0723" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.50833 27.3421 A 2.96392 1.81747 180 0 1 6.50833 23.7072" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.50833 30.8771 A 2.96392 1.81747 180 0 1 6.50833 27.2421" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="29.60833333333333" y2="29.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="28.60833333333333" y2="28.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="12" y1="2.25" y2="4.916666666666668"/>
   <path d="M 7.26667 9.85 A 4.91667 4.75 -450 1 0 12.0167 4.93333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 7.33333 9.83333 L 12 9.91667 L 12 18" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="20.56666666666668" y2="21.08015580397416"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.28450340888154" x2="18.47116270499357" y1="26.7156109584975" y2="26.7156109584975"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510778" x2="17.14262023217246" y1="23.75922956383932" y2="22.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.50700202690252" x2="18.32283029297954" y1="27.0857461490052" y2="27.0857461490052"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="25.36667381975841" y2="26.33114037330986"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.98783858485353" x2="18.76782752902158" y1="26.34547576798984" y2="26.34547576798984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="21.08015580397418" y2="23.73243882624067"/>
   <rect fill-opacity="0" height="4.29" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,17.83,23.22) scale(1,1) translate(0,0)" width="2.33" x="16.67" y="21.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510781" x2="18.53565505804314" y1="23.75922956383932" y2="22.95550743587977"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:线路PT11带避雷器_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="23.75" y2="23.75"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,11) scale(1,1) translate(0,0)" width="6" x="17" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="20" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="32" y1="17" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.13040845230975" x2="32.13040845230975" y1="16.76788570496156" y2="23.53635804601289"/>
   <rect fill-opacity="0" height="6.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,32.04,24.56) scale(1,1) translate(0,0)" width="15.34" x="24.37" y="21.53"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.98821300514558" x2="19.98821300514558" y1="1.029523490692871" y2="18.75"/>
   <path d="M 30.0147 23.4481 L 34.2865 23.4481 L 32.115 28.6155 L 30.0147 23.4481" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <ellipse cx="19.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.22179295459144" x2="32.22179295459144" y1="32.22550978083666" y2="36.2625308385742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.01788570496155" x2="35.42570020422134" y1="36.23023467011234" y2="36.23023467011234"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.26384963537316" x2="34.53572596821302" y1="37.52208140858838" y2="37.52208140858838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.15382387138148" x2="33.11176719059974" y1="38.49096646244535" y2="38.49096646244535"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="32.91666666666667" y2="32.91666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:硅厂炉变YY_0" viewBox="0,0,17,30">
   <use terminal-index="0" type="0" x="8.666666666666666" xlink:href="#terminal" y="0.1666666666666714"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="8.5" y1="25.25" y2="30.08333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.597025948103791" x2="8.597025948103791" y1="1.79293756914176" y2="0.2382945839350779"/>
   <ellipse cx="8.42" cy="8.35" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.5" cy="18.92" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.611718118722354" x2="8.611718118722354" y1="16.35874008086165" y2="18.95772818087859"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.2052656081938" x2="8.611718118722351" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.018170629250881" x2="8.611718118722335" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.611718118722354" x2="8.611718118722354" y1="5.503560677018264" y2="8.102548777035198"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.2052656081938" x2="8.611718118722351" y1="10.70153687705211" y2="8.102548777035178"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.018170629250881" x2="8.611718118722335" y1="10.70153687705211" y2="8.102548777035178"/>
  </symbol>
  <symbol id="EnergyConsumer:炉变20210831_0" viewBox="0,0,17,30">
   <use terminal-index="0" type="0" x="8.666666666666666" xlink:href="#terminal" y="0.1666666666666714"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="8.5" y1="25.25" y2="30.08333333333334"/>
   <path d="M 8.5 5.5 L 5.5 9.5 L 11.5 9.5 L 8.5 5.5 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.597025948103791" x2="8.597025948103791" y1="1.79293756914176" y2="0.2382945839350779"/>
   <ellipse cx="8.42" cy="8.35" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.5" cy="18.92" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 8.5 21 L 5.5 17 L 11.5 17 L 8.5 21 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车开关带避雷器_0" viewBox="0,0,20,26">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="10" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982326301579349" x2="9.982326301579349" y1="18.5" y2="24.08333333333334"/>
   <rect fill-opacity="0" height="12.42" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.08,12.13) scale(1,1) translate(0,0)" width="6.67" x="6.75" y="5.92"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.06661748931881" x2="10.06661748931881" y1="2.083333333333329" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.75" x2="9.916666666666666" y1="21.25" y2="21.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.7" x2="17.7" y1="12.75" y2="21.08333333333334"/>
   <path d="M 13.6321 23.6556 L 9.98274 25.9797 L 6.33333 23.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 2.40358 L 10.0648 0.181096 L 6.45119 2.40358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.6321 21.8223 L 9.98274 24.1463 L 6.33333 21.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 4.15358 L 10.0648 1.9311 L 6.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438356" x2="17.20890410958904" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.44863013698632" x2="17.92808219178084" y1="8.036096718478305" y2="8.036096718478305"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438357" x2="18.1678082191781" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.9691780821918" x2="18.40753424657536" y1="8.42469775474782" y2="8.42469775474782"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.48972602739728" x2="18.88698630136988" y1="8.813298791017345" y2="8.813298791017345"/>
   <rect fill-opacity="0" height="5.57" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,17.71,12.73) scale(1,-1) translate(0,-1034.27)" width="2.42" x="16.5" y="9.949999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438358" x2="17.68835616438358" y1="9.979101899825864" y2="8.813298791017306"/>
  </symbol>
  <symbol id="Breaker:小车开关带避雷器_1" viewBox="0,0,20,26">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="10" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.75" x2="9.916666666666666" y1="21.25" y2="21.25"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="12.42" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.08,12.13) scale(1,1) translate(0,0)" width="6.67" x="6.75" y="5.92"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.7" x2="17.7" y1="12.75" y2="21.08333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.06661748931881" x2="10.06661748931881" y1="0.3333333333333321" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982326301579349" x2="9.982326301579349" y1="18.5" y2="26.08333333333333"/>
   <path d="M 13.6321 23.6556 L 9.98274 25.9797 L 6.33333 23.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 2.40358 L 10.0648 0.181096 L 6.45119 2.40358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.9691780821918" x2="18.40753424657536" y1="8.42469775474782" y2="8.42469775474782"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.48972602739728" x2="18.88698630136988" y1="8.813298791017345" y2="8.813298791017345"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438357" x2="18.1678082191781" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.44863013698632" x2="17.92808219178084" y1="8.036096718478305" y2="8.036096718478305"/>
   <rect fill-opacity="0" height="5.57" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,17.71,12.73) scale(1,-1) translate(0,-1034.27)" width="2.42" x="16.5" y="9.949999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438356" x2="17.20890410958904" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438358" x2="17.68835616438358" y1="9.979101899825864" y2="8.813298791017306"/>
  </symbol>
  <symbol id="Breaker:小车开关带避雷器_2" viewBox="0,0,20,26">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="10" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333334" x2="13.08333333333333" y1="6.083333333333334" y2="18.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="7" y1="6.166666666666669" y2="18"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.7" x2="17.7" y1="12.75" y2="21.08333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.75" x2="9.916666666666666" y1="21.25" y2="21.25"/>
   <rect fill-opacity="0" height="12.42" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.08,12.13) scale(1,1) translate(0,0)" width="6.67" x="6.75" y="5.92"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.06661748931881" x2="10.06661748931881" y1="2.083333333333329" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982326301579349" x2="9.982326301579349" y1="18.5" y2="24.08333333333334"/>
   <path d="M 13.6321 23.6556 L 9.98274 25.9797 L 6.33333 23.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 2.40358 L 10.0648 0.181096 L 6.45119 2.40358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.6321 21.8223 L 9.98274 24.1463 L 6.33333 21.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 4.15358 L 10.0648 1.9311 L 6.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.9691780821918" x2="18.40753424657536" y1="8.42469775474782" y2="8.42469775474782"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.48972602739728" x2="18.88698630136988" y1="8.813298791017345" y2="8.813298791017345"/>
   <rect fill-opacity="0" height="5.57" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,17.71,12.73) scale(1,-1) translate(0,-1034.27)" width="2.42" x="16.5" y="9.949999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.44863013698632" x2="17.92808219178084" y1="8.036096718478305" y2="8.036096718478305"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438357" x2="18.1678082191781" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438356" x2="17.20890410958904" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438358" x2="17.68835616438358" y1="9.979101899825864" y2="8.813298791017306"/>
  </symbol>
  <symbol id="EnergyConsumer:Y-Y站用_0" viewBox="0,0,17,26">
   <use terminal-index="0" type="0" x="8.666666666666666" xlink:href="#terminal" y="0.1666666666666714"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.597025948103791" x2="8.597025948103791" y1="1.79293756914176" y2="0.2382945839350814"/>
   <ellipse cx="8.42" cy="8.35" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.5" cy="18.92" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.611718118722354" x2="8.611718118722354" y1="16.35874008086165" y2="18.95772818087859"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.2052656081938" x2="8.611718118722351" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.018170629250881" x2="8.611718118722335" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.611718118722354" x2="8.611718118722354" y1="5.503560677018262" y2="8.102548777035198"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.2052656081938" x2="8.611718118722351" y1="10.70153687705211" y2="8.102548777035178"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.018170629250881" x2="8.611718118722335" y1="10.70153687705211" y2="8.102548777035178"/>
  </symbol>
  <symbol id="Accessory:母线PT带保险_0" viewBox="0,0,44,51">
   <use terminal-index="0" type="0" x="23.21996232536395" xlink:href="#terminal" y="50.54273758135757"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.81333347460429" x2="37.91333348509471" y1="15.35514348552543" y2="20.85514353797752"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.89666669429144" x2="24.89666669429144" y1="17.55514350650627" y2="13.1551434645446"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.81333347460429" x2="35.71333346411387" y1="15.35514348552543" y2="20.85514353797752"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.89666669429144" x2="27.09666671527227" y1="13.1551434645446" y2="14.25514347503501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.89666669429144" x2="27.09666671527227" y1="17.55514350650627" y2="16.45514349601585"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.97499997115135" x2="18.97499997115135" y1="23.84999998426438" y2="43.92500017571449"/>
   <rect fill-opacity="0" height="9.9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,18.97,32.93) scale(1,1) translate(0,0)" width="5.5" x="16.22" y="27.98"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.88333330361049" x2="36.85000014162064" y1="43.92500017571449" y2="43.92500017571449"/>
   <path d="M 11.55 15.875 L 6.05 15.875 L 6.05 27.975" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.85000014162064" x2="36.85000014162064" y1="43.92500017571449" y2="15.04999990034103"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.10000001049042" x2="23.10000001049042" y1="43.92500017571449" y2="50.525000238657"/>
   <rect fill-opacity="0" height="17.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,36.85,16) scale(1,-1) translate(0,-699.43)" width="7.7" x="33" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.099999800682067" x2="10.99999989509582" y1="37.60000011539459" y2="28.80000003147125"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.77237710644068" x2="36.77237710644068" y1="7.471810077010772" y2="4.008772090519184"/>
   <ellipse cx="19.04" cy="19.44" fill-opacity="0" rx="4.59" ry="4.59" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="39.00513934691844" x2="34.30064187290063" y1="3.934072700740849" y2="3.934072700740849"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="38.08847267150976" x2="35.2173085483093" y1="2.559072687627836" y2="2.559072687627836"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.44680599872369" x2="35.85897522109538" y1="1.184072674514812" y2="1.184072674514812"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.01064172708383" x2="21.65064175226082" y1="19.67729253017713" y2="21.03858652594676"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.01064172708382" x2="19.01064172708382" y1="16.95470453863786" y2="19.67729253017711"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.01064172708382" x2="16.37064170190682" y1="19.67729253017713" y2="21.03858652594676"/>
   <ellipse cx="18.67" cy="11.65" fill-opacity="0" rx="4.59" ry="4.59" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.8" cy="15.59" fill-opacity="0" rx="4.59" ry="4.59" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.76897499135525" x2="14.40897501653224" y1="15.82729249346067" y2="17.1885864892303"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.76897499135524" x2="9.128974966178244" y1="15.82729249346067" y2="17.1885864892303"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.64397505692036" x2="21.28397508209735" y1="11.88562578920334" y2="13.24691978497297"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.64397505692035" x2="16.00397503174336" y1="11.88562578920334" y2="13.24691978497297"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.64397505692035" x2="18.64397505692035" y1="9.163037797664071" y2="11.88562578920332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.76897499135524" x2="11.76897499135524" y1="13.1047045019214" y2="15.82729249346065"/>
   <ellipse cx="25.46" cy="15.41" fill-opacity="0" rx="4.59" ry="4.59" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <rect fill-opacity="0" height="9.77" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6.19,32.74) scale(1,1) translate(0,0)" width="5.78" x="3.3" y="27.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.129282572815271" x2="6.129282572815271" y1="37.59454254183299" y2="39.80000013637542"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.387769126221219" x2="4.691566274612683" y1="39.65334678993617" y2="39.65334678993617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.862416669798145" x2="5.216918731035754" y1="40.44137547457077" y2="40.44137547457077"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.494669950301995" x2="5.584665450531904" y1="41.22940415920537" y2="41.22940415920537"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="1"/>
   <ellipse cx="14.99" cy="7.93" fill-opacity="0" rx="7.43" ry="6.85" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.93" cy="17.56" fill-opacity="0" rx="7.43" ry="6.85" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.9834232682896" x2="14.9834232682896" y1="14.95787681993586" y2="18.2114918126664"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.98342326828961" x2="11.56276000329148" y1="18.23128772350278" y2="20.61950731913853"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00726151837816" x2="18.51522958638884" y1="18.19641261556525" y2="20.42154821077479"/>
   <path d="M 11.6895 9.29402 L 18.7806 9.29402 L 15.2653 3.08877 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="3" y1="23" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="12" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="12" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0.3333333333333357" y2="12.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="12" y2="12"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:PT带保险_0" viewBox="0,0,11,29">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="0.1666666666666661"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.5,7.5) scale(1,1) translate(0,0)" width="4" x="3.5" y="4.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="13.16666666666667" y2="0.5"/>
   <ellipse cx="5.4" cy="18.1" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.4" cy="23.78" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV晶准硅厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1045" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <line fill="none" id="156" stroke="rgb(0,39,45)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.22222222222285" x2="318.8097708603564" y1="76.70055797356122" y2="76.70055797356122" zvalue="190"/>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="4" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,96.625,325.25) scale(1,1) translate(0,0)" width="72.88" x="60.19" y="313.25" zvalue="534"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,96.625,325.25) scale(1,1) translate(0,0)" writing-mode="lr" x="96.63" xml:space="preserve" y="329.75" zvalue="534">信号一览</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="273.81" x="48.44" xlink:href="logo.png" y="50.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,185.347,80.75) scale(1,1) translate(-1.07568e-14,0)" writing-mode="lr" x="185.35" xml:space="preserve" y="84.25" zvalue="798"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,178.833,78.8153) scale(1,1) translate(2.02061e-14,0)" writing-mode="lr" x="178.83" xml:space="preserve" y="87.81999999999999" zvalue="799">35kV晶准硅厂</text>
  <line fill="none" id="130" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="375" x2="375" y1="49.5" y2="1039.5" zvalue="210"/>
  <line fill="none" id="136" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.47692454998014" x2="318.0881846035993" y1="169.1279458827686" y2="169.1279458827686" zvalue="501"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="928.0627909390723" y2="928.0627909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="979.4022909390724" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="41.57142857142856" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0114285714285" x2="113.0114285714285" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="347.5719285714285" y1="928.0627909390723" y2="928.0627909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="347.5719285714285" y1="979.4022909390724" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="113.0119285714286" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.5719285714285" x2="347.5719285714285" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="41.57142857142856" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0114285714285" x2="113.0114285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="113.0119285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8093285714285" x2="265.8093285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="265.8092285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.5713285714286" x2="347.5713285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="41.57142857142856" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0114285714285" x2="113.0114285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="113.0119285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8093285714285" x2="265.8093285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="265.8092285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.5713285714286" x2="347.5713285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="134" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,188.624,957.636) scale(1,1) translate(-1.04198e-14,1.05117e-13)" writing-mode="lr" x="46.93" xml:space="preserve" y="963.64" zvalue="503">参考图号      JingZhunGuiChang-01-2021</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="109" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,119.149,995.005) scale(1,1) translate(5.03187e-14,-1.52972e-12)" writing-mode="lr" x="56.65" xml:space="preserve" y="1001" zvalue="504">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="98" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,262.452,996.005) scale(1,1) translate(1.29063e-13,-1.53127e-12)" writing-mode="lr" x="193.75" xml:space="preserve" y="1002" zvalue="505">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.2423,1024.56) scale(1,1) translate(-4.58202e-14,1.01293e-12)" writing-mode="lr" x="70.23999999999999" xml:space="preserve" y="1030.56" zvalue="506">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="96" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,228.461,1022.56) scale(1,1) translate(0,1.12325e-13)" writing-mode="lr" x="193.92" xml:space="preserve" y="1028.56" zvalue="507">更新日期 </text>
  <line fill="none" id="91" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="40.85611355802348" x2="345.4673736116426" y1="623.8945306693694" y2="623.8945306693694" zvalue="508"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.9207,640.936) scale(1,1) translate(2.94653e-15,-1.38164e-13)" writing-mode="lr" x="83.92070806204504" xml:space="preserve" y="645.4363811688671" zvalue="510">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="172.3571428571429" y2="172.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="172.3571428571429" y2="172.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="290.8571428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="290.8571428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" x="130.578125" xml:space="preserve" y="467.359375" zvalue="513">35kV     母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="130.578125" xml:space="preserve" y="483.359375" zvalue="513">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.97,327.199) scale(1,1) translate(0,0)" writing-mode="lr" x="199.97" xml:space="preserve" y="331.7" zvalue="514">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,304.97,327.199) scale(1,1) translate(0,0)" writing-mode="lr" x="304.97" xml:space="preserve" y="331.7" zvalue="515">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,502.857) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="507.3571428571429" zvalue="517">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,528.357) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="532.8571428571429" zvalue="518">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,553.857) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="558.3571428571429" zvalue="519">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,579.357) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="583.8571428571429" zvalue="520">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,604.857) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="609.3571428571429" zvalue="521">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,40.5714,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="40.57" xml:space="preserve" y="191.86" zvalue="522">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.821,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="226.82" xml:space="preserve" y="191.86" zvalue="523">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.2589,210.607) scale(1,1) translate(0,0)" writing-mode="lr" x="44.26" xml:space="preserve" y="215.11" zvalue="524">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,518.75,326) scale(1,1) translate(0,0)" writing-mode="lr" x="518.75" xml:space="preserve" y="330.5" zvalue="556">35kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,668,122.5) scale(1,1) translate(0,0)" writing-mode="lr" x="668" xml:space="preserve" y="127" zvalue="558">35kV腊晶Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,983.25,397.5) scale(1,1) translate(0,0)" writing-mode="lr" x="983.25" xml:space="preserve" y="402" zvalue="560">303</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,965.922,564) scale(1,1) translate(0,0)" writing-mode="lr" x="965.92" xml:space="preserve" y="568.5" zvalue="562">#1动力变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1138.75,326) scale(1,1) translate(0,0)" writing-mode="lr" x="1138.75" xml:space="preserve" y="330.5" zvalue="567">35kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,943.75,326) scale(1,1) translate(0,0)" writing-mode="lr" x="943.75" xml:space="preserve" y="330.5" zvalue="569">35kVⅢ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1538.75,326) scale(1,1) translate(0,0)" writing-mode="lr" x="1538.75" xml:space="preserve" y="330.5" zvalue="571">35kV V段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,690.412,258.5) scale(1,1) translate(0,0)" writing-mode="lr" x="690.41" xml:space="preserve" y="263" zvalue="622">3616</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1006.41,258.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1006.41" xml:space="preserve" y="263" zvalue="626">3133</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1292,122.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1292" xml:space="preserve" y="127" zvalue="630">35kV腊晶Ⅱ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1314.41,258.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1314.41" xml:space="preserve" y="263" zvalue="633">3626</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1608,269) scale(1,1) translate(0,0)" writing-mode="lr" x="1608" xml:space="preserve" y="273.5" zvalue="642">365</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,601.229,539) scale(1,1) translate(0,0)" writing-mode="lr" x="601.23" xml:space="preserve" y="543.5" zvalue="645">#1炉变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,631.519,395.5) scale(1,1) translate(0,0)" writing-mode="lr" x="631.52" xml:space="preserve" y="400" zvalue="646">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,728.75,388.5) scale(1,1) translate(0,0)" writing-mode="lr" x="728.75" xml:space="preserve" y="393" zvalue="648">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" x="710.8125" xml:space="preserve" y="496.5" zvalue="651">35kVⅠ段母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="710.8125" xml:space="preserve" y="512.5" zvalue="651">互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,819.561,527) scale(1,1) translate(0,0)" writing-mode="lr" x="819.5599999999999" xml:space="preserve" y="531.5" zvalue="659">1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,832,438.5) scale(1,1) translate(0,0)" writing-mode="lr" x="832" xml:space="preserve" y="443" zvalue="662">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,845.415,397) scale(1,1) translate(0,0)" writing-mode="lr" x="845.41" xml:space="preserve" y="401.5" zvalue="666">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,824,552) scale(1,1) translate(0,0)" writing-mode="lr" x="824" xml:space="preserve" y="556.5" zvalue="668">8400Kvar</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="151" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1140.5,543.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1140.5" xml:space="preserve" y="548" zvalue="671">#2炉变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="150" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1170.79,400) scale(1,1) translate(0,0)" writing-mode="lr" x="1170.79" xml:space="preserve" y="404.5" zvalue="673">304</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1268.02,393) scale(1,1) translate(0,0)" writing-mode="lr" x="1268.02" xml:space="preserve" y="397.5" zvalue="675">3902</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" x="1250.078125" xml:space="preserve" y="501" zvalue="678">35kVⅡ段母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1250.078125" xml:space="preserve" y="517" zvalue="678">互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1358.83,531.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1358.83" xml:space="preserve" y="536" zvalue="683">2号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1371.27,443) scale(1,1) translate(0,0)" writing-mode="lr" x="1371.27" xml:space="preserve" y="447.5" zvalue="686">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1384.69,401.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1384.69" xml:space="preserve" y="406" zvalue="690">305</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1363.27,556.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1363.27" xml:space="preserve" y="561" zvalue="691">8400Kvar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1147,571) scale(1,1) translate(0,0)" writing-mode="lr" x="1147" xml:space="preserve" y="575.5" zvalue="693">12600kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="171" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1645.23,539) scale(1,1) translate(0,0)" writing-mode="lr" x="1645.23" xml:space="preserve" y="543.5" zvalue="696">5号主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="170" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1675.52,395.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1675.52" xml:space="preserve" y="400" zvalue="698">306</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1733.23,539) scale(1,1) translate(0,0)" writing-mode="lr" x="1733.23" xml:space="preserve" y="543.5" zvalue="703">6号主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="178" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1763.52,395.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1763.52" xml:space="preserve" y="400" zvalue="705">307</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1580.02,393) scale(1,1) translate(0,0)" writing-mode="lr" x="1580.02" xml:space="preserve" y="397.5" zvalue="710">3905</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" x="1565.1015625" xml:space="preserve" y="493.75" zvalue="712">35kV V段母线电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1565.1015625" xml:space="preserve" y="509.75" zvalue="712">压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,545,396) scale(1,1) translate(0,0)" writing-mode="lr" x="545" xml:space="preserve" y="400.5" zvalue="716">314</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="196" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,745.5,619.5) scale(1,1) translate(0,0)" writing-mode="lr" x="745.5" xml:space="preserve" y="624" zvalue="720">35kVⅣ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,780,693) scale(1,1) translate(0,0)" writing-mode="lr" x="780" xml:space="preserve" y="697.5" zvalue="729">341</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="230" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,900.75,688.5) scale(1,1) translate(0,0)" writing-mode="lr" x="900.75" xml:space="preserve" y="693" zvalue="757">3904</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" x="882.8125" xml:space="preserve" y="796.5" zvalue="760">35kVⅣ段母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="882.8125" xml:space="preserve" y="812.5" zvalue="760">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1080.5,696) scale(1,1) translate(0,0)" writing-mode="lr" x="1080.5" xml:space="preserve" y="700.5" zvalue="764">363</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1126,773.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1126" xml:space="preserve" y="778" zvalue="765">1号主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="239" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1651,568) scale(1,1) translate(0,0)" writing-mode="lr" x="1651" xml:space="preserve" y="572.5" zvalue="766">3150kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1735,568) scale(1,1) translate(0,0)" writing-mode="lr" x="1735" xml:space="preserve" y="572.5" zvalue="768">4000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="241" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1126,792) scale(1,1) translate(0,0)" writing-mode="lr" x="1126" xml:space="preserve" y="796.5" zvalue="770">4000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1083.75,874.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1083.75" xml:space="preserve" y="879" zvalue="774">001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1125,932.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1125" xml:space="preserve" y="937" zvalue="775">1号发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,989.5,850) scale(1,1) translate(0,0)" writing-mode="lr" x="989.5" xml:space="preserve" y="854.5" zvalue="779">061</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,909,894.5) scale(1,1) translate(0,0)" writing-mode="lr" x="909" xml:space="preserve" y="899" zvalue="780">10kV厂用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1141,853.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1141" xml:space="preserve" y="858" zvalue="783">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1127,954) scale(1,1) translate(0,0)" writing-mode="lr" x="1127" xml:space="preserve" y="958.5" zvalue="791">3000kW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,148.971,1022.33) scale(1,1) translate(-1.33227e-13,-1.57219e-12)" writing-mode="lr" x="148.97" xml:space="preserve" y="1028.33" zvalue="794">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="17" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,306.539,1022.33) scale(1,1) translate(0,1.12299e-13)" writing-mode="lr" x="272" xml:space="preserve" y="1028.33" zvalue="796">20211129</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="60.19" y="313.25" zvalue="534"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="51">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="51" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,127.571,210.468) scale(1,1) translate(0,0)" writing-mode="lr" x="127.72" xml:space="preserve" y="216.9" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="8" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,127.571,186.5) scale(1,1) translate(0,0)" writing-mode="lr" x="127.72" xml:space="preserve" y="192.93" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124849844229" ObjectName=""/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="13" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,304.571,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="304.72" xml:space="preserve" y="192.79" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124849909765" ObjectName=""/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,125.071,528.093) scale(1,1) translate(0,0)" writing-mode="lr" x="125.2" xml:space="preserve" y="533" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="143" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,125.071,553.218) scale(1,1) translate(0,-1.20508e-13)" writing-mode="lr" x="125.2" xml:space="preserve" y="558.13" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,125.071,578.343) scale(1,1) translate(0,-2.52173e-13)" writing-mode="lr" x="125.2" xml:space="preserve" y="583.25" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,126.5,502.968) scale(1,1) translate(0,0)" writing-mode="lr" x="126.63" xml:space="preserve" y="507.88" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="148" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,124.321,603.468) scale(1,1) translate(0,0)" writing-mode="lr" x="124.45" xml:space="preserve" y="608.38" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="160">
   <use height="30" transform="rotate(0,334.196,327.857) scale(0.708333,0.665547) translate(133.235,159.739)" width="30" x="323.57" xlink:href="#State:红绿圆(方形)_0" y="317.87" zvalue="538"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.196,327.857) scale(0.708333,0.665547) translate(133.235,159.739)" width="30" x="323.57" y="317.87"/></g>
  <g id="54">
   <use height="30" transform="rotate(0,238.571,327.857) scale(0.708333,0.665547) translate(93.8603,159.739)" width="30" x="227.95" xlink:href="#State:红绿圆(方形)_0" y="317.87" zvalue="539"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,238.571,327.857) scale(0.708333,0.665547) translate(93.8603,159.739)" width="30" x="227.95" y="317.87"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="100">
   <path class="kv35" d="M 504 339 L 845 339" stroke-width="4" zvalue="555"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674239971333" ObjectName="35kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674239971333"/></metadata>
  <path d="M 504 339 L 845 339" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv35" d="M 1091 339 L 1479 339" stroke-width="4" zvalue="566"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674239905797" ObjectName="35kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674239905797"/></metadata>
  <path d="M 1091 339 L 1479 339" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv35" d="M 904 339 L 1033 339" stroke-width="4" zvalue="568"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674239840260" ObjectName="35kVⅢ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674239840260"/></metadata>
  <path d="M 904 339 L 1033 339" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv35" d="M 1524 339 L 1753 339" stroke-width="4" zvalue="570"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674239774724" ObjectName="35kV V段母线"/>
   <cge:TPSR_Ref TObjectID="9288674239774724"/></metadata>
  <path d="M 1524 339 L 1753 339" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv35" d="M 704 635.5 L 1305 635.5" stroke-width="4" zvalue="719"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674240036868" ObjectName="35kVⅣ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674240036868"/></metadata>
  <path d="M 704 635.5 L 1305 635.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="90">
   <use class="kv35" height="20" transform="rotate(0,964,398.5) scale(1.5,1.35) translate(-318.833,-99.8148)" width="10" x="956.5" xlink:href="#Breaker:小车断路器_0" y="385" zvalue="559"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924496392197" ObjectName="#1动力变303断路器"/>
   <cge:TPSR_Ref TObjectID="6473924496392197"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,964,398.5) scale(1.5,1.35) translate(-318.833,-99.8148)" width="10" x="956.5" y="385"/></g>
  <g id="102">
   <use class="kv35" height="20" transform="rotate(0,1587.5,270) scale(1.5,1.35) translate(-526.667,-66.5)" width="10" x="1580" xlink:href="#Breaker:小车断路器_0" y="256.5" zvalue="641"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924496457733" ObjectName="35kV腊晶Ⅱ回线365断路器"/>
   <cge:TPSR_Ref TObjectID="6473924496457733"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1587.5,270) scale(1.5,1.35) translate(-526.667,-66.5)" width="10" x="1580" y="256.5"/></g>
  <g id="31">
   <use class="kv35" height="26" transform="rotate(0,603.596,396) scale(1.18269,1.57692) translate(-91.4115,-137.378)" width="20" x="591.7692307692307" xlink:href="#Breaker:小车开关带避雷器_0" y="375.5" zvalue="645"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924496523269" ObjectName="#1炉变301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924496523269"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,603.596,396) scale(1.18269,1.57692) translate(-91.4115,-137.378)" width="20" x="591.7692307692307" y="375.5"/></g>
  <g id="129">
   <use class="kv35" height="26" transform="rotate(0,821.088,398) scale(1.18269,1.57692) translate(-125.008,-138.11)" width="20" x="809.2608539739377" xlink:href="#Breaker:小车开关带避雷器_0" y="377.5" zvalue="665"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924496588805" ObjectName="1号电容器302断路器"/>
   <cge:TPSR_Ref TObjectID="6473924496588805"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,821.088,398) scale(1.18269,1.57692) translate(-125.008,-138.11)" width="20" x="809.2608539739377" y="377.5"/></g>
  <g id="167">
   <use class="kv35" height="26" transform="rotate(0,1142.87,400.5) scale(1.18269,1.57692) translate(-174.713,-139.024)" width="20" x="1131.039743589744" xlink:href="#Breaker:小车开关带避雷器_0" y="380" zvalue="672"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924496719877" ObjectName="#2炉变304断路器"/>
   <cge:TPSR_Ref TObjectID="6473924496719877"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1142.87,400.5) scale(1.18269,1.57692) translate(-174.713,-139.024)" width="20" x="1131.039743589744" y="380"/></g>
  <g id="154">
   <use class="kv35" height="26" transform="rotate(0,1360.36,402.5) scale(1.18269,1.57692) translate(-208.31,-139.756)" width="20" x="1348.531366794451" xlink:href="#Breaker:小车开关带避雷器_0" y="382" zvalue="688"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924496654341" ObjectName="2号电容器305断路器"/>
   <cge:TPSR_Ref TObjectID="6473924496654341"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1360.36,402.5) scale(1.18269,1.57692) translate(-208.31,-139.756)" width="20" x="1348.531366794451" y="382"/></g>
  <g id="176">
   <use class="kv35" height="26" transform="rotate(0,1647.6,396) scale(1.18269,1.57692) translate(-252.68,-137.378)" width="20" x="1635.769230769231" xlink:href="#Breaker:小车开关带避雷器_0" y="375.5" zvalue="697"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924496785413" ObjectName="5号主变306断路器"/>
   <cge:TPSR_Ref TObjectID="6473924496785413"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1647.6,396) scale(1.18269,1.57692) translate(-252.68,-137.378)" width="20" x="1635.769230769231" y="375.5"/></g>
  <g id="183">
   <use class="kv35" height="26" transform="rotate(0,1735.6,396) scale(1.18269,1.57692) translate(-266.273,-137.378)" width="20" x="1723.769230769231" xlink:href="#Breaker:小车开关带避雷器_0" y="375.5" zvalue="704"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924496850949" ObjectName="6号主变307断路器"/>
   <cge:TPSR_Ref TObjectID="6473924496850949"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1735.6,396) scale(1.18269,1.57692) translate(-266.273,-137.378)" width="20" x="1723.769230769231" y="375.5"/></g>
  <g id="191">
   <use class="kv35" height="20" transform="rotate(0,524.5,397) scale(1.5,1.35) translate(-172.333,-99.4259)" width="10" x="517" xlink:href="#Breaker:小车断路器_0" y="383.5" zvalue="715"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924496916485" ObjectName="314"/>
   <cge:TPSR_Ref TObjectID="6473924496916485"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,524.5,397) scale(1.5,1.35) translate(-172.333,-99.4259)" width="10" x="517" y="383.5"/></g>
  <g id="203">
   <use class="kv35" height="20" transform="rotate(0,760.5,694) scale(1.5,1.35) translate(-251,-176.426)" width="10" x="753" xlink:href="#Breaker:小车断路器_0" y="680.5" zvalue="728"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924497047557" ObjectName="341"/>
   <cge:TPSR_Ref TObjectID="6473924497047557"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,760.5,694) scale(1.5,1.35) translate(-251,-176.426)" width="10" x="753" y="680.5"/></g>
  <g id="235">
   <use class="kv35" height="20" transform="rotate(0,1060.5,697) scale(1.5,1.35) translate(-351,-177.204)" width="10" x="1053" xlink:href="#Breaker:小车断路器_0" y="683.5" zvalue="763"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924497178629" ObjectName="1号主变363断路器"/>
   <cge:TPSR_Ref TObjectID="6473924497178629"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1060.5,697) scale(1.5,1.35) translate(-351,-177.204)" width="10" x="1053" y="683.5"/></g>
  <g id="244">
   <use class="kv10" height="20" transform="rotate(0,1062,875.5) scale(1.5,1.35) translate(-351.5,-223.481)" width="10" x="1054.5" xlink:href="#Breaker:开关_0" y="862" zvalue="773"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924497244165" ObjectName="1号发电机001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924497244165"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1062,875.5) scale(1.5,1.35) translate(-351.5,-223.481)" width="10" x="1054.5" y="862"/></g>
  <g id="250">
   <use class="kv10" height="20" transform="rotate(0,968.5,851) scale(1.5,1.35) translate(-320.333,-217.13)" width="10" x="961" xlink:href="#Breaker:小车断路器_0" y="837.5" zvalue="778"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924497309701" ObjectName="061"/>
   <cge:TPSR_Ref TObjectID="6473924497309701"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,968.5,851) scale(1.5,1.35) translate(-320.333,-217.13)" width="10" x="961" y="837.5"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="89">
   <use class="kv35" height="30" transform="rotate(0,963.489,490.5) scale(3.06667,3.06667) translate(-631.741,-299.554)" width="17" x="937.4222222222222" xlink:href="#EnergyConsumer:硅厂炉变YY_0" y="444.5" zvalue="561"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449725005830" ObjectName="#1动力变"/>
   <cge:TPSR_Ref TObjectID="6192449725005830"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,963.489,490.5) scale(3.06667,3.06667) translate(-631.741,-299.554)" width="17" x="937.4222222222222" y="444.5"/></g>
  <g id="1">
   <use class="kv35" height="30" transform="rotate(0,603.079,477) scale(3.1,3.1) translate(-390.688,-291.629)" width="17" x="576.729487179487" xlink:href="#EnergyConsumer:炉变20210831_0" y="430.5" zvalue="644"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449725136902" ObjectName="#1炉变"/>
   <cge:TPSR_Ref TObjectID="6192449725136902"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,603.079,477) scale(3.1,3.1) translate(-390.688,-291.629)" width="17" x="576.729487179487" y="430.5"/></g>
  <g id="168">
   <use class="kv35" height="30" transform="rotate(0,1142.35,481.5) scale(3.1,3.1) translate(-756,-294.677)" width="17" x="1116" xlink:href="#EnergyConsumer:炉变20210831_0" y="435" zvalue="670"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449725726726" ObjectName="#2炉变"/>
   <cge:TPSR_Ref TObjectID="6192449725726726"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1142.35,481.5) scale(3.1,3.1) translate(-756,-294.677)" width="17" x="1116" y="435"/></g>
  <g id="177">
   <use class="kv35" height="26" transform="rotate(0,1647.08,472.5) scale(3.1,3.23077) translate(-1097.91,-297.25)" width="17" x="1620.729487179487" xlink:href="#EnergyConsumer:Y-Y站用_0" y="430.5" zvalue="695"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449725792262" ObjectName="5号主变"/>
   <cge:TPSR_Ref TObjectID="6192449725792262"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1647.08,472.5) scale(3.1,3.23077) translate(-1097.91,-297.25)" width="17" x="1620.729487179487" y="430.5"/></g>
  <g id="184">
   <use class="kv35" height="26" transform="rotate(0,1735.08,472.5) scale(3.1,3.23077) translate(-1157.53,-297.25)" width="17" x="1708.729487179487" xlink:href="#EnergyConsumer:Y-Y站用_0" y="430.5" zvalue="702"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449725857798" ObjectName="6号主变"/>
   <cge:TPSR_Ref TObjectID="6192449725857798"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1735.08,472.5) scale(3.1,3.23077) translate(-1157.53,-297.25)" width="17" x="1708.729487179487" y="430.5"/></g>
  <g id="252">
   <use class="kv10" height="30" transform="rotate(0,968.5,903) scale(1.5,1.5) translate(-315.333,-293.5)" width="30" x="946" xlink:href="#EnergyConsumer:站用变DY_0" y="880.5" zvalue="779"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449726513158" ObjectName="10kV厂用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,968.5,903) scale(1.5,1.5) translate(-315.333,-293.5)" width="30" x="946" y="880.5"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="86">
   <path class="kv35" d="M 964 445.01 L 964 410.65" stroke-width="1" zvalue="563"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="90@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 964 445.01 L 964 410.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv35" d="M 964 386.01 L 964 339" stroke-width="1" zvalue="572"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="81@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 964 386.01 L 964 339" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv35" d="M 668.5 179.34 L 668.5 248.86" stroke-width="1" zvalue="623"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="118@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 668.5 179.34 L 668.5 248.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv35" d="M 668.47 270.31 L 668.47 339" stroke-width="1" zvalue="624"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@1" LinkObjectIDznd="100@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 668.47 270.31 L 668.47 339" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv35" d="M 984.47 270.31 L 984.47 339" stroke-width="1" zvalue="627"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@1" LinkObjectIDznd="81@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 984.47 270.31 L 984.47 339" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv35" d="M 984.5 248.86 L 984.5 224.5 L 668.5 224.5" stroke-width="1" zvalue="628"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@0" LinkObjectIDznd="36" MaxPinNum="2"/>
   </metadata>
  <path d="M 984.5 248.86 L 984.5 224.5 L 668.5 224.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="kv35" d="M 1292.5 179.34 L 1292.5 248.86" stroke-width="1" zvalue="632"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@0" LinkObjectIDznd="133@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1292.5 179.34 L 1292.5 248.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv35" d="M 1292.47 270.31 L 1292.47 339" stroke-width="1" zvalue="637"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@1" LinkObjectIDznd="82@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1292.47 270.31 L 1292.47 339" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv35" d="M 707.09 378.86 L 707.09 339" stroke-width="1" zvalue="650"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="100@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 707.09 378.86 L 707.09 339" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv35" d="M 707.06 400.31 L 707.06 435.42" stroke-width="1" zvalue="652"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@1" LinkObjectIDznd="115@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 707.06 400.31 L 707.06 435.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv35" d="M 603.6 375.5 L 603.6 339" stroke-width="1" zvalue="653"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31@0" LinkObjectIDznd="100@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 603.6 375.5 L 603.6 339" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv35" d="M 603.6 416.5 L 603.6 431.02" stroke-width="1" zvalue="654"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31@1" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 603.6 416.5 L 603.6 431.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv35" d="M 821.09 418.5 L 821.09 428.86" stroke-width="1" zvalue="661"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@1" LinkObjectIDznd="126@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 821.09 418.5 L 821.09 428.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv35" d="M 821.06 450.31 L 821.06 465.87" stroke-width="1" zvalue="663"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@1" LinkObjectIDznd="127@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 821.06 450.31 L 821.06 465.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="kv35" d="M 821.09 377.5 L 821.09 339" stroke-width="1" zvalue="666"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="100@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 821.09 377.5 L 821.09 339" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv35" d="M 1246.36 383.36 L 1246.36 339" stroke-width="1" zvalue="677"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="82@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1246.36 383.36 L 1246.36 339" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv35" d="M 1246.33 404.81 L 1246.33 439.92" stroke-width="1" zvalue="679"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@1" LinkObjectIDznd="165@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1246.33 404.81 L 1246.33 439.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv35" d="M 1142.87 380 L 1142.87 339" stroke-width="1" zvalue="680"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="167@0" LinkObjectIDznd="82@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1142.87 380 L 1142.87 339" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="kv35" d="M 1142.87 421 L 1142.87 435.52" stroke-width="1" zvalue="681"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="167@1" LinkObjectIDznd="168@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1142.87 421 L 1142.87 435.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv35" d="M 1360.36 423 L 1360.36 433.36" stroke-width="1" zvalue="685"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@1" LinkObjectIDznd="158@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1360.36 423 L 1360.36 433.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv35" d="M 1360.33 454.81 L 1360.33 470.37" stroke-width="1" zvalue="687"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@1" LinkObjectIDznd="159@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1360.33 454.81 L 1360.33 470.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv35" d="M 1360.36 382 L 1360.36 339" stroke-width="1" zvalue="689"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@0" LinkObjectIDznd="82@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1360.36 382 L 1360.36 339" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv35" d="M 1647.6 375.5 L 1647.6 339" stroke-width="1" zvalue="699"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@0" LinkObjectIDznd="80@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1647.6 375.5 L 1647.6 339" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv35" d="M 1647.6 416.5 L 1647.6 431.04" stroke-width="1" zvalue="700"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@1" LinkObjectIDznd="177@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1647.6 416.5 L 1647.6 431.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv35" d="M 1735.6 375.5 L 1735.6 339" stroke-width="1" zvalue="706"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@0" LinkObjectIDznd="80@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1735.6 375.5 L 1735.6 339" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv35" d="M 1735.6 416.5 L 1735.6 431.04" stroke-width="1" zvalue="707"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@1" LinkObjectIDznd="184@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1735.6 416.5 L 1735.6 431.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv35" d="M 1558.36 383.36 L 1558.36 339" stroke-width="1" zvalue="712"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189@0" LinkObjectIDznd="80@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1558.36 383.36 L 1558.36 339" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv35" d="M 1558.33 404.81 L 1558.33 439.36" stroke-width="1" zvalue="713"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189@1" LinkObjectIDznd="188@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1558.33 404.81 L 1558.33 439.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="kv35" d="M 524.5 384.51 L 524.5 339" stroke-width="1" zvalue="723"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="100@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 524.5 384.51 L 524.5 339" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv35" d="M 540.25 455.43 L 540.25 433.5 L 524.5 433.5" stroke-width="1" zvalue="726"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="205" MaxPinNum="2"/>
   </metadata>
  <path d="M 540.25 455.43 L 540.25 433.5 L 524.5 433.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="205">
   <path class="kv35" d="M 524.5 409.15 L 524.5 787.5 L 760.5 787.5 L 760.5 706.15" stroke-width="1" zvalue="729"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@1" LinkObjectIDznd="203@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 524.5 409.15 L 524.5 787.5 L 760.5 787.5 L 760.5 706.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="206">
   <path class="kv35" d="M 760.5 681.51 L 760.5 635.5" stroke-width="1" zvalue="730"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@0" LinkObjectIDznd="195@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 760.5 681.51 L 760.5 635.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv35" d="M 727.75 740.93 L 727.75 731.5 L 760.5 731.5" stroke-width="1" zvalue="733"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="205" MaxPinNum="2"/>
   </metadata>
  <path d="M 727.75 740.93 L 727.75 731.5 L 760.5 731.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv35" d="M 1587.5 257.51 L 1587.5 229.5 L 1292.5 229.5" stroke-width="1" zvalue="751"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="32" MaxPinNum="2"/>
   </metadata>
  <path d="M 1587.5 257.51 L 1587.5 229.5 L 1292.5 229.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv35" d="M 1587.5 282.15 L 1587.5 339" stroke-width="1" zvalue="752"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@1" LinkObjectIDznd="80@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1587.5 282.15 L 1587.5 339" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="kv35" d="M 879.09 678.86 L 879.09 635.5" stroke-width="1" zvalue="759"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@0" LinkObjectIDznd="195@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.09 678.86 L 879.09 635.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="kv35" d="M 879.06 700.31 L 879.06 735.42" stroke-width="1" zvalue="761"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@1" LinkObjectIDznd="233@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.06 700.31 L 879.06 735.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="kv35" d="M 1060.5 684.51 L 1060.5 635.5" stroke-width="1" zvalue="771"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@0" LinkObjectIDznd="195@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1060.5 684.51 L 1060.5 635.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="243">
   <path class="kv35" d="M 1060.5 709.15 L 1060.5 740.3" stroke-width="1" zvalue="772"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@1" LinkObjectIDznd="237@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1060.5 709.15 L 1060.5 740.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="248">
   <path class="kv10" d="M 1061 813.02 L 1061 862.59" stroke-width="1" zvalue="775"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@1" LinkObjectIDznd="244@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1061 813.02 L 1061 862.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="249">
   <path class="kv10" d="M 1062.1 888.39 L 1062 909.38" stroke-width="1" zvalue="776"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="244@1" LinkObjectIDznd="246@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.1 888.39 L 1062 909.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="254">
   <path class="kv10" d="M 968.5 838.51 L 968.5 828.5 L 1061 828.5" stroke-width="1" zvalue="780"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@0" LinkObjectIDznd="248" MaxPinNum="2"/>
   </metadata>
  <path d="M 968.5 838.51 L 968.5 828.5 L 1061 828.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="255">
   <path class="kv10" d="M 968.5 863.15 L 968.5 882" stroke-width="1" zvalue="781"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@1" LinkObjectIDznd="252@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 968.5 863.15 L 968.5 882" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="kv10" d="M 1126.08 831.5 L 1061 831.5" stroke-width="1" zvalue="785"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="256@1" LinkObjectIDznd="248" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.08 831.5 L 1061 831.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv10" d="M 1151.97 831.42 L 1176.74 831.42" stroke-width="1" zvalue="786"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="256@0" LinkObjectIDznd="7@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1151.97 831.42 L 1176.74 831.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv10" d="M 1019.75 872.93 L 1019.75 850.5 L 1061 850.5" stroke-width="1" zvalue="789"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="11@0" LinkObjectIDznd="248" MaxPinNum="2"/>
   </metadata>
  <path d="M 1019.75 872.93 L 1019.75 850.5 L 1061 850.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="118">
   <use class="kv35" height="30" transform="rotate(0,668.412,259.5) scale(1,0.733333) translate(0,90.3636)" width="15" x="660.9122229491393" xlink:href="#Disconnector:刀闸_0" y="248.5" zvalue="621"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449724940294" ObjectName="35kV腊晶Ⅰ回线3616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449724940294"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,668.412,259.5) scale(1,0.733333) translate(0,90.3636)" width="15" x="660.9122229491393" y="248.5"/></g>
  <g id="123">
   <use class="kv35" height="30" transform="rotate(0,984.412,259.5) scale(1,0.733333) translate(0,90.3636)" width="15" x="976.9122229491393" xlink:href="#Disconnector:刀闸_0" y="248.5" zvalue="625"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449724874758" ObjectName="35kV腊晶Ⅰ回线3133隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449724874758"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,984.412,259.5) scale(1,0.733333) translate(0,90.3636)" width="15" x="976.9122229491393" y="248.5"/></g>
  <g id="133">
   <use class="kv35" height="30" transform="rotate(0,1292.41,259.5) scale(1,0.733333) translate(0,90.3636)" width="15" x="1284.912222949139" xlink:href="#Disconnector:刀闸_0" y="248.5" zvalue="631"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449724743686" ObjectName="35kV腊晶Ⅱ回线3626隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449724743686"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1292.41,259.5) scale(1,0.733333) translate(0,90.3636)" width="15" x="1284.912222949139" y="248.5"/></g>
  <g id="116">
   <use class="kv35" height="30" transform="rotate(0,707,389.5) scale(1,0.733333) translate(0,137.636)" width="15" x="699.5" xlink:href="#Disconnector:刀闸_0" y="378.5" zvalue="647"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449725267974" ObjectName="35kVⅠ段母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449725267974"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,707,389.5) scale(1,0.733333) translate(0,137.636)" width="15" x="699.5" y="378.5"/></g>
  <g id="126">
   <use class="kv35" height="30" transform="rotate(0,821,439.5) scale(1,0.733333) translate(0,155.818)" width="15" x="813.5" xlink:href="#Disconnector:刀闸_0" y="428.5" zvalue="660"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449725333510" ObjectName="1号电容器3026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449725333510"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,821,439.5) scale(1,0.733333) translate(0,155.818)" width="15" x="813.5" y="428.5"/></g>
  <g id="166">
   <use class="kv35" height="30" transform="rotate(0,1246.27,394) scale(1,0.733333) translate(0,139.273)" width="15" x="1238.770512820513" xlink:href="#Disconnector:刀闸_0" y="383" zvalue="674"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449725661190" ObjectName="35kVⅡ段母线电压互感器3902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449725661190"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1246.27,394) scale(1,0.733333) translate(0,139.273)" width="15" x="1238.770512820513" y="383"/></g>
  <g id="158">
   <use class="kv35" height="30" transform="rotate(0,1360.27,444) scale(1,0.733333) translate(0,157.455)" width="15" x="1352.770512820513" xlink:href="#Disconnector:刀闸_0" y="433" zvalue="684"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449725464582" ObjectName="2号电容器3056隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449725464582"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1360.27,444) scale(1,0.733333) translate(0,157.455)" width="15" x="1352.770512820513" y="433"/></g>
  <g id="189">
   <use class="kv35" height="30" transform="rotate(0,1558.27,394) scale(1,0.733333) translate(0,139.273)" width="15" x="1550.770512820513" xlink:href="#Disconnector:刀闸_0" y="383" zvalue="709"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449725988870" ObjectName="35kV V段母线电压互感器3905隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449725988870"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1558.27,394) scale(1,0.733333) translate(0,139.273)" width="15" x="1550.770512820513" y="383"/></g>
  <g id="234">
   <use class="kv35" height="30" transform="rotate(0,879,689.5) scale(1,0.733333) translate(0,246.727)" width="15" x="871.5" xlink:href="#Disconnector:刀闸_0" y="678.5" zvalue="756"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449726382086" ObjectName="35kVⅣ段母线电压互感器3904隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449726382086"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,879,689.5) scale(1,0.733333) translate(0,246.727)" width="15" x="871.5" y="678.5"/></g>
  <g id="256">
   <use class="kv10" height="26" transform="rotate(270,1139,831.5) scale(1,1) translate(0,0)" width="12" x="1133" xlink:href="#Disconnector:手车刀闸2020_0" y="818.5" zvalue="782"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449726578694" ObjectName="0901"/>
   <cge:TPSR_Ref TObjectID="6192449726578694"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1139,831.5) scale(1,1) translate(0,0)" width="12" x="1133" y="818.5"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="115">
   <use class="kv35" height="40" transform="rotate(0,707.061,454.5) scale(1,1) translate(0,0)" width="40" x="687.0611644241058" xlink:href="#Accessory:线路PT11带避雷器_0" y="434.5" zvalue="649"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449725202438" ObjectName="35kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,707.061,454.5) scale(1,1) translate(0,0)" width="40" x="687.0611644241058" y="434.5"/></g>
  <g id="165">
   <use class="kv35" height="40" transform="rotate(0,1246.33,459) scale(1,1) translate(0,0)" width="40" x="1226.331677244619" xlink:href="#Accessory:线路PT11带避雷器_0" y="439" zvalue="676"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449725595654" ObjectName="35kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1246.33,459) scale(1,1) translate(0,0)" width="40" x="1226.331677244619" y="439"/></g>
  <g id="188">
   <use class="kv35" height="51" transform="rotate(0,1557.22,459) scale(0.909091,-0.784314) translate(153.722,-1049.72)" width="44" x="1537.222620585197" xlink:href="#Accessory:母线PT带保险_0" y="439" zvalue="711"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449725923334" ObjectName="35kV V段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="51" opacity="0" stroke="white" transform="rotate(0,1557.22,459) scale(0.909091,-0.784314) translate(153.722,-1049.72)" width="44" x="1537.222620585197" y="439"/></g>
  <g id="197">
   <use class="kv35" height="20" transform="rotate(0,540.25,465.75) scale(1.475,1.475) translate(-169.229,-145.237)" width="20" x="525.5" xlink:href="#Accessory:线路PT3_0" y="451" zvalue="720"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449726054406" ObjectName="避雷器1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,540.25,465.75) scale(1.475,1.475) translate(-169.229,-145.237)" width="20" x="525.5" y="451"/></g>
  <g id="207">
   <use class="kv35" height="20" transform="rotate(0,727.75,751.25) scale(1.475,1.475) translate(-229.61,-237.178)" width="20" x="713" xlink:href="#Accessory:线路PT3_0" y="736.5" zvalue="732"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449726185478" ObjectName="避雷器3"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,727.75,751.25) scale(1.475,1.475) translate(-229.61,-237.178)" width="20" x="713" y="736.5"/></g>
  <g id="233">
   <use class="kv35" height="40" transform="rotate(0,879.061,754.5) scale(1,1) translate(0,0)" width="40" x="859.0611644241058" xlink:href="#Accessory:线路PT11带避雷器_0" y="734.5" zvalue="758"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449726316550" ObjectName="35kVⅣ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,879.061,754.5) scale(1,1) translate(0,0)" width="40" x="859.0611644241058" y="734.5"/></g>
  <g id="7">
   <use class="kv10" height="29" transform="rotate(270,1197.67,830.75) scale(1.22727,1.46003) translate(-220.541,-255.085)" width="11" x="1190.920454545455" xlink:href="#Accessory:PT带保险_0" y="809.5795454545455" zvalue="784"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449726644230" ObjectName="PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(270,1197.67,830.75) scale(1.22727,1.46003) translate(-220.541,-255.085)" width="11" x="1190.920454545455" y="809.5795454545455"/></g>
  <g id="11">
   <use class="kv10" height="20" transform="rotate(0,1019.75,883.25) scale(1.475,1.475) translate(-323.644,-279.686)" width="20" x="1005" xlink:href="#Accessory:线路PT3_0" y="868.5" zvalue="788"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449726709766" ObjectName="避雷器5"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1019.75,883.25) scale(1.475,1.475) translate(-323.644,-279.686)" width="20" x="1005" y="868.5"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="127">
   <use class="kv35" height="40" transform="rotate(0,821.061,490) scale(1.375,1.375) translate(-219.426,-126.136)" width="24" x="804.5611644241058" xlink:href="#Compensator:西郊变电容_0" y="462.5" zvalue="657"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449725399046" ObjectName="1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192449725399046"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,821.061,490) scale(1.375,1.375) translate(-219.426,-126.136)" width="24" x="804.5611644241058" y="462.5"/></g>
  <g id="159">
   <use class="kv35" height="40" transform="rotate(0,1360.33,494.5) scale(1.375,1.375) translate(-366.5,-127.364)" width="24" x="1343.831677244619" xlink:href="#Compensator:西郊变电容_0" y="467" zvalue="682"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449725530118" ObjectName="2号电容器"/>
   <cge:TPSR_Ref TObjectID="6192449725530118"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1360.33,494.5) scale(1.375,1.375) translate(-366.5,-127.364)" width="24" x="1343.831677244619" y="467"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="237">
   <g id="2370">
    <use class="kv35" height="30" transform="rotate(0,1061,776.5) scale(2.58333,2.6) translate(-631.29,-453.846)" width="24" x="1030" xlink:href="#PowerTransformer2:可调不带中性点_0" y="737.5" zvalue="764"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874430283780" ObjectName="35"/>
    </metadata>
   </g>
   <g id="2371">
    <use class="kv10" height="30" transform="rotate(0,1061,776.5) scale(2.58333,2.6) translate(-631.29,-453.846)" width="24" x="1030" xlink:href="#PowerTransformer2:可调不带中性点_1" y="737.5" zvalue="764"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874430349316" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399447412740" ObjectName="1号主变"/>
   <cge:TPSR_Ref TObjectID="6755399447412740"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1061,776.5) scale(2.58333,2.6) translate(-631.29,-453.846)" width="24" x="1030" y="737.5"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="246">
   <use class="kv10" height="30" transform="rotate(0,1062,931.5) scale(1.5,1.5) translate(-346.5,-303)" width="30" x="1039.5" xlink:href="#Generator:发电机_0" y="909" zvalue="774"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449726447622" ObjectName="1号发电机"/>
   <cge:TPSR_Ref TObjectID="6192449726447622"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1062,931.5) scale(1.5,1.5) translate(-346.5,-303)" width="30" x="1039.5" y="909"/></g>
 </g>
</svg>