<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549684469761" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_0" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3" x2="7" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.25" x2="5.083333333333332" y1="7.083333333333336" y2="19.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_1" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.833333333333333" x2="6.916666666666667" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.05" x2="5.05" y1="5.000000000000004" y2="22.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_2" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.166666666666668" x2="1.333333333333333" y1="5.083333333333332" y2="24.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1" x2="8.833333333333334" y1="4.999999999999998" y2="24.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:PT232_0" viewBox="0,0,25,35">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0.2666666666666657"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="12.5" y1="15.16666666666667" y2="0.5"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12.5,7.5) scale(1,1) translate(0,0)" width="6" x="9.5" y="2.5"/>
   <ellipse cx="12.75" cy="20" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.75" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.67" cy="29.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.82" cy="24.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333336" x2="5.583333333333336" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="5.583333333333334" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="5.5" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.75" x2="12.75" y1="22.25" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="12.75" y1="17.63888888888889" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="12.75" y1="17.63888888888889" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="32" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="12.58333333333333" y1="27.38888888888889" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="12.58333333333333" y1="27.38888888888889" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="19.31481481481482" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.94444444444444" x2="20.62962962962963" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21.94444444444444" y1="24.96612466124661" y2="24.96612466124661"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="Accessory:厂用变2020_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <path d="M 13 13.5 L 17 13.5 L 15 10.5 L 13 13.5 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="20.66666666666666" y2="22.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="17.91666666666666" y2="20.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="25.41666666666667" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="20.66666666666666" y2="22.66666666666666"/>
   <ellipse cx="14.95" cy="20.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.95" cy="12.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0833 3.5 L 14.0833 4.5 L 16.0833 4.5 L 15.0833 3.5 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="1" y2="7.333333333333331"/>
  </symbol>
  <symbol id="PowerTransformer2:允金接地变_0" viewBox="0,0,20,30">
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.77838478538902" x2="6.77838478538902" y1="3.797799292079558" y2="6.396787392096492"/>
   <use terminal-index="0" type="1" x="6.75" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="2" x="6.85" xlink:href="#terminal" y="6.65"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.184837295917547" x2="6.778384785389" y1="8.995775492113406" y2="6.396787392096474"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.371932274860468" x2="6.778384785389015" y1="8.995775492113406" y2="6.396787392096474"/>
   <ellipse cx="6.78" cy="6.8" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:允金接地变_1" viewBox="0,0,20,30">
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.184837295917547" x2="6.778384785389" y1="20.49577549211341" y2="17.89678739209647"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.77838478538902" x2="6.77838478538902" y1="15.29779929207956" y2="17.89678739209649"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.371932274860468" x2="6.778384785389015" y1="20.49577549211341" y2="17.89678739209647"/>
   <ellipse cx="6.78" cy="17.55" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="16" y1="18" y2="21"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="18" y1="18" y2="18"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="18" y1="18" y2="22"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.5" x2="18.5" y1="24" y2="24"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="19" y1="23" y2="23"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="20" y1="22" y2="22"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="7" y1="21" y2="28"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="6.75" y1="24" y2="29"/>
   <use terminal-index="2" type="1" x="6.666666666666667" xlink:href="#terminal" y="29.25000000000001"/>
  </symbol>
  <symbol id="Accessory:四卷带壁雷器母线PT_0" viewBox="0,0,40,35">
   <use terminal-index="0" type="0" x="22.10905664884498" xlink:href="#terminal" y="34.51612485684674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="28.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,30,14.36) scale(1,-1) translate(0,-930.43)" width="7" x="26.5" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="16.27740325661302" y2="15.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="7.166666666666671" y1="16.27740325661302" y2="12.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="12.27740325661302" y2="13.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="30.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="22" y1="28.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="14" y1="21.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="30" y1="28.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="30" y1="28.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.92943360505483" x2="29.92943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="13.81" cy="17.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.95921744067709" x2="27.68240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.12588410734375" x2="28.51573492932657" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="30.54255077401042" x2="29.09906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599325" x2="16.18240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="13.78240159599324" y1="15.23154965466559" y2="17.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="11.38240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <ellipse cx="20.23" cy="13.99" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.48" cy="10.41" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="17.79906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="20.19906826265991" y1="11.73154965466559" y2="14.2066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="22.59906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="8.148216321332258" y2="10.62329629003646"/>
   <ellipse cx="6.64" cy="14.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer3:主变高压侧有中性点_0" viewBox="0,0,50,50">
   <use terminal-index="0" type="1" x="14.75" xlink:href="#terminal" y="0.2499999999999929"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.33333333333333" x2="15.15740740740741" y1="10.58333333333333" y2="14.1754686785551"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.08333333333333" x2="15.17203932327389" y1="10.66666666666667" y2="14.1754686785551"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.16666666666666" x2="15.16666666666666" y1="14.16666666666666" y2="18.91666666666666"/>
   <ellipse cx="14.93" cy="15" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="2" x="15.08333333333334" xlink:href="#terminal" y="14.16666666666666"/>
  </symbol>
  <symbol id="PowerTransformer3:主变高压侧有中性点_1" viewBox="0,0,50,50">
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.66666666666666" x2="34.66666666666666" y1="29.41666666666667" y2="25.11796982167353"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.66598079561043" x2="29.75" y1="25.11796982167353" y2="20.66666666666667"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.67329675354367" x2="39.25" y1="25.11065386374029" y2="20.58333333333334"/>
   <ellipse cx="34.78" cy="25.08" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="1" x="49.50000000000001" xlink:href="#terminal" y="25.08333333333333"/>
  </symbol>
  <symbol id="PowerTransformer3:主变高压侧有中性点_2" viewBox="0,0,50,50">
   <path d="M 15.1667 32 L 10.0833 40 L 20.1667 40 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.12" cy="35.17" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="3" type="1" x="15.08333333333334" xlink:href="#terminal" y="49.91666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV槟榔江增容电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="49.25" xlink:href="logo.png" y="48.25"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,177.25,78.25) scale(1,1) translate(0,0)" writing-mode="lr" x="177.25" xml:space="preserve" y="81.75" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,176.5,77.5653) scale(1,1) translate(0,0)" writing-mode="lr" x="176.5" xml:space="preserve" y="86.56999999999999" zvalue="3">110kV槟榔江增容电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="245" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="354"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="354">信号一览</text>
  <line fill="none" id="33" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="383.25" x2="383.25" y1="16.25" y2="1046.25" zvalue="4"/>
  <line fill="none" id="31" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.250000000000455" x2="376.25" y1="152.1204926140824" y2="152.1204926140824" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,449.5,164.5) scale(1,1) translate(0,0)" writing-mode="lr" x="449.5" xml:space="preserve" y="169" zvalue="38">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,508.5,108.5) scale(1,1) translate(0,0)" writing-mode="lr" x="508.5" xml:space="preserve" y="113" zvalue="40">110kV槟榔江内联线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,563.556,281.024) scale(1,1) translate(0,0)" writing-mode="lr" x="563.5599999999999" xml:space="preserve" y="285.52" zvalue="44">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,594.655,338.337) scale(1,1) translate(0,0)" writing-mode="lr" x="594.65" xml:space="preserve" y="342.84" zvalue="46">7</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,597.905,227.452) scale(1,1) translate(0,0)" writing-mode="lr" x="597.9" xml:space="preserve" y="231.95" zvalue="52">0</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,810.665,233.472) scale(1,1) translate(0,0)" writing-mode="lr" x="810.67" xml:space="preserve" y="237.97" zvalue="65">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,820.107,277.472) scale(1,1) translate(0,4.47929e-13)" writing-mode="lr" x="820.11" xml:space="preserve" y="281.97" zvalue="66">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,814.009,340.139) scale(1,1) translate(0,0)" writing-mode="lr" x="814.01" xml:space="preserve" y="344.64" zvalue="68">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,884.944,267.167) scale(1,1) translate(0,0)" writing-mode="lr" x="884.9400000000001" xml:space="preserve" y="271.67" zvalue="77">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,834.861,578.858) scale(1,1) translate(4.56779e-13,0)" writing-mode="lr" x="834.86" xml:space="preserve" y="583.36" zvalue="82">002</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,740.968,370.5) scale(1,1) translate(0,0)" writing-mode="lr" x="740.9678676470588" xml:space="preserve" y="375" zvalue="90">#2主变25MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,884.444,328.611) scale(1,1) translate(0,0)" writing-mode="lr" x="884.4400000000001" xml:space="preserve" y="333.11" zvalue="93">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,645.75,467) scale(1,1) translate(0,0)" writing-mode="lr" x="645.75" xml:space="preserve" y="471.5" zvalue="98">1020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1106.61,263.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1106.61" xml:space="preserve" y="268.22" zvalue="110">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1115.94,319.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1115.94" xml:space="preserve" y="324.22" zvalue="112">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1107.61,367.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1107.61" xml:space="preserve" y="372.22" zvalue="123">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1034.94,245.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1034.94" xml:space="preserve" y="250" zvalue="125">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1671.38,622) scale(1,1) translate(0,0)" writing-mode="lr" x="1671.38" xml:space="preserve" y="626.5" zvalue="142">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,510.28,693.361) scale(1,1) translate(0,0)" writing-mode="lr" x="510.28" xml:space="preserve" y="697.86" zvalue="145">084</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" x="488.25" xml:space="preserve" y="913" zvalue="148">#4发电机    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="488.25" xml:space="preserve" y="929" zvalue="148">10MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,541.25,747.75) scale(1,1) translate(0,0)" writing-mode="lr" x="541.25" xml:space="preserve" y="752.25" zvalue="151">0941</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1022.78,695.861) scale(1,1) translate(0,0)" writing-mode="lr" x="1022.78" xml:space="preserve" y="700.36" zvalue="172">085</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" x="998.75" xml:space="preserve" y="923.5" zvalue="175">#5发电机    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="998.75" xml:space="preserve" y="939.5" zvalue="175">10MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1053.75,750.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1053.75" xml:space="preserve" y="754.75" zvalue="178">0951</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,778.75,705.25) scale(1,1) translate(0,0)" writing-mode="lr" x="778.75" xml:space="preserve" y="709.75" zvalue="192">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1432.4,681.57) scale(1,1) translate(0,0)" writing-mode="lr" x="1432.4" xml:space="preserve" y="686.0700000000001" zvalue="198">0861</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1425.76,739.88) scale(1,1) translate(0,0)" writing-mode="lr" x="1425.76" xml:space="preserve" y="744.38" zvalue="200">086</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1489.84,834.85) scale(1,1) translate(-1.29442e-12,-9.06246e-13)" writing-mode="lr" x="1489.841344652651" xml:space="preserve" y="839.3500094885671" zvalue="206">站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="222" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1437.13,894.025) scale(1,1) translate(0,0)" writing-mode="lr" x="1437.13" xml:space="preserve" y="898.52" zvalue="219">485</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="223" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1393.7,942.715) scale(1,1) translate(0,0)" writing-mode="lr" x="1393.7" xml:space="preserve" y="947.21" zvalue="223">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1798.88,975.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1798.88" xml:space="preserve" y="980.25" zvalue="225">0.4kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="224" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1678.63,892.775) scale(1,1) translate(0,0)" writing-mode="lr" x="1678.63" xml:space="preserve" y="897.27" zvalue="228">484</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1646.7,942.715) scale(1,1) translate(0,0)" writing-mode="lr" x="1646.7" xml:space="preserve" y="947.21" zvalue="229">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="210" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1672.62,815.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1672.63" xml:space="preserve" y="820.25" zvalue="232">老厂房</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="212" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,503.75,129.75) scale(1,1) translate(0,0)" writing-mode="lr" x="503.75" xml:space="preserve" y="134.25" zvalue="234">（至老站）</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="217" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1094.79,120.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1094.79" xml:space="preserve" y="124.63" zvalue="238">35kV和硅线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="220" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1200.5,154.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1200.5" xml:space="preserve" y="159.13" zvalue="241">35kV电压互感器</text>
  
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="271" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="331"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="269" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="333">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="334">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="335">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="336">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="337">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="261" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="339">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="340">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="341">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="254" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="345">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="346">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="347">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.9375,358) scale(1,1) translate(0,0)" writing-mode="lr" x="60.94" xml:space="preserve" y="362.5" zvalue="348">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="244" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="355">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="243" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="356">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="239" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="359">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="237" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="361">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="235" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="363">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="231" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="365">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="366">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="368">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="273" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,242.188,333) scale(1,1) translate(0,0)" writing-mode="lr" x="242.19" xml:space="preserve" y="337.5" zvalue="372">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.5,953) scale(1,1) translate(0,0)" writing-mode="lr" x="228.5" xml:space="preserve" y="959" zvalue="376">BinLsngJiang-01-2011</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="354"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="36">
   <path class="kv110" d="M 470 191.75 L 867.5 191.75" stroke-width="6" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674421440515" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674421440515"/></metadata>
  <path d="M 470 191.75 L 867.5 191.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv10" d="M 437.5 641.75 L 1706.25 641.75" stroke-width="6" zvalue="141"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674421506051" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674421506051"/></metadata>
  <path d="M 437.5 641.75 L 1706.25 641.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="v400" d="M 1305 976.75 L 1758.75 976.75" stroke-width="6" zvalue="224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674421571587" ObjectName="0.4kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674421571587"/></metadata>
  <path d="M 1305 976.75 L 1758.75 976.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="v400" d="M 1592.5 845.5 L 1747.5 845.5" stroke-width="6" zvalue="231"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674421637123" ObjectName="老厂房"/>
   <cge:TPSR_Ref TObjectID="9288674421637123"/></metadata>
  <path d="M 1592.5 845.5 L 1747.5 845.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="39">
   <path class="kv110" d="M 574.27 157.53 L 574.27 191.75" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@0" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 574.27 157.53 L 574.27 191.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv110" d="M 538.15 270.01 L 538.15 191.75" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="381@1" LinkObjectIDznd="36@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 538.15 270.01 L 538.15 191.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv110" d="M 585.82 245.74 L 538.15 245.74" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="382@0" LinkObjectIDznd="50" MaxPinNum="2"/>
   </metadata>
  <path d="M 585.82 245.74 L 538.15 245.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv110" d="M 799.29 246.49 L 799.29 267.84" stroke-width="1" zvalue="71"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@1" LinkObjectIDznd="101@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 799.29 246.49 L 799.29 267.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv110" d="M 799.41 289.08 L 799.41 329.32" stroke-width="1" zvalue="72"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@1" LinkObjectIDznd="103@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 799.41 289.08 L 799.41 329.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv110" d="M 798.8 385.42 L 798.8 353.15" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@0" LinkObjectIDznd="103@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 798.8 385.42 L 798.8 353.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv110" d="M 799.32 222.65 L 799.32 191.75" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 799.32 222.65 L 799.32 191.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv110" d="M 874.11 255 L 799.29 255" stroke-width="1" zvalue="104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="77" MaxPinNum="2"/>
   </metadata>
  <path d="M 874.11 255 L 799.29 255" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv110" d="M 873.61 315.11 L 799.41 315.11" stroke-width="1" zvalue="105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="76" MaxPinNum="2"/>
   </metadata>
  <path d="M 873.61 315.11 L 799.41 315.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv35" d="M 1095.23 276.74 L 1095.23 310.09" stroke-width="1" zvalue="113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@1" LinkObjectIDznd="267@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1095.23 276.74 L 1095.23 310.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv35" d="M 1095.25 331.33 L 1095.25 356.9" stroke-width="1" zvalue="127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@1" LinkObjectIDznd="104@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1095.25 331.33 L 1095.25 356.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv10" d="M 799.51 605.11 L 799.51 641.75" stroke-width="1" zvalue="142"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@1" LinkObjectIDznd="125@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 799.51 605.11 L 799.51 641.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv10" d="M 485.28 675.86 L 485.28 641.75" stroke-width="1" zvalue="146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@0" LinkObjectIDznd="125@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 485.28 675.86 L 485.28 641.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv10" d="M 485.28 712.36 L 485.25 855.13" stroke-width="1" zvalue="162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@1" LinkObjectIDznd="850@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 485.28 712.36 L 485.25 855.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv10" d="M 575.91 767.63 L 544.72 767.63" stroke-width="1" zvalue="165"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="145@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 575.91 767.63 L 544.72 767.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv10" d="M 518.83 766.25 L 485.27 766.25" stroke-width="1" zvalue="166"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@1" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 518.83 766.25 L 485.27 766.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv10" d="M 438.87 796.28 L 485.26 796.28" stroke-width="1" zvalue="167"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@0" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 438.87 796.28 L 485.26 796.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv10" d="M 558.95 872.22 L 558.95 801.75 L 485.26 801.75" stroke-width="1" zvalue="168"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="143@0" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 558.95 872.22 L 558.95 801.75 L 485.26 801.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv10" d="M 597.88 863.72 L 597.88 801.75 L 551.41 801.75" stroke-width="1" zvalue="169"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="151" MaxPinNum="2"/>
   </metadata>
  <path d="M 597.88 863.72 L 597.88 801.75 L 551.41 801.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv10" d="M 997.78 678.36 L 997.78 641.75" stroke-width="1" zvalue="173"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="125@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 997.78 678.36 L 997.78 641.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv10" d="M 997.78 714.86 L 997.75 857.63" stroke-width="1" zvalue="182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@1" LinkObjectIDznd="169@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 997.78 714.86 L 997.75 857.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv10" d="M 1088.41 770.13 L 1057.22 770.13" stroke-width="1" zvalue="184"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@0" LinkObjectIDznd="167@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1088.41 770.13 L 1057.22 770.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="kv10" d="M 1031.33 768.75 L 997.77 768.75" stroke-width="1" zvalue="185"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="167@1" LinkObjectIDznd="164" MaxPinNum="2"/>
   </metadata>
  <path d="M 1031.33 768.75 L 997.77 768.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv10" d="M 951.37 798.78 L 997.76 798.78" stroke-width="1" zvalue="186"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@0" LinkObjectIDznd="164" MaxPinNum="2"/>
   </metadata>
  <path d="M 951.37 798.78 L 997.76 798.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="kv10" d="M 1071.45 874.72 L 1071.45 804.25 L 997.76 804.25" stroke-width="1" zvalue="187"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="164" MaxPinNum="2"/>
   </metadata>
  <path d="M 1071.45 874.72 L 1071.45 804.25 L 997.76 804.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv10" d="M 1110.38 866.22 L 1110.38 804.25 L 1063.91 804.25" stroke-width="1" zvalue="188"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="159" MaxPinNum="2"/>
   </metadata>
  <path d="M 1110.38 866.22 L 1110.38 804.25 L 1063.91 804.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="kv10" d="M 814.78 730.01 L 814.78 712.97" stroke-width="1" zvalue="194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@0" LinkObjectIDznd="174@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 814.78 730.01 L 814.78 712.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv10" d="M 814.25 687.08 L 814.25 641.75" stroke-width="1" zvalue="195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@1" LinkObjectIDznd="125@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 814.25 687.08 L 814.25 641.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="kv10" d="M 1407.22 695.72 L 1407.12 731.26" stroke-width="1" zvalue="201"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@1" LinkObjectIDznd="193@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1407.22 695.72 L 1407.12 731.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv10" d="M 1407.23 750.27 L 1407.23 786.18" stroke-width="1" zvalue="208"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@1" LinkObjectIDznd="188@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1407.23 750.27 L 1407.23 786.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv10" d="M 1407.25 641.75 L 1407.25 674.38" stroke-width="1" zvalue="216"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@3" LinkObjectIDznd="194@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1407.25 641.75 L 1407.25 674.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="v400" d="M 1408.37 884.26 L 1408.37 863.71" stroke-width="1" zvalue="220"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="188@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1408.37 884.26 L 1408.37 863.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="v400" d="M 1408.48 903.27 L 1408.48 933.13" stroke-width="1" zvalue="223"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@1" LinkObjectIDznd="199@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1408.48 903.27 L 1408.48 933.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="v400" d="M 1409.72 954.47 L 1409.72 976.75" stroke-width="1" zvalue="225"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@1" LinkObjectIDznd="201@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.72 954.47 L 1409.72 976.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="206">
   <path class="v400" d="M 1657.48 903.27 L 1657.48 933.13" stroke-width="1" zvalue="229"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@1" LinkObjectIDznd="207@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1657.48 903.27 L 1657.48 933.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="205">
   <path class="v400" d="M 1658.72 954.47 L 1658.72 976.75" stroke-width="1" zvalue="230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@1" LinkObjectIDznd="201@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1658.72 954.47 L 1658.72 976.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="v400" d="M 1657.37 884.26 L 1657.37 845.5" stroke-width="1" zvalue="232"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@0" LinkObjectIDznd="209@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1657.37 884.26 L 1657.37 845.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv35" d="M 1094.79 169.13 L 1094.79 252.9" stroke-width="1" zvalue="238"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@0" LinkObjectIDznd="268@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1094.79 169.13 L 1094.79 252.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv35" d="M 1046.56 222.23 L 1094.79 222.23" stroke-width="1" zvalue="239"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="215" MaxPinNum="2"/>
   </metadata>
  <path d="M 1046.56 222.23 L 1094.79 222.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="kv35" d="M 1177.52 183.98 L 1094.79 183.98" stroke-width="1" zvalue="241"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@0" LinkObjectIDznd="215" MaxPinNum="2"/>
   </metadata>
  <path d="M 1177.52 183.98 L 1094.79 183.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv10" d="M 799.3 467.88 L 799.3 554.68" stroke-width="1" zvalue="242"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@3" LinkObjectIDznd="141@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 799.3 467.88 L 799.3 554.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="227">
   <path class="kv10" d="M 836.88 522.78 L 799.3 522.78" stroke-width="1" zvalue="243"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="226" MaxPinNum="2"/>
   </metadata>
  <path d="M 836.88 522.78 L 799.3 522.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv35" d="M 851.4 425.5 L 851.4 426.65" stroke-width="1" zvalue="252"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64" LinkObjectIDznd="73@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 851.4 425.5 L 851.4 426.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv35" d="M 851.4 426.65 L 851.4 425.5" stroke-width="1" zvalue="254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="46" MaxPinNum="2"/>
   </metadata>
  <path d="M 851.4 426.65 L 851.4 425.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv35" d="M 1096.23 380.74 L 1096.23 470.06" stroke-width="1" zvalue="256"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@1" LinkObjectIDznd="118@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1096.23 380.74 L 1096.23 470.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv35" d="M 851.4 425.5 L 1096.23 425.5" stroke-width="1" zvalue="258"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48" LinkObjectIDznd="59" MaxPinNum="2"/>
   </metadata>
  <path d="M 851.4 425.5 L 1096.23 425.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv35" d="M 851.4 456.56 L 851.4 425.5" stroke-width="1" zvalue="259"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="64" MaxPinNum="2"/>
   </metadata>
  <path d="M 851.4 456.56 L 851.4 425.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv110" d="M 799.3 408.52 L 672.49 408.52 L 672.49 450.1" stroke-width="1" zvalue="260"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@1" LinkObjectIDznd="80@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 799.3 408.52 L 672.49 408.52 L 672.49 450.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv110" d="M 701.6 448.81 L 701.6 408.52" stroke-width="1" zvalue="261"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="68" MaxPinNum="2"/>
   </metadata>
  <path d="M 701.6 448.81 L 701.6 408.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv110" d="M 538.12 293.84 L 538.12 350.58" stroke-width="1" zvalue="264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="381@0" LinkObjectIDznd="70@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 538.12 293.84 L 538.12 350.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv110" d="M 583.82 322.06 L 538.12 322.06" stroke-width="1" zvalue="265"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="380@0" LinkObjectIDznd="74" MaxPinNum="2"/>
   </metadata>
  <path d="M 583.82 322.06 L 538.12 322.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="381">
   <use class="kv110" height="30" transform="rotate(0,538.222,282.024) scale(-1.11111,-0.814815) translate(-1021.79,-630.922)" width="15" x="529.8888956705728" xlink:href="#Disconnector:刀闸_0" y="269.8016009709192" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454791462914" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454791462914"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,538.222,282.024) scale(-1.11111,-0.814815) translate(-1021.79,-630.922)" width="15" x="529.8888956705728" y="269.8016009709192"/></g>
  <g id="99">
   <use class="kv110" height="30" transform="rotate(0,799.221,234.472) scale(1.11111,0.814815) translate(-79.0887,50.5114)" width="15" x="790.887240747528" xlink:href="#Disconnector:刀闸_0" y="222.2499999999999" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454791921666" ObjectName="#2主变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454791921666"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,799.221,234.472) scale(1.11111,0.814815) translate(-79.0887,50.5114)" width="15" x="790.887240747528" y="222.2499999999999"/></g>
  <g id="103">
   <use class="kv110" height="30" transform="rotate(0,800.231,341.139) scale(1.11111,0.814815) translate(-79.1898,74.7538)" width="15" x="791.8977748228889" xlink:href="#Disconnector:刀闸_0" y="328.9166666666667" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454791856130" ObjectName="#2主变110kV侧1026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454791856130"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,800.231,341.139) scale(1.11111,0.814815) translate(-79.1898,74.7538)" width="15" x="791.8977748228889" y="328.9166666666667"/></g>
  <g id="268">
   <use class="kv35" height="30" transform="rotate(0,1095.17,264.722) scale(1.11111,0.814815) translate(-108.683,57.3864)" width="15" x="1086.83334350586" xlink:href="#Disconnector:刀闸_0" y="252.4999999999999" zvalue="109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454792380418" ObjectName="#2主变35kV侧3026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454792380418"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1095.17,264.722) scale(1.11111,0.814815) translate(-108.683,57.3864)" width="15" x="1086.83334350586" y="252.4999999999999"/></g>
  <g id="104">
   <use class="kv35" height="30" transform="rotate(0,1096.17,368.722) scale(1.11111,0.814815) translate(-108.783,81.0227)" width="15" x="1087.83334350586" xlink:href="#Disconnector:刀闸_0" y="356.5" zvalue="122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454792314882" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454792314882"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1096.17,368.722) scale(1.11111,0.814815) translate(-108.783,81.0227)" width="15" x="1087.83334350586" y="356.5"/></g>
  <g id="145">
   <use class="kv10" height="26" transform="rotate(270,531.75,766.25) scale(1,1) translate(0,0)" width="12" x="525.75" xlink:href="#Disconnector:单手车刀闸1212_0" y="753.25" zvalue="150"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454792642562" ObjectName="#4发电机0941隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454792642562"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,531.75,766.25) scale(1,1) translate(0,0)" width="12" x="525.75" y="753.25"/></g>
  <g id="167">
   <use class="kv10" height="26" transform="rotate(270,1044.25,768.75) scale(1,1) translate(0,0)" width="12" x="1038.25" xlink:href="#Disconnector:单手车刀闸1212_0" y="755.75" zvalue="177"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454793166850" ObjectName="#5发电机0951隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454793166850"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1044.25,768.75) scale(1,1) translate(0,0)" width="12" x="1038.25" y="755.75"/></g>
  <g id="174">
   <use class="kv10" height="26" transform="rotate(0,814.25,700) scale(1,1) translate(0,0)" width="12" x="808.25" xlink:href="#Disconnector:单手车刀闸1212_0" y="687" zvalue="191"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454793363458" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454793363458"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,814.25,700) scale(1,1) translate(0,0)" width="12" x="808.25" y="687"/></g>
  <g id="194">
   <use class="kv10" height="30" transform="rotate(0,1407.16,684.965) scale(0.994718,0.72946) translate(7.43242,249.98)" width="15" x="1399.697347564075" xlink:href="#Disconnector:刀闸_0" y="674.0230572160561" zvalue="197"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454793494530" ObjectName="站用变10kV侧0861隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454793494530"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1407.16,684.965) scale(0.994718,0.72946) translate(7.43242,249.98)" width="15" x="1399.697347564075" y="674.0230572160561"/></g>
  <g id="199">
   <use class="v400" height="30" transform="rotate(0,1409.66,943.715) scale(0.994718,0.72946) translate(7.44569,345.944)" width="15" x="1402.197347564075" xlink:href="#Disconnector:刀闸_0" y="932.773057216056" zvalue="222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454793560066" ObjectName="站用变0.4kV侧4851隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454793560066"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1409.66,943.715) scale(0.994718,0.72946) translate(7.44569,345.944)" width="15" x="1402.197347564075" y="932.773057216056"/></g>
  <g id="207">
   <use class="v400" height="30" transform="rotate(0,1658.66,943.715) scale(0.994718,0.72946) translate(8.76788,345.944)" width="15" x="1651.197347564075" xlink:href="#Disconnector:刀闸_0" y="932.773057216056" zvalue="228"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454793625602" ObjectName="老厂房0.4kV母线4841隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454793625602"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1658.66,943.715) scale(0.994718,0.72946) translate(8.76788,345.944)" width="15" x="1651.197347564075" y="932.773057216056"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="380">
   <use class="kv110" height="20" transform="rotate(270,594.655,322.115) scale(1.11111,1.11111) translate(-58.9099,-31.1004)" width="10" x="589.0992131308901" xlink:href="#GroundDisconnector:地刀_0" y="311.0039579754784" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454791397378" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454791397378"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,594.655,322.115) scale(1.11111,1.11111) translate(-58.9099,-31.1004)" width="10" x="589.0992131308901" y="311.0039579754784"/></g>
  <g id="382">
   <use class="kv110" height="20" transform="rotate(270,596.655,245.794) scale(1.11111,1.11111) translate(-59.1099,-23.4683)" width="10" x="591.0992131308901" xlink:href="#GroundDisconnector:地刀_0" y="234.6825294040499" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454791135234" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454791135234"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,596.655,245.794) scale(1.11111,1.11111) translate(-59.1099,-23.4683)" width="10" x="591.0992131308901" y="234.6825294040499"/></g>
  <g id="115">
   <use class="kv110" height="20" transform="rotate(270,884.944,254.944) scale(-1.11111,1.11111) translate(-1680.84,-24.3833)" width="10" x="879.3888990614151" xlink:href="#GroundDisconnector:地刀_0" y="243.8333333333332" zvalue="76"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454791790594" ObjectName="#2主变110kV侧10227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454791790594"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,884.944,254.944) scale(-1.11111,1.11111) translate(-1680.84,-24.3833)" width="10" x="879.3888990614151" y="243.8333333333332"/></g>
  <g id="65">
   <use class="kv110" height="20" transform="rotate(270,884.444,315.056) scale(-1.11111,1.11111) translate(-1679.89,-30.3944)" width="10" x="878.888899061415" xlink:href="#GroundDisconnector:地刀_0" y="303.9444444444443" zvalue="91"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454791593986" ObjectName="#2主变110kV侧10267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454791593986"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,884.444,315.056) scale(-1.11111,1.11111) translate(-1679.89,-30.3944)" width="10" x="878.888899061415" y="303.9444444444443"/></g>
  <g id="80">
   <use class="kv110" height="30" transform="rotate(0,672.5,468) scale(1.25,1.25) translate(-133.25,-89.85)" width="10" x="666.25" xlink:href="#GroundDisconnector:配网地刀_0" y="449.25" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454792118274" ObjectName="#2主变110kV侧1020中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454792118274"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,672.5,468) scale(1.25,1.25) translate(-133.25,-89.85)" width="10" x="666.25" y="449.25"/></g>
  <g id="102">
   <use class="kv35" height="20" transform="rotate(90,1027.28,222.125) scale(2.07501,1.97778) translate(-526.833,-100.037)" width="10" x="1016.902707947625" xlink:href="#GroundDisconnector:地刀_0" y="202.3472920523749" zvalue="124"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454792249346" ObjectName="#2主变35kV侧30217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454792249346"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1027.28,222.125) scale(2.07501,1.97778) translate(-526.833,-100.037)" width="10" x="1016.902707947625" y="202.3472920523749"/></g>
 </g>
 <g id="BreakerClass">
  <g id="101">
   <use class="kv110" height="20" transform="rotate(0,799.329,278.472) scale(1.22222,1.11111) translate(-144.221,-26.7361)" width="10" x="793.2181641817197" xlink:href="#Breaker:开关_0" y="267.3611111111111" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925217026051" ObjectName="#2主变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473925217026051"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,799.329,278.472) scale(1.22222,1.11111) translate(-144.221,-26.7361)" width="10" x="793.2181641817197" y="267.3611111111111"/></g>
  <g id="141">
   <use class="kv10" height="20" transform="rotate(0,799.507,580.239) scale(2.76308,2.76308) translate(-501.338,-352.611)" width="10" x="785.6919316124622" xlink:href="#Breaker:小车断路器_0" y="552.6083675142227" zvalue="81"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925216960515" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473925216960515"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,799.507,580.239) scale(2.76308,2.76308) translate(-501.338,-352.611)" width="10" x="785.6919316124622" y="552.6083675142227"/></g>
  <g id="267">
   <use class="kv35" height="20" transform="rotate(0,1095.17,320.722) scale(1.22222,1.11111) translate(-198.01,-30.9611)" width="10" x="1089.055565741327" xlink:href="#Breaker:开关_0" y="309.6111111111111" zvalue="111"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925217091587" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925217091587"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1095.17,320.722) scale(1.22222,1.11111) translate(-198.01,-30.9611)" width="10" x="1089.055565741327" y="309.6111111111111"/></g>
  <g id="341">
   <use class="kv10" height="20" transform="rotate(0,485.28,694.361) scale(2,2) translate(-237.64,-337.181)" width="10" x="475.2795695852834" xlink:href="#Breaker:小车断路器_0" y="674.3611128065321" zvalue="144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925217157123" ObjectName="#4发电机084断路器"/>
   <cge:TPSR_Ref TObjectID="6473925217157123"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,485.28,694.361) scale(2,2) translate(-237.64,-337.181)" width="10" x="475.2795695852834" y="674.3611128065321"/></g>
  <g id="171">
   <use class="kv10" height="20" transform="rotate(0,997.78,696.861) scale(2,2) translate(-493.89,-338.431)" width="10" x="987.7795695852833" xlink:href="#Breaker:小车断路器_0" y="676.8611128065321" zvalue="171"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925217222659" ObjectName="#5发电机085断路器"/>
   <cge:TPSR_Ref TObjectID="6473925217222659"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,997.78,696.861) scale(2,2) translate(-493.89,-338.431)" width="10" x="987.7795695852833" y="676.8611128065321"/></g>
  <g id="193">
   <use class="kv10" height="20" transform="rotate(0,1407.16,740.775) scale(1.09419,0.994718) translate(-120.66,3.88071)" width="10" x="1401.68678363881" xlink:href="#Breaker:开关_0" y="730.8276243792908" zvalue="199"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925217288196" ObjectName="站用变10kV侧086断路器"/>
   <cge:TPSR_Ref TObjectID="6473925217288196"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1407.16,740.775) scale(1.09419,0.994718) translate(-120.66,3.88071)" width="10" x="1401.68678363881" y="730.8276243792908"/></g>
  <g id="196">
   <use class="v400" height="20" transform="rotate(0,1408.41,893.775) scale(1.09419,0.994718) translate(-120.767,4.69314)" width="10" x="1402.93678363881" xlink:href="#Breaker:开关_0" y="883.8276243792908" zvalue="218"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925217353732" ObjectName="站用变0.4kV侧485断路器"/>
   <cge:TPSR_Ref TObjectID="6473925217353732"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1408.41,893.775) scale(1.09419,0.994718) translate(-120.767,4.69314)" width="10" x="1402.93678363881" y="883.8276243792908"/></g>
  <g id="208">
   <use class="v400" height="20" transform="rotate(0,1657.41,893.775) scale(1.09419,0.994718) translate(-142.202,4.69314)" width="10" x="1651.93678363881" xlink:href="#Breaker:开关_0" y="883.8276243792908" zvalue="227"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925217419268" ObjectName="老厂房0.4kV母线484断路器"/>
   <cge:TPSR_Ref TObjectID="6473925217419268"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1657.41,893.775) scale(1.09419,0.994718) translate(-142.202,4.69314)" width="10" x="1651.93678363881" y="883.8276243792908"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="67">
   <use class="kv10" height="26" transform="rotate(270,853.961,522.828) scale(1.38154,1.38154) translate(-233.549,-139.43)" width="12" x="845.6715109551789" xlink:href="#Accessory:避雷器1_0" y="504.867733567232" zvalue="87"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454791659522" ObjectName="#2主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,853.961,522.828) scale(1.38154,1.38154) translate(-233.549,-139.43)" width="12" x="845.6715109551789" y="504.867733567232"/></g>
  <g id="79">
   <use class="kv110" height="26" transform="rotate(0,701.571,461.179) scale(1,1) translate(0,0)" width="12" x="695.5714285714287" xlink:href="#Accessory:避雷器1_0" y="448.1785714285716" zvalue="96"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454791987202" ObjectName="#2主变中性点避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,701.571,461.179) scale(1,1) translate(0,0)" width="12" x="695.5714285714287" y="448.1785714285716"/></g>
  <g id="118">
   <use class="kv35" height="26" transform="rotate(0,1095.57,482.429) scale(1,1) translate(0,0)" width="12" x="1089.571428571429" xlink:href="#Accessory:避雷器1_0" y="469.4285714285716" zvalue="134"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454792445954" ObjectName="35kV母线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1095.57,482.429) scale(1,1) translate(0,0)" width="12" x="1089.571428571429" y="469.4285714285716"/></g>
  <g id="120">
   <use class="kv35" height="26" transform="rotate(0,851.369,468.929) scale(1,1) translate(0,0)" width="12" x="845.3687990196078" xlink:href="#Accessory:避雷器1_0" y="455.9285714285716" zvalue="137"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454792511490" ObjectName="#2主变35kV侧避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,851.369,468.929) scale(1,1) translate(0,0)" width="12" x="845.3687990196078" y="455.9285714285716"/></g>
  <g id="146">
   <use class="kv10" height="35" transform="rotate(270,602.275,767.625) scale(1.53,1.53) translate(-202.006,-256.634)" width="25" x="583.15" xlink:href="#Accessory:PT232_0" y="740.85" zvalue="149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454792708098" ObjectName="#4发电机PT1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(270,602.275,767.625) scale(1.53,1.53) translate(-202.006,-256.634)" width="25" x="583.15" y="740.85"/></g>
  <g id="143">
   <use class="kv10" height="30" transform="rotate(0,559,891.75) scale(1,1.33333) translate(0,-217.937)" width="30" x="544" xlink:href="#Accessory:PT789_0" y="871.75" zvalue="153"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454792904706" ObjectName="#4发电机PT2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,559,891.75) scale(1,1.33333) translate(0,-217.937)" width="30" x="544" y="871.75"/></g>
  <g id="140">
   <use class="kv10" height="29" transform="rotate(0,597.875,890.232) scale(1.89167,-1.8608) translate(-268.443,-1356.16)" width="30" x="569.5" xlink:href="#Accessory:厂用变2020_0" y="863.25" zvalue="155"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454792839170" ObjectName="#4励磁变"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,597.875,890.232) scale(1.89167,-1.8608) translate(-268.443,-1356.16)" width="30" x="569.5" y="863.25"/></g>
  <g id="134">
   <use class="kv10" height="26" transform="rotate(90,426.5,796.25) scale(1,1) translate(0,0)" width="12" x="420.5" xlink:href="#Accessory:避雷器1_0" y="783.25" zvalue="163"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454792577026" ObjectName="#4发电机避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,426.5,796.25) scale(1,1) translate(0,0)" width="12" x="420.5" y="783.25"/></g>
  <g id="168">
   <use class="kv10" height="35" transform="rotate(270,1114.78,770.125) scale(1.53,1.53) translate(-379.539,-257.5)" width="25" x="1095.65" xlink:href="#Accessory:PT232_0" y="743.35" zvalue="176"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454793232386" ObjectName="#5发电机PT1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(270,1114.78,770.125) scale(1.53,1.53) translate(-379.539,-257.5)" width="25" x="1095.65" y="743.35"/></g>
  <g id="166">
   <use class="kv10" height="30" transform="rotate(0,1071.5,894.25) scale(1,1.33333) translate(0,-218.562)" width="30" x="1056.5" xlink:href="#Accessory:PT789_0" y="874.25" zvalue="179"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454793101314" ObjectName="#5发电机PT2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1071.5,894.25) scale(1,1.33333) translate(0,-218.562)" width="30" x="1056.5" y="874.25"/></g>
  <g id="165">
   <use class="kv10" height="29" transform="rotate(0,1110.38,892.732) scale(1.89167,-1.8608) translate(-510.018,-1360.01)" width="30" x="1082" xlink:href="#Accessory:厂用变2020_0" y="865.75" zvalue="180"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454793035778" ObjectName="#5励磁变"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1110.38,892.732) scale(1.89167,-1.8608) translate(-510.018,-1360.01)" width="30" x="1082" y="865.75"/></g>
  <g id="163">
   <use class="kv10" height="26" transform="rotate(90,939,798.75) scale(1,1) translate(0,0)" width="12" x="933" xlink:href="#Accessory:避雷器1_0" y="785.75" zvalue="183"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454792970242" ObjectName="#5发电机避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,939,798.75) scale(1,1) translate(0,0)" width="12" x="933" y="785.75"/></g>
  <g id="175">
   <use class="kv10" height="35" transform="rotate(0,814.775,756.375) scale(1.53,1.53) translate(-275.617,-252.737)" width="25" x="795.65" xlink:href="#Accessory:PT232_0" y="729.6" zvalue="190"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454793428994" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,814.775,756.375) scale(1.53,1.53) translate(-275.617,-252.737)" width="25" x="795.65" y="729.6"/></g>
  <g id="218">
   <use class="kv35" height="35" transform="rotate(90,1204.62,187.188) scale(-1.52188,1.59286) translate(-1985.73,-59.2957)" width="40" x="1174.1875" xlink:href="#Accessory:四卷带壁雷器母线PT_0" y="159.3125" zvalue="240"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454793756674" ObjectName="35kV电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(90,1204.62,187.188) scale(-1.52188,1.59286) translate(-1985.73,-59.2957)" width="40" x="1174.1875" y="159.3125"/></g>
  <g id="70">
   <use class="kv110" height="35" transform="rotate(180,534.625,377.688) scale(-1.52188,1.59286) translate(-875.481,-130.199)" width="40" x="504.1875" xlink:href="#Accessory:四卷带壁雷器母线PT_0" y="349.8125" zvalue="263"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454791266306" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(180,534.625,377.688) scale(-1.52188,1.59286) translate(-875.481,-130.199)" width="40" x="504.1875" y="349.8125"/></g>
 </g>
 <g id="PowerTransformer3Class">
  <g id="73">
   <g id="730">
    <use class="kv110" height="50" transform="rotate(0,814.313,426.508) scale(1.51382,1.66032) translate(-263.55,-153.117)" width="50" x="776.47" xlink:href="#PowerTransformer3:主变高压侧有中性点_0" y="385" zvalue="89"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874592944130" ObjectName="110"/>
    </metadata>
   </g>
   <g id="731">
    <use class="kv35" height="50" transform="rotate(0,814.313,426.508) scale(1.51382,1.66032) translate(-263.55,-153.117)" width="50" x="776.47" xlink:href="#PowerTransformer3:主变高压侧有中性点_1" y="385" zvalue="89"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874593009666" ObjectName="35"/>
    </metadata>
   </g>
   <g id="732">
    <use class="kv10" height="50" transform="rotate(0,814.313,426.508) scale(1.51382,1.66032) translate(-263.55,-153.117)" width="50" x="776.47" xlink:href="#PowerTransformer3:主变高压侧有中性点_2" y="385" zvalue="89"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874593075202" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399533658114" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399533658114"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,814.313,426.508) scale(1.51382,1.66032) translate(-263.55,-153.117)" width="50" x="776.47" y="385"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="kv10" height="30" transform="rotate(0,485.25,877.25) scale(1.5,1.5) translate(-154.25,-284.917)" width="30" x="462.75" xlink:href="#Generator:发电机_0" y="854.75" zvalue="147"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454792773634" ObjectName="#4发电机"/>
   <cge:TPSR_Ref TObjectID="6192454792773634"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,485.25,877.25) scale(1.5,1.5) translate(-154.25,-284.917)" width="30" x="462.75" y="854.75"/></g>
  <g id="169">
   <use class="kv10" height="30" transform="rotate(0,997.75,879.75) scale(1.5,1.5) translate(-325.083,-285.75)" width="30" x="975.25" xlink:href="#Generator:发电机_0" y="857.25" zvalue="174"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454793297922" ObjectName="#5发电机"/>
   <cge:TPSR_Ref TObjectID="6192454793297922"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,997.75,879.75) scale(1.5,1.5) translate(-325.083,-285.75)" width="30" x="975.25" y="857.25"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="188">
   <g id="1880">
    <use class="kv10" height="30" transform="rotate(0,1415.93,825.283) scale(2.47858,2.69687) translate(-829.876,-493.815)" width="20" x="1391.14" xlink:href="#PowerTransformer2:允金接地变_0" y="784.83" zvalue="205"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874593140738" ObjectName="10"/>
    </metadata>
   </g>
   <g id="1881">
    <use class="v400" height="30" transform="rotate(0,1415.93,825.283) scale(2.47858,2.69687) translate(-829.876,-493.815)" width="20" x="1391.14" xlink:href="#PowerTransformer2:允金接地变_1" y="784.83" zvalue="205"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874593206274" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399533723650" ObjectName="站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1415.93,825.283) scale(2.47858,2.69687) translate(-829.876,-493.815)" width="20" x="1391.14" y="784.83"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="41">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="41" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,485,208.75) scale(1,1) translate(0,0)" writing-mode="lr" x="484.53" xml:space="preserve" y="213.53" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136411770882" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="44">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="44" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,479.5,618.75) scale(1,1) translate(0,0)" writing-mode="lr" x="479.03" xml:space="preserve" y="623.53" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136417472514" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="45">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="45" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1315.5,951.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1315.03" xml:space="preserve" y="956.28" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136423436290" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="49">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="49" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,732.313,251.399) scale(1,1) translate(0,4.52971e-13)" writing-mode="lr" x="731.84" xml:space="preserve" y="256.08" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136413081602" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="52" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,732.313,275.5) scale(1,1) translate(0,1.75927e-13)" writing-mode="lr" x="731.84" xml:space="preserve" y="280.18" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136413147138" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="53" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,732.313,299.601) scale(1,1) translate(0,-1.91981e-13)" writing-mode="lr" x="731.84" xml:space="preserve" y="304.28" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136413212674" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="56" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,732.313,597.925) scale(1,1) translate(0,-1.30307e-13)" writing-mode="lr" x="731.84" xml:space="preserve" y="602.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136413343746" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="60">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="60" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1181.16,293.085) scale(1,1) translate(0,1.87855e-13)" writing-mode="lr" x="1180.69" xml:space="preserve" y="297.76" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136413540354" ObjectName="MP"/>
   </metadata>
  </g>
  <g id="82">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="82" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1181.16,316.504) scale(1,1) translate(0,2.03455e-13)" writing-mode="lr" x="1180.69" xml:space="preserve" y="321.18" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136413605890" ObjectName="MQ"/>
   </metadata>
  </g>
  <g id="83">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="83" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,732.313,551.092) scale(1,1) translate(0,5.99538e-14)" writing-mode="lr" x="731.84" xml:space="preserve" y="555.77" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136413671426" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="84">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="84" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,732.313,574.508) scale(1,1) translate(0,-1.25107e-13)" writing-mode="lr" x="731.84" xml:space="preserve" y="579.1799999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136413736962" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="85" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1181.16,339.923) scale(1,1) translate(0,2.19056e-13)" writing-mode="lr" x="1180.69" xml:space="preserve" y="344.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136413868034" ObjectName="MIa"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="86" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,477.25,948.226) scale(1,1) translate(0,1.45756e-12)" writing-mode="lr" x="476.7" xml:space="preserve" y="952.89" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136417800194" ObjectName="P"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="87" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,996.75,952.214) scale(1,1) translate(0,7.29944e-13)" writing-mode="lr" x="996.2" xml:space="preserve" y="956.92" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136418979842" ObjectName="P"/>
   </metadata>
  </g>
  <g id="90">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="90" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,477.25,970.375) scale(1,1) translate(0,1.49198e-12)" writing-mode="lr" x="476.7" xml:space="preserve" y="975.04" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136417865730" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="91" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,996.75,979.625) scale(1,1) translate(0,7.51246e-13)" writing-mode="lr" x="996.2" xml:space="preserve" y="984.33" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136419045378" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="94" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,477.25,992.524) scale(1,1) translate(0,1.52641e-12)" writing-mode="lr" x="476.7" xml:space="preserve" y="997.1900000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136417931266" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="95" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,996.75,1007.04) scale(1,1) translate(0,7.72548e-13)" writing-mode="lr" x="996.2" xml:space="preserve" y="1011.74" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136419110914" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="250">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="250" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136411901954" ObjectName="F"/>
   </metadata>
  </g>
  <g id="249">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="249" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136425992194" ObjectName="F"/>
   </metadata>
  </g>
  <g id="248">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="248" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136426057730" ObjectName="F"/>
   </metadata>
  </g>
  <g id="242">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="242" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136425861122" ObjectName="F"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="241" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136425926658" ObjectName="F"/>
   </metadata>
  </g>
  <g id="238">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="238" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124540448772" ObjectName="F"/>
   </metadata>
  </g>
  <g id="236">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124540579844" ObjectName="F"/>
   </metadata>
  </g>
  <g id="232">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="232" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,402.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="407.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124540514311" ObjectName="F"/>
   </metadata>
  </g>
  <g id="219">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="219" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127180763141" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="197">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="197" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127180697605" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="191">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="191" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,358.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="363.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136423632898" ObjectName="F"/>
   </metadata>
  </g>
  <g id="274">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="274" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.111,333.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.27" xml:space="preserve" y="338.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136417669122" ObjectName="F"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="2" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,574.266,41) scale(1,1) translate(0,-6.6446e-15)" writing-mode="lr" x="531.14" xml:space="preserve" y="45.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136412033026" ObjectName="P"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="3" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,574.266,66) scale(1,1) translate(0,-1.21957e-14)" writing-mode="lr" x="531.14" xml:space="preserve" y="70.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136412098562" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,574.266,91) scale(1,1) translate(0,-1.77468e-14)" writing-mode="lr" x="531.14" xml:space="preserve" y="95.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136412164098" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="247">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="352"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374929952769" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="246">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="353"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562952947105799" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
</svg>