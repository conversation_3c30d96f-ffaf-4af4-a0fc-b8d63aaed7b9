<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549678243841" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:传输线_0" viewBox="0,0,12,22">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="1"/>
   <path d="M 1.16667 1 L 10.8333 1 L 6 7.63889 L 1.16667 1 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="14.27777777777778" y2="7.638888888888888"/>
   <path d="M 1.16667 20.9167 L 10.8333 20.9167 L 6 14.2778 L 1.16667 20.9167 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸1_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="35.06751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="9" y1="9" y2="9"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.084670781893005" x2="1.626337448559672" y1="4.853614126578682" y2="9.663958954164888"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.084670781893004" x2="12.54300411522634" y1="4.853614126578682" y2="9.663958954164888"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.27315435646374" y2="26.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="31.27315435646374" y2="26.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="7.00133744855967" y1="4.916666666666663" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="1.833333333333333" x2="7.001337448559672" y1="10.75" y2="27"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="7.00133744855967" x2="7.00133744855967" y1="26.75" y2="31.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸1_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="35.06751120764343"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="7.00133744855967" y1="0.7499999999999964" y2="35.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="9" y1="9" y2="9"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸1_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="35.06751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="2.083333333333333" y1="11.16666666666666" y2="25.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.133446357018218" x2="11.79254048508705" y1="11.12564285751284" y2="24.83570582669768"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.27315435646374" y2="27.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.27315435646374" y2="27.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="35.07918883922238" y2="30.26884401163617"/>
  </symbol>
  <symbol id="Breaker:小车母联_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="17.58333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车母联_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="0.8333333333333304" y2="5.749999999999999"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车母联_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="14" y2="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.666666666666666" x2="7.25" y1="5.833333333333333" y2="14.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:2绕组线路PT_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.4166666666666714"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="17" y1="28" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="17.5" y1="9.16666666666667" y2="9.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="9.166666666666671" y2="0.3333333333333321"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.5) scale(1,1) translate(0,0)" width="4" x="13" y="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="23.41666666666666" y2="9.083333333333332"/>
   <ellipse cx="15.15" cy="28.4" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="34" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.16666666666667" x2="17.16666666666667" y1="34.50000000000001" y2="34.50000000000001"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:母线电压互感器11_0" viewBox="0,0,45,48">
   <use terminal-index="0" type="0" x="25.25" xlink:href="#terminal" y="47.78457520218115"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.5,30.95) scale(1,-1) translate(0,-1330.05)" width="6" x="10.5" y="23.95"/>
   <path d="M 8.64167 11.4096 L 4.64167 11.4096 L 6.50709 7.52624 L 7.67376 9.52624 L 8.64167 11.4096" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="13.5" y1="19.78457520218114" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.25" x2="25.25" y1="40.5" y2="47.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="37.25" x2="13.58333333333333" y1="40.5" y2="40.5"/>
   <ellipse cx="13.56" cy="5.42" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799663" x2="13.56620079799663" y1="3.155388266900378" y2="5.82957386674455"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799661" x2="10.88240159599323" y1="5.829573866744575" y2="7.166666666666657"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799662" x2="16.25" y1="5.829573866744575" y2="7.166666666666657"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,36.96,25.94) scale(1,-1) translate(0,-1129.72)" width="7.58" x="33.17" y="17.87"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="34.01790853551448" y2="24.11790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="40.43457520218114" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.7861111111111" x2="39.3" y1="11.60124186884782" y2="11.60124186884782"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.82777777777777" x2="39.93888888888888" y1="12.98282081621623" y2="12.98282081621623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.55" x2="41.21666666666666" y1="14.36439976358464" y2="14.36439976358464"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="34.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="18.01790853551448" y2="14.41790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="40.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <ellipse cx="13.59" cy="15.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.51" cy="10.09" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.67" cy="10.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490864" x2="10.91228003290526" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="13.59607923490865" y1="12.74178419246191" y2="15.41596979230608"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="23.19654510357869" y1="10.49930312563944" y2="11.83639592556152"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="20.51274590157532" y1="7.825117525795246" y2="10.49930312563942"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="16.27987843691203" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5127459015753" x2="17.82894669957193" y1="10.49930312563944" y2="11.83639592556152"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.08333333333333" x2="9.833333333333332" y1="16.5" y2="18.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14" y1="9.75" y2="16.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="11" y1="11.58333333333333" y2="9.666666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="9.75" y1="11.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="15.08333333333333" y1="8.416666666666668" y2="25.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_1" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.96,13) scale(1,1) translate(0,0)" width="4.42" x="12.75" y="9"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="5.416666666666668" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_2" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="20" y1="6" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="10.08333333333333" y1="6.000000000000004" y2="25.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(0,255,127)" fill-opacity="1" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <rect height="12.17" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.75,25.42) scale(1,1) translate(0,0)" width="6" x="8.75" y="19.33"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="16.33333333333333" y2="31.58333333333333"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_1" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <rect fill-opacity="0" height="8" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(255,0,0)" fill-opacity="1" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="7.333333333333334" y2="41.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
   <rect height="12.17" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.75,25.42) scale(1,1) translate(0,0)" width="6" x="8.75" y="19.33"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_2" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.75" x2="14.75" y1="17" y2="32"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.65" x2="8.65" y1="17.1" y2="32.1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(0,255,127)" fill-opacity="1" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="32.35" y2="41.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.71666666666667" x2="11.71666666666667" y1="17.05" y2="7.466666666666663"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
  </symbol>
  <symbol id="Accessory:4绕组母线PT_0" viewBox="0,0,25,20">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0"/>
   <ellipse cx="12.75" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.75" cy="9.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.67" cy="14.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.82" cy="9.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="5.583333333333334" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333336" x2="5.583333333333336" y1="12" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="5.5" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.75" x2="12.75" y1="7.500000000000002" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="17.25" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="19.31481481481482" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.94444444444444" x2="20.62962962962963" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21.94444444444444" y1="10.21612466124661" y2="10.21612466124661"/>
  </symbol>
  <symbol id="Accessory:PT象达_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15.5" xlink:href="#terminal" y="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="18.25" y1="12.08333333333333" y2="12.08333333333333"/>
   <ellipse cx="15.65" cy="12.68" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.9" cy="18.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="18.25" y1="18.33333333333333" y2="18.33333333333333"/>
  </symbol>
  <symbol id="Compensator:并联电容器12121_0" viewBox="0,0,60,30">
   <use terminal-index="0" type="0" x="3.373372509142698" xlink:href="#terminal" y="12.54754746794735"/>
   <rect fill-opacity="0" height="2.8" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,35.96,12.55) scale(1,1) translate(0,0)" width="8.41" x="31.75" y="11.15"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="45.41541591575949" x2="50.34627285851084" y1="12.54754746794735" y2="12.54754746794735"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="47.20609554233762" x2="50.34627285851084" y1="22.75961171517187" y2="22.75961171517187"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.10210217033084" x2="29.01382861144973" y1="22.70770795787974" y2="22.70770795787974"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.92592676741369" x2="55.92592676741369" y1="1.569902800664089" y2="23.73646842528693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.92592676741369" x2="53.12312387363924" y1="23.73646842528693" y2="23.73646842528693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="50.32032097986479" x2="55.92592676741369" y1="12.54754746794735" y2="12.54754746794735"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="53.12312387363924" x2="55.92592676741369" y1="1.569902800664083" y2="1.569902800664083"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="50.32032097986479" x2="50.32032097986479" y1="12.54754746794735" y2="22.72068389720278"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.75175180860903" x2="48.21821880953394" y1="29.01401446887226" y2="29.01401446887226"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.94894891483458" x2="28.94894891483458" y1="22.66878013991066" y2="12.54754746794735"/>
   <path d="M 37.1455 22.7077 A 4.15364 2.547 -90 0 1 32.0515 22.7077" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 42.2395 22.7077 A 4.15364 2.547 -90 0 1 37.1455 22.7077" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 47.1934 22.7077 A 4.15364 2.547 -90 0 1 42.0994 22.7077" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="44.01401446887226" x2="44.01401446887226" y1="10.44544529761651" y2="14.64964963827819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="45.41541591575949" x2="45.41541591575949" y1="10.44544529761651" y2="14.64964963827819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.789455239606355" x2="8.803803115830696" y1="12.51251243177518" y2="12.51251243177518"/>
   <path d="M 15.7174 19.1458 A 6.89022 6.65666 -180 1 0 8.82716 12.4892" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.8108 19.0524 L 15.9276 12.5125 L 44.1308 12.5125" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变无融断_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <path d="M 10 9 L 10 0" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="26.83333333333333" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="10.8868007916835" y2="12.55855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="14.23031840279781" y2="12.55855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
  <symbol id="State:间隔模板_0" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(85,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="State:间隔模板_1" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(185,185,185)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV姐相变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="35kV姐相变_110kV母线.svg"><rect fill-opacity="0" height="40" width="12" x="1101" y="266" zvalue="1053"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="170.09" y="392.25" zvalue="1480"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="68.19" y="392.25" zvalue="1481"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="68.19" y="351.75" zvalue="1482"/></g>
 </g>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="60.16" xlink:href="logo.png" y="21.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="338" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,185.01,62.7136) scale(1,1) translate(-1.33591e-14,0)" writing-mode="lr" x="185.01" xml:space="preserve" y="66.20999999999999" zvalue="1345"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,213.857,62.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="213.86" xml:space="preserve" y="71.69" zvalue="1346">35kV姐相变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="369" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,206.531,404.25) scale(1,1) translate(0,0)" width="72.88" x="170.09" y="392.25" zvalue="1480"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,206.531,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="206.53" xml:space="preserve" y="408.75" zvalue="1480">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="368" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,104.625,404.25) scale(1,1) translate(0,0)" width="72.88" x="68.19" y="392.25" zvalue="1481"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,104.625,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="104.63" xml:space="preserve" y="408.75" zvalue="1481">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="367" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,104.625,363.75) scale(1,1) translate(0,0)" width="72.88" x="68.19" y="351.75" zvalue="1482"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,104.625,363.75) scale(1,1) translate(0,0)" writing-mode="lr" x="104.63" xml:space="preserve" y="368.25" zvalue="1482">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,799.222,300.889) scale(1,1) translate(0,0)" writing-mode="lr" x="799.22" xml:space="preserve" y="305.39" zvalue="7">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1789.67,675.361) scale(1,1) translate(0,-8.86698e-13)" writing-mode="lr" x="1789.67" xml:space="preserve" y="679.86" zvalue="14">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,483.133,672.556) scale(1,1) translate(0,0)" writing-mode="lr" x="483.13" xml:space="preserve" y="677.0599999999999" zvalue="16">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,941.333,236.111) scale(1,1) translate(0,0)" writing-mode="lr" x="941.33" xml:space="preserve" y="240.61" zvalue="17">351</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,932.556,283.667) scale(1,1) translate(0,0)" writing-mode="lr" x="932.5599999999999" xml:space="preserve" y="288.17" zvalue="18">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,934.222,175.667) scale(1,1) translate(0,0)" writing-mode="lr" x="934.22" xml:space="preserve" y="180.17" zvalue="20">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,918.932,77.4444) scale(1,1) translate(0,0)" writing-mode="lr" x="918.9299999999999" xml:space="preserve" y="81.94" zvalue="25">35kV汉相弄线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,873.056,274.778) scale(1,1) translate(0,-4.73893e-13)" writing-mode="lr" x="873.0599999999999" xml:space="preserve" y="279.28" zvalue="27">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,873.056,222.333) scale(1,1) translate(0,0)" writing-mode="lr" x="873.0599999999999" xml:space="preserve" y="226.83" zvalue="29">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,873.056,167.889) scale(1,1) translate(0,0)" writing-mode="lr" x="873.0599999999999" xml:space="preserve" y="172.39" zvalue="32">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1132.56,239.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1132.56" xml:space="preserve" y="244.17" zvalue="94">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1065.33,230.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1065.33" xml:space="preserve" y="235.44" zvalue="96">39017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" x="1104.6875" xml:space="preserve" y="124.4809003406101" zvalue="98">35kV母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1104.6875" xml:space="preserve" y="140.4809003406101" zvalue="98">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,890.778,363.222) scale(1,1) translate(0,0)" writing-mode="lr" x="890.78" xml:space="preserve" y="367.72" zvalue="102">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,900.111,403.222) scale(1,1) translate(0,0)" writing-mode="lr" x="900.11" xml:space="preserve" y="407.72" zvalue="103">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,830.444,392.222) scale(1,1) translate(-9.14762e-13,0)" writing-mode="lr" x="830.4400000000001" xml:space="preserve" y="396.72" zvalue="115">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,904.222,613.556) scale(1,1) translate(0,0)" writing-mode="lr" x="904.22" xml:space="preserve" y="618.0599999999999" zvalue="142">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="304" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,856.059,737.222) scale(1,1) translate(0,0)" writing-mode="lr" x="856.0599999999999" xml:space="preserve" y="741.72" zvalue="634">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="303" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,817.67,788.333) scale(1,1) translate(0,0)" writing-mode="lr" x="817.67" xml:space="preserve" y="792.83" zvalue="636">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,817.67,885.333) scale(1,1) translate(0,0)" writing-mode="lr" x="817.67" xml:space="preserve" y="889.83" zvalue="638">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,842.628,840.444) scale(1,1) translate(0,0)" writing-mode="lr" x="842.63" xml:space="preserve" y="844.9400000000001" zvalue="643">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="300" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,831.015,950.222) scale(1,1) translate(1.7631e-13,0)" writing-mode="lr" x="831.0147847926416" xml:space="preserve" y="954.7222256130642" zvalue="645">10kV俄罗线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="571" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1096,735.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1096" xml:space="preserve" y="739.72" zvalue="879">012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="574" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1161.5,736.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1161.5" xml:space="preserve" y="740.72" zvalue="882">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="604" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1324.61,235.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1324.61" xml:space="preserve" y="239.67" zvalue="1065">352</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="603" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1315.83,282.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1315.83" xml:space="preserve" y="287.22" zvalue="1066">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="602" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1317.5,174.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1317.5" xml:space="preserve" y="179.22" zvalue="1068">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="601" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1302.21,76.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1302.21" xml:space="preserve" y="81" zvalue="1073">35kV勐相线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="600" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1256.33,273.833) scale(1,1) translate(0,4.72215e-13)" writing-mode="lr" x="1256.33" xml:space="preserve" y="278.33" zvalue="1076">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="599" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1256.33,221.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1256.33" xml:space="preserve" y="225.89" zvalue="1078">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="598" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1256.33,166.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1256.33" xml:space="preserve" y="171.44" zvalue="1080">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="681" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,938.5,506) scale(1,1) translate(0,0)" writing-mode="lr" x="938.5" xml:space="preserve" y="510.5" zvalue="1093">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="687" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1064.5,284.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1064.5" xml:space="preserve" y="289.28" zvalue="1100">39010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1101.72,466) scale(1,1) translate(0,0)" writing-mode="lr" x="1101.72" xml:space="preserve" y="470.5" zvalue="1103">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1349.44,362.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1349.44" xml:space="preserve" y="367.06" zvalue="1108">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1358.78,402.556) scale(1,1) translate(0,3.50436e-13)" writing-mode="lr" x="1358.78" xml:space="preserve" y="407.06" zvalue="1110">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1289.11,391.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1289.11" xml:space="preserve" y="396.06" zvalue="1113">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1364.19,612.889) scale(1,1) translate(1.50145e-13,0)" writing-mode="lr" x="1364.19" xml:space="preserve" y="617.39" zvalue="1116">001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1397.17,505.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1397.17" xml:space="preserve" y="509.83" zvalue="1120">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,596,647) scale(1,1) translate(0,0)" writing-mode="lr" x="596" xml:space="preserve" y="651.5" zvalue="1126">0902</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" x="567.5625" xml:space="preserve" y="528.5" zvalue="1131">10kVⅡ段母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="567.5625" xml:space="preserve" y="544.5" zvalue="1131">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1698.19,646) scale(1,1) translate(0,0)" writing-mode="lr" x="1698.19" xml:space="preserve" y="650.5" zvalue="1136">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" x="1669.75" xml:space="preserve" y="527.5" zvalue="1138">10kVⅠ段母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1669.75" xml:space="preserve" y="543.5" zvalue="1138">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1383.03,736.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1383.03" xml:space="preserve" y="740.83" zvalue="1143">053</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1344.64,787.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1344.64" xml:space="preserve" y="791.9400000000001" zvalue="1145">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1344.64,884.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1344.64" xml:space="preserve" y="888.9400000000001" zvalue="1147">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1369.6,839.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1369.6" xml:space="preserve" y="844.0599999999999" zvalue="1151">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1359.5,953.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1359.5" xml:space="preserve" y="957.8333435058595" zvalue="1153">10kV小等喊线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1487.03,737.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1487.03" xml:space="preserve" y="741.83" zvalue="1163">054</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1448.64,788.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1448.64" xml:space="preserve" y="792.9400000000001" zvalue="1165">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1448.64,885.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1448.64" xml:space="preserve" y="889.9400000000001" zvalue="1167">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1473.6,840.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1473.6" xml:space="preserve" y="845.0599999999999" zvalue="1171">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1463,953.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1463" xml:space="preserve" y="957.8333435455959" zvalue="1173">10kV云井线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1594.03,737.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1594.03" xml:space="preserve" y="741.83" zvalue="1183">055</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1555.64,788.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1555.64" xml:space="preserve" y="792.9400000000001" zvalue="1185">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1555.64,885.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1555.64" xml:space="preserve" y="889.9400000000001" zvalue="1187">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1580.6,840.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1580.6" xml:space="preserve" y="845.0599999999999" zvalue="1191">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1569.5,953.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1569.5" xml:space="preserve" y="957.8333435058595" zvalue="1193">10kV户育线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1226.03,736.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1226.03" xml:space="preserve" y="740.83" zvalue="1203">052</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1187.64,787.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1187.64" xml:space="preserve" y="791.9400000000001" zvalue="1205">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="166" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1187.64,884.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1187.64" xml:space="preserve" y="888.9400000000001" zvalue="1207">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1212.6,839.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1212.6" xml:space="preserve" y="844.0599999999999" zvalue="1211">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" x="1200.5" xml:space="preserve" y="948.828125" zvalue="1213">10kV相帮贺</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1200.5" xml:space="preserve" y="964.828125" zvalue="1213">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1261.72,958) scale(1,1) translate(0,0)" writing-mode="lr" x="1261.72" xml:space="preserve" y="962.5" zvalue="1226">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="196" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1703.03,737.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1703.03" xml:space="preserve" y="741.83" zvalue="1234">056</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1720.64,788.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1720.64" xml:space="preserve" y="792.9400000000001" zvalue="1236">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1664.64,885.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1664.64" xml:space="preserve" y="889.9400000000001" zvalue="1238">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1689.6,840.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1689.6" xml:space="preserve" y="845.0599999999999" zvalue="1242">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="212" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1664.64,822.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1664.64" xml:space="preserve" y="826.61" zvalue="1254">60</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="215" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1668.73,970.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1668.730063290596" xml:space="preserve" y="975" zvalue="1256">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,948.5,530.5) scale(1,1) translate(0,0)" writing-mode="lr" x="948.5" xml:space="preserve" y="535" zvalue="1337">5000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="230" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1401.5,531) scale(1,1) translate(0,0)" writing-mode="lr" x="1401.5" xml:space="preserve" y="535.5" zvalue="1339">5000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="231" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1108.5,496) scale(1,1) translate(0,0)" writing-mode="lr" x="1108.5" xml:space="preserve" y="500.5" zvalue="1341">50kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="232" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1266.5,973) scale(1,1) translate(0,0)" writing-mode="lr" x="1266.5" xml:space="preserve" y="977.5" zvalue="1343">50kVA</text>
  <line fill="none" id="335" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.857142857143344" x2="376.8571428571429" y1="147.8704926140825" y2="147.8704926140825" zvalue="1348"/>
  <line fill="none" id="334" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.8571428571431" x2="377.8571428571431" y1="6" y2="1036" zvalue="1349"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.85714285714312" x2="191.8571428571431" y1="160.0000000000001" y2="160.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.85714285714312" x2="191.8571428571431" y1="186.0000000000001" y2="186.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.85714285714312" x2="10.85714285714312" y1="160.0000000000001" y2="186.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="191.8571428571431" y1="160.0000000000001" y2="186.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="372.8571428571431" y1="160.0000000000001" y2="160.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="372.8571428571431" y1="186.0000000000001" y2="186.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="191.8571428571431" y1="160.0000000000001" y2="186.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.8571428571431" x2="372.8571428571431" y1="160.0000000000001" y2="186.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.85714285714312" x2="191.8571428571431" y1="186.0000000000001" y2="186.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.85714285714312" x2="191.8571428571431" y1="210.2500000000001" y2="210.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.85714285714312" x2="10.85714285714312" y1="186.0000000000001" y2="210.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="191.8571428571431" y1="186.0000000000001" y2="210.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="372.8571428571431" y1="186.0000000000001" y2="186.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="372.8571428571431" y1="210.2500000000001" y2="210.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="191.8571428571431" y1="186.0000000000001" y2="210.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.8571428571431" x2="372.8571428571431" y1="186.0000000000001" y2="210.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.85714285714312" x2="191.8571428571431" y1="210.2500000000001" y2="210.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.85714285714312" x2="191.8571428571431" y1="233.0000000000001" y2="233.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.85714285714312" x2="10.85714285714312" y1="210.2500000000001" y2="233.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="191.8571428571431" y1="210.2500000000001" y2="233.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="372.8571428571431" y1="210.2500000000001" y2="210.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="372.8571428571431" y1="233.0000000000001" y2="233.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="191.8571428571431" y1="210.2500000000001" y2="233.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.8571428571431" x2="372.8571428571431" y1="210.2500000000001" y2="233.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.85714285714312" x2="191.8571428571431" y1="233.0000000000001" y2="233.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.85714285714312" x2="191.8571428571431" y1="255.7500000000001" y2="255.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.85714285714312" x2="10.85714285714312" y1="233.0000000000001" y2="255.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="191.8571428571431" y1="233.0000000000001" y2="255.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="372.8571428571431" y1="233.0000000000001" y2="233.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="372.8571428571431" y1="255.7500000000001" y2="255.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="191.8571428571431" y1="233.0000000000001" y2="255.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.8571428571431" x2="372.8571428571431" y1="233.0000000000001" y2="255.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.85714285714312" x2="191.8571428571431" y1="255.7500000000001" y2="255.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.85714285714312" x2="191.8571428571431" y1="278.5000000000001" y2="278.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.85714285714312" x2="10.85714285714312" y1="255.7500000000001" y2="278.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="191.8571428571431" y1="255.7500000000001" y2="278.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="372.8571428571431" y1="255.7500000000001" y2="255.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="372.8571428571431" y1="278.5000000000001" y2="278.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.8571428571431" x2="191.8571428571431" y1="255.7500000000001" y2="278.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.8571428571431" x2="372.8571428571431" y1="255.7500000000001" y2="278.5000000000001"/>
  <line fill="none" id="332" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.857142857143344" x2="376.8571428571429" y1="617.8704926140824" y2="617.8704926140824" zvalue="1351"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.85714285714312" x2="107.6316428571431" y1="440.0000000000001" y2="440.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.85714285714312" x2="107.6316428571431" y1="478.2823000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.85714285714312" x2="61.85714285714312" y1="440.0000000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="107.6316428571431" y1="440.0000000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="166.4380428571432" y1="440.0000000000001" y2="440.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="166.4380428571432" y1="478.2823000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="107.6316428571431" y1="440.0000000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="166.4380428571432" y1="440.0000000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="225.2444428571431" y1="440.0000000000001" y2="440.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="225.2444428571431" y1="478.2823000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="166.4380428571432" y1="440.0000000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2444428571431" x2="225.2444428571431" y1="440.0000000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2443428571431" x2="284.0507428571432" y1="440.0000000000001" y2="440.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2443428571431" x2="284.0507428571432" y1="478.2823000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2443428571431" x2="225.2443428571431" y1="440.0000000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="284.0507428571432" y1="440.0000000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="342.8571428571431" y1="440.0000000000001" y2="440.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="342.8571428571431" y1="478.2823000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="284.0507428571432" y1="440.0000000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="342.8571428571431" x2="342.8571428571431" y1="440.0000000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.85714285714312" x2="107.6316428571431" y1="478.2823000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.85714285714312" x2="107.6316428571431" y1="502.9617000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.85714285714312" x2="61.85714285714312" y1="478.2823000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="107.6316428571431" y1="478.2823000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="166.4380428571432" y1="478.2823000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="166.4380428571432" y1="502.9617000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="107.6316428571431" y1="478.2823000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="166.4380428571432" y1="478.2823000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="225.2444428571431" y1="478.2823000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="225.2444428571431" y1="502.9617000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="166.4380428571432" y1="478.2823000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2444428571431" x2="225.2444428571431" y1="478.2823000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2443428571431" x2="284.0507428571432" y1="478.2823000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2443428571431" x2="284.0507428571432" y1="502.9617000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2443428571431" x2="225.2443428571431" y1="478.2823000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="284.0507428571432" y1="478.2823000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="342.8571428571431" y1="478.2823000000001" y2="478.2823000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="342.8571428571431" y1="502.9617000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="284.0507428571432" y1="478.2823000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="342.8571428571431" x2="342.8571428571431" y1="478.2823000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.85714285714312" x2="107.6316428571431" y1="502.9617000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.85714285714312" x2="107.6316428571431" y1="527.6411000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.85714285714312" x2="61.85714285714312" y1="502.9617000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="107.6316428571431" y1="502.9617000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="166.4380428571432" y1="502.9617000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="166.4380428571432" y1="527.6411000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="107.6316428571431" y1="502.9617000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="166.4380428571432" y1="502.9617000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="225.2444428571431" y1="502.9617000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="225.2444428571431" y1="527.6411000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="166.4380428571432" y1="502.9617000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2444428571431" x2="225.2444428571431" y1="502.9617000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2443428571431" x2="284.0507428571432" y1="502.9617000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2443428571431" x2="284.0507428571432" y1="527.6411000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2443428571431" x2="225.2443428571431" y1="502.9617000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="284.0507428571432" y1="502.9617000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="342.8571428571431" y1="502.9617000000001" y2="502.9617000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="342.8571428571431" y1="527.6411000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="284.0507428571432" y1="502.9617000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="342.8571428571431" x2="342.8571428571431" y1="502.9617000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.85714285714312" x2="107.6316428571431" y1="527.6411000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.85714285714312" x2="107.6316428571431" y1="552.3205" y2="552.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.85714285714312" x2="61.85714285714312" y1="527.6411000000001" y2="552.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="107.6316428571431" y1="527.6411000000001" y2="552.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="166.4380428571432" y1="527.6411000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="166.4380428571432" y1="552.3205" y2="552.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="107.6316428571431" y1="527.6411000000001" y2="552.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="166.4380428571432" y1="527.6411000000001" y2="552.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="225.2444428571431" y1="527.6411000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="225.2444428571431" y1="552.3205" y2="552.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="166.4380428571432" y1="527.6411000000001" y2="552.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2444428571431" x2="225.2444428571431" y1="527.6411000000001" y2="552.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2443428571431" x2="284.0507428571432" y1="527.6411000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2443428571431" x2="284.0507428571432" y1="552.3205" y2="552.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2443428571431" x2="225.2443428571431" y1="527.6411000000001" y2="552.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="284.0507428571432" y1="527.6411000000001" y2="552.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="342.8571428571431" y1="527.6411000000001" y2="527.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="342.8571428571431" y1="552.3205" y2="552.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="284.0507428571432" y1="527.6411000000001" y2="552.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="342.8571428571431" x2="342.8571428571431" y1="527.6411000000001" y2="552.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.85714285714312" x2="107.6316428571431" y1="552.3206" y2="552.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.85714285714312" x2="107.6316428571431" y1="577" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.85714285714312" x2="61.85714285714312" y1="552.3206" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="107.6316428571431" y1="552.3206" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="166.4380428571432" y1="552.3206" y2="552.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="166.4380428571432" y1="577" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="107.6316428571431" y1="552.3206" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="166.4380428571432" y1="552.3206" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="225.2444428571431" y1="552.3206" y2="552.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="225.2444428571431" y1="577" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="166.4380428571432" y1="552.3206" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2444428571431" x2="225.2444428571431" y1="552.3206" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2443428571431" x2="284.0507428571432" y1="552.3206" y2="552.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2443428571431" x2="284.0507428571432" y1="577" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2443428571431" x2="225.2443428571431" y1="552.3206" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="284.0507428571432" y1="552.3206" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="342.8571428571431" y1="552.3206" y2="552.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="342.8571428571431" y1="577" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="284.0507428571432" y1="552.3206" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="342.8571428571431" x2="342.8571428571431" y1="552.3206" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.85714285714312" x2="107.6316428571431" y1="577" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.85714285714312" x2="107.6316428571431" y1="601.6794" y2="601.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.85714285714312" x2="61.85714285714312" y1="577" y2="601.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="107.6316428571431" y1="577" y2="601.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="166.4380428571432" y1="577" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="166.4380428571432" y1="601.6794" y2="601.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.6316428571431" x2="107.6316428571431" y1="577" y2="601.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="166.4380428571432" y1="577" y2="601.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="225.2444428571431" y1="577" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="225.2444428571431" y1="601.6794" y2="601.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.4380428571432" x2="166.4380428571432" y1="577" y2="601.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2444428571431" x2="225.2444428571431" y1="577" y2="601.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2443428571431" x2="284.0507428571432" y1="577" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2443428571431" x2="284.0507428571432" y1="601.6794" y2="601.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="225.2443428571431" x2="225.2443428571431" y1="577" y2="601.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="284.0507428571432" y1="577" y2="601.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="342.8571428571431" y1="577" y2="577"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="342.8571428571431" y1="601.6794" y2="601.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="284.0507428571432" x2="284.0507428571432" y1="577" y2="601.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="342.8571428571431" x2="342.8571428571431" y1="577" y2="601.6794"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="9.857142857143117" x2="99.85714285714312" y1="933" y2="933"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="9.857142857143117" x2="99.85714285714312" y1="972.1632999999999" y2="972.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="9.857142857143117" x2="9.857142857143117" y1="933" y2="972.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.85714285714312" x2="99.85714285714312" y1="933" y2="972.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.85714285714312" x2="369.8571428571431" y1="933" y2="933"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.85714285714312" x2="369.8571428571431" y1="972.1632999999999" y2="972.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.85714285714312" x2="99.85714285714312" y1="933" y2="972.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="369.8571428571431" x2="369.8571428571431" y1="933" y2="972.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="9.857142857143117" x2="99.85714285714312" y1="972.16327" y2="972.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="9.857142857143117" x2="99.85714285714312" y1="1000.08167" y2="1000.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="9.857142857143117" x2="9.857142857143117" y1="972.16327" y2="1000.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.85714285714312" x2="99.85714285714312" y1="972.16327" y2="1000.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.85714285714312" x2="189.8571428571431" y1="972.16327" y2="972.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.85714285714312" x2="189.8571428571431" y1="1000.08167" y2="1000.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.85714285714312" x2="99.85714285714312" y1="972.16327" y2="1000.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="189.8571428571431" x2="189.8571428571431" y1="972.16327" y2="1000.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="189.8571428571432" x2="279.8571428571432" y1="972.16327" y2="972.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="189.8571428571432" x2="279.8571428571432" y1="1000.08167" y2="1000.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="189.8571428571432" x2="189.8571428571432" y1="972.16327" y2="1000.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.8571428571432" x2="279.8571428571432" y1="972.16327" y2="1000.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.8571428571431" x2="369.8571428571431" y1="972.16327" y2="972.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.8571428571431" x2="369.8571428571431" y1="1000.08167" y2="1000.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.8571428571431" x2="279.8571428571431" y1="972.16327" y2="1000.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="369.8571428571431" x2="369.8571428571431" y1="972.16327" y2="1000.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="9.857142857143117" x2="99.85714285714312" y1="1000.0816" y2="1000.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="9.857142857143117" x2="99.85714285714312" y1="1028" y2="1028"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="9.857142857143117" x2="9.857142857143117" y1="1000.0816" y2="1028"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.85714285714312" x2="99.85714285714312" y1="1000.0816" y2="1028"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.85714285714312" x2="189.8571428571431" y1="1000.0816" y2="1000.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.85714285714312" x2="189.8571428571431" y1="1028" y2="1028"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="99.85714285714312" x2="99.85714285714312" y1="1000.0816" y2="1028"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="189.8571428571431" x2="189.8571428571431" y1="1000.0816" y2="1028"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="189.8571428571432" x2="279.8571428571432" y1="1000.0816" y2="1000.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="189.8571428571432" x2="279.8571428571432" y1="1028" y2="1028"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="189.8571428571432" x2="189.8571428571432" y1="1000.0816" y2="1028"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.8571428571432" x2="279.8571428571432" y1="1000.0816" y2="1028"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.8571428571431" x2="369.8571428571431" y1="1000.0816" y2="1000.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.8571428571431" x2="369.8571428571431" y1="1028" y2="1028"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.8571428571431" x2="279.8571428571431" y1="1000.0816" y2="1028"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="369.8571428571431" x2="369.8571428571431" y1="1000.0816" y2="1028"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="328" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.8571,954) scale(1,1) translate(0,0)" writing-mode="lr" x="55.86" xml:space="preserve" y="960" zvalue="1355">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="327" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.8571,988) scale(1,1) translate(0,0)" writing-mode="lr" x="52.86" xml:space="preserve" y="994" zvalue="1356">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="326" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.857,988) scale(1,1) translate(0,0)" writing-mode="lr" x="234.86" xml:space="preserve" y="994" zvalue="1357">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="322" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.8571,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="51.86" xml:space="preserve" y="1022" zvalue="1358">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="313" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.857,1015) scale(1,1) translate(0,0)" writing-mode="lr" x="232.86" xml:space="preserve" y="1021" zvalue="1359">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="309" stroke="rgb(255,255,255)" text-anchor="middle" x="138.1953125" xml:space="preserve" y="457.3993055555555" zvalue="1360">35kV Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="309" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="138.1953125" xml:space="preserve" y="474.3993055555555" zvalue="1360">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="307" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,75.3571,647.5) scale(1,1) translate(0,2.09333e-13)" writing-mode="lr" x="75.35714285714312" xml:space="preserve" y="652.0000000000001" zvalue="1362">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="306" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,208.256,314.841) scale(1,1) translate(0,0)" writing-mode="lr" x="208.26" xml:space="preserve" y="319.34" zvalue="1363">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="305" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,313.256,314.841) scale(1,1) translate(0,0)" writing-mode="lr" x="313.26" xml:space="preserve" y="319.34" zvalue="1364">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="299" stroke="rgb(255,255,255)" text-anchor="middle" x="253.8984375" xml:space="preserve" y="455.8368055555555" zvalue="1365">10kV Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="299" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="253.8984375" xml:space="preserve" y="472.8368055555555" zvalue="1365">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="298" stroke="rgb(255,255,255)" text-anchor="middle" x="313.4296875" xml:space="preserve" y="456.8368055555555" zvalue="1366">10kV Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="298" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="313.4296875" xml:space="preserve" y="473.8368055555555" zvalue="1366">母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="297" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.8571,490.75) scale(1,1) translate(0,0)" writing-mode="lr" x="86.85714285714312" xml:space="preserve" y="495.2500000000001" zvalue="1367">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="296" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.8571,516.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.85714285714312" xml:space="preserve" y="520.75" zvalue="1368">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="295" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.8571,539.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.85714285714312" xml:space="preserve" y="543.7500000000001" zvalue="1369">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="294" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.8571,562.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.85714285714312" xml:space="preserve" y="566.75" zvalue="1370">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="293" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.8571,589.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.85714285714312" xml:space="preserve" y="593.75" zvalue="1371">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.911,955) scale(1,1) translate(0,0)" writing-mode="lr" x="234.91" xml:space="preserve" y="961" zvalue="1372">JieXiang-01-2020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="290" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,324.911,987) scale(1,1) translate(0,0)" writing-mode="lr" x="324.91" xml:space="preserve" y="993" zvalue="1374">20200901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.8571,174) scale(1,1) translate(0,0)" writing-mode="lr" x="48.86" xml:space="preserve" y="179.5" zvalue="1375">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.857,174) scale(1,1) translate(0,0)" writing-mode="lr" x="228.86" xml:space="preserve" y="179.5" zvalue="1376">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.5446,198.25) scale(1,1) translate(0,0)" writing-mode="lr" x="52.54" xml:space="preserve" y="202.75" zvalue="1377">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="286" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.0446,246) scale(1,1) translate(0,0)" writing-mode="lr" x="56.04" xml:space="preserve" y="251.5" zvalue="1378">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.607,245.5) scale(1,1) translate(0,0)" writing-mode="lr" x="237.61" xml:space="preserve" y="251" zvalue="1379">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="281" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.0446,269) scale(1,1) translate(0,0)" writing-mode="lr" x="56.04" xml:space="preserve" y="274.5" zvalue="1380">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="280" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.607,268.5) scale(1,1) translate(0,0)" writing-mode="lr" x="237.61" xml:space="preserve" y="274" zvalue="1381">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="275" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.5446,222.25) scale(1,1) translate(0,0)" writing-mode="lr" x="53.54" xml:space="preserve" y="226.75" zvalue="1382">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="274" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236.607,222) scale(1,1) translate(0,0)" writing-mode="lr" x="236.61" xml:space="preserve" y="226.5" zvalue="1383">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,145.911,986) scale(1,1) translate(0,0)" writing-mode="lr" x="145.91" xml:space="preserve" y="992" zvalue="1484">唐涛</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="205" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,308.235,405) scale(1,1) translate(0,0)" writing-mode="lr" x="308.2350463867188" xml:space="preserve" y="409.5" zvalue="1494">小电流接地</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,106.286,324.539) scale(1,1) translate(0,3.47988e-13)" writing-mode="lr" x="106.2857055664062" xml:space="preserve" y="329.0388641357421" zvalue="1495">全站公用</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="6">
   <path class="kv35" d="M 775 316.67 L 1468 316.67" stroke-width="4" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674396667907" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674396667907"/></metadata>
  <path d="M 775 316.67 L 1468 316.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv10" d="M 1139 690 L 1820.44 690" stroke-width="4" zvalue="13"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674396733443" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674396733443"/></metadata>
  <path d="M 1139 690 L 1820.44 690" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv10" d="M 437.13 688.89 L 1082 688.89" stroke-width="4" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674396798979" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674396798979"/></metadata>
  <path d="M 437.13 688.89 L 1082 688.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="19">
   <use class="kv35" height="20" transform="rotate(0,920,237.111) scale(1.22222,1.11111) translate(-166.162,-22.6)" width="10" x="913.8888888888889" xlink:href="#Breaker:开关_0" y="226" zvalue="16"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925079728133" ObjectName="35kV汉相弄线351断路器"/>
   <cge:TPSR_Ref TObjectID="6473925079728133"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,920,237.111) scale(1.22222,1.11111) translate(-166.162,-22.6)" width="10" x="913.8888888888889" y="226"/></g>
  <g id="101">
   <use class="kv35" height="20" transform="rotate(0,879.333,404.222) scale(1.22222,1.11111) translate(-158.768,-39.3111)" width="10" x="873.2222324079938" xlink:href="#Breaker:开关_0" y="393.1111111111111" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925079793669" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925079793669"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,879.333,404.222) scale(1.22222,1.11111) translate(-158.768,-39.3111)" width="10" x="873.2222324079938" y="393.1111111111111"/></g>
  <g id="141">
   <use class="kv10" height="20" transform="rotate(0,880.444,614.556) scale(2,2) translate(-435.222,-297.278)" width="10" x="870.4444444444445" xlink:href="#Breaker:小车断路器_0" y="594.5555555555557" zvalue="141"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925079859205" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473925079859205"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,880.444,614.556) scale(2,2) translate(-435.222,-297.278)" width="10" x="870.4444444444445" y="594.5555555555557"/></g>
  <g id="325">
   <use class="kv10" height="20" transform="rotate(0,831.059,738.222) scale(2,2) translate(-410.53,-359.111)" width="10" x="821.0591391705666" xlink:href="#Breaker:小车断路器_0" y="718.2222256130643" zvalue="633"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925079924741" ObjectName="10kV俄罗线051断路器"/>
   <cge:TPSR_Ref TObjectID="6473925079924741"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,831.059,738.222) scale(2,2) translate(-410.53,-359.111)" width="10" x="821.0591391705666" y="718.2222256130643"/></g>
  <g id="572">
   <use class="kv10" height="20" transform="rotate(0,1071,739) scale(2,2) translate(-530.5,-359.5)" width="10" x="1061" xlink:href="#Breaker:小车母联_0" y="719" zvalue="878"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925079990277" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473925079990277"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1071,739) scale(2,2) translate(-530.5,-359.5)" width="10" x="1061" y="719"/></g>
  <g id="673">
   <use class="kv35" height="20" transform="rotate(0,1303.28,236.167) scale(1.22222,1.11111) translate(-235.848,-22.5056)" width="10" x="1297.166664971246" xlink:href="#Breaker:开关_0" y="225.0555553436279" zvalue="1063"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925080055813" ObjectName="35kV勐相线352断路器"/>
   <cge:TPSR_Ref TObjectID="6473925080055813"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1303.28,236.167) scale(1.22222,1.11111) translate(-235.848,-22.5056)" width="10" x="1297.166664971246" y="225.0555553436279"/></g>
  <g id="50">
   <use class="kv35" height="20" transform="rotate(0,1338,403.556) scale(1.22222,1.11111) translate(-242.162,-39.2444)" width="10" x="1331.888888902134" xlink:href="#Breaker:开关_0" y="392.4444393581814" zvalue="1109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925080186885" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925080186885"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1338,403.556) scale(1.22222,1.11111) translate(-242.162,-39.2444)" width="10" x="1331.888888902134" y="392.4444393581814"/></g>
  <g id="46">
   <use class="kv10" height="20" transform="rotate(0,1339.11,613.889) scale(2,2) translate(-664.556,-296.944)" width="10" x="1329.111100938585" xlink:href="#Breaker:小车断路器_0" y="593.888883802626" zvalue="1115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925080121349" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925080121349"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1339.11,613.889) scale(2,2) translate(-664.556,-296.944)" width="10" x="1329.111100938585" y="593.888883802626"/></g>
  <g id="122">
   <use class="kv10" height="20" transform="rotate(0,1358.03,737.333) scale(2,2) translate(-674.015,-358.667)" width="10" x="1348.029569585283" xlink:href="#Breaker:小车断路器_0" y="717.3333384195964" zvalue="1142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925080252421" ObjectName="10kV小等喊线053断路器"/>
   <cge:TPSR_Ref TObjectID="6473925080252421"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1358.03,737.333) scale(2,2) translate(-674.015,-358.667)" width="10" x="1348.029569585283" y="717.3333384195964"/></g>
  <g id="142">
   <use class="kv10" height="20" transform="rotate(0,1462.03,738.333) scale(2,2) translate(-726.015,-359.167)" width="10" x="1452.029569585283" xlink:href="#Breaker:小车断路器_0" y="718.3333384195964" zvalue="1162"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925080317957" ObjectName="10kV云井线054断路器"/>
   <cge:TPSR_Ref TObjectID="6473925080317957"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1462.03,738.333) scale(2,2) translate(-726.015,-359.167)" width="10" x="1452.029569585283" y="718.3333384195964"/></g>
  <g id="163">
   <use class="kv10" height="20" transform="rotate(0,1569.03,738.333) scale(2,2) translate(-779.515,-359.167)" width="10" x="1559.029569585283" xlink:href="#Breaker:小车断路器_0" y="718.3333384195964" zvalue="1182"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925080383493" ObjectName="10kV户育线055断路器"/>
   <cge:TPSR_Ref TObjectID="6473925080383493"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1569.03,738.333) scale(2,2) translate(-779.515,-359.167)" width="10" x="1559.029569585283" y="718.3333384195964"/></g>
  <g id="182">
   <use class="kv10" height="20" transform="rotate(0,1201.03,737.333) scale(2,2) translate(-595.515,-358.667)" width="10" x="1191.029569585283" xlink:href="#Breaker:小车断路器_0" y="717.3333384195964" zvalue="1202"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925080449029" ObjectName="10kV相帮贺线052断路器"/>
   <cge:TPSR_Ref TObjectID="6473925080449029"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1201.03,737.333) scale(2,2) translate(-595.515,-358.667)" width="10" x="1191.029569585283" y="717.3333384195964"/></g>
  <g id="210">
   <use class="kv10" height="20" transform="rotate(0,1678.03,738.333) scale(2,2) translate(-834.015,-359.167)" width="10" x="1668.029569585283" xlink:href="#Breaker:小车断路器_0" y="718.3333401150174" zvalue="1233"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925080514565" ObjectName="10kV1号电容器056断路器"/>
   <cge:TPSR_Ref TObjectID="6473925080514565"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1678.03,738.333) scale(2,2) translate(-834.015,-359.167)" width="10" x="1668.029569585283" y="718.3333401150174"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="21">
   <use class="kv35" height="30" transform="rotate(0,920,284.667) scale(-1.11111,-0.814815) translate(-1747.17,-636.808)" width="15" x="911.6666666666666" xlink:href="#Disconnector:刀闸_0" y="272.4444580078125" zvalue="17"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453686329346" ObjectName="35kV汉相弄线3511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453686329346"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,920,284.667) scale(-1.11111,-0.814815) translate(-1747.17,-636.808)" width="15" x="911.6666666666666" y="272.4444580078125"/></g>
  <g id="22">
   <use class="kv35" height="30" transform="rotate(0,920,176.667) scale(-1.11111,-0.814815) translate(-1747.17,-396.263)" width="15" x="911.6666666931577" xlink:href="#Disconnector:刀闸_0" y="164.4444444444445" zvalue="19"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453686394882" ObjectName="35kV汉相弄线3516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453686394882"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,920,176.667) scale(-1.11111,-0.814815) translate(-1747.17,-396.263)" width="15" x="911.6666666931577" y="164.4444444444445"/></g>
  <g id="89">
   <use class="kv35" height="30" transform="rotate(0,1107.22,240.667) scale(-1.11111,-0.814815) translate(-2102.89,-538.808)" width="15" x="1098.888888888889" xlink:href="#Disconnector:刀闸_0" y="228.4444581137762" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453686919170" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453686919170"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1107.22,240.667) scale(-1.11111,-0.814815) translate(-2102.89,-538.808)" width="15" x="1098.888888888889" y="228.4444581137762"/></g>
  <g id="99">
   <use class="kv35" height="30" transform="rotate(0,879.333,364.222) scale(1.11111,0.814815) translate(-87.1,80)" width="15" x="871.0000101725263" xlink:href="#Disconnector:刀闸_0" y="352" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453687181314" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453687181314"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,879.333,364.222) scale(1.11111,0.814815) translate(-87.1,80)" width="15" x="871.0000101725263" y="352"/></g>
  <g id="320">
   <use class="kv10" height="30" transform="rotate(0,830.962,841.444) scale(1.11111,0.814815) translate(-82.2628,188.46)" width="15" x="822.6282757807213" xlink:href="#Disconnector:刀闸_0" y="829.2222256130642" zvalue="641"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453687574530" ObjectName="10kV俄罗线0516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453687574530"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,830.962,841.444) scale(1.11111,0.814815) translate(-82.2628,188.46)" width="15" x="822.6282757807213" y="829.2222256130642"/></g>
  <g id="573">
   <use class="kv10" height="36" transform="rotate(0,1148,738) scale(1,1) translate(0,0)" width="14" x="1141" xlink:href="#Disconnector:联体手车刀闸1_0" y="720" zvalue="881"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453687967746" ObjectName="10kV分段0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453687967746"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1148,738) scale(1,1) translate(0,0)" width="14" x="1141" y="720"/></g>
  <g id="672">
   <use class="kv35" height="30" transform="rotate(0,1303.28,283.722) scale(-1.11111,-0.814815) translate(-2475.39,-634.705)" width="15" x="1294.944442749024" xlink:href="#Disconnector:刀闸_0" y="271.5000133514404" zvalue="1064"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453688819714" ObjectName="35kV勐相线3521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453688819714"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1303.28,283.722) scale(-1.11111,-0.814815) translate(-2475.39,-634.705)" width="15" x="1294.944442749024" y="271.5000133514404"/></g>
  <g id="671">
   <use class="kv35" height="30" transform="rotate(0,1303.28,175.722) scale(-1.11111,-0.814815) translate(-2475.39,-394.159)" width="15" x="1294.944442775515" xlink:href="#Disconnector:刀闸_0" y="163.4999997880724" zvalue="1067"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453688754178" ObjectName="35kV勐相线3526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453688754178"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1303.28,175.722) scale(-1.11111,-0.814815) translate(-2475.39,-394.159)" width="15" x="1294.944442775515" y="163.4999997880724"/></g>
  <g id="9">
   <use class="kv35" height="30" transform="rotate(0,1105.56,348.571) scale(1.00339,0.971429) translate(-3.68661,9.82353)" width="30" x="1090.504673822301" xlink:href="#Disconnector:跌落刀闸_0" y="334" zvalue="1103"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453689081858" ObjectName="#1站用变高压侧跌落开关"/>
   <cge:TPSR_Ref TObjectID="6192453689081858"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1105.56,348.571) scale(1.00339,0.971429) translate(-3.68661,9.82353)" width="30" x="1090.504673822301" y="334"/></g>
  <g id="51">
   <use class="kv35" height="30" transform="rotate(0,1338,363.556) scale(1.11111,0.814815) translate(-132.967,79.8485)" width="15" x="1329.666666666667" xlink:href="#Disconnector:刀闸_0" y="351.3333282470703" zvalue="1107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453689344002" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453689344002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1338,363.556) scale(1.11111,0.814815) translate(-132.967,79.8485)" width="15" x="1329.666666666667" y="351.3333282470703"/></g>
  <g id="68">
   <use class="kv10" height="42" transform="rotate(0,573,640) scale(1,1) translate(0,0)" width="30" x="558" xlink:href="#Disconnector:手车刀闸带避雷器_0" y="619" zvalue="1125"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453689409538" ObjectName="10kVⅡ段母线电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453689409538"/></metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,573,640) scale(1,1) translate(0,0)" width="30" x="558" y="619"/></g>
  <g id="85">
   <use class="kv10" height="42" transform="rotate(0,1675.19,639) scale(1,1) translate(0,0)" width="30" x="1660.193181818182" xlink:href="#Disconnector:手车刀闸带避雷器_0" y="618" zvalue="1135"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453689606146" ObjectName="10kVⅠ段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453689606146"/></metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1675.19,639) scale(1,1) translate(0,0)" width="30" x="1660.193181818182" y="618"/></g>
  <g id="117">
   <use class="kv10" height="30" transform="rotate(0,1357.93,840.556) scale(1.11111,0.814815) translate(-134.96,188.258)" width="15" x="1349.598706195438" xlink:href="#Disconnector:刀闸_0" y="828.3333384195962" zvalue="1149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453689802754" ObjectName="10kV小等喊线0536隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453689802754"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1357.93,840.556) scale(1.11111,0.814815) translate(-134.96,188.258)" width="15" x="1349.598706195438" y="828.3333384195962"/></g>
  <g id="137">
   <use class="kv10" height="30" transform="rotate(0,1461.93,841.556) scale(1.11111,0.814815) translate(-145.36,188.485)" width="15" x="1453.598706195438" xlink:href="#Disconnector:刀闸_0" y="829.3333384195962" zvalue="1169"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453690327042" ObjectName="10kV云井线0546隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453690327042"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1461.93,841.556) scale(1.11111,0.814815) translate(-145.36,188.485)" width="15" x="1453.598706195438" y="829.3333384195962"/></g>
  <g id="159">
   <use class="kv10" height="30" transform="rotate(0,1568.93,841.556) scale(1.11111,0.814815) translate(-156.06,188.485)" width="15" x="1560.598706195438" xlink:href="#Disconnector:刀闸_0" y="829.3333384195962" zvalue="1189"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453690851330" ObjectName="10kV户育线0556隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453690851330"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1568.93,841.556) scale(1.11111,0.814815) translate(-156.06,188.485)" width="15" x="1560.598706195438" y="829.3333384195962"/></g>
  <g id="178">
   <use class="kv10" height="30" transform="rotate(0,1200.93,840.556) scale(1.11111,0.814815) translate(-119.26,188.258)" width="15" x="1192.598706195438" xlink:href="#Disconnector:刀闸_0" y="828.3333384195962" zvalue="1209"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453691375618" ObjectName="10kV相帮贺线0526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453691375618"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1200.93,840.556) scale(1.11111,0.814815) translate(-119.26,188.258)" width="15" x="1192.598706195438" y="828.3333384195962"/></g>
  <g id="187">
   <use class="kv10" height="30" transform="rotate(0,1255.56,847.5) scale(1.00339,0.971429) translate(-4.19371,24.4979)" width="30" x="1240.504673822301" xlink:href="#Disconnector:跌落刀闸_0" y="832.9285714285713" zvalue="1225"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453691834370" ObjectName="#2站用变高压侧跌落开关"/>
   <cge:TPSR_Ref TObjectID="6192453691834370"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1255.56,847.5) scale(1.00339,0.971429) translate(-4.19371,24.4979)" width="30" x="1240.504673822301" y="832.9285714285713"/></g>
  <g id="206">
   <use class="kv10" height="30" transform="rotate(0,1677.93,841.556) scale(1.11111,0.814815) translate(-166.96,188.485)" width="15" x="1669.598706195438" xlink:href="#Disconnector:刀闸_0" y="829.3333401150173" zvalue="1240"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453692096514" ObjectName="10kV1号电容器0566隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453692096514"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1677.93,841.556) scale(1.11111,0.814815) translate(-166.96,188.485)" width="15" x="1669.598706195438" y="829.3333401150173"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="23">
   <path class="kv35" d="M 919.9 296.48 L 919.9 316.67" stroke-width="1" zvalue="20"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@0" LinkObjectIDznd="6@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.9 296.48 L 919.9 316.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv35" d="M 919.93 272.65 L 919.93 247.72" stroke-width="1" zvalue="21"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@1" LinkObjectIDznd="19@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.93 272.65 L 919.93 247.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv35" d="M 919.96 226.48 L 919.9 188.48" stroke-width="1" zvalue="22"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="19@0" LinkObjectIDznd="22@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.96 226.48 L 919.9 188.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv35" d="M 919.93 164.65 L 919.93 122.32" stroke-width="1" zvalue="25"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@1" LinkObjectIDznd="27@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.93 164.65 L 919.93 122.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv35" d="M 882.44 260.28 L 919.93 260.28" stroke-width="1" zvalue="33"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="24" MaxPinNum="2"/>
   </metadata>
  <path d="M 882.44 260.28 L 919.93 260.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv35" d="M 882.44 207.83 L 919.93 207.83" stroke-width="1" zvalue="34"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8@0" LinkObjectIDznd="25" MaxPinNum="2"/>
   </metadata>
  <path d="M 882.44 207.83 L 919.93 207.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv35" d="M 882.44 153.39 L 919.93 153.43" stroke-width="1" zvalue="35"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 882.44 153.39 L 919.93 153.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv35" d="M 1107.12 252.48 L 1107.12 316.67" stroke-width="1" zvalue="98"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="6@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1107.12 252.48 L 1107.12 316.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv35" d="M 1107.15 228.65 L 1107.15 200.59" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@1" LinkObjectIDznd="94@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1107.15 228.65 L 1107.15 200.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv35" d="M 1076.17 214.78 L 1107.15 214.78" stroke-width="1" zvalue="100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="97" MaxPinNum="2"/>
   </metadata>
  <path d="M 1076.17 214.78 L 1107.15 214.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv35" d="M 879.4 376.24 L 879.4 393.59" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@1" LinkObjectIDznd="101@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.4 376.24 L 879.4 393.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv35" d="M 841.28 380.06 L 879.4 380.06" stroke-width="1" zvalue="117"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="108" MaxPinNum="2"/>
   </metadata>
  <path d="M 841.28 380.06 L 879.4 380.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="318">
   <path class="kv10" d="M 831.06 719.72 L 831.06 688.89" stroke-width="1" zvalue="644"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="325@0" LinkObjectIDznd="15@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 831.06 719.72 L 831.06 688.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="317">
   <path class="kv10" d="M 831.06 829.63 L 831.06 756.22" stroke-width="1" zvalue="646"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@0" LinkObjectIDznd="325@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 831.06 829.63 L 831.06 756.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="316">
   <path class="kv10" d="M 831.03 853.46 L 831.03 898.72" stroke-width="1" zvalue="647"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@1" LinkObjectIDznd="319@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 831.03 853.46 L 831.03 898.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="314">
   <path class="kv10" d="M 802.67 778.5 L 802.67 764.22 L 831.06 764.22" stroke-width="1" zvalue="649"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="324@0" LinkObjectIDznd="317" MaxPinNum="2"/>
   </metadata>
  <path d="M 802.67 778.5 L 802.67 764.22 L 831.06 764.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="kv10" d="M 802.67 875.5 L 802.67 864.56 L 831.03 864.56" stroke-width="1" zvalue="651"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="323@0" LinkObjectIDznd="316" MaxPinNum="2"/>
   </metadata>
  <path d="M 802.67 875.5 L 802.67 864.56 L 831.03 864.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv10" d="M 856.09 874.86 L 856.09 863.89 L 831.03 863.89" stroke-width="1" zvalue="652"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="321@0" LinkObjectIDznd="316" MaxPinNum="2"/>
   </metadata>
  <path d="M 856.09 874.86 L 856.09 863.89 L 831.03 863.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="310">
   <path class="kv10" d="M 831.06 801.22 L 831.06 785.22" stroke-width="1" zvalue="653"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="315@0" LinkObjectIDznd="317" MaxPinNum="2"/>
   </metadata>
  <path d="M 831.06 801.22 L 831.06 785.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="576">
   <path class="kv10" d="M 1071 721 L 1071 688.89" stroke-width="1" zvalue="882"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="572@0" LinkObjectIDznd="15@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1071 721 L 1071 688.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="577">
   <path class="kv10" d="M 1071 756.5 L 1071 766 L 1148.02 766 L 1148.02 755.07" stroke-width="1" zvalue="883"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="572@1" LinkObjectIDznd="573@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1071 756.5 L 1071 766 L 1148.02 766 L 1148.02 755.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="580">
   <path class="kv10" d="M 1147.99 720.95 L 1147.99 690" stroke-width="1" zvalue="884"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="573@0" LinkObjectIDznd="13@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1147.99 720.95 L 1147.99 690" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="596">
   <path class="kv35" d="M 932.42 131 L 919.93 131" stroke-width="1" zvalue="1059"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="595@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 932.42 131 L 919.93 131" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="597">
   <path class="kv35" d="M 891.37 131 L 919.93 131" stroke-width="1" zvalue="1060"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="592@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 891.37 131 L 919.93 131" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="670">
   <path class="kv35" d="M 1303.18 295.54 L 1303.18 316.67" stroke-width="1" zvalue="1069"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="672@0" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1303.18 295.54 L 1303.18 316.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="669">
   <path class="kv35" d="M 1303.21 271.71 L 1303.21 246.78" stroke-width="1" zvalue="1070"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="672@1" LinkObjectIDznd="673@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1303.21 271.71 L 1303.21 246.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="668">
   <path class="kv35" d="M 1303.24 225.54 L 1303.18 187.54" stroke-width="1" zvalue="1071"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="673@0" LinkObjectIDznd="671@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1303.24 225.54 L 1303.18 187.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="666">
   <path class="kv35" d="M 1303.21 163.71 L 1303.21 121.38" stroke-width="1" zvalue="1074"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="671@1" LinkObjectIDznd="667@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1303.21 163.71 L 1303.21 121.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="611">
   <path class="kv35" d="M 1265.72 259.33 L 1303.21 259.33" stroke-width="1" zvalue="1081"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="665@0" LinkObjectIDznd="669" MaxPinNum="2"/>
   </metadata>
  <path d="M 1265.72 259.33 L 1303.21 259.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="610">
   <path class="kv35" d="M 1265.72 206.89 L 1303.21 206.89" stroke-width="1" zvalue="1082"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="613@0" LinkObjectIDznd="668" MaxPinNum="2"/>
   </metadata>
  <path d="M 1265.72 206.89 L 1303.21 206.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="609">
   <path class="kv35" d="M 1265.72 152.44 L 1303.21 152.49" stroke-width="1" zvalue="1083"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="612@0" LinkObjectIDznd="666" MaxPinNum="2"/>
   </metadata>
  <path d="M 1265.72 152.44 L 1303.21 152.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="606">
   <path class="kv35" d="M 1315.69 130.06 L 1303.21 130.06" stroke-width="1" zvalue="1086"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="607@0" LinkObjectIDznd="666" MaxPinNum="2"/>
   </metadata>
  <path d="M 1315.69 130.06 L 1303.21 130.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="605">
   <path class="kv35" d="M 1274.64 130.06 L 1303.21 130.06" stroke-width="1" zvalue="1087"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="608@0" LinkObjectIDznd="606" MaxPinNum="2"/>
   </metadata>
  <path d="M 1274.64 130.06 L 1303.21 130.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="682">
   <path class="kv35" d="M 879.43 352.4 L 879.43 316.67" stroke-width="1" zvalue="1093"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="6@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.43 352.4 L 879.43 316.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="683">
   <path class="kv35" d="M 879.41 414.83 L 879.41 467.75" stroke-width="1" zvalue="1094"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@1" LinkObjectIDznd="680@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.41 414.83 L 879.41 467.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="684">
   <path class="kv10" d="M 880.44 596.06 L 880.44 549.41" stroke-width="1" zvalue="1095"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@0" LinkObjectIDznd="680@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 880.44 596.06 L 880.44 549.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="685">
   <path class="kv10" d="M 880.44 632.56 L 880.44 688.89" stroke-width="1" zvalue="1096"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@1" LinkObjectIDznd="15@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 880.44 632.56 L 880.44 688.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="686">
   <path class="kv10" d="M 930.03 578.63 L 930.03 566 L 880.44 566" stroke-width="1" zvalue="1097"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@0" LinkObjectIDznd="684" MaxPinNum="2"/>
   </metadata>
  <path d="M 930.03 578.63 L 930.03 566 L 880.44 566" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="689">
   <path class="kv35" d="M 1076.33 268.61 L 1107.12 268.61" stroke-width="1" zvalue="1101"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="688@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 1076.33 268.61 L 1107.12 268.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv35" d="M 1105.56 386.53 L 1105.56 362.17" stroke-width="1" zvalue="1104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="3@0" LinkObjectIDznd="9@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1105.56 386.53 L 1105.56 362.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv35" d="M 1105.56 334 L 1105.56 316.67" stroke-width="1" zvalue="1105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="6@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1105.56 334 L 1105.56 316.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv35" d="M 1338.07 375.57 L 1338.07 392.93" stroke-width="1" zvalue="1111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@1" LinkObjectIDznd="50@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1338.07 375.57 L 1338.07 392.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv35" d="M 1299.94 379.39 L 1338.07 379.39" stroke-width="1" zvalue="1114"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@0" LinkObjectIDznd="49" MaxPinNum="2"/>
   </metadata>
  <path d="M 1299.94 379.39 L 1338.07 379.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv35" d="M 1338.1 351.74 L 1338.1 316.67" stroke-width="1" zvalue="1119"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="6@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1338.1 351.74 L 1338.1 316.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv35" d="M 1338.08 414.17 L 1338.08 467.08" stroke-width="1" zvalue="1121"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@1" LinkObjectIDznd="44@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1338.08 414.17 L 1338.08 467.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv10" d="M 1339.11 595.39 L 1339.11 548.75" stroke-width="1" zvalue="1122"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="44@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1339.11 595.39 L 1339.11 548.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv10" d="M 1339.11 631.89 L 1339.11 690" stroke-width="1" zvalue="1123"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@1" LinkObjectIDznd="13@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1339.11 631.89 L 1339.11 690" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv10" d="M 1388.7 577.97 L 1388.7 565.33 L 1339.11 565.33" stroke-width="1" zvalue="1124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="41" MaxPinNum="2"/>
   </metadata>
  <path d="M 1388.7 577.97 L 1388.7 565.33 L 1339.11 565.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv10" d="M 569.75 596.48 L 569.75 626.25" stroke-width="1" zvalue="1132"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@0" LinkObjectIDznd="68@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 569.75 596.48 L 569.75 626.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv10" d="M 569.75 660.75 L 569.75 688.89" stroke-width="1" zvalue="1133"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@1" LinkObjectIDznd="15@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 569.75 660.75 L 569.75 688.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv10" d="M 1671.94 595.48 L 1671.94 625.25" stroke-width="1" zvalue="1139"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="85@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1671.94 595.48 L 1671.94 625.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv10" d="M 1671.94 659.75 L 1671.94 690" stroke-width="1" zvalue="1140"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@1" LinkObjectIDznd="13@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1671.94 659.75 L 1671.94 690" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv10" d="M 1358.03 718.83 L 1358.03 690" stroke-width="1" zvalue="1152"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@0" LinkObjectIDznd="13@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1358.03 718.83 L 1358.03 690" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv10" d="M 1358.03 828.74 L 1358.03 755.33" stroke-width="1" zvalue="1154"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="122@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1358.03 828.74 L 1358.03 755.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv10" d="M 1358 852.57 L 1358 897.83" stroke-width="1" zvalue="1155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@1" LinkObjectIDznd="115@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1358 852.57 L 1358 897.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 1329.64 777.61 L 1329.64 763.33 L 1358.03 763.33" stroke-width="1" zvalue="1157"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@0" LinkObjectIDznd="112" MaxPinNum="2"/>
   </metadata>
  <path d="M 1329.64 777.61 L 1329.64 763.33 L 1358.03 763.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv10" d="M 1329.64 874.61 L 1329.64 863.67 L 1358 863.67" stroke-width="1" zvalue="1158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="111" MaxPinNum="2"/>
   </metadata>
  <path d="M 1329.64 874.61 L 1329.64 863.67 L 1358 863.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv10" d="M 1383.06 873.97 L 1383.06 863 L 1358 863" stroke-width="1" zvalue="1159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="111" MaxPinNum="2"/>
   </metadata>
  <path d="M 1383.06 873.97 L 1383.06 863 L 1358 863" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv10" d="M 1358.03 800.33 L 1358.03 784.33" stroke-width="1" zvalue="1160"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="112" MaxPinNum="2"/>
   </metadata>
  <path d="M 1358.03 800.33 L 1358.03 784.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv10" d="M 1462.03 719.83 L 1462.03 690" stroke-width="1" zvalue="1172"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@0" LinkObjectIDznd="13@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1462.03 719.83 L 1462.03 690" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv10" d="M 1462.03 829.74 L 1462.03 756.33" stroke-width="1" zvalue="1174"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="142@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1462.03 829.74 L 1462.03 756.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv10" d="M 1462 853.57 L 1462 898.83" stroke-width="1" zvalue="1175"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@1" LinkObjectIDznd="136@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1462 853.57 L 1462 898.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv10" d="M 1433.64 778.61 L 1433.64 764.33 L 1462.03 764.33" stroke-width="1" zvalue="1177"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="134" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.64 778.61 L 1433.64 764.33 L 1462.03 764.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="kv10" d="M 1433.64 875.61 L 1433.64 864.67 L 1462 864.67" stroke-width="1" zvalue="1178"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@0" LinkObjectIDznd="133" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.64 875.61 L 1433.64 864.67 L 1462 864.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv10" d="M 1487.06 874.97 L 1487.06 864 L 1462 864" stroke-width="1" zvalue="1179"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@0" LinkObjectIDznd="133" MaxPinNum="2"/>
   </metadata>
  <path d="M 1487.06 874.97 L 1487.06 864 L 1462 864" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv10" d="M 1462.03 801.33 L 1462.03 785.33" stroke-width="1" zvalue="1180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="134" MaxPinNum="2"/>
   </metadata>
  <path d="M 1462.03 801.33 L 1462.03 785.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv10" d="M 1569.03 719.83 L 1569.03 690" stroke-width="1" zvalue="1192"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@0" LinkObjectIDznd="13@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1569.03 719.83 L 1569.03 690" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv10" d="M 1569.03 829.74 L 1569.03 756.33" stroke-width="1" zvalue="1194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="163@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1569.03 829.74 L 1569.03 756.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv10" d="M 1569 853.57 L 1569 898.83" stroke-width="1" zvalue="1195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@1" LinkObjectIDznd="158@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1569 853.57 L 1569 898.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv10" d="M 1540.64 778.61 L 1540.64 764.33 L 1569.03 764.33" stroke-width="1" zvalue="1197"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162@0" LinkObjectIDznd="156" MaxPinNum="2"/>
   </metadata>
  <path d="M 1540.64 778.61 L 1540.64 764.33 L 1569.03 764.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv10" d="M 1540.64 875.61 L 1540.64 864.67 L 1569 864.67" stroke-width="1" zvalue="1198"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="155" MaxPinNum="2"/>
   </metadata>
  <path d="M 1540.64 875.61 L 1540.64 864.67 L 1569 864.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv10" d="M 1594.06 874.97 L 1594.06 864 L 1569 864" stroke-width="1" zvalue="1199"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160@0" LinkObjectIDznd="155" MaxPinNum="2"/>
   </metadata>
  <path d="M 1594.06 874.97 L 1594.06 864 L 1569 864" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv10" d="M 1569.03 801.33 L 1569.03 785.33" stroke-width="1" zvalue="1200"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@0" LinkObjectIDznd="156" MaxPinNum="2"/>
   </metadata>
  <path d="M 1569.03 801.33 L 1569.03 785.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="kv10" d="M 1201.03 718.83 L 1201.03 690" stroke-width="1" zvalue="1212"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="13@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1201.03 718.83 L 1201.03 690" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv10" d="M 1201.03 828.74 L 1201.03 755.33" stroke-width="1" zvalue="1214"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="182@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1201.03 828.74 L 1201.03 755.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="kv10" d="M 1201 852.57 L 1201 897.83" stroke-width="1" zvalue="1215"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@1" LinkObjectIDznd="177@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1201 852.57 L 1201 897.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv10" d="M 1172.64 777.61 L 1172.64 763.33 L 1201.03 763.33" stroke-width="1" zvalue="1217"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="175" MaxPinNum="2"/>
   </metadata>
  <path d="M 1172.64 777.61 L 1172.64 763.33 L 1201.03 763.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv10" d="M 1172.64 874.61 L 1172.64 863.67 L 1201 863.67" stroke-width="1" zvalue="1218"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@0" LinkObjectIDznd="174" MaxPinNum="2"/>
   </metadata>
  <path d="M 1172.64 874.61 L 1172.64 863.67 L 1201 863.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv10" d="M 1226.06 873.97 L 1226.06 863 L 1201 863" stroke-width="1" zvalue="1219"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@0" LinkObjectIDznd="174" MaxPinNum="2"/>
   </metadata>
  <path d="M 1226.06 873.97 L 1226.06 863 L 1201 863" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv10" d="M 1201.03 800.33 L 1201.03 784.33" stroke-width="1" zvalue="1220"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="173@0" LinkObjectIDznd="175" MaxPinNum="2"/>
   </metadata>
  <path d="M 1201.03 800.33 L 1201.03 784.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="184">
   <path class="kv10" d="M 1228.5 780.67 L 1228.5 763 L 1201.03 763" stroke-width="1" zvalue="1222"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@0" LinkObjectIDznd="175" MaxPinNum="2"/>
   </metadata>
  <path d="M 1228.5 780.67 L 1228.5 763 L 1201.03 763" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv10" d="M 1255.56 879.53 L 1255.56 861.1" stroke-width="1" zvalue="1227"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@0" LinkObjectIDznd="187@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1255.56 879.53 L 1255.56 861.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="kv10" d="M 1282.03 877.63 L 1282.03 869.68 L 1255.56 869.68" stroke-width="1" zvalue="1231"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@0" LinkObjectIDznd="186" MaxPinNum="2"/>
   </metadata>
  <path d="M 1282.03 877.63 L 1282.03 869.68 L 1255.56 869.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="kv10" d="M 1678.03 719.83 L 1678.03 690" stroke-width="1" zvalue="1243"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="210@0" LinkObjectIDznd="13@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1678.03 719.83 L 1678.03 690" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="203">
   <path class="kv10" d="M 1678.03 829.74 L 1678.03 756.33" stroke-width="1" zvalue="1245"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@0" LinkObjectIDznd="210@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1678.03 829.74 L 1678.03 756.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv10" d="M 1678 853.57 L 1678 881.94" stroke-width="1" zvalue="1246"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@1" LinkObjectIDznd="214@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1678 853.57 L 1678 881.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="kv10" d="M 1705.64 778.61 L 1705.64 764.33 L 1678.03 764.33" stroke-width="1" zvalue="1248"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="209@0" LinkObjectIDznd="203" MaxPinNum="2"/>
   </metadata>
  <path d="M 1705.64 778.61 L 1705.64 764.33 L 1678.03 764.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="kv10" d="M 1649.64 875.61 L 1649.64 864.67 L 1678 864.67" stroke-width="1" zvalue="1249"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@0" LinkObjectIDznd="202" MaxPinNum="2"/>
   </metadata>
  <path d="M 1649.64 875.61 L 1649.64 864.67 L 1678 864.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="kv10" d="M 1703.06 874.97 L 1703.06 864 L 1678 864" stroke-width="1" zvalue="1250"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="202" MaxPinNum="2"/>
   </metadata>
  <path d="M 1703.06 874.97 L 1703.06 864 L 1678 864" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv10" d="M 1678.03 773.33 L 1678.03 785.33" stroke-width="1" zvalue="1251"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@0" LinkObjectIDznd="203" MaxPinNum="2"/>
   </metadata>
  <path d="M 1678.03 773.33 L 1678.03 785.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv10" d="M 1649.64 812.28 L 1649.64 803 L 1678.03 803" stroke-width="1" zvalue="1254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@0" LinkObjectIDznd="203" MaxPinNum="2"/>
   </metadata>
  <path d="M 1649.64 812.28 L 1649.64 803 L 1678.03 803" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv10" d="M 1201.03 810.29 L 1255.56 810.29 L 1255.56 832.93" stroke-width="1" zvalue="1426"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175" LinkObjectIDznd="187@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1201.03 810.29 L 1255.56 810.29 L 1255.56 832.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="27">
   <use class="kv35" height="30" transform="rotate(0,919.932,110.222) scale(6.34921,0.814815) translate(-756.321,22.2727)" width="7" x="897.7098173330401" xlink:href="#ACLineSegment:线路_0" y="98" zvalue="24"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249323012100" ObjectName="35kV汉相弄线姐相变侧"/>
   <cge:TPSR_Ref TObjectID="8444249323012100_5066549678243841"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,919.932,110.222) scale(6.34921,0.814815) translate(-756.321,22.2727)" width="7" x="897.7098173330401" y="98"/></g>
  <g id="667">
   <use class="kv35" height="30" transform="rotate(0,1303.21,109.278) scale(6.34921,0.814815) translate(-1079.23,22.0581)" width="7" x="1280.987593415397" xlink:href="#ACLineSegment:线路_0" y="97.05555534362793" zvalue="1072"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249332383746" ObjectName="35kV勐相线"/>
   <cge:TPSR_Ref TObjectID="8444249332383746_5066549678243841"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1303.21,109.278) scale(6.34921,0.814815) translate(-1079.23,22.0581)" width="7" x="1280.987593415397" y="97.05555534362793"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="1">
   <use class="kv35" height="20" transform="rotate(90,871.611,260.222) scale(1.11111,1.11111) translate(-86.6056,-24.9111)" width="10" x="866.0555556615193" xlink:href="#GroundDisconnector:地刀_0" y="249.1111111111111" zvalue="26"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453686591490" ObjectName="35kV汉相弄线35117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453686591490"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,871.611,260.222) scale(1.11111,1.11111) translate(-86.6056,-24.9111)" width="10" x="866.0555556615193" y="249.1111111111111"/></g>
  <g id="8">
   <use class="kv35" height="20" transform="rotate(90,871.611,207.778) scale(1.11111,1.11111) translate(-86.6056,-19.6667)" width="10" x="866.0555556615193" xlink:href="#GroundDisconnector:地刀_0" y="196.6666666666667" zvalue="28"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453686722562" ObjectName="35kV汉相弄线35160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453686722562"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,871.611,207.778) scale(1.11111,1.11111) translate(-86.6056,-19.6667)" width="10" x="866.0555556615193" y="196.6666666666667"/></g>
  <g id="32">
   <use class="kv35" height="20" transform="rotate(90,871.611,153.333) scale(1.11111,1.11111) translate(-86.6056,-14.2222)" width="10" x="866.0555555555555" xlink:href="#GroundDisconnector:地刀_0" y="142.2222222222221" zvalue="31"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453686853634" ObjectName="35kV汉相弄线35167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453686853634"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,871.611,153.333) scale(1.11111,1.11111) translate(-86.6056,-14.2222)" width="10" x="866.0555555555555" y="142.2222222222221"/></g>
  <g id="91">
   <use class="kv35" height="20" transform="rotate(90,1065.33,214.722) scale(1.11111,1.11111) translate(-105.978,-20.3611)" width="10" x="1059.777777777778" xlink:href="#GroundDisconnector:地刀_0" y="203.6111008326212" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453687050242" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453687050242"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1065.33,214.722) scale(1.11111,1.11111) translate(-105.978,-20.3611)" width="10" x="1059.777777777778" y="203.6111008326212"/></g>
  <g id="114">
   <use class="kv35" height="20" transform="rotate(90,830.444,380) scale(1.11111,1.11111) translate(-82.4889,-36.8889)" width="10" x="824.8888990614149" xlink:href="#GroundDisconnector:地刀_0" y="368.8888888888889" zvalue="114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453687312386" ObjectName="#2主变35kV侧30217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453687312386"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,830.444,380) scale(1.11111,1.11111) translate(-82.4889,-36.8889)" width="10" x="824.8888990614149" y="368.8888888888889"/></g>
  <g id="324">
   <use class="kv10" height="20" transform="rotate(0,802.615,789.333) scale(1.11111,1.11111) translate(-79.7059,-77.8222)" width="10" x="797.0591391705665" xlink:href="#GroundDisconnector:地刀_0" y="778.2222256130642" zvalue="635"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453687902210" ObjectName="10kV俄罗线05110接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453687902210"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,802.615,789.333) scale(1.11111,1.11111) translate(-79.7059,-77.8222)" width="10" x="797.0591391705665" y="778.2222256130642"/></g>
  <g id="323">
   <use class="kv10" height="20" transform="rotate(0,802.615,886.333) scale(1.11111,1.11111) translate(-79.7059,-87.5222)" width="10" x="797.0591391705665" xlink:href="#GroundDisconnector:地刀_0" y="875.2222256130642" zvalue="637"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453687771138" ObjectName="10kV俄罗线05167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453687771138"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,802.615,886.333) scale(1.11111,1.11111) translate(-79.7059,-87.5222)" width="10" x="797.0591391705665" y="875.2222256130642"/></g>
  <g id="665">
   <use class="kv35" height="20" transform="rotate(90,1254.89,259.278) scale(1.11111,1.11111) translate(-124.933,-24.8167)" width="10" x="1249.333331743876" xlink:href="#GroundDisconnector:地刀_0" y="248.166666454739" zvalue="1075"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453688623106" ObjectName="35kV勐相线35217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453688623106"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1254.89,259.278) scale(1.11111,1.11111) translate(-124.933,-24.8167)" width="10" x="1249.333331743876" y="248.166666454739"/></g>
  <g id="613">
   <use class="kv35" height="20" transform="rotate(90,1254.89,206.833) scale(1.11111,1.11111) translate(-124.933,-19.5722)" width="10" x="1249.333331743876" xlink:href="#GroundDisconnector:地刀_0" y="195.7222220102947" zvalue="1077"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453688492034" ObjectName="35kV勐相线35260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453688492034"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1254.89,206.833) scale(1.11111,1.11111) translate(-124.933,-19.5722)" width="10" x="1249.333331743876" y="195.7222220102947"/></g>
  <g id="612">
   <use class="kv35" height="20" transform="rotate(90,1254.89,152.389) scale(1.11111,1.11111) translate(-124.933,-14.1278)" width="10" x="1249.333331637913" xlink:href="#GroundDisconnector:地刀_0" y="141.2777775658501" zvalue="1079"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453688360962" ObjectName="35kV勐相线35267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453688360962"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1254.89,152.389) scale(1.11111,1.11111) translate(-124.933,-14.1278)" width="10" x="1249.333331637913" y="141.2777775658501"/></g>
  <g id="688">
   <use class="kv35" height="20" transform="rotate(90,1065.5,268.556) scale(1.11111,1.11111) translate(-105.994,-25.7444)" width="10" x="1059.944444444444" xlink:href="#GroundDisconnector:地刀_0" y="257.4444444444444" zvalue="1099"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453688950786" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453688950786"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1065.5,268.556) scale(1.11111,1.11111) translate(-105.994,-25.7444)" width="10" x="1059.944444444444" y="257.4444444444444"/></g>
  <g id="48">
   <use class="kv35" height="20" transform="rotate(90,1289.11,379.333) scale(1.11111,1.11111) translate(-128.356,-36.8222)" width="10" x="1283.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="368.2222171359592" zvalue="1112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453689278466" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453689278466"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1289.11,379.333) scale(1.11111,1.11111) translate(-128.356,-36.8222)" width="10" x="1283.555555555556" y="368.2222171359592"/></g>
  <g id="121">
   <use class="kv10" height="20" transform="rotate(0,1329.59,788.444) scale(1.11111,1.11111) translate(-132.403,-77.7333)" width="10" x="1324.029569585283" xlink:href="#GroundDisconnector:地刀_0" y="777.3333384195963" zvalue="1144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453690130434" ObjectName="10kV小等喊线05310接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453690130434"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1329.59,788.444) scale(1.11111,1.11111) translate(-132.403,-77.7333)" width="10" x="1324.029569585283" y="777.3333384195963"/></g>
  <g id="120">
   <use class="kv10" height="20" transform="rotate(0,1329.59,885.444) scale(1.11111,1.11111) translate(-132.403,-87.4333)" width="10" x="1324.029569585283" xlink:href="#GroundDisconnector:地刀_0" y="874.3333384195963" zvalue="1146"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453689999362" ObjectName="10kV小等喊线05367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453689999362"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1329.59,885.444) scale(1.11111,1.11111) translate(-132.403,-87.4333)" width="10" x="1324.029569585283" y="874.3333384195963"/></g>
  <g id="140">
   <use class="kv10" height="20" transform="rotate(0,1433.59,789.444) scale(1.11111,1.11111) translate(-142.803,-77.8333)" width="10" x="1428.029569585283" xlink:href="#GroundDisconnector:地刀_0" y="778.3333384195963" zvalue="1164"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453690654722" ObjectName="10kV云井线05410接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453690654722"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1433.59,789.444) scale(1.11111,1.11111) translate(-142.803,-77.8333)" width="10" x="1428.029569585283" y="778.3333384195963"/></g>
  <g id="139">
   <use class="kv10" height="20" transform="rotate(0,1433.59,886.444) scale(1.11111,1.11111) translate(-142.803,-87.5333)" width="10" x="1428.029569585283" xlink:href="#GroundDisconnector:地刀_0" y="875.3333384195963" zvalue="1166"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453690523650" ObjectName="10kV云井线05467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453690523650"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1433.59,886.444) scale(1.11111,1.11111) translate(-142.803,-87.5333)" width="10" x="1428.029569585283" y="875.3333384195963"/></g>
  <g id="162">
   <use class="kv10" height="20" transform="rotate(0,1540.59,789.444) scale(1.11111,1.11111) translate(-153.503,-77.8333)" width="10" x="1535.029569585283" xlink:href="#GroundDisconnector:地刀_0" y="778.3333384195963" zvalue="1184"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453691179010" ObjectName="10kV户育线05510接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453691179010"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1540.59,789.444) scale(1.11111,1.11111) translate(-153.503,-77.8333)" width="10" x="1535.029569585283" y="778.3333384195963"/></g>
  <g id="161">
   <use class="kv10" height="20" transform="rotate(0,1540.59,886.444) scale(1.11111,1.11111) translate(-153.503,-87.5333)" width="10" x="1535.029569585283" xlink:href="#GroundDisconnector:地刀_0" y="875.3333384195963" zvalue="1186"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453691047938" ObjectName="10kV户育线05567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453691047938"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1540.59,886.444) scale(1.11111,1.11111) translate(-153.503,-87.5333)" width="10" x="1535.029569585283" y="875.3333384195963"/></g>
  <g id="181">
   <use class="kv10" height="20" transform="rotate(0,1172.59,788.444) scale(1.11111,1.11111) translate(-116.703,-77.7333)" width="10" x="1167.029569585283" xlink:href="#GroundDisconnector:地刀_0" y="777.3333384195963" zvalue="1204"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453691703298" ObjectName="10kV相帮贺线05210接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453691703298"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1172.59,788.444) scale(1.11111,1.11111) translate(-116.703,-77.7333)" width="10" x="1167.029569585283" y="777.3333384195963"/></g>
  <g id="180">
   <use class="kv10" height="20" transform="rotate(0,1172.59,885.444) scale(1.11111,1.11111) translate(-116.703,-87.4333)" width="10" x="1167.029569585283" xlink:href="#GroundDisconnector:地刀_0" y="874.3333384195963" zvalue="1206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453691572226" ObjectName="10kV相帮贺线05267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453691572226"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1172.59,885.444) scale(1.11111,1.11111) translate(-116.703,-87.4333)" width="10" x="1167.029569585283" y="874.3333384195963"/></g>
  <g id="209">
   <use class="kv10" height="20" transform="rotate(0,1705.59,789.444) scale(1.11111,1.11111) translate(-170.003,-77.8333)" width="10" x="1700.029569585283" xlink:href="#GroundDisconnector:地刀_0" y="778.3333401150173" zvalue="1235"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453692424194" ObjectName="10kV1号电容器05610接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453692424194"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1705.59,789.444) scale(1.11111,1.11111) translate(-170.003,-77.8333)" width="10" x="1700.029569585283" y="778.3333401150173"/></g>
  <g id="208">
   <use class="kv10" height="20" transform="rotate(0,1649.59,886.444) scale(1.11111,1.11111) translate(-164.403,-87.5333)" width="10" x="1644.029574924045" xlink:href="#GroundDisconnector:地刀_0" y="875.3333401150173" zvalue="1237"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453692293122" ObjectName="10kV1号电容器05667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453692293122"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1649.59,886.444) scale(1.11111,1.11111) translate(-164.403,-87.5333)" width="10" x="1644.029574924045" y="875.3333401150173"/></g>
  <g id="211">
   <use class="kv10" height="20" transform="rotate(0,1649.59,823.111) scale(1.11111,1.11111) translate(-164.403,-81.2)" width="10" x="1644.0295753479" xlink:href="#GroundDisconnector:地刀_0" y="812" zvalue="1253"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453692555266" ObjectName="10kV1号电容器05660接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453692555266"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1649.59,823.111) scale(1.11111,1.11111) translate(-164.403,-81.2)" width="10" x="1644.0295753479" y="812"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="94">
   <use class="kv35" height="48" transform="rotate(0,1103.34,173.527) scale(1.38692,1.13771) translate(-299.101,-17.6986)" width="45" x="1072.134556020327" xlink:href="#Accessory:母线电压互感器11_0" y="146.222222222222" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453687115778" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,1103.34,173.527) scale(1.38692,1.13771) translate(-299.101,-17.6986)" width="45" x="1072.134556020327" y="146.222222222222"/></g>
  <g id="39">
   <use class="kv10" height="26" transform="rotate(0,930,591) scale(1,1) translate(0,0)" width="12" x="924" xlink:href="#Accessory:避雷器1_0" y="578" zvalue="147"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453687377922" ObjectName="#2主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,930,591) scale(1,1) translate(0,0)" width="12" x="924" y="578"/></g>
  <g id="321">
   <use class="kv10" height="26" transform="rotate(0,856.059,887.222) scale(1,1) translate(0,0)" width="12" x="850.0591391705666" xlink:href="#Accessory:避雷器1_0" y="874.2222256130642" zvalue="640"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453687640066" ObjectName="10kV俄罗线避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,856.059,887.222) scale(1,1) translate(0,0)" width="12" x="850.0591391705666" y="874.2222256130642"/></g>
  <g id="315">
   <use class="kv10" height="22" transform="rotate(0,831.059,811.222) scale(1,1) translate(0,0)" width="12" x="825.0591391705666" xlink:href="#Accessory:传输线_0" y="800.2222256130642" zvalue="648"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453687443458" ObjectName="10kV俄罗线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,831.059,811.222) scale(1,1) translate(0,0)" width="12" x="825.0591391705666" y="800.2222256130642"/></g>
  <g id="592">
   <use class="kv35" height="26" transform="rotate(90,879,131.033) scale(-1,1) translate(-1758,0)" width="12" x="873" xlink:href="#Accessory:避雷器1_0" y="118.0333333333334" zvalue="1055"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453688033282" ObjectName="35kV汉相弄线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,879,131.033) scale(-1,1) translate(-1758,0)" width="12" x="873" y="118.0333333333334"/></g>
  <g id="595">
   <use class="kv35" height="40" transform="rotate(270,952,131) scale(1,1) translate(0,0)" width="30" x="937" xlink:href="#Accessory:2绕组线路PT_0" y="111" zvalue="1058"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453688098818" ObjectName="35kV汉相弄线线路避雷器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,952,131) scale(1,1) translate(0,0)" width="30" x="937" y="111"/></g>
  <g id="608">
   <use class="kv35" height="26" transform="rotate(90,1262.28,130.089) scale(-1,1) translate(-2524.56,0)" width="12" x="1256.277776082357" xlink:href="#Accessory:避雷器1_0" y="117.0888886769612" zvalue="1084"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453688229890" ObjectName="35kV勐相线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1262.28,130.089) scale(-1,1) translate(-2524.56,0)" width="12" x="1256.277776082357" y="117.0888886769612"/></g>
  <g id="607">
   <use class="kv35" height="40" transform="rotate(270,1335.28,130.056) scale(1,1) translate(0,0)" width="30" x="1320.277776082357" xlink:href="#Accessory:2绕组线路PT_0" y="110.0555553436279" zvalue="1085"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453688164354" ObjectName="35kV勐相线线路避雷器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,1335.28,130.056) scale(1,1) translate(0,0)" width="30" x="1320.277776082357" y="110.0555553436279"/></g>
  <g id="45">
   <use class="kv10" height="26" transform="rotate(0,1388.67,590.333) scale(1,1) translate(0,0)" width="12" x="1382.666656494141" xlink:href="#Accessory:避雷器1_0" y="577.3333282470703" zvalue="1117"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453689147394" ObjectName="#1主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1388.67,590.333) scale(1,1) translate(0,0)" width="12" x="1382.666656494141" y="577.3333282470703"/></g>
  <g id="75">
   <use class="kv10" height="20" transform="rotate(0,570.239,575.5) scale(1.95455,-2.15) translate(-266.557,-831.674)" width="25" x="545.8068181818182" xlink:href="#Accessory:4绕组母线PT_0" y="554" zvalue="1130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453689475074" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,570.239,575.5) scale(1.95455,-2.15) translate(-266.557,-831.674)" width="25" x="545.8068181818182" y="554"/></g>
  <g id="84">
   <use class="kv10" height="20" transform="rotate(0,1672.43,574.5) scale(1.95455,-2.15) translate(-804.837,-830.209)" width="25" x="1648" xlink:href="#Accessory:4绕组母线PT_0" y="553" zvalue="1137"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453689540610" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1672.43,574.5) scale(1.95455,-2.15) translate(-804.837,-830.209)" width="25" x="1648" y="553"/></g>
  <g id="119">
   <use class="kv10" height="26" transform="rotate(0,1383.03,886.333) scale(1,1) translate(0,0)" width="12" x="1377.029569585283" xlink:href="#Accessory:避雷器1_0" y="873.3333384195963" zvalue="1148"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453689868290" ObjectName="10kV小等喊线避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1383.03,886.333) scale(1,1) translate(0,0)" width="12" x="1377.029569585283" y="873.3333384195963"/></g>
  <g id="110">
   <use class="kv10" height="22" transform="rotate(0,1358.03,810.333) scale(1,1) translate(0,0)" width="12" x="1352.029569585283" xlink:href="#Accessory:传输线_0" y="799.3333384195963" zvalue="1156"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453689671682" ObjectName="10kV小等喊线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1358.03,810.333) scale(1,1) translate(0,0)" width="12" x="1352.029569585283" y="799.3333384195963"/></g>
  <g id="138">
   <use class="kv10" height="26" transform="rotate(0,1487.03,887.333) scale(1,1) translate(0,0)" width="12" x="1481.029569585283" xlink:href="#Accessory:避雷器1_0" y="874.3333384195963" zvalue="1168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453690392578" ObjectName="10kV云井线避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1487.03,887.333) scale(1,1) translate(0,0)" width="12" x="1481.029569585283" y="874.3333384195963"/></g>
  <g id="132">
   <use class="kv10" height="22" transform="rotate(0,1462.03,811.333) scale(1,1) translate(0,0)" width="12" x="1456.029569585283" xlink:href="#Accessory:传输线_0" y="800.3333384195963" zvalue="1176"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453690195970" ObjectName="10kV云井线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1462.03,811.333) scale(1,1) translate(0,0)" width="12" x="1456.029569585283" y="800.3333384195963"/></g>
  <g id="160">
   <use class="kv10" height="26" transform="rotate(0,1594.03,887.333) scale(1,1) translate(0,0)" width="12" x="1588.029569585283" xlink:href="#Accessory:避雷器1_0" y="874.3333384195963" zvalue="1188"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453690916866" ObjectName="10kV户育线避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1594.03,887.333) scale(1,1) translate(0,0)" width="12" x="1588.029569585283" y="874.3333384195963"/></g>
  <g id="154">
   <use class="kv10" height="22" transform="rotate(0,1569.03,811.333) scale(1,1) translate(0,0)" width="12" x="1563.029569585283" xlink:href="#Accessory:传输线_0" y="800.3333384195963" zvalue="1196"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453690720258" ObjectName="10kV户育线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1569.03,811.333) scale(1,1) translate(0,0)" width="12" x="1563.029569585283" y="800.3333384195963"/></g>
  <g id="179">
   <use class="kv10" height="26" transform="rotate(0,1226.03,886.333) scale(1,1) translate(0,0)" width="12" x="1220.029569585283" xlink:href="#Accessory:避雷器1_0" y="873.3333384195963" zvalue="1208"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453691441154" ObjectName="10kV相帮贺线避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1226.03,886.333) scale(1,1) translate(0,0)" width="12" x="1220.029569585283" y="873.3333384195963"/></g>
  <g id="173">
   <use class="kv10" height="22" transform="rotate(0,1201.03,810.333) scale(1,1) translate(0,0)" width="12" x="1195.029569585283" xlink:href="#Accessory:传输线_0" y="799.3333384195963" zvalue="1216"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453691244546" ObjectName="10kV相帮贺线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1201.03,810.333) scale(1,1) translate(0,0)" width="12" x="1195.029569585283" y="799.3333384195963"/></g>
  <g id="183">
   <use class="kv10" height="30" transform="rotate(0,1228,788) scale(1,1) translate(0,0)" width="30" x="1213" xlink:href="#Accessory:PT象达_0" y="773" zvalue="1221"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453691768834" ObjectName="10kV相帮贺线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1228,788) scale(1,1) translate(0,0)" width="30" x="1213" y="773"/></g>
  <g id="190">
   <use class="kv10" height="26" transform="rotate(0,1282,890) scale(1,1) translate(0,0)" width="12" x="1276" xlink:href="#Accessory:避雷器1_0" y="877" zvalue="1230"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453691965442" ObjectName="#2站用变侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1282,890) scale(1,1) translate(0,0)" width="12" x="1276" y="877"/></g>
  <g id="207">
   <use class="kv10" height="26" transform="rotate(0,1703.03,887.333) scale(1,1) translate(0,0)" width="12" x="1697.029569585283" xlink:href="#Accessory:避雷器1_0" y="874.3333401150173" zvalue="1239"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453692162050" ObjectName="10kV1号电容器避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1703.03,887.333) scale(1,1) translate(0,0)" width="12" x="1697.029569585283" y="874.3333401150173"/></g>
  <g id="201">
   <use class="kv10" height="22" transform="rotate(0,1678.03,783.333) scale(1,1) translate(0,0)" width="12" x="1672.029569585283" xlink:href="#Accessory:传输线_0" y="772.3333401150173" zvalue="1247"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453692030978" ObjectName="10kV1号电容器电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1678.03,783.333) scale(1,1) translate(0,0)" width="12" x="1672.029569585283" y="772.3333401150173"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="319">
   <use class="kv10" height="30" transform="rotate(0,831.03,912.222) scale(1,-1) translate(0,-1824.44)" width="12" x="825.0295695852833" xlink:href="#EnergyConsumer:负荷_0" y="897.2222256130642" zvalue="642"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453687508994" ObjectName="10kV俄罗线"/>
   <cge:TPSR_Ref TObjectID="6192453687508994"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,831.03,912.222) scale(1,-1) translate(0,-1824.44)" width="12" x="825.0295695852833" y="897.2222256130642"/></g>
  <g id="3">
   <use class="kv35" height="30" transform="rotate(0,1105.56,420.25) scale(2.38333,2.38333) translate(-627.853,-223.171)" width="20" x="1081.722222222222" xlink:href="#EnergyConsumer:站用变无融断_0" y="384.5" zvalue="1102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453689016322" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1105.56,420.25) scale(2.38333,2.38333) translate(-627.853,-223.171)" width="20" x="1081.722222222222" y="384.5"/></g>
  <g id="115">
   <use class="kv10" height="30" transform="rotate(0,1358,911.333) scale(1,-1) translate(0,-1822.67)" width="12" x="1352" xlink:href="#EnergyConsumer:负荷_0" y="896.3333384195963" zvalue="1150"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453689737218" ObjectName="10kV小等喊线"/>
   <cge:TPSR_Ref TObjectID="6192453689737218"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1358,911.333) scale(1,-1) translate(0,-1822.67)" width="12" x="1352" y="896.3333384195963"/></g>
  <g id="136">
   <use class="kv10" height="30" transform="rotate(0,1462,912.333) scale(1,-1) translate(0,-1824.67)" width="12" x="1456" xlink:href="#EnergyConsumer:负荷_0" y="897.3333384195963" zvalue="1170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453690261506" ObjectName="10kV云井线"/>
   <cge:TPSR_Ref TObjectID="6192453690261506"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1462,912.333) scale(1,-1) translate(0,-1824.67)" width="12" x="1456" y="897.3333384195963"/></g>
  <g id="158">
   <use class="kv10" height="30" transform="rotate(0,1569,912.333) scale(1,-1) translate(0,-1824.67)" width="12" x="1563" xlink:href="#EnergyConsumer:负荷_0" y="897.3333384195963" zvalue="1190"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453690785794" ObjectName="10kV户育线"/>
   <cge:TPSR_Ref TObjectID="6192453690785794"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1569,912.333) scale(1,-1) translate(0,-1824.67)" width="12" x="1563" y="897.3333384195963"/></g>
  <g id="177">
   <use class="kv10" height="30" transform="rotate(0,1201,911.333) scale(1,-1) translate(0,-1822.67)" width="12" x="1195" xlink:href="#EnergyConsumer:负荷_0" y="896.3333384195963" zvalue="1210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453691310082" ObjectName="10kV相帮贺线"/>
   <cge:TPSR_Ref TObjectID="6192453691310082"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1201,911.333) scale(1,-1) translate(0,-1822.67)" width="12" x="1195" y="896.3333384195963"/></g>
  <g id="188">
   <use class="kv10" height="30" transform="rotate(0,1255.56,913.25) scale(2.38333,2.38333) translate(-714.916,-509.318)" width="20" x="1231.722222222222" xlink:href="#EnergyConsumer:站用变无融断_0" y="877.5" zvalue="1224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453691899906" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1255.56,913.25) scale(2.38333,2.38333) translate(-714.916,-509.318)" width="20" x="1231.722222222222" y="877.5"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="680">
   <g id="6800">
    <use class="kv35" height="60" transform="rotate(0,879.667,508.5) scale(1.38333,1.38333) translate(-236.096,-129.41)" width="40" x="852" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="467" zvalue="1092"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874562207746" ObjectName="35"/>
    </metadata>
   </g>
   <g id="6801">
    <use class="kv10" height="60" transform="rotate(0,879.667,508.5) scale(1.38333,1.38333) translate(-236.096,-129.41)" width="40" x="852" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="467" zvalue="1092"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874562273282" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399522320386" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399522320386"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,879.667,508.5) scale(1.38333,1.38333) translate(-236.096,-129.41)" width="40" x="852" y="467"/></g>
  <g id="44">
   <g id="440">
    <use class="kv35" height="60" transform="rotate(0,1338.33,507.833) scale(1.38333,1.38333) translate(-363.197,-129.225)" width="40" x="1310.67" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="466.33" zvalue="1118"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874562338818" ObjectName="35"/>
    </metadata>
   </g>
   <g id="441">
    <use class="kv10" height="60" transform="rotate(0,1338.33,507.833) scale(1.38333,1.38333) translate(-363.197,-129.225)" width="40" x="1310.67" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="466.33" zvalue="1118"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874562404354" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399522385922" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399522385922"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1338.33,507.833) scale(1.38333,1.38333) translate(-363.197,-129.225)" width="40" x="1310.67" y="466.33"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="214">
   <use class="kv10" height="30" transform="rotate(90,1674.73,913) scale(1.16667,1.33333) translate(-234.247,-223.25)" width="60" x="1639.730063290596" xlink:href="#Compensator:并联电容器12121_0" y="893" zvalue="1255"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453692620802" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453692620802"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1674.73,913) scale(1.16667,1.33333) translate(-234.247,-223.25)" width="60" x="1639.730063290596" y="893"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="70">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="70" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,919.932,15.5) scale(1,1) translate(0,0)" writing-mode="lr" x="920.13" xml:space="preserve" y="20.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132658524164" ObjectName="P"/>
   </metadata>
  </g>
  <g id="71">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="71" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1303.21,15.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1303.41" xml:space="preserve" y="20.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132662718468" ObjectName="P"/>
   </metadata>
  </g>
  <g id="72">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="72" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,919.932,30.9815) scale(1,1) translate(0,0)" writing-mode="lr" x="920.13" xml:space="preserve" y="35.89" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132658589700" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="73" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1303.21,30.9815) scale(1,1) translate(0,0)" writing-mode="lr" x="1303.41" xml:space="preserve" y="35.89" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132662784004" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="74">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="74" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,919.932,46.463) scale(1,1) translate(0,0)" writing-mode="lr" x="920.13" xml:space="preserve" y="51.37" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132658655236" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="77">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="77" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1303.21,46.4629) scale(1,1) translate(0,0)" writing-mode="lr" x="1303.41" xml:space="preserve" y="51.37" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132662849540" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="107" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,919.932,61.9445) scale(1,1) translate(0,0)" writing-mode="lr" x="920.13" xml:space="preserve" y="66.84999999999999" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132659310595" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="143" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1303.21,61.9445) scale(1,1) translate(0,0)" writing-mode="lr" x="1303.41" xml:space="preserve" y="66.84999999999999" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132663504900" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="249">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="249" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,831.03,980.722) scale(1,1) translate(0,0)" writing-mode="lr" x="831.22" xml:space="preserve" y="985.63" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132660555780" ObjectName="P"/>
   </metadata>
  </g>
  <g id="250">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="250" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1358,979.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1358.2" xml:space="preserve" y="984.74" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132669468675" ObjectName="P"/>
   </metadata>
  </g>
  <g id="251">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="251" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1462,980.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1462.2" xml:space="preserve" y="985.74" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132670779395" ObjectName="P"/>
   </metadata>
  </g>
  <g id="252">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="252" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1569,980.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1569.2" xml:space="preserve" y="985.74" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132672090116" ObjectName="P"/>
   </metadata>
  </g>
  <g id="253">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="253" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1201,979.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1201.2" xml:space="preserve" y="984.74" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132673400835" ObjectName="P"/>
   </metadata>
  </g>
  <g id="254">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="254" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,831.03,1003.72) scale(1,1) translate(0,0)" writing-mode="lr" x="831.22" xml:space="preserve" y="1008.63" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132660621316" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="255">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="255" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1358,1002.83) scale(1,1) translate(0,0)" writing-mode="lr" x="1358.2" xml:space="preserve" y="1007.74" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132669534211" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="256" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1462,1003.83) scale(1,1) translate(0,0)" writing-mode="lr" x="1462.2" xml:space="preserve" y="1008.74" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132670844931" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1569,1003.83) scale(1,1) translate(0,0)" writing-mode="lr" x="1569.2" xml:space="preserve" y="1008.74" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132672155652" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="258">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="258" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1201,1002.83) scale(1,1) translate(0,0)" writing-mode="lr" x="1201.2" xml:space="preserve" y="1007.74" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132673466371" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="259">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="259" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,831.03,1026.72) scale(1,1) translate(0,0)" writing-mode="lr" x="831.22" xml:space="preserve" y="1031.63" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132660686852" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="260">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="260" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1358,1025.83) scale(1,1) translate(0,0)" writing-mode="lr" x="1358.2" xml:space="preserve" y="1030.74" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132669599747" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="261">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="261" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1462,1026.83) scale(1,1) translate(0,0)" writing-mode="lr" x="1462.2" xml:space="preserve" y="1031.74" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132670910467" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="262">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="262" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1569,1026.83) scale(1,1) translate(0,0)" writing-mode="lr" x="1569.2" xml:space="preserve" y="1031.74" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132672221188" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="263">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="263" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1201,1025.83) scale(1,1) translate(0,0)" writing-mode="lr" x="1201.2" xml:space="preserve" y="1030.74" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132673531907" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="264">
   <text Format="f5.2" Plane="0" SignFlag="right" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="264" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1670.73,1003.83) scale(1,1) translate(0,0)" writing-mode="lr" x="1670.93" xml:space="preserve" y="1008.74" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132676022276" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="265">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="265" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1670.73,1026.83) scale(1,1) translate(0,0)" writing-mode="lr" x="1670.93" xml:space="preserve" y="1031.74" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132676087812" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="282">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="282" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1017,720.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1017.2" xml:space="preserve" y="725.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132661866500" ObjectName="P"/>
   </metadata>
  </g>
  <g id="283">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="283" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1017,736) scale(1,1) translate(0,0)" writing-mode="lr" x="1017.2" xml:space="preserve" y="740.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132661932036" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="284">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="284" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1017,751.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1017.2" xml:space="preserve" y="756.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132661997572" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="273">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="273" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,135.496,516.25) scale(1,1) translate(1.22274e-14,-1.13076e-13)" writing-mode="lr" x="135.61" xml:space="preserve" y="521.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132656427012" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="272">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="272" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,135.496,539.25) scale(1,1) translate(1.22274e-14,5.90916e-14)" writing-mode="lr" x="135.61" xml:space="preserve" y="544.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132656492548" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="271">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="271" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,135.496,562.25) scale(1,1) translate(1.22274e-14,-1.2329e-13)" writing-mode="lr" x="135.61" xml:space="preserve" y="567.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132656558084" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="270">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="270" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,135.496,490.75) scale(1,1) translate(1.22274e-14,-1.07414e-13)" writing-mode="lr" x="135.61" xml:space="preserve" y="495.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132656689156" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="269">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="269" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,139.857,198.111) scale(1,1) translate(0,0)" writing-mode="lr" x="140.01" xml:space="preserve" y="204.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132656820228" ObjectName="F"/>
   </metadata>
  </g>
  <g id="268">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="268" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,254.899,516.25) scale(1,1) translate(2.54838e-14,-1.13076e-13)" writing-mode="lr" x="255.01" xml:space="preserve" y="521.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132656951300" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="267">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="267" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,254.899,539.25) scale(1,1) translate(2.54838e-14,0)" writing-mode="lr" x="255.01" xml:space="preserve" y="544.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132657016836" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="266">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="266" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,254.899,562.25) scale(1,1) translate(2.54838e-14,-1.2329e-13)" writing-mode="lr" x="255.01" xml:space="preserve" y="567.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132657082372" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="248">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="248" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,254.899,490.75) scale(1,1) translate(2.54838e-14,-1.07414e-13)" writing-mode="lr" x="255.01" xml:space="preserve" y="495.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132657213443" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="247">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="247" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,139.857,223) scale(1,1) translate(0,0)" writing-mode="lr" x="140.01" xml:space="preserve" y="229.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132657344516" ObjectName="F"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="246" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,312.857,516.25) scale(1,1) translate(3.19185e-14,-1.13076e-13)" writing-mode="lr" x="312.97" xml:space="preserve" y="521.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132657475588" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,312.857,539.25) scale(1,1) translate(3.19185e-14,5.90916e-14)" writing-mode="lr" x="312.97" xml:space="preserve" y="544.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132657541124" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,312.857,562.25) scale(1,1) translate(3.19185e-14,-1.2329e-13)" writing-mode="lr" x="312.97" xml:space="preserve" y="567.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132657606660" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="243">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="243" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,316.857,222.125) scale(1,1) translate(0,4.77396e-14)" writing-mode="lr" x="317.01" xml:space="preserve" y="228.4" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132657868804" ObjectName="F"/>
   </metadata>
  </g>
  <g id="242">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="242" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,312.857,490.75) scale(1,1) translate(3.19185e-14,-1.07414e-13)" writing-mode="lr" x="312.97" xml:space="preserve" y="495.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132657737732" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="241" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,139.857,245.194) scale(1,1) translate(0,0)" writing-mode="lr" x="140.01" xml:space="preserve" y="251.47" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132667174916" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="240" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,139.857,268.194) scale(1,1) translate(0,3.76979e-13)" writing-mode="lr" x="140.01" xml:space="preserve" y="274.47" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132667240452" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="239">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="239" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,316.857,244.194) scale(1,1) translate(0,0)" writing-mode="lr" x="317.01" xml:space="preserve" y="250.47" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132664684547" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="238">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="238" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,316.857,267.194) scale(1,1) translate(0,2.02212e-13)" writing-mode="lr" x="317.01" xml:space="preserve" y="273.47" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132664750083" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="237">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="237" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,135.496,589.25) scale(1,1) translate(1.22274e-14,1.29285e-13)" writing-mode="lr" x="135.61" xml:space="preserve" y="594.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132656885764" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="236">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,254.899,589.25) scale(1,1) translate(2.54838e-14,1.29285e-13)" writing-mode="lr" x="255.01" xml:space="preserve" y="594.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132657410052" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="235">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="235" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,312.857,589.25) scale(1,1) translate(3.19185e-14,1.29285e-13)" writing-mode="lr" x="312.97" xml:space="preserve" y="594.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132657934340" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="234" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,139.857,173.5) scale(1,1) translate(0,0)" writing-mode="lr" x="140.01" xml:space="preserve" y="179.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132681986052" ObjectName=""/>
   </metadata>
  </g>
  <g id="233">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="233" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,316.857,173.5) scale(1,1) translate(0,0)" writing-mode="lr" x="317.01" xml:space="preserve" y="179.85" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132682051588" ObjectName=""/>
   </metadata>
  </g>
  <g id="339">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="339" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,570.99,503.175) scale(1,1) translate(0,0)" writing-mode="lr" x="571.1900000000001" xml:space="preserve" y="508.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132657737732" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="340">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="340" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1674.71,497.714) scale(1,1) translate(0,-1.08183e-13)" writing-mode="lr" x="1674.91" xml:space="preserve" y="502.62" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132657213443" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="341">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="341" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1100.71,97.9524) scale(1,1) translate(0,0)" writing-mode="lr" x="1100.91" xml:space="preserve" y="102.86" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132656689156" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="342">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="342" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,731.167,378.5) scale(1,1) translate(-1.5236e-13,0)" writing-mode="lr" x="731.36" xml:space="preserve" y="383.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132664225796" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="343">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="343" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,731.167,405.5) scale(1,1) translate(-1.5236e-13,0)" writing-mode="lr" x="731.36" xml:space="preserve" y="410.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132664291332" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="344">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="344" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,731.167,432.5) scale(1,1) translate(-1.5236e-13,0)" writing-mode="lr" x="731.36" xml:space="preserve" y="437.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132664487940" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="345">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="345" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,798.31,589.786) scale(1,1) translate(0,0)" writing-mode="lr" x="798.5" xml:space="preserve" y="594.7" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132664356867" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="346">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="346" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,797.31,615.786) scale(1,1) translate(0,0)" writing-mode="lr" x="797.5" xml:space="preserve" y="620.7" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132664422403" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="347">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="347" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,797.31,642.786) scale(1,1) translate(0,0)" writing-mode="lr" x="797.5" xml:space="preserve" y="647.7" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132664815619" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="348">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="348" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1440.26,375.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1440.46" xml:space="preserve" y="380.74" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132666716164" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="349">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="349" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1440.26,402.833) scale(1,1) translate(0,-2.61346e-13)" writing-mode="lr" x="1440.46" xml:space="preserve" y="407.74" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132666781699" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="350">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="350" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1440.26,429.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1440.46" xml:space="preserve" y="434.74" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132666978308" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="351">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="351" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1458.83,575.262) scale(1,1) translate(9.41802e-13,0)" writing-mode="lr" x="1459.03" xml:space="preserve" y="580.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132666847235" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="352">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="352" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1458.83,602.262) scale(1,1) translate(9.41802e-13,0)" writing-mode="lr" x="1459.03" xml:space="preserve" y="607.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132666912771" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="353">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="353" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1458.83,629.262) scale(1,1) translate(9.41802e-13,0)" writing-mode="lr" x="1459.03" xml:space="preserve" y="634.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132667305987" ObjectName="LIa"/>
   </metadata>
  </g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,346.482,315.5) scale(0.708333,0.665547) translate(138.294,153.53)" width="30" x="335.86" xlink:href="#State:红绿圆(方形)_0" y="305.52" zvalue="1403"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374921170945" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,346.482,315.5) scale(0.708333,0.665547) translate(138.294,153.53)" width="30" x="335.86" y="305.52"/></g>
  <g id="1035">
   <use height="30" transform="rotate(0,250.857,315.5) scale(0.708333,0.665547) translate(98.9191,153.53)" width="30" x="240.23" xlink:href="#State:红绿圆(方形)_0" y="305.52" zvalue="1404"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,250.857,315.5) scale(0.708333,0.665547) translate(98.9191,153.53)" width="30" x="240.23" y="305.52"/></g>
  <g id="772">
   <use height="30" transform="rotate(0,308.235,405) scale(0.910937,0.8) translate(26.5737,98.25)" width="80" x="271.8" xlink:href="#State:间隔模板_0" y="393" zvalue="1492"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629500520267778" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,308.235,405) scale(0.910937,0.8) translate(26.5737,98.25)" width="80" x="271.8" y="393"/></g>
  <g id="1082">
   <use height="30" transform="rotate(0,106.286,324.539) scale(0.910937,0.8) translate(6.82907,78.1347)" width="80" x="69.84999999999999" xlink:href="#State:间隔模板_0" y="312.54" zvalue="1493"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629500369731586" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,106.286,324.539) scale(0.910937,0.8) translate(6.82907,78.1347)" width="80" x="69.84999999999999" y="312.54"/></g>
 </g>
</svg>