<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549583609858" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="Accessory:三卷PT带容断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,6.5) scale(1,1) translate(0,0)" width="4" x="13" y="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1754.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2334.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2027.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="Accessory:带电容电阻接地_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.75"/>
   <rect fill-opacity="0" height="11.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.94,6.29) scale(1,1) translate(0,0)" width="6.08" x="2.9" y="0.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666663" x2="10.5" y1="20.49453511141348" y2="20.49453511141348"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.603429610654176" x2="7.693139016796801" y1="25.38116790988687" y2="25.38116790988687"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.965686274509802" x2="9.330882352941176" y1="22.90451817731685" y2="22.90451817731685"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.916666666666667" x2="5.916666666666667" y1="14.58333333333333" y2="11.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.083333333333333" x2="6.083333333333333" y1="20.58333333333334" y2="16.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.833333333333333" x2="9" y1="16.16120177808014" y2="16.16120177808014"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.833333333333333" x2="9.25" y1="14.66120177808014" y2="14.66120177808014"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变1节点_0" viewBox="0,0,26,38">
   <use terminal-index="0" type="0" x="9.116666666666667" xlink:href="#terminal" y="0.2500000000000071"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.45" x2="25.45" y1="30" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.45" x2="24.45" y1="31" y2="31"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="33" y2="38"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.45" x2="23.45" y1="32" y2="32"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="23" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="19" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="23" y1="24" y2="24"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="2.25" y2="0.25"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="9" y1="30" y2="37"/>
   <ellipse cx="9.210000000000001" cy="10.97" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="6.6" y2="10.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="10.73394833233988" y2="13.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="10.68990500916851" y2="13.5"/>
   <ellipse cx="9.210000000000001" cy="24.47" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="20.1" y2="24.20894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="24.23394833233988" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="24.18990500916851" y2="27"/>
  </symbol>
  <symbol id="Accessory:避雷器11_0" viewBox="0,0,12,27">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="1.133333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.91666666666667" x2="1.083333333333333" y1="16.16666666666666" y2="4.083333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="1.183333333333332" y2="3.099999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.96666666666667" x2="10.96666666666667" y1="18.91666666666667" y2="16.18333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="1.133333333333334" x2="1.133333333333334" y1="4.350000000000003" y2="8.333333333333337"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="19.1" y2="22.7"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.75350877192984" y2="22.75350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.08,11.27) scale(1,1) translate(0,0)" width="8.67" x="1.75" y="3.27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.51666666666667" y2="25.51666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="24.13508771929826" y2="24.13508771929826"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV勐劈河一级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="58" xlink:href="logo.png" y="37"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,186,67) scale(1,1) translate(0,0)" writing-mode="lr" x="186" xml:space="preserve" y="70.5" zvalue="34"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,186.75,66.4403) scale(1,1) translate(0,0)" writing-mode="lr" x="186.75" xml:space="preserve" y="75.44" zvalue="35">35kV勐劈河一级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="62" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="166"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="166">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,547.845,558.242) scale(1,1) translate(0,0)" writing-mode="lr" x="547.85" xml:space="preserve" y="562.74" zvalue="3">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,665,842) scale(1,1) translate(0,0)" writing-mode="lr" x="665" xml:space="preserve" y="846.5" zvalue="5">#1发电机4MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,695.625,673.75) scale(1,1) translate(0,0)" writing-mode="lr" x="695.63" xml:space="preserve" y="678.25" zvalue="8">641</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,672.667,617.222) scale(1,1) translate(0,0)" writing-mode="lr" x="672.67" xml:space="preserve" y="621.72" zvalue="11">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="267" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,551.42,903.598) scale(1,1) translate(0,0)" writing-mode="lr" x="551.42" xml:space="preserve" y="908.1" zvalue="17">机组PT</text>
  <line fill="none" id="133" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="392" x2="392" y1="49.42857142857144" y2="1020.857142857143" zvalue="36"/>
  <line fill="none" id="130" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.00000000000045" x2="385" y1="140.8704926140824" y2="140.8704926140824" zvalue="38"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,747.556,538.778) scale(1,1) translate(0,0)" writing-mode="lr" x="747.5599999999999" xml:space="preserve" y="543.28" zvalue="41">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,705.284,477.083) scale(1,1) translate(-7.70015e-13,0)" writing-mode="lr" x="705.28" xml:space="preserve" y="481.58" zvalue="43">601</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,660.371,386.787) scale(1,1) translate(0,0)" writing-mode="lr" x="660.37" xml:space="preserve" y="391.29" zvalue="47">#1主变12.5MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,702.222,311.389) scale(1,1) translate(-7.66547e-13,0)" writing-mode="lr" x="702.22" xml:space="preserve" y="315.89" zvalue="50">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,666.556,291.556) scale(1,1) translate(0,0)" writing-mode="lr" x="666.5599999999999" xml:space="preserve" y="296.06" zvalue="57">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,753.76,236.556) scale(1,1) translate(0,0)" writing-mode="lr" x="753.76" xml:space="preserve" y="241.06" zvalue="59">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,659.484,135.381) scale(1,1) translate(0,0)" writing-mode="lr" x="659.48" xml:space="preserve" y="139.88" zvalue="65">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,709.56,198.103) scale(1,1) translate(0,0)" writing-mode="lr" x="709.5599999999999" xml:space="preserve" y="202.6" zvalue="70">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,630.167,224.403) scale(1,1) translate(0,0)" writing-mode="lr" x="630.17" xml:space="preserve" y="228.9" zvalue="74">97</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,733.488,84.873) scale(1,1) translate(0,0)" writing-mode="lr" x="733.49" xml:space="preserve" y="89.37" zvalue="76">35kV勐小T线勐劈河一级支线</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="78" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="145"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="926" y2="926"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="965.1632999999999" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="926" y2="926"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="965.1632999999999" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="993.0816" y2="1021"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,946) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="952" zvalue="147">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.8889,978.889) scale(1,1) translate(0,0)" writing-mode="lr" x="52.89" xml:space="preserve" y="984.89" zvalue="148">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.889,978.889) scale(1,1) translate(0,0)" writing-mode="lr" x="234.89" xml:space="preserve" y="984.89" zvalue="149">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.8889,1006.89) scale(1,1) translate(0,0)" writing-mode="lr" x="51.89" xml:space="preserve" y="1012.89" zvalue="150">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.889,1006.89) scale(1,1) translate(0,0)" writing-mode="lr" x="233.89" xml:space="preserve" y="1012.89" zvalue="151">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,560.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="565" zvalue="153">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="154">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="155">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="156">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="157">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="158">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="167">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="168">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="384.75" zvalue="171">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="173">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="175">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="176">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="177">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="179">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="230" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,681.333,546.222) scale(1,1) translate(0,0)" writing-mode="lr" x="681.33" xml:space="preserve" y="550.72" zvalue="202">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="206" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1019.56,538.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1019.56" xml:space="preserve" y="543.28" zvalue="218">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,971.284,476.083) scale(1,1) translate(0,0)" writing-mode="lr" x="971.28" xml:space="preserve" y="480.58" zvalue="220">644</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,928.371,386.787) scale(1,1) translate(0,0)" writing-mode="lr" x="928.37" xml:space="preserve" y="391.29" zvalue="224">首部变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,973.222,312.389) scale(1,1) translate(0,0)" writing-mode="lr" x="973.22" xml:space="preserve" y="316.89" zvalue="227">041</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1022.76,252.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1022.76" xml:space="preserve" y="257.06" zvalue="231">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="233" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,954.667,549.556) scale(1,1) translate(0,0)" writing-mode="lr" x="954.67" xml:space="preserve" y="554.0599999999999" zvalue="235">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1003.2,126.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1003.2" xml:space="preserve" y="131.06" zvalue="242">10kV首部线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="242" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1354.89,340.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1354.89" xml:space="preserve" y="345.28" zvalue="249">6.3kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="244" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1355.56,526.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1355.56" xml:space="preserve" y="531.28" zvalue="252">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="243" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1290.67,517.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1290.67" xml:space="preserve" y="522.0599999999999" zvalue="254">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,746.889,649.556) scale(1,1) translate(0,0)" writing-mode="lr" x="746.89" xml:space="preserve" y="654.0599999999999" zvalue="260">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="268" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,519,769.444) scale(1,1) translate(0,0)" writing-mode="lr" x="519" xml:space="preserve" y="773.9400000000001" zvalue="268">6911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="269" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,601.333,790.667) scale(1,1) translate(0,0)" writing-mode="lr" x="601.33" xml:space="preserve" y="795.17" zvalue="269">69117</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="272" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,814.68,912.932) scale(1,1) translate(0,0)" writing-mode="lr" x="814.6799999999999" xml:space="preserve" y="917.4299999999999" zvalue="276">励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="271" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,782.333,782.778) scale(1,1) translate(0,1.28943e-12)" writing-mode="lr" x="782.33" xml:space="preserve" y="787.28" zvalue="278">6912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="270" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,864.667,804) scale(1,1) translate(0,0)" writing-mode="lr" x="864.67" xml:space="preserve" y="808.5" zvalue="280">69127</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="290" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1077,842) scale(1,1) translate(0,0)" writing-mode="lr" x="1077" xml:space="preserve" y="846.5" zvalue="287">#2发电机4MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1107.62,673.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1107.63" xml:space="preserve" y="678.25" zvalue="290">642</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1084.67,617.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1084.67" xml:space="preserve" y="621.72" zvalue="293">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,963.42,903.598) scale(1,1) translate(0,0)" writing-mode="lr" x="963.42" xml:space="preserve" y="908.1" zvalue="297">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="286" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1158.89,649.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1158.89" xml:space="preserve" y="654.0599999999999" zvalue="299">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,931,769.444) scale(1,1) translate(1.01696e-13,0)" writing-mode="lr" x="931" xml:space="preserve" y="773.9400000000001" zvalue="304">6921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1013.33,790.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1013.33" xml:space="preserve" y="795.17" zvalue="307">69217</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1226.68,912.932) scale(1,1) translate(0,0)" writing-mode="lr" x="1226.68" xml:space="preserve" y="917.4299999999999" zvalue="313">励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1194.33,782.778) scale(1,1) translate(0,1.28943e-12)" writing-mode="lr" x="1194.33" xml:space="preserve" y="787.28" zvalue="315">6922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="281" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1276.67,804) scale(1,1) translate(0,0)" writing-mode="lr" x="1276.67" xml:space="preserve" y="808.5" zvalue="317">69227</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="320" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1410.96,682.194) scale(1,1) translate(0,0)" writing-mode="lr" x="1410.96" xml:space="preserve" y="686.6900000000001" zvalue="324">643</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="319" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1388,617.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1388" xml:space="preserve" y="621.72" zvalue="327">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="318" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1462.22,649.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1462.22" xml:space="preserve" y="654.0599999999999" zvalue="330">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="332" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1391.53,821.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1391.53" xml:space="preserve" y="825.9400000000001" zvalue="336">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.125,946) scale(1,1) translate(0,0)" writing-mode="lr" x="202.13" xml:space="preserve" y="952" zvalue="341">MengPiHe1-01-2012</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="166"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="173">
   <path class="v6300" d="M 507.78 583.19 L 1521.35 583.19" stroke-width="4" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674238464005" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674238464005"/></metadata>
  <path d="M 507.78 583.19 L 1521.35 583.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="172">
   <use class="v6300" height="30" transform="rotate(0,663,803.25) scale(1.5,1.45) translate(-213.5,-242.534)" width="30" x="640.5" xlink:href="#Generator:发电机_0" y="781.5" zvalue="4"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449689485318" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449689485318"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,663,803.25) scale(1.5,1.45) translate(-213.5,-242.534)" width="30" x="640.5" y="781.5"/></g>
  <g id="317">
   <use class="v6300" height="30" transform="rotate(0,1075,803.25) scale(1.5,1.45) translate(-350.833,-242.534)" width="30" x="1052.5" xlink:href="#Generator:发电机_0" y="781.5" zvalue="286"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449692106757" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449692106757"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1075,803.25) scale(1.5,1.45) translate(-350.833,-242.534)" width="30" x="1052.5" y="781.5"/></g>
 </g>
 <g id="BreakerClass">
  <g id="171">
   <use class="v6300" height="20" transform="rotate(0,661,676) scale(1.5,1.35) translate(-217.833,-171.759)" width="10" x="653.5" xlink:href="#Breaker:开关_0" y="662.5" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924491542533" ObjectName="#1发电机641断路器"/>
   <cge:TPSR_Ref TObjectID="6473924491542533"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,661,676) scale(1.5,1.35) translate(-217.833,-171.759)" width="10" x="653.5" y="662.5"/></g>
  <g id="126">
   <use class="v6300" height="20" transform="rotate(0,734.485,477.944) scale(1.5,1.35) translate(-242.328,-120.412)" width="10" x="726.9845519911909" xlink:href="#Breaker:开关_0" y="464.4444444444445" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924491476997" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924491476997"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,734.485,477.944) scale(1.5,1.35) translate(-242.328,-120.412)" width="10" x="726.9845519911909" y="464.4444444444445"/></g>
  <g id="122">
   <use class="kv35" height="20" transform="rotate(0,734.389,312.389) scale(1.5,1.35) translate(-242.296,-77.4897)" width="10" x="726.8888888888889" xlink:href="#Breaker:开关_0" y="298.8888888888889" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924491411461" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924491411461"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,734.389,312.389) scale(1.5,1.35) translate(-242.296,-77.4897)" width="10" x="726.8888888888889" y="298.8888888888889"/></g>
  <g id="226">
   <use class="v6300" height="20" transform="rotate(0,1002.48,477.944) scale(1.5,1.35) translate(-331.662,-120.412)" width="10" x="994.9845519911909" xlink:href="#Breaker:开关_0" y="464.4444444444445" zvalue="219"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924491673605" ObjectName="首部变6.3kV侧644断路器"/>
   <cge:TPSR_Ref TObjectID="6473924491673605"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1002.48,477.944) scale(1.5,1.35) translate(-331.662,-120.412)" width="10" x="994.9845519911909" y="464.4444444444445"/></g>
  <g id="219">
   <use class="kv10" height="20" transform="rotate(0,1002.39,312.389) scale(1.5,1.35) translate(-331.63,-77.4897)" width="10" x="994.8888888888889" xlink:href="#Breaker:开关_0" y="298.8888888888889" zvalue="225"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924491608069" ObjectName="首部变10kV侧041断路器"/>
   <cge:TPSR_Ref TObjectID="6473924491608069"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1002.39,312.389) scale(1.5,1.35) translate(-331.63,-77.4897)" width="10" x="994.8888888888889" y="298.8888888888889"/></g>
  <g id="316">
   <use class="v6300" height="20" transform="rotate(0,1073,676) scale(1.5,1.35) translate(-355.167,-171.759)" width="10" x="1065.5" xlink:href="#Breaker:开关_0" y="662.5" zvalue="288"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924491739141" ObjectName="#2发电机642断路器"/>
   <cge:TPSR_Ref TObjectID="6473924491739141"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1073,676) scale(1.5,1.35) translate(-355.167,-171.759)" width="10" x="1065.5" y="662.5"/></g>
  <g id="327">
   <use class="v6300" height="20" transform="rotate(0,1376.33,680) scale(1.5,1.35) translate(-456.278,-172.796)" width="10" x="1368.833333333333" xlink:href="#Breaker:开关_0" y="666.5" zvalue="323"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924491804677" ObjectName="#1站用变643断路器"/>
   <cge:TPSR_Ref TObjectID="6473924491804677"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1376.33,680) scale(1.5,1.35) translate(-456.278,-172.796)" width="10" x="1368.833333333333" y="666.5"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="168">
   <path class="v6300" d="M 661.1 688.89 L 661.1 781.86" stroke-width="1" zvalue="7"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@1" LinkObjectIDznd="172@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 661.1 688.89 L 661.1 781.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="v6300" d="M 660.27 583.19 L 660.27 606.21" stroke-width="1" zvalue="10"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="173@1" LinkObjectIDznd="763@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 660.27 583.19 L 660.27 606.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="v6300" d="M 660.24 630.04 L 660.24 663.09" stroke-width="1" zvalue="12"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="763@0" LinkObjectIDznd="171@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 660.24 630.04 L 660.24 663.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="v6300" d="M 734.9 583.19 L 734.9 551.6" stroke-width="1" zvalue="39"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="173@0" LinkObjectIDznd="128@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.9 583.19 L 734.9 551.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="v6300" d="M 734.93 527.76 L 734.93 490.84" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@1" LinkObjectIDznd="126@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.93 527.76 L 734.93 490.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="v6300" d="M 734.43 465.03 L 734.43 427.67" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="178@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.43 465.03 L 734.43 427.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv35" d="M 734.49 348.06 L 734.49 325.28" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="122@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.49 348.06 L 734.49 325.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="v6300" d="M 734.43 445.11 L 673.17 445.11" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123" LinkObjectIDznd="179@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.43 445.11 L 673.17 445.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv35" d="M 649.14 158.95 L 596.99 158.95" stroke-width="1" zvalue="206"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@1" LinkObjectIDznd="181@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 649.14 158.95 L 596.99 158.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="184">
   <path class="kv35" d="M 630.23 182.9 L 630.23 158.95" stroke-width="1" zvalue="207"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="183" MaxPinNum="2"/>
   </metadata>
  <path d="M 630.23 182.9 L 630.23 158.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv35" d="M 735.2 225.54 L 735.2 131.73" stroke-width="1" zvalue="210"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@1" LinkObjectIDznd="134@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 735.2 225.54 L 735.2 131.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="kv35" d="M 672.97 158.98 L 735.2 158.98" stroke-width="1" zvalue="211"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="189" MaxPinNum="2"/>
   </metadata>
  <path d="M 672.97 158.98 L 735.2 158.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv35" d="M 694.71 188.27 L 694.71 158.98" stroke-width="1" zvalue="212"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="191" MaxPinNum="2"/>
   </metadata>
  <path d="M 694.71 188.27 L 694.71 158.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv35" d="M 734.34 299.47 L 734.34 249.37" stroke-width="1" zvalue="213"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@0" LinkObjectIDznd="115@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.34 299.47 L 734.34 249.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv35" d="M 675.72 274.29 L 734.34 274.29" stroke-width="1" zvalue="214"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="279@0" LinkObjectIDznd="195" MaxPinNum="2"/>
   </metadata>
  <path d="M 675.72 274.29 L 734.34 274.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="v6300" d="M 1002.9 583.19 L 1002.9 551.6" stroke-width="1" zvalue="216"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="173@2" LinkObjectIDznd="227@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1002.9 583.19 L 1002.9 551.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="v6300" d="M 1002.93 527.76 L 1002.93 490.84" stroke-width="1" zvalue="221"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="227@1" LinkObjectIDznd="226@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1002.93 527.76 L 1002.93 490.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="223">
   <path class="v6300" d="M 1002.43 465.03 L 1002.43 427.67" stroke-width="1" zvalue="223"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@0" LinkObjectIDznd="224@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1002.43 465.03 L 1002.43 427.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv10" d="M 1002.49 348.06 L 1002.49 325.28" stroke-width="1" zvalue="226"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="224@0" LinkObjectIDznd="219@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1002.49 348.06 L 1002.49 325.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="v6300" d="M 1002.43 445.11 L 941.17 445.11" stroke-width="1" zvalue="232"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223" LinkObjectIDznd="229@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1002.43 445.11 L 941.17 445.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv10" d="M 1002.34 299.47 L 1002.34 261.37" stroke-width="1" zvalue="236"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@0" LinkObjectIDznd="214@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1002.34 299.47 L 1002.34 261.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="v6300" d="M 695.5 517.84 L 734.93 517.84" stroke-width="1" zvalue="238"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="124" MaxPinNum="2"/>
   </metadata>
  <path d="M 695.5 517.84 L 734.93 517.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="v6300" d="M 965.5 512.29 L 1002.93 512.29" stroke-width="1" zvalue="239"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="225" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.5 512.29 L 1002.93 512.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="235">
   <path class="kv10" d="M 1003.2 237.54 L 1003.2 167.73" stroke-width="1" zvalue="242"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@1" LinkObjectIDznd="234@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1003.2 237.54 L 1003.2 167.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv10" d="M 941.17 209.11 L 1003.2 209.11" stroke-width="1" zvalue="247"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@0" LinkObjectIDznd="235" MaxPinNum="2"/>
   </metadata>
  <path d="M 941.17 209.11 L 1003.2 209.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="248">
   <path class="v6300" d="M 1338.9 583.19 L 1338.9 539.6" stroke-width="1" zvalue="250"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="173@3" LinkObjectIDznd="247@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1338.9 583.19 L 1338.9 539.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="249">
   <path class="v6300" d="M 1338.93 515.76 L 1338.93 421.81" stroke-width="1" zvalue="256"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247@1" LinkObjectIDznd="241@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1338.93 515.76 L 1338.93 421.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="v6300" d="M 1301.5 480.29 L 1338.93 480.29" stroke-width="1" zvalue="257"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@0" LinkObjectIDznd="249" MaxPinNum="2"/>
   </metadata>
  <path d="M 1301.5 480.29 L 1338.93 480.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="252">
   <path class="v6300" d="M 708.28 645.49 L 660.24 645.49" stroke-width="1" zvalue="260"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="251@0" LinkObjectIDznd="165" MaxPinNum="2"/>
   </metadata>
  <path d="M 708.28 645.49 L 660.24 645.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="257">
   <path class="v6300" d="M 732.06 715.11 L 661.1 715.11" stroke-width="1" zvalue="265"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="256@0" LinkObjectIDznd="168" MaxPinNum="2"/>
   </metadata>
  <path d="M 732.06 715.11 L 661.1 715.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="261">
   <path class="v6300" d="M 548.01 781.15 L 548.01 839.33" stroke-width="1" zvalue="270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="258@0" LinkObjectIDznd="159@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 548.01 781.15 L 548.01 839.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="262">
   <path class="v6300" d="M 589.39 814.38 L 548.01 814.38" stroke-width="1" zvalue="271"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@0" LinkObjectIDznd="261" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.39 814.38 L 548.01 814.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="265">
   <path class="v6300" d="M 548.04 757.32 L 548.04 681.61" stroke-width="1" zvalue="272"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="258@1" LinkObjectIDznd="260@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 548.04 757.32 L 548.04 681.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="v6300" d="M 548.04 734.67 L 661.1 734.67" stroke-width="1" zvalue="273"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265" LinkObjectIDznd="168" MaxPinNum="2"/>
   </metadata>
  <path d="M 548.04 734.67 L 661.1 734.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="274">
   <path class="v6300" d="M 811.35 794.48 L 811.35 849.8" stroke-width="1" zvalue="281"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="277@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 811.35 794.48 L 811.35 849.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="273">
   <path class="v6300" d="M 852.72 827.71 L 811.35 827.71" stroke-width="1" zvalue="282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="275@0" LinkObjectIDznd="274" MaxPinNum="2"/>
   </metadata>
  <path d="M 852.72 827.71 L 811.35 827.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="v6300" d="M 811.38 770.65 L 811.38 693.4" stroke-width="1" zvalue="283"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@1" LinkObjectIDznd="158@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 811.38 770.65 L 811.38 693.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="280">
   <path class="v6300" d="M 661.1 734.67 L 811.38 734.67" stroke-width="1" zvalue="284"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266" LinkObjectIDznd="278" MaxPinNum="2"/>
   </metadata>
  <path d="M 661.1 734.67 L 811.38 734.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="315">
   <path class="v6300" d="M 1073.1 688.89 L 1073.1 781.86" stroke-width="1" zvalue="289"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="316@1" LinkObjectIDznd="317@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1073.1 688.89 L 1073.1 781.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="313">
   <path class="v6300" d="M 1072.27 583.19 L 1072.27 606.21" stroke-width="1" zvalue="292"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="173@4" LinkObjectIDznd="314@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1072.27 583.19 L 1072.27 606.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="v6300" d="M 1072.24 630.04 L 1072.24 663.09" stroke-width="1" zvalue="294"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="314@0" LinkObjectIDznd="316@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1072.24 630.04 L 1072.24 663.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="308">
   <path class="v6300" d="M 1120.28 645.49 L 1072.24 645.49" stroke-width="1" zvalue="300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="309@0" LinkObjectIDznd="312" MaxPinNum="2"/>
   </metadata>
  <path d="M 1120.28 645.49 L 1072.24 645.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="306">
   <path class="v6300" d="M 1144.06 715.11 L 1073.1 715.11" stroke-width="1" zvalue="302"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="307@0" LinkObjectIDznd="315" MaxPinNum="2"/>
   </metadata>
  <path d="M 1144.06 715.11 L 1073.1 715.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="302">
   <path class="v6300" d="M 960.01 781.15 L 960.01 839.33" stroke-width="1" zvalue="308"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="305@0" LinkObjectIDznd="311@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.01 781.15 L 960.01 839.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="301">
   <path class="v6300" d="M 1001.39 814.38 L 960.01 814.38" stroke-width="1" zvalue="309"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="304@0" LinkObjectIDznd="302" MaxPinNum="2"/>
   </metadata>
  <path d="M 1001.39 814.38 L 960.01 814.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="300">
   <path class="v6300" d="M 960.04 757.32 L 960.04 681.61" stroke-width="1" zvalue="310"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="305@1" LinkObjectIDznd="303@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.04 757.32 L 960.04 681.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="299">
   <path class="v6300" d="M 960.04 734.67 L 1073.1 734.67" stroke-width="1" zvalue="311"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="300" LinkObjectIDznd="315" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.04 734.67 L 1073.1 734.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="294">
   <path class="v6300" d="M 1223.35 794.48 L 1223.35 849.8" stroke-width="1" zvalue="318"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="297@0" LinkObjectIDznd="298@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1223.35 794.48 L 1223.35 849.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="293">
   <path class="v6300" d="M 1264.72 827.71 L 1223.35 827.71" stroke-width="1" zvalue="319"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="295@0" LinkObjectIDznd="294" MaxPinNum="2"/>
   </metadata>
  <path d="M 1264.72 827.71 L 1223.35 827.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="292">
   <path class="v6300" d="M 1223.38 770.65 L 1223.38 693.4" stroke-width="1" zvalue="320"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="297@1" LinkObjectIDznd="310@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1223.38 770.65 L 1223.38 693.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="291">
   <path class="v6300" d="M 1073.1 734.67 L 1223.38 734.67" stroke-width="1" zvalue="321"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="299" LinkObjectIDznd="292" MaxPinNum="2"/>
   </metadata>
  <path d="M 1073.1 734.67 L 1223.38 734.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="325">
   <path class="v6300" d="M 1375.6 583.19 L 1375.6 606.21" stroke-width="1" zvalue="326"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="173@5" LinkObjectIDznd="326@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1375.6 583.19 L 1375.6 606.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="324">
   <path class="v6300" d="M 1375.57 630.04 L 1375.57 667.09" stroke-width="1" zvalue="328"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="326@0" LinkObjectIDznd="327@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1375.57 630.04 L 1375.57 667.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="322">
   <path class="v6300" d="M 1423.61 645.49 L 1375.57 645.49" stroke-width="1" zvalue="331"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="323@0" LinkObjectIDznd="324" MaxPinNum="2"/>
   </metadata>
  <path d="M 1423.61 645.49 L 1375.57 645.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="333">
   <path class="v6300" d="M 1376.43 692.89 L 1376.43 764.94" stroke-width="1" zvalue="336"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="327@1" LinkObjectIDznd="331@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1376.43 692.89 L 1376.43 764.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="336">
   <path class="v6300" d="M 1432.09 729.72 L 1376.43 729.72" stroke-width="1" zvalue="339"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="328@0" LinkObjectIDznd="333" MaxPinNum="2"/>
   </metadata>
  <path d="M 1432.09 729.72 L 1376.43 729.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="763">
   <use class="v6300" height="30" transform="rotate(0,660.333,618.222) scale(-1.11111,-0.814815) translate(-1253.8,-1379.73)" width="15" x="652" xlink:href="#Disconnector:刀闸_0" y="606" zvalue="9"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449689419782" ObjectName="#1发电机6411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449689419782"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,660.333,618.222) scale(-1.11111,-0.814815) translate(-1253.8,-1379.73)" width="15" x="652" y="606"/></g>
  <g id="128">
   <use class="v6300" height="30" transform="rotate(0,735,539.778) scale(-1.11111,-0.814815) translate(-1395.67,-1205.01)" width="15" x="726.6666666666666" xlink:href="#Disconnector:刀闸_0" y="527.5555555555555" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449689223174" ObjectName="#1主变6.3kV侧6011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449689223174"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,735,539.778) scale(-1.11111,-0.814815) translate(-1395.67,-1205.01)" width="15" x="726.6666666666666" y="527.5555555555555"/></g>
  <g id="115">
   <use class="kv35" height="30" transform="rotate(0,735.27,237.556) scale(-1.11111,-0.814815) translate(-1396.18,-531.879)" width="15" x="726.9364189454009" xlink:href="#Disconnector:刀闸_0" y="225.3333333333333" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449689026566" ObjectName="#1主变35kV侧3016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449689026566"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,735.27,237.556) scale(-1.11111,-0.814815) translate(-1396.18,-531.879)" width="15" x="726.9364189454009" y="225.3333333333333"/></g>
  <g id="117">
   <use class="kv35" height="30" transform="rotate(270,661.151,158.881) scale(-1.11111,-0.814815) translate(-1255.35,-356.649)" width="15" x="652.8174603174602" xlink:href="#Disconnector:刀闸_0" y="146.6587301587301" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449688961030" ObjectName="#1主变35kV侧3019隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449688961030"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,661.151,158.881) scale(-1.11111,-0.814815) translate(-1255.35,-356.649)" width="15" x="652.8174603174602" y="146.6587301587301"/></g>
  <g id="227">
   <use class="v6300" height="30" transform="rotate(0,1003,539.778) scale(-1.11111,-0.814815) translate(-1904.87,-1205.01)" width="15" x="994.6666666666666" xlink:href="#Disconnector:刀闸_0" y="527.5555555555555" zvalue="217"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449689944070" ObjectName="首部变6.3kV侧6441隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449689944070"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1003,539.778) scale(-1.11111,-0.814815) translate(-1904.87,-1205.01)" width="15" x="994.6666666666666" y="527.5555555555555"/></g>
  <g id="214">
   <use class="kv10" height="30" transform="rotate(0,1003.27,249.556) scale(-1.11111,-0.814815) translate(-1905.38,-558.606)" width="15" x="994.9364189454009" xlink:href="#Disconnector:刀闸_0" y="237.3333333333333" zvalue="230"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449689878534" ObjectName="首部变10kV侧0416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449689878534"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1003.27,249.556) scale(-1.11111,-0.814815) translate(-1905.38,-558.606)" width="15" x="994.9364189454009" y="237.3333333333333"/></g>
  <g id="247">
   <use class="v6300" height="30" transform="rotate(0,1339,527.778) scale(-1.11111,-0.814815) translate(-2543.27,-1178.28)" width="15" x="1330.666666666667" xlink:href="#Disconnector:刀闸_0" y="515.5555555555555" zvalue="251"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449690402822" ObjectName="6.3kV母线电压互感器6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449690402822"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1339,527.778) scale(-1.11111,-0.814815) translate(-2543.27,-1178.28)" width="15" x="1330.666666666667" y="515.5555555555555"/></g>
  <g id="258">
   <use class="v6300" height="30" transform="rotate(0,548.111,769.333) scale(-1.11111,-0.814815) translate(-1040.58,-1716.29)" width="15" x="539.7777777777778" xlink:href="#Disconnector:刀闸_0" y="757.1111111111111" zvalue="267"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449690664966" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449690664966"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,548.111,769.333) scale(-1.11111,-0.814815) translate(-1040.58,-1716.29)" width="15" x="539.7777777777778" y="757.1111111111111"/></g>
  <g id="276">
   <use class="v6300" height="30" transform="rotate(0,811.444,782.667) scale(-1.11111,-0.814815) translate(-1540.91,-1745.99)" width="15" x="803.1111111111111" xlink:href="#Disconnector:刀闸_0" y="770.4444444444443" zvalue="277"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449691058181" ObjectName="#1发电机6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449691058181"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,811.444,782.667) scale(-1.11111,-0.814815) translate(-1540.91,-1745.99)" width="15" x="803.1111111111111" y="770.4444444444443"/></g>
  <g id="314">
   <use class="v6300" height="30" transform="rotate(0,1072.33,618.222) scale(-1.11111,-0.814815) translate(-2036.6,-1379.73)" width="15" x="1064" xlink:href="#Disconnector:刀闸_0" y="606" zvalue="291"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449692041221" ObjectName="#2发电机6421隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449692041221"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1072.33,618.222) scale(-1.11111,-0.814815) translate(-2036.6,-1379.73)" width="15" x="1064" y="606"/></g>
  <g id="305">
   <use class="v6300" height="30" transform="rotate(0,960.111,769.333) scale(-1.11111,-0.814815) translate(-1823.38,-1716.29)" width="15" x="951.7777777777778" xlink:href="#Disconnector:刀闸_0" y="757.1111111111111" zvalue="303"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449691648005" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449691648005"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,960.111,769.333) scale(-1.11111,-0.814815) translate(-1823.38,-1716.29)" width="15" x="951.7777777777778" y="757.1111111111111"/></g>
  <g id="297">
   <use class="v6300" height="30" transform="rotate(0,1223.44,782.667) scale(-1.11111,-0.814815) translate(-2323.71,-1745.99)" width="15" x="1215.111111111111" xlink:href="#Disconnector:刀闸_0" y="770.4444444444443" zvalue="314"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449691320325" ObjectName="#2发电机6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449691320325"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1223.44,782.667) scale(-1.11111,-0.814815) translate(-2323.71,-1745.99)" width="15" x="1215.111111111111" y="770.4444444444443"/></g>
  <g id="326">
   <use class="v6300" height="30" transform="rotate(0,1375.67,618.222) scale(-1.11111,-0.814815) translate(-2612.93,-1379.73)" width="15" x="1367.333333333333" xlink:href="#Disconnector:刀闸_0" y="606" zvalue="325"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449692303365" ObjectName="#1站用变6431隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449692303365"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1375.67,618.222) scale(-1.11111,-0.814815) translate(-2612.93,-1379.73)" width="15" x="1367.333333333333" y="606"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="159">
   <use class="v6300" height="30" transform="rotate(0,548.087,860.786) scale(1.46439,1.46439) translate(-166.844,-266.007)" width="30" x="526.1209884620066" xlink:href="#Accessory:三卷PT带容断器_0" y="838.8205128205124" zvalue="16"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449689354246" ObjectName="#1发电机机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,548.087,860.786) scale(1.46439,1.46439) translate(-166.844,-266.007)" width="30" x="526.1209884620066" y="838.8205128205124"/></g>
  <g id="158">
   <use class="v6300" height="29" transform="rotate(0,811.376,674.067) scale(1.35684,1.35684) translate(-208.033,-172.1)" width="30" x="791.0239198706518" xlink:href="#Accessory:PT12321_0" y="654.3931623931626" zvalue="17"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449689288710" ObjectName="#1发电机PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,811.376,674.067) scale(1.35684,1.35684) translate(-208.033,-172.1)" width="30" x="791.0239198706518" y="654.3931623931626"/></g>
  <g id="179">
   <use class="v6300" height="20" transform="rotate(90,655.278,445.111) scale(2.55556,2.55556) translate(-383.309,-255.382)" width="20" x="629.7222222222222" xlink:href="#Accessory:线路PT3_0" y="419.5555555555555" zvalue="109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449688567814" ObjectName="#1主变PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,655.278,445.111) scale(2.55556,2.55556) translate(-383.309,-255.382)" width="20" x="629.7222222222222" y="419.5555555555555"/></g>
  <g id="181">
   <use class="kv35" height="30" transform="rotate(90,572.222,160.775) scale(1.61111,1.77778) translate(-207.883,-58.6723)" width="30" x="548.0555555555554" xlink:href="#Accessory:避雷器PT带熔断器_0" y="134.1081721114403" zvalue="205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449689681926" ObjectName="35kV勐小T线勐劈河一级支线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,572.222,160.775) scale(1.61111,1.77778) translate(-207.883,-58.6723)" width="30" x="548.0555555555554" y="134.1081721114403"/></g>
  <g id="229">
   <use class="v6300" height="20" transform="rotate(90,923.278,445.111) scale(2.55556,2.55556) translate(-546.44,-255.382)" width="20" x="897.7222222222222" xlink:href="#Accessory:线路PT3_0" y="419.5555555555555" zvalue="233"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449690009606" ObjectName="首部变PT1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,923.278,445.111) scale(2.55556,2.55556) translate(-546.44,-255.382)" width="20" x="897.7222222222222" y="419.5555555555555"/></g>
  <g id="237">
   <use class="kv10" height="20" transform="rotate(90,923.278,209.111) scale(2.55556,2.55556) translate(-546.44,-111.729)" width="20" x="897.7222222222222" xlink:href="#Accessory:线路PT3_0" y="183.5555555555555" zvalue="244"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449690140678" ObjectName="首部变PT2"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,923.278,209.111) scale(2.55556,2.55556) translate(-546.44,-111.729)" width="20" x="897.7222222222222" y="183.5555555555555"/></g>
  <g id="241">
   <use class="v6300" height="42" transform="rotate(0,1352.03,393.778) scale(1.37566,1.37566) translate(-363.573,-99.6427)" width="30" x="1331.394618630467" xlink:href="#Accessory:4卷PT带容断器_0" y="364.8888888888889" zvalue="248"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449690206214" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1352.03,393.778) scale(1.37566,1.37566) translate(-363.573,-99.6427)" width="30" x="1331.394618630467" y="364.8888888888889"/></g>
  <g id="256">
   <use class="v6300" height="20" transform="rotate(270,749.944,715.111) scale(-2.55556,2.55556) translate(-1027.85,-419.729)" width="20" x="724.3888888888889" xlink:href="#Accessory:线路PT3_0" y="689.5555555555555" zvalue="264"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449690599430" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,749.944,715.111) scale(-2.55556,2.55556) translate(-1027.85,-419.729)" width="20" x="724.3888888888889" y="689.5555555555555"/></g>
  <g id="260">
   <use class="v6300" height="26" transform="rotate(0,548.043,668) scale(1.11111,-1.11111) translate(-54.1376,-1267.76)" width="12" x="541.376483973216" xlink:href="#Accessory:带电容电阻接地_0" y="653.5555555555555" zvalue="269"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449690861573" ObjectName="#1发电机接地"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,548.043,668) scale(1.11111,-1.11111) translate(-54.1376,-1267.76)" width="12" x="541.376483973216" y="653.5555555555555"/></g>
  <g id="277">
   <use class="v6300" height="40" transform="rotate(0,811.347,870.12) scale(1.46439,-1.09829) translate(-250.329,-1660.4)" width="30" x="789.3811024221206" xlink:href="#Accessory:带熔断器的线路PT1_0" y="848.1538461538456" zvalue="275"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449691123717" ObjectName="#1发电机励磁PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,811.347,870.12) scale(1.46439,-1.09829) translate(-250.329,-1660.4)" width="30" x="789.3811024221206" y="848.1538461538456"/></g>
  <g id="311">
   <use class="v6300" height="30" transform="rotate(0,960.087,860.786) scale(1.46439,1.46439) translate(-297.498,-266.007)" width="30" x="938.1209884620066" xlink:href="#Accessory:三卷PT带容断器_0" y="838.8205128205124" zvalue="295"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449691975685" ObjectName="#2发电机机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,960.087,860.786) scale(1.46439,1.46439) translate(-297.498,-266.007)" width="30" x="938.1209884620066" y="838.8205128205124"/></g>
  <g id="310">
   <use class="v6300" height="29" transform="rotate(0,1223.38,674.067) scale(1.35684,1.35684) translate(-316.386,-172.1)" width="30" x="1203.023919870652" xlink:href="#Accessory:PT12321_0" y="654.3931623931626" zvalue="296"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449691910149" ObjectName="#2发电机PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1223.38,674.067) scale(1.35684,1.35684) translate(-316.386,-172.1)" width="30" x="1203.023919870652" y="654.3931623931626"/></g>
  <g id="307">
   <use class="v6300" height="20" transform="rotate(270,1161.94,715.111) scale(-2.55556,2.55556) translate(-1601.06,-419.729)" width="20" x="1136.388888888889" xlink:href="#Accessory:线路PT3_0" y="689.5555555555555" zvalue="301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449691713541" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1161.94,715.111) scale(-2.55556,2.55556) translate(-1601.06,-419.729)" width="20" x="1136.388888888889" y="689.5555555555555"/></g>
  <g id="303">
   <use class="v6300" height="26" transform="rotate(0,960.043,668) scale(1.11111,-1.11111) translate(-95.3376,-1267.76)" width="12" x="953.3764839732158" xlink:href="#Accessory:带电容电阻接地_0" y="653.5555555555555" zvalue="306"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449691451397" ObjectName="#2发电机接地"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,960.043,668) scale(1.11111,-1.11111) translate(-95.3376,-1267.76)" width="12" x="953.3764839732158" y="653.5555555555555"/></g>
  <g id="298">
   <use class="v6300" height="40" transform="rotate(0,1223.35,870.12) scale(1.46439,-1.09829) translate(-380.983,-1660.4)" width="30" x="1201.381102422121" xlink:href="#Accessory:带熔断器的线路PT1_0" y="848.1538461538456" zvalue="312"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449691385861" ObjectName="#2发电机励磁PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1223.35,870.12) scale(1.46439,-1.09829) translate(-380.983,-1660.4)" width="30" x="1201.381102422121" y="848.1538461538456"/></g>
  <g id="328">
   <use class="v6300" height="27" transform="rotate(270,1450.61,729.667) scale(-1.57407,1.49794) translate(-2368.73,-235.832)" width="12" x="1441.166666666667" xlink:href="#Accessory:避雷器11_0" y="709.4444444444445" zvalue="332"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449692368901" ObjectName="#1站用变避雷器"/>
   </metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(270,1450.61,729.667) scale(-1.57407,1.49794) translate(-2368.73,-235.832)" width="12" x="1441.166666666667" y="709.4444444444445"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="PowerTransformer2Class">
  <g id="178">
   <g id="1780">
    <use class="kv35" height="60" transform="rotate(0,734.435,387.787) scale(1.3754,1.34844) translate(-192.949,-89.7511)" width="40" x="706.9299999999999" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="347.33" zvalue="45"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874427662340" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1781">
    <use class="v6300" height="60" transform="rotate(0,734.435,387.787) scale(1.3754,1.34844) translate(-192.949,-89.7511)" width="40" x="706.9299999999999" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="347.33" zvalue="45"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874427727876" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399446102020" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399446102020"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,734.435,387.787) scale(1.3754,1.34844) translate(-192.949,-89.7511)" width="40" x="706.9299999999999" y="347.33"/></g>
  <g id="224">
   <g id="2240">
    <use class="kv10" height="60" transform="rotate(0,1002.43,387.787) scale(1.3754,1.34844) translate(-266.097,-89.7511)" width="40" x="974.9299999999999" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="347.33" zvalue="222"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874427793412" ObjectName="10"/>
    </metadata>
   </g>
   <g id="2241">
    <use class="v6300" height="60" transform="rotate(0,1002.43,387.787) scale(1.3754,1.34844) translate(-266.097,-89.7511)" width="40" x="974.9299999999999" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="347.33" zvalue="222"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874427858948" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399446167556" ObjectName="首部变"/>
   <cge:TPSR_Ref TObjectID="6755399446167556"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1002.43,387.787) scale(1.3754,1.34844) translate(-266.097,-89.7511)" width="40" x="974.9299999999999" y="347.33"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="279">
   <use class="kv35" height="20" transform="rotate(270,664.889,274.222) scale(-1.33333,-1.11111) translate(-1161.89,-519.911)" width="10" x="658.2222222222222" xlink:href="#GroundDisconnector:地刀_0" y="263.1111111111111" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449689157638" ObjectName="#1主变35kV侧30160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449689157638"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,664.889,274.222) scale(-1.33333,-1.11111) translate(-1161.89,-519.911)" width="10" x="658.2222222222222" y="263.1111111111111"/></g>
  <g id="125">
   <use class="kv35" height="20" transform="rotate(180,694.643,199.103) scale(-1.33333,-1.11111) translate(-1213.96,-377.185)" width="10" x="687.9761904761904" xlink:href="#GroundDisconnector:地刀_0" y="187.9920634920634" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449688895494" ObjectName="#1主变35kV侧30167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449688895494"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,694.643,199.103) scale(-1.33333,-1.11111) translate(-1213.96,-377.185)" width="10" x="687.9761904761904" y="187.9920634920634"/></g>
  <g id="131">
   <use class="kv35" height="20" transform="rotate(180,630.167,193.736) scale(-1.33333,-1.11111) translate(-1101.12,-366.988)" width="10" x="623.4999999999998" xlink:href="#GroundDisconnector:地刀_0" y="182.6250724003416" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449688764422" ObjectName="#1主变35kV侧30197接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449688764422"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,630.167,193.736) scale(-1.33333,-1.11111) translate(-1101.12,-366.988)" width="10" x="623.4999999999998" y="182.6250724003416"/></g>
  <g id="174">
   <use class="v6300" height="20" transform="rotate(270,684.667,517.778) scale(-1.33333,-1.11111) translate(-1196.5,-982.667)" width="10" x="678" xlink:href="#GroundDisconnector:地刀_0" y="506.6666666666667" zvalue="201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449689616390" ObjectName="#1主变6.3kV侧60117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449689616390"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,684.667,517.778) scale(-1.33333,-1.11111) translate(-1196.5,-982.667)" width="10" x="678" y="506.6666666666667"/></g>
  <g id="212">
   <use class="v6300" height="20" transform="rotate(270,954.667,512.222) scale(-1.33333,-1.11111) translate(-1669,-972.111)" width="10" x="948" xlink:href="#GroundDisconnector:地刀_0" y="501.1111111111111" zvalue="234"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449689812998" ObjectName="首部变6.3kV侧64417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449689812998"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,954.667,512.222) scale(-1.33333,-1.11111) translate(-1669,-972.111)" width="10" x="948" y="501.1111111111111"/></g>
  <g id="246">
   <use class="v6300" height="20" transform="rotate(270,1290.67,480.222) scale(-1.33333,-1.11111) translate(-2257,-911.311)" width="10" x="1284" xlink:href="#GroundDisconnector:地刀_0" y="469.1111111111111" zvalue="253"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449690337286" ObjectName="6.3kV母线电压互感器69017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449690337286"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1290.67,480.222) scale(-1.33333,-1.11111) translate(-2257,-911.311)" width="10" x="1284" y="469.1111111111111"/></g>
  <g id="251">
   <use class="v6300" height="20" transform="rotate(90,719.111,645.556) scale(-1.33333,-1.11111) translate(-1256.78,-1225.44)" width="10" x="712.4444444444447" xlink:href="#GroundDisconnector:地刀_0" y="634.4444444444446" zvalue="259"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449690533894" ObjectName="#1发电机64117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449690533894"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,719.111,645.556) scale(-1.33333,-1.11111) translate(-1256.78,-1225.44)" width="10" x="712.4444444444447" y="634.4444444444446"/></g>
  <g id="259">
   <use class="v6300" height="20" transform="rotate(90,600.222,814.444) scale(-1.33333,-1.11111) translate(-1048.72,-1546.33)" width="10" x="593.5555555555557" xlink:href="#GroundDisconnector:地刀_0" y="803.3333333333335" zvalue="268"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449690796038" ObjectName="#1发电机69117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449690796038"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,600.222,814.444) scale(-1.33333,-1.11111) translate(-1048.72,-1546.33)" width="10" x="593.5555555555557" y="803.3333333333335"/></g>
  <g id="275">
   <use class="v6300" height="20" transform="rotate(90,863.556,827.778) scale(-1.33333,-1.11111) translate(-1509.56,-1571.67)" width="10" x="856.888888888889" xlink:href="#GroundDisconnector:地刀_0" y="816.6666666666667" zvalue="279"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449690992645" ObjectName="#1发电机69127接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449690992645"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,863.556,827.778) scale(-1.33333,-1.11111) translate(-1509.56,-1571.67)" width="10" x="856.888888888889" y="816.6666666666667"/></g>
  <g id="309">
   <use class="v6300" height="20" transform="rotate(90,1131.11,645.556) scale(-1.33333,-1.11111) translate(-1977.78,-1225.44)" width="10" x="1124.444444444445" xlink:href="#GroundDisconnector:地刀_0" y="634.4444444444446" zvalue="298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449691844613" ObjectName="#2发电机64217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449691844613"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1131.11,645.556) scale(-1.33333,-1.11111) translate(-1977.78,-1225.44)" width="10" x="1124.444444444445" y="634.4444444444446"/></g>
  <g id="304">
   <use class="v6300" height="20" transform="rotate(90,1012.22,814.444) scale(-1.33333,-1.11111) translate(-1769.72,-1546.33)" width="10" x="1005.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="803.3333333333335" zvalue="305"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449691582469" ObjectName="#2发电机69217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449691582469"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1012.22,814.444) scale(-1.33333,-1.11111) translate(-1769.72,-1546.33)" width="10" x="1005.555555555556" y="803.3333333333335"/></g>
  <g id="295">
   <use class="v6300" height="20" transform="rotate(90,1275.56,827.778) scale(-1.33333,-1.11111) translate(-2230.56,-1571.67)" width="10" x="1268.888888888889" xlink:href="#GroundDisconnector:地刀_0" y="816.6666666666667" zvalue="316"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449691254789" ObjectName="#2发电机69227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449691254789"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1275.56,827.778) scale(-1.33333,-1.11111) translate(-2230.56,-1571.67)" width="10" x="1268.888888888889" y="816.6666666666667"/></g>
  <g id="323">
   <use class="v6300" height="20" transform="rotate(90,1434.44,645.556) scale(-1.33333,-1.11111) translate(-2508.61,-1225.44)" width="10" x="1427.777777777778" xlink:href="#GroundDisconnector:地刀_0" y="634.4444444444446" zvalue="329"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449692237829" ObjectName="#1站用变64317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449692237829"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1434.44,645.556) scale(-1.33333,-1.11111) translate(-2508.61,-1225.44)" width="10" x="1427.777777777778" y="634.4444444444446"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="222">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="222" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124682334213" ObjectName="GEN_PSUM"/>
   </metadata>
  </g>
  <g id="221">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="221" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124682399749" ObjectName="GEN_QSUM"/>
   </metadata>
  </g>
  <g id="192">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="192" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124682203141" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="182">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="182" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124682268677" ObjectName="LOAD_QSUM"/>
   </metadata>
  </g>
  <g id="167">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="167" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124681744389" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="160">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="160" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124681875461" ObjectName="雨量采集"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="140" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124682203141" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="136" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124682203141" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="263">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="263" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,403.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="408.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124681809925" ObjectName="坝下水位"/>
   </metadata>
  </g>
  <g id="264">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="264" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124674863109" ObjectName="F"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,399.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="404.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124682006533" ObjectName="入库流量"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="3" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,806.435,289.679) scale(1,1) translate(0,3.09012e-13)" writing-mode="lr" x="805.88" xml:space="preserve" y="294.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124670734341" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="4" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1073.43,277.359) scale(1,1) translate(1.13291e-13,-1.76684e-13)" writing-mode="lr" x="1072.88" xml:space="preserve" y="282.05" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124675518469" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="5" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,806.435,313.667) scale(1,1) translate(0,3.35644e-13)" writing-mode="lr" x="805.88" xml:space="preserve" y="318.35" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124670799877" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="6" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1073.43,308.119) scale(1,1) translate(1.13291e-13,1.64312e-13)" writing-mode="lr" x="1072.88" xml:space="preserve" y="312.81" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124675584005" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="7" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,805.435,457.44) scale(1,1) translate(0,0)" writing-mode="lr" x="804.88" xml:space="preserve" y="462.12" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124670865413" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="8" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1074.43,454.885) scale(1,1) translate(1.13402e-13,-9.8197e-14)" writing-mode="lr" x="1073.88" xml:space="preserve" y="459.59" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124675649543" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="9" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,805.435,481.12) scale(1,1) translate(0,0)" writing-mode="lr" x="804.88" xml:space="preserve" y="485.8" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124670930949" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="10" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1074.43,481.62) scale(1,1) translate(1.13402e-13,0)" writing-mode="lr" x="1073.88" xml:space="preserve" y="486.32" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124675715080" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="11" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,806.435,337.654) scale(1,1) translate(0,3.62276e-13)" writing-mode="lr" x="805.88" xml:space="preserve" y="342.33" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124670996485" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="12" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1073.43,338.879) scale(1,1) translate(1.13291e-13,1.81387e-13)" writing-mode="lr" x="1072.88" xml:space="preserve" y="343.57" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124675780616" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="13" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,805.435,504.8) scale(1,1) translate(0,-1.09601e-13)" writing-mode="lr" x="804.88" xml:space="preserve" y="509.48" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124671324165" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="14" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1074.43,508.355) scale(1,1) translate(1.13402e-13,-1.1007e-13)" writing-mode="lr" x="1073.88" xml:space="preserve" y="513.0599999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124676108296" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="15" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,736.202,20.1713) scale(1,1) translate(-1.53478e-13,2.22538e-14)" writing-mode="lr" x="735.73" xml:space="preserve" y="24.85" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124669161479" ObjectName="P"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="16" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1004.2,47.6162) scale(1,1) translate(0,7.40642e-14)" writing-mode="lr" x="1003.73" xml:space="preserve" y="52.31" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124678074373" ObjectName="P"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="19" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,736.202,43.5556) scale(1,1) translate(-1.53478e-13,7.93697e-14)" writing-mode="lr" x="735.73" xml:space="preserve" y="48.23" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124669227015" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="22" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1004.2,74.0556) scale(1,1) translate(0,1.29836e-13)" writing-mode="lr" x="1003.73" xml:space="preserve" y="78.75" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124678139909" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="25">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="25" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,736.202,66.9399) scale(1,1) translate(-1.53478e-13,1.36486e-13)" writing-mode="lr" x="735.73" xml:space="preserve" y="71.62" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124669292549" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="30" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1004.2,100.495) scale(1,1) translate(0,1.85608e-13)" writing-mode="lr" x="1003.73" xml:space="preserve" y="105.19" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124678205445" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="31" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,535.778,607.194) scale(1,1) translate(0,0)" writing-mode="lr" x="535.3099999999999" xml:space="preserve" y="611.97" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124674666501" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="32" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,662,869.647) scale(1,1) translate(0,-1.52056e-12)" writing-mode="lr" x="661.45" xml:space="preserve" y="874.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124673814533" ObjectName="P"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="37" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1074,869.647) scale(1,1) translate(0,-1.52056e-12)" writing-mode="lr" x="1073.45" xml:space="preserve" y="874.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124679647237" ObjectName="P"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="38" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,662,898.5) scale(1,1) translate(0,-1.57182e-12)" writing-mode="lr" x="661.45" xml:space="preserve" y="903.22" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124673880069" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="39" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1074,898.5) scale(1,1) translate(0,-1.57182e-12)" writing-mode="lr" x="1073.45" xml:space="preserve" y="903.22" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124679712773" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="40">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="40" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,662,927.353) scale(1,1) translate(0,-1.62307e-12)" writing-mode="lr" x="661.45" xml:space="preserve" y="932.0700000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124673945605" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="41">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="41" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1074,927.353) scale(1,1) translate(0,-1.62307e-12)" writing-mode="lr" x="1073.45" xml:space="preserve" y="932.0700000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124679778309" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="218">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="164"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374886961155" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="215">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="165"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950284836872" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="234">
   <use class="kv10" height="30" transform="rotate(0,1003.2,154.486) scale(1.54762,0.891667) translate(-353.062,17.1443)" width="7" x="997.7851251408389" xlink:href="#ACLineSegment:线路_0" y="141.1111111111111" zvalue="241"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449690075142" ObjectName="10kV首部线"/>
   <cge:TPSR_Ref TObjectID="6192449690075142_5066549583609858"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1003.2,154.486) scale(1.54762,0.891667) translate(-353.062,17.1443)" width="7" x="997.7851251408389" y="141.1111111111111"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="331">
   <use class="v6300" height="38" transform="rotate(0,1380.75,785.778) scale(1.11111,1.11111) translate(-136.63,-76.4667)" width="26" x="1366.303703703704" xlink:href="#EnergyConsumer:站用变1节点_0" y="764.6666666666667" zvalue="335"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449692434437" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,1380.75,785.778) scale(1.11111,1.11111) translate(-136.63,-76.4667)" width="26" x="1366.303703703704" y="764.6666666666667"/></g>
 </g>
</svg>