<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549678505985" height="1080" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1080" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1754.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2334.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_0" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="3" y1="19" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="4.75" y2="8.75"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="18.75" y2="21.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_1" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="2" y2="24"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_2" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.08333333333333" x2="3.995614035087721" y1="9.083333333333332" y2="17.19627192982456"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.717927631578948" x2="10.16666666666667" y1="9.172423245614038" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="2" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="21.25" y2="24.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:PT232_0" viewBox="0,0,25,35">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0.2666666666666657"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="12.5" y1="15.16666666666667" y2="0.5"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12.5,7.5) scale(1,1) translate(0,0)" width="6" x="9.5" y="2.5"/>
   <ellipse cx="12.75" cy="20" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.75" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.67" cy="29.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.82" cy="24.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333336" x2="5.583333333333336" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="5.583333333333334" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="5.5" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.75" x2="12.75" y1="22.25" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="12.75" y1="17.63888888888889" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="12.75" y1="17.63888888888889" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="32" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="12.58333333333333" y1="27.38888888888889" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="12.58333333333333" y1="27.38888888888889" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="19.31481481481482" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.94444444444444" x2="20.62962962962963" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21.94444444444444" y1="24.96612466124661" y2="24.96612466124661"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Compensator:并联电容器12121_0" viewBox="0,0,60,30">
   <use terminal-index="0" type="0" x="3.373372509142698" xlink:href="#terminal" y="12.54754746794735"/>
   <rect fill-opacity="0" height="2.8" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,35.96,12.55) scale(1,1) translate(0,0)" width="8.41" x="31.75" y="11.15"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="45.41541591575949" x2="50.34627285851084" y1="12.54754746794735" y2="12.54754746794735"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="47.20609554233762" x2="50.34627285851084" y1="22.75961171517187" y2="22.75961171517187"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.10210217033084" x2="29.01382861144973" y1="22.70770795787974" y2="22.70770795787974"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.92592676741369" x2="55.92592676741369" y1="1.569902800664089" y2="23.73646842528693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.92592676741369" x2="53.12312387363924" y1="23.73646842528693" y2="23.73646842528693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="50.32032097986479" x2="55.92592676741369" y1="12.54754746794735" y2="12.54754746794735"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="53.12312387363924" x2="55.92592676741369" y1="1.569902800664083" y2="1.569902800664083"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="50.32032097986479" x2="50.32032097986479" y1="12.54754746794735" y2="22.72068389720278"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.75175180860903" x2="48.21821880953394" y1="29.01401446887226" y2="29.01401446887226"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.94894891483458" x2="28.94894891483458" y1="22.66878013991066" y2="12.54754746794735"/>
   <path d="M 37.1455 22.7077 A 4.15364 2.547 -90 0 1 32.0515 22.7077" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 42.2395 22.7077 A 4.15364 2.547 -90 0 1 37.1455 22.7077" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 47.1934 22.7077 A 4.15364 2.547 -90 0 1 42.0994 22.7077" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="44.01401446887226" x2="44.01401446887226" y1="10.44544529761651" y2="14.64964963827819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="45.41541591575949" x2="45.41541591575949" y1="10.44544529761651" y2="14.64964963827819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.789455239606355" x2="8.803803115830696" y1="12.51251243177518" y2="12.51251243177518"/>
   <path d="M 15.7174 19.1458 A 6.89022 6.65666 -180 1 0 8.82716 12.4892" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.8108 19.0524 L 15.9276 12.5125 L 44.1308 12.5125" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀-带电显示_0" viewBox="0,0,21,29">
   <use terminal-index="0" type="0" x="10.57144275301999" xlink:href="#terminal" y="0.9988473696347349"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="4.5" y1="4.5" y2="4.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="4.5" y2="8.5"/>
   <ellipse cx="4.5" cy="16.25" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="21.5" y2="19.08333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="1.15" y2="4.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.5" y2="21.5"/>
   <path d="M 2.25 14.4167 L 6.58333 18" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.41666666666667" y2="18.01429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="21.35" y2="24.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.166666666666666" x2="13.5" y1="24.24694619969017" y2="24.24694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.583333333333334" x2="12.5" y1="26.08157590710536" y2="26.08157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.274021844661625" x2="11.93079938068318" y1="27.83348701738202" y2="27.83348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.16666666666667" x2="16.33141025641025" y1="8.166666666666666" y2="18.0142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.66666666666667" x2="18" y1="8.04249548976499" y2="8.04249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="8.000000000000004" y2="4.416666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.41666666666666" x2="4.416666666666666" y1="21.41666666666666" y2="21.41666666666666"/>
   <path d="M 6.66667 14.25 L 2.41667 18.3333" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="10.91666666666667" y2="10.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀-带电显示_1" viewBox="0,0,21,29">
   <use terminal-index="0" type="0" x="10.57144275301999" xlink:href="#terminal" y="0.9988473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.166666666666666" x2="13.5" y1="24.24694619969017" y2="24.24694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.583333333333334" x2="12.5" y1="26.08157590710536" y2="26.08157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.274021844661625" x2="11.93079938068318" y1="27.83348701738202" y2="27.83348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.367489181242" x2="16.367489181242" y1="8.166666666666666" y2="20.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="8.000000000000004" y2="4.416666666666668"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.66666666666667" x2="18" y1="8.04249548976499" y2="8.04249548976499"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="21.5" y2="19.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.367489181242" x2="16.367489181242" y1="20.41666666666667" y2="18.01429160524171"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="1.15" y2="4.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.5" y2="21.5"/>
   <ellipse cx="4.5" cy="16.25" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="4.5" y1="4.5" y2="4.5"/>
   <path d="M 2.25 14.4167 L 6.58333 18" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="4.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="21.35" y2="24.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.367489181242" x2="16.367489181242" y1="8.000000000000004" y2="4.416666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="10.91666666666667" y2="10.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.41666666666666" x2="4.416666666666666" y1="21.41666666666666" y2="21.41666666666666"/>
   <path d="M 6.66667 14.25 L 2.41667 18.3333" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀-带电显示_2" viewBox="0,0,21,29">
   <use terminal-index="0" type="0" x="10.57144275301999" xlink:href="#terminal" y="0.9988473696347349"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.75" x2="12.75" y1="8.5" y2="17.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.41666666666667" y2="18.01429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.166666666666666" x2="13.5" y1="24.24694619969017" y2="24.24694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.583333333333334" x2="12.5" y1="26.08157590710536" y2="26.08157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.274021844661625" x2="11.93079938068318" y1="27.83348701738202" y2="27.83348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.75" x2="20" y1="8.383333333333333" y2="17.8"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="8.000000000000004" y2="4.416666666666668"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.66666666666667" x2="18" y1="8.04249548976499" y2="8.04249548976499"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="21.5" y2="19.08333333333333"/>
   <ellipse cx="4.5" cy="16.25" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="1.15" y2="4.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="21.35" y2="24.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="4.5" y1="4.5" y2="4.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.5" y2="21.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="4.5" y2="8.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="11" y2="13.5"/>
   <path d="M 2.25 14.4167 L 6.58333 18" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.41666666666666" x2="4.416666666666666" y1="21.41666666666666" y2="21.41666666666666"/>
   <path d="M 6.66667 14.25 L 2.41667 18.3333" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="10.91666666666667" y2="10.91666666666667"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.998992968246015" x2="4.998992968246015" y1="14.41666666666666" y2="17.66666666666666"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="1.08333333333333" y2="5.749999999999999"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="6" y2="14"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="14" y2="6"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id="State:间隔模板_0" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(85,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="State:间隔模板_1" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(185,185,185)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV江东变" InitShowingPlane="" fill="rgb(0,0,0)" height="1080" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="59.4" id="1" preserveAspectRatio="xMidYMid slice" width="189.48" x="68.31" xlink:href="logo.png" y="61.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="424" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,163.046,91.1272) scale(1,1) translate(7.58357e-15,-5.45583e-14)" writing-mode="lr" x="163.05" xml:space="preserve" y="95.63" zvalue="5718"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,174.5,89.3894) scale(1,1) translate(0,7.48076e-15)" writing-mode="lr" x="174.5" xml:space="preserve" y="96.89" zvalue="5719">    35kV江东变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="213" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,90.625,313.25) scale(1,1) translate(0,0)" width="72.88" x="54.19" y="301.25" zvalue="11068"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90.625,313.25) scale(1,1) translate(0,0)" writing-mode="lr" x="90.63" xml:space="preserve" y="317.75" zvalue="11068">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="296" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,192.531,386.25) scale(1,1) translate(0,0)" width="72.88" x="156.09" y="374.25" zvalue="11069"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,192.531,386.25) scale(1,1) translate(0,0)" writing-mode="lr" x="192.53" xml:space="preserve" y="390.75" zvalue="11069">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="295" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,90.625,386.25) scale(1,1) translate(0,0)" width="72.88" x="54.19" y="374.25" zvalue="11070"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90.625,386.25) scale(1,1) translate(0,0)" writing-mode="lr" x="90.63" xml:space="preserve" y="390.75" zvalue="11070">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="215" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,90.625,349.75) scale(1,1) translate(0,0)" width="72.88" x="54.19" y="337.75" zvalue="11071"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90.625,349.75) scale(1,1) translate(0,0)" writing-mode="lr" x="90.63" xml:space="preserve" y="354.25" zvalue="11071">信号一览</text>
  <line fill="none" id="170" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="371.9999999999999" x2="371.9999999999999" y1="3.75" y2="1078.75" zvalue="10340"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.57100000000014" x2="96.78530000000012" y1="963" y2="963"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.57100000000014" x2="96.78530000000012" y1="1002.1633" y2="1002.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.57100000000014" x2="8.57100000000014" y1="963" y2="1002.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.78530000000012" x2="96.78530000000012" y1="963" y2="1002.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.78570000000013" x2="361.4287000000002" y1="963" y2="963"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.78570000000013" x2="361.4287000000002" y1="1002.1633" y2="1002.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.78570000000013" x2="96.78570000000013" y1="963" y2="1002.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.4287000000002" x2="361.4287000000002" y1="963" y2="1002.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.57100000000014" x2="96.78530000000012" y1="1002.16327" y2="1002.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.57100000000014" x2="96.78530000000012" y1="1030.08167" y2="1030.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.57100000000014" x2="8.57100000000014" y1="1002.16327" y2="1030.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.78530000000012" x2="96.78530000000012" y1="1002.16327" y2="1030.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.78570000000013" x2="185.0000000000001" y1="1002.16327" y2="1002.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.78570000000013" x2="185.0000000000001" y1="1030.08167" y2="1030.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.78570000000013" x2="96.78570000000013" y1="1002.16327" y2="1030.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="1002.16327" y2="1030.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="273.2143000000002" y1="1002.16327" y2="1002.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="273.2143000000002" y1="1030.08167" y2="1030.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="185.0000000000002" y1="1002.16327" y2="1030.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2143000000002" x2="273.2143000000002" y1="1002.16327" y2="1030.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2143000000001" x2="361.4286000000001" y1="1002.16327" y2="1002.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2143000000001" x2="361.4286000000001" y1="1030.08167" y2="1030.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2143000000001" x2="273.2143000000001" y1="1002.16327" y2="1030.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.4286000000001" x2="361.4286000000001" y1="1002.16327" y2="1030.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.57100000000014" x2="96.78530000000012" y1="1030.0816" y2="1030.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.57100000000014" x2="96.78530000000012" y1="1058" y2="1058"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.57100000000014" x2="8.57100000000014" y1="1030.0816" y2="1058"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.78530000000012" x2="96.78530000000012" y1="1030.0816" y2="1058"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.78570000000013" x2="185.0000000000001" y1="1030.0816" y2="1030.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.78570000000013" x2="185.0000000000001" y1="1058" y2="1058"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.78570000000013" x2="96.78570000000013" y1="1030.0816" y2="1058"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="1030.0816" y2="1058"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="273.2143000000002" y1="1030.0816" y2="1030.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="273.2143000000002" y1="1058" y2="1058"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="185.0000000000002" y1="1030.0816" y2="1058"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2143000000002" x2="273.2143000000002" y1="1030.0816" y2="1058"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2143000000001" x2="361.4286000000001" y1="1030.0816" y2="1030.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2143000000001" x2="361.4286000000001" y1="1058" y2="1058"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2143000000001" x2="273.2143000000001" y1="1030.0816" y2="1058"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.4286000000001" x2="361.4286000000001" y1="1030.0816" y2="1058"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49,983) scale(1,1) translate(0,0)" writing-mode="lr" x="49" xml:space="preserve" y="989" zvalue="10346">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46,1017) scale(1,1) translate(0,0)" writing-mode="lr" x="46" xml:space="preserve" y="1023" zvalue="10347">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228,1017) scale(1,1) translate(0,0)" writing-mode="lr" x="228" xml:space="preserve" y="1023" zvalue="10348">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45,1045) scale(1,1) translate(0,0)" writing-mode="lr" x="45" xml:space="preserve" y="1051" zvalue="10349">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227,1045) scale(1,1) translate(0,0)" writing-mode="lr" x="227" xml:space="preserve" y="1051" zvalue="10350">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,81.5635,634.325) scale(1,1) translate(0,0)" writing-mode="lr" x="81.56349206349171" xml:space="preserve" y="638.8253968253969" zvalue="10353">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229.054,985) scale(1,1) translate(0,0)" writing-mode="lr" x="229.05" xml:space="preserve" y="991" zvalue="10364">JiangDong-01-2020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,139.054,1017) scale(1,1) translate(0,0)" writing-mode="lr" x="139.05" xml:space="preserve" y="1023" zvalue="10365">李艳</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,319.054,1017) scale(1,1) translate(0,0)" writing-mode="lr" x="319.05" xml:space="preserve" y="1023" zvalue="10366">20200818</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,805.985,347.208) scale(1,1) translate(0,0)" writing-mode="lr" x="805.98" xml:space="preserve" y="351.71" zvalue="10398">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,535.386,704.003) scale(1,1) translate(0,0)" writing-mode="lr" x="535.39" xml:space="preserve" y="708.5" zvalue="10400">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,947.386,243.39) scale(1,1) translate(0,0)" writing-mode="lr" x="947.39" xml:space="preserve" y="247.89" zvalue="10406">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="249" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1030.09,100.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1030.09" xml:space="preserve" y="105.07" zvalue="10442">35kV轩江线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="248" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1009.31,275.404) scale(1,1) translate(0,0)" writing-mode="lr" x="1009.31" xml:space="preserve" y="279.9" zvalue="10443">351</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1012.8,212.514) scale(1,1) translate(0,0)" writing-mode="lr" x="1012.8" xml:space="preserve" y="217.01" zvalue="10445">3516</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="246" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1065.73,189.075) scale(1,1) translate(0,0)" writing-mode="lr" x="1065.73" xml:space="preserve" y="193.57" zvalue="10447">35167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1012.92,334.308) scale(1,1) translate(0,0)" writing-mode="lr" x="1012.92" xml:space="preserve" y="338.81" zvalue="10450">3511</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="244" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1065.73,317.075) scale(1,1) translate(0,0)" writing-mode="lr" x="1065.73" xml:space="preserve" y="321.57" zvalue="10458">35117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="243" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1070.72,139.014) scale(1,1) translate(0,0)" writing-mode="lr" x="1070.72" xml:space="preserve" y="143.51" zvalue="10462">3519</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="231" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1599.07,285.074) scale(1,1) translate(0,4.92182e-13)" writing-mode="lr" x="1599.07" xml:space="preserve" y="289.57" zvalue="10491">39017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="230" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1599.07,352.074) scale(1,1) translate(0,0)" writing-mode="lr" x="1599.07" xml:space="preserve" y="356.57" zvalue="10494">39010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,918.048,476.35) scale(1,1) translate(0,-2.07989e-13)" writing-mode="lr" x="918.05" xml:space="preserve" y="480.85" zvalue="10500">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="227" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,920.256,398.809) scale(1,1) translate(0,0)" writing-mode="lr" x="920.26" xml:space="preserve" y="403.31" zvalue="10502">3021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="222" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,947.847,442.45) scale(1,1) translate(0,0)" writing-mode="lr" x="947.85" xml:space="preserve" y="446.95" zvalue="10506">30217</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1248.27,704.013) scale(1,1) translate(0,0)" writing-mode="lr" x="1248.27" xml:space="preserve" y="708.51" zvalue="10568">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1133.25,769.477) scale(1,1) translate(0,0)" writing-mode="lr" x="1133.25" xml:space="preserve" y="773.98" zvalue="10575">0122</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,957.895,542.263) scale(1,1) translate(0,0)" writing-mode="lr" x="957.9" xml:space="preserve" y="546.76" zvalue="10583">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1065.73,249.075) scale(1,1) translate(0,0)" writing-mode="lr" x="1065.73" xml:space="preserve" y="253.57" zvalue="10584">35160</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1531.25,303.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1531.25" xml:space="preserve" y="307.72" zvalue="10591">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1230.09,100.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1230.09" xml:space="preserve" y="105.07" zvalue="10726">35kV江五线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1209.31,275.404) scale(1,1) translate(0,0)" writing-mode="lr" x="1209.31" xml:space="preserve" y="279.9" zvalue="10727">352</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1212.8,212.514) scale(1,1) translate(0,0)" writing-mode="lr" x="1212.8" xml:space="preserve" y="217.01" zvalue="10729">3526</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1265.73,189.075) scale(1,1) translate(0,0)" writing-mode="lr" x="1265.73" xml:space="preserve" y="193.57" zvalue="10731">35267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1212.92,334.308) scale(1,1) translate(-1.33163e-12,0)" writing-mode="lr" x="1212.92" xml:space="preserve" y="338.81" zvalue="10734">3521</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1265.73,317.075) scale(1,1) translate(0,0)" writing-mode="lr" x="1265.73" xml:space="preserve" y="321.57" zvalue="10742">35217</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1265.73,249.075) scale(1,1) translate(0,0)" writing-mode="lr" x="1265.73" xml:space="preserve" y="253.57" zvalue="10750">35260</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,925.19,639.909) scale(1,1) translate(0,0)" writing-mode="lr" x="925.1900000000001" xml:space="preserve" y="644.41" zvalue="10756">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,964.895,564.354) scale(1,1) translate(0,1.23535e-13)" writing-mode="lr" x="964.9" xml:space="preserve" y="568.85" zvalue="10757">2500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1430.05,476.35) scale(1,1) translate(0,-2.07989e-13)" writing-mode="lr" x="1430.05" xml:space="preserve" y="480.85" zvalue="10781">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1432.26,398.809) scale(1,1) translate(0,0)" writing-mode="lr" x="1432.26" xml:space="preserve" y="403.31" zvalue="10783">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1458.57,443.359) scale(1,1) translate(-2.2391e-12,0)" writing-mode="lr" x="1458.57" xml:space="preserve" y="447.86" zvalue="10787">30117</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1467.56,531.242) scale(1,1) translate(-1.92854e-12,0)" writing-mode="lr" x="1467.56" xml:space="preserve" y="535.74" zvalue="10790">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1437.19,639.909) scale(1,1) translate(0,0)" writing-mode="lr" x="1437.19" xml:space="preserve" y="644.41" zvalue="10793">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1476.9,564.354) scale(1,1) translate(0,1.23535e-13)" writing-mode="lr" x="1476.9" xml:space="preserve" y="568.85" zvalue="10794">2000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,595.099,764.909) scale(1,1) translate(0,0)" writing-mode="lr" x="595.1" xml:space="preserve" y="769.41" zvalue="10798">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,588.942,837.502) scale(1,1) translate(0,0)" writing-mode="lr" x="588.9400000000001" xml:space="preserve" y="842" zvalue="10805">0516</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,616.655,817.038) scale(1,1) translate(0,0)" writing-mode="lr" x="616.66" xml:space="preserve" y="821.54" zvalue="10811">05160</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" x="589.6640625" xml:space="preserve" y="578.288353139704" zvalue="10814">10kVⅡ段母线电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="589.6640625" xml:space="preserve" y="594.288353139704" zvalue="10814">压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,612.909,676.273) scale(1,1) translate(0,0)" writing-mode="lr" x="612.91" xml:space="preserve" y="680.77" zvalue="10816">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,669.636,676.273) scale(1,1) translate(0,0)" writing-mode="lr" x="669.64" xml:space="preserve" y="680.77" zvalue="10818">0802</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,619.382,886.401) scale(1,1) translate(0,0)" writing-mode="lr" x="619.38" xml:space="preserve" y="890.9" zvalue="10824">05167</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,572.909,969) scale(1,1) translate(0,0)" writing-mode="lr" x="572.91" xml:space="preserve" y="973.5" zvalue="10826">10kV2号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,538.792,825.413) scale(1,1) translate(0,0)" writing-mode="lr" x="538.79" xml:space="preserve" y="829.91" zvalue="10828">05117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,759.099,764.909) scale(1,1) translate(0,0)" writing-mode="lr" x="759.1" xml:space="preserve" y="769.41" zvalue="10832">052</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,752.942,837.502) scale(1,1) translate(0,0)" writing-mode="lr" x="752.9400000000001" xml:space="preserve" y="842" zvalue="10836">0526</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,704.221,829.699) scale(1,1) translate(0,0)" writing-mode="lr" x="704.22" xml:space="preserve" y="834.2" zvalue="10848">05217</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,734,968.638) scale(1,1) translate(-3.10418e-13,0)" writing-mode="lr" x="734" xml:space="preserve" y="973.14" zvalue="10852">10kV江东线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,899.099,764.909) scale(1,1) translate(0,0)" writing-mode="lr" x="899.1" xml:space="preserve" y="769.41" zvalue="10858">053</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,892.942,837.502) scale(1,1) translate(0,0)" writing-mode="lr" x="892.9400000000001" xml:space="preserve" y="842" zvalue="10862">0536</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,844.221,829.699) scale(1,1) translate(-3.66694e-13,0)" writing-mode="lr" x="844.22" xml:space="preserve" y="834.2" zvalue="10864">05317</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,874,972.638) scale(1,1) translate(0,0)" writing-mode="lr" x="874" xml:space="preserve" y="977.14" zvalue="10867">10kV仙人洞线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1039.1,764.909) scale(1,1) translate(0,0)" writing-mode="lr" x="1039.1" xml:space="preserve" y="769.41" zvalue="10873">054</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1032.94,837.502) scale(1,1) translate(0,0)" writing-mode="lr" x="1032.94" xml:space="preserve" y="842" zvalue="10877">0546</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,984.221,829.699) scale(1,1) translate(0,0)" writing-mode="lr" x="984.22" xml:space="preserve" y="834.2" zvalue="10879">05417</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1014,972.638) scale(1,1) translate(0,0)" writing-mode="lr" x="1014" xml:space="preserve" y="977.14" zvalue="10882">10kV李子坪线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1241.64,769.455) scale(1,1) translate(0,0)" writing-mode="lr" x="1241.64" xml:space="preserve" y="773.95" zvalue="10891">012</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="235" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1161.64,815.184) scale(1,1) translate(0,0)" writing-mode="lr" x="1161.64" xml:space="preserve" y="819.6799999999999" zvalue="10894">10kV分段</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" x="1553.6484375" xml:space="preserve" y="578.288353139704" zvalue="10897">10kVⅠ段母线电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1553.6484375" xml:space="preserve" y="594.288353139704" zvalue="10897">压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="237" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1576.91,676.273) scale(1,1) translate(0,0)" writing-mode="lr" x="1576.91" xml:space="preserve" y="680.77" zvalue="10900">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1633.64,676.273) scale(1,1) translate(0,0)" writing-mode="lr" x="1633.64" xml:space="preserve" y="680.77" zvalue="10902">0801</text>
  <line fill="none" id="293" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0" x2="367.9999999999995" y1="146.3741690846741" y2="146.3741690846741" zvalue="10965"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.999999999999773" x2="182.9999999999998" y1="158.5036764705917" y2="158.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.999999999999773" x2="182.9999999999998" y1="184.5036764705917" y2="184.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.999999999999773" x2="1.999999999999773" y1="158.5036764705917" y2="184.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="182.9999999999998" y1="158.5036764705917" y2="184.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="363.9999999999998" y1="158.5036764705917" y2="158.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="363.9999999999998" y1="184.5036764705917" y2="184.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="182.9999999999998" y1="158.5036764705917" y2="184.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.9999999999998" x2="363.9999999999998" y1="158.5036764705917" y2="184.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.999999999999773" x2="182.9999999999998" y1="184.5036764705917" y2="184.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.999999999999773" x2="182.9999999999998" y1="208.7536764705917" y2="208.7536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.999999999999773" x2="1.999999999999773" y1="184.5036764705917" y2="208.7536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="182.9999999999998" y1="184.5036764705917" y2="208.7536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="363.9999999999998" y1="184.5036764705917" y2="184.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="363.9999999999998" y1="208.7536764705917" y2="208.7536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="182.9999999999998" y1="184.5036764705917" y2="208.7536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.9999999999998" x2="363.9999999999998" y1="184.5036764705917" y2="208.7536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.999999999999773" x2="182.9999999999998" y1="208.7536764705917" y2="208.7536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.999999999999773" x2="182.9999999999998" y1="231.5036764705917" y2="231.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.999999999999773" x2="1.999999999999773" y1="208.7536764705917" y2="231.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="182.9999999999998" y1="208.7536764705917" y2="231.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="363.9999999999998" y1="208.7536764705917" y2="208.7536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="363.9999999999998" y1="231.5036764705917" y2="231.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="182.9999999999998" y1="208.7536764705917" y2="231.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.9999999999998" x2="363.9999999999998" y1="208.7536764705917" y2="231.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.999999999999773" x2="182.9999999999998" y1="231.5036764705917" y2="231.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.999999999999773" x2="182.9999999999998" y1="254.2536764705917" y2="254.2536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.999999999999773" x2="1.999999999999773" y1="231.5036764705917" y2="254.2536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="182.9999999999998" y1="231.5036764705917" y2="254.2536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="363.9999999999998" y1="231.5036764705917" y2="231.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="363.9999999999998" y1="254.2536764705917" y2="254.2536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="182.9999999999998" y1="231.5036764705917" y2="254.2536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.9999999999998" x2="363.9999999999998" y1="231.5036764705917" y2="254.2536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.999999999999773" x2="182.9999999999998" y1="254.2536764705917" y2="254.2536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.999999999999773" x2="182.9999999999998" y1="277.0036764705917" y2="277.0036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.999999999999773" x2="1.999999999999773" y1="254.2536764705917" y2="277.0036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="182.9999999999998" y1="254.2536764705917" y2="277.0036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="363.9999999999998" y1="254.2536764705917" y2="254.2536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="363.9999999999998" y1="277.0036764705917" y2="277.0036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.9999999999998" x2="182.9999999999998" y1="254.2536764705917" y2="277.0036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.9999999999998" x2="363.9999999999998" y1="254.2536764705917" y2="277.0036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="52.99999999999977" x2="98.77449999999976" y1="438.5036764705916" y2="438.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="52.99999999999977" x2="98.77449999999976" y1="476.7859764705917" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="52.99999999999977" x2="52.99999999999977" y1="438.5036764705916" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="98.77449999999976" y1="438.5036764705916" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="157.5808999999998" y1="438.5036764705916" y2="438.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="157.5808999999998" y1="476.7859764705917" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="98.77449999999976" y1="438.5036764705916" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="157.5808999999998" y1="438.5036764705916" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="216.3872999999998" y1="438.5036764705916" y2="438.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="216.3872999999998" y1="476.7859764705917" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="157.5808999999998" y1="438.5036764705916" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3872999999998" x2="216.3872999999998" y1="438.5036764705916" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3871999999998" x2="275.1935999999998" y1="438.5036764705916" y2="438.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3871999999998" x2="275.1935999999998" y1="476.7859764705917" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3871999999998" x2="216.3871999999998" y1="438.5036764705916" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="275.1935999999998" y1="438.5036764705916" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="333.9999999999998" y1="438.5036764705916" y2="438.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="333.9999999999998" y1="476.7859764705917" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="275.1935999999998" y1="438.5036764705916" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="333.9999999999998" x2="333.9999999999998" y1="438.5036764705916" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="52.99999999999977" x2="98.77449999999976" y1="476.7859764705917" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="52.99999999999977" x2="98.77449999999976" y1="501.4653764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="52.99999999999977" x2="52.99999999999977" y1="476.7859764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="98.77449999999976" y1="476.7859764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="157.5808999999998" y1="476.7859764705917" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="157.5808999999998" y1="501.4653764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="98.77449999999976" y1="476.7859764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="157.5808999999998" y1="476.7859764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="216.3872999999998" y1="476.7859764705917" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="216.3872999999998" y1="501.4653764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="157.5808999999998" y1="476.7859764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3872999999998" x2="216.3872999999998" y1="476.7859764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3871999999998" x2="275.1935999999998" y1="476.7859764705917" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3871999999998" x2="275.1935999999998" y1="501.4653764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3871999999998" x2="216.3871999999998" y1="476.7859764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="275.1935999999998" y1="476.7859764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="333.9999999999998" y1="476.7859764705917" y2="476.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="333.9999999999998" y1="501.4653764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="275.1935999999998" y1="476.7859764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="333.9999999999998" x2="333.9999999999998" y1="476.7859764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="52.99999999999977" x2="98.77449999999976" y1="501.4653764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="52.99999999999977" x2="98.77449999999976" y1="526.1447764705916" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="52.99999999999977" x2="52.99999999999977" y1="501.4653764705917" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="98.77449999999976" y1="501.4653764705917" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="157.5808999999998" y1="501.4653764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="157.5808999999998" y1="526.1447764705916" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="98.77449999999976" y1="501.4653764705917" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="157.5808999999998" y1="501.4653764705917" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="216.3872999999998" y1="501.4653764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="216.3872999999998" y1="526.1447764705916" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="157.5808999999998" y1="501.4653764705917" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3872999999998" x2="216.3872999999998" y1="501.4653764705917" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3871999999998" x2="275.1935999999998" y1="501.4653764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3871999999998" x2="275.1935999999998" y1="526.1447764705916" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3871999999998" x2="216.3871999999998" y1="501.4653764705917" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="275.1935999999998" y1="501.4653764705917" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="333.9999999999998" y1="501.4653764705917" y2="501.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="333.9999999999998" y1="526.1447764705916" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="275.1935999999998" y1="501.4653764705917" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="333.9999999999998" x2="333.9999999999998" y1="501.4653764705917" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="52.99999999999977" x2="98.77449999999976" y1="526.1447764705916" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="52.99999999999977" x2="98.77449999999976" y1="550.8241764705916" y2="550.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="52.99999999999977" x2="52.99999999999977" y1="526.1447764705916" y2="550.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="98.77449999999976" y1="526.1447764705916" y2="550.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="157.5808999999998" y1="526.1447764705916" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="157.5808999999998" y1="550.8241764705916" y2="550.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="98.77449999999976" y1="526.1447764705916" y2="550.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="157.5808999999998" y1="526.1447764705916" y2="550.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="216.3872999999998" y1="526.1447764705916" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="216.3872999999998" y1="550.8241764705916" y2="550.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="157.5808999999998" y1="526.1447764705916" y2="550.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3872999999998" x2="216.3872999999998" y1="526.1447764705916" y2="550.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3871999999998" x2="275.1935999999998" y1="526.1447764705916" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3871999999998" x2="275.1935999999998" y1="550.8241764705916" y2="550.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3871999999998" x2="216.3871999999998" y1="526.1447764705916" y2="550.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="275.1935999999998" y1="526.1447764705916" y2="550.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="333.9999999999998" y1="526.1447764705916" y2="526.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="333.9999999999998" y1="550.8241764705916" y2="550.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="275.1935999999998" y1="526.1447764705916" y2="550.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="333.9999999999998" x2="333.9999999999998" y1="526.1447764705916" y2="550.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="52.99999999999977" x2="98.77449999999976" y1="550.8242764705916" y2="550.8242764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="52.99999999999977" x2="98.77449999999976" y1="575.5036764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="52.99999999999977" x2="52.99999999999977" y1="550.8242764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="98.77449999999976" y1="550.8242764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="157.5808999999998" y1="550.8242764705916" y2="550.8242764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="157.5808999999998" y1="575.5036764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="98.77449999999976" y1="550.8242764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="157.5808999999998" y1="550.8242764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="216.3872999999998" y1="550.8242764705916" y2="550.8242764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="216.3872999999998" y1="575.5036764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="157.5808999999998" y1="550.8242764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3872999999998" x2="216.3872999999998" y1="550.8242764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3871999999998" x2="275.1935999999998" y1="550.8242764705916" y2="550.8242764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3871999999998" x2="275.1935999999998" y1="575.5036764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3871999999998" x2="216.3871999999998" y1="550.8242764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="275.1935999999998" y1="550.8242764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="333.9999999999998" y1="550.8242764705916" y2="550.8242764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="333.9999999999998" y1="575.5036764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="275.1935999999998" y1="550.8242764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="333.9999999999998" x2="333.9999999999998" y1="550.8242764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="52.99999999999977" x2="98.77449999999976" y1="575.5036764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="52.99999999999977" x2="98.77449999999976" y1="600.1830764705917" y2="600.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="52.99999999999977" x2="52.99999999999977" y1="575.5036764705916" y2="600.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="98.77449999999976" y1="575.5036764705916" y2="600.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="157.5808999999998" y1="575.5036764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="157.5808999999998" y1="600.1830764705917" y2="600.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="98.77449999999976" x2="98.77449999999976" y1="575.5036764705916" y2="600.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="157.5808999999998" y1="575.5036764705916" y2="600.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="216.3872999999998" y1="575.5036764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="216.3872999999998" y1="600.1830764705917" y2="600.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="157.5808999999998" x2="157.5808999999998" y1="575.5036764705916" y2="600.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3872999999998" x2="216.3872999999998" y1="575.5036764705916" y2="600.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3871999999998" x2="275.1935999999998" y1="575.5036764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3871999999998" x2="275.1935999999998" y1="600.1830764705917" y2="600.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.3871999999998" x2="216.3871999999998" y1="575.5036764705916" y2="600.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="275.1935999999998" y1="575.5036764705916" y2="600.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="333.9999999999998" y1="575.5036764705916" y2="575.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="333.9999999999998" y1="600.1830764705917" y2="600.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.1935999999998" x2="275.1935999999998" y1="575.5036764705916" y2="600.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="333.9999999999998" x2="333.9999999999998" y1="575.5036764705916" y2="600.1830764705917"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="289" stroke="rgb(255,255,255)" text-anchor="middle" x="125.53125" xml:space="preserve" y="455.8993055555555" zvalue="10969">35kV     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="289" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="125.53125" xml:space="preserve" y="472.8993055555555" zvalue="10969">Ⅰ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.399,313.345) scale(1,1) translate(0,0)" writing-mode="lr" x="199.4" xml:space="preserve" y="317.85" zvalue="10970">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,304.399,313.345) scale(1,1) translate(0,0)" writing-mode="lr" x="304.4" xml:space="preserve" y="317.85" zvalue="10971">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="286" stroke="rgb(255,255,255)" text-anchor="middle" x="244.9375" xml:space="preserve" y="454.3524305555555" zvalue="10972">10kV     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="286" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="244.9375" xml:space="preserve" y="471.3524305555555" zvalue="10972">Ⅰ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" x="304" xml:space="preserve" y="454.3524305555555" zvalue="10973">10kV      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="304" xml:space="preserve" y="471.3524305555555" zvalue="10973">Ⅱ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78,489.254) scale(1,1) translate(0,0)" writing-mode="lr" x="77.99999999999977" xml:space="preserve" y="493.7536764705916" zvalue="10974">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78,514.754) scale(1,1) translate(0,-1.11079e-13)" writing-mode="lr" x="77.99999999999977" xml:space="preserve" y="519.2536764705916" zvalue="10975">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78,537.754) scale(1,1) translate(0,0)" writing-mode="lr" x="77.99999999999977" xml:space="preserve" y="542.2536764705917" zvalue="10976">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="281" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78,560.754) scale(1,1) translate(0,6.06463e-14)" writing-mode="lr" x="77.99999999999977" xml:space="preserve" y="565.2536764705916" zvalue="10977">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="280" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78,587.754) scale(1,1) translate(0,0)" writing-mode="lr" x="77.99999999999977" xml:space="preserve" y="592.2536764705917" zvalue="10978">Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="278" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,40,172.504) scale(1,1) translate(0,0)" writing-mode="lr" x="40" xml:space="preserve" y="178" zvalue="10979">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="277" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,220,172.504) scale(1,1) translate(0,0)" writing-mode="lr" x="220" xml:space="preserve" y="178" zvalue="10980">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="276" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43.6875,196.754) scale(1,1) translate(0,0)" writing-mode="lr" x="43.69" xml:space="preserve" y="201.25" zvalue="10981">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="275" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.1875,244.504) scale(1,1) translate(0,0)" writing-mode="lr" x="47.19" xml:space="preserve" y="250" zvalue="10982">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="274" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.75,244.004) scale(1,1) translate(0,0)" writing-mode="lr" x="228.75" xml:space="preserve" y="249.5" zvalue="10983">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="272" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.75,267.004) scale(1,1) translate(0,0)" writing-mode="lr" x="228.75" xml:space="preserve" y="272.5" zvalue="10985">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="271" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.6875,220.754) scale(1,1) translate(0,0)" writing-mode="lr" x="44.69" xml:space="preserve" y="225.25" zvalue="10986">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="270" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.75,220.504) scale(1,1) translate(0,0)" writing-mode="lr" x="227.75" xml:space="preserve" y="225" zvalue="10987">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,296.235,387) scale(1,1) translate(0,-1.66533e-13)" writing-mode="lr" x="296.2350158691406" xml:space="preserve" y="391.5" zvalue="11086">小电流接地</text>
 </g>
 <g id="ButtonClass">
  <g href="全站公用20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="54.19" y="301.25" zvalue="11068"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="156.09" y="374.25" zvalue="11069"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="54.19" y="374.25" zvalue="11070"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="54.19" y="337.75" zvalue="11071"/></g>
 </g>
 <g id="StateClass">
  <g id="71">
   <use height="30" transform="rotate(0,306.812,123.464) scale(1.22222,1.03092) translate(-45.7841,-3.23939)" width="90" x="251.81" xlink:href="#State:全站检修_0" y="108" zvalue="11081"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549678505985" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,306.812,123.464) scale(1.22222,1.03092) translate(-45.7841,-3.23939)" width="90" x="251.81" y="108"/></g>
  <g id="897">
   <use height="30" transform="rotate(0,337.625,314.004) scale(0.708333,0.665547) translate(134.647,152.778)" width="30" x="327" xlink:href="#State:红绿圆(方形)_0" y="304.02" zvalue="11007"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374921367553" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,337.625,314.004) scale(0.708333,0.665547) translate(134.647,152.778)" width="30" x="327" y="304.02"/></g>
  <g id="1035">
   <use height="30" transform="rotate(0,242,314.004) scale(0.708333,0.665547) translate(95.2721,152.778)" width="30" x="231.37" xlink:href="#State:红绿圆(方形)_0" y="304.02" zvalue="11008"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562959882846213" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,242,314.004) scale(0.708333,0.665547) translate(95.2721,152.778)" width="30" x="231.37" y="304.02"/></g>
  <g id="1084">
   <use height="30" transform="rotate(0,297.235,387) scale(0.910937,0.8) translate(25.4982,93.75)" width="80" x="260.8" xlink:href="#State:间隔模板_0" y="375" zvalue="11085"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629499534868485" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,297.235,387) scale(0.910937,0.8) translate(25.4982,93.75)" width="80" x="260.8" y="375"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="646">
   <path class="kv35" d="M 787.73 365.66 L 1695.18 365.66" stroke-width="6" zvalue="10397"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674397913091" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674397913091"/></metadata>
  <path d="M 787.73 365.66 L 1695.18 365.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="645">
   <path class="kv10" d="M 513.16 719.56 L 1141.27 719.56" stroke-width="6" zvalue="10399"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674397782019" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674397782019"/></metadata>
  <path d="M 513.16 719.56 L 1141.27 719.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="521">
   <path class="kv10" d="M 1204.25 719.56 L 1632.18 719.56" stroke-width="6" zvalue="10567"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674397847555" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674397847555"/></metadata>
  <path d="M 1204.25 719.56 L 1632.18 719.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="643">
   <use class="kv35" height="30" transform="rotate(0,1549.79,206.379) scale(-1.25,-1.25) translate(-2785.88,-367.733)" width="30" x="1531.043768050567" xlink:href="#Accessory:避雷器PT带熔断器_0" y="187.629422301836" zvalue="10403"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453731155970" ObjectName="35kV母线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1549.79,206.379) scale(-1.25,-1.25) translate(-2785.88,-367.733)" width="30" x="1531.043768050567" y="187.629422301836"/></g>
  <g id="607">
   <use class="kv35" height="26" transform="rotate(90,996.306,142.537) scale(0.838049,0.927421) translate(191.563,10.2113)" width="12" x="991.2781809263649" xlink:href="#Accessory:避雷器1_0" y="130.4810223032669" zvalue="10455"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453730369538" ObjectName="35kV轩江线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,996.306,142.537) scale(0.838049,0.927421) translate(191.563,10.2113)" width="12" x="991.2781809263649" y="130.4810223032669"/></g>
  <g id="603">
   <use class="kv35" height="40" transform="rotate(90,1108.59,153.887) scale(1.12267,1.12267) translate(-119.292,-14.3613)" width="30" x="1091.75" xlink:href="#Accessory:带熔断器的线路PT1_0" y="131.4332298136646" zvalue="10460"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453734432770" ObjectName="35kV轩江线3519PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1108.59,153.887) scale(1.12267,1.12267) translate(-119.292,-14.3613)" width="30" x="1091.75" y="131.4332298136646"/></g>
  <g id="25">
   <use class="kv35" height="26" transform="rotate(90,1196.31,142.537) scale(0.838049,0.927421) translate(230.212,10.2113)" width="12" x="1191.278180926365" xlink:href="#Accessory:避雷器1_0" y="130.4810223032669" zvalue="10739"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453731745794" ObjectName="35kV江五线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1196.31,142.537) scale(0.838049,0.927421) translate(230.212,10.2113)" width="12" x="1191.278180926365" y="130.4810223032669"/></g>
  <g id="49">
   <use class="kv10" height="35" transform="rotate(180,590.591,630.773) scale(1.43636,1.43636) translate(-173.965,-183.991)" width="25" x="572.6363636363637" xlink:href="#Accessory:PT232_0" y="605.6363636363635" zvalue="10813"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453735612418" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(180,590.591,630.773) scale(1.43636,1.43636) translate(-173.965,-183.991)" width="25" x="572.6363636363637" y="605.6363636363635"/></g>
  <g id="51">
   <use class="kv10" height="26" transform="rotate(180,640.818,624.167) scale(1.28788,1.28788) translate(-141.514,-135.777)" width="12" x="633.090909090909" xlink:href="#Accessory:避雷器_0" y="607.4242424242425" zvalue="10814"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453735677954" ObjectName="10kVⅡ段母线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,640.818,624.167) scale(1.28788,1.28788) translate(-141.514,-135.777)" width="12" x="633.090909090909" y="607.4242424242425"/></g>
  <g id="125">
   <use class="kv10" height="26" transform="rotate(0,699,903.258) scale(1.28788,1.28788) translate(-154.52,-198.162)" width="12" x="691.2727272727273" xlink:href="#Accessory:避雷器_0" y="886.5151515151516" zvalue="10854"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453731024898" ObjectName="10kV江东线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,699,903.258) scale(1.28788,1.28788) translate(-154.52,-198.162)" width="12" x="691.2727272727273" y="886.5151515151516"/></g>
  <g id="133">
   <use class="kv10" height="26" transform="rotate(0,839,903.258) scale(1.28788,1.28788) translate(-185.814,-198.162)" width="12" x="831.2727272727273" xlink:href="#Accessory:避雷器_0" y="886.5151515151516" zvalue="10869"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453733974018" ObjectName="10kV仙人洞线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,839,903.258) scale(1.28788,1.28788) translate(-185.814,-198.162)" width="12" x="831.2727272727273" y="886.5151515151516"/></g>
  <g id="181">
   <use class="kv10" height="26" transform="rotate(0,979,903.258) scale(1.28788,1.28788) translate(-217.108,-198.162)" width="12" x="971.2727272727273" xlink:href="#Accessory:避雷器_0" y="886.5151515151516" zvalue="10884"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453733515266" ObjectName="10kV李子坪线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,979,903.258) scale(1.28788,1.28788) translate(-217.108,-198.162)" width="12" x="971.2727272727273" y="886.5151515151516"/></g>
  <g id="254">
   <use class="kv10" height="35" transform="rotate(180,1554.59,630.773) scale(1.43636,1.43636) translate(-466.826,-183.991)" width="25" x="1536.636363636364" xlink:href="#Accessory:PT232_0" y="605.6363636363635" zvalue="10896"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453737381890" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(180,1554.59,630.773) scale(1.43636,1.43636) translate(-466.826,-183.991)" width="25" x="1536.636363636364" y="605.6363636363635"/></g>
  <g id="253">
   <use class="kv10" height="26" transform="rotate(180,1604.82,624.167) scale(1.28788,1.28788) translate(-356.997,-135.777)" width="12" x="1597.090909090909" xlink:href="#Accessory:避雷器_0" y="607.4242424242425" zvalue="10898"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453737316354" ObjectName="10kVⅠ段母线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1604.82,624.167) scale(1.28788,1.28788) translate(-356.997,-135.777)" width="12" x="1597.090909090909" y="607.4242424242425"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="642">
   <use class="kv35" height="30" transform="rotate(180,947.386,210.99) scale(1.53571,-1.53571) translate(-322.984,-340.344)" width="28" x="925.886363371627" xlink:href="#EnergyConsumer:站用变DY接地_0" y="187.954544587569" zvalue="10405"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453731221506" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,947.386,210.99) scale(1.53571,-1.53571) translate(-322.984,-340.344)" width="28" x="925.886363371627" y="187.954544587569"/></g>
  <g id="122">
   <use class="kv10" height="30" transform="rotate(180,730.364,944.182) scale(0.909091,0.909091) translate(72.4909,93.0545)" width="12" x="724.909090909091" xlink:href="#EnergyConsumer:负荷_0" y="930.5454545454547" zvalue="10851"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453730959362" ObjectName="10kV江东线"/>
   <cge:TPSR_Ref TObjectID="6192453730959362"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,730.364,944.182) scale(0.909091,0.909091) translate(72.4909,93.0545)" width="12" x="724.909090909091" y="930.5454545454547"/></g>
  <g id="135">
   <use class="kv10" height="30" transform="rotate(180,870.364,940.182) scale(0.909091,0.909091) translate(86.4909,92.6545)" width="12" x="864.909090909091" xlink:href="#EnergyConsumer:负荷_0" y="926.5454545454547" zvalue="10866"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453733908482" ObjectName="10kV仙人洞线"/>
   <cge:TPSR_Ref TObjectID="6192453733908482"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,870.364,940.182) scale(0.909091,0.909091) translate(86.4909,92.6545)" width="12" x="864.909090909091" y="926.5454545454547"/></g>
  <g id="186">
   <use class="kv10" height="30" transform="rotate(180,1010.36,940.182) scale(0.909091,0.909091) translate(100.491,92.6545)" width="12" x="1004.909090909091" xlink:href="#EnergyConsumer:负荷_0" y="926.5454545454547" zvalue="10881"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453733449730" ObjectName="10kV李子坪线"/>
   <cge:TPSR_Ref TObjectID="6192453733449730"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1010.36,940.182) scale(0.909091,0.909091) translate(100.491,92.6545)" width="12" x="1004.909090909091" y="926.5454545454547"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="631">
   <use class="kv35" height="30" transform="rotate(90,985.545,163.649) scale(1,1) translate(0,0)" width="15" x="978.0454545454546" xlink:href="#Disconnector:令克_0" y="148.6493497823742" zvalue="10424"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453734498306" ObjectName="35kV1号站用变隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453734498306"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,985.545,163.649) scale(1,1) translate(0,0)" width="15" x="978.0454545454546" y="148.6493497823742"/></g>
  <g id="615">
   <use class="kv35" height="30" transform="rotate(0,1030.71,210.347) scale(-0.947693,0.6712) translate(-2118.7,98.1103)" width="15" x="1023.603492892157" xlink:href="#Disconnector:刀闸_0" y="200.2789959504901" zvalue="10444"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453730631682" ObjectName="35kV轩江线3516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453730631682"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1030.71,210.347) scale(-0.947693,0.6712) translate(-2118.7,98.1103)" width="15" x="1023.603492892157" y="200.2789959504901"/></g>
  <g id="612">
   <use class="kv35" height="30" transform="rotate(180,1030.89,333.616) scale(0.947693,-0.6712) translate(56.5069,-835.591)" width="15" x="1023.784133425995" xlink:href="#Disconnector:刀闸_0" y="323.5477337373497" zvalue="10449"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453730435074" ObjectName="35kV轩江线3511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453730435074"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1030.89,333.616) scale(0.947693,-0.6712) translate(56.5069,-835.591)" width="15" x="1023.784133425995" y="323.5477337373497"/></g>
  <g id="602">
   <use class="kv35" height="30" transform="rotate(90,1072.02,154.296) scale(0.947693,0.6712) translate(58.777,70.6527)" width="15" x="1064.914042245145" xlink:href="#Disconnector:刀闸_0" y="144.2280599011806" zvalue="10461"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453730107394" ObjectName="35kV轩江线3519隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453730107394"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1072.02,154.296) scale(0.947693,0.6712) translate(58.777,70.6527)" width="15" x="1064.914042245145" y="144.2280599011806"/></g>
  <g id="574">
   <use class="kv35" height="30" transform="rotate(0,896.016,401.229) scale(0.947693,-0.6712) translate(49.0625,-1003.94)" width="15" x="888.9085819644126" xlink:href="#Disconnector:刀闸_0" y="391.1614816563945" zvalue="10501"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453734367234" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453734367234"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,896.016,401.229) scale(0.947693,-0.6712) translate(49.0625,-1003.94)" width="15" x="888.9085819644126" y="391.1614816563945"/></g>
  <g id="516">
   <use class="kv10" height="26" transform="rotate(0,1100.25,771.386) scale(1.42857,1.42857) translate(-327.075,-225.844)" width="14" x="1090.25" xlink:href="#Disconnector:联体手车刀闸_0" y="752.8149350649351" zvalue="10574"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453728731138" ObjectName="10kV分段0122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453728731138"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1100.25,771.386) scale(1.42857,1.42857) translate(-327.075,-225.844)" width="14" x="1090.25" y="752.8149350649351"/></g>
  <g id="503">
   <use class="kv35" height="30" transform="rotate(180,1552.03,304.222) scale(0.947693,-0.6712) translate(85.2705,-762.404)" width="15" x="1544.920497062359" xlink:href="#Disconnector:刀闸_0" y="294.1537943434105" zvalue="10590"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453728534530" ObjectName="35kV母线PT3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453728534530"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1552.03,304.222) scale(0.947693,-0.6712) translate(85.2705,-762.404)" width="15" x="1544.920497062359" y="294.1537943434105"/></g>
  <g id="33">
   <use class="kv35" height="30" transform="rotate(0,1230.71,210.347) scale(-0.947693,0.6712) translate(-2529.74,98.1103)" width="15" x="1223.603492892157" xlink:href="#Disconnector:刀闸_0" y="200.2789959504901" zvalue="10728"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453735088130" ObjectName="35kV江五线3526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453735088130"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1230.71,210.347) scale(-0.947693,0.6712) translate(-2529.74,98.1103)" width="15" x="1223.603492892157" y="200.2789959504901"/></g>
  <g id="30">
   <use class="kv35" height="30" transform="rotate(180,1230.89,333.616) scale(0.947693,-0.6712) translate(67.5457,-835.591)" width="15" x="1223.784133425996" xlink:href="#Disconnector:刀闸_0" y="323.5477337373497" zvalue="10733"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453734891522" ObjectName="35kV江五线3521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453734891522"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1230.89,333.616) scale(0.947693,-0.6712) translate(67.5457,-835.591)" width="15" x="1223.784133425996" y="323.5477337373497"/></g>
  <g id="81">
   <use class="kv35" height="30" transform="rotate(0,1408.02,401.229) scale(0.947693,-0.6712) translate(77.3219,-1003.94)" width="15" x="1400.908581964413" xlink:href="#Disconnector:刀闸_0" y="391.1614816563945" zvalue="10782"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453735350274" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453735350274"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1408.02,401.229) scale(0.947693,-0.6712) translate(77.3219,-1003.94)" width="15" x="1400.908581964413" y="391.1614816563945"/></g>
  <g id="36">
   <use class="kv10" height="30" transform="rotate(0,566.198,838.502) scale(0.947693,-0.6712) translate(30.8585,-2092.69)" width="15" x="559.0904001462309" xlink:href="#Disconnector:刀闸_0" y="828.4342089291217" zvalue="10804"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453735415810" ObjectName="10kV2号电容器0516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453735415810"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,566.198,838.502) scale(0.947693,-0.6712) translate(30.8585,-2092.69)" width="15" x="559.0904001462309" y="828.4342089291217"/></g>
  <g id="52">
   <use class="kv10" height="26" transform="rotate(180,590.364,679.091) scale(0.909091,0.909091) translate(58.4909,66.7273)" width="12" x="584.9090909090909" xlink:href="#Disconnector:单手车刀闸1212_0" y="667.2727272727273" zvalue="10815"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453735743490" ObjectName="10kVⅡ段母线电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453735743490"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,590.364,679.091) scale(0.909091,0.909091) translate(58.4909,66.7273)" width="12" x="584.9090909090909" y="667.2727272727273"/></g>
  <g id="54">
   <use class="kv10" height="26" transform="rotate(180,642.182,679.091) scale(0.909091,0.909091) translate(63.6727,66.7273)" width="12" x="636.7272727272725" xlink:href="#Disconnector:单手车刀闸1212_0" y="667.2727272727273" zvalue="10817"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453735809026" ObjectName="10kVⅡ段母线电压互感器0802隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453735809026"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,642.182,679.091) scale(0.909091,0.909091) translate(63.6727,66.7273)" width="12" x="636.7272727272725" y="667.2727272727273"/></g>
  <g id="116">
   <use class="kv10" height="30" transform="rotate(0,730.198,838.502) scale(0.947693,-0.6712) translate(39.9103,-2092.69)" width="15" x="723.0904001462308" xlink:href="#Disconnector:刀闸_0" y="828.4342089291217" zvalue="10834"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453736333314" ObjectName="10kV江东线0526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453736333314"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,730.198,838.502) scale(0.947693,-0.6712) translate(39.9103,-2092.69)" width="15" x="723.0904001462308" y="828.4342089291217"/></g>
  <g id="149">
   <use class="kv10" height="30" transform="rotate(0,870.198,838.502) scale(0.947693,-0.6712) translate(47.6375,-2092.69)" width="15" x="863.0904001462308" xlink:href="#Disconnector:刀闸_0" y="828.4342089291217" zvalue="10860"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453736792066" ObjectName="10kV仙人洞线0536隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453736792066"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,870.198,838.502) scale(0.947693,-0.6712) translate(47.6375,-2092.69)" width="15" x="863.0904001462308" y="828.4342089291217"/></g>
  <g id="202">
   <use class="kv10" height="30" transform="rotate(0,1010.2,838.502) scale(0.947693,-0.6712) translate(55.3647,-2092.69)" width="15" x="1003.090400146231" xlink:href="#Disconnector:刀闸_0" y="828.4342089291217" zvalue="10875"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453737119746" ObjectName="10kV李子坪线0546隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453737119746"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1010.2,838.502) scale(0.947693,-0.6712) translate(55.3647,-2092.69)" width="15" x="1003.090400146231" y="828.4342089291217"/></g>
  <g id="251">
   <use class="kv10" height="26" transform="rotate(180,1554.36,679.091) scale(0.909091,0.909091) translate(154.891,66.7273)" width="12" x="1548.909090909091" xlink:href="#Disconnector:单手车刀闸1212_0" y="667.2727272727273" zvalue="10899"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453737250818" ObjectName="10kVⅠ段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453737250818"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1554.36,679.091) scale(0.909091,0.909091) translate(154.891,66.7273)" width="12" x="1548.909090909091" y="667.2727272727273"/></g>
  <g id="250">
   <use class="kv10" height="26" transform="rotate(180,1606.18,679.091) scale(0.909091,0.909091) translate(160.073,66.7273)" width="12" x="1600.727272727273" xlink:href="#Disconnector:单手车刀闸1212_0" y="667.2727272727273" zvalue="10901"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453737185282" ObjectName="10kVⅠ段母线电压互感器0801隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453737185282"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1606.18,679.091) scale(0.909091,0.909091) translate(160.073,66.7273)" width="12" x="1600.727272727273" y="667.2727272727273"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="617">
   <use class="kv35" height="30" transform="rotate(0,1030.63,120.603) scale(1.98323,0.522926) translate(-507.515,102.872)" width="7" x="1023.686704179698" xlink:href="#ACLineSegment:线路_0" y="112.7591931680256" zvalue="10440"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249320652804" ObjectName="35kV轩江线"/>
   <cge:TPSR_Ref TObjectID="8444249320652804_5066549678505985"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1030.63,120.603) scale(1.98323,0.522926) translate(-507.515,102.872)" width="7" x="1023.686704179698" y="112.7591931680256"/></g>
  <g id="35">
   <use class="kv35" height="30" transform="rotate(0,1230.63,120.603) scale(1.98323,0.522926) translate(-606.669,102.872)" width="7" x="1223.686704179698" xlink:href="#ACLineSegment:线路_0" y="112.7591931680256" zvalue="10724"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249321373700" ObjectName="35kV江五线"/>
   <cge:TPSR_Ref TObjectID="8444249321373700_5066549678505985"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1230.63,120.603) scale(1.98323,0.522926) translate(-606.669,102.872)" width="7" x="1223.686704179698" y="112.7591931680256"/></g>
 </g>
 <g id="BreakerClass">
  <g id="616">
   <use class="kv35" height="20" transform="rotate(0,1030.71,275.779) scale(1.5542,1.35421) translate(-364.76,-68.5914)" width="10" x="1022.934034534565" xlink:href="#Breaker:开关_0" y="262.2371790820895" zvalue="10441"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925085429764" ObjectName="35kV轩江线351断路器"/>
   <cge:TPSR_Ref TObjectID="6473925085429764"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1030.71,275.779) scale(1.5542,1.35421) translate(-364.76,-68.5914)" width="10" x="1022.934034534565" y="262.2371790820895"/></g>
  <g id="575">
   <use class="kv35" height="20" transform="rotate(180,896.016,475.066) scale(1.5542,1.35421) translate(-316.732,-120.717)" width="10" x="888.2452821514723" xlink:href="#Breaker:开关_0" y="461.5238390693762" zvalue="10499"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925085954052" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925085954052"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,896.016,475.066) scale(1.5542,1.35421) translate(-316.732,-120.717)" width="10" x="888.2452821514723" y="461.5238390693762"/></g>
  <g id="34">
   <use class="kv35" height="20" transform="rotate(0,1230.71,275.779) scale(1.5542,1.35421) translate(-436.076,-68.5914)" width="10" x="1222.934034534565" xlink:href="#Breaker:开关_0" y="262.2371790820895" zvalue="10725"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925086019588" ObjectName="35kV江五线352断路器"/>
   <cge:TPSR_Ref TObjectID="6473925086019588"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1230.71,275.779) scale(1.5542,1.35421) translate(-436.076,-68.5914)" width="10" x="1222.934034534565" y="262.2371790820895"/></g>
  <g id="38">
   <use class="kv10" height="20" transform="rotate(0,896.053,641.364) scale(2.31818,2.31818) translate(-502.93,-351.515)" width="10" x="884.4624429761119" xlink:href="#Breaker:小车断路器_0" y="618.1818181818182" zvalue="10755"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925086085124" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473925086085124"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,896.053,641.364) scale(2.31818,2.31818) translate(-502.93,-351.515)" width="10" x="884.4624429761119" y="618.1818181818182"/></g>
  <g id="82">
   <use class="kv35" height="20" transform="rotate(180,1408.02,475.066) scale(1.5542,1.35421) translate(-499.302,-120.717)" width="10" x="1400.245282151472" xlink:href="#Breaker:开关_0" y="461.5238390693762" zvalue="10780"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925086216196" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925086216196"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1408.02,475.066) scale(1.5542,1.35421) translate(-499.302,-120.717)" width="10" x="1400.245282151472" y="461.5238390693762"/></g>
  <g id="70">
   <use class="kv10" height="20" transform="rotate(0,1408.05,641.364) scale(2.31818,2.31818) translate(-794.067,-351.515)" width="10" x="1396.462442976112" xlink:href="#Breaker:小车断路器_0" y="618.1818181818182" zvalue="10792"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925086150660" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925086150660"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1408.05,641.364) scale(2.31818,2.31818) translate(-794.067,-351.515)" width="10" x="1396.462442976112" y="618.1818181818182"/></g>
  <g id="8">
   <use class="kv10" height="20" transform="rotate(0,566.235,765.909) scale(2.31818,2.31818) translate(-315.386,-422.335)" width="10" x="554.6442611579304" xlink:href="#Breaker:小车断路器_0" y="742.7272727272727" zvalue="10797"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925086281732" ObjectName="10kV2号电容器051断路器"/>
   <cge:TPSR_Ref TObjectID="6473925086281732"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,566.235,765.909) scale(2.31818,2.31818) translate(-315.386,-422.335)" width="10" x="554.6442611579304" y="742.7272727272727"/></g>
  <g id="119">
   <use class="kv10" height="20" transform="rotate(0,730.235,765.909) scale(2.31818,2.31818) translate(-408.641,-422.335)" width="10" x="718.6442611579303" xlink:href="#Breaker:小车断路器_0" y="742.7272727272727" zvalue="10831"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925086347268" ObjectName="10kV江东线052断路器"/>
   <cge:TPSR_Ref TObjectID="6473925086347268"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,730.235,765.909) scale(2.31818,2.31818) translate(-408.641,-422.335)" width="10" x="718.6442611579303" y="742.7272727272727"/></g>
  <g id="155">
   <use class="kv10" height="20" transform="rotate(0,870.235,765.909) scale(2.31818,2.31818) translate(-488.249,-422.335)" width="10" x="858.6442611579303" xlink:href="#Breaker:小车断路器_0" y="742.7272727272727" zvalue="10857"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925086412804" ObjectName="10kV仙人洞线053断路器"/>
   <cge:TPSR_Ref TObjectID="6473925086412804"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,870.235,765.909) scale(2.31818,2.31818) translate(-488.249,-422.335)" width="10" x="858.6442611579303" y="742.7272727272727"/></g>
  <g id="205">
   <use class="kv10" height="20" transform="rotate(0,1010.24,765.909) scale(2.31818,2.31818) translate(-567.857,-422.335)" width="10" x="998.6442611579303" xlink:href="#Breaker:小车断路器_0" y="742.7272727272727" zvalue="10872"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925086478340" ObjectName="10kV李子坪线054断路器"/>
   <cge:TPSR_Ref TObjectID="6473925086478340"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1010.24,765.909) scale(2.31818,2.31818) translate(-567.857,-422.335)" width="10" x="998.6442611579303" y="742.7272727272727"/></g>
  <g id="229">
   <use class="kv10" height="20" transform="rotate(0,1215.69,772.273) scale(2.31818,2.31818) translate(-684.684,-425.954)" width="10" x="1204.098806612476" xlink:href="#Breaker:母联小车开关_0" y="749.0909090909091" zvalue="10890"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925086543876" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473925086543876"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1215.69,772.273) scale(2.31818,2.31818) translate(-684.684,-425.954)" width="10" x="1204.098806612476" y="749.0909090909091"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="614">
   <use class="kv35" height="20" transform="rotate(90,1060.5,175.074) scale(1.24619,-1.0068) translate(-208.273,-348.897)" width="10" x="1054.268227141073" xlink:href="#GroundDisconnector:地刀_0" y="165.0057110429577" zvalue="10446"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453730566146" ObjectName="35kV轩江线35167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453730566146"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1060.5,175.074) scale(1.24619,-1.0068) translate(-208.273,-348.897)" width="10" x="1054.268227141073" y="165.0057110429577"/></g>
  <g id="605">
   <use class="kv35" height="20" transform="rotate(90,1060.5,303.074) scale(1.24619,-1.0068) translate(-208.273,-604.032)" width="10" x="1054.268227141073" xlink:href="#GroundDisconnector:地刀_0" y="293.0057110429577" zvalue="10457"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453730304002" ObjectName="35kV轩江线35117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453730304002"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1060.5,303.074) scale(1.24619,-1.0068) translate(-208.273,-604.032)" width="10" x="1054.268227141073" y="293.0057110429577"/></g>
  <g id="581">
   <use class="kv35" height="20" transform="rotate(90,1597.04,267.074) scale(1.24619,-1.0068) translate(-314.268,-532.275)" width="10" x="1590.813681686528" xlink:href="#GroundDisconnector:地刀_0" y="257.0057110429577" zvalue="10490"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453730041858" ObjectName="35kV母线PT39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453730041858"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1597.04,267.074) scale(1.24619,-1.0068) translate(-314.268,-532.275)" width="10" x="1590.813681686528" y="257.0057110429577"/></g>
  <g id="579">
   <use class="kv35" height="20" transform="rotate(90,1597.04,335.074) scale(1.24619,-1.0068) translate(-314.268,-667.816)" width="10" x="1590.813681686528" xlink:href="#GroundDisconnector:地刀_0" y="325.0057110429577" zvalue="10493"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453729910786" ObjectName="35kV母线PT39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453729910786"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1597.04,335.074) scale(1.24619,-1.0068) translate(-314.268,-667.816)" width="10" x="1590.813681686528" y="325.0057110429577"/></g>
  <g id="571">
   <use class="kv35" height="20" transform="rotate(90,942.837,429.541) scale(1.24619,-1.0068) translate(-185.028,-856.113)" width="10" x="936.6058894787358" xlink:href="#GroundDisconnector:地刀_0" y="419.4732435104904" zvalue="10505"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453734301698" ObjectName="#2主变35kV侧30217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453734301698"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,942.837,429.541) scale(1.24619,-1.0068) translate(-185.028,-856.113)" width="10" x="936.6058894787358" y="419.4732435104904"/></g>
  <g id="509">
   <use class="kv35" height="20" transform="rotate(90,1060.5,235.074) scale(1.24619,-1.0068) translate(-208.273,-468.492)" width="10" x="1054.268227141073" xlink:href="#GroundDisconnector:地刀_0" y="225.0057110429577" zvalue="10583"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453728665602" ObjectName="35kV轩江线35160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453728665602"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1060.5,235.074) scale(1.24619,-1.0068) translate(-208.273,-468.492)" width="10" x="1054.268227141073" y="225.0057110429577"/></g>
  <g id="32">
   <use class="kv35" height="20" transform="rotate(90,1260.5,175.074) scale(1.24619,-1.0068) translate(-247.783,-348.897)" width="10" x="1254.268227141073" xlink:href="#GroundDisconnector:地刀_0" y="165.0057110429577" zvalue="10730"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453735022594" ObjectName="35kV江五线35267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453735022594"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1260.5,175.074) scale(1.24619,-1.0068) translate(-247.783,-348.897)" width="10" x="1254.268227141073" y="165.0057110429577"/></g>
  <g id="23">
   <use class="kv35" height="20" transform="rotate(90,1260.5,303.074) scale(1.24619,-1.0068) translate(-247.783,-604.032)" width="10" x="1254.268227141073" xlink:href="#GroundDisconnector:地刀_0" y="293.0057110429577" zvalue="10741"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453734760450" ObjectName="35kV江五线35217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453734760450"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1260.5,303.074) scale(1.24619,-1.0068) translate(-247.783,-604.032)" width="10" x="1254.268227141073" y="293.0057110429577"/></g>
  <g id="17">
   <use class="kv35" height="20" transform="rotate(90,1260.5,235.074) scale(1.24619,-1.0068) translate(-247.783,-468.492)" width="10" x="1254.268227141073" xlink:href="#GroundDisconnector:地刀_0" y="225.0057110429577" zvalue="10749"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453734629378" ObjectName="35kV江五线35260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453734629378"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1260.5,235.074) scale(1.24619,-1.0068) translate(-247.783,-468.492)" width="10" x="1254.268227141073" y="225.0057110429577"/></g>
  <g id="78">
   <use class="kv35" height="20" transform="rotate(90,1453.56,430.45) scale(1.24619,-1.0068) translate(-285.924,-857.925)" width="10" x="1447.333162206009" xlink:href="#GroundDisconnector:地刀_0" y="420.3823344195812" zvalue="10786"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453735284738" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453735284738"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1453.56,430.45) scale(1.24619,-1.0068) translate(-285.924,-857.925)" width="10" x="1447.333162206009" y="420.3823344195812"/></g>
  <g id="47">
   <use class="kv10" height="20" transform="rotate(90,614.837,801.996) scale(1.24619,-1.0068) translate(-120.231,-1598.51)" width="10" x="608.6058894787359" xlink:href="#GroundDisconnector:地刀_0" y="791.9277889650359" zvalue="10810"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453735546882" ObjectName="10kV2号电容器05160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453735546882"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,614.837,801.996) scale(1.24619,-1.0068) translate(-120.231,-1598.51)" width="10" x="608.6058894787359" y="791.9277889650359"/></g>
  <g id="60">
   <use class="kv10" height="20" transform="rotate(90,616.655,872.269) scale(1.24619,-1.0068) translate(-120.591,-1738.58)" width="10" x="610.4240712969176" xlink:href="#GroundDisconnector:地刀_0" y="862.2005162377632" zvalue="10823"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453735940098" ObjectName="10kV2号电容器05167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453735940098"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,616.655,872.269) scale(1.24619,-1.0068) translate(-120.591,-1738.58)" width="10" x="610.4240712969176" y="862.2005162377632"/></g>
  <g id="90">
   <use class="kv10" height="29" transform="rotate(0,505.799,830.017) scale(1.36364,1.36364) translate(-131.061,-216.065)" width="21" x="491.4805001419858" xlink:href="#GroundDisconnector:地刀-带电显示_0" y="810.2439960111042" zvalue="10827"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453736136706" ObjectName="10kV2号电容器05117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453736136706"/></metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,505.799,830.017) scale(1.36364,1.36364) translate(-131.061,-216.065)" width="21" x="491.4805001419858" y="810.2439960111042"/></g>
  <g id="105">
   <use class="kv10" height="29" transform="rotate(0,668.084,830.017) scale(1.36364,1.36364) translate(-174.338,-216.065)" width="21" x="653.7662144277002" xlink:href="#GroundDisconnector:地刀-带电显示_0" y="810.2439960111042" zvalue="10847"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453736267778" ObjectName="10kV江东线05217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453736267778"/></metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,668.084,830.017) scale(1.36364,1.36364) translate(-174.338,-216.065)" width="21" x="653.7662144277002" y="810.2439960111042"/></g>
  <g id="147">
   <use class="kv10" height="29" transform="rotate(0,808.084,830.017) scale(1.36364,1.36364) translate(-211.671,-216.065)" width="21" x="793.7662144277002" xlink:href="#GroundDisconnector:地刀-带电显示_0" y="810.2439960111042" zvalue="10863"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453736726530" ObjectName="10kV仙人洞线05317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453736726530"/></metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,808.084,830.017) scale(1.36364,1.36364) translate(-211.671,-216.065)" width="21" x="793.7662144277002" y="810.2439960111042"/></g>
  <g id="195">
   <use class="kv10" height="29" transform="rotate(0,948.084,830.017) scale(1.36364,1.36364) translate(-249.004,-216.065)" width="21" x="933.7662144277002" xlink:href="#GroundDisconnector:地刀-带电显示_0" y="810.2439960111042" zvalue="10878"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453737054210" ObjectName="10kV李子坪线05417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453737054210"/></metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,948.084,830.017) scale(1.36364,1.36364) translate(-249.004,-216.065)" width="21" x="933.7662144277002" y="810.2439960111042"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="613">
   <path class="kv35" d="M 1030.65 262.82 L 1030.65 220.24" stroke-width="1" zvalue="10448"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="616@0" LinkObjectIDznd="615@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1030.65 262.82 L 1030.65 220.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="611">
   <path class="kv35" d="M 1030.63 200.61 L 1030.63 128.37" stroke-width="1" zvalue="10451"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="615@0" LinkObjectIDznd="617@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1030.63 200.61 L 1030.63 128.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="610">
   <path class="kv35" d="M 1007.78 142.57 L 1030.63 142.57" stroke-width="1" zvalue="10452"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="607@0" LinkObjectIDznd="611" MaxPinNum="2"/>
   </metadata>
  <path d="M 1007.78 142.57 L 1030.63 142.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="609">
   <path class="kv35" d="M 1030.81 288.71 L 1030.81 323.88" stroke-width="1" zvalue="10453"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="616@1" LinkObjectIDznd="612@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1030.81 288.71 L 1030.81 323.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="608">
   <path class="kv35" d="M 1050.68 175.14 L 1030.63 175.11" stroke-width="1" zvalue="10454"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="614@0" LinkObjectIDznd="611" MaxPinNum="2"/>
   </metadata>
  <path d="M 1050.68 175.14 L 1030.63 175.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="606">
   <path class="kv35" d="M 1030.83 343.51 L 1030.83 365.66" stroke-width="1" zvalue="10456"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="612@1" LinkObjectIDznd="646@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1030.83 343.51 L 1030.83 365.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="604">
   <path class="kv35" d="M 1050.68 303.14 L 1030.81 303.11" stroke-width="1" zvalue="10459"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="605@0" LinkObjectIDznd="609" MaxPinNum="2"/>
   </metadata>
  <path d="M 1050.68 303.14 L 1030.81 303.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="601">
   <path class="kv35" d="M 1081.76 154.38 L 1087.82 154.38" stroke-width="1" zvalue="10463"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="602@0" LinkObjectIDznd="603@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1081.76 154.38 L 1087.82 154.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="600">
   <path class="kv35" d="M 1062.13 154.35 L 1030.63 154.35" stroke-width="1" zvalue="10464"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="602@1" LinkObjectIDznd="611" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.13 154.35 L 1030.63 154.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="580">
   <path class="kv35" d="M 1587.23 267.14 L 1551.21 267.14" stroke-width="1" zvalue="10492"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="581@0" LinkObjectIDznd="502" MaxPinNum="2"/>
   </metadata>
  <path d="M 1587.23 267.14 L 1551.21 267.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="578">
   <path class="kv35" d="M 1587.23 335.14 L 1551.97 335.14" stroke-width="1" zvalue="10495"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="579@0" LinkObjectIDznd="501" MaxPinNum="2"/>
   </metadata>
  <path d="M 1587.23 335.14 L 1551.97 335.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="573">
   <path class="kv35" d="M 896.07 391.33 L 896.07 365.66" stroke-width="1" zvalue="10503"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="574@1" LinkObjectIDznd="646@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.07 391.33 L 896.07 365.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="572">
   <path class="kv35" d="M 895.91 462.13 L 895.91 410.96" stroke-width="1" zvalue="10504"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="575@1" LinkObjectIDznd="574@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 895.91 462.13 L 895.91 410.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="570">
   <path class="kv35" d="M 933.02 429.6 L 895.91 429.6" stroke-width="1" zvalue="10507"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="571@0" LinkObjectIDznd="572" MaxPinNum="2"/>
   </metadata>
  <path d="M 933.02 429.6 L 895.91 429.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="511">
   <path class="kv10" d="M 1100.23 755.24 L 1100.23 719.56" stroke-width="1" zvalue="10581"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="516@0" LinkObjectIDznd="645@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100.23 755.24 L 1100.23 719.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="508">
   <path class="kv35" d="M 1050.68 235.14 L 1030.65 235.11" stroke-width="1" zvalue="10585"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="509@0" LinkObjectIDznd="613" MaxPinNum="2"/>
   </metadata>
  <path d="M 1050.68 235.14 L 1030.65 235.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="502">
   <path class="kv35" d="M 1551.21 223.8 L 1551.21 294.49" stroke-width="1" zvalue="10592"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="643@0" LinkObjectIDznd="503@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1551.21 223.8 L 1551.21 294.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="501">
   <path class="kv35" d="M 1551.97 314.12 L 1551.97 365.66" stroke-width="1" zvalue="10593"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="503@1" LinkObjectIDznd="646@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1551.97 314.12 L 1551.97 365.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv35" d="M 998.8 163.73 L 1030.63 163.73" stroke-width="1" zvalue="10721"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="631@0" LinkObjectIDznd="611" MaxPinNum="2"/>
   </metadata>
  <path d="M 998.8 163.73 L 1030.63 163.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv35" d="M 973.3 163.57 L 947.25 163.57 L 947.25 188.87" stroke-width="1" zvalue="10722"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="631@1" LinkObjectIDznd="642@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 973.3 163.57 L 947.25 163.57 L 947.25 188.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv35" d="M 1230.65 262.82 L 1230.65 220.24" stroke-width="1" zvalue="10732"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@0" LinkObjectIDznd="33@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1230.65 262.82 L 1230.65 220.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv35" d="M 1230.63 200.61 L 1230.63 128.37" stroke-width="1" zvalue="10735"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1230.63 200.61 L 1230.63 128.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv35" d="M 1207.78 142.57 L 1230.63 142.57" stroke-width="1" zvalue="10736"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="25@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 1207.78 142.57 L 1230.63 142.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv35" d="M 1230.81 288.71 L 1230.81 323.88" stroke-width="1" zvalue="10737"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@1" LinkObjectIDznd="30@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1230.81 288.71 L 1230.81 323.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv35" d="M 1250.68 175.14 L 1230.63 175.11" stroke-width="1" zvalue="10738"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 1250.68 175.14 L 1230.63 175.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv35" d="M 1230.83 343.51 L 1230.83 365.66" stroke-width="1" zvalue="10740"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="30@1" LinkObjectIDznd="646@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1230.83 343.51 L 1230.83 365.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv35" d="M 1250.68 303.14 L 1230.81 303.11" stroke-width="1" zvalue="10743"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="23@0" LinkObjectIDznd="27" MaxPinNum="2"/>
   </metadata>
  <path d="M 1250.68 303.14 L 1230.81 303.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv35" d="M 1250.68 235.14 L 1230.65 235.11" stroke-width="1" zvalue="10751"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="17@0" LinkObjectIDznd="31" MaxPinNum="2"/>
   </metadata>
  <path d="M 1250.68 235.14 L 1230.65 235.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv10" d="M 896.05 662.23 L 896.05 719.56" stroke-width="1" zvalue="10759"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@1" LinkObjectIDznd="645@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.05 662.23 L 896.05 719.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv35" d="M 1408.07 391.33 L 1408.07 365.66" stroke-width="1" zvalue="10784"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@1" LinkObjectIDznd="646@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1408.07 391.33 L 1408.07 365.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv35" d="M 1407.91 462.13 L 1407.91 410.96" stroke-width="1" zvalue="10785"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@1" LinkObjectIDznd="81@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1407.91 462.13 L 1407.91 410.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv35" d="M 1443.75 430.51 L 1407.91 430.51" stroke-width="1" zvalue="10788"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="79" MaxPinNum="2"/>
   </metadata>
  <path d="M 1443.75 430.51 L 1407.91 430.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv10" d="M 1408.05 662.23 L 1408.05 719.56" stroke-width="1" zvalue="10795"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@1" LinkObjectIDznd="521@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1408.05 662.23 L 1408.05 719.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv10" d="M 566.24 719.56 L 566.24 744.47" stroke-width="1" zvalue="10801"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="645@2" LinkObjectIDznd="8@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 566.24 719.56 L 566.24 744.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv10" d="M 566.24 786.77 L 566.26 828.61" stroke-width="1" zvalue="10805"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8@1" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 566.24 786.77 L 566.26 828.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv10" d="M 605.02 802.06 L 566.24 802.06" stroke-width="1" zvalue="10811"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="37" MaxPinNum="2"/>
   </metadata>
  <path d="M 605.02 802.06 L 566.24 802.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv10" d="M 590.29 667.3 L 590.29 661.41 L 590.59 661.41 L 590.59 655.53" stroke-width="1" zvalue="10818"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 590.29 667.3 L 590.29 661.41 L 590.59 661.41 L 590.59 655.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv10" d="M 640.78 640.09 L 640.78 667.3" stroke-width="1" zvalue="10819"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="54@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 640.78 640.09 L 640.78 667.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv10" d="M 590.36 690.84 L 590.36 702.73 L 642.18 702.73 L 642.18 690.84" stroke-width="1" zvalue="10820"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@1" LinkObjectIDznd="54@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 590.36 690.84 L 590.36 702.73 L 642.18 702.73 L 642.18 690.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv10" d="M 614.91 702.73 L 614.91 719.56" stroke-width="1" zvalue="10821"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58" LinkObjectIDznd="645@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 614.91 702.73 L 614.91 719.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv10" d="M 606.84 872.33 L 566.28 871.82" stroke-width="1" zvalue="10824"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="121" MaxPinNum="2"/>
   </metadata>
  <path d="M 606.84 872.33 L 566.28 871.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv10" d="M 505.9 811.61 L 505.9 802.73 L 566.24 802.73" stroke-width="1" zvalue="10829"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="37" MaxPinNum="2"/>
   </metadata>
  <path d="M 505.9 811.61 L 505.9 802.73 L 566.24 802.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv10" d="M 730.24 719.56 L 730.24 744.47" stroke-width="1" zvalue="10833"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="645@3" LinkObjectIDznd="119@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 730.24 719.56 L 730.24 744.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv10" d="M 730.24 786.77 L 730.26 828.61" stroke-width="1" zvalue="10835"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@1" LinkObjectIDznd="116@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 730.24 786.77 L 730.26 828.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv10" d="M 668.18 811.61 L 668.18 802.73 L 730.24 802.73" stroke-width="1" zvalue="10849"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="115" MaxPinNum="2"/>
   </metadata>
  <path d="M 668.18 811.61 L 668.18 802.73 L 730.24 802.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv10" d="M 566.28 848.24 L 566.28 894.27" stroke-width="1" zvalue="10850"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="62@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 566.28 848.24 L 566.28 894.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv10" d="M 730.28 848.24 L 730.36 931.91" stroke-width="1" zvalue="10852"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="122@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 730.28 848.24 L 730.36 931.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv10" d="M 699.04 887.33 L 699.04 870.91 L 730.3 870.91" stroke-width="1" zvalue="10855"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="124" MaxPinNum="2"/>
   </metadata>
  <path d="M 699.04 887.33 L 699.04 870.91 L 730.3 870.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv10" d="M 870.24 719.56 L 870.24 744.47" stroke-width="1" zvalue="10859"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="645@4" LinkObjectIDznd="155@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 870.24 719.56 L 870.24 744.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv10" d="M 870.24 786.77 L 870.26 828.61" stroke-width="1" zvalue="10861"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@1" LinkObjectIDznd="149@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 870.24 786.77 L 870.26 828.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="kv10" d="M 808.18 811.61 L 808.18 802.73 L 870.24 802.73" stroke-width="1" zvalue="10865"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="147@0" LinkObjectIDznd="148" MaxPinNum="2"/>
   </metadata>
  <path d="M 808.18 811.61 L 808.18 802.73 L 870.24 802.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv10" d="M 870.28 848.24 L 870.36 927.91" stroke-width="1" zvalue="10868"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="149@0" LinkObjectIDznd="135@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 870.28 848.24 L 870.36 927.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="kv10" d="M 839.04 887.33 L 839.04 869.82 L 870.3 869.82" stroke-width="1" zvalue="10870"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@0" LinkObjectIDznd="134" MaxPinNum="2"/>
   </metadata>
  <path d="M 839.04 887.33 L 839.04 869.82 L 870.3 869.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="kv10" d="M 1010.24 719.56 L 1010.24 744.47" stroke-width="1" zvalue="10874"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="645@5" LinkObjectIDznd="205@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1010.24 719.56 L 1010.24 744.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="kv10" d="M 1010.24 786.77 L 1010.26 828.61" stroke-width="1" zvalue="10876"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@1" LinkObjectIDznd="202@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1010.24 786.77 L 1010.26 828.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 948.18 811.61 L 948.18 802.73 L 1010.24 802.73" stroke-width="1" zvalue="10880"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="200" MaxPinNum="2"/>
   </metadata>
  <path d="M 948.18 811.61 L 948.18 802.73 L 1010.24 802.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv10" d="M 1010.28 848.24 L 1010.36 927.91" stroke-width="1" zvalue="10883"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="186@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1010.28 848.24 L 1010.36 927.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv10" d="M 979.04 887.33 L 979.04 869.82 L 1010.3 869.82" stroke-width="1" zvalue="10885"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="182" MaxPinNum="2"/>
   </metadata>
  <path d="M 979.04 887.33 L 979.04 869.82 L 1010.3 869.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="kv10" d="M 1100.28 787.55 L 1100.28 828.18 L 1215.69 828.18 L 1215.69 793.14" stroke-width="1" zvalue="10891"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="516@1" LinkObjectIDznd="229@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100.28 787.55 L 1100.28 828.18 L 1215.69 828.18 L 1215.69 793.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="kv10" d="M 1215.81 751.29 L 1215.81 719.56" stroke-width="1" zvalue="10892"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@0" LinkObjectIDznd="521@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1215.81 751.29 L 1215.81 719.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="kv10" d="M 1554.29 667.3 L 1554.29 655.53" stroke-width="1" zvalue="10903"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="251@0" LinkObjectIDznd="254@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1554.29 667.3 L 1554.29 655.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="241">
   <path class="kv10" d="M 1604.78 640.09 L 1604.78 667.3" stroke-width="1" zvalue="10904"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@0" LinkObjectIDznd="250@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1604.78 640.09 L 1604.78 667.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv10" d="M 1554.36 690.84 L 1554.36 702.73 L 1606.18 702.73 L 1606.18 690.84" stroke-width="1" zvalue="10905"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="251@1" LinkObjectIDznd="250@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1554.36 690.84 L 1554.36 702.73 L 1606.18 702.73 L 1606.18 690.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="kv10" d="M 1578.91 702.73 L 1578.91 719.56" stroke-width="1" zvalue="10906"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="240" LinkObjectIDznd="521@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1578.91 702.73 L 1578.91 719.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv10" d="M 1408.05 619.92 L 1408.05 588.54" stroke-width="1" zvalue="11059"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="72@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1408.05 619.92 L 1408.05 588.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="297">
   <path class="kv35" d="M 1408.07 488.02 L 1408.07 523.69" stroke-width="1" zvalue="11065"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="72@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1408.07 488.02 L 1408.07 523.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="291">
   <path class="kv10" d="M 896.05 619.92 L 896.05 591.72" stroke-width="1" zvalue="11076"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="510@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.05 619.92 L 896.05 591.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="299">
   <path class="kv35" d="M 895.34 516.95 L 895.34 488.02" stroke-width="1" zvalue="11077"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="510@0" LinkObjectIDznd="575@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 895.34 516.95 L 895.34 488.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="510">
   <g id="5100">
    <use class="kv35" height="30" transform="rotate(0,895.311,554.172) scale(2.3074,2.67338) translate(-491.605,-321.778)" width="24" x="867.62" xlink:href="#PowerTransformer2:可调不带中性点_0" y="514.0700000000001" zvalue="10582"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874563584002" ObjectName="35"/>
    </metadata>
   </g>
   <g id="5101">
    <use class="kv10" height="30" transform="rotate(0,895.311,554.172) scale(2.3074,2.67338) translate(-491.605,-321.778)" width="24" x="867.62" xlink:href="#PowerTransformer2:可调不带中性点_1" y="514.0700000000001" zvalue="10582"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874563649538" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399522975746" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399522975746"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,895.311,554.172) scale(2.3074,2.67338) translate(-491.605,-321.778)" width="24" x="867.62" y="514.0700000000001"/></g>
  <g id="72">
   <g id="720">
    <use class="kv35" height="60" transform="rotate(0,1408.92,556.046) scale(1.03469,1.09846) translate(-46.5442,-46.8854)" width="40" x="1388.22" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="523.09" zvalue="10789"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874563715074" ObjectName="35"/>
    </metadata>
   </g>
   <g id="721">
    <use class="kv10" height="60" transform="rotate(0,1408.92,556.046) scale(1.03469,1.09846) translate(-46.5442,-46.8854)" width="40" x="1388.22" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="523.09" zvalue="10789"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874563780610" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399523041282" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399523041282"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1408.92,556.046) scale(1.03469,1.09846) translate(-46.5442,-46.8854)" width="40" x="1388.22" y="523.09"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="62">
   <use class="kv10" height="30" transform="rotate(90,564.727,922.909) scale(1.06061,1.21212) translate(-30.4519,-158.327)" width="60" x="532.909090909091" xlink:href="#Compensator:并联电容器12121_0" y="904.7272727272727" zvalue="10825"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453732663298" ObjectName="10kV2号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453732663298"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,564.727,922.909) scale(1.06061,1.21212) translate(-30.4519,-158.327)" width="60" x="532.909090909091" y="904.7272727272727"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1031.13,16.3501) scale(1,1) translate(0,0)" writing-mode="lr" x="1031.3" xml:space="preserve" y="21.22" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132816990211" ObjectName="P"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="2" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1228.13,16.2592) scale(1,1) translate(0,0)" writing-mode="lr" x="1228.3" xml:space="preserve" y="21.13" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132815745027" ObjectName="P"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="3" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1031.13,37.3501) scale(1,1) translate(0,0)" writing-mode="lr" x="1031.3" xml:space="preserve" y="42.22" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132817055747" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="4" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1226.13,37.2592) scale(1,1) translate(0,0)" writing-mode="lr" x="1226.3" xml:space="preserve" y="42.13" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132815810563" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="15" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1031.13,58.3501) scale(1,1) translate(0,0)" writing-mode="lr" x="1031.3" xml:space="preserve" y="63.22" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132817121283" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="18" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1227.13,59.2592) scale(1,1) translate(0,0)" writing-mode="lr" x="1227.3" xml:space="preserve" y="64.13" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132815876099" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="19" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1035.69,78.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1035.9" xml:space="preserve" y="85.12" zvalue="1">Ua:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132817514499" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="21" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1235.69,79.6031) scale(1,1) translate(0,0)" writing-mode="lr" x="1235.9" xml:space="preserve" y="86.03" zvalue="1">Ua:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132816269315" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="40">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="40" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,730.364,990.045) scale(1,1) translate(0,0)" writing-mode="lr" x="730.5599999999999" xml:space="preserve" y="994.96" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132810043395" ObjectName="P"/>
   </metadata>
  </g>
  <g id="43">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="43" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,870.364,994.045) scale(1,1) translate(0,0)" writing-mode="lr" x="870.5599999999999" xml:space="preserve" y="998.96" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132819808259" ObjectName="P"/>
   </metadata>
  </g>
  <g id="45">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="45" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1010.36,994.045) scale(1,1) translate(0,0)" writing-mode="lr" x="1010.56" xml:space="preserve" y="998.96" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132818497539" ObjectName="P"/>
   </metadata>
  </g>
  <g id="48">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="48" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,730.364,1013.05) scale(1,1) translate(0,0)" writing-mode="lr" x="730.5599999999999" xml:space="preserve" y="1017.96" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132810108931" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="85" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,870.364,1017.05) scale(1,1) translate(0,0)" writing-mode="lr" x="870.5599999999999" xml:space="preserve" y="1021.96" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132819873795" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="92" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1010.36,1017.05) scale(1,1) translate(0,0)" writing-mode="lr" x="1010.56" xml:space="preserve" y="1021.96" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132818563075" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="99" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,730.364,1036.05) scale(1,1) translate(0,0)" writing-mode="lr" x="730.5599999999999" xml:space="preserve" y="1040.96" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132810174467" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="100" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,870.364,1040.05) scale(1,1) translate(0,0)" writing-mode="lr" x="870.5599999999999" xml:space="preserve" y="1044.96" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132819939331" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="101" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1010.36,1040.05) scale(1,1) translate(0,0)" writing-mode="lr" x="1010.56" xml:space="preserve" y="1044.96" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132818628611" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="109">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="109" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,564.727,990.227) scale(1,1) translate(0,0)" writing-mode="lr" x="564.92" xml:space="preserve" y="995.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132816728067" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="110">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="110" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,564.727,1013.23) scale(1,1) translate(0,0)" writing-mode="lr" x="564.92" xml:space="preserve" y="1018.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132816793603" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="73" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,767.997,434.571) scale(1,1) translate(0,0)" writing-mode="lr" x="768.24" xml:space="preserve" y="440.56" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132810829827" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="74">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="74" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,767.997,461.571) scale(1,1) translate(0,0)" writing-mode="lr" x="768.24" xml:space="preserve" y="467.56" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132810895363" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="75">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="75" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,767.997,488.571) scale(1,1) translate(0,0)" writing-mode="lr" x="768.24" xml:space="preserve" y="494.56" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132811091971" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="76">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="76" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,777.997,605.773) scale(1,1) translate(0,0)" writing-mode="lr" x="778.24" xml:space="preserve" y="611.76" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132810960899" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="86" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,777.997,632.773) scale(1,1) translate(0,0)" writing-mode="lr" x="778.24" xml:space="preserve" y="638.76" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132811026435" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="87" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,777.997,659.773) scale(1,1) translate(0,-4.3117e-13)" writing-mode="lr" x="778.24" xml:space="preserve" y="665.76" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132811419651" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="88" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1308.66,416.55) scale(1,1) translate(0,0)" writing-mode="lr" x="1308.9" xml:space="preserve" y="422.54" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132824788995" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="89" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1308.66,443.55) scale(1,1) translate(0,0)" writing-mode="lr" x="1308.9" xml:space="preserve" y="449.54" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132824854531" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="112">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="112" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1308.66,470.55) scale(1,1) translate(0,0)" writing-mode="lr" x="1308.9" xml:space="preserve" y="476.54" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132825051140" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="114">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="114" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1307.66,599.752) scale(1,1) translate(0,-1.30396e-13)" writing-mode="lr" x="1307.9" xml:space="preserve" y="605.74" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132824920067" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="137" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1307.66,626.752) scale(1,1) translate(0,0)" writing-mode="lr" x="1307.9" xml:space="preserve" y="632.74" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132824985603" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="138" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1307.66,653.752) scale(1,1) translate(0,0)" writing-mode="lr" x="1307.9" xml:space="preserve" y="659.74" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132825378820" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="193">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="193" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,804.727,324.662) scale(1,1) translate(0,0)" writing-mode="lr" x="804.92" xml:space="preserve" y="329.57" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132814368771" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="208">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="208" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,592.159,548.558) scale(1,1) translate(0,0)" writing-mode="lr" x="592.35" xml:space="preserve" y="553.47" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132807159811" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="214">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="214" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1551.25,550.558) scale(1,1) translate(0,0)" writing-mode="lr" x="1551.45" xml:space="preserve" y="555.47" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132813844483" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="269">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="269" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,123.233,514.754) scale(1,1) translate(1.0866e-14,1.12744e-13)" writing-mode="lr" x="123.34" xml:space="preserve" y="519.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132814106627" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="268">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="268" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,123.233,537.754) scale(1,1) translate(1.0866e-14,0)" writing-mode="lr" x="123.34" xml:space="preserve" y="542.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132814172163" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="267">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="267" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,123.233,560.754) scale(1,1) translate(1.0866e-14,6.1479e-14)" writing-mode="lr" x="123.34" xml:space="preserve" y="565.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132814237699" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="266">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="266" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,123.233,489.254) scale(1,1) translate(1.0866e-14,-1.07082e-13)" writing-mode="lr" x="123.34" xml:space="preserve" y="494.03" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132814368771" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="265">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="265" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,127.594,196.615) scale(1,1) translate(0,0)" writing-mode="lr" x="127.75" xml:space="preserve" y="202.89" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132814499843" ObjectName="F"/>
   </metadata>
  </g>
  <g id="262">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="262" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,246.042,514.754) scale(1,1) translate(2.45005e-14,1.12744e-13)" writing-mode="lr" x="246.15" xml:space="preserve" y="519.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132813582339" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="261">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="261" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,246.042,537.754) scale(1,1) translate(2.45005e-14,0)" writing-mode="lr" x="246.15" xml:space="preserve" y="542.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132813647875" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="258">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="258" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,246.042,560.754) scale(1,1) translate(2.45005e-14,6.1479e-14)" writing-mode="lr" x="246.15" xml:space="preserve" y="565.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132813713411" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,246.042,489.254) scale(1,1) translate(2.45005e-14,-1.07082e-13)" writing-mode="lr" x="246.15" xml:space="preserve" y="494.03" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132813844483" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="256" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,131,221.504) scale(1,1) translate(0,0)" writing-mode="lr" x="131.15" xml:space="preserve" y="227.78" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132813975555" ObjectName="F"/>
   </metadata>
  </g>
  <g id="255">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="255" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,309,515.754) scale(1,1) translate(3.14902e-14,1.12966e-13)" writing-mode="lr" x="309.11" xml:space="preserve" y="520.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132806897667" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="252">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="252" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,309,538.754) scale(1,1) translate(3.14902e-14,0)" writing-mode="lr" x="309.11" xml:space="preserve" y="543.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132806963203" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="226">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="226" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,309,561.754) scale(1,1) translate(3.14902e-14,6.159e-14)" writing-mode="lr" x="309.11" xml:space="preserve" y="566.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132807028739" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="225">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="225" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,309,221.754) scale(1,1) translate(0,0)" writing-mode="lr" x="309.15" xml:space="preserve" y="228.03" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132807290883" ObjectName="F"/>
   </metadata>
  </g>
  <g id="224">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="224" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,309,490.254) scale(1,1) translate(3.14902e-14,-1.07304e-13)" writing-mode="lr" x="309.11" xml:space="preserve" y="495.03" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132807159811" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="223">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="223" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,131,243.698) scale(1,1) translate(0,0)" writing-mode="lr" x="131.15" xml:space="preserve" y="249.97" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132825247748" ObjectName="YW1"/>
   </metadata>
  </g>
  <g id="220">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="220" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,312,242.698) scale(1,1) translate(0,0)" writing-mode="lr" x="312.15" xml:space="preserve" y="248.97" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132811288579" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="219">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="219" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,308,265.698) scale(1,1) translate(0,2.01049e-13)" writing-mode="lr" x="308.15" xml:space="preserve" y="271.97" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132811354115" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="218">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="218" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,123.233,587.754) scale(1,1) translate(1.0866e-14,-1.28953e-13)" writing-mode="lr" x="123.34" xml:space="preserve" y="592.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132814565379" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="217">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="217" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,246.042,587.754) scale(1,1) translate(2.45005e-14,-1.28953e-13)" writing-mode="lr" x="246.15" xml:space="preserve" y="592.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132814041091" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="108">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="108" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,309,588.754) scale(1,1) translate(3.14902e-14,-1.29175e-13)" writing-mode="lr" x="309.11" xml:space="preserve" y="593.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132807356419" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="107" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,131,172.004) scale(1,1) translate(0,0)" writing-mode="lr" x="131.15" xml:space="preserve" y="178.34" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132832260099" ObjectName=""/>
   </metadata>
  </g>
  <g id="106">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="106" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,308,172.004) scale(1,1) translate(0,0)" writing-mode="lr" x="308.15" xml:space="preserve" y="178.36" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132832325635" ObjectName="LOAD_QSUM"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="111" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1292.69,793.591) scale(1,1) translate(0,1.04329e-12)" writing-mode="lr" x="1292.89" xml:space="preserve" y="798.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132831932419" ObjectName="P"/>
   </metadata>
  </g>
  <g id="113">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="113" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1293.69,777.591) scale(1,1) translate(0,0)" writing-mode="lr" x="1293.89" xml:space="preserve" y="782.5" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132831997955" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="117" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1292.69,809.591) scale(1,1) translate(0,0)" writing-mode="lr" x="1292.89" xml:space="preserve" y="814.5" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132831735811" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="ClockClass">
  
 </g>
</svg>