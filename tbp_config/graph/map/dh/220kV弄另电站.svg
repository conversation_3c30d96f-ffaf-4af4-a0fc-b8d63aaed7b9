<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549584658434" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="Accessory:线路PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="17.90667828048159" xlink:href="#terminal" y="33.53457520218116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.5" x2="30.5" y1="19" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.58333333333334" x2="32.58333333333334" y1="10.75" y2="10.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.5" x2="18" y1="23" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.93916361389984" x2="17.93916361389984" y1="27.04012345679012" y2="18.96704491718448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.93916361389984" x2="17.93916361389984" y1="30.21630546659505" y2="33.51790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.91963465760507" x2="17.91963465760507" y1="15.99560215515698" y2="9.314303947281834"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.01963465760507" x2="18.01963465760507" y1="19.00831495554599" y2="19.00831495554599"/>
   <ellipse cx="26.87" cy="16.85" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.5565174886495" x2="21.39655620359946" y1="18.92498162221265" y2="18.92498162221265"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.01963465760507" x2="18.01963465760507" y1="30.15122531314907" y2="30.15122531314907"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.63985082198282" x2="21.47988953693279" y1="15.91306207843401" y2="15.91306207843401"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.63985082198283" x2="21.47988953693279" y1="30.15122531314905" y2="30.15122531314905"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.63985082198283" x2="21.47988953693279" y1="27.05597243603708" y2="27.05597243603708"/>
   <ellipse cx="30.46" cy="10.68" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="15.72318415531615" x2="20" y1="9.24639541176734" y2="9.24639541176734"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="16.47318415531615" x2="19.08333333333333" y1="7.99639541176734" y2="7.99639541176734"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="16.97318415531615" x2="18.41666666666666" y1="6.496395411767342" y2="6.496395411767342"/>
   <ellipse cx="34.12" cy="16.85" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.75" x2="28.75" y1="16.75" y2="16.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.58333333333334" x2="36.58333333333334" y1="16.66666666666667" y2="16.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.633333333333335" x2="8.833333333333337" y1="13.29999999999999" y2="17.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.633333333333331" x2="6.250000000000004" y1="13.29999999999999" y2="16.91666666666666"/>
   <rect fill-opacity="0" height="12.12" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,7.62,14.61) scale(1,-1) translate(0,-840.33)" width="6.08" x="4.58" y="8.550000000000001"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.383333333333324" x2="7.383333333333324" y1="8.549999999999994" y2="4.949999999999994"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.633333333333324" x2="7.633333333333324" y1="23" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.75" x2="17.83333333333333" y1="22.98597664515375" y2="22.98597664515375"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.512285139234804" x2="5.235469294550956" y1="4.80515846663106" y2="4.80515846663106"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.67895180590147" x2="6.06880262788429" y1="3.43015846663106" y2="3.43015846663106"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.833333333333332" x2="6.914421100452429" y1="2.05515846663106" y2="2.05515846663106"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="4.416666666666668" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.249999999999998" y2="20.16666666666667"/>
  </symbol>
  <symbol id="Accessory:PT232_0" viewBox="0,0,25,35">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0.2666666666666657"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="12.5" y1="15.16666666666667" y2="0.5"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12.5,7.5) scale(1,1) translate(0,0)" width="6" x="9.5" y="2.5"/>
   <ellipse cx="12.75" cy="20" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.75" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.67" cy="29.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.82" cy="24.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333336" x2="5.583333333333336" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="5.583333333333334" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="5.5" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.75" x2="12.75" y1="22.25" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="12.75" y1="17.63888888888889" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="12.75" y1="17.63888888888889" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="32" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="12.58333333333333" y1="27.38888888888889" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="12.58333333333333" y1="27.38888888888889" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="19.31481481481482" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.94444444444444" x2="20.62962962962963" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21.94444444444444" y1="24.96612466124661" y2="24.96612466124661"/>
  </symbol>
  <symbol id="DollyBreaker:手车_0" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="0.3833333333333346" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="21.8" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.0246913580247" x2="0.3833333333333346" y1="11.2962962962963" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.07407407407407" x2="21.53229166666667" y1="11.2962962962963" y2="21.41666666666666"/>
  </symbol>
  <symbol id="DollyBreaker:手车_1" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="21" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="1" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="11" y1="1" y2="11"/>
  </symbol>
  <symbol id="DollyBreaker:手车_2" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <path d="M 3.6066 1.05 L 18.5833 9.95" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 18.4234 1 L 3.5 10" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT6_0" viewBox="0,0,15,20">
   <use terminal-index="0" type="0" x="7.560000000000002" xlink:href="#terminal" y="0.42361930658363"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.160000000000005" x2="8.559999999999999" y1="15.48035578286757" y2="14.24281579851547"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.060000000000002" x2="8.459999999999996" y1="16.09912577504353" y2="17.33666575939564"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.459999999999999" x2="5.060000000000007" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000005" x2="9.859999999999998" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.859999999999999" x2="8.859999999999999" y1="14.65532912663279" y2="17.13040909533699"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000003" x2="7.460000000000003" y1="2.279929283111803" y2="4.755009251816007"/>
   <ellipse cx="7.5" cy="6.51" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.7" cy="13.44" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸333_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,17.5) scale(1,1) translate(0,0)" width="6" x="3" y="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="12.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:刀闸333_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="4.583333333333332" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,17.5) scale(1,1) translate(0,0)" width="6" x="3" y="12"/>
  </symbol>
  <symbol id="Disconnector:刀闸333_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:220kV线路PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="12.90667828048158" xlink:href="#terminal" y="37.28457520218116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27" x2="13" y1="26.75" y2="26.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.08333333333334" x2="29.08333333333334" y1="14.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27" x2="27" y1="22.75" y2="26.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.93916361389983" x2="12.93916361389983" y1="33.96630546659506" y2="37.26790853551449"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.93916361389983" x2="12.93916361389983" y1="30.79012345679012" y2="22.71704491718448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.91963465760507" x2="12.91963465760507" y1="19.74560215515698" y2="13.06430394728183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.01963465760507" x2="13.01963465760507" y1="22.75831495554599" y2="22.75831495554599"/>
   <ellipse cx="23.37" cy="20.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.556517488649495" x2="16.39655620359946" y1="22.67498162221265" y2="22.67498162221265"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.01963465760507" x2="13.01963465760507" y1="33.90122531314907" y2="33.90122531314907"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982819" x2="16.47988953693279" y1="19.66306207843401" y2="19.66306207843401"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982824" x2="16.47988953693279" y1="33.90122531314905" y2="33.90122531314905"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982824" x2="16.47988953693279" y1="30.80597243603708" y2="30.80597243603708"/>
   <ellipse cx="26.96" cy="14.43" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="10.72318415531615" x2="15" y1="12.99639541176734" y2="12.99639541176734"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.47318415531615" x2="14.08333333333333" y1="11.74639541176734" y2="11.74639541176734"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.97318415531615" x2="13.41666666666667" y1="10.24639541176734" y2="10.24639541176734"/>
   <ellipse cx="30.62" cy="20.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.25" x2="25.25" y1="20.5" y2="20.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.08333333333334" x2="33.08333333333334" y1="20.41666666666667" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="220kV弄另电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="286" x="57.67" xlink:href="logo.png" y="48.33"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,200.667,78.3333) scale(1,1) translate(0,0)" writing-mode="lr" x="200.67" xml:space="preserve" y="81.83" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,200.167,78.0237) scale(1,1) translate(0,0)" writing-mode="lr" x="200.17" xml:space="preserve" y="87.02" zvalue="3">220kV弄另电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="41" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="386"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="386">信号一览</text>
  <line fill="none" id="169" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="391.6666666666667" x2="391.6666666666667" y1="16.33333333333343" y2="1046.333333333333" zvalue="4"/>
  <line fill="none" id="167" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6666666666672" x2="384.6666666666667" y1="152.2038259474159" y2="152.2038259474159" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1652.89,348.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1652.89" xml:space="preserve" y="352.72" zvalue="8">220kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,803,249.444) scale(1,1) translate(0,0)" writing-mode="lr" x="803" xml:space="preserve" y="253.94" zvalue="11">261</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,794.222,297) scale(1,1) translate(0,0)" writing-mode="lr" x="794.22" xml:space="preserve" y="301.5" zvalue="12">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,795.889,189) scale(1,1) translate(0,0)" writing-mode="lr" x="795.89" xml:space="preserve" y="193.5" zvalue="15">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,769.599,86.7778) scale(1,1) translate(0,0)" writing-mode="lr" x="769.6" xml:space="preserve" y="91.28" zvalue="20">220kV弄潞线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1373.82,227) scale(1,1) translate(0,0)" writing-mode="lr" x="1373.82" xml:space="preserve" y="231.5" zvalue="48">2901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,905.444,370.556) scale(1,1) translate(0,0)" writing-mode="lr" x="905.4400000000001" xml:space="preserve" y="375.06" zvalue="64">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,869.778,435.556) scale(1,1) translate(0,0)" writing-mode="lr" x="869.78" xml:space="preserve" y="440.06" zvalue="66">201</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,822.222,555) scale(1,1) translate(0,0)" writing-mode="lr" x="822.22" xml:space="preserve" y="559.5" zvalue="68">1010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,975.518,538.583) scale(1,1) translate(0,0)" writing-mode="lr" x="975.518391530079" xml:space="preserve" y="543.0833333333335" zvalue="75">1号主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,896.552,998.833) scale(1,1) translate(9.62621e-14,0)" writing-mode="lr" x="896.55" xml:space="preserve" y="1003.33" zvalue="105">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,869.667,657.556) scale(1,1) translate(0,0)" writing-mode="lr" x="869.67" xml:space="preserve" y="662.0599999999999" zvalue="116">1</text>
  <line fill="none" id="75" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6666666666672" x2="379.6666666666667" y1="498.2038259474159" y2="498.2038259474159" zvalue="166"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12.66666666666697" x2="102.666666666667" y1="937.3333333333335" y2="937.3333333333335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12.66666666666697" x2="102.666666666667" y1="976.4966333333334" y2="976.4966333333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12.66666666666697" x2="12.66666666666697" y1="937.3333333333335" y2="976.4966333333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="102.666666666667" y1="937.3333333333335" y2="976.4966333333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="372.666666666667" y1="937.3333333333335" y2="937.3333333333335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="372.666666666667" y1="976.4966333333334" y2="976.4966333333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="102.666666666667" y1="937.3333333333335" y2="976.4966333333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372.666666666667" x2="372.666666666667" y1="937.3333333333335" y2="976.4966333333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12.66666666666697" x2="102.666666666667" y1="976.4966033333335" y2="976.4966033333335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12.66666666666697" x2="102.666666666667" y1="1004.415003333334" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12.66666666666697" x2="12.66666666666697" y1="976.4966033333335" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="102.666666666667" y1="976.4966033333335" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="192.666666666667" y1="976.4966033333335" y2="976.4966033333335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="192.666666666667" y1="1004.415003333334" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="102.666666666667" y1="976.4966033333335" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.666666666667" x2="192.666666666667" y1="976.4966033333335" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.6666666666671" x2="282.6666666666671" y1="976.4966033333335" y2="976.4966033333335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.6666666666671" x2="282.6666666666671" y1="1004.415003333334" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.6666666666671" x2="192.6666666666671" y1="976.4966033333335" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.6666666666671" x2="282.6666666666671" y1="976.4966033333335" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.666666666667" x2="372.666666666667" y1="976.4966033333335" y2="976.4966033333335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.666666666667" x2="372.666666666667" y1="1004.415003333334" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.666666666667" x2="282.666666666667" y1="976.4966033333335" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372.666666666667" x2="372.666666666667" y1="976.4966033333335" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12.66666666666697" x2="102.666666666667" y1="1004.414933333333" y2="1004.414933333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12.66666666666697" x2="102.666666666667" y1="1032.333333333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12.66666666666697" x2="12.66666666666697" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="102.666666666667" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="192.666666666667" y1="1004.414933333333" y2="1004.414933333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="192.666666666667" y1="1032.333333333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="102.666666666667" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.666666666667" x2="192.666666666667" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.6666666666671" x2="282.6666666666671" y1="1004.414933333333" y2="1004.414933333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.6666666666671" x2="282.6666666666671" y1="1032.333333333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.6666666666671" x2="192.6666666666671" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.6666666666671" x2="282.6666666666671" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.666666666667" x2="372.666666666667" y1="1004.414933333333" y2="1004.414933333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.666666666667" x2="372.666666666667" y1="1032.333333333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.666666666667" x2="282.666666666667" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372.666666666667" x2="372.666666666667" y1="1004.414933333333" y2="1032.333333333333"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.6667,957.333) scale(1,1) translate(0,0)" writing-mode="lr" x="57.67" xml:space="preserve" y="963.33" zvalue="168">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.6667,991.333) scale(1,1) translate(0,0)" writing-mode="lr" x="54.67" xml:space="preserve" y="997.33" zvalue="169">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236.667,991.333) scale(1,1) translate(0,0)" writing-mode="lr" x="236.67" xml:space="preserve" y="997.33" zvalue="170">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.6667,1019.33) scale(1,1) translate(0,0)" writing-mode="lr" x="53.67" xml:space="preserve" y="1025.33" zvalue="171">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.667,1019.33) scale(1,1) translate(0,0)" writing-mode="lr" x="235.67" xml:space="preserve" y="1025.33" zvalue="172">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.1667,571.833) scale(1,1) translate(0,0)" writing-mode="lr" x="78.16666666666697" xml:space="preserve" y="576.3333333333335" zvalue="174">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.721,959.333) scale(1,1) translate(0,0)" writing-mode="lr" x="237.72" xml:space="preserve" y="965.33" zvalue="177">NongLing-01-2011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1462.5,156) scale(1,1) translate(0,0)" writing-mode="lr" x="1462.5" xml:space="preserve" y="160.5" zvalue="196">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1439,296) scale(1,1) translate(0,0)" writing-mode="lr" x="1439" xml:space="preserve" y="300.5" zvalue="198">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,851.5,301) scale(1,1) translate(0,0)" writing-mode="lr" x="851.5" xml:space="preserve" y="305.5" zvalue="204">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,845,193) scale(1,1) translate(0,0)" writing-mode="lr" x="845" xml:space="preserve" y="197.5" zvalue="206">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,841,144) scale(1,1) translate(0,0)" writing-mode="lr" x="841" xml:space="preserve" y="148.5" zvalue="208">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="206" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,951.5,430) scale(1,1) translate(0,0)" writing-mode="lr" x="951.5" xml:space="preserve" y="434.5" zvalue="212">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="205" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,955.5,484) scale(1,1) translate(0,0)" writing-mode="lr" x="955.5" xml:space="preserve" y="488.5" zvalue="214">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="216" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,912.5,758.444) scale(1,1) translate(0,0)" writing-mode="lr" x="912.5" xml:space="preserve" y="762.9400000000001" zvalue="224">011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="218" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,983.5,659) scale(1,1) translate(0,0)" writing-mode="lr" x="983.5" xml:space="preserve" y="663.5" zvalue="226">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="220" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,984.5,743) scale(1,1) translate(0,0)" writing-mode="lr" x="984.5" xml:space="preserve" y="747.5" zvalue="228">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="235" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,996.738,946) scale(1,1) translate(0,0)" writing-mode="lr" x="996.74" xml:space="preserve" y="950.5" zvalue="241">0912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="241" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1080.68,939) scale(1,1) translate(0,0)" writing-mode="lr" x="1080.68" xml:space="preserve" y="943.5" zvalue="245">0913</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="242" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1164,945) scale(1,1) translate(0,0)" writing-mode="lr" x="1164" xml:space="preserve" y="949.5" zvalue="249">0914</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1473.34,370.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1473.34" xml:space="preserve" y="375.06" zvalue="273">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="286" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1433.78,435.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1433.78" xml:space="preserve" y="440.06" zvalue="275">202</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1386.22,555) scale(1,1) translate(0,0)" writing-mode="lr" x="1386.22" xml:space="preserve" y="559.5" zvalue="277">2020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1539.52,538.583) scale(1,1) translate(-1.33517e-12,0)" writing-mode="lr" x="1539.518391530079" xml:space="preserve" y="543.0833333333335" zvalue="282">2号主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1459.55,998.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1459.55" xml:space="preserve" y="1003.33" zvalue="285">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1432.67,658.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1432.67" xml:space="preserve" y="663.0599999999999" zvalue="287">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="280" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1515.5,430) scale(1,1) translate(0,0)" writing-mode="lr" x="1515.5" xml:space="preserve" y="434.5" zvalue="291">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="279" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1519.5,484) scale(1,1) translate(0,0)" writing-mode="lr" x="1519.5" xml:space="preserve" y="488.5" zvalue="293">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="277" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1475.5,758.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1475.5" xml:space="preserve" y="762.9400000000001" zvalue="301">021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="276" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1546.5,660) scale(1,1) translate(0,0)" writing-mode="lr" x="1546.5" xml:space="preserve" y="664.5" zvalue="303">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="275" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1547.5,744) scale(1,1) translate(0,0)" writing-mode="lr" x="1547.5" xml:space="preserve" y="748.5" zvalue="306">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="274" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1560.74,946) scale(1,1) translate(0,0)" writing-mode="lr" x="1560.74" xml:space="preserve" y="950.5" zvalue="310">0922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="273" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1644.68,939) scale(1,1) translate(0,0)" writing-mode="lr" x="1644.68" xml:space="preserve" y="943.5" zvalue="312">0923</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="272" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1729,946) scale(1,1) translate(0,0)" writing-mode="lr" x="1729" xml:space="preserve" y="950.5" zvalue="314">0924</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="331" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,684,762.5) scale(1,1) translate(0,0)" writing-mode="lr" x="684" xml:space="preserve" y="767" zvalue="331">1号厂用电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="334" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1257.12,756.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1257.13" xml:space="preserve" y="761" zvalue="333">2号厂用电</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="336" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,835.5,687) scale(1,1) translate(0,0)" writing-mode="lr" x="835.5" xml:space="preserve" y="691.5" zvalue="335">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="340" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1386.5,683.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1386.5" xml:space="preserve" y="687.75" zvalue="339">0921</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="390.25" y2="413"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="376">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="377">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,64.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="22" xml:space="preserve" y="266.5" zvalue="378">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="379">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="380">220kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="387">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="388">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,357.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="361.75" zvalue="391">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,309.75) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="314.25" zvalue="392">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="394">厂用电率</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="386"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="166">
   <path class="kv220" d="M 608.67 331 L 1671.67 331" stroke-width="4" zvalue="7"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674240495621" ObjectName="220kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674240495621"/></metadata>
  <path d="M 608.67 331 L 1671.67 331" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="162">
   <use class="kv220" height="20" transform="rotate(0,781.667,250.444) scale(1.22222,1.11111) translate(-141.01,-23.9333)" width="10" x="775.5555555555557" xlink:href="#Breaker:开关_0" y="239.3333333333335" zvalue="9"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924500258821" ObjectName="220kV弄潞线261断路器"/>
   <cge:TPSR_Ref TObjectID="6473924500258821"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,781.667,250.444) scale(1.22222,1.11111) translate(-141.01,-23.9333)" width="10" x="775.5555555555557" y="239.3333333333335"/></g>
  <g id="185">
   <use class="kv220" height="20" transform="rotate(0,894,435.556) scale(1.22222,1.11111) translate(-161.434,-42.4444)" width="10" x="887.8888990746607" xlink:href="#Breaker:开关_0" y="424.4444444444446" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924500193285" ObjectName="#1主变220kV侧201断路器"/>
   <cge:TPSR_Ref TObjectID="6473924500193285"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,894,435.556) scale(1.22222,1.11111) translate(-161.434,-42.4444)" width="10" x="887.8888990746607" y="424.4444444444446"/></g>
  <g id="215">
   <use class="kv10" height="20" transform="rotate(0,894.111,760.167) scale(1.3,1.18333) translate(-204.833,-115.939)" width="10" x="887.6112938045621" xlink:href="#Breaker:开关_0" y="748.3333333333335" zvalue="223"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924500324357" ObjectName="#1发电机011断路器"/>
   <cge:TPSR_Ref TObjectID="6473924500324357"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,894.111,760.167) scale(1.3,1.18333) translate(-204.833,-115.939)" width="10" x="887.6112938045621" y="748.3333333333335"/></g>
  <g id="328">
   <use class="kv220" height="20" transform="rotate(0,1458,435.556) scale(1.22222,1.11111) translate(-263.98,-42.4444)" width="10" x="1451.888899074661" xlink:href="#Breaker:开关_0" y="424.4444444444446" zvalue="274"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924500455429" ObjectName="#2主变220kV侧202断路器"/>
   <cge:TPSR_Ref TObjectID="6473924500455429"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1458,435.556) scale(1.22222,1.11111) translate(-263.98,-42.4444)" width="10" x="1451.888899074661" y="424.4444444444446"/></g>
  <g id="311">
   <use class="kv10" height="20" transform="rotate(0,1457.11,760.167) scale(1.3,1.18333) translate(-334.756,-115.939)" width="10" x="1450.611293804562" xlink:href="#Breaker:开关_0" y="748.3333333333335" zvalue="300"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924500389893" ObjectName="#2发电机021断路器"/>
   <cge:TPSR_Ref TObjectID="6473924500389893"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1457.11,760.167) scale(1.3,1.18333) translate(-334.756,-115.939)" width="10" x="1450.611293804562" y="748.3333333333335"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="161">
   <use class="kv220" height="30" transform="rotate(0,781.667,298) scale(-1.11111,-0.814815) translate(-1484.33,-666.505)" width="15" x="773.3333333333335" xlink:href="#Disconnector:刀闸_0" y="285.777791341146" zvalue="10"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449749909509" ObjectName="220kV弄潞线2611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449749909509"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,781.667,298) scale(-1.11111,-0.814815) translate(-1484.33,-666.505)" width="15" x="773.3333333333335" y="285.777791341146"/></g>
  <g id="160">
   <use class="kv220" height="30" transform="rotate(0,781.667,190) scale(-1.11111,-0.814815) translate(-1484.33,-425.96)" width="15" x="773.3333333598246" xlink:href="#Disconnector:刀闸_0" y="177.7777777777779" zvalue="13"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449749843973" ObjectName="220kV弄潞线2616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449749843973"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,781.667,190) scale(-1.11111,-0.814815) translate(-1484.33,-425.96)" width="15" x="773.3333333598246" y="177.7777777777779"/></g>
  <g id="268">
   <use class="kv220" height="30" transform="rotate(0,1407.67,223) scale(-1.11111,-0.814815) translate(-2673.73,-499.46)" width="15" x="1399.333333333333" xlink:href="#Disconnector:刀闸_0" y="210.777791341146" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449749712901" ObjectName="220kVⅠ母电压互感器2901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449749712901"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1407.67,223) scale(-1.11111,-0.814815) translate(-2673.73,-499.46)" width="15" x="1399.333333333333" y="210.777791341146"/></g>
  <g id="186">
   <use class="kv220" height="30" transform="rotate(0,894,371.556) scale(1.11111,0.814815) translate(-88.5667,81.6667)" width="15" x="885.6666768391931" xlink:href="#Disconnector:刀闸_0" y="359.3333333333335" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449749647365" ObjectName="#1主变220kV侧2011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449749647365"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,894,371.556) scale(1.11111,0.814815) translate(-88.5667,81.6667)" width="15" x="885.6666768391931" y="359.3333333333335"/></g>
  <g id="98">
   <use class="kv10" height="30" transform="rotate(0,894,658.556) scale(1.11111,0.814815) translate(-88.5667,146.894)" width="15" x="885.6666666666667" xlink:href="#Disconnector:刀闸_0" y="646.3333333333335" zvalue="115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449749385221" ObjectName="#1发电机0111隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449749385221"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,894,658.556) scale(1.11111,0.814815) translate(-88.5667,146.894)" width="15" x="885.6666666666667" y="646.3333333333335"/></g>
  <g id="329">
   <use class="kv220" height="30" transform="rotate(0,1457.89,371.556) scale(1.11111,0.814815) translate(-144.956,81.6667)" width="15" x="1449.557975640469" xlink:href="#Disconnector:刀闸_0" y="359.3333333333335" zvalue="272"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449752924166" ObjectName="#2主变220kV侧2021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449752924166"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1457.89,371.556) scale(1.11111,0.814815) translate(-144.956,81.6667)" width="15" x="1449.557975640469" y="359.3333333333335"/></g>
  <g id="320">
   <use class="kv10" height="30" transform="rotate(0,1457,659.556) scale(1.11111,0.814815) translate(-144.867,147.121)" width="15" x="1448.666666666667" xlink:href="#Disconnector:刀闸_0" y="647.3333333333335" zvalue="286"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449752662022" ObjectName="#2发电机0211隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449752662022"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1457,659.556) scale(1.11111,0.814815) translate(-144.867,147.121)" width="15" x="1448.666666666667" y="647.3333333333335"/></g>
  <g id="335">
   <use class="kv10" height="26" transform="rotate(0,792.692,681.75) scale(2.11538,2.11538) translate(-411.273,-344.968)" width="12" x="779.9999999999999" xlink:href="#Disconnector:刀闸333_0" y="654.25" zvalue="334"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449753120774" ObjectName="1号厂用电0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449753120774"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,792.692,681.75) scale(2.11538,2.11538) translate(-411.273,-344.968)" width="12" x="779.9999999999999" y="654.25"/></g>
  <g id="339">
   <use class="kv10" height="26" transform="rotate(0,1352.44,671.75) scale(2.11538,2.11538) translate(-706.414,-339.695)" width="12" x="1339.75" xlink:href="#Disconnector:刀闸333_0" y="644.25" zvalue="338"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449753186310" ObjectName="2号厂用电0921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449753186310"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1352.44,671.75) scale(2.11538,2.11538) translate(-706.414,-339.695)" width="12" x="1339.75" y="644.25"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="158">
   <path class="kv220" d="M 781.57 309.82 L 781.57 331" stroke-width="1" zvalue="14"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="166@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.57 309.82 L 781.57 331" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv220" d="M 781.6 285.99 L 781.6 261.06" stroke-width="1" zvalue="16"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@1" LinkObjectIDznd="162@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.6 285.99 L 781.6 261.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv220" d="M 781.63 239.81 L 781.57 201.82" stroke-width="1" zvalue="17"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162@0" LinkObjectIDznd="160@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.63 239.81 L 781.57 201.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv220" d="M 781.6 177.99 L 781.6 125.88" stroke-width="1" zvalue="19"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160@1" LinkObjectIDznd="154@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.6 177.99 L 781.6 125.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv220" d="M 1407.57 234.82 L 1407.57 331" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1407.57 234.82 L 1407.57 331" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv220" d="M 894.07 383.57 L 894.07 424.93" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@1" LinkObjectIDznd="185@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 894.07 383.57 L 894.07 424.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv220" d="M 894.05 516.06 L 763.56 516.06 L 763.56 542.44" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@2" LinkObjectIDznd="181@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 894.05 516.06 L 763.56 516.06 L 763.56 542.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv220" d="M 894.1 359.74 L 894.1 331" stroke-width="1" zvalue="73"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 894.1 359.74 L 894.1 331" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv220" d="M 894.08 446.17 L 894.08 488.22" stroke-width="1" zvalue="85"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@1" LinkObjectIDznd="163@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 894.08 446.17 L 894.08 488.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv220" d="M 1407.6 131.53 L 1407.6 210.99" stroke-width="1" zvalue="198"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@0" LinkObjectIDznd="268@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1407.6 131.53 L 1407.6 210.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv220" d="M 1438.68 179.99 L 1407.6 179.99" stroke-width="1" zvalue="199"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@0" LinkObjectIDznd="188" MaxPinNum="2"/>
   </metadata>
  <path d="M 1438.68 179.99 L 1407.6 179.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv220" d="M 1464.01 278.68 L 1464.01 261 L 1407.57 261" stroke-width="1" zvalue="201"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="129" MaxPinNum="2"/>
   </metadata>
  <path d="M 1464.01 278.68 L 1464.01 261 L 1407.57 261" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="kv220" d="M 781.6 163.99 L 826.68 163.99" stroke-width="1" zvalue="208"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151" LinkObjectIDznd="198@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.6 163.99 L 826.68 163.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv220" d="M 829.68 215.99 L 781.59 215.99" stroke-width="1" zvalue="209"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="155" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.68 215.99 L 781.59 215.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="207">
   <path class="kv220" d="M 837.01 287.68 L 837.01 271 L 781.6 271" stroke-width="1" zvalue="215"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@0" LinkObjectIDznd="157" MaxPinNum="2"/>
   </metadata>
  <path d="M 837.01 287.68 L 837.01 271 L 781.6 271" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv220" d="M 938.51 417.68 L 938.51 402 L 895.57 402" stroke-width="1" zvalue="216"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="125" MaxPinNum="2"/>
   </metadata>
  <path d="M 938.51 417.68 L 938.51 402 L 895.57 402" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv220" d="M 939.51 470.68 L 939.51 460 L 894.08 460" stroke-width="1" zvalue="217"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@0" LinkObjectIDznd="113" MaxPinNum="2"/>
   </metadata>
  <path d="M 939.51 470.68 L 939.51 460 L 894.08 460" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="243">
   <path class="kv10" d="M 966.79 948.82 L 966.79 922.29" stroke-width="1" zvalue="249"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="233@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.79 948.82 L 966.79 922.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv10" d="M 1049.73 944.82 L 1049.73 915.29" stroke-width="1" zvalue="250"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="227@0" LinkObjectIDznd="237@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1049.73 944.82 L 1049.73 915.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="245">
   <path class="kv10" d="M 1132.68 943.82 L 1132.68 917.29" stroke-width="1" zvalue="251"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="240@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.68 943.82 L 1132.68 917.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="246">
   <path class="kv10" d="M 894.1 646.74 L 894.1 584.8" stroke-width="1" zvalue="252"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="163@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 894.1 646.74 L 894.1 584.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="247">
   <path class="kv10" d="M 894.07 670.57 L 894.07 748.85" stroke-width="1" zvalue="253"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@1" LinkObjectIDznd="215@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 894.07 670.57 L 894.07 748.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="249">
   <path class="kv10" d="M 895.55 925.8 L 895.55 771.47" stroke-width="1" zvalue="255"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="215@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 895.55 925.8 L 895.55 771.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="253">
   <path class="kv10" d="M 688.29 681.16 L 688.29 623 L 894.1 623" stroke-width="1" zvalue="259"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="330@0" LinkObjectIDznd="255" MaxPinNum="2"/>
   </metadata>
  <path d="M 688.29 681.16 L 688.29 623 L 894.1 623" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="255">
   <path class="kv10" d="M 894.1 623 L 969.01 623 L 969.01 645.68" stroke-width="1" zvalue="261"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246" LinkObjectIDznd="217@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 894.1 623 L 969.01 623 L 969.01 645.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="256">
   <path class="kv10" d="M 969.01 729.68 L 969.01 709.71 L 894.07 709.71" stroke-width="1" zvalue="262"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@0" LinkObjectIDznd="247" MaxPinNum="2"/>
   </metadata>
  <path d="M 969.01 729.68 L 969.01 709.71 L 894.07 709.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="kv10" d="M 1133.01 907.01 L 1133.01 856 L 895.55 856" stroke-width="1" zvalue="268"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="240@0" LinkObjectIDznd="249" MaxPinNum="2"/>
   </metadata>
  <path d="M 1133.01 907.01 L 1133.01 856 L 895.55 856" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv10" d="M 1049.7 905.01 L 1049.7 856" stroke-width="1" zvalue="269"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@0" LinkObjectIDznd="266" MaxPinNum="2"/>
   </metadata>
  <path d="M 1049.7 905.01 L 1049.7 856" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="271">
   <path class="kv10" d="M 966.75 912.01 L 966.75 856" stroke-width="1" zvalue="270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@0" LinkObjectIDznd="266" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.75 912.01 L 966.75 856" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="326">
   <path class="kv220" d="M 1457.96 383.57 L 1457.96 424.93" stroke-width="1" zvalue="278"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="329@1" LinkObjectIDznd="328@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1457.96 383.57 L 1457.96 424.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="325">
   <path class="kv220" d="M 1458.05 516.06 L 1327.56 516.06 L 1327.56 542.44" stroke-width="1" zvalue="279"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="323@2" LinkObjectIDznd="327@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1458.05 516.06 L 1327.56 516.06 L 1327.56 542.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="324">
   <path class="kv220" d="M 1457.99 359.74 L 1457.99 331" stroke-width="1" zvalue="280"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="329@0" LinkObjectIDznd="166@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1457.99 359.74 L 1457.99 331" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="322">
   <path class="kv220" d="M 1458.08 446.17 L 1458.08 488.22" stroke-width="1" zvalue="283"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="328@1" LinkObjectIDznd="323@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1458.08 446.17 L 1458.08 488.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="316">
   <path class="kv220" d="M 1501.01 416.68 L 1501.01 402 L 1457.96 402" stroke-width="1" zvalue="294"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="318@0" LinkObjectIDznd="326" MaxPinNum="2"/>
   </metadata>
  <path d="M 1501.01 416.68 L 1501.01 402 L 1457.96 402" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="315">
   <path class="kv220" d="M 1503.51 470.68 L 1503.51 460 L 1458.08 460" stroke-width="1" zvalue="295"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="317@0" LinkObjectIDznd="322" MaxPinNum="2"/>
   </metadata>
  <path d="M 1503.51 470.68 L 1503.51 460 L 1458.08 460" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="302">
   <path class="kv10" d="M 1530.79 948.82 L 1530.79 922.29" stroke-width="1" zvalue="315"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="308@0" LinkObjectIDznd="305@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1530.79 948.82 L 1530.79 922.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="301">
   <path class="kv10" d="M 1613.73 944.82 L 1613.73 915.29" stroke-width="1" zvalue="316"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="307@0" LinkObjectIDznd="304@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1613.73 944.82 L 1613.73 915.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="300">
   <path class="kv10" d="M 1698.05 947.82 L 1698.05 918.29" stroke-width="1" zvalue="317"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="306@0" LinkObjectIDznd="303@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1698.05 947.82 L 1698.05 918.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="299">
   <path class="kv10" d="M 1457.1 647.74 L 1457.1 584.8" stroke-width="1" zvalue="318"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@0" LinkObjectIDznd="323@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1457.1 647.74 L 1457.1 584.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="298">
   <path class="kv10" d="M 1457.07 671.57 L 1457.07 748.85" stroke-width="1" zvalue="319"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@1" LinkObjectIDznd="311@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1457.07 671.57 L 1457.07 748.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="297">
   <path class="kv10" d="M 1458.55 925.8 L 1458.55 771.47" stroke-width="1" zvalue="320"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="321@0" LinkObjectIDznd="311@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1458.55 925.8 L 1458.55 771.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="294">
   <path class="kv10" d="M 1252.29 683.16 L 1252.29 623 L 1457.1 623" stroke-width="1" zvalue="323"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="332@0" LinkObjectIDznd="299" MaxPinNum="2"/>
   </metadata>
  <path d="M 1252.29 683.16 L 1252.29 623 L 1457.1 623" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="292">
   <path class="kv10" d="M 1456.11 623 L 1532.01 623 L 1532.01 646.68" stroke-width="1" zvalue="325"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="294" LinkObjectIDznd="310@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1456.11 623 L 1532.01 623 L 1532.01 646.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="291">
   <path class="kv10" d="M 1532.01 730.68 L 1532.01 710.21 L 1457.07 710.21" stroke-width="1" zvalue="326"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="309@0" LinkObjectIDznd="298" MaxPinNum="2"/>
   </metadata>
  <path d="M 1532.01 730.68 L 1532.01 710.21 L 1457.07 710.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="290">
   <path class="kv10" d="M 1698.01 908.01 L 1698.01 856 L 1458.55 856" stroke-width="1" zvalue="327"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="303@0" LinkObjectIDznd="297" MaxPinNum="2"/>
   </metadata>
  <path d="M 1698.01 908.01 L 1698.01 856 L 1458.55 856" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="289">
   <path class="kv10" d="M 1613.7 905.01 L 1613.7 856" stroke-width="1" zvalue="328"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="304@0" LinkObjectIDznd="290" MaxPinNum="2"/>
   </metadata>
  <path d="M 1613.7 905.01 L 1613.7 856" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="288">
   <path class="kv10" d="M 1530.75 912.01 L 1530.75 856" stroke-width="1" zvalue="329"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="305@0" LinkObjectIDznd="290" MaxPinNum="2"/>
   </metadata>
  <path d="M 1530.75 912.01 L 1530.75 856" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="337">
   <path class="kv10" d="M 792.7 654.41 L 792.7 623" stroke-width="1" zvalue="335"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="335@1" LinkObjectIDznd="253" MaxPinNum="2"/>
   </metadata>
  <path d="M 792.7 654.41 L 792.7 623" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="338">
   <path class="kv10" d="M 792.87 709.18 L 792.87 727.92" stroke-width="1" zvalue="336"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="335@0" LinkObjectIDznd="213@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 792.87 709.18 L 792.87 727.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="342">
   <path class="kv10" d="M 1352.45 644.41 L 1352.45 623" stroke-width="1" zvalue="340"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="339@1" LinkObjectIDznd="294" MaxPinNum="2"/>
   </metadata>
  <path d="M 1352.45 644.41 L 1352.45 623" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="343">
   <path class="kv10" d="M 1352.62 699.18 L 1352.62 711.93" stroke-width="1" zvalue="341"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="339@0" LinkObjectIDznd="312@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1352.62 699.18 L 1352.62 711.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv220" d="M 781.6 139.46 L 757.94 139.46" stroke-width="1" zvalue="349"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151" LinkObjectIDznd="174@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.6 139.46 L 757.94 139.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv220" d="M 805.36 139.81 L 781.6 139.81" stroke-width="1" zvalue="350"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1423@0" LinkObjectIDznd="151" MaxPinNum="2"/>
   </metadata>
  <path d="M 805.36 139.81 L 781.6 139.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="181">
   <use class="kv220" height="40" transform="rotate(0,778.444,556) scale(1.11111,-1.11111) translate(-75.6222,-1054.18)" width="40" x="756.2222222222224" xlink:href="#GroundDisconnector:中性点地刀12_0" y="533.7777777777778" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449749581830" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449749581830"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,778.444,556) scale(1.11111,-1.11111) translate(-75.6222,-1054.18)" width="40" x="756.2222222222224" y="533.7777777777778"/></g>
  <g id="176">
   <use class="kv220" height="30" transform="rotate(90,1453,180) scale(1,-1) translate(0,-360)" width="12" x="1447" xlink:href="#GroundDisconnector:地刀12_0" y="165" zvalue="195"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449750106117" ObjectName="220kVⅠ母电压互感器29017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449750106117"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1453,180) scale(1,-1) translate(0,-360)" width="12" x="1447" y="165"/></g>
  <g id="182">
   <use class="kv220" height="30" transform="rotate(180,1464,293) scale(1,-1) translate(0,-586)" width="12" x="1458" xlink:href="#GroundDisconnector:地刀12_0" y="278" zvalue="197"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449750237189" ObjectName="220kVⅠ母电压互感器29010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449750237189"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1464,293) scale(1,-1) translate(0,-586)" width="12" x="1458" y="278"/></g>
  <g id="194">
   <use class="kv220" height="30" transform="rotate(180,837,302) scale(1,-1) translate(0,-604)" width="12" x="831" xlink:href="#GroundDisconnector:地刀12_0" y="287" zvalue="203"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449750368261" ObjectName="220kV弄潞线26117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449750368261"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,837,302) scale(1,-1) translate(0,-604)" width="12" x="831" y="287"/></g>
  <g id="196">
   <use class="kv220" height="30" transform="rotate(90,844,216) scale(1,-1) translate(0,-432)" width="12" x="838" xlink:href="#GroundDisconnector:地刀12_0" y="201" zvalue="205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449750499333" ObjectName="220kV弄潞线26160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449750499333"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,844,216) scale(1,-1) translate(0,-432)" width="12" x="838" y="201"/></g>
  <g id="198">
   <use class="kv220" height="30" transform="rotate(90,841,164) scale(1,-1) translate(0,-328)" width="12" x="835" xlink:href="#GroundDisconnector:地刀12_0" y="149" zvalue="207"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449750630405" ObjectName="220kV弄潞线26167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449750630405"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,841,164) scale(1,-1) translate(0,-328)" width="12" x="835" y="149"/></g>
  <g id="202">
   <use class="kv220" height="30" transform="rotate(180,937,431) scale(1,-1) translate(0,-862)" width="12" x="931" xlink:href="#GroundDisconnector:地刀12_0" y="416" zvalue="211"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449750761477" ObjectName="#1主变220kV侧20117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449750761477"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,937,431) scale(1,-1) translate(0,-862)" width="12" x="931" y="416"/></g>
  <g id="203">
   <use class="kv220" height="30" transform="rotate(180,939.5,485) scale(1.08333,-1) translate(-71.7692,-970)" width="12" x="933" xlink:href="#GroundDisconnector:地刀12_0" y="470" zvalue="213"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449750892549" ObjectName="#1主变220kV侧20167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449750892549"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,939.5,485) scale(1.08333,-1) translate(-71.7692,-970)" width="12" x="933" y="470"/></g>
  <g id="217">
   <use class="kv10" height="30" transform="rotate(180,969,660) scale(1,-1) translate(0,-1320)" width="12" x="963" xlink:href="#GroundDisconnector:地刀12_0" y="645" zvalue="225"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449751089157" ObjectName="#1发电机01117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449751089157"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,969,660) scale(1,-1) translate(0,-1320)" width="12" x="963" y="645"/></g>
  <g id="219">
   <use class="kv10" height="30" transform="rotate(180,969,744) scale(1,-1) translate(0,-1488)" width="12" x="963" xlink:href="#GroundDisconnector:地刀12_0" y="729" zvalue="227"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449751220230" ObjectName="#1发电机01167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449751220230"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,969,744) scale(1,-1) translate(0,-1488)" width="12" x="963" y="729"/></g>
  <g id="327">
   <use class="kv220" height="40" transform="rotate(0,1342.44,556) scale(1.11111,-1.11111) translate(-132.022,-1054.18)" width="40" x="1320.222222222222" xlink:href="#GroundDisconnector:中性点地刀12_0" y="533.7777777777778" zvalue="276"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449752858630" ObjectName="#2主变220kV侧2020中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449752858630"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1342.44,556) scale(1.11111,-1.11111) translate(-132.022,-1054.18)" width="40" x="1320.222222222222" y="533.7777777777778"/></g>
  <g id="318">
   <use class="kv220" height="30" transform="rotate(180,1501,431) scale(1,-1) translate(0,-862)" width="12" x="1495" xlink:href="#GroundDisconnector:地刀12_0" y="416" zvalue="290"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449752596486" ObjectName="#2主变220kV侧20217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449752596486"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1501,431) scale(1,-1) translate(0,-862)" width="12" x="1495" y="416"/></g>
  <g id="317">
   <use class="kv220" height="30" transform="rotate(180,1503.5,485) scale(1.08333,-1) translate(-115.154,-970)" width="12" x="1497" xlink:href="#GroundDisconnector:地刀12_0" y="470" zvalue="292"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449752465414" ObjectName="#2主变220kV侧20267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449752465414"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1503.5,485) scale(1.08333,-1) translate(-115.154,-970)" width="12" x="1497" y="470"/></g>
  <g id="310">
   <use class="kv10" height="30" transform="rotate(180,1532,661) scale(1,-1) translate(0,-1322)" width="12" x="1526" xlink:href="#GroundDisconnector:地刀12_0" y="646" zvalue="302"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449752268806" ObjectName="#2发电机02117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449752268806"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1532,661) scale(1,-1) translate(0,-1322)" width="12" x="1526" y="646"/></g>
  <g id="309">
   <use class="kv10" height="30" transform="rotate(180,1532,745) scale(1,-1) translate(0,-1490)" width="12" x="1526" xlink:href="#GroundDisconnector:地刀12_0" y="730" zvalue="304"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449752137734" ObjectName="#2发电机02167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449752137734"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1532,745) scale(1,-1) translate(0,-1490)" width="12" x="1526" y="730"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="163">
   <g id="1630">
    <use class="kv220" height="50" transform="rotate(0,894.018,536.414) scale(1.91667,1.96323) translate(-413.824,-239.102)" width="30" x="865.27" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="487.33" zvalue="74"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874431070212" ObjectName="220"/>
    </metadata>
   </g>
   <g id="1631">
    <use class="kv10" height="50" transform="rotate(0,894.018,536.414) scale(1.91667,1.96323) translate(-413.824,-239.102)" width="30" x="865.27" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="487.33" zvalue="74"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874431135748" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399447805956" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399447805956"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,894.018,536.414) scale(1.91667,1.96323) translate(-413.824,-239.102)" width="30" x="865.27" y="487.33"/></g>
  <g id="323">
   <g id="3230">
    <use class="kv220" height="50" transform="rotate(0,1458.02,536.414) scale(1.91667,1.96323) translate(-683.563,-239.102)" width="30" x="1429.27" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="487.33" zvalue="281"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874431201284" ObjectName="220"/>
    </metadata>
   </g>
   <g id="3231">
    <use class="kv10" height="50" transform="rotate(0,1458.02,536.414) scale(1.91667,1.96323) translate(-683.563,-239.102)" width="30" x="1429.27" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="487.33" zvalue="281"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874431266820" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399447871492" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399447871492"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1458.02,536.414) scale(1.91667,1.96323) translate(-683.563,-239.102)" width="30" x="1429.27" y="487.33"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="kv10" height="30" transform="rotate(0,895.552,953.218) scale(1.85899,1.85899) translate(-400.926,-427.572)" width="30" x="867.6666666666669" xlink:href="#Generator:发电机_0" y="925.3333333333334" zvalue="104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449749450757" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449749450757"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,895.552,953.218) scale(1.85899,1.85899) translate(-400.926,-427.572)" width="30" x="867.6666666666669" y="925.3333333333334"/></g>
  <g id="321">
   <use class="kv10" height="30" transform="rotate(0,1458.55,953.218) scale(1.85899,1.85899) translate(-661.073,-427.572)" width="30" x="1430.666666666667" xlink:href="#Generator:发电机_0" y="925.3333333333334" zvalue="284"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449752727558" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449752727558"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1458.55,953.218) scale(1.85899,1.85899) translate(-661.073,-427.572)" width="30" x="1430.666666666667" y="925.3333333333334"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="175">
   <use class="kv220" height="40" transform="rotate(0,1409.69,118) scale(1,1) translate(0,0)" width="40" x="1389.692027914956" xlink:href="#Accessory:线路PT_0" y="98" zvalue="194"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449749975045" ObjectName="220kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1409.69,118) scale(1,1) translate(0,0)" width="40" x="1389.692027914956" y="98"/></g>
  <g id="213">
   <use class="kv10" height="18" transform="rotate(0,797.5,753) scale(3,3) translate(-516.667,-484)" width="15" x="775" xlink:href="#Accessory:PT8_0" y="726" zvalue="220"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449750958085" ObjectName="1号厂用电PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,797.5,753) scale(3,3) translate(-516.667,-484)" width="15" x="775" y="726"/></g>
  <g id="221">
   <use class="kv10" height="35" transform="rotate(0,966.79,969.75) scale(1.21429,1.21429) translate(-167.931,-167.382)" width="25" x="951.6111111111112" xlink:href="#Accessory:PT232_0" y="948.5" zvalue="228"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449751285766" ObjectName="#1发电机励磁1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,966.79,969.75) scale(1.21429,1.21429) translate(-167.931,-167.382)" width="25" x="951.6111111111112" y="948.5"/></g>
  <g id="227">
   <use class="kv10" height="35" transform="rotate(0,1049.73,965.75) scale(1.21429,1.21429) translate(-182.569,-166.676)" width="25" x="1034.555555555556" xlink:href="#Accessory:PT232_0" y="944.5" zvalue="233"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449751351302" ObjectName="#1发电机励磁2"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1049.73,965.75) scale(1.21429,1.21429) translate(-182.569,-166.676)" width="25" x="1034.555555555556" y="944.5"/></g>
  <g id="230">
   <use class="kv10" height="35" transform="rotate(0,1132.68,964.75) scale(1.21429,1.21429) translate(-197.206,-166.5)" width="25" x="1117.5" xlink:href="#Accessory:PT232_0" y="943.5" zvalue="237"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449751416838" ObjectName="#1发电机励磁3"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1132.68,964.75) scale(1.21429,1.21429) translate(-197.206,-166.5)" width="25" x="1117.5" y="943.5"/></g>
  <g id="312">
   <use class="kv10" height="18" transform="rotate(0,1355.43,737.15) scale(3.01667,3.01667) translate(-890.988,-474.641)" width="15" x="1332.800391290831" xlink:href="#Accessory:PT8_0" y="710" zvalue="299"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449752334342" ObjectName="2号厂用电PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1355.43,737.15) scale(3.01667,3.01667) translate(-890.988,-474.641)" width="15" x="1332.800391290831" y="710"/></g>
  <g id="308">
   <use class="kv10" height="35" transform="rotate(0,1530.79,969.75) scale(1.21429,1.21429) translate(-267.461,-167.382)" width="25" x="1515.611111111111" xlink:href="#Accessory:PT232_0" y="948.5" zvalue="305"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449752006662" ObjectName="#2发电机励磁1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1530.79,969.75) scale(1.21429,1.21429) translate(-267.461,-167.382)" width="25" x="1515.611111111111" y="948.5"/></g>
  <g id="307">
   <use class="kv10" height="35" transform="rotate(0,1613.73,965.75) scale(1.21429,1.21429) translate(-282.098,-166.676)" width="25" x="1598.555555555556" xlink:href="#Accessory:PT232_0" y="944.5" zvalue="307"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449751941126" ObjectName="#2发电机励磁2"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1613.73,965.75) scale(1.21429,1.21429) translate(-282.098,-166.676)" width="25" x="1598.555555555556" y="944.5"/></g>
  <g id="306">
   <use class="kv10" height="35" transform="rotate(0,1698.05,968.75) scale(1.21429,1.21429) translate(-296.978,-167.206)" width="25" x="1682.873241843034" xlink:href="#Accessory:PT232_0" y="947.5" zvalue="308"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449751875590" ObjectName="#2发电机励磁3"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1698.05,968.75) scale(1.21429,1.21429) translate(-296.978,-167.206)" width="25" x="1682.873241843034" y="947.5"/></g>
  <g id="330">
   <use class="kv10" height="20" transform="rotate(0,688.125,707.5) scale(2.75,2.75) translate(-424.773,-432.727)" width="15" x="667.5" xlink:href="#Accessory:PT6_0" y="680" zvalue="330"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449752989702" ObjectName="1号厂用电"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,688.125,707.5) scale(2.75,2.75) translate(-424.773,-432.727)" width="15" x="667.5" y="680"/></g>
  <g id="332">
   <use class="kv10" height="20" transform="rotate(0,1252.12,709.5) scale(2.75,2.75) translate(-783.682,-434)" width="15" x="1231.5" xlink:href="#Accessory:PT6_0" y="682" zvalue="332"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449753055238" ObjectName="2号厂用电"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1252.12,709.5) scale(2.75,2.75) translate(-783.682,-434)" width="15" x="1231.5" y="682"/></g>
  <g id="1423">
   <use class="kv220" height="26" transform="rotate(270,816.827,139.778) scale(-0.838049,0.927421) translate(-1792.48,9.99538)" width="12" x="811.7984603262458" xlink:href="#Accessory:避雷器1_0" y="127.7219083764127" zvalue="346"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449753317382" ObjectName="220kV弄潞线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,816.827,139.778) scale(-0.838049,0.927421) translate(-1792.48,9.99538)" width="12" x="811.7984603262458" y="127.7219083764127"/></g>
  <g id="174">
   <use class="kv220" height="40" transform="rotate(270,738.45,132.651) scale(0.960317,1.1275) translate(29.7208,-12.4504)" width="40" x="719.2436507936511" xlink:href="#Accessory:220kV线路PT_0" y="110.1007936507941" zvalue="347"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449753251846" ObjectName="220kV弄潞线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,738.45,132.651) scale(0.960317,1.1275) translate(29.7208,-12.4504)" width="40" x="719.2436507936511" y="110.1007936507941"/></g>
 </g>
 <g id="DollyBreakerClass">
  <g id="233">
   <use class="kv10" height="22" transform="rotate(0,966.738,922) scale(1,1) translate(0,0)" width="22" x="955.7378692680777" xlink:href="#DollyBreaker:手车_0" y="911" zvalue="240"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449751482374" ObjectName="#1发电机励磁0912"/>
   <cge:TPSR_Ref TObjectID="6192449751482374"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,966.738,922) scale(1,1) translate(0,0)" width="22" x="955.7378692680777" y="911"/></g>
  <g id="237">
   <use class="kv10" height="22" transform="rotate(0,1049.68,915) scale(1,1) translate(0,0)" width="22" x="1038.682313712522" xlink:href="#DollyBreaker:手车_0" y="904" zvalue="244"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449751547910" ObjectName="#1发电机励磁0913"/>
   <cge:TPSR_Ref TObjectID="6192449751547910"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1049.68,915) scale(1,1) translate(0,0)" width="22" x="1038.682313712522" y="904"/></g>
  <g id="240">
   <use class="kv10" height="22" transform="rotate(0,1133,917) scale(1,1) translate(0,0)" width="22" x="1122" xlink:href="#DollyBreaker:手车_0" y="906" zvalue="248"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449751613446" ObjectName="#1发电机励磁0914"/>
   <cge:TPSR_Ref TObjectID="6192449751613446"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1133,917) scale(1,1) translate(0,0)" width="22" x="1122" y="906"/></g>
  <g id="305">
   <use class="kv10" height="22" transform="rotate(0,1530.74,922) scale(1,1) translate(0,0)" width="22" x="1519.737869268078" xlink:href="#DollyBreaker:手车_0" y="911" zvalue="309"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449751810054" ObjectName="#2发电机励磁0922"/>
   <cge:TPSR_Ref TObjectID="6192449751810054"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1530.74,922) scale(1,1) translate(0,0)" width="22" x="1519.737869268078" y="911"/></g>
  <g id="304">
   <use class="kv10" height="22" transform="rotate(0,1613.68,915) scale(1,1) translate(0,0)" width="22" x="1602.682313712522" xlink:href="#DollyBreaker:手车_0" y="904" zvalue="311"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449751744518" ObjectName="#2发电机励磁0923"/>
   <cge:TPSR_Ref TObjectID="6192449751744518"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1613.68,915) scale(1,1) translate(0,0)" width="22" x="1602.682313712522" y="904"/></g>
  <g id="303">
   <use class="kv10" height="22" transform="rotate(0,1698,918) scale(1,1) translate(0,0)" width="22" x="1687" xlink:href="#DollyBreaker:手车_0" y="907" zvalue="313"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449751678982" ObjectName="#2发电机励磁0924"/>
   <cge:TPSR_Ref TObjectID="6192449751678982"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1698,918) scale(1,1) translate(0,0)" width="22" x="1687" y="907"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,648.599,36.8333) scale(1,1) translate(0,0)" writing-mode="lr" x="648.13" xml:space="preserve" y="41.61" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124916822021" ObjectName="P"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="2" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,648.599,63.8333) scale(1,1) translate(0,0)" writing-mode="lr" x="648.13" xml:space="preserve" y="68.61" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124916887557" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,649.599,91.8333) scale(1,1) translate(0,0)" writing-mode="lr" x="649.13" xml:space="preserve" y="96.61" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124916953093" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124918788103" ObjectName="F"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="62" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124923310085" ObjectName="F"/>
   </metadata>
  </g>
  <g id="44">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124923375621" ObjectName="F"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="38" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124923179013" ObjectName="F"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124923244549" ObjectName="F"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="33" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124923179013" ObjectName="F"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124923179013" ObjectName="F"/>
   </metadata>
  </g>
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,356.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="361.3" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="水位"/>
   </metadata>
  </g>
  <g id="83">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="83" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,758.518,427.833) scale(1,1) translate(-1.56101e-13,0)" writing-mode="lr" x="757.9400000000001" xml:space="preserve" y="434.11" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124914266117" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="84">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="84" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,761.518,457.833) scale(1,1) translate(0,0)" writing-mode="lr" x="760.9400000000001" xml:space="preserve" y="464.11" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124914331653" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="85" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,760.518,483.833) scale(1,1) translate(0,0)" writing-mode="lr" x="759.9400000000001" xml:space="preserve" y="490.11" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124914528261" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="86" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1590.52,417.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1589.94" xml:space="preserve" y="424.11" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124920623109" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="87" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1588.52,444.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1587.94" xml:space="preserve" y="451.11" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124920688645" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="88" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1593.52,472.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1592.94" xml:space="preserve" y="479.11" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124920885253" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="89" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,797.052,930.603) scale(1,1) translate(0,0)" writing-mode="lr" x="796.47" xml:space="preserve" y="936.88" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124913610760" ObjectName="P"/>
   </metadata>
  </g>
  <g id="90">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="90" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,796.052,957.603) scale(1,1) translate(0,0)" writing-mode="lr" x="795.47" xml:space="preserve" y="963.88" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124913676293" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="91" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,798.552,983.353) scale(1,1) translate(0,0)" writing-mode="lr" x="797.97" xml:space="preserve" y="989.63" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124913741829" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="92" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1360.3,900.603) scale(1,1) translate(0,0)" writing-mode="lr" x="1359.72" xml:space="preserve" y="906.88" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124919967749" ObjectName="P"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="93" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1365.3,932.603) scale(1,1) translate(0,0)" writing-mode="lr" x="1364.72" xml:space="preserve" y="938.88" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124920033285" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="94" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1367.8,965.853) scale(1,1) translate(0,0)" writing-mode="lr" x="1367.22" xml:space="preserve" y="972.13" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124920098821" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="95" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1659.17,301.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1658.7" xml:space="preserve" y="306.53" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124918394888" ObjectName="Ua"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="43">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="384"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374889713667" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="42">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="385"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
  <g id="4">
   <use height="30" transform="rotate(0,326.812,131.464) scale(1.27778,1.03333) translate(-58.5462,-3.74077)" width="90" x="269.31" xlink:href="#State:全站检修_0" y="115.96" zvalue="413"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549584658434" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,326.812,131.464) scale(1.27778,1.03333) translate(-58.5462,-3.74077)" width="90" x="269.31" y="115.96"/></g>
 </g>
</svg>