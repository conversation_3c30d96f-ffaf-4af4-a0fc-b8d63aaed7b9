<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549682765825" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="PowerTransformer3:可调三卷变YNYND_0" viewBox="0,0,50,50">
   <ellipse cx="32.93" cy="16.75" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.83333333333333" x2="33.6574074074074" y1="9.833333333333332" y2="13.4254686785551"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.58333333333333" x2="33.67203932327389" y1="9.916666666666668" y2="13.4254686785551"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.66666666666666" x2="33.66666666666666" y1="13.41666666666666" y2="18.16666666666666"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="39.9188048530154" x2="16.66666666666667" y1="2.701343733814291" y2="42.33333333333333"/>
   <path d="M 41.9167 -0.0833333 L 38.9167 2.08333 L 40.7911 3.34973 z" fill="rgb(255,0,0)" fill-opacity="1" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <use terminal-index="0" type="1" x="33.16666666666667" xlink:href="#terminal" y="2.083333333333329"/>
   <use terminal-index="3" type="2" x="33.75" xlink:href="#terminal" y="13.25"/>
  </symbol>
  <symbol id="PowerTransformer3:可调三卷变YNYND_1" viewBox="0,0,50,50">
   <ellipse cx="32.28" cy="34.42" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.83401920438957" x2="37.75" y1="38.36796982167353" y2="33.91666666666668"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.82670324645633" x2="28.24999999999999" y1="38.36065386374028" y2="33.83333333333334"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.83333333333334" x2="32.83333333333334" y1="42.66666666666667" y2="38.36796982167353"/>
   <use terminal-index="1" type="1" x="33" xlink:href="#terminal" y="49.25"/>
   <use terminal-index="2" type="2" x="32.75" xlink:href="#terminal" y="38"/>
  </symbol>
  <symbol id="PowerTransformer3:可调三卷变YNYND_2" viewBox="0,0,50,50">
   <ellipse cx="16.2" cy="24.75" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 13.5833 19.0833 L 8.5 27.0833 L 18.5833 27.0833 z" fill-opacity="0" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="4" type="1" x="1.5" xlink:href="#terminal" y="25"/>
  </symbol>
  <symbol id="ACLineSegment:线路911_0" viewBox="0,0,30,38">
   <use terminal-index="0" type="0" x="6.95833333333333" xlink:href="#terminal" y="37.9408407585088"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.25545055364641" x2="14.25545055364641" y1="12.29138864447597" y2="18.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.21058610156545" x2="6.90436235204273" y1="12.17541949757221" y2="12.17541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.66094883543335" x2="17.13823024054981" y1="12.17541949757221" y2="12.17541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.910223367697554" x2="6.910223367697554" y1="38.08333333333334" y2="0.08333333333333215"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.21383161512025" x2="24.16666666666666" y1="12.24758812251703" y2="12.24758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.10940244368076" x2="17.10940244368076" y1="12.24758812251703" y2="12.24758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.11598892707138" x2="24.11598892707138" y1="8.999999999999996" y2="15.49517624503405"/>
   <ellipse cx="14.26" cy="23.07" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.3" cy="27.33" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.51" cy="31.33" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="17.10940244368077" x2="17.10940244368077" y1="9.733252937417257" y2="14.83409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="25.26910080183275" x2="25.26910080183275" y1="10.26295093653441" y2="14.59306843322376"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.13393470790376" x2="26.13393470790376" y1="11.16505874834466" y2="13.14969593432727"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.325897289041579" x2="9.325897289041579" y1="12.24758812251703" y2="12.24758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="19.27148720885832" x2="19.27148720885832" y1="9.733252937417255" y2="14.8340919325617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.242563955708249" x2="9.242563955708249" y1="9.583333333333334" y2="14.68417232847779"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.48798205421914" x2="11.48798205421914" y1="9.733252937417257" y2="14.83409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="16.66666666666667" y1="23.33333333333334" y2="23.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.08333333333334" x2="24.08333333333334" y1="27.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="17.33333333333334" y1="31.25" y2="31.25"/>
  </symbol>
  <symbol id="Accessory:避雷器PT（德宏变）_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="31.00905664884499" xlink:href="#terminal" y="3.977945066432934"/>
   <path d="M 10 25 L 4 25 L 4 32" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 5.11667 17.3167 L 5.11667 21.3167 L 8.11667 19.3167 L 5.11667 17.3167" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,31.02,18.92) scale(1,1) translate(0,0)" width="5.87" x="28.08" y="11.92"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31" x2="31" y1="12" y2="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="31" y1="8" y2="8"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31" x2="31" y1="4" y2="12"/>
   <path d="M 31 20 L 32 18" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.75" x2="22.16666666666667" y1="18.54585360194742" y2="18.54585360194742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.15990464876007" x2="22.15990464876007" y1="14.7557301451573" y2="22.82880868476295"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.08490464876007" x2="22.08490464876007" y1="11.65160994962663" y2="7.974999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.17943360505483" x2="22.17943360505483" y1="25.80025144679045" y2="32.48154965466559"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.07943360505483" x2="22.07943360505483" y1="22.78753864640143" y2="22.78753864640143"/>
   <ellipse cx="13.48" cy="11.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.54255077401041" x2="18.70251205906045" y1="22.87087197973477" y2="22.87087197973477"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.07943360505483" x2="22.07943360505483" y1="11.64462828879835" y2="11.64462828879835"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.45921744067708" x2="18.61917872572711" y1="11.64462828879838" y2="11.64462828879838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.45921744067709" x2="18.61917872572711" y1="25.88279152351342" y2="25.88279152351342"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.45921744067708" x2="18.61917872572711" y1="14.73988116591035" y2="14.73988116591035"/>
   <ellipse cx="9.890000000000001" cy="25.37" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.29255077401042" x2="20.01573492932657" y1="32.54945819018009" y2="32.54945819018009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.45921744067708" x2="20.84906826265991" y1="33.79945819018008" y2="33.79945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="22.87588410734375" x2="21.43240159599324" y1="35.29945819018008" y2="35.29945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="11.73744029990987" y2="10.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="14.21252026861409" y2="11.73744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="11.73744029990987" y2="10.49990031555776"/>
   <ellipse cx="6.48" cy="11.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.48" cy="18.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="18.73744029990987" y2="17.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="18.73744029990987" y2="17.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659907" x2="4.049068262659912" y1="11.73744029990987" y2="10.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.44906826265991" x2="6.44906826265991" y1="14.21252026861409" y2="11.73744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="21.21252026861409" y2="18.73744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659912" x2="8.849068262659907" y1="11.73744029990987" y2="10.49990031555776"/>
   <ellipse cx="6.48" cy="18.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.98240159599324" x2="9.98240159599324" y1="27.74585360194743" y2="25.27077363324322"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982401595993235" x2="7.582401595993243" y1="25.2707736332432" y2="24.0332336488911"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982401595993244" x2="12.38240159599323" y1="25.2707736332432" y2="24.0332336488911"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.042550774010419" x2="1.765734929326573" y1="32.04945819018009" y2="32.04945819018009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.209217440677085" x2="2.599068262659905" y1="33.29945819018008" y2="33.29945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.625884107343754" x2="3.182401595993237" y1="34.79945819018008" y2="34.79945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.92943360505483" x2="30.92943360505483" y1="25.80025144679045" y2="32.48154965466559"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.04255077401042" x2="28.76573492932657" y1="32.54945819018009" y2="32.54945819018009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.20921744067708" x2="29.59906826265991" y1="33.79945819018008" y2="33.79945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.62588410734375" x2="30.18240159599324" y1="35.29945819018008" y2="35.29945819018008"/>
   <path d="M 31 20 L 30 18" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill="rgb(170,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.333333333333333" x2="9.75" y1="0.4166666666666696" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.666666666666668" x2="0.4166666666666661" y1="0.4999999999999982" y2="19.66666666666667"/>
   <rect fill-opacity="0" height="19.58" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.04,10.04) scale(1,1) translate(0,0)" width="9.58" x="0.25" y="0.25"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="Compensator:电抗器1(德宏变)_0" viewBox="0,0,25,22">
   <use terminal-index="0" type="0" x="12.49341397849462" xlink:href="#terminal" y="0.2093137254902402"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.475" x2="20.475" y1="21.575" y2="21.575"/>
   <path d="M 8.71237 4.45801 L 12.4899 4.45801 L 12.4899 21.575" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 8.675 4.44379 A 3.97937 3.875 -90 1 0 12.55 0.464423" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 16.7124 13.458 L 20.4899 13.458 L 20.4899 21.6" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 16.675 13.4438 A 3.97937 3.875 -90 1 0 20.55 9.46442" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 0.675 13.4438 A 3.97937 3.875 -90 1 0 4.55 9.46442" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 0.712366 13.458 L 4.48992 13.458 L 4.48992 21.575" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:五卷PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="25.16666666666667" y2="1"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,14) scale(1,1) translate(0,0)" width="6" x="7" y="7"/>
   <path d="M 5.11667 33.0667 L 5.11667 37.0667 L 8.11667 35.0667 L 5.11667 33.0667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.66666666666667" x2="9.999999999999998" y1="4" y2="4"/>
   <ellipse cx="13.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <ellipse cx="13.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.44906826265991" x2="6.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659912" x2="8.849068262659907" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659907" x2="4.049068262659912" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="36.96252026861409" y2="34.48744029990989"/>
   <ellipse cx="6.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.11944444444444" x2="29.63333333333333" y1="28.26666666666667" y2="28.26666666666667"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,27.29,13.93) scale(1,1) translate(0,0)" width="7.58" x="23.5" y="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="3.93333333333333" y2="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="5.85" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="24.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="30.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.16111111111111" x2="30.27222222222222" y1="26.88508771929826" y2="26.88508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="21.85" y2="25.45"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.88333333333333" x2="31.55" y1="25.50350877192984" y2="25.50350877192984"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变D-Y_0" viewBox="0,0,23,39">
   <use terminal-index="0" type="0" x="7.666666666666666" xlink:href="#terminal" y="0.4166666666666714"/>
   <path d="M 7.5 4.25 L 4.58333 10.1667 L 10.8333 10.1667 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.42034 32.6694 L 7.64848 37.3333 L 7.05673 32.6694 L 7.72567 32.7212 z" fill="rgb(170,170,127)" fill-opacity="1" stroke="rgb(170,170,127)" stroke-dasharray="6 2 2 2" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 6.20768 29.4824 L 9.29511 29.4824 L 7.7514 31.037 L 6.20768 29.4824" fill="none" stroke="rgb(170,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.597025948103791" x2="7.597025948103791" y1="2.04293756914176" y2="0.4882945839350796"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.597025948103783" x2="19.17487025948103" y1="29.63785055656028" y2="21.32051058570456"/>
   <ellipse cx="7.42" cy="8.6" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.5" cy="17.92" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.611718118722353" x2="7.611718118722353" y1="15.85874008086165" y2="18.45772818087859"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.2052656081938" x2="7.61171811872235" y1="21.05671628089551" y2="18.45772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.018170629250884" x2="7.611718118722337" y1="21.05671628089551" y2="18.45772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.41666666666666" x2="15.93279794298801" y1="26.63147854022231" y2="26.63147854022231"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.11989292193093" x2="17.22957168772375" y1="27.93097259023087" y2="27.93097259023087"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8231191771952" x2="18.52634543245948" y1="29.23046664023932" y2="29.23046664023932"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.611718118722351" x2="19.17487025948103" y1="18.45772818087858" y2="18.45772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.17473230482734" x2="19.17473230482734" y1="18.49624249591244" y2="26.39901100404639"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.756205920707614" x2="7.756205920707614" y1="32.74713652697363" y2="24.41980688231738"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id=":110kV线路带PT避雷器_0" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="22.5" xlink:href="#terminal" y="44.60000000000001"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="33" y1="29.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="16" y1="40.5" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="27.41094883543335" x2="32.88823024054981" y1="18.42541949757221" y2="18.42541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.66022336769755" x2="22.66022336769755" y1="44.33333333333334" y2="0.2499999999999964"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="13.5" y1="37.5" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36" x2="36" y1="31.25" y2="36.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36" x2="39" y1="31.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="15" y1="41.5" y2="41.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="14" y1="42.5" y2="42.5"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.54,30.42) scale(1,1) translate(0,0)" width="6.08" x="10.5" y="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36" x2="39" y1="36.25" y2="34.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.96058610156545" x2="22.65436235204273" y1="18.42541949757221" y2="18.42541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="12.5" y1="31.25" y2="25.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="14.5" y1="31.25" y2="25.25"/>
   <path d="M 22.5 18.5 L 13.5 18.5 L 13.5 30.5 L 13.5 30.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="30" y1="26.25" y2="29.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.00545055364641" x2="30.00545055364641" y1="18.54138864447597" y2="24.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="27" y1="29.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.96383161512026" x2="39.91666666666666" y1="18.49758812251703" y2="18.49758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.85940244368076" x2="32.85940244368076" y1="18.49758812251703" y2="18.49758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="39.86598892707138" x2="39.86598892707138" y1="15.25" y2="21.74517624503405"/>
   <ellipse cx="30.01" cy="29.32" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="37.05" cy="33.58" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="30.26" cy="37.58" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="32.85940244368077" x2="32.85940244368077" y1="15.98325293741726" y2="21.08409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="41.01910080183274" x2="41.01910080183274" y1="16.51295093653441" y2="20.84306843322376"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="41.88393470790376" x2="41.88393470790376" y1="17.41505874834466" y2="19.39969593432727"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="25.07589728904158" x2="25.07589728904158" y1="18.49758812251703" y2="18.49758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="24.99256395570825" x2="24.99256395570825" y1="15.83333333333333" y2="20.93417232847779"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="35.02148720885832" x2="35.02148720885832" y1="15.98325293741726" y2="21.08409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="27.23798205421914" x2="27.23798205421914" y1="15.98325293741726" y2="21.08409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.08333333333334" x2="33.08333333333334" y1="38" y2="40"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.08333333333334" x2="27.08333333333334" y1="38" y2="40"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.08333333333334" x2="30.08333333333334" y1="35" y2="38"/>
  </symbol>
  <symbol id="ACLineSegment:线路电压互感器_0" viewBox="0,0,35,40">
   <use terminal-index="0" type="0" x="14.20833333333333" xlink:href="#terminal" y="38.6908407585088"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.50545055364641" x2="21.50545055364641" y1="13.04138864447597" y2="19.16666666666667"/>
   <path d="M 14 13 L 5 13 L 5 25 L 5 25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.91094883543335" x2="24.38823024054981" y1="12.92541949757221" y2="12.92541949757221"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.04,24.92) scale(1,1) translate(0,0)" width="6.08" x="2" y="17.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="4" y1="25.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="5.5" y1="37" y2="37"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="6" y1="25.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="6.5" y1="36" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="32" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="7.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.46058610156545" x2="14.15436235204273" y1="12.92541949757221" y2="12.92541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16022336769755" x2="14.16022336769755" y1="38.83333333333334" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.46383161512025" x2="31.41666666666666" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.36598892707138" x2="31.36598892707138" y1="9.749999999999996" y2="16.24517624503405"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.35940244368076" x2="24.35940244368076" y1="12.99758812251703" y2="12.99758812251703"/>
   <ellipse cx="21.51" cy="23.82" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="28.55" cy="28.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.76" cy="32.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="24.35940244368077" x2="24.35940244368077" y1="10.48325293741726" y2="15.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.51910080183274" x2="32.51910080183274" y1="11.01295093653441" y2="15.34306843322376"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="33.38393470790376" x2="33.38393470790376" y1="11.91505874834466" y2="13.89969593432727"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.57589728904158" x2="16.57589728904158" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="26.52148720885832" x2="26.52148720885832" y1="10.48325293741726" y2="15.5840919325617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="16.49256395570825" x2="16.49256395570825" y1="10.33333333333333" y2="15.43417232847779"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="18.73798205421914" x2="18.73798205421914" y1="10.48325293741726" y2="15.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.91666666666667" x2="23.91666666666667" y1="24.08333333333334" y2="24.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.33333333333334" x2="31.33333333333334" y1="28.08333333333334" y2="28.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="24.58333333333334" y1="32" y2="32"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id=":220kV等值机_0" viewBox="0,0,35,40">
   <use terminal-index="0" type="0" x="14.91666666666667" xlink:href="#terminal" y="38.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.25000000000001" x2="31.25000000000001" y1="25.58333333333334" y2="27.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25000000000001" x2="22.25000000000001" y1="20.58333333333334" y2="23.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="4.75" y1="25.58333333333334" y2="19.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25000000000001" x2="25.25000000000001" y1="23.58333333333334" y2="25.58333333333334"/>
   <path d="M 14.75 12.8333 L 5.75 12.8333 L 5.75 24.8333 L 5.75 24.8333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25000000000001" x2="19.25" y1="23.58333333333334" y2="25.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.25000000000001" x2="28.25000000000001" y1="25.58333333333334" y2="30.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.25" x2="7.25" y1="35.83333333333333" y2="35.83333333333333"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.79,24.75) scale(1,1) translate(0,0)" width="6.08" x="2.75" y="17.58"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.25" x2="6.25" y1="36.83333333333333" y2="36.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="5.75" y1="31.83333333333333" y2="34.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.25000000000001" x2="31.25000000000001" y1="30.58333333333334" y2="28.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="8.25" y1="34.83333333333333" y2="34.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="6.75" y1="25.58333333333334" y2="19.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.91022336769756" x2="14.91022336769756" y1="38.66666666666667" y2="0.6666666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.21058610156545" x2="14.90436235204273" y1="12.75875283090555" y2="12.75875283090555"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.2554505536464" x2="22.2554505536464" y1="12.8747219778093" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.66094883543335" x2="25.13823024054981" y1="12.75875283090555" y2="12.75875283090555"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.21383161512025" x2="32.16666666666666" y1="12.83092145585036" y2="12.83092145585036"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="25.10940244368076" x2="25.10940244368076" y1="12.83092145585036" y2="12.83092145585036"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.11598892707139" x2="32.11598892707139" y1="9.583333333333332" y2="16.07850957836739"/>
   <ellipse cx="22.26" cy="23.66" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="29.3" cy="27.91" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="22.51" cy="31.92" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="25.10940244368077" x2="25.10940244368077" y1="10.31658627075059" y2="15.41742526589504"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="33.26910080183274" x2="33.26910080183274" y1="10.84628426986774" y2="15.1764017665571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="34.13393470790376" x2="34.13393470790376" y1="11.74839208167799" y2="13.73302926766061"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.32589728904158" x2="17.32589728904158" y1="12.83092145585036" y2="12.83092145585036"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="27.27148720885832" x2="27.27148720885832" y1="10.31658627075059" y2="15.41742526589504"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="17.24256395570825" x2="17.24256395570825" y1="10.16666666666667" y2="15.26750566181112"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="19.48798205421914" x2="19.48798205421914" y1="10.31658627075059" y2="15.41742526589504"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.33333333333334" x2="19.33333333333334" y1="32.33333333333333" y2="34.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.33333333333334" x2="22.33333333333334" y1="29.33333333333334" y2="32.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.33333333333334" x2="25.33333333333334" y1="32.33333333333333" y2="34.33333333333333"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变无融断_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <path d="M 10 9 L 10 0" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="26.83333333333333" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="10.8868007916835" y2="12.55855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="14.23031840279781" y2="12.55855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="220kV卡场变" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="39.16" xlink:href="logo.png" y="36.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="352" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,164.01,77.7136) scale(1,1) translate(-8.69612e-15,0)" writing-mode="lr" x="164.01" xml:space="preserve" y="81.20999999999999" zvalue="1161"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,192.857,77.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="192.86" xml:space="preserve" y="86.69" zvalue="1162">220kV卡场变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="291" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,69.625,334.25) scale(1,1) translate(0,0)" width="72.88" x="33.19" y="322.25" zvalue="1383"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.625,334.25) scale(1,1) translate(0,0)" writing-mode="lr" x="69.63" xml:space="preserve" y="338.75" zvalue="1383">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="316" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,180.25,415.25) scale(1,1) translate(0,0)" width="72.88" x="143.81" y="403.25" zvalue="1384"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,180.25,415.25) scale(1,1) translate(0,0)" writing-mode="lr" x="180.25" xml:space="preserve" y="419.75" zvalue="1384">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="314" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,69.625,415.25) scale(1,1) translate(0,0)" width="72.88" x="33.19" y="403.25" zvalue="1385"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.625,415.25) scale(1,1) translate(0,0)" writing-mode="lr" x="69.63" xml:space="preserve" y="419.75" zvalue="1385">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="306" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,69.625,374.75) scale(1,1) translate(0,0)" width="72.88" x="33.19" y="362.75" zvalue="1386"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.625,374.75) scale(1,1) translate(0,0)" writing-mode="lr" x="69.63" xml:space="preserve" y="379.25" zvalue="1386">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1615.34,698.078) scale(1,1) translate(0,0)" writing-mode="lr" x="1615.34" xml:space="preserve" y="702.58" zvalue="46">110kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1615.84,737.961) scale(1,1) translate(0,0)" writing-mode="lr" x="1615.84" xml:space="preserve" y="742.46" zvalue="47">110kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="650" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,469.644,809.714) scale(1,1) translate(0,0)" writing-mode="lr" x="469.64" xml:space="preserve" y="814.21" zvalue="48">132</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="652" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,439.403,741.857) scale(1,1) translate(0,0)" writing-mode="lr" x="439.4" xml:space="preserve" y="746.36" zvalue="49">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="653" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,474.909,741.857) scale(1,1) translate(0,0)" writing-mode="lr" x="474.91" xml:space="preserve" y="746.36" zvalue="52">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="651" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,483.948,782.853) scale(1,1) translate(0,0)" writing-mode="lr" x="483.95" xml:space="preserve" y="787.35" zvalue="54">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="648" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,457.779,857.909) scale(1,1) translate(0,0)" writing-mode="lr" x="457.78" xml:space="preserve" y="862.41" zvalue="56">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="649" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,483.584,836.424) scale(1,1) translate(0,0)" writing-mode="lr" x="483.58" xml:space="preserve" y="840.92" zvalue="58">60</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="640" stroke="rgb(255,255,255)" text-anchor="middle" x="448.6484375" xml:space="preserve" y="948.2017036103582" zvalue="60">110kV高河一级电站</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="640" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="448.6484375" xml:space="preserve" y="965.2017036103582" zvalue="60">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="641" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,483.584,882.482) scale(1,1) translate(0,0)" writing-mode="lr" x="483.58" xml:space="preserve" y="886.98" zvalue="64">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="675" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,909.991,617.667) scale(1,1) translate(0,0)" writing-mode="lr" x="909.99" xml:space="preserve" y="622.17" zvalue="77">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="680" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,883.502,685.524) scale(1,1) translate(0,0)" writing-mode="lr" x="883.5" xml:space="preserve" y="690.02" zvalue="78">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="681" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,916.281,685.524) scale(1,1) translate(0,0)" writing-mode="lr" x="916.28" xml:space="preserve" y="690.02" zvalue="81">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="679" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,932.281,646) scale(1,1) translate(2.05343e-13,0)" writing-mode="lr" x="932.28" xml:space="preserve" y="650.5" zvalue="83">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="676" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,899.366,571.762) scale(1,1) translate(0,0)" writing-mode="lr" x="899.37" xml:space="preserve" y="576.26" zvalue="85">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="678" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,935.918,593.338) scale(1,1) translate(2.05928e-13,0)" writing-mode="lr" x="935.92" xml:space="preserve" y="597.84" zvalue="87">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="677" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,929.91,546.944) scale(1,1) translate(0,0)" writing-mode="lr" x="929.91" xml:space="preserve" y="551.4400000000001" zvalue="89">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,805.091,440.432) scale(1,1) translate(0,1.88314e-13)" writing-mode="lr" x="805.0909090909091" xml:space="preserve" y="444.9318171414462" zvalue="103">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="687" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1301.26,618.576) scale(1,1) translate(0,0)" writing-mode="lr" x="1301.26" xml:space="preserve" y="623.08" zvalue="105">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="689" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1275.77,685.524) scale(1,1) translate(0,0)" writing-mode="lr" x="1275.77" xml:space="preserve" y="690.02" zvalue="106">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="690" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1308.55,685.524) scale(1,1) translate(0,0)" writing-mode="lr" x="1308.55" xml:space="preserve" y="690.02" zvalue="109">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="686" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1320.55,645.091) scale(1,1) translate(0,0)" writing-mode="lr" x="1320.55" xml:space="preserve" y="649.59" zvalue="111">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="688" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1294.33,573.108) scale(1,1) translate(0,0)" writing-mode="lr" x="1294.33" xml:space="preserve" y="577.61" zvalue="113">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="685" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1322.37,592.428) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.37" xml:space="preserve" y="596.9299999999999" zvalue="115">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="683" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1325.1,549.445) scale(1,1) translate(0,0)" writing-mode="lr" x="1325.1" xml:space="preserve" y="553.9400000000001" zvalue="117">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1365.5,439.018) scale(1,1) translate(0,1.40916e-13)" writing-mode="lr" x="1365.5" xml:space="preserve" y="443.5181807778098" zvalue="128">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,889.929,100.955) scale(1,1) translate(0,0)" writing-mode="lr" x="889.9299999999999" xml:space="preserve" y="105.45" zvalue="130">220kV卡盈线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1291.91,370.381) scale(1,1) translate(0,0)" writing-mode="lr" x="1291.91" xml:space="preserve" y="374.88" zvalue="136">3</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1303.88,320.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1303.88" xml:space="preserve" y="325.17" zvalue="140">232</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1292.38,265.381) scale(1,1) translate(0,0)" writing-mode="lr" x="1292.38" xml:space="preserve" y="269.88" zvalue="144">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1327.01,293.326) scale(1,1) translate(0,0)" writing-mode="lr" x="1327.01" xml:space="preserve" y="297.83" zvalue="149">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1325.32,345.455) scale(1,1) translate(0,0)" writing-mode="lr" x="1325.32" xml:space="preserve" y="349.95" zvalue="151">30</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1324.5,400.273) scale(1,1) translate(0,-2.59974e-13)" writing-mode="lr" x="1324.5" xml:space="preserve" y="404.77" zvalue="153">37</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,900.879,370.381) scale(1,1) translate(0,0)" writing-mode="lr" x="900.88" xml:space="preserve" y="374.88" zvalue="161">3</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,916.879,320.667) scale(1,1) translate(0,0)" writing-mode="lr" x="916.88" xml:space="preserve" y="325.17" zvalue="163">231</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,901.379,273.381) scale(1,1) translate(0,0)" writing-mode="lr" x="901.38" xml:space="preserve" y="277.88" zvalue="166">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,934.372,298.55) scale(1,1) translate(-2.05807e-13,0)" writing-mode="lr" x="934.37" xml:space="preserve" y="303.05" zvalue="169">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,934.781,343.636) scale(1,1) translate(0,0)" writing-mode="lr" x="934.78" xml:space="preserve" y="348.14" zvalue="171">30</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="151" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,933.872,399.364) scale(1,1) translate(2.05696e-13,0)" writing-mode="lr" x="933.87" xml:space="preserve" y="403.86" zvalue="174">37</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="186" stroke="rgb(255,255,255)" text-anchor="middle" x="847.1015625" xml:space="preserve" y="861.6143201458576" zvalue="189">110kVⅠ母电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="186" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="847.1015625" xml:space="preserve" y="878.6143201458576" zvalue="189">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,857.37,768) scale(1,1) translate(0,0)" writing-mode="lr" x="857.37" xml:space="preserve" y="772.5" zvalue="192">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,872.948,743.498) scale(1,1) translate(0,0)" writing-mode="lr" x="872.95" xml:space="preserve" y="748" zvalue="194">0</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,871.312,791.482) scale(1,1) translate(0,0)" writing-mode="lr" x="871.3099999999999" xml:space="preserve" y="795.98" zvalue="196">7</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="206" stroke="rgb(255,255,255)" text-anchor="middle" x="1122.921875" xml:space="preserve" y="861.1611951458576" zvalue="205">110kVⅡ母电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="206" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1122.921875" xml:space="preserve" y="878.1611951458576" zvalue="205">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="205" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1132.38,765.273) scale(1,1) translate(0,0)" writing-mode="lr" x="1132.38" xml:space="preserve" y="769.77" zvalue="207">1902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1154.22,744.771) scale(1,1) translate(0,0)" writing-mode="lr" x="1154.22" xml:space="preserve" y="749.27" zvalue="209">0</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1154.4,787.832) scale(1,1) translate(0,0)" writing-mode="lr" x="1154.4" xml:space="preserve" y="792.33" zvalue="211">7</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="647" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,970.403,741.857) scale(1,1) translate(0,0)" writing-mode="lr" x="970.4" xml:space="preserve" y="746.36" zvalue="218">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="358" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1050.23,741.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1050.23" xml:space="preserve" y="746.36" zvalue="220">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="224" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1000.32,783.625) scale(1,1) translate(0,1.72221e-13)" writing-mode="lr" x="1000.324675324675" xml:space="preserve" y="788.125" zvalue="226">112</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,959.407,824.862) scale(1,1) translate(0,0)" writing-mode="lr" x="959.41" xml:space="preserve" y="829.36" zvalue="230">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1037.86,824.862) scale(1,1) translate(0,0)" writing-mode="lr" x="1037.86" xml:space="preserve" y="829.36" zvalue="232">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="495" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1076.98,253.489) scale(1,1) translate(-2.2987e-13,1.82897e-13)" writing-mode="lr" x="1076.983766407162" xml:space="preserve" y="257.9886360168455" zvalue="506">212</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="497" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,958.45,245.537) scale(1,1) translate(0,0)" writing-mode="lr" x="958.45" xml:space="preserve" y="250.04" zvalue="510">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="503" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1177.45,250.283) scale(1,1) translate(0,0)" writing-mode="lr" x="1177.45" xml:space="preserve" y="254.78" zvalue="514">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="508" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1014.04,277.009) scale(1,1) translate(0,0)" writing-mode="lr" x="1014.04" xml:space="preserve" y="281.51" zvalue="523">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="510" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1142.68,276.099) scale(1,1) translate(0,-3.54516e-13)" writing-mode="lr" x="1142.68" xml:space="preserve" y="280.6" zvalue="527">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="516" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,902.333,202.835) scale(1,1) translate(0,0)" writing-mode="lr" x="902.33" xml:space="preserve" y="207.34" zvalue="530">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="520" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,929.485,173.071) scale(1,1) translate(0,0)" writing-mode="lr" x="929.48" xml:space="preserve" y="177.57" zvalue="536">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="535" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,487.841,351.367) scale(1,1) translate(0,0)" writing-mode="lr" x="487.84" xml:space="preserve" y="355.87" zvalue="552">35kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="539" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1460,348.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1460" xml:space="preserve" y="353.14" zvalue="555">35kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="542" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,715.879,456.199) scale(1,1) translate(0,0)" writing-mode="lr" x="715.88" xml:space="preserve" y="460.7" zvalue="558">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="541" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,727.879,423.5) scale(1,1) translate(0,0)" writing-mode="lr" x="727.88" xml:space="preserve" y="428" zvalue="560">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="540" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,716.379,391.199) scale(1,1) translate(0,0)" writing-mode="lr" x="716.38" xml:space="preserve" y="395.7" zvalue="562">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="555" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,704.496,524.281) scale(1,1) translate(1.23723e-12,0)" writing-mode="lr" x="704.5" xml:space="preserve" y="528.78" zvalue="573">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="560" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1490.88,455.29) scale(1,1) translate(0,0)" writing-mode="lr" x="1490.88" xml:space="preserve" y="459.79" zvalue="576">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="559" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1502.88,423.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1502.88" xml:space="preserve" y="428" zvalue="578">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="558" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1491.38,390.29) scale(1,1) translate(0,0)" writing-mode="lr" x="1491.38" xml:space="preserve" y="394.79" zvalue="580">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="557" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1481.31,519.736) scale(1,1) translate(0,0)" writing-mode="lr" x="1481.31" xml:space="preserve" y="524.24" zvalue="585">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="585" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,961.636,494.273) scale(1,1) translate(0,0)" writing-mode="lr" x="961.64" xml:space="preserve" y="498.77" zvalue="606">2010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="589" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,822.636,546.091) scale(1,1) translate(0,0)" writing-mode="lr" x="822.64" xml:space="preserve" y="550.59" zvalue="609">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1215.91,494.182) scale(1,1) translate(1.06529e-12,0)" writing-mode="lr" x="1215.91" xml:space="preserve" y="498.68" zvalue="612">2020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1358,546.636) scale(1,1) translate(0,0)" writing-mode="lr" x="1358" xml:space="preserve" y="551.14" zvalue="613">1020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" x="546.2109375" xml:space="preserve" y="559.6434667760675" zvalue="616">35kV1号电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="546.2109375" xml:space="preserve" y="576.6434667760675" zvalue="616">抗器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,558.333,482.926) scale(1,1) translate(0,0)" writing-mode="lr" x="558.33" xml:space="preserve" y="487.43" zvalue="619">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,569.424,424.212) scale(1,1) translate(0,0)" writing-mode="lr" x="569.42" xml:space="preserve" y="428.71" zvalue="621">331</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,557.924,393.926) scale(1,1) translate(0,0)" writing-mode="lr" x="557.92" xml:space="preserve" y="398.43" zvalue="623">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,593.502,453.926) scale(1,1) translate(1.29897e-13,0)" writing-mode="lr" x="593.5" xml:space="preserve" y="458.43" zvalue="635">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" x="626.6640625" xml:space="preserve" y="560.5653417760675" zvalue="638">35kV2号电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="626.6640625" xml:space="preserve" y="577.5653417760675" zvalue="638">抗器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="191" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,638.788,483.835) scale(1,1) translate(0,0)" writing-mode="lr" x="638.79" xml:space="preserve" y="488.34" zvalue="640">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,649.879,423.5) scale(1,1) translate(0,0)" writing-mode="lr" x="649.88" xml:space="preserve" y="428" zvalue="642">332</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="189" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,638.379,394.835) scale(1,1) translate(0,0)" writing-mode="lr" x="638.38" xml:space="preserve" y="399.34" zvalue="644">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,671.232,454.926) scale(1,1) translate(0,0)" writing-mode="lr" x="671.23" xml:space="preserve" y="459.43" zvalue="650">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="290" stroke="rgb(255,255,255)" text-anchor="middle" x="558.546875" xml:space="preserve" y="235.3522735942494" zvalue="653">35kVⅠ母电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="290" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="558.546875" xml:space="preserve" y="252.3522735942494" zvalue="653">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="295" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,575.931,347.835) scale(1,1) translate(0,0)" writing-mode="lr" x="575.9299999999999" xml:space="preserve" y="352.34" zvalue="656">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="297" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,604.414,327.653) scale(1,1) translate(0,0)" writing-mode="lr" x="604.41" xml:space="preserve" y="332.15" zvalue="660">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="300" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,654.263,179.636) scale(1,1) translate(0,0)" writing-mode="lr" x="654.26" xml:space="preserve" y="184.14" zvalue="662">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="304" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,666.606,276.381) scale(1,1) translate(0,0)" writing-mode="lr" x="666.61" xml:space="preserve" y="280.88" zvalue="665">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="303" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,672.788,321.758) scale(1,1) translate(0,0)" writing-mode="lr" x="672.79" xml:space="preserve" y="326.26" zvalue="667">333</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,667.106,345.745) scale(1,1) translate(0,0)" writing-mode="lr" x="667.11" xml:space="preserve" y="350.24" zvalue="669">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="348" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,701.414,300.199) scale(1,1) translate(0,0)" writing-mode="lr" x="701.41" xml:space="preserve" y="304.7" zvalue="682">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="505" stroke="rgb(255,255,255)" text-anchor="middle" x="1634.8359375" xml:space="preserve" y="558.7372167760675" zvalue="685">35kV4号电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="505" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1634.8359375" xml:space="preserve" y="575.7372167760675" zvalue="685">抗器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="504" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1646.06,486.017) scale(1,1) translate(0,0)" writing-mode="lr" x="1646.06" xml:space="preserve" y="490.52" zvalue="687">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="494" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1658.06,423.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1658.06" xml:space="preserve" y="428" zvalue="689">336</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="493" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1646.56,393.017) scale(1,1) translate(0,0)" writing-mode="lr" x="1646.56" xml:space="preserve" y="397.52" zvalue="691">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="473" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1682.14,455.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1682.14" xml:space="preserve" y="460.44" zvalue="697">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="448" stroke="rgb(255,255,255)" text-anchor="middle" x="1570.8125" xml:space="preserve" y="233.5241485942494" zvalue="700">35kVⅡ母电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="448" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1570.8125" xml:space="preserve" y="250.5241485942494" zvalue="700">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="423" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1590.02,348.745) scale(1,1) translate(0,0)" writing-mode="lr" x="1590.02" xml:space="preserve" y="353.24" zvalue="703">3902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="398" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1617.6,325.835) scale(1,1) translate(0,0)" writing-mode="lr" x="1617.6" xml:space="preserve" y="330.34" zvalue="706">27</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="595" stroke="rgb(255,255,255)" text-anchor="middle" x="1558.0078125" xml:space="preserve" y="559.6434667760675" zvalue="710">35kV3号电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="595" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1558.0078125" xml:space="preserve" y="576.6434667760675" zvalue="710">抗器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="594" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1569.24,482.926) scale(1,1) translate(0,0)" writing-mode="lr" x="1569.24" xml:space="preserve" y="487.43" zvalue="712">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="593" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1581.24,423.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1581.24" xml:space="preserve" y="428" zvalue="714">335</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="592" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1569.74,393.926) scale(1,1) translate(0,0)" writing-mode="lr" x="1569.74" xml:space="preserve" y="398.43" zvalue="716">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="591" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1605.32,455.832) scale(1,1) translate(0,0)" writing-mode="lr" x="1605.32" xml:space="preserve" y="460.33" zvalue="721">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="310" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1705.73,94.4091) scale(1,1) translate(0,0)" writing-mode="lr" x="1705.73" xml:space="preserve" y="98.91" zvalue="756">10kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="305" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1731.61,202.53) scale(1,1) translate(0,0)" writing-mode="lr" x="1731.61" xml:space="preserve" y="207.03" zvalue="760">031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1724.11,247.426) scale(1,1) translate(0,0)" writing-mode="lr" x="1724.11" xml:space="preserve" y="251.93" zvalue="762">1</text>
  <rect fill="none" fill-opacity="0" height="250.91" id="635" stroke="rgb(255,255,255)" stroke-dasharray="6 2" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1770,186.182) scale(1,1) translate(0,-1.34842e-14)" width="254.55" x="1642.73" y="60.73" zvalue="773"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="636" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1813.59,145.795) scale(1,1) translate(0,-1.05734e-13)" writing-mode="lr" x="1813.590909090909" xml:space="preserve" y="150.2954543720594" zvalue="775">外接站用电示意图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="639" stroke="rgb(255,255,255)" text-anchor="middle" x="1818.265625" xml:space="preserve" y="205.3877840909091" zvalue="777">勐嘎四级站10kV鲁苗村专</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="639" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1818.265625" xml:space="preserve" y="222.3877840909091" zvalue="777">线卡场变T线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="663" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1080.22,211.918) scale(1,1) translate(-2.32308e-13,0)" writing-mode="lr" x="1080.22" xml:space="preserve" y="216.42" zvalue="793">220kV外桥</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="691" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1367.32,456.291) scale(1,1) translate(0,4.88897e-14)" writing-mode="lr" x="1367.318181818182" xml:space="preserve" y="460.7909080505371" zvalue="808">180MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="692" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,804.545,459.523) scale(1,1) translate(0,9.8396e-14)" writing-mode="lr" x="804.5454545454546" xml:space="preserve" y="464.0227262323552" zvalue="810">180MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="727" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,599.644,809.714) scale(1,1) translate(0,0)" writing-mode="lr" x="599.64" xml:space="preserve" y="814.21" zvalue="842">133</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="726" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,569.403,741.857) scale(1,1) translate(0,0)" writing-mode="lr" x="569.4" xml:space="preserve" y="746.36" zvalue="843">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="725" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,604.909,741.857) scale(1,1) translate(0,0)" writing-mode="lr" x="604.91" xml:space="preserve" y="746.36" zvalue="845">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="724" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,613.948,782.853) scale(1,1) translate(0,0)" writing-mode="lr" x="613.95" xml:space="preserve" y="787.35" zvalue="847">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="723" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,587.779,857.909) scale(1,1) translate(0,0)" writing-mode="lr" x="587.78" xml:space="preserve" y="862.41" zvalue="849">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="722" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,613.584,836.424) scale(1,1) translate(0,0)" writing-mode="lr" x="613.58" xml:space="preserve" y="840.92" zvalue="851">60</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="721" stroke="rgb(255,255,255)" text-anchor="middle" x="575.9765625" xml:space="preserve" y="948.2017036103582" zvalue="853">110kV高河三级</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="721" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="575.9765625" xml:space="preserve" y="965.2017036103582" zvalue="853">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="720" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,613.584,882.482) scale(1,1) translate(0,0)" writing-mode="lr" x="613.58" xml:space="preserve" y="886.98" zvalue="855">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="754" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,729.644,809.714) scale(1,1) translate(0,0)" writing-mode="lr" x="729.64" xml:space="preserve" y="814.21" zvalue="870">134</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="753" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,699.403,741.857) scale(1,1) translate(0,0)" writing-mode="lr" x="699.4" xml:space="preserve" y="746.36" zvalue="871">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="752" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,734.909,741.857) scale(1,1) translate(0,0)" writing-mode="lr" x="734.91" xml:space="preserve" y="746.36" zvalue="873">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="751" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,743.948,782.853) scale(1,1) translate(0,0)" writing-mode="lr" x="743.95" xml:space="preserve" y="787.35" zvalue="875">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="750" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,717.779,857.909) scale(1,1) translate(0,0)" writing-mode="lr" x="717.78" xml:space="preserve" y="862.41" zvalue="877">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="749" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,743.584,836.424) scale(1,1) translate(0,0)" writing-mode="lr" x="743.58" xml:space="preserve" y="840.92" zvalue="879">60</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="748" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,704.948,951.205) scale(1,1) translate(0,-1.24234e-12)" writing-mode="lr" x="704.9479421962067" xml:space="preserve" y="955.7045596543846" zvalue="881">110kV卡嘎典线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="747" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,743.584,882.482) scale(1,1) translate(0,0)" writing-mode="lr" x="743.58" xml:space="preserve" y="886.98" zvalue="883">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="797" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1263.64,809.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1263.64" xml:space="preserve" y="814.21" zvalue="898">135</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="796" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1233.4,741.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1233.4" xml:space="preserve" y="746.36" zvalue="899">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="795" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1268.91,741.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1268.91" xml:space="preserve" y="746.36" zvalue="901">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="794" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1277.95,782.853) scale(1,1) translate(0,0)" writing-mode="lr" x="1277.95" xml:space="preserve" y="787.35" zvalue="903">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="793" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1251.78,857.909) scale(1,1) translate(0,0)" writing-mode="lr" x="1251.78" xml:space="preserve" y="862.41" zvalue="905">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="792" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1277.58,836.424) scale(1,1) translate(0,0)" writing-mode="lr" x="1277.58" xml:space="preserve" y="840.92" zvalue="907">60</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="791" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1238.95,951.205) scale(1,1) translate(3.96882e-13,-1.24234e-12)" writing-mode="lr" x="1238.947942196207" xml:space="preserve" y="955.7045591775475" zvalue="909">110kV卡典T线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="790" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1277.58,882.482) scale(1,1) translate(0,0)" writing-mode="lr" x="1277.58" xml:space="preserve" y="886.98" zvalue="911">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="789" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1393.64,809.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1393.64" xml:space="preserve" y="814.21" zvalue="925">136</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="788" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1363.4,741.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1363.4" xml:space="preserve" y="746.36" zvalue="926">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="787" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1398.91,741.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1398.91" xml:space="preserve" y="746.36" zvalue="928">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="786" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1407.95,782.853) scale(1,1) translate(0,0)" writing-mode="lr" x="1407.95" xml:space="preserve" y="787.35" zvalue="930">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="785" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1381.78,857.909) scale(1,1) translate(0,0)" writing-mode="lr" x="1381.78" xml:space="preserve" y="862.41" zvalue="932">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="784" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1407.58,836.424) scale(1,1) translate(0,0)" writing-mode="lr" x="1407.58" xml:space="preserve" y="840.92" zvalue="934">60</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="783" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1371.3,951.205) scale(1,1) translate(7.3494e-13,-1.24234e-12)" writing-mode="lr" x="1371.299132672397" xml:space="preserve" y="955.7045591775475" zvalue="936">110kV卡勐线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="782" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1407.58,882.482) scale(1,1) translate(0,0)" writing-mode="lr" x="1407.58" xml:space="preserve" y="886.98" zvalue="938">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="781" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1523.64,809.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1523.64" xml:space="preserve" y="814.21" zvalue="952">138</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="780" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1493.4,741.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1493.4" xml:space="preserve" y="746.36" zvalue="953">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="779" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1528.91,741.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1528.91" xml:space="preserve" y="746.36" zvalue="955">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="778" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1537.95,782.853) scale(1,1) translate(0,0)" writing-mode="lr" x="1537.95" xml:space="preserve" y="787.35" zvalue="957">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="777" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1511.78,857.909) scale(1,1) translate(0,0)" writing-mode="lr" x="1511.78" xml:space="preserve" y="862.41" zvalue="959">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="776" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1537.58,836.424) scale(1,1) translate(0,0)" writing-mode="lr" x="1537.58" xml:space="preserve" y="840.92" zvalue="961">60</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="775" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1506.97,951.205) scale(1,1) translate(3.22508e-13,0)" writing-mode="lr" x="1506.974998473263" xml:space="preserve" y="955.7045591775475" zvalue="963">110kV勐嘎四级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="774" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1537.58,882.482) scale(1,1) translate(0,0)" writing-mode="lr" x="1537.58" xml:space="preserve" y="886.98" zvalue="965">67</text>
  <line fill="none" id="349" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.85714285714334" x2="379.8571428571429" y1="154.8704926140824" y2="154.8704926140824" zvalue="1164"/>
  <line fill="none" id="345" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="380.8571428571431" x2="380.8571428571431" y1="13" y2="1043" zvalue="1165"/>
  <line fill="none" id="344" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.10714285714334" x2="381.1071428571429" y1="613.8704926140824" y2="613.8704926140824" zvalue="1166"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.10714285714312" x2="100.1071428571431" y1="927" y2="927"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.10714285714312" x2="100.1071428571431" y1="979.1632999999999" y2="979.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.10714285714312" x2="10.10714285714312" y1="927" y2="979.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.1071428571431" x2="100.1071428571431" y1="927" y2="979.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.1071428571431" x2="370.1071428571431" y1="927" y2="927"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.1071428571431" x2="370.1071428571431" y1="979.1632999999999" y2="979.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.1071428571431" x2="100.1071428571431" y1="927" y2="979.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.1071428571431" x2="370.1071428571431" y1="927" y2="979.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.10714285714312" x2="100.1071428571431" y1="979.16327" y2="979.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.10714285714312" x2="100.1071428571431" y1="1007.08167" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.10714285714312" x2="10.10714285714312" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.1071428571431" x2="100.1071428571431" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.1071428571431" x2="190.1071428571431" y1="979.16327" y2="979.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.1071428571431" x2="190.1071428571431" y1="1007.08167" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.1071428571431" x2="100.1071428571431" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.1071428571431" x2="190.1071428571431" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.1071428571432" x2="280.1071428571432" y1="979.16327" y2="979.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.1071428571432" x2="280.1071428571432" y1="1007.08167" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.1071428571432" x2="190.1071428571432" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.1071428571432" x2="280.1071428571432" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.1071428571431" x2="370.1071428571431" y1="979.16327" y2="979.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.1071428571431" x2="370.1071428571431" y1="1007.08167" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.1071428571431" x2="280.1071428571431" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.1071428571431" x2="370.1071428571431" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.10714285714312" x2="100.1071428571431" y1="1007.0816" y2="1007.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.10714285714312" x2="100.1071428571431" y1="1035" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.10714285714312" x2="10.10714285714312" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.1071428571431" x2="100.1071428571431" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.1071428571431" x2="190.1071428571431" y1="1007.0816" y2="1007.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.1071428571431" x2="190.1071428571431" y1="1035" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.1071428571431" x2="100.1071428571431" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.1071428571431" x2="190.1071428571431" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.1071428571432" x2="280.1071428571432" y1="1007.0816" y2="1007.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.1071428571432" x2="280.1071428571432" y1="1035" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.1071428571432" x2="190.1071428571432" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.1071428571432" x2="280.1071428571432" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.1071428571431" x2="370.1071428571431" y1="1007.0816" y2="1007.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.1071428571431" x2="370.1071428571431" y1="1035" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.1071428571431" x2="280.1071428571431" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.1071428571431" x2="370.1071428571431" y1="1007.0816" y2="1035"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="341" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,60.0536,955.25) scale(1,1) translate(0,0)" writing-mode="lr" x="27.11" xml:space="preserve" y="961.25" zvalue="1169">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="340" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,62.0536,994) scale(1,1) translate(0,0)" writing-mode="lr" x="37.11" xml:space="preserve" y="1000" zvalue="1170">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="339" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,284.429,994) scale(1,1) translate(-4.48213e-14,0)" writing-mode="lr" x="201.86" xml:space="preserve" y="1000" zvalue="1171">绘制日期    20200916</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="338" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.8571,1022) scale(1,1) translate(0,0)" writing-mode="lr" x="53.86" xml:space="preserve" y="1028" zvalue="1172">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="337" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.857,1022) scale(1,1) translate(0,0)" writing-mode="lr" x="235.86" xml:space="preserve" y="1028" zvalue="1173">更新日期</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.85714285714312" x2="194.8571428571431" y1="164" y2="164"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.85714285714312" x2="194.8571428571431" y1="190" y2="190"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.85714285714312" x2="13.85714285714312" y1="164" y2="190"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="194.8571428571431" y1="164" y2="190"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="375.8571428571431" y1="164" y2="164"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="375.8571428571431" y1="190" y2="190"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="194.8571428571431" y1="164" y2="190"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375.8571428571431" x2="375.8571428571431" y1="164" y2="190"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.85714285714312" x2="194.8571428571431" y1="190" y2="190"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.85714285714312" x2="194.8571428571431" y1="214.25" y2="214.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.85714285714312" x2="13.85714285714312" y1="190" y2="214.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="194.8571428571431" y1="190" y2="214.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="375.8571428571431" y1="190" y2="190"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="375.8571428571431" y1="214.25" y2="214.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="194.8571428571431" y1="190" y2="214.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375.8571428571431" x2="375.8571428571431" y1="190" y2="214.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.85714285714312" x2="194.8571428571431" y1="214.25" y2="214.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.85714285714312" x2="194.8571428571431" y1="238.5" y2="238.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.85714285714312" x2="13.85714285714312" y1="214.25" y2="238.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="194.8571428571431" y1="214.25" y2="238.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="375.8571428571431" y1="214.25" y2="214.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="375.8571428571431" y1="238.5" y2="238.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="194.8571428571431" y1="214.25" y2="238.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375.8571428571431" x2="375.8571428571431" y1="214.25" y2="238.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.85714285714312" x2="194.8571428571431" y1="238.5" y2="238.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.85714285714312" x2="194.8571428571431" y1="261.25" y2="261.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.85714285714312" x2="13.85714285714312" y1="238.5" y2="261.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="194.8571428571431" y1="238.5" y2="261.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="375.8571428571431" y1="238.5" y2="238.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="375.8571428571431" y1="261.25" y2="261.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="194.8571428571431" y1="238.5" y2="261.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375.8571428571431" x2="375.8571428571431" y1="238.5" y2="261.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.85714285714312" x2="194.8571428571431" y1="261.25" y2="261.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.85714285714312" x2="194.8571428571431" y1="284" y2="284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.85714285714312" x2="13.85714285714312" y1="261.25" y2="284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="194.8571428571431" y1="261.25" y2="284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="375.8571428571431" y1="261.25" y2="261.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="375.8571428571431" y1="284" y2="284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="194.8571428571431" y1="261.25" y2="284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375.8571428571431" x2="375.8571428571431" y1="261.25" y2="284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.85714285714312" x2="194.8571428571431" y1="284" y2="284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.85714285714312" x2="194.8571428571431" y1="306.75" y2="306.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.85714285714312" x2="13.85714285714312" y1="284" y2="306.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="194.8571428571431" y1="284" y2="306.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="375.8571428571431" y1="284" y2="284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="375.8571428571431" y1="306.75" y2="306.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.8571428571431" x2="194.8571428571431" y1="284" y2="306.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375.8571428571431" x2="375.8571428571431" y1="284" y2="306.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="335" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.8571,178) scale(1,1) translate(0,0)" writing-mode="lr" x="51.86" xml:space="preserve" y="182.5" zvalue="1175">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="334" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231.857,178) scale(1,1) translate(0,0)" writing-mode="lr" x="231.86" xml:space="preserve" y="182.5" zvalue="1176">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="333" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.5446,203.25) scale(1,1) translate(0,0)" writing-mode="lr" x="54.54" xml:space="preserve" y="207.75" zvalue="1177">#1主变高频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="332" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.607,203) scale(1,1) translate(0,0)" writing-mode="lr" x="237.61" xml:space="preserve" y="207.5" zvalue="1178">#1主变高频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="331" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.0446,274) scale(1,1) translate(0,0)" writing-mode="lr" x="67.04000000000001" xml:space="preserve" y="278.5" zvalue="1179">220kV#1主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="330" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,250.607,273.5) scale(1,1) translate(0,0)" writing-mode="lr" x="250.61" xml:space="preserve" y="278" zvalue="1180">220kV#2主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="329" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.0446,297) scale(1,1) translate(0,0)" writing-mode="lr" x="69.04000000000001" xml:space="preserve" y="301.5" zvalue="1181">220kV#1主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="328" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,249.357,296.5) scale(1,1) translate(0,0)" writing-mode="lr" x="249.36" xml:space="preserve" y="301" zvalue="1182">220kV#2主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="327" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.5446,228.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.54" xml:space="preserve" y="232.75" zvalue="1183">110kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="326" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,245.607,228) scale(1,1) translate(0,0)" writing-mode="lr" x="245.61" xml:space="preserve" y="232.5" zvalue="1184">110kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="325" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.7946,250.75) scale(1,1) translate(0,0)" writing-mode="lr" x="55.79" xml:space="preserve" y="255.25" zvalue="1185">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="324" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.607,251.75) scale(1,1) translate(0,0)" writing-mode="lr" x="237.61" xml:space="preserve" y="256.25" zvalue="1186">35kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="323" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,194.256,341.341) scale(1,1) translate(0,0)" writing-mode="lr" x="194.26" xml:space="preserve" y="345.84" zvalue="1187">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="322" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,299.256,341.341) scale(1,1) translate(0,0)" writing-mode="lr" x="299.26" xml:space="preserve" y="345.84" zvalue="1188">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.1071,635.25) scale(1,1) translate(0,0)" writing-mode="lr" x="80.10714285714312" xml:space="preserve" y="639.75" zvalue="1190">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.92888888888911" x2="63.5321888888891" y1="447.9999777777778" y2="447.9999777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.92888888888911" x2="63.5321888888891" y1="483.5369777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.92888888888911" x2="28.92888888888911" y1="447.9999777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.5321888888891" x2="63.5321888888891" y1="447.9999777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.53188888888917" x2="111.9873888888892" y1="447.9999777777778" y2="447.9999777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.53188888888917" x2="111.9873888888892" y1="483.5369777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.53188888888917" x2="63.53188888888917" y1="447.9999777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9873888888892" x2="111.9873888888892" y1="447.9999777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9871888888891" x2="158.5941888888891" y1="447.9999777777778" y2="447.9999777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9871888888891" x2="158.5941888888891" y1="483.5369777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9871888888891" x2="111.9871888888891" y1="447.9999777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="158.5941888888891" y1="447.9999777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="206.8656888888892" y1="447.9999777777778" y2="447.9999777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="206.8656888888892" y1="483.5369777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="158.5941888888891" y1="447.9999777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8656888888892" x2="206.8656888888892" y1="447.9999777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8655888888892" x2="259.1932888888891" y1="447.9999777777778" y2="447.9999777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8655888888892" x2="259.1932888888891" y1="483.5369777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8655888888892" x2="206.8655888888892" y1="447.9999777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1932888888891" x2="259.1932888888891" y1="447.9999777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1933888888891" x2="311.5210888888892" y1="447.9999777777778" y2="447.9999777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1933888888891" x2="311.5210888888892" y1="483.5369777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1933888888891" x2="259.1933888888891" y1="447.9999777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5210888888892" x2="311.5210888888892" y1="447.9999777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5218888888892" x2="363.8495888888891" y1="447.9999777777778" y2="447.9999777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5218888888892" x2="363.8495888888891" y1="483.5369777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5218888888892" x2="311.5218888888892" y1="447.9999777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.8495888888891" x2="363.8495888888891" y1="447.9999777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.92888888888911" x2="63.5321888888891" y1="483.5369777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.92888888888911" x2="63.5321888888891" y1="507.2406777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.92888888888911" x2="28.92888888888911" y1="483.5369777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.5321888888891" x2="63.5321888888891" y1="483.5369777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.53188888888917" x2="111.9873888888892" y1="483.5369777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.53188888888917" x2="111.9873888888892" y1="507.2406777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.53188888888917" x2="63.53188888888917" y1="483.5369777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9873888888892" x2="111.9873888888892" y1="483.5369777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9871888888891" x2="158.5941888888891" y1="483.5369777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9871888888891" x2="158.5941888888891" y1="507.2406777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9871888888891" x2="111.9871888888891" y1="483.5369777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="158.5941888888891" y1="483.5369777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="206.8656888888892" y1="483.5369777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="206.8656888888892" y1="507.2406777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="158.5941888888891" y1="483.5369777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8656888888892" x2="206.8656888888892" y1="483.5369777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8655888888892" x2="259.1932888888891" y1="483.5369777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8655888888892" x2="259.1932888888891" y1="507.2406777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8655888888892" x2="206.8655888888892" y1="483.5369777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1932888888891" x2="259.1932888888891" y1="483.5369777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1933888888891" x2="311.5210888888892" y1="483.5369777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1933888888891" x2="311.5210888888892" y1="507.2406777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1933888888891" x2="259.1933888888891" y1="483.5369777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5210888888892" x2="311.5210888888892" y1="483.5369777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5218888888892" x2="363.8495888888891" y1="483.5369777777778" y2="483.5369777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5218888888892" x2="363.8495888888891" y1="507.2406777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5218888888892" x2="311.5218888888892" y1="483.5369777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.8495888888891" x2="363.8495888888891" y1="483.5369777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.92888888888911" x2="63.5321888888891" y1="507.2406777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.92888888888911" x2="63.5321888888891" y1="530.9443777777777" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.92888888888911" x2="28.92888888888911" y1="507.2406777777778" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.5321888888891" x2="63.5321888888891" y1="507.2406777777778" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.53188888888917" x2="111.9873888888892" y1="507.2406777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.53188888888917" x2="111.9873888888892" y1="530.9443777777777" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.53188888888917" x2="63.53188888888917" y1="507.2406777777778" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9873888888892" x2="111.9873888888892" y1="507.2406777777778" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9871888888891" x2="158.5941888888891" y1="507.2406777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9871888888891" x2="158.5941888888891" y1="530.9443777777777" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9871888888891" x2="111.9871888888891" y1="507.2406777777778" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="158.5941888888891" y1="507.2406777777778" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="206.8656888888892" y1="507.2406777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="206.8656888888892" y1="530.9443777777777" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="158.5941888888891" y1="507.2406777777778" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8656888888892" x2="206.8656888888892" y1="507.2406777777778" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8655888888892" x2="259.1932888888891" y1="507.2406777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8655888888892" x2="259.1932888888891" y1="530.9443777777777" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8655888888892" x2="206.8655888888892" y1="507.2406777777778" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1932888888891" x2="259.1932888888891" y1="507.2406777777778" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1933888888891" x2="311.5210888888892" y1="507.2406777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1933888888891" x2="311.5210888888892" y1="530.9443777777777" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1933888888891" x2="259.1933888888891" y1="507.2406777777778" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5210888888892" x2="311.5210888888892" y1="507.2406777777778" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5218888888892" x2="363.8495888888891" y1="507.2406777777778" y2="507.2406777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5218888888892" x2="363.8495888888891" y1="530.9443777777777" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5218888888892" x2="311.5218888888892" y1="507.2406777777778" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.8495888888891" x2="363.8495888888891" y1="507.2406777777778" y2="530.9443777777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.92888888888911" x2="63.5321888888891" y1="530.9444377777778" y2="530.9444377777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.92888888888911" x2="63.5321888888891" y1="554.6481377777777" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.92888888888911" x2="28.92888888888911" y1="530.9444377777778" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.5321888888891" x2="63.5321888888891" y1="530.9444377777778" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.53188888888917" x2="111.9873888888892" y1="530.9444377777778" y2="530.9444377777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.53188888888917" x2="111.9873888888892" y1="554.6481377777777" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.53188888888917" x2="63.53188888888917" y1="530.9444377777778" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9873888888892" x2="111.9873888888892" y1="530.9444377777778" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9871888888891" x2="158.5941888888891" y1="530.9444377777778" y2="530.9444377777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9871888888891" x2="158.5941888888891" y1="554.6481377777777" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9871888888891" x2="111.9871888888891" y1="530.9444377777778" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="158.5941888888891" y1="530.9444377777778" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="206.8656888888892" y1="530.9444377777778" y2="530.9444377777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="206.8656888888892" y1="554.6481377777777" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="158.5941888888891" y1="530.9444377777778" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8656888888892" x2="206.8656888888892" y1="530.9444377777778" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8655888888892" x2="259.1932888888891" y1="530.9444377777778" y2="530.9444377777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8655888888892" x2="259.1932888888891" y1="554.6481377777777" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8655888888892" x2="206.8655888888892" y1="530.9444377777778" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1932888888891" x2="259.1932888888891" y1="530.9444377777778" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1933888888891" x2="311.5210888888892" y1="530.9444377777778" y2="530.9444377777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1933888888891" x2="311.5210888888892" y1="554.6481377777777" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1933888888891" x2="259.1933888888891" y1="530.9444377777778" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5210888888892" x2="311.5210888888892" y1="530.9444377777778" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5218888888892" x2="363.8495888888891" y1="530.9444377777778" y2="530.9444377777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5218888888892" x2="363.8495888888891" y1="554.6481377777777" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5218888888892" x2="311.5218888888892" y1="530.9444377777778" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.8495888888891" x2="363.8495888888891" y1="530.9444377777778" y2="554.6481377777777"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.92888888888911" x2="63.5321888888891" y1="554.6480777777778" y2="554.6480777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.92888888888911" x2="63.5321888888891" y1="578.3517777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.92888888888911" x2="28.92888888888911" y1="554.6480777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.5321888888891" x2="63.5321888888891" y1="554.6480777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.53188888888917" x2="111.9873888888892" y1="554.6480777777778" y2="554.6480777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.53188888888917" x2="111.9873888888892" y1="578.3517777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.53188888888917" x2="63.53188888888917" y1="554.6480777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9873888888892" x2="111.9873888888892" y1="554.6480777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9871888888891" x2="158.5941888888891" y1="554.6480777777778" y2="554.6480777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9871888888891" x2="158.5941888888891" y1="578.3517777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9871888888891" x2="111.9871888888891" y1="554.6480777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="158.5941888888891" y1="554.6480777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="206.8656888888892" y1="554.6480777777778" y2="554.6480777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="206.8656888888892" y1="578.3517777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="158.5941888888891" y1="554.6480777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8656888888892" x2="206.8656888888892" y1="554.6480777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8655888888892" x2="259.1932888888891" y1="554.6480777777778" y2="554.6480777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8655888888892" x2="259.1932888888891" y1="578.3517777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8655888888892" x2="206.8655888888892" y1="554.6480777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1932888888891" x2="259.1932888888891" y1="554.6480777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1933888888891" x2="311.5210888888892" y1="554.6480777777778" y2="554.6480777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1933888888891" x2="311.5210888888892" y1="578.3517777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1933888888891" x2="259.1933888888891" y1="554.6480777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5210888888892" x2="311.5210888888892" y1="554.6480777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5218888888892" x2="363.8495888888891" y1="554.6480777777778" y2="554.6480777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5218888888892" x2="363.8495888888891" y1="578.3517777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5218888888892" x2="311.5218888888892" y1="554.6480777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.8495888888891" x2="363.8495888888891" y1="554.6480777777778" y2="578.3517777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.92888888888911" x2="63.5321888888891" y1="578.3518777777778" y2="578.3518777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.92888888888911" x2="63.5321888888891" y1="602.0555777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="28.92888888888911" x2="28.92888888888911" y1="578.3518777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.5321888888891" x2="63.5321888888891" y1="578.3518777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.53188888888917" x2="111.9873888888892" y1="578.3518777777778" y2="578.3518777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.53188888888917" x2="111.9873888888892" y1="602.0555777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.53188888888917" x2="63.53188888888917" y1="578.3518777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9873888888892" x2="111.9873888888892" y1="578.3518777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9871888888891" x2="158.5941888888891" y1="578.3518777777778" y2="578.3518777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9871888888891" x2="158.5941888888891" y1="602.0555777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="111.9871888888891" x2="111.9871888888891" y1="578.3518777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="158.5941888888891" y1="578.3518777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="206.8656888888892" y1="578.3518777777778" y2="578.3518777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="206.8656888888892" y1="602.0555777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5941888888891" x2="158.5941888888891" y1="578.3518777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8656888888892" x2="206.8656888888892" y1="578.3518777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8655888888892" x2="259.1932888888891" y1="578.3518777777778" y2="578.3518777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8655888888892" x2="259.1932888888891" y1="602.0555777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206.8655888888892" x2="206.8655888888892" y1="578.3518777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1932888888891" x2="259.1932888888891" y1="578.3518777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1933888888891" x2="311.5210888888892" y1="578.3518777777778" y2="578.3518777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1933888888891" x2="311.5210888888892" y1="602.0555777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="259.1933888888891" x2="259.1933888888891" y1="578.3518777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5210888888892" x2="311.5210888888892" y1="578.3518777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5218888888892" x2="363.8495888888891" y1="578.3518777777778" y2="578.3518777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5218888888892" x2="363.8495888888891" y1="602.0555777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="311.5218888888892" x2="311.5218888888892" y1="578.3518777777778" y2="602.0555777777778"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.8495888888891" x2="363.8495888888891" y1="578.3518777777778" y2="602.0555777777778"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" x="186.53125" xml:space="preserve" y="463.0779656304019" zvalue="1192">110kVⅠ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="186.53125" xml:space="preserve" y="480.0779656304019" zvalue="1192">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" x="235.328125" xml:space="preserve" y="463.0779656304019" zvalue="1193">110kVⅡ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="235.328125" xml:space="preserve" y="480.0779656304019" zvalue="1193">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" x="87.859375" xml:space="preserve" y="463.0779656304019" zvalue="1194">#1主变高</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="87.859375" xml:space="preserve" y="480.0779656304019" zvalue="1194">压侧</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" x="135.359375" xml:space="preserve" y="463.0779656304019" zvalue="1195">#2主变高</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="135.359375" xml:space="preserve" y="480.0779656304019" zvalue="1195">压侧</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,286.604,466.078) scale(1,1) translate(0,9.9698e-14)" writing-mode="lr" x="286.6037452692576" xml:space="preserve" y="470.5779639614954" zvalue="1196">35kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,340.187,466.078) scale(1,1) translate(0,9.9698e-14)" writing-mode="lr" x="340.1870786025911" xml:space="preserve" y="470.5779639614952" zvalue="1197">35kVⅡ母</text>
  <text fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="33" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,47,495.92) scale(1,1) translate(0,0)" writing-mode="lr" x="47" xml:space="preserve" y="500.42" zvalue="1198">Uab</text>
  <text fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="32" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,47,519.872) scale(1,1) translate(0,0)" writing-mode="lr" x="47" xml:space="preserve" y="524.37" zvalue="1199">Ua</text>
  <text fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,47,543.824) scale(1,1) translate(0,0)" writing-mode="lr" x="47" xml:space="preserve" y="548.3200000000001" zvalue="1200">Ub</text>
  <text fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="30" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,47,591.729) scale(1,1) translate(0,1.93755e-13)" writing-mode="lr" x="47" xml:space="preserve" y="596.23" zvalue="1201">U0</text>
  <text fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,47,567.776) scale(1,1) translate(0,0)" writing-mode="lr" x="47" xml:space="preserve" y="572.28" zvalue="1206">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.429,954) scale(1,1) translate(-3.19427e-14,0)" writing-mode="lr" x="227.43" xml:space="preserve" y="960" zvalue="1390">KaChangZ-01-2019</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,144.857,995) scale(1,1) translate(0,0)" writing-mode="lr" x="144.86" xml:space="preserve" y="1001" zvalue="1392">李艳</text>
  <ellipse cx="1262.66" cy="319.66" fill="rgb(255,0,0)" fill-opacity="1" id="283" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1483"/>
  <ellipse cx="1077.66" cy="271.66" fill="rgb(255,0,0)" fill-opacity="1" id="285" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1485"/>
  <ellipse cx="868.66" cy="321.66" fill="rgb(255,0,0)" fill-opacity="1" id="286" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1487"/>
  <rect fill="none" height="21" id="288" stroke="rgb(255,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,806.5,417.5) scale(1,1) translate(0,0)" width="59" x="777" y="407" zvalue="1489"/>
  <text fill="rgb(255,170,0)" font-family="FangSong" font-size="13" id="288" stroke="rgb(255,170,0)" text-anchor="middle" transform="rotate(0,806.5,417.5) scale(1,1) translate(0,0)" writing-mode="lr" x="806.5" xml:space="preserve" y="422" zvalue="1489">强油循环</text>
  <rect fill="none" height="21" id="311" stroke="rgb(255,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1367.5,418.5) scale(1,1) translate(0,0)" width="59" x="1338" y="408" zvalue="1491"/>
  <text fill="rgb(255,170,0)" font-family="FangSong" font-size="13" id="311" stroke="rgb(255,170,0)" text-anchor="middle" transform="rotate(0,1367.5,418.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1367.5" xml:space="preserve" y="423" zvalue="1491">强油循环</text>
  <ellipse cx="1352.66" cy="859.66" fill="rgb(255,0,0)" fill-opacity="1" id="363" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1493"/>
  <ellipse cx="1221.66" cy="860.66" fill="rgb(255,0,0)" fill-opacity="1" id="364" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1495"/>
  <ellipse cx="556.66" cy="859.66" fill="rgb(255,0,0)" fill-opacity="1" id="365" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1497"/>
  <ellipse cx="426.66" cy="859.66" fill="rgb(255,0,0)" fill-opacity="1" id="366" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1499"/>
  <ellipse cx="1481.66" cy="861.66" fill="rgb(255,0,0)" fill-opacity="1" id="367" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1501"/>
  <ellipse cx="687.66" cy="862.66" fill="rgb(255,0,0)" fill-opacity="1" id="368" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1503"/>
  <ellipse cx="871.66" cy="573.66" fill="rgb(255,0,0)" fill-opacity="1" id="369" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1505"/>
  <ellipse cx="1263.66" cy="620.66" fill="rgb(255,0,0)" fill-opacity="1" id="370" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1507"/>
 </g>
 <g id="ButtonClass">
  <g href="全站公用_遥控.svg"><rect fill-opacity="0" height="24" width="72.88" x="33.19" y="322.25" zvalue="1383"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="143.81" y="403.25" zvalue="1384"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="33.19" y="403.25" zvalue="1385"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="33.19" y="362.75" zvalue="1386"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="856">
   <path class="kv110" d="M 419.73 712.71 L 1648.27 712.71" stroke-width="4" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674415345667" ObjectName="110kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674415345667"/></metadata>
  <path d="M 419.73 712.71 L 1648.27 712.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="855">
   <path class="kv110" d="M 418.73 723.14 L 1646.36 723.14" stroke-width="4" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674415411203" ObjectName="110kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674415411203"/></metadata>
  <path d="M 418.73 723.14 L 1646.36 723.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="534">
   <path class="kv35" d="M 506.23 371.32 L 736.25 371.32" stroke-width="4" zvalue="551"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674415476739" ObjectName="35kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674415476739"/></metadata>
  <path d="M 506.23 371.32 L 736.25 371.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="537">
   <path class="kv35" d="M 1450 371.32 L 1681.09 371.32" stroke-width="4" zvalue="554"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674415542275" ObjectName="35kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674415542275"/></metadata>
  <path d="M 1450 371.32 L 1681.09 371.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="637">
   <path class="kv10" d="M 1809.09 235.27 L 1834.55 235.27" stroke-width="6" zvalue="776"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674415607811" ObjectName="勐嘎四级站10kV鲁苗村专线卡场变T线"/>
   <cge:TPSR_Ref TObjectID="9288674415607811"/></metadata>
  <path d="M 1809.09 235.27 L 1834.55 235.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="118">
   <use class="kv110" height="20" transform="rotate(0,445.651,810.714) scale(1.5,-1.35) translate(-146.05,-1407.74)" width="10" x="438.1508317315045" xlink:href="#Breaker:开关_0" y="797.214285850525" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925181505539" ObjectName="110kV高河一级电站线132断路器"/>
   <cge:TPSR_Ref TObjectID="6473925181505539"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,445.651,810.714) scale(1.5,-1.35) translate(-146.05,-1407.74)" width="10" x="438.1508317315045" y="797.214285850525"/></g>
  <g id="158">
   <use class="kv110" height="20" transform="rotate(0,889.984,618.667) scale(1.5,1.35) translate(-294.161,-156.895)" width="10" x="882.484165064838" xlink:href="#Breaker:开关_0" y="605.1666665304275" zvalue="76"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925181571075" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925181571075"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,889.984,618.667) scale(1.5,1.35) translate(-294.161,-156.895)" width="10" x="882.484165064838" y="605.1666665304275"/></g>
  <g id="119">
   <use class="kv110" height="20" transform="rotate(0,1282.26,618.667) scale(1.5,1.35) translate(-424.919,-156.895)" width="10" x="1274.756892337565" xlink:href="#Breaker:开关_0" y="605.1666665304275" zvalue="104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925181636611" ObjectName="#2主变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473925181636611"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1282.26,618.667) scale(1.5,1.35) translate(-424.919,-156.895)" width="10" x="1274.756892337565" y="605.1666665304275"/></g>
  <g id="130">
   <use class="kv220" height="20" transform="rotate(0,1281.18,321.667) scale(1.5,1.35) translate(-424.56,-79.8951)" width="10" x="1273.67977815593" xlink:href="#Breaker:开关_0" y="308.1666665304274" zvalue="139"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925181702147" ObjectName="#2主变220kV侧232断路器"/>
   <cge:TPSR_Ref TObjectID="6473925181702147"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1281.18,321.667) scale(1.5,1.35) translate(-424.56,-79.8951)" width="10" x="1273.67977815593" y="308.1666665304274"/></g>
  <g id="170">
   <use class="kv220" height="20" transform="rotate(0,890.18,321.667) scale(1.5,1.35) translate(-294.227,-79.8951)" width="10" x="882.6797781559301" xlink:href="#Breaker:开关_0" y="308.1666665304274" zvalue="162"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925181767683" ObjectName="#1主变220kV侧231断路器"/>
   <cge:TPSR_Ref TObjectID="6473925181767683"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,890.18,321.667) scale(1.5,1.35) translate(-294.227,-79.8951)" width="10" x="882.6797781559301" y="308.1666665304274"/></g>
  <g id="223">
   <use class="kv110" height="20" transform="rotate(90,998.393,768.75) scale(1.5,1.35) translate(-330.298,-195.806)" width="10" x="990.8928571428571" xlink:href="#Breaker:母联开关_0" y="755.25" zvalue="225"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925181833219" ObjectName="110kV母联112断路器"/>
   <cge:TPSR_Ref TObjectID="6473925181833219"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,998.393,768.75) scale(1.5,1.35) translate(-330.298,-195.806)" width="10" x="990.8928571428571" y="755.25"/></g>
  <g id="492">
   <use class="kv220" height="20" transform="rotate(90,1076.48,232.386) scale(1.5,1.35) translate(-356.328,-56.7483)" width="10" x="1068.983766233766" xlink:href="#Breaker:母联开关_0" y="218.8863636363635" zvalue="505"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925181898755" ObjectName="220kV外桥212断路器"/>
   <cge:TPSR_Ref TObjectID="6473925181898755"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1076.48,232.386) scale(1.5,1.35) translate(-356.328,-56.7483)" width="10" x="1068.983766233766" y="218.8863636363635"/></g>
  <g id="546">
   <use class="kv35" height="20" transform="rotate(0,705.18,424.5) scale(1.5,1.35) translate(-232.56,-106.556)" width="10" x="697.6797781559301" xlink:href="#Breaker:开关_0" y="410.9999998493113" zvalue="559"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925181964291" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925181964291"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,705.18,424.5) scale(1.5,1.35) translate(-232.56,-106.556)" width="10" x="697.6797781559301" y="410.9999998493113"/></g>
  <g id="567">
   <use class="kv35" height="20" transform="rotate(0,1480.18,424.5) scale(1.5,1.35) translate(-490.893,-106.556)" width="10" x="1472.67977815593" xlink:href="#Breaker:开关_0" y="410.999999936009" zvalue="577"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925182029827" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925182029827"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1480.18,424.5) scale(1.5,1.35) translate(-490.893,-106.556)" width="10" x="1472.67977815593" y="410.999999936009"/></g>
  <g id="138">
   <use class="kv35" height="20" transform="rotate(0,546.33,424.5) scale(1.5,1.35) translate(-179.61,-106.556)" width="10" x="538.8296098814501" xlink:href="#Breaker:开关_0" y="411" zvalue="620"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925182095363" ObjectName="35kV1号电抗器331断路器"/>
   <cge:TPSR_Ref TObjectID="6473925182095363"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,546.33,424.5) scale(1.5,1.35) translate(-179.61,-106.556)" width="10" x="538.8296098814501" y="411"/></g>
  <g id="261">
   <use class="kv35" height="20" transform="rotate(0,627.18,424.5) scale(1.5,1.35) translate(-206.56,-106.556)" width="10" x="619.6797781559302" xlink:href="#Breaker:开关_0" y="410.9999999793578" zvalue="641"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925182160899" ObjectName="35kV2号电抗器332断路器"/>
   <cge:TPSR_Ref TObjectID="6473925182160899"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,627.18,424.5) scale(1.5,1.35) translate(-206.56,-106.556)" width="10" x="619.6797781559302" y="410.9999999793578"/></g>
  <g id="308">
   <use class="kv35" height="20" transform="rotate(0,654.089,322.758) scale(1.5,1.35) translate(-215.53,-80.1779)" width="10" x="646.5888690650211" xlink:href="#Breaker:开关_0" y="309.2575756213366" zvalue="666"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925182226435" ObjectName="35kV1号站用变333断路器"/>
   <cge:TPSR_Ref TObjectID="6473925182226435"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,654.089,322.758) scale(1.5,1.35) translate(-215.53,-80.1779)" width="10" x="646.5888690650211" y="309.2575756213366"/></g>
  <g id="569">
   <use class="kv35" height="20" transform="rotate(0,1635.36,424.5) scale(1.5,1.35) translate(-542.621,-106.556)" width="10" x="1627.861596337748" xlink:href="#Breaker:开关_0" y="410.9999999143345" zvalue="688"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925182291971" ObjectName="35kV4号电抗器336断路器"/>
   <cge:TPSR_Ref TObjectID="6473925182291971"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1635.36,424.5) scale(1.5,1.35) translate(-542.621,-106.556)" width="10" x="1627.861596337748" y="410.9999999143345"/></g>
  <g id="602">
   <use class="kv35" height="20" transform="rotate(0,1558.54,424.5) scale(1.5,1.35) translate(-517.014,-106.556)" width="10" x="1551.043414519567" xlink:href="#Breaker:开关_0" y="411.0000000660554" zvalue="713"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925182357507" ObjectName="35kV3号电抗器335断路器"/>
   <cge:TPSR_Ref TObjectID="6473925182357507"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1558.54,424.5) scale(1.5,1.35) translate(-517.014,-106.556)" width="10" x="1551.043414519567" y="411.0000000660554"/></g>
  <g id="622">
   <use class="kv10" height="20" transform="rotate(0,1704.91,203.53) scale(1.5,1.35) translate(-565.802,-49.2671)" width="10" x="1697.407050883203" xlink:href="#Breaker:开关_0" y="190.030304628017" zvalue="759"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925182423043" ObjectName="10kV2号站用变031断路器"/>
   <cge:TPSR_Ref TObjectID="6473925182423043"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1704.91,203.53) scale(1.5,1.35) translate(-565.802,-49.2671)" width="10" x="1697.407050883203" y="190.030304628017"/></g>
  <g id="746">
   <use class="kv110" height="20" transform="rotate(0,575.651,810.714) scale(1.5,-1.35) translate(-189.384,-1407.74)" width="10" x="568.1508317315045" xlink:href="#Breaker:开关_0" y="797.214285850525" zvalue="840"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925182488579" ObjectName="110kV高河三级线133断路器"/>
   <cge:TPSR_Ref TObjectID="6473925182488579"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,575.651,810.714) scale(1.5,-1.35) translate(-189.384,-1407.74)" width="10" x="568.1508317315045" y="797.214285850525"/></g>
  <g id="773">
   <use class="kv110" height="20" transform="rotate(0,705.651,810.714) scale(1.5,-1.35) translate(-232.717,-1407.74)" width="10" x="698.1508317315045" xlink:href="#Breaker:开关_0" y="797.214285850525" zvalue="868"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925182554115" ObjectName="110kV卡嘎典线134断路器"/>
   <cge:TPSR_Ref TObjectID="6473925182554115"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,705.651,810.714) scale(1.5,-1.35) translate(-232.717,-1407.74)" width="10" x="698.1508317315045" y="797.214285850525"/></g>
  <g id="854">
   <use class="kv110" height="20" transform="rotate(0,1239.65,810.714) scale(1.5,-1.35) translate(-410.717,-1407.74)" width="10" x="1232.150831731504" xlink:href="#Breaker:开关_0" y="797.214285850525" zvalue="896"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925182750723" ObjectName="110kV卡典T线135断路器"/>
   <cge:TPSR_Ref TObjectID="6473925182750723"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1239.65,810.714) scale(1.5,-1.35) translate(-410.717,-1407.74)" width="10" x="1232.150831731504" y="797.214285850525"/></g>
  <g id="835">
   <use class="kv110" height="20" transform="rotate(0,1369.65,810.714) scale(1.5,-1.35) translate(-454.05,-1407.74)" width="10" x="1362.150831731504" xlink:href="#Breaker:开关_0" y="797.214285850525" zvalue="923"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925182685187" ObjectName="110kV卡勐线136断路器"/>
   <cge:TPSR_Ref TObjectID="6473925182685187"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1369.65,810.714) scale(1.5,-1.35) translate(-454.05,-1407.74)" width="10" x="1362.150831731504" y="797.214285850525"/></g>
  <g id="816">
   <use class="kv110" height="20" transform="rotate(0,1499.65,810.714) scale(1.5,-1.35) translate(-497.384,-1407.74)" width="10" x="1492.150831731504" xlink:href="#Breaker:开关_0" y="797.214285850525" zvalue="950"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925182816259" ObjectName="110kV勐嘎四级线138断路器"/>
   <cge:TPSR_Ref TObjectID="6473925182816259"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1499.65,810.714) scale(1.5,-1.35) translate(-497.384,-1407.74)" width="10" x="1492.150831731504" y="797.214285850525"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="117">
   <use class="kv110" height="30" transform="rotate(0,429.286,742.857) scale(0.839827,-0.615873) translate(80.6726,-1954.8)" width="15" x="422.9870107111994" xlink:href="#Disconnector:刀闸_0" y="733.6190509569078" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454515752963" ObjectName="110kV高河一级电站线1321隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454515752963"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,429.286,742.857) scale(0.839827,-0.615873) translate(80.6726,-1954.8)" width="15" x="422.9870107111994" y="733.6190509569078"/></g>
  <g id="114">
   <use class="kv110" height="30" transform="rotate(0,463.429,742.857) scale(0.839827,-0.615873) translate(87.1844,-1954.8)" width="15" x="457.1298678540568" xlink:href="#Disconnector:刀闸_0" y="733.6190509569078" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454515687427" ObjectName="110kV高河一级电站线1322隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454515687427"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,463.429,742.857) scale(0.839827,-0.615873) translate(87.1844,-1954.8)" width="15" x="457.1298678540568" y="733.6190509569078"/></g>
  <g id="105">
   <use class="kv110" height="30" transform="rotate(0,445.571,858) scale(0.839827,-0.615873) translate(83.7786,-2256.91)" width="15" x="439.2727249969139" xlink:href="#Disconnector:刀闸_0" y="848.7619080997649" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454515490819" ObjectName="110kV高河一级电站线1326隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454515490819"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,445.571,858) scale(0.839827,-0.615873) translate(83.7786,-2256.91)" width="15" x="439.2727249969139" y="848.7619080997649"/></g>
  <g id="157">
   <use class="kv110" height="30" transform="rotate(0,873.619,686.524) scale(0.839827,0.615873) translate(165.416,422.43)" width="15" x="867.3203440445327" xlink:href="#Disconnector:刀闸_0" y="677.2857042721339" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454516342787" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454516342787"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,873.619,686.524) scale(0.839827,0.615873) translate(165.416,422.43)" width="15" x="867.3203440445327" y="677.2857042721339"/></g>
  <g id="154">
   <use class="kv110" height="30" transform="rotate(0,907.762,686.524) scale(0.839827,0.615873) translate(171.928,422.43)" width="15" x="901.4632011873902" xlink:href="#Disconnector:刀闸_0" y="677.285704272134" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454516277251" ObjectName="#1主变110kV侧1012隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454516277251"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,907.762,686.524) scale(0.839827,0.615873) translate(171.928,422.43)" width="15" x="901.4632011873902" y="677.285704272134"/></g>
  <g id="150">
   <use class="kv110" height="30" transform="rotate(0,890.846,572.762) scale(0.839827,0.615873) translate(168.702,351.476)" width="15" x="884.5476190476195" xlink:href="#Disconnector:刀闸_0" y="563.5238028480893" zvalue="84"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454516080643" ObjectName="#1主变110kV侧1016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454516080643"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,890.846,572.762) scale(0.839827,0.615873) translate(168.702,351.476)" width="15" x="884.5476190476195" y="563.5238028480893"/></g>
  <g id="116">
   <use class="kv110" height="30" transform="rotate(0,1265.89,686.524) scale(0.839827,0.615873) translate(240.231,422.43)" width="15" x="1259.59307131726" xlink:href="#Disconnector:刀闸_0" y="677.2857042721339" zvalue="105"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454516932611" ObjectName="#2主变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454516932611"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1265.89,686.524) scale(0.839827,0.615873) translate(240.231,422.43)" width="15" x="1259.59307131726" y="677.2857042721339"/></g>
  <g id="112">
   <use class="kv110" height="30" transform="rotate(0,1300.03,686.524) scale(0.839827,0.615873) translate(246.743,422.43)" width="15" x="1293.735928460118" xlink:href="#Disconnector:刀闸_0" y="677.285704272134" zvalue="108"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454516867075" ObjectName="#2主变110kV侧1022隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454516867075"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1300.03,686.524) scale(0.839827,0.615873) translate(246.743,422.43)" width="15" x="1293.735928460118" y="677.285704272134"/></g>
  <g id="107">
   <use class="kv110" height="30" transform="rotate(0,1282.18,571.381) scale(0.839827,0.615873) translate(243.337,350.615)" width="15" x="1275.878785602975" xlink:href="#Disconnector:刀闸_0" y="562.1428471292769" zvalue="112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454516670467" ObjectName="#2主变110kV侧1026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454516670467"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1282.18,571.381) scale(0.839827,0.615873) translate(243.337,350.615)" width="15" x="1275.878785602975" y="562.1428471292769"/></g>
  <g id="126">
   <use class="kv220" height="30" transform="rotate(0,1282.67,369.381) scale(0.839827,0.615873) translate(243.43,224.625)" width="15" x="1276.3681107001" xlink:href="#Disconnector:刀闸_0" y="360.1428471292768" zvalue="135"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454517129219" ObjectName="#2主变220kV侧2323隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454517129219"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1282.67,369.381) scale(0.839827,0.615873) translate(243.43,224.625)" width="15" x="1276.3681107001" y="360.1428471292768"/></g>
  <g id="134">
   <use class="kv220" height="30" transform="rotate(0,1281.18,266.381) scale(0.839827,0.615873) translate(243.146,160.383)" width="15" x="1274.878785602975" xlink:href="#Disconnector:刀闸_0" y="257.1428471292768" zvalue="143"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454517194755" ObjectName="#2主变220kV侧2322隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454517194755"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1281.18,266.381) scale(0.839827,0.615873) translate(243.146,160.383)" width="15" x="1274.878785602975" y="257.1428471292768"/></g>
  <g id="172">
   <use class="kv220" height="30" transform="rotate(0,890.177,369.381) scale(0.839827,0.615873) translate(168.574,224.625)" width="15" x="883.8787856029746" xlink:href="#Disconnector:刀闸_0" y="360.1428471292768" zvalue="159"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454518112259" ObjectName="#1主变220kV侧2313隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454518112259"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,890.177,369.381) scale(0.839827,0.615873) translate(168.574,224.625)" width="15" x="883.8787856029746" y="360.1428471292768"/></g>
  <g id="168">
   <use class="kv220" height="30" transform="rotate(0,890.177,274.381) scale(0.839827,0.615873) translate(168.574,165.372)" width="15" x="883.8787856029746" xlink:href="#Disconnector:刀闸_0" y="265.1428471292768" zvalue="165"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454518046723" ObjectName="#1主变220kV侧2311隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454518046723"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,890.177,274.381) scale(0.839827,0.615873) translate(168.574,165.372)" width="15" x="883.8787856029746" y="265.1428471292768"/></g>
  <g id="196">
   <use class="kv110" height="30" transform="rotate(0,838.571,766) scale(0.839827,-0.615873) translate(158.732,-2015.52)" width="15" x="832.2727249969139" xlink:href="#Disconnector:刀闸_0" y="756.7619080997649" zvalue="191"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454518571011" ObjectName="110kVⅠ母电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454518571011"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,838.571,766) scale(0.839827,-0.615873) translate(158.732,-2015.52)" width="15" x="832.2727249969139" y="756.7619080997649"/></g>
  <g id="213">
   <use class="kv110" height="30" transform="rotate(0,1114.85,765.091) scale(0.839827,-0.615873) translate(211.425,-2013.14)" width="15" x="1108.554110278299" xlink:href="#Disconnector:刀闸_0" y="755.8528171906739" zvalue="206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454518898691" ObjectName="110kVⅡ母电压互感器1902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454518898691"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1114.85,765.091) scale(0.839827,-0.615873) translate(211.425,-2013.14)" width="15" x="1108.554110278299" y="755.8528171906739"/></g>
  <g id="221">
   <use class="kv110" height="30" transform="rotate(0,960.286,742.857) scale(0.839827,-0.615873) translate(181.946,-1954.8)" width="15" x="953.9870107111993" xlink:href="#Disconnector:刀闸_0" y="733.6190509569078" zvalue="217"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454519095299" ObjectName="110kV母联1121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454519095299"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,960.286,742.857) scale(0.839827,-0.615873) translate(181.946,-1954.8)" width="15" x="953.9870107111993" y="733.6190509569078"/></g>
  <g id="219">
   <use class="kv110" height="30" transform="rotate(0,1038.43,742.857) scale(0.839827,-0.615873) translate(196.849,-1954.8)" width="15" x="1032.129867854057" xlink:href="#Disconnector:刀闸_0" y="733.6190509569078" zvalue="219"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454519029763" ObjectName="110kV母联1122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454519029763"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1038.43,742.857) scale(0.839827,-0.615873) translate(196.849,-1954.8)" width="15" x="1032.129867854057" y="733.6190509569078"/></g>
  <g id="496">
   <use class="kv220" height="30" transform="rotate(90,958.45,231.654) scale(0.839827,0.615873) translate(181.595,138.723)" width="15" x="952.1515128757019" xlink:href="#Disconnector:刀闸_0" y="222.415574402004" zvalue="509"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454519554051" ObjectName="220kV外桥2121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454519554051"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,958.45,231.654) scale(0.839827,0.615873) translate(181.595,138.723)" width="15" x="952.1515128757019" y="222.415574402004"/></g>
  <g id="500">
   <use class="kv220" height="30" transform="rotate(90,1177.45,232.399) scale(0.839827,0.615873) translate(223.363,139.188)" width="15" x="1171.151512875702" xlink:href="#Disconnector:刀闸_0" y="223.1613265291874" zvalue="513"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454519619587" ObjectName="220kV外桥2122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454519619587"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1177.45,232.399) scale(0.839827,0.615873) translate(223.363,139.188)" width="15" x="1171.151512875702" y="223.1613265291874"/></g>
  <g id="513">
   <use class="kv220" height="30" transform="rotate(0,889.814,203.835) scale(0.839827,0.615873) translate(168.505,121.372)" width="15" x="883.5151492393381" xlink:href="#Disconnector:刀闸_0" y="194.5973925838223" zvalue="529"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454519947267" ObjectName="220kV卡盈线2316隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454519947267"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,889.814,203.835) scale(0.839827,0.615873) translate(168.505,121.372)" width="15" x="883.5151492393381" y="194.5973925838223"/></g>
  <g id="547">
   <use class="kv35" height="30" transform="rotate(0,705.177,455.199) scale(0.839827,0.615873) translate(133.291,278.151)" width="15" x="698.8787856029746" xlink:href="#Disconnector:刀闸_0" y="445.9610289474587" zvalue="557"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454520209411" ObjectName="#1主变35kV侧3016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454520209411"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,705.177,455.199) scale(0.839827,0.615873) translate(133.291,278.151)" width="15" x="698.8787856029746" y="445.9610289474587"/></g>
  <g id="545">
   <use class="kv35" height="30" transform="rotate(0,705.177,392.199) scale(0.839827,0.615873) translate(133.291,238.857)" width="15" x="698.8787856029746" xlink:href="#Disconnector:刀闸_0" y="382.9610289474588" zvalue="561"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454520143875" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454520143875"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,705.177,392.199) scale(0.839827,0.615873) translate(133.291,238.857)" width="15" x="698.8787856029746" y="382.9610289474588"/></g>
  <g id="568">
   <use class="kv35" height="30" transform="rotate(0,1480.18,454.29) scale(0.839827,0.615873) translate(281.1,277.584)" width="15" x="1473.878785602974" xlink:href="#Disconnector:刀闸_0" y="445.0519380383678" zvalue="575"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454520602627" ObjectName="#2主变35kV侧3026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454520602627"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1480.18,454.29) scale(0.839827,0.615873) translate(281.1,277.584)" width="15" x="1473.878785602974" y="445.0519380383678"/></g>
  <g id="566">
   <use class="kv35" height="30" transform="rotate(0,1480.18,391.29) scale(0.839827,0.615873) translate(281.1,238.29)" width="15" x="1473.878785602974" xlink:href="#Disconnector:刀闸_0" y="382.0519380383679" zvalue="579"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454520537091" ObjectName="#2主变35kV侧3022隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454520537091"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1480.18,391.29) scale(0.839827,0.615873) translate(281.1,238.29)" width="15" x="1473.878785602974" y="382.0519380383679"/></g>
  <g id="161">
   <use class="kv35" height="30" transform="rotate(0,546.723,481.926) scale(0.839827,0.615873) translate(103.07,294.821)" width="15" x="540.4242401484291" xlink:href="#Disconnector:刀闸_0" y="472.6883016747315" zvalue="618"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454521520131" ObjectName="35kV1号电抗器3316隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454521520131"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,546.723,481.926) scale(0.839827,0.615873) translate(103.07,294.821)" width="15" x="540.4242401484291" y="472.6883016747315"/></g>
  <g id="136">
   <use class="kv35" height="30" transform="rotate(0,546.723,394.926) scale(0.839827,0.615873) translate(103.07,240.558)" width="15" x="540.4242401484291" xlink:href="#Disconnector:刀闸_0" y="385.6883016747315" zvalue="622"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454521454595" ObjectName="35kV1号电抗器3311隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454521454595"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,546.723,394.926) scale(0.839827,0.615873) translate(103.07,240.558)" width="15" x="540.4242401484291" y="385.6883016747315"/></g>
  <g id="270">
   <use class="kv35" height="30" transform="rotate(0,628.087,482.835) scale(0.839827,0.615873) translate(118.588,295.388)" width="15" x="621.7878765120656" xlink:href="#Disconnector:刀闸_0" y="473.5973925838223" zvalue="639"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454521913347" ObjectName="35kV2号电抗器3326隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454521913347"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,628.087,482.835) scale(0.839827,0.615873) translate(118.588,295.388)" width="15" x="621.7878765120656" y="473.5973925838223"/></g>
  <g id="259">
   <use class="kv35" height="30" transform="rotate(0,627.177,395.835) scale(0.839827,0.615873) translate(118.415,241.125)" width="15" x="620.8787856029746" xlink:href="#Disconnector:刀闸_0" y="386.5973925838223" zvalue="643"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454521847811" ObjectName="35kV2号电抗器3321隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454521847811"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,627.177,395.835) scale(0.839827,0.615873) translate(118.415,241.125)" width="15" x="620.8787856029746" y="386.5973925838223"/></g>
  <g id="292">
   <use class="kv35" height="30" transform="rotate(180,554.905,347.926) scale(-0.839827,-0.615873) translate(-1216.84,-918.62)" width="15" x="548.6060583302476" xlink:href="#Disconnector:刀闸_0" y="338.6883016747313" zvalue="655"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454522109955" ObjectName="35kVⅠ母电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454522109955"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,554.905,347.926) scale(-0.839827,-0.615873) translate(-1216.84,-918.62)" width="15" x="548.6060583302476" y="338.6883016747313"/></g>
  <g id="309">
   <use class="kv35" height="30" transform="rotate(0,654.087,276.29) scale(0.839827,0.615873) translate(123.547,166.563)" width="15" x="647.7878765120655" xlink:href="#Disconnector:刀闸_0" y="267.0519380383678" zvalue="664"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454522437635" ObjectName="35kV1号站用变3336隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454522437635"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,654.087,276.29) scale(0.839827,0.615873) translate(123.547,166.563)" width="15" x="647.7878765120655" y="267.0519380383678"/></g>
  <g id="307">
   <use class="kv35" height="30" transform="rotate(0,654.087,347.654) scale(0.839827,0.615873) translate(123.547,211.073)" width="15" x="647.7878765120656" xlink:href="#Disconnector:刀闸_0" y="338.4155744020042" zvalue="668"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454522372099" ObjectName="35kV1号站用变3331隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454522372099"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,654.087,347.654) scale(0.839827,0.615873) translate(123.547,211.073)" width="15" x="647.7878765120656" y="338.4155744020042"/></g>
  <g id="571">
   <use class="kv35" height="30" transform="rotate(0,1635.36,485.017) scale(0.839827,0.615873) translate(310.696,296.749)" width="15" x="1629.060603784793" xlink:href="#Disconnector:刀闸_0" y="475.7792107656405" zvalue="686"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454523092995" ObjectName="35kV4号电抗器3366隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454523092995"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1635.36,485.017) scale(0.839827,0.615873) translate(310.696,296.749)" width="15" x="1629.060603784793" y="475.7792107656405"/></g>
  <g id="565">
   <use class="kv35" height="30" transform="rotate(0,1635.36,394.017) scale(0.839827,0.615873) translate(310.696,239.991)" width="15" x="1629.060603784793" xlink:href="#Disconnector:刀闸_0" y="384.7792107656405" zvalue="690"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454523027459" ObjectName="35kV4号电抗器3362隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454523027459"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1635.36,394.017) scale(0.839827,0.615873) translate(310.696,239.991)" width="15" x="1629.060603784793" y="384.7792107656405"/></g>
  <g id="538">
   <use class="kv35" height="30" transform="rotate(180,1567.18,346.108) scale(-0.839827,-0.615873) translate(-3434.45,-913.85)" width="15" x="1560.878785602975" xlink:href="#Disconnector:刀闸_0" y="336.8701198565495" zvalue="701"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454522765315" ObjectName="35kVⅡ母电压互感器3902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454522765315"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1567.18,346.108) scale(-0.839827,-0.615873) translate(-3434.45,-913.85)" width="15" x="1560.878785602975" y="336.8701198565495"/></g>
  <g id="603">
   <use class="kv35" height="30" transform="rotate(0,1558.54,481.926) scale(0.839827,0.615873) translate(296.046,294.821)" width="15" x="1552.242421966611" xlink:href="#Disconnector:刀闸_0" y="472.6883016747315" zvalue="711"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454523420675" ObjectName="35kV3号电抗器3356隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454523420675"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1558.54,481.926) scale(0.839827,0.615873) translate(296.046,294.821)" width="15" x="1552.242421966611" y="472.6883016747315"/></g>
  <g id="601">
   <use class="kv35" height="30" transform="rotate(0,1558.54,394.926) scale(0.839827,0.615873) translate(296.046,240.558)" width="15" x="1552.242421966611" xlink:href="#Disconnector:刀闸_0" y="385.6883016747315" zvalue="715"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454523355139" ObjectName="35kV3号电抗器3352隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454523355139"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1558.54,394.926) scale(0.839827,0.615873) translate(296.046,240.558)" width="15" x="1552.242421966611" y="385.6883016747315"/></g>
  <g id="621">
   <use class="kv10" height="30" transform="rotate(0,1704.9,248.426) scale(0.839827,0.615873) translate(323.96,149.184)" width="15" x="1698.606058330247" xlink:href="#Disconnector:令克_0" y="239.1883034086845" zvalue="761"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454523551747" ObjectName="10kV2号站用变0311隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454523551747"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1704.9,248.426) scale(0.839827,0.615873) translate(323.96,149.184)" width="15" x="1698.606058330247" y="239.1883034086845"/></g>
  <g id="745">
   <use class="kv110" height="30" transform="rotate(0,559.286,742.857) scale(0.839827,-0.615873) translate(105.466,-1954.8)" width="15" x="552.9870107111994" xlink:href="#Disconnector:刀闸_0" y="733.6190509569078" zvalue="841"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454524338179" ObjectName="110kV高河三级线1331隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454524338179"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,559.286,742.857) scale(0.839827,-0.615873) translate(105.466,-1954.8)" width="15" x="552.9870107111994" y="733.6190509569078"/></g>
  <g id="744">
   <use class="kv110" height="30" transform="rotate(0,593.429,742.857) scale(0.839827,-0.615873) translate(111.978,-1954.8)" width="15" x="587.1298678540568" xlink:href="#Disconnector:刀闸_0" y="733.6190509569078" zvalue="844"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454524272643" ObjectName="110kV高河三级线1332隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454524272643"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,593.429,742.857) scale(0.839827,-0.615873) translate(111.978,-1954.8)" width="15" x="587.1298678540568" y="733.6190509569078"/></g>
  <g id="742">
   <use class="kv110" height="30" transform="rotate(0,575.571,858) scale(0.839827,-0.615873) translate(108.572,-2256.91)" width="15" x="569.2727249969139" xlink:href="#Disconnector:刀闸_0" y="848.7619080997649" zvalue="848"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454524076035" ObjectName="110kV高河三级线1336隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454524076035"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,575.571,858) scale(0.839827,-0.615873) translate(108.572,-2256.91)" width="15" x="569.2727249969139" y="848.7619080997649"/></g>
  <g id="772">
   <use class="kv110" height="30" transform="rotate(0,689.286,742.857) scale(0.839827,-0.615873) translate(130.26,-1954.8)" width="15" x="682.9870107111994" xlink:href="#Disconnector:刀闸_0" y="733.6190509569078" zvalue="869"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454524993539" ObjectName="110kV卡嘎典线1341隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454524993539"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,689.286,742.857) scale(0.839827,-0.615873) translate(130.26,-1954.8)" width="15" x="682.9870107111994" y="733.6190509569078"/></g>
  <g id="771">
   <use class="kv110" height="30" transform="rotate(0,723.429,742.857) scale(0.839827,-0.615873) translate(136.772,-1954.8)" width="15" x="717.1298678540568" xlink:href="#Disconnector:刀闸_0" y="733.6190509569078" zvalue="872"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454524928003" ObjectName="110kV卡嘎典线1342隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454524928003"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,723.429,742.857) scale(0.839827,-0.615873) translate(136.772,-1954.8)" width="15" x="717.1298678540568" y="733.6190509569078"/></g>
  <g id="769">
   <use class="kv110" height="30" transform="rotate(0,705.571,858) scale(0.839827,-0.615873) translate(133.366,-2256.91)" width="15" x="699.2727249969139" xlink:href="#Disconnector:刀闸_0" y="848.7619080997649" zvalue="876"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454524731395" ObjectName="110kV卡嘎典线1346隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454524731395"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,705.571,858) scale(0.839827,-0.615873) translate(133.366,-2256.91)" width="15" x="699.2727249969139" y="848.7619080997649"/></g>
  <g id="853">
   <use class="kv110" height="30" transform="rotate(0,1223.29,742.857) scale(0.839827,-0.615873) translate(232.105,-1954.8)" width="15" x="1216.987010711199" xlink:href="#Disconnector:刀闸_0" y="733.6190509569078" zvalue="897"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454526959619" ObjectName="110kV卡典T线1351隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454526959619"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1223.29,742.857) scale(0.839827,-0.615873) translate(232.105,-1954.8)" width="15" x="1216.987010711199" y="733.6190509569078"/></g>
  <g id="852">
   <use class="kv110" height="30" transform="rotate(0,1257.43,742.857) scale(0.839827,-0.615873) translate(238.617,-1954.8)" width="15" x="1251.129867854057" xlink:href="#Disconnector:刀闸_0" y="733.6190509569078" zvalue="900"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454526894083" ObjectName="110kV卡典T线1352隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454526894083"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1257.43,742.857) scale(0.839827,-0.615873) translate(238.617,-1954.8)" width="15" x="1251.129867854057" y="733.6190509569078"/></g>
  <g id="850">
   <use class="kv110" height="30" transform="rotate(0,1239.57,858) scale(0.839827,-0.615873) translate(235.211,-2256.91)" width="15" x="1233.272724996914" xlink:href="#Disconnector:刀闸_0" y="848.7619080997649" zvalue="904"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454526697475" ObjectName="110kV卡典T线1356隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454526697475"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1239.57,858) scale(0.839827,-0.615873) translate(235.211,-2256.91)" width="15" x="1233.272724996914" y="848.7619080997649"/></g>
  <g id="834">
   <use class="kv110" height="30" transform="rotate(0,1353.29,742.857) scale(0.839827,-0.615873) translate(256.899,-1954.8)" width="15" x="1346.987010711199" xlink:href="#Disconnector:刀闸_0" y="733.6190509569078" zvalue="924"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454526304259" ObjectName="110kV卡勐线1361隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454526304259"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1353.29,742.857) scale(0.839827,-0.615873) translate(256.899,-1954.8)" width="15" x="1346.987010711199" y="733.6190509569078"/></g>
  <g id="833">
   <use class="kv110" height="30" transform="rotate(0,1387.43,742.857) scale(0.839827,-0.615873) translate(263.411,-1954.8)" width="15" x="1381.129867854057" xlink:href="#Disconnector:刀闸_0" y="733.6190509569078" zvalue="927"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454526238723" ObjectName="110kV卡勐线1362隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454526238723"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1387.43,742.857) scale(0.839827,-0.615873) translate(263.411,-1954.8)" width="15" x="1381.129867854057" y="733.6190509569078"/></g>
  <g id="831">
   <use class="kv110" height="30" transform="rotate(0,1369.57,858) scale(0.839827,-0.615873) translate(260.005,-2256.91)" width="15" x="1363.272724996914" xlink:href="#Disconnector:刀闸_0" y="848.7619080997649" zvalue="931"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454526042115" ObjectName="110kV卡勐线1366隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454526042115"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1369.57,858) scale(0.839827,-0.615873) translate(260.005,-2256.91)" width="15" x="1363.272724996914" y="848.7619080997649"/></g>
  <g id="815">
   <use class="kv110" height="30" transform="rotate(0,1483.29,742.857) scale(0.839827,-0.615873) translate(281.693,-1954.8)" width="15" x="1476.987010711199" xlink:href="#Disconnector:刀闸_0" y="733.6190509569078" zvalue="951"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454527614979" ObjectName="110kV勐嘎四级线1381隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454527614979"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1483.29,742.857) scale(0.839827,-0.615873) translate(281.693,-1954.8)" width="15" x="1476.987010711199" y="733.6190509569078"/></g>
  <g id="814">
   <use class="kv110" height="30" transform="rotate(0,1517.43,742.857) scale(0.839827,-0.615873) translate(288.205,-1954.8)" width="15" x="1511.129867854057" xlink:href="#Disconnector:刀闸_0" y="733.6190509569078" zvalue="954"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454527549443" ObjectName="110kV勐嘎四级线1382隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454527549443"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1517.43,742.857) scale(0.839827,-0.615873) translate(288.205,-1954.8)" width="15" x="1511.129867854057" y="733.6190509569078"/></g>
  <g id="812">
   <use class="kv110" height="30" transform="rotate(0,1499.57,858) scale(0.839827,-0.615873) translate(284.799,-2256.91)" width="15" x="1493.272724996914" xlink:href="#Disconnector:刀闸_0" y="848.7619080997649" zvalue="958"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454527352835" ObjectName="110kV勐嘎四级线1386隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454527352835"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1499.57,858) scale(0.839827,-0.615873) translate(284.799,-2256.91)" width="15" x="1493.272724996914" y="848.7619080997649"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="108">
   <use class="kv110" height="20" transform="rotate(90,463.948,782.662) scale(0.94375,-0.835887) translate(27.3713,-1720.63)" width="10" x="459.229310658845" xlink:href="#GroundDisconnector:地刀_0" y="774.3034665133042" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454515621891" ObjectName="110kV高河一级电站线13217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454515621891"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,463.948,782.662) scale(0.94375,-0.835887) translate(27.3713,-1720.63)" width="10" x="459.229310658845" y="774.3034665133042"/></g>
  <g id="102">
   <use class="kv110" height="20" transform="rotate(90,462.675,837.143) scale(0.94375,-0.835887) translate(27.2954,-1840.29)" width="10" x="457.9565833605725" xlink:href="#GroundDisconnector:地刀_0" y="828.7839859938233" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454515425283" ObjectName="110kV高河一级电站线13260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454515425283"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,462.675,837.143) scale(0.94375,-0.835887) translate(27.2954,-1840.29)" width="10" x="457.9565833605725" y="828.7839859938233"/></g>
  <g id="93">
   <use class="kv110" height="20" transform="rotate(90,464.494,881.036) scale(0.94375,-0.835887) translate(27.4038,-1936.69)" width="10" x="459.7747651787548" xlink:href="#GroundDisconnector:地刀_0" y="872.6768430004414" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454519488515" ObjectName="110kV高河一级电站线13267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454519488515"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,464.494,881.036) scale(0.94375,-0.835887) translate(27.4038,-1936.69)" width="10" x="459.7747651787548" y="872.6768430004414"/></g>
  <g id="152">
   <use class="kv110" height="20" transform="rotate(270,913.19,645.81) scale(0.94375,0.835887) translate(54.1473,125.153)" width="10" x="908.4717349012694" xlink:href="#GroundDisconnector:地刀_0" y="637.4506523880118" zvalue="82"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454516211715" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454516211715"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,913.19,645.81) scale(0.94375,0.835887) translate(54.1473,125.153)" width="10" x="908.4717349012694" y="637.4506523880118"/></g>
  <g id="148">
   <use class="kv110" height="20" transform="rotate(270,913.19,592.238) scale(0.94375,0.835887) translate(54.1473,114.635)" width="10" x="908.4717348757242" xlink:href="#GroundDisconnector:地刀_0" y="583.8792238165834" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454516015107" ObjectName="#1主变110kV侧10160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454516015107"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,913.19,592.238) scale(0.94375,0.835887) translate(54.1473,114.635)" width="10" x="908.4717348757242" y="583.8792238165834"/></g>
  <g id="144">
   <use class="kv110" height="20" transform="rotate(270,906.274,548.887) scale(-0.94375,0.835887) translate(-1866.85,106.124)" width="10" x="901.5550682090578" xlink:href="#GroundDisconnector:地刀_0" y="540.528033476632" zvalue="88"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454515884035" ObjectName="#1主变110kV侧10167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454515884035"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,906.274,548.887) scale(-0.94375,0.835887) translate(-1866.85,106.124)" width="10" x="901.5550682090578" y="540.528033476632"/></g>
  <g id="110">
   <use class="kv110" height="20" transform="rotate(270,1301.46,645.81) scale(0.94375,0.835887) translate(77.2894,125.153)" width="10" x="1296.744462173997" xlink:href="#GroundDisconnector:地刀_0" y="637.4506523880118" zvalue="110"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454516801539" ObjectName="#2主变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454516801539"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1301.46,645.81) scale(0.94375,0.835887) translate(77.2894,125.153)" width="10" x="1296.744462173997" y="637.4506523880118"/></g>
  <g id="104">
   <use class="kv110" height="20" transform="rotate(270,1301.46,592.238) scale(0.94375,0.835887) translate(77.2894,114.635)" width="10" x="1296.744462148451" xlink:href="#GroundDisconnector:地刀_0" y="583.8792238165834" zvalue="114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454516604931" ObjectName="#2主变110kV侧10260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454516604931"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1301.46,592.238) scale(0.94375,0.835887) translate(77.2894,114.635)" width="10" x="1296.744462148451" y="583.8792238165834"/></g>
  <g id="101">
   <use class="kv110" height="20" transform="rotate(270,1301.46,548.345) scale(-0.94375,0.835887) translate(-2680.78,106.018)" width="10" x="1296.744462148452" xlink:href="#GroundDisconnector:地刀_0" y="539.9863668099655" zvalue="116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454516473859" ObjectName="#2主变110kV侧10267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454516473859"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1301.46,548.345) scale(-0.94375,0.835887) translate(-2680.78,106.018)" width="10" x="1296.744462148452" y="539.9863668099655"/></g>
  <g id="139">
   <use class="kv220" height="20" transform="rotate(270,1302.69,294.148) scale(0.94375,0.835887) translate(77.3623,56.11)" width="10" x="1297.968052048443" xlink:href="#GroundDisconnector:地刀_0" y="285.7888066949122" zvalue="148"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454517325827" ObjectName="#2主变220kV侧23227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454517325827"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1302.69,294.148) scale(0.94375,0.835887) translate(77.3623,56.11)" width="10" x="1297.968052048443" y="285.7888066949122"/></g>
  <g id="141">
   <use class="kv220" height="20" transform="rotate(270,1303.6,345.281) scale(0.94375,0.835887) translate(77.4165,66.1492)" width="10" x="1298.877142957534" xlink:href="#GroundDisconnector:地刀_0" y="336.9223786806674" zvalue="150"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454517456899" ObjectName="#2主变220kV侧23230接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454517456899"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1303.6,345.281) scale(0.94375,0.835887) translate(77.4165,66.1492)" width="10" x="1298.877142957534" y="336.9223786806674"/></g>
  <g id="143">
   <use class="kv220" height="20" transform="rotate(270,1304.6,399.281) scale(0.94375,0.835887) translate(77.4761,76.7513)" width="10" x="1299.877142957534" xlink:href="#GroundDisconnector:地刀_0" y="390.9223786806674" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454517587971" ObjectName="#2主变220kV侧23237接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454517587971"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1304.6,399.281) scale(0.94375,0.835887) translate(77.4761,76.7513)" width="10" x="1299.877142957534" y="390.9223786806674"/></g>
  <g id="166">
   <use class="kv220" height="20" transform="rotate(270,912.596,298.19) scale(0.94375,0.835887) translate(54.1119,56.9037)" width="10" x="907.8771439979058" xlink:href="#GroundDisconnector:地刀_0" y="289.8314695897583" zvalue="168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454517981187" ObjectName="#1主变220kV侧23117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454517981187"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,912.596,298.19) scale(0.94375,0.835887) translate(54.1119,56.9037)" width="10" x="907.8771439979058" y="289.8314695897583"/></g>
  <g id="165">
   <use class="kv220" height="20" transform="rotate(270,912.596,344.372) scale(0.94375,0.835887) translate(54.1119,65.9708)" width="10" x="907.8771440004916" xlink:href="#GroundDisconnector:地刀_0" y="336.0132877715765" zvalue="170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454517850115" ObjectName="#1主变220kV侧23130接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454517850115"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,912.596,344.372) scale(0.94375,0.835887) translate(54.1119,65.9708)" width="10" x="907.8771440004916" y="336.0132877715765"/></g>
  <g id="164">
   <use class="kv220" height="20" transform="rotate(270,912.596,399.281) scale(0.94375,0.835887) translate(54.1119,76.7513)" width="10" x="907.8771439112081" xlink:href="#GroundDisconnector:地刀_0" y="390.9223786806674" zvalue="172"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454517719043" ObjectName="#1主变220kV侧23137接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454517719043"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,912.596,399.281) scale(0.94375,0.835887) translate(54.1119,76.7513)" width="10" x="907.8771439112081" y="390.9223786806674"/></g>
  <g id="194">
   <use class="kv110" height="20" transform="rotate(90,853.857,745.143) scale(0.94375,-0.835887) translate(50.6109,-1638.22)" width="10" x="849.1384015423907" xlink:href="#GroundDisconnector:地刀_0" y="736.7839859938235" zvalue="193"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454518505475" ObjectName="110kVⅠ母电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454518505475"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,853.857,745.143) scale(0.94375,-0.835887) translate(50.6109,-1638.22)" width="10" x="849.1384015423907" y="736.7839859938235"/></g>
  <g id="192">
   <use class="kv110" height="20" transform="rotate(90,853.857,789.036) scale(0.94375,-0.835887) translate(50.6109,-1734.63)" width="10" x="849.1384015423912" xlink:href="#GroundDisconnector:地刀_0" y="780.6768430004415" zvalue="195"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454518374403" ObjectName="110kVⅠ母电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454518374403"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,853.857,789.036) scale(0.94375,-0.835887) translate(50.6109,-1734.63)" width="10" x="849.1384015423912" y="780.6768430004415"/></g>
  <g id="212">
   <use class="kv110" height="20" transform="rotate(90,1137.86,745.143) scale(0.94375,-0.835887) translate(67.538,-1638.22)" width="10" x="1133.138401542391" xlink:href="#GroundDisconnector:地刀_0" y="736.7839859938234" zvalue="208"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454518833155" ObjectName="110kVⅡ母电压互感器19020接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454518833155"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1137.86,745.143) scale(0.94375,-0.835887) translate(67.538,-1638.22)" width="10" x="1133.138401542391" y="736.7839859938234"/></g>
  <g id="211">
   <use class="kv110" height="20" transform="rotate(90,1138.77,788.127) scale(0.94375,-0.835887) translate(67.5922,-1732.63)" width="10" x="1134.047492451482" xlink:href="#GroundDisconnector:地刀_0" y="779.7677520913505" zvalue="210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454518702083" ObjectName="110kVⅡ母电压互感器19027接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454518702083"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1138.77,788.127) scale(0.94375,-0.835887) translate(67.5922,-1732.63)" width="10" x="1134.047492451482" y="779.7677520913505"/></g>
  <g id="227">
   <use class="kv110" height="20" transform="rotate(180,960.407,804.143) scale(0.94375,-0.835887) translate(56.9615,-1767.81)" width="10" x="955.6878693018642" xlink:href="#GroundDisconnector:地刀_0" y="795.7839969610781" zvalue="229"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454519226371" ObjectName="110kV母联11217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454519226371"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,960.407,804.143) scale(0.94375,-0.835887) translate(56.9615,-1767.81)" width="10" x="955.6878693018642" y="795.7839969610781"/></g>
  <g id="230">
   <use class="kv110" height="20" transform="rotate(180,1038.86,804.143) scale(0.94375,-0.835887) translate(61.6374,-1767.81)" width="10" x="1034.138401542391" xlink:href="#GroundDisconnector:地刀_0" y="795.7839969314076" zvalue="231"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454519357443" ObjectName="110kV母联11227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454519357443"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1038.86,804.143) scale(0.94375,-0.835887) translate(61.6374,-1767.81)" width="10" x="1034.138401542391" y="795.7839969314076"/></g>
  <g id="507">
   <use class="kv220" height="20" transform="rotate(0,1015.05,252.554) scale(0.94375,0.835887) translate(60.2184,47.9437)" width="10" x="1010.331688412079" xlink:href="#GroundDisconnector:地刀_0" y="244.1951059533946" zvalue="522"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454519750659" ObjectName="220kV外桥21217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454519750659"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1015.05,252.554) scale(0.94375,0.835887) translate(60.2184,47.9437)" width="10" x="1010.331688412079" y="244.1951059533946"/></g>
  <g id="512">
   <use class="kv220" height="20" transform="rotate(0,1143.69,251.645) scale(0.94375,0.835887) translate(67.8855,47.7653)" width="10" x="1138.968052048443" xlink:href="#GroundDisconnector:地刀_0" y="243.2860150443037" zvalue="525"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454519881731" ObjectName="220kV外桥21227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454519881731"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1143.69,251.645) scale(0.94375,0.835887) translate(67.8855,47.7653)" width="10" x="1138.968052048443" y="243.2860150443037"/></g>
  <g id="518">
   <use class="kv220" height="20" transform="rotate(270,907.667,174.352) scale(0.94375,0.835887) translate(53.8181,32.59)" width="10" x="902.9478500282407" xlink:href="#GroundDisconnector:地刀_0" y="165.9930857513743" zvalue="535"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454520078339" ObjectName="220kV卡盈线23167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454520078339"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,907.667,174.352) scale(0.94375,0.835887) translate(53.8181,32.59)" width="10" x="902.9478500282407" y="165.9930857513743"/></g>
  <g id="554">
   <use class="kv35" height="20" transform="rotate(0,705.505,500.736) scale(0.94375,0.835887) translate(41.7687,96.6702)" width="10" x="700.7862338666248" xlink:href="#GroundDisconnector:地刀_0" y="492.3769241352128" zvalue="572"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454520340483" ObjectName="#1主变35kV侧30167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454520340483"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,705.505,500.736) scale(0.94375,0.835887) translate(41.7687,96.6702)" width="10" x="700.7862338666248" y="492.3769241352128"/></g>
  <g id="562">
   <use class="kv35" height="20" transform="rotate(0,1479.6,499.827) scale(0.94375,0.835887) translate(87.9066,96.4917)" width="10" x="1474.877142957534" xlink:href="#GroundDisconnector:地刀_0" y="491.4678332261219" zvalue="584"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454520471555" ObjectName="#2主变35kV侧30267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454520471555"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1479.6,499.827) scale(0.94375,0.835887) translate(87.9066,96.4917)" width="10" x="1474.877142957534" y="491.4678332261219"/></g>
  <g id="584">
   <use class="kv220" height="40" transform="rotate(0,958.727,466.182) scale(0.909091,-0.909091) translate(94.0545,-980.8)" width="40" x="940.5454545454545" xlink:href="#GroundDisconnector:中性点地刀12_0" y="448" zvalue="605"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454520930307" ObjectName="#1主变220kV侧中性点2010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454520930307"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,958.727,466.182) scale(0.909091,-0.909091) translate(94.0545,-980.8)" width="40" x="940.5454545454545" y="448"/></g>
  <g id="587">
   <use class="kv110" height="40" transform="rotate(0,825.545,521.636) scale(-0.909091,-0.909091) translate(-1735.46,-1097.25)" width="40" x="807.3636363636364" xlink:href="#GroundDisconnector:中性点地刀12_0" y="503.4545454545455" zvalue="608"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454521061379" ObjectName="#1主变110kV侧中性点1010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454521061379"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,825.545,521.636) scale(-0.909091,-0.909091) translate(-1735.46,-1097.25)" width="40" x="807.3636363636364" y="503.4545454545455"/></g>
  <g id="43">
   <use class="kv220" height="40" transform="rotate(0,1217,465.182) scale(-0.909091,-0.909091) translate(-2557.52,-978.7)" width="40" x="1198.818181818182" xlink:href="#GroundDisconnector:中性点地刀12_0" y="447" zvalue="611"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454521192451" ObjectName="#2主变220kV侧中性点2020接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454521192451"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1217,465.182) scale(-0.909091,-0.909091) translate(-2557.52,-978.7)" width="40" x="1198.818181818182" y="447"/></g>
  <g id="51">
   <use class="kv110" height="40" transform="rotate(0,1355.09,522.545) scale(0.909091,-0.909091) translate(133.691,-1099.16)" width="40" x="1336.909090909091" xlink:href="#GroundDisconnector:中性点地刀12_0" y="504.3636363636364" zvalue="612"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454521323523" ObjectName="#2主变110kV侧中性点1020接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454521323523"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1355.09,522.545) scale(0.909091,-0.909091) translate(133.691,-1099.16)" width="40" x="1336.909090909091" y="504.3636363636364"/></g>
  <g id="175">
   <use class="kv35" height="20" transform="rotate(270,572.596,452.915) scale(-0.94375,0.835887) translate(-1179.6,87.2813)" width="10" x="567.8771429575338" xlink:href="#GroundDisconnector:地刀_0" y="444.5557805863926" zvalue="634"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454521651203" ObjectName="35kV1号电抗器33167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454521651203"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,572.596,452.915) scale(-0.94375,0.835887) translate(-1179.6,87.2813)" width="10" x="567.8771429575338" y="444.5557805863926"/></g>
  <g id="215">
   <use class="kv35" height="20" transform="rotate(270,653.05,453.827) scale(-0.94375,0.835887) translate(-1345.31,87.4604)" width="10" x="648.3316884120793" xlink:href="#GroundDisconnector:地刀_0" y="445.4678332261219" zvalue="649"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454521782275" ObjectName="35kV2号电抗器33267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454521782275"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,653.05,453.827) scale(-0.94375,0.835887) translate(-1345.31,87.4604)" width="10" x="648.3316884120793" y="445.4678332261219"/></g>
  <g id="296">
   <use class="kv35" height="20" transform="rotate(270,581.687,327.463) scale(-0.94375,0.835887) translate(-1198.32,62.6509)" width="10" x="576.9680520484432" xlink:href="#GroundDisconnector:地刀_0" y="319.1041968624856" zvalue="659"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454522241027" ObjectName="35kVⅠ母电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454522241027"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,581.687,327.463) scale(-0.94375,0.835887) translate(-1198.32,62.6509)" width="10" x="576.9680520484432" y="319.1041968624856"/></g>
  <g id="347">
   <use class="kv35" height="20" transform="rotate(270,679.05,299.099) scale(-0.94375,0.835887) translate(-1398.86,57.0822)" width="10" x="674.3316884120795" xlink:href="#GroundDisconnector:地刀_0" y="290.7405604988492" zvalue="681"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454522568707" ObjectName="35kV1号站用变33367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454522568707"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,679.05,299.099) scale(-0.94375,0.835887) translate(-1398.86,57.0822)" width="10" x="674.3316884120795" y="290.7405604988492"/></g>
  <g id="548">
   <use class="kv35" height="20" transform="rotate(270,1661.23,455.753) scale(-0.94375,0.835887) translate(-3421.76,87.8386)" width="10" x="1656.513506593898" xlink:href="#GroundDisconnector:地刀_0" y="447.3944705139081" zvalue="696"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454522961923" ObjectName="35kV4号电抗器33667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454522961923"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1661.23,455.753) scale(-0.94375,0.835887) translate(-3421.76,87.8386)" width="10" x="1656.513506593898" y="447.3944705139081"/></g>
  <g id="517">
   <use class="kv35" height="20" transform="rotate(270,1593.96,325.645) scale(-0.94375,0.835887) translate(-3283.2,62.294)" width="10" x="1589.240779321171" xlink:href="#GroundDisconnector:地刀_0" y="317.2860150443037" zvalue="705"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454522699779" ObjectName="35kVⅡ母电压互感器39027接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454522699779"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1593.96,325.645) scale(-0.94375,0.835887) translate(-3283.2,62.294)" width="10" x="1589.240779321171" y="317.2860150443037"/></g>
  <g id="597">
   <use class="kv35" height="20" transform="rotate(270,1584.41,455.642) scale(-0.94375,0.835887) translate(-3263.54,87.8168)" width="10" x="1579.695324775716" xlink:href="#GroundDisconnector:地刀_0" y="447.2832259036593" zvalue="720"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454523289603" ObjectName="35kV3号电抗器33567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454523289603"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1584.41,455.642) scale(-0.94375,0.835887) translate(-3263.54,87.8168)" width="10" x="1579.695324775716" y="447.2832259036593"/></g>
  <g id="743">
   <use class="kv110" height="20" transform="rotate(90,593.948,782.662) scale(0.94375,-0.835887) translate(35.1196,-1720.63)" width="10" x="589.229310658845" xlink:href="#GroundDisconnector:地刀_0" y="774.3034665133042" zvalue="846"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454524207107" ObjectName="110kV高河三级线13317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454524207107"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,593.948,782.662) scale(0.94375,-0.835887) translate(35.1196,-1720.63)" width="10" x="589.229310658845" y="774.3034665133042"/></g>
  <g id="741">
   <use class="kv110" height="20" transform="rotate(90,592.675,837.143) scale(0.94375,-0.835887) translate(35.0438,-1840.29)" width="10" x="587.9565833605725" xlink:href="#GroundDisconnector:地刀_0" y="828.7839859938233" zvalue="850"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454524010499" ObjectName="110kV高河三级线13360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454524010499"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,592.675,837.143) scale(0.94375,-0.835887) translate(35.0438,-1840.29)" width="10" x="587.9565833605725" y="828.7839859938233"/></g>
  <g id="739">
   <use class="kv110" height="20" transform="rotate(90,594.494,881.036) scale(0.94375,-0.835887) translate(35.1521,-1936.69)" width="10" x="589.7747651787548" xlink:href="#GroundDisconnector:地刀_0" y="872.6768430004414" zvalue="854"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454523813891" ObjectName="110kV高河三级线13367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454523813891"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,594.494,881.036) scale(0.94375,-0.835887) translate(35.1521,-1936.69)" width="10" x="589.7747651787548" y="872.6768430004414"/></g>
  <g id="770">
   <use class="kv110" height="20" transform="rotate(90,723.948,782.662) scale(0.94375,-0.835887) translate(42.868,-1720.63)" width="10" x="719.229310658845" xlink:href="#GroundDisconnector:地刀_0" y="774.3034665133042" zvalue="874"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454524862467" ObjectName="110kV卡嘎典线13417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454524862467"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,723.948,782.662) scale(0.94375,-0.835887) translate(42.868,-1720.63)" width="10" x="719.229310658845" y="774.3034665133042"/></g>
  <g id="768">
   <use class="kv110" height="20" transform="rotate(90,722.675,837.143) scale(0.94375,-0.835887) translate(42.7921,-1840.29)" width="10" x="717.9565833605725" xlink:href="#GroundDisconnector:地刀_0" y="828.7839859938233" zvalue="878"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454524665859" ObjectName="110kV卡嘎典线13460接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454524665859"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,722.675,837.143) scale(0.94375,-0.835887) translate(42.7921,-1840.29)" width="10" x="717.9565833605725" y="828.7839859938233"/></g>
  <g id="766">
   <use class="kv110" height="20" transform="rotate(90,724.494,881.036) scale(0.94375,-0.835887) translate(42.9005,-1936.69)" width="10" x="719.7747651787548" xlink:href="#GroundDisconnector:地刀_0" y="872.6768430004414" zvalue="882"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454524469251" ObjectName="110kV卡嘎典线13467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454524469251"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,724.494,881.036) scale(0.94375,-0.835887) translate(42.9005,-1936.69)" width="10" x="719.7747651787548" y="872.6768430004414"/></g>
  <g id="851">
   <use class="kv110" height="20" transform="rotate(90,1257.95,782.662) scale(0.94375,-0.835887) translate(74.6958,-1720.63)" width="10" x="1253.229310658845" xlink:href="#GroundDisconnector:地刀_0" y="774.3034665133042" zvalue="902"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454526828547" ObjectName="110kV卡典T线13517接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454526828547"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1257.95,782.662) scale(0.94375,-0.835887) translate(74.6958,-1720.63)" width="10" x="1253.229310658845" y="774.3034665133042"/></g>
  <g id="849">
   <use class="kv110" height="20" transform="rotate(90,1256.68,837.143) scale(0.94375,-0.835887) translate(74.6199,-1840.29)" width="10" x="1251.956583360572" xlink:href="#GroundDisconnector:地刀_0" y="828.7839859938233" zvalue="906"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454526631939" ObjectName="110kV卡典T线13560接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454526631939"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1256.68,837.143) scale(0.94375,-0.835887) translate(74.6199,-1840.29)" width="10" x="1251.956583360572" y="828.7839859938233"/></g>
  <g id="847">
   <use class="kv110" height="20" transform="rotate(90,1258.49,881.036) scale(0.94375,-0.835887) translate(74.7283,-1936.69)" width="10" x="1253.774765178755" xlink:href="#GroundDisconnector:地刀_0" y="872.6768430004414" zvalue="910"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454526435331" ObjectName="110kV卡典T线13567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454526435331"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1258.49,881.036) scale(0.94375,-0.835887) translate(74.7283,-1936.69)" width="10" x="1253.774765178755" y="872.6768430004414"/></g>
  <g id="832">
   <use class="kv110" height="20" transform="rotate(90,1387.95,782.662) scale(0.94375,-0.835887) translate(82.4441,-1720.63)" width="10" x="1383.229310658845" xlink:href="#GroundDisconnector:地刀_0" y="774.3034665133042" zvalue="929"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454526173187" ObjectName="110kV卡勐线13617接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454526173187"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1387.95,782.662) scale(0.94375,-0.835887) translate(82.4441,-1720.63)" width="10" x="1383.229310658845" y="774.3034665133042"/></g>
  <g id="830">
   <use class="kv110" height="20" transform="rotate(90,1386.68,837.143) scale(0.94375,-0.835887) translate(82.3683,-1840.29)" width="10" x="1381.956583360572" xlink:href="#GroundDisconnector:地刀_0" y="828.7839859938233" zvalue="933"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454525976579" ObjectName="110kV卡勐线13660接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454525976579"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1386.68,837.143) scale(0.94375,-0.835887) translate(82.3683,-1840.29)" width="10" x="1381.956583360572" y="828.7839859938233"/></g>
  <g id="828">
   <use class="kv110" height="20" transform="rotate(90,1388.49,881.036) scale(0.94375,-0.835887) translate(82.4766,-1936.69)" width="10" x="1383.774765178755" xlink:href="#GroundDisconnector:地刀_0" y="872.6768430004414" zvalue="937"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454525779971" ObjectName="110kV卡勐线13667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454525779971"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1388.49,881.036) scale(0.94375,-0.835887) translate(82.4766,-1936.69)" width="10" x="1383.774765178755" y="872.6768430004414"/></g>
  <g id="813">
   <use class="kv110" height="20" transform="rotate(90,1517.95,782.662) scale(0.94375,-0.835887) translate(90.1925,-1720.63)" width="10" x="1513.229310658845" xlink:href="#GroundDisconnector:地刀_0" y="774.3034665133042" zvalue="956"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454527483907" ObjectName="110kV勐嘎四级线13817接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454527483907"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1517.95,782.662) scale(0.94375,-0.835887) translate(90.1925,-1720.63)" width="10" x="1513.229310658845" y="774.3034665133042"/></g>
  <g id="811">
   <use class="kv110" height="20" transform="rotate(90,1516.68,837.143) scale(0.94375,-0.835887) translate(90.1166,-1840.29)" width="10" x="1511.956583360572" xlink:href="#GroundDisconnector:地刀_0" y="828.7839859938233" zvalue="960"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454527287299" ObjectName="110kV勐嘎四级线13860接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454527287299"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1516.68,837.143) scale(0.94375,-0.835887) translate(90.1166,-1840.29)" width="10" x="1511.956583360572" y="828.7839859938233"/></g>
  <g id="809">
   <use class="kv110" height="20" transform="rotate(90,1518.49,881.036) scale(0.94375,-0.835887) translate(90.225,-1936.69)" width="10" x="1513.774765178755" xlink:href="#GroundDisconnector:地刀_0" y="872.6768430004414" zvalue="964"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454527090691" ObjectName="110kV勐嘎四级线13867接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454527090691"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1518.49,881.036) scale(0.94375,-0.835887) translate(90.225,-1936.69)" width="10" x="1513.774765178755" y="872.6768430004414"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="69">
   <path class="kv110" d="M 429.34 733.78 L 429.34 712.71" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@1" LinkObjectIDznd="856@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 429.34 733.78 L 429.34 712.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv110" d="M 463.48 733.78 L 463.48 723.14" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@1" LinkObjectIDznd="855@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 463.48 733.78 L 463.48 723.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv110" d="M 429.36 751.79 L 429.35 764 L 463.49 764 L 463.5 751.79" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="114@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 429.36 751.79 L 429.35 764 L 463.49 764 L 463.5 751.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv110" d="M 445.75 797.82 L 445.75 764" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@1" LinkObjectIDznd="65" MaxPinNum="2"/>
   </metadata>
  <path d="M 445.75 797.82 L 445.75 764" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv110" d="M 890.9 581.84 L 890.9 605.75" stroke-width="1" zvalue="91"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@1" LinkObjectIDznd="158@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 890.9 581.84 L 890.9 605.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv110" d="M 873.69 677.59 L 873.68 665.38 L 907.82 665.38 L 907.84 677.59" stroke-width="1" zvalue="92"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="154@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 873.69 677.59 L 873.68 665.38 L 907.82 665.38 L 907.84 677.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv110" d="M 890.08 631.56 L 890.08 665.38" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@1" LinkObjectIDznd="49" MaxPinNum="2"/>
   </metadata>
  <path d="M 890.08 631.56 L 890.08 665.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv110" d="M 890.92 563.83 L 890.92 511.36" stroke-width="1" zvalue="96"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="84@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 890.92 563.83 L 890.92 511.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv110" d="M 898.12 548.93 L 890.92 548.93" stroke-width="1" zvalue="97"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="144@0" LinkObjectIDznd="45" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.12 548.93 L 890.92 548.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv110" d="M 873.67 695.6 L 873.67 712.71" stroke-width="1" zvalue="100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@1" LinkObjectIDznd="856@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 873.67 695.6 L 873.67 712.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv110" d="M 907.81 695.6 L 907.81 723.14" stroke-width="1" zvalue="101"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@1" LinkObjectIDznd="855@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 907.81 695.6 L 907.81 723.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv110" d="M 1282.23 580.46 L 1282.21 605.75" stroke-width="1" zvalue="118"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@1" LinkObjectIDznd="119@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1282.23 580.46 L 1282.21 605.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv110" d="M 1265.97 677.59 L 1265.95 665.38 L 1300.1 665.38 L 1300.11 677.59" stroke-width="1" zvalue="119"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="112@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1265.97 677.59 L 1265.95 665.38 L 1300.1 665.38 L 1300.11 677.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv110" d="M 1282.36 631.56 L 1282.36 665.38" stroke-width="1" zvalue="120"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@1" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 1282.36 631.56 L 1282.36 665.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv110" d="M 1282.25 562.45 L 1282.25 512.04" stroke-width="1" zvalue="123"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="87@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1282.25 562.45 L 1282.25 512.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv110" d="M 1293.31 548.39 L 1282.25 548.39" stroke-width="1" zvalue="124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="91" MaxPinNum="2"/>
   </metadata>
  <path d="M 1293.31 548.39 L 1282.25 548.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv110" d="M 1265.94 695.6 L 1265.94 712.71" stroke-width="1" zvalue="125"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@1" LinkObjectIDznd="856@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1265.94 695.6 L 1265.94 712.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv110" d="M 1300.09 695.6 L 1300.09 723.14" stroke-width="1" zvalue="126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@1" LinkObjectIDznd="855@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1300.09 695.6 L 1300.09 723.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv220" d="M 1282.72 437.43 L 1282.72 378.46" stroke-width="1" zvalue="136"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@0" LinkObjectIDznd="126@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1282.72 437.43 L 1282.72 378.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv220" d="M 1282.74 360.45 L 1282.74 334.56" stroke-width="1" zvalue="140"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="130@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1282.74 360.45 L 1282.74 334.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv220" d="M 1281.13 308.75 L 1281.23 275.46" stroke-width="1" zvalue="144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="134@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1281.13 308.75 L 1281.23 275.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv220" d="M 890.23 283.46 L 890.13 308.75" stroke-width="1" zvalue="179"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@1" LinkObjectIDznd="170@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 890.23 283.46 L 890.13 308.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv220" d="M 890.28 334.56 L 890.25 360.45" stroke-width="1" zvalue="181"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@1" LinkObjectIDznd="172@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 890.28 334.56 L 890.25 360.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv220" d="M 890.23 378.46 L 890.28 436.75" stroke-width="1" zvalue="182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@1" LinkObjectIDznd="84@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 890.23 378.46 L 890.28 436.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv110" d="M 838.65 809.31 L 838.65 774.93" stroke-width="1" zvalue="200"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@0" LinkObjectIDznd="196@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 838.65 809.31 L 838.65 774.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="kv110" d="M 838.62 756.92 L 838.62 712.71" stroke-width="1" zvalue="201"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@1" LinkObjectIDznd="856@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 838.62 756.92 L 838.62 712.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv110" d="M 1114.93 809.31 L 1114.93 774.02" stroke-width="1" zvalue="213"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@0" LinkObjectIDznd="213@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1114.93 809.31 L 1114.93 774.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv110" d="M 1114.9 756.01 L 1114.9 723.14" stroke-width="1" zvalue="214"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="213@1" LinkObjectIDznd="855@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1114.9 756.01 L 1114.9 723.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv110" d="M 960.34 733.78 L 960.34 712.71" stroke-width="1" zvalue="221"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@1" LinkObjectIDznd="856@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.34 733.78 L 960.34 712.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="kv110" d="M 1038.48 733.78 L 1038.48 723.14" stroke-width="1" zvalue="224"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@1" LinkObjectIDznd="855@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1038.48 733.78 L 1038.48 723.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv110" d="M 960.36 751.79 L 960.36 768.87 L 985.03 768.9" stroke-width="1" zvalue="226"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="223@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.36 751.79 L 960.36 768.87 L 985.03 768.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv110" d="M 1011.65 768.83 L 1038.5 768.81 L 1038.5 751.79" stroke-width="1" zvalue="227"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@1" LinkObjectIDznd="219@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1011.65 768.83 L 1038.5 768.81 L 1038.5 751.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="kv110" d="M 960.36 795.99 L 960.36 768.06" stroke-width="1" zvalue="233"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="227@0" LinkObjectIDznd="225" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.36 795.99 L 960.36 768.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="kv110" d="M 1038.5 766.48 L 1038.5 795.99" stroke-width="1" zvalue="234"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226" LinkObjectIDznd="230@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1038.5 766.48 L 1038.5 795.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="260">
   <path class="kv110" d="M 445.6 823.63 L 445.62 848.92" stroke-width="1" zvalue="263"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@0" LinkObjectIDznd="105@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 445.6 823.63 L 445.62 848.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="498">
   <path class="kv220" d="M 1063.12 232.53 L 967.38 232.53" stroke-width="1" zvalue="510"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="492@0" LinkObjectIDznd="496@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.12 232.53 L 967.38 232.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="499">
   <path class="kv220" d="M 949.37 231.71 L 890.28 231.71 L 890.25 265.45" stroke-width="1" zvalue="511"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="496@1" LinkObjectIDznd="168@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 949.37 231.71 L 890.28 231.71 L 890.25 265.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="502">
   <path class="kv220" d="M 1168.37 232.45 L 1089.74 232.46" stroke-width="1" zvalue="515"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="500@1" LinkObjectIDznd="492@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1168.37 232.45 L 1089.74 232.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="509">
   <path class="kv220" d="M 1015.1 244.4 L 1015.1 232.53" stroke-width="1" zvalue="523"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="507@0" LinkObjectIDznd="498" MaxPinNum="2"/>
   </metadata>
  <path d="M 1015.1 244.4 L 1015.1 232.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="511">
   <path class="kv220" d="M 1143.73 243.49 L 1143.73 232.45" stroke-width="1" zvalue="526"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="512@0" LinkObjectIDznd="502" MaxPinNum="2"/>
   </metadata>
  <path d="M 1143.73 243.49 L 1143.73 232.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="515">
   <path class="kv220" d="M 889.89 194.9 L 889.89 152.4" stroke-width="1" zvalue="531"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="513@0" LinkObjectIDznd="120@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 889.89 194.9 L 889.89 152.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="522">
   <path class="kv110" d="M 445.65 866.93 L 445.65 898.2" stroke-width="1" zvalue="538"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="100@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 445.65 866.93 L 445.65 898.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="549">
   <path class="kv35" d="M 705.25 383.27 L 705.25 371.32" stroke-width="1" zvalue="566"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="545@0" LinkObjectIDznd="534@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.25 383.27 L 705.25 371.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="551">
   <path class="kv35" d="M 705.13 411.58 L 705.23 401.28" stroke-width="1" zvalue="568"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="546@0" LinkObjectIDznd="545@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.13 411.58 L 705.23 401.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="552">
   <path class="kv35" d="M 840.19 473 L 705.23 473 L 705.23 464.28" stroke-width="1" zvalue="569"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@4" LinkObjectIDznd="547@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 840.19 473 L 705.23 473 L 705.23 464.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="553">
   <path class="kv35" d="M 705.25 446.27 L 705.28 437.39" stroke-width="1" zvalue="570"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="547@0" LinkObjectIDznd="546@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.25 446.27 L 705.28 437.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="556">
   <path class="kv35" d="M 705.55 492.59 L 705.55 473" stroke-width="1" zvalue="573"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="554@0" LinkObjectIDznd="552" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.55 492.59 L 705.55 473" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="570">
   <path class="kv35" d="M 1480.25 382.36 L 1480.25 371.32" stroke-width="1" zvalue="588"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="566@0" LinkObjectIDznd="537@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1480.25 382.36 L 1480.25 371.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="572">
   <path class="kv35" d="M 1480.13 411.58 L 1480.23 400.37" stroke-width="1" zvalue="590"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="567@0" LinkObjectIDznd="566@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1480.13 411.58 L 1480.23 400.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="573">
   <path class="kv35" d="M 1332.81 473.68 L 1480.23 473.68 L 1480.23 463.37" stroke-width="1" zvalue="591"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@4" LinkObjectIDznd="568@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1332.81 473.68 L 1480.23 473.68 L 1480.23 463.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="574">
   <path class="kv35" d="M 1480.25 445.36 L 1480.28 437.39" stroke-width="1" zvalue="592"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="568@0" LinkObjectIDznd="567@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1480.25 445.36 L 1480.28 437.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="575">
   <path class="kv35" d="M 1479.64 491.68 L 1479.64 470.8" stroke-width="1" zvalue="593"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="562@0" LinkObjectIDznd="573" MaxPinNum="2"/>
   </metadata>
  <path d="M 1479.64 491.68 L 1479.64 470.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="577">
   <path class="kv35" d="M 759.09 461.07 L 759.09 473" stroke-width="1" zvalue="595"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="576@0" LinkObjectIDznd="552" MaxPinNum="2"/>
   </metadata>
  <path d="M 759.09 461.07 L 759.09 473" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="579">
   <path class="kv35" d="M 1419.55 461.07 L 1419.55 473.68" stroke-width="1" zvalue="598"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="578@0" LinkObjectIDznd="573" MaxPinNum="2"/>
   </metadata>
  <path d="M 1419.55 461.07 L 1419.55 473.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="581">
   <path class="kv110" d="M 891.75 524.91 L 890.92 524.91" stroke-width="1" zvalue="601"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="580@0" LinkObjectIDznd="45" MaxPinNum="2"/>
   </metadata>
  <path d="M 891.75 524.91 L 890.92 524.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="586">
   <path class="kv220" d="M 891.2 454.41 L 946.55 454.41" stroke-width="1" zvalue="606"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@3" LinkObjectIDznd="584@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 891.2 454.41 L 946.55 454.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="588">
   <path class="kv110" d="M 889.62 493.56 L 837.73 510.55" stroke-width="1" zvalue="609"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@2" LinkObjectIDznd="587@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 889.62 493.56 L 837.73 510.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv220" d="M 1281.8 455.09 L 1229.18 455.09" stroke-width="1" zvalue="613"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@3" LinkObjectIDznd="43@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1281.8 455.09 L 1229.18 455.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv110" d="M 1283.38 494.24 L 1342.91 511.45" stroke-width="1" zvalue="614"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@2" LinkObjectIDznd="51@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1283.38 494.24 L 1342.91 511.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv35" d="M 546.8 385.99 L 546.8 371.32" stroke-width="1" zvalue="628"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136@0" LinkObjectIDznd="534@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 546.8 385.99 L 546.8 371.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv35" d="M 546.28 411.59 L 546.28 404.01" stroke-width="1" zvalue="630"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@0" LinkObjectIDznd="136@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 546.28 411.59 L 546.28 404.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv35" d="M 546.86 514.3 L 546.77 491.01" stroke-width="1" zvalue="631"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="161@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 546.86 514.3 L 546.77 491.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv35" d="M 546.8 472.99 L 546.8 437.39" stroke-width="1" zvalue="632"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="138@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 546.8 472.99 L 546.8 437.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv35" d="M 564.45 452.96 L 546.8 452.96" stroke-width="1" zvalue="635"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@0" LinkObjectIDznd="173" MaxPinNum="2"/>
   </metadata>
  <path d="M 564.45 452.96 L 546.8 452.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="258">
   <path class="kv35" d="M 627.25 386.9 L 627.25 371.32" stroke-width="1" zvalue="645"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@0" LinkObjectIDznd="534@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 627.25 386.9 L 627.25 371.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="kv35" d="M 627.13 411.58 L 627.23 404.92" stroke-width="1" zvalue="646"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@0" LinkObjectIDznd="259@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 627.13 411.58 L 627.23 404.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="kv35" d="M 627.31 515.21 L 627.31 491.92" stroke-width="1" zvalue="647"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@0" LinkObjectIDznd="270@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 627.31 515.21 L 627.31 491.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv35" d="M 628.16 473.9 L 628.16 437.39" stroke-width="1" zvalue="648"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="270@0" LinkObjectIDznd="261@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 628.16 473.9 L 628.16 437.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv35" d="M 644.9 453.87 L 628.16 453.87" stroke-width="1" zvalue="651"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@0" LinkObjectIDznd="216" MaxPinNum="2"/>
   </metadata>
  <path d="M 644.9 453.87 L 628.16 453.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="293">
   <path class="kv35" d="M 554.8 310.84 L 554.8 338.99" stroke-width="1" zvalue="656"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="289@0" LinkObjectIDznd="292@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 554.8 310.84 L 554.8 338.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="294">
   <path class="kv35" d="M 554.96 357.01 L 554.96 371.32" stroke-width="1" zvalue="657"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="292@1" LinkObjectIDznd="534@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 554.96 357.01 L 554.96 371.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="298">
   <path class="kv35" d="M 573.54 327.51 L 554.8 327.51" stroke-width="1" zvalue="660"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="296@0" LinkObjectIDznd="293" MaxPinNum="2"/>
   </metadata>
  <path d="M 573.54 327.51 L 554.8 327.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="kv35" d="M 654.16 255.84 L 654.16 267.36" stroke-width="1" zvalue="674"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="299@0" LinkObjectIDznd="309@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 654.16 255.84 L 654.16 267.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="313">
   <path class="kv35" d="M 654.14 285.37 L 654.04 309.84" stroke-width="1" zvalue="675"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="309@1" LinkObjectIDznd="308@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 654.14 285.37 L 654.04 309.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="315">
   <path class="kv35" d="M 654.19 335.65 L 654.16 338.72" stroke-width="1" zvalue="676"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="308@1" LinkObjectIDznd="307@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 654.19 335.65 L 654.16 338.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="321">
   <path class="kv35" d="M 654.14 356.73 L 654.14 371.32" stroke-width="1" zvalue="677"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="307@1" LinkObjectIDznd="534@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 654.14 356.73 L 654.14 371.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="375">
   <path class="kv35" d="M 670.9 299.15 L 654.08 299.15" stroke-width="1" zvalue="682"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="347@0" LinkObjectIDznd="313" MaxPinNum="2"/>
   </metadata>
  <path d="M 670.9 299.15 L 654.08 299.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="564">
   <path class="kv35" d="M 1635.43 385.08 L 1635.43 371.32" stroke-width="1" zvalue="692"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="565@0" LinkObjectIDznd="537@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1635.43 385.08 L 1635.43 371.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="563">
   <path class="kv35" d="M 1635.31 411.58 L 1635.41 403.1" stroke-width="1" zvalue="693"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="569@0" LinkObjectIDznd="565@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1635.31 411.58 L 1635.41 403.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="561">
   <path class="kv35" d="M 1635.49 513.39 L 1635.41 494.1" stroke-width="1" zvalue="694"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="590@0" LinkObjectIDznd="571@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1635.49 513.39 L 1635.41 494.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="550">
   <path class="kv35" d="M 1635.43 476.08 L 1635.46 437.39" stroke-width="1" zvalue="695"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="571@0" LinkObjectIDznd="569@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1635.43 476.08 L 1635.46 437.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="544">
   <path class="kv35" d="M 1653.08 455.8 L 1635.45 455.8" stroke-width="1" zvalue="698"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="548@0" LinkObjectIDznd="550" MaxPinNum="2"/>
   </metadata>
  <path d="M 1653.08 455.8 L 1635.45 455.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="536">
   <path class="kv35" d="M 1567.07 309.02 L 1567.07 337.18" stroke-width="1" zvalue="702"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="543@0" LinkObjectIDznd="538@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1567.07 309.02 L 1567.07 337.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="521">
   <path class="kv35" d="M 1567.23 355.19 L 1567.23 371.32" stroke-width="1" zvalue="704"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="538@1" LinkObjectIDznd="537@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1567.23 355.19 L 1567.23 371.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="506">
   <path class="kv35" d="M 1585.81 325.69 L 1567.07 325.69" stroke-width="1" zvalue="707"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="517@0" LinkObjectIDznd="536" MaxPinNum="2"/>
   </metadata>
  <path d="M 1585.81 325.69 L 1567.07 325.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="600">
   <path class="kv35" d="M 1558.49 411.59 L 1558.59 404.01" stroke-width="1" zvalue="717"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="602@0" LinkObjectIDznd="601@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1558.49 411.59 L 1558.59 404.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="599">
   <path class="kv35" d="M 1558.67 514.3 L 1558.59 491.01" stroke-width="1" zvalue="718"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="604@0" LinkObjectIDznd="603@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1558.67 514.3 L 1558.59 491.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="598">
   <path class="kv35" d="M 1558.61 472.99 L 1558.64 437.39" stroke-width="1" zvalue="719"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="603@0" LinkObjectIDznd="602@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1558.61 472.99 L 1558.64 437.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="596">
   <path class="kv35" d="M 1576.26 455.69 L 1558.63 455.69" stroke-width="1" zvalue="722"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="597@0" LinkObjectIDznd="598" MaxPinNum="2"/>
   </metadata>
  <path d="M 1576.26 455.69 L 1558.63 455.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="605">
   <path class="kv35" d="M 1558.61 385.99 L 1558.61 371.32" stroke-width="1" zvalue="723"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="601@0" LinkObjectIDznd="537@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1558.61 385.99 L 1558.61 371.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv220" d="M 889.87 212.92 L 889.87 238.97" stroke-width="1" zvalue="748"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="513@1" LinkObjectIDznd="499" MaxPinNum="2"/>
   </metadata>
  <path d="M 889.87 212.92 L 889.87 238.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv220" d="M 1186.38 232.47 L 1281.25 232.47 L 1281.25 257.45" stroke-width="1" zvalue="753"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="500@0" LinkObjectIDznd="134@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1186.38 232.47 L 1281.25 232.47 L 1281.25 257.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="614">
   <path class="kv10" d="M 1705.01 216.42 L 1704.97 240.27" stroke-width="1" zvalue="765"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="622@1" LinkObjectIDznd="621@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1705.01 216.42 L 1704.97 240.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="631">
   <path class="kv10" d="M 1705.42 176.77 L 1705.42 190.62" stroke-width="1" zvalue="770"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="630@0" LinkObjectIDznd="622@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1705.42 176.77 L 1705.42 190.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="632">
   <path class="kv10" d="M 1704.83 255.97 L 1704.83 289.82 L 1820.91 289.82 L 1820.91 235.27" stroke-width="1" zvalue="771"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="621@1" LinkObjectIDznd="637@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1704.83 255.97 L 1704.83 289.82 L 1820.91 289.82 L 1820.91 235.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="642">
   <path class="kv110" d="M 454.53 837.19 L 445.61 837.19" stroke-width="1" zvalue="779"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="260" MaxPinNum="2"/>
   </metadata>
  <path d="M 454.53 837.19 L 445.61 837.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="644">
   <path class="kv110" d="M 456.34 881.08 L 445.65 881.08" stroke-width="1" zvalue="781"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@0" LinkObjectIDznd="522" MaxPinNum="2"/>
   </metadata>
  <path d="M 456.34 881.08 L 445.65 881.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="645">
   <path class="kv110" d="M 455.8 782.71 L 445.75 782.71" stroke-width="1" zvalue="782"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="64" MaxPinNum="2"/>
   </metadata>
  <path d="M 455.8 782.71 L 445.75 782.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="655">
   <path class="kv110" d="M 845.71 745.19 L 838.62 745.19" stroke-width="1" zvalue="784"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@0" LinkObjectIDznd="198" MaxPinNum="2"/>
   </metadata>
  <path d="M 845.71 745.19 L 838.62 745.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="656">
   <path class="kv110" d="M 845.71 789.08 L 838.65 789.08" stroke-width="1" zvalue="785"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="197" MaxPinNum="2"/>
   </metadata>
  <path d="M 845.71 789.08 L 838.65 789.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="657">
   <path class="kv110" d="M 1129.71 745.19 L 1114.9 745.19" stroke-width="1" zvalue="786"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="208" MaxPinNum="2"/>
   </metadata>
  <path d="M 1129.71 745.19 L 1114.9 745.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="658">
   <path class="kv110" d="M 1130.62 788.17 L 1114.93 788.17" stroke-width="1" zvalue="787"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@0" LinkObjectIDznd="209" MaxPinNum="2"/>
   </metadata>
  <path d="M 1130.62 788.17 L 1114.93 788.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="659">
   <path class="kv220" d="M 899.52 174.3 L 889.89 174.3" stroke-width="1" zvalue="788"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="518@0" LinkObjectIDznd="515" MaxPinNum="2"/>
   </metadata>
  <path d="M 899.52 174.3 L 889.89 174.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="660">
   <path class="kv220" d="M 904.45 298.14 L 890.18 298.14" stroke-width="1" zvalue="789"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="177" MaxPinNum="2"/>
   </metadata>
  <path d="M 904.45 298.14 L 890.18 298.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="661">
   <path class="kv220" d="M 904.45 344.32 L 890.27 344.32" stroke-width="1" zvalue="790"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 904.45 344.32 L 890.27 344.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="662">
   <path class="kv220" d="M 904.45 399.23 L 890.25 399.23" stroke-width="1" zvalue="791"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@0" LinkObjectIDznd="180" MaxPinNum="2"/>
   </metadata>
  <path d="M 904.45 399.23 L 890.25 399.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="664">
   <path class="kv220" d="M 885.66 416.8 L 890.26 416.8" stroke-width="1" zvalue="794"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="180" MaxPinNum="2"/>
   </metadata>
  <path d="M 885.66 416.8 L 890.26 416.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="665">
   <path class="kv220" d="M 1294.54 294.1 L 1281.18 294.1" stroke-width="1" zvalue="795"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@0" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 1294.54 294.1 L 1281.18 294.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="666">
   <path class="kv220" d="M 1295.45 345.23 L 1282.74 345.23" stroke-width="1" zvalue="796"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@0" LinkObjectIDznd="131" MaxPinNum="2"/>
   </metadata>
  <path d="M 1295.45 345.23 L 1282.74 345.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="667">
   <path class="kv220" d="M 1296.45 399.23 L 1282.72 399.23" stroke-width="1" zvalue="797"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="143@0" LinkObjectIDznd="128" MaxPinNum="2"/>
   </metadata>
  <path d="M 1296.45 399.23 L 1282.72 399.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="669">
   <path class="kv110" d="M 905.04 645.76 L 890.08 645.76" stroke-width="1" zvalue="799"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="48" MaxPinNum="2"/>
   </metadata>
  <path d="M 905.04 645.76 L 890.08 645.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="670">
   <path class="kv110" d="M 905.04 592.19 L 890.9 592.19" stroke-width="1" zvalue="800"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="148@0" LinkObjectIDznd="50" MaxPinNum="2"/>
   </metadata>
  <path d="M 905.04 592.19 L 890.9 592.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="673">
   <path class="kv110" d="M 1293.31 592.19 L 1282.22 592.19" stroke-width="1" zvalue="803"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="98" MaxPinNum="2"/>
   </metadata>
  <path d="M 1293.31 592.19 L 1282.22 592.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="674">
   <path class="kv110" d="M 1293.31 645.76 L 1282.36 645.76" stroke-width="1" zvalue="804"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="95" MaxPinNum="2"/>
   </metadata>
  <path d="M 1293.31 645.76 L 1282.36 645.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="684">
   <path class="kv110" d="M 1282.2 521.64 L 1282.25 521.64" stroke-width="1" zvalue="806"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="582@0" LinkObjectIDznd="91" MaxPinNum="2"/>
   </metadata>
  <path d="M 1282.2 521.64 L 1282.25 521.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="738">
   <path class="kv110" d="M 559.34 733.78 L 559.34 712.71" stroke-width="1" zvalue="856"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="745@1" LinkObjectIDznd="856@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 559.34 733.78 L 559.34 712.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="737">
   <path class="kv110" d="M 593.48 733.78 L 593.48 723.14" stroke-width="1" zvalue="857"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="744@1" LinkObjectIDznd="855@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 593.48 733.78 L 593.48 723.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="736">
   <path class="kv110" d="M 559.36 751.79 L 559.35 764 L 593.49 764 L 593.5 751.79" stroke-width="1" zvalue="858"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="745@0" LinkObjectIDznd="744@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 559.36 751.79 L 559.35 764 L 593.49 764 L 593.5 751.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="735">
   <path class="kv110" d="M 575.75 797.82 L 575.75 764" stroke-width="1" zvalue="859"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="746@1" LinkObjectIDznd="736" MaxPinNum="2"/>
   </metadata>
  <path d="M 575.75 797.82 L 575.75 764" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="734">
   <path class="kv110" d="M 575.6 823.63 L 575.62 848.92" stroke-width="1" zvalue="860"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="746@0" LinkObjectIDznd="742@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 575.6 823.63 L 575.62 848.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="732">
   <path class="kv110" d="M 575.65 866.93 L 575.65 898.2" stroke-width="1" zvalue="862"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="742@0" LinkObjectIDznd="740@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 575.65 866.93 L 575.65 898.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="731">
   <path class="kv110" d="M 584.53 837.19 L 575.61 837.19" stroke-width="1" zvalue="863"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="741@0" LinkObjectIDznd="734" MaxPinNum="2"/>
   </metadata>
  <path d="M 584.53 837.19 L 575.61 837.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="729">
   <path class="kv110" d="M 586.34 881.08 L 575.65 881.08" stroke-width="1" zvalue="865"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="739@0" LinkObjectIDznd="732" MaxPinNum="2"/>
   </metadata>
  <path d="M 586.34 881.08 L 575.65 881.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="728">
   <path class="kv110" d="M 585.8 782.71 L 575.75 782.71" stroke-width="1" zvalue="866"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="743@0" LinkObjectIDznd="735" MaxPinNum="2"/>
   </metadata>
  <path d="M 585.8 782.71 L 575.75 782.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="765">
   <path class="kv110" d="M 689.34 733.78 L 689.34 712.71" stroke-width="1" zvalue="884"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="772@1" LinkObjectIDznd="856@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 689.34 733.78 L 689.34 712.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="764">
   <path class="kv110" d="M 723.48 733.78 L 723.48 723.14" stroke-width="1" zvalue="885"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="771@1" LinkObjectIDznd="855@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 723.48 733.78 L 723.48 723.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="763">
   <path class="kv110" d="M 689.36 751.79 L 689.35 764 L 723.49 764 L 723.5 751.79" stroke-width="1" zvalue="886"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="772@0" LinkObjectIDznd="771@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 689.36 751.79 L 689.35 764 L 723.49 764 L 723.5 751.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="762">
   <path class="kv110" d="M 705.75 797.82 L 705.75 764" stroke-width="1" zvalue="887"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="773@1" LinkObjectIDznd="763" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.75 797.82 L 705.75 764" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="761">
   <path class="kv110" d="M 705.6 823.63 L 705.62 848.92" stroke-width="1" zvalue="888"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="773@0" LinkObjectIDznd="769@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.6 823.63 L 705.62 848.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="759">
   <path class="kv110" d="M 705.65 866.93 L 705.65 897.57" stroke-width="1" zvalue="890"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="769@0" LinkObjectIDznd="767@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.65 866.93 L 705.65 897.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="758">
   <path class="kv110" d="M 714.53 837.19 L 705.61 837.19" stroke-width="1" zvalue="891"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="768@0" LinkObjectIDznd="761" MaxPinNum="2"/>
   </metadata>
  <path d="M 714.53 837.19 L 705.61 837.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="756">
   <path class="kv110" d="M 716.34 881.08 L 705.65 881.08" stroke-width="1" zvalue="893"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="766@0" LinkObjectIDznd="759" MaxPinNum="2"/>
   </metadata>
  <path d="M 716.34 881.08 L 705.65 881.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="755">
   <path class="kv110" d="M 715.8 782.71 L 705.75 782.71" stroke-width="1" zvalue="894"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="770@0" LinkObjectIDznd="762" MaxPinNum="2"/>
   </metadata>
  <path d="M 715.8 782.71 L 705.75 782.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="846">
   <path class="kv110" d="M 1223.34 733.78 L 1223.34 712.71" stroke-width="1" zvalue="912"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="853@1" LinkObjectIDznd="856@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1223.34 733.78 L 1223.34 712.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="845">
   <path class="kv110" d="M 1257.48 733.78 L 1257.48 723.14" stroke-width="1" zvalue="913"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="852@1" LinkObjectIDznd="855@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1257.48 733.78 L 1257.48 723.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="844">
   <path class="kv110" d="M 1223.36 751.79 L 1223.35 764 L 1257.49 764 L 1257.5 751.79" stroke-width="1" zvalue="914"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="853@0" LinkObjectIDznd="852@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1223.36 751.79 L 1223.35 764 L 1257.49 764 L 1257.5 751.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="843">
   <path class="kv110" d="M 1239.75 797.82 L 1239.75 764" stroke-width="1" zvalue="915"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="854@1" LinkObjectIDznd="844" MaxPinNum="2"/>
   </metadata>
  <path d="M 1239.75 797.82 L 1239.75 764" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="842">
   <path class="kv110" d="M 1239.6 823.63 L 1239.62 848.92" stroke-width="1" zvalue="916"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="854@0" LinkObjectIDznd="850@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1239.6 823.63 L 1239.62 848.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="840">
   <path class="kv110" d="M 1239.65 866.93 L 1239.65 900.68" stroke-width="1" zvalue="918"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="848@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1239.65 866.93 L 1239.65 900.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="839">
   <path class="kv110" d="M 1248.53 837.19 L 1239.61 837.19" stroke-width="1" zvalue="919"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="849@0" LinkObjectIDznd="842" MaxPinNum="2"/>
   </metadata>
  <path d="M 1248.53 837.19 L 1239.61 837.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="837">
   <path class="kv110" d="M 1250.34 881.08 L 1239.65 881.08" stroke-width="1" zvalue="921"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="847@0" LinkObjectIDznd="840" MaxPinNum="2"/>
   </metadata>
  <path d="M 1250.34 881.08 L 1239.65 881.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="836">
   <path class="kv110" d="M 1249.8 782.71 L 1239.75 782.71" stroke-width="1" zvalue="922"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="851@0" LinkObjectIDznd="843" MaxPinNum="2"/>
   </metadata>
  <path d="M 1249.8 782.71 L 1239.75 782.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="827">
   <path class="kv110" d="M 1353.34 733.78 L 1353.34 712.71" stroke-width="1" zvalue="939"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="834@1" LinkObjectIDznd="856@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1353.34 733.78 L 1353.34 712.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="826">
   <path class="kv110" d="M 1387.48 733.78 L 1387.48 723.14" stroke-width="1" zvalue="940"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="833@1" LinkObjectIDznd="855@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1387.48 733.78 L 1387.48 723.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="825">
   <path class="kv110" d="M 1353.36 751.79 L 1353.35 764 L 1387.49 764 L 1387.5 751.79" stroke-width="1" zvalue="941"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="834@0" LinkObjectIDznd="833@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1353.36 751.79 L 1353.35 764 L 1387.49 764 L 1387.5 751.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="824">
   <path class="kv110" d="M 1369.75 797.82 L 1369.75 764" stroke-width="1" zvalue="942"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="835@1" LinkObjectIDznd="825" MaxPinNum="2"/>
   </metadata>
  <path d="M 1369.75 797.82 L 1369.75 764" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="823">
   <path class="kv110" d="M 1369.6 823.63 L 1369.62 848.92" stroke-width="1" zvalue="943"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="835@0" LinkObjectIDznd="831@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1369.6 823.63 L 1369.62 848.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="821">
   <path class="kv110" d="M 1369.65 866.93 L 1369.65 901.36" stroke-width="1" zvalue="945"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="831@0" LinkObjectIDznd="829@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1369.65 866.93 L 1369.65 901.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="820">
   <path class="kv110" d="M 1378.53 837.19 L 1369.61 837.19" stroke-width="1" zvalue="946"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="830@0" LinkObjectIDznd="823" MaxPinNum="2"/>
   </metadata>
  <path d="M 1378.53 837.19 L 1369.61 837.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="818">
   <path class="kv110" d="M 1380.34 881.08 L 1369.65 881.08" stroke-width="1" zvalue="948"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="828@0" LinkObjectIDznd="821" MaxPinNum="2"/>
   </metadata>
  <path d="M 1380.34 881.08 L 1369.65 881.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="817">
   <path class="kv110" d="M 1379.8 782.71 L 1369.75 782.71" stroke-width="1" zvalue="949"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="832@0" LinkObjectIDznd="824" MaxPinNum="2"/>
   </metadata>
  <path d="M 1379.8 782.71 L 1369.75 782.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="808">
   <path class="kv110" d="M 1483.34 733.78 L 1483.34 712.71" stroke-width="1" zvalue="966"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="815@1" LinkObjectIDznd="856@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1483.34 733.78 L 1483.34 712.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="807">
   <path class="kv110" d="M 1517.48 733.78 L 1517.48 723.14" stroke-width="1" zvalue="967"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="814@1" LinkObjectIDznd="855@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.48 733.78 L 1517.48 723.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="806">
   <path class="kv110" d="M 1483.36 751.79 L 1483.35 764 L 1517.49 764 L 1517.5 751.79" stroke-width="1" zvalue="968"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="815@0" LinkObjectIDznd="814@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1483.36 751.79 L 1483.35 764 L 1517.49 764 L 1517.5 751.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="805">
   <path class="kv110" d="M 1499.75 797.82 L 1499.75 764" stroke-width="1" zvalue="969"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="816@1" LinkObjectIDznd="806" MaxPinNum="2"/>
   </metadata>
  <path d="M 1499.75 797.82 L 1499.75 764" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="804">
   <path class="kv110" d="M 1499.6 823.63 L 1499.62 848.92" stroke-width="1" zvalue="970"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="816@0" LinkObjectIDznd="812@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1499.6 823.63 L 1499.62 848.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="802">
   <path class="kv110" d="M 1499.65 866.93 L 1499.65 901.2" stroke-width="1" zvalue="972"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="812@0" LinkObjectIDznd="810@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1499.65 866.93 L 1499.65 901.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="801">
   <path class="kv110" d="M 1508.53 837.19 L 1499.61 837.19" stroke-width="1" zvalue="973"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="811@0" LinkObjectIDznd="804" MaxPinNum="2"/>
   </metadata>
  <path d="M 1508.53 837.19 L 1499.61 837.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="799">
   <path class="kv110" d="M 1510.34 881.08 L 1499.65 881.08" stroke-width="1" zvalue="975"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="809@0" LinkObjectIDznd="802" MaxPinNum="2"/>
   </metadata>
  <path d="M 1510.34 881.08 L 1499.65 881.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="798">
   <path class="kv110" d="M 1509.8 782.71 L 1499.75 782.71" stroke-width="1" zvalue="976"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="813@0" LinkObjectIDznd="805" MaxPinNum="2"/>
   </metadata>
  <path d="M 1509.8 782.71 L 1499.75 782.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="857">
   <path class="kv220" d="M 1277.57 418.62 L 1282.72 418.62" stroke-width="1" zvalue="977"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@0" LinkObjectIDznd="128" MaxPinNum="2"/>
   </metadata>
  <path d="M 1277.57 418.62 L 1282.72 418.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer3Class">
  <g id="84">
   <g id="840">
    <use class="kv220" height="50" transform="rotate(0,877.364,473) scale(1.58182,1.58182) translate(-308.163,-159.432)" width="50" x="837.8200000000001" xlink:href="#PowerTransformer3:可调三卷变YNYND_0" y="433.45" zvalue="102"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874584686594" ObjectName="220"/>
    </metadata>
   </g>
   <g id="841">
    <use class="kv110" height="50" transform="rotate(0,877.364,473) scale(1.58182,1.58182) translate(-308.163,-159.432)" width="50" x="837.8200000000001" xlink:href="#PowerTransformer3:可调三卷变YNYND_1" y="433.45" zvalue="102"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874584752130" ObjectName="110"/>
    </metadata>
   </g>
   <g id="842">
    <use class="kv35" height="50" transform="rotate(0,877.364,473) scale(1.58182,1.58182) translate(-308.163,-159.432)" width="50" x="837.8200000000001" xlink:href="#PowerTransformer3:可调三卷变YNYND_2" y="433.45" zvalue="102"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874584817666" ObjectName="35"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399530250242" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399530250242"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,877.364,473) scale(1.58182,1.58182) translate(-308.163,-159.432)" width="50" x="837.8200000000001" y="433.45"/></g>
  <g id="87">
   <g id="870">
    <use class="kv220" height="50" transform="rotate(0,1295.64,473.677) scale(-1.58182,1.58182) translate(-2100.17,-159.681)" width="50" x="1256.09" xlink:href="#PowerTransformer3:可调三卷变YNYND_0" y="434.13" zvalue="127"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874584883202" ObjectName="220"/>
    </metadata>
   </g>
   <g id="871">
    <use class="kv110" height="50" transform="rotate(0,1295.64,473.677) scale(-1.58182,1.58182) translate(-2100.17,-159.681)" width="50" x="1256.09" xlink:href="#PowerTransformer3:可调三卷变YNYND_1" y="434.13" zvalue="127"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874584948738" ObjectName="110"/>
    </metadata>
   </g>
   <g id="872">
    <use class="kv35" height="50" transform="rotate(0,1295.64,473.677) scale(-1.58182,1.58182) translate(-2100.17,-159.681)" width="50" x="1256.09" xlink:href="#PowerTransformer3:可调三卷变YNYND_2" y="434.13" zvalue="127"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874585014274" ObjectName="35"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399530315778" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399530315778"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1295.64,473.677) scale(-1.58182,1.58182) translate(-2100.17,-159.681)" width="50" x="1256.09" y="434.13"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="120">
   <use class="kv220" height="38" transform="rotate(0,897.929,133.455) scale(1,1) translate(0,0)" width="30" x="882.9292370303889" xlink:href="#ACLineSegment:线路911_0" y="114.4545454545454" zvalue="129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249315803141" ObjectName="220kV卡盈线"/>
   <cge:TPSR_Ref TObjectID="8444249315803141_5066549682765825"/></metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,897.929,133.455) scale(1,1) translate(0,0)" width="30" x="882.9292370303889" y="114.4545454545454"/></g>
  <g id="767">
   <use class="kv110" height="30" transform="rotate(0,705.645,911.714) scale(3.57143,-0.952381) translate(-499.065,-1869.73)" width="7" x="693.1451461212978" xlink:href="#ACLineSegment:线路_0" y="897.4285715648107" zvalue="880"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249308004358" ObjectName="110kV卡嘎典线"/>
   <cge:TPSR_Ref TObjectID="8444249308004358_5066549682765825"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,705.645,911.714) scale(3.57143,-0.952381) translate(-499.065,-1869.73)" width="7" x="693.1451461212978" y="897.4285715648107"/></g>
  <g id="829">
   <use class="kv110" height="40" transform="rotate(0,1372,914.714) scale(0.714286,-0.714286) translate(543.799,-2201.03)" width="35" x="1359.496336597488" xlink:href="#ACLineSegment:线路电压互感器_0" y="900.4285715648107" zvalue="935"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249316065285" ObjectName="110kV卡勐线"/>
   <cge:TPSR_Ref TObjectID="8444249316065285_5066549682765825"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1372,914.714) scale(0.714286,-0.714286) translate(543.799,-2201.03)" width="35" x="1359.496336597488" y="900.4285715648107"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="124">
   <use class="kv220" height="40" transform="rotate(90,1261.55,407.61) scale(1,1) translate(0,0)" width="40" x="1241.545454545455" xlink:href="#Accessory:避雷器PT（德宏变）_0" y="387.6102213878531" zvalue="132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454517063683" ObjectName="#2主变220kV侧电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1261.55,407.61) scale(1,1) translate(0,0)" width="40" x="1241.545454545455" y="387.6102213878531"/></g>
  <g id="174">
   <use class="kv220" height="40" transform="rotate(90,869.636,405.788) scale(1,1) translate(0,0)" width="40" x="849.6363636363636" xlink:href="#Accessory:避雷器PT（德宏变）_0" y="385.7884051628304" zvalue="157"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454518177795" ObjectName="#1主变220kV侧电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,869.636,405.788) scale(1,1) translate(0,0)" width="40" x="849.6363636363636" y="385.7884051628304"/></g>
  <g id="185">
   <use class="kv110" height="40" transform="rotate(0,849.654,825.334) scale(-1,1) translate(-1699.31,0)" width="40" x="829.6542027701428" xlink:href="#Accessory:避雷器PT（德宏变）_0" y="805.3338597082849" zvalue="188"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454518243331" ObjectName="110kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,849.654,825.334) scale(-1,1) translate(-1699.31,0)" width="40" x="829.6542027701428" y="805.3338597082849"/></g>
  <g id="214">
   <use class="kv110" height="40" transform="rotate(0,1125.94,825.334) scale(-1,1) translate(-2251.87,0)" width="40" x="1105.935588051528" xlink:href="#Accessory:避雷器PT（德宏变）_0" y="805.3338597082849" zvalue="204"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454518964227" ObjectName="110kVⅡ母电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1125.94,825.334) scale(-1,1) translate(-2251.87,0)" width="40" x="1105.935588051528" y="805.3338597082849"/></g>
  <g id="576">
   <use class="kv35" height="20" transform="rotate(0,759.091,444.364) scale(1.90909,-1.90909) translate(-352.381,-668.035)" width="20" x="740.0000000000001" xlink:href="#Accessory:线路PT3_0" y="425.2727258855646" zvalue="594"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454520668163" ObjectName="#1主变低壁雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,759.091,444.364) scale(1.90909,-1.90909) translate(-352.381,-668.035)" width="20" x="740.0000000000001" y="425.2727258855646"/></g>
  <g id="578">
   <use class="kv35" height="20" transform="rotate(0,1419.55,444.364) scale(1.90909,-1.90909) translate(-666.883,-668.035)" width="20" x="1400.454545454545" xlink:href="#Accessory:线路PT3_0" y="425.2727258693089" zvalue="597"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454520733699" ObjectName="#2主变低壁雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1419.55,444.364) scale(1.90909,-1.90909) translate(-666.883,-668.035)" width="20" x="1400.454545454545" y="425.2727258693089"/></g>
  <g id="580">
   <use class="kv110" height="20" transform="rotate(90,908.455,524.909) scale(1.90909,-1.90909) translate(-423.506,-790.771)" width="20" x="889.3636363636365" xlink:href="#Accessory:线路PT3_0" y="505.8181804310191" zvalue="600"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454523682819" ObjectName="#1主变中壁雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,908.455,524.909) scale(1.90909,-1.90909) translate(-423.506,-790.771)" width="20" x="889.3636363636365" y="505.8181804310191"/></g>
  <g id="582">
   <use class="kv110" height="20" transform="rotate(90,1298.91,521.636) scale(1.90909,-1.90909) translate(-609.437,-785.784)" width="20" x="1279.818181818182" xlink:href="#Accessory:线路PT3_0" y="502.5454531582919" zvalue="603"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454520799235" ObjectName="#2主变中壁雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1298.91,521.636) scale(1.90909,-1.90909) translate(-609.437,-785.784)" width="20" x="1279.818181818182" y="502.5454531582919"/></g>
  <g id="289">
   <use class="kv35" height="40" transform="rotate(0,567.297,287.091) scale(1.25,-1.25) translate(-108.459,-511.764)" width="40" x="542.296661272813" xlink:href="#Accessory:五卷PT_0" y="262.0909090909092" zvalue="652"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454522044419" ObjectName="35kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,567.297,287.091) scale(1.25,-1.25) translate(-108.459,-511.764)" width="40" x="542.296661272813" y="262.0909090909092"/></g>
  <g id="543">
   <use class="kv35" height="40" transform="rotate(0,1579.57,285.273) scale(1.25,-1.25) translate(-310.914,-508.491)" width="40" x="1554.56938854554" xlink:href="#Accessory:五卷PT_0" y="260.2727272727273" zvalue="699"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454522830851" ObjectName="35kVⅡ母电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1579.57,285.273) scale(1.25,-1.25) translate(-310.914,-508.491)" width="40" x="1554.56938854554" y="260.2727272727273"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="66">
   <use class="kv35" height="22" transform="rotate(0,546.864,527.836) scale(1.25455,1.25455) translate(-107.776,-104.297)" width="25" x="531.1818181818182" xlink:href="#Compensator:电抗器1(德宏变)_0" y="514.0363636363636" zvalue="615"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454521389059" ObjectName="35kV1号电抗器"/>
   <cge:TPSR_Ref TObjectID="6192454521389059"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,546.864,527.836) scale(1.25455,1.25455) translate(-107.776,-104.297)" width="25" x="531.1818181818182" y="514.0363636363636"/></g>
  <g id="271">
   <use class="kv35" height="22" transform="rotate(0,627.318,528.745) scale(1.25455,1.25455) translate(-124.1,-104.482)" width="25" x="611.6363636363637" xlink:href="#Compensator:电抗器1(德宏变)_0" y="514.9454545454546" zvalue="637"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454521978883" ObjectName="35kV2号电抗器"/>
   <cge:TPSR_Ref TObjectID="6192454521978883"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,627.318,528.745) scale(1.25455,1.25455) translate(-124.1,-104.482)" width="25" x="611.6363636363637" y="514.9454545454546"/></g>
  <g id="590">
   <use class="kv35" height="22" transform="rotate(0,1635.5,526.927) scale(1.25455,1.25455) translate(-328.659,-104.113)" width="25" x="1619.818181818182" xlink:href="#Compensator:电抗器1(德宏变)_0" y="513.1272727272727" zvalue="684"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454523158531" ObjectName="35kV4号电抗器"/>
   <cge:TPSR_Ref TObjectID="6192454523158531"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1635.5,526.927) scale(1.25455,1.25455) translate(-328.659,-104.113)" width="25" x="1619.818181818182" y="513.1272727272727"/></g>
  <g id="604">
   <use class="kv35" height="22" transform="rotate(0,1558.68,527.836) scale(1.25455,1.25455) translate(-313.072,-104.297)" width="25" x="1543" xlink:href="#Compensator:电抗器1(德宏变)_0" y="514.0363636363636" zvalue="709"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454523486211" ObjectName="35kV3号电抗器"/>
   <cge:TPSR_Ref TObjectID="6192454523486211"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1558.68,527.836) scale(1.25455,1.25455) translate(-313.072,-104.297)" width="25" x="1543" y="514.0363636363636"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="299">
   <use class="kv35" height="30" transform="rotate(0,654.16,224.939) scale(2.50789,-2.18379) translate(-378.241,-310.185)" width="20" x="629.0813555455834" xlink:href="#EnergyConsumer:站用变无融断_0" y="192.1818181818183" zvalue="661"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454522306563" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,654.16,224.939) scale(2.50789,-2.18379) translate(-378.241,-310.185)" width="20" x="629.0813555455834" y="192.1818181818183"/></g>
  <g id="630">
   <use class="kv10" height="39" transform="rotate(0,1711.86,144.711) scale(1.67984,-1.67984) translate(-684.983,-217.6)" width="23" x="1692.545454545455" xlink:href="#EnergyConsumer:站用变D-Y_0" y="111.9545471884987" zvalue="755"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454523617283" ObjectName="10kV2号站用变"/>
   <cge:TPSR_Ref TObjectID="6192454523617283"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,1711.86,144.711) scale(1.67984,-1.67984) translate(-684.983,-217.6)" width="23" x="1692.545454545455" y="111.9545471884987"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="858">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="858" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,438.49,980) scale(1,1) translate(0,0)" writing-mode="lr" x="438.07" xml:space="preserve" y="984.64" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135519039490" ObjectName="P"/>
   </metadata>
  </g>
  <g id="859">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="859" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,889.929,25.4545) scale(1,1) translate(0,0)" writing-mode="lr" x="889.51" xml:space="preserve" y="30.1" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135527952386" ObjectName="P"/>
   </metadata>
  </g>
  <g id="860">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="860" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,569.49,980) scale(1,1) translate(0,0)" writing-mode="lr" x="569.0700000000001" xml:space="preserve" y="984.64" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135542370306" ObjectName="P"/>
   </metadata>
  </g>
  <g id="861">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="861" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,705.645,980) scale(1,1) translate(0,0)" writing-mode="lr" x="705.23" xml:space="preserve" y="984.64" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135543943170" ObjectName="P"/>
   </metadata>
  </g>
  <g id="862">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="862" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1502.49,980) scale(1,1) translate(0,0)" writing-mode="lr" x="1502.07" xml:space="preserve" y="984.64" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135545516034" ObjectName="P"/>
   </metadata>
  </g>
  <g id="863">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="863" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1372,980) scale(1,1) translate(0,0)" writing-mode="lr" x="1371.58" xml:space="preserve" y="984.64" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135547088898" ObjectName="P"/>
   </metadata>
  </g>
  <g id="864">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="864" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1239.65,980) scale(1,1) translate(0,0)" writing-mode="lr" x="1239.23" xml:space="preserve" y="984.64" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135548661762" ObjectName="P"/>
   </metadata>
  </g>
  <g id="865">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="865" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,438.49,997.476) scale(1,1) translate(0,0)" writing-mode="lr" x="438.07" xml:space="preserve" y="1002.12" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135519105026" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="866">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="866" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,889.929,45.4545) scale(1,1) translate(0,0)" writing-mode="lr" x="889.51" xml:space="preserve" y="50.1" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135528017922" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="867">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="867" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,569.49,997.476) scale(1,1) translate(0,0)" writing-mode="lr" x="569.0700000000001" xml:space="preserve" y="1002.12" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135542435842" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="868">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="868" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,705.645,997.476) scale(1,1) translate(0,0)" writing-mode="lr" x="705.23" xml:space="preserve" y="1002.12" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135544008706" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="869">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="869" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1502.49,997.476) scale(1,1) translate(0,0)" writing-mode="lr" x="1502.07" xml:space="preserve" y="1002.12" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135545581570" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="870">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="870" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1372,997.476) scale(1,1) translate(0,0)" writing-mode="lr" x="1371.58" xml:space="preserve" y="1002.12" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135547154434" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="871">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="871" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1239.65,997.476) scale(1,1) translate(0,0)" writing-mode="lr" x="1239.23" xml:space="preserve" y="1002.12" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135548727298" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="872">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="872" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,438.49,1014.95) scale(1,1) translate(0,0)" writing-mode="lr" x="438.07" xml:space="preserve" y="1019.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135519170562" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="873">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="873" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,889.929,65.4545) scale(1,1) translate(0,0)" writing-mode="lr" x="889.51" xml:space="preserve" y="70.09999999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135528083458" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="874">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="874" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,569.49,1014.95) scale(1,1) translate(0,0)" writing-mode="lr" x="569.0700000000001" xml:space="preserve" y="1019.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135542501378" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="875">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="875" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,705.645,1014.95) scale(1,1) translate(0,0)" writing-mode="lr" x="705.23" xml:space="preserve" y="1019.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135544074242" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="876">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="876" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1502.49,1014.95) scale(1,1) translate(0,0)" writing-mode="lr" x="1502.07" xml:space="preserve" y="1019.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135545647106" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="877">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="877" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1372,1014.95) scale(1,1) translate(0,0)" writing-mode="lr" x="1371.58" xml:space="preserve" y="1019.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135547219970" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="878">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="878" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1239.65,1014.95) scale(1,1) translate(0,0)" writing-mode="lr" x="1239.23" xml:space="preserve" y="1019.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135548792834" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="914">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="914" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,549.227,210.325) scale(1,1) translate(0,0)" writing-mode="lr" x="548.77" xml:space="preserve" y="214.98" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135532146690" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="918">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="918" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1564.73,208.325) scale(1,1) translate(0,0)" writing-mode="lr" x="1564.27" xml:space="preserve" y="212.98" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135532670978" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="922">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="922" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,848.727,894.714) scale(1,1) translate(8.93427e-14,0)" writing-mode="lr" x="848.27" xml:space="preserve" y="899.37" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135520874498" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="926">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="926" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1128.73,892.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1128.27" xml:space="preserve" y="896.8" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135521398786" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="46">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="46" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,997.143,858.25) scale(1,1) translate(0,0)" writing-mode="lr" x="996.67" xml:space="preserve" y="862.92" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135530049538" ObjectName="P"/>
   </metadata>
  </g>
  <g id="47">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="47" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,997.143,876.5) scale(1,1) translate(0,0)" writing-mode="lr" x="996.67" xml:space="preserve" y="881.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135530115074" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="52" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,997.143,898.5) scale(1,1) translate(0,0)" writing-mode="lr" x="996.67" xml:space="preserve" y="903.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135530180610" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="53" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,546.864,593.136) scale(1,1) translate(0,0)" writing-mode="lr" x="546.39" xml:space="preserve" y="597.8" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135533981698" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="54" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,627.318,594.045) scale(1,1) translate(0,0)" writing-mode="lr" x="626.85" xml:space="preserve" y="598.71" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135535751170" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="55" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1635.5,592.227) scale(1,1) translate(0,0)" writing-mode="lr" x="1635.03" xml:space="preserve" y="596.89" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135538438146" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="56" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1558.68,593.136) scale(1,1) translate(0,0)" writing-mode="lr" x="1558.21" xml:space="preserve" y="597.8" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135539683330" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="57" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,546.864,616.136) scale(1,1) translate(0,0)" writing-mode="lr" x="546.39" xml:space="preserve" y="620.8" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135534047234" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="58">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="58" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,627.318,617.045) scale(1,1) translate(0,0)" writing-mode="lr" x="626.85" xml:space="preserve" y="621.71" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135535816706" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="63" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1635.5,615.227) scale(1,1) translate(0,0)" writing-mode="lr" x="1635.03" xml:space="preserve" y="619.89" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135538503682" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="70">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="70" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1558.68,616.136) scale(1,1) translate(0,0)" writing-mode="lr" x="1558.21" xml:space="preserve" y="620.8" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135539748866" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="72">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="72" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,996.114,376.705) scale(1,1) translate(0,0)" writing-mode="lr" x="995.64" xml:space="preserve" y="381.37" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135522185218" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="73" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,996.114,399.705) scale(1,1) translate(0,0)" writing-mode="lr" x="995.64" xml:space="preserve" y="404.37" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135522250754" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="74">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="74" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,996.114,422.705) scale(1,1) translate(0,0)" writing-mode="lr" x="995.64" xml:space="preserve" y="427.37" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135522316290" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="75">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="75" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,995.159,529) scale(1,1) translate(0,0)" writing-mode="lr" x="994.6900000000001" xml:space="preserve" y="533.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135522643970" ObjectName="MP"/>
   </metadata>
  </g>
  <g id="76">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="76" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,995.159,552) scale(1,1) translate(0,0)" writing-mode="lr" x="994.6900000000001" xml:space="preserve" y="556.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135522709506" ObjectName="MQ"/>
   </metadata>
  </g>
  <g id="77">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="77" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,995.159,575) scale(1,1) translate(0,0)" writing-mode="lr" x="994.6900000000001" xml:space="preserve" y="579.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135522971650" ObjectName="MIa"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="78" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,755.364,535.045) scale(1,1) translate(0,0)" writing-mode="lr" x="754.89" xml:space="preserve" y="539.71" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135522447362" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="92" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,755.364,489.045) scale(1,1) translate(0,0)" writing-mode="lr" x="754.89" xml:space="preserve" y="493.71" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135522775042" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="94" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,755.364,512.045) scale(1,1) translate(0,0)" writing-mode="lr" x="754.89" xml:space="preserve" y="516.71" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135522840578" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="99" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1198.89,378.632) scale(1,1) translate(3.84321e-13,0)" writing-mode="lr" x="1198.42" xml:space="preserve" y="383.3" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135524806658" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="103" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1198.89,401.632) scale(1,1) translate(3.84321e-13,0)" writing-mode="lr" x="1198.42" xml:space="preserve" y="406.3" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135524872194" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="106">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="106" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1198.89,424.632) scale(1,1) translate(3.84321e-13,0)" writing-mode="lr" x="1198.42" xml:space="preserve" y="429.3" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135524937730" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="109">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="109" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1199.68,532.177) scale(1,1) translate(0,0)" writing-mode="lr" x="1199.21" xml:space="preserve" y="536.84" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135525265410" ObjectName="MP"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="111" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1199.68,555.177) scale(1,1) translate(0,1.20943e-13)" writing-mode="lr" x="1199.21" xml:space="preserve" y="559.84" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135525330946" ObjectName="MQ"/>
   </metadata>
  </g>
  <g id="113">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="113" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1199.68,578.177) scale(1,1) translate(0,0)" writing-mode="lr" x="1199.21" xml:space="preserve" y="582.84" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135525593090" ObjectName="MIa"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="115" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1425.39,536.473) scale(1,1) translate(0,0)" writing-mode="lr" x="1424.92" xml:space="preserve" y="541.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135525068802" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="123">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="123" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1425.39,490.473) scale(1,1) translate(0,0)" writing-mode="lr" x="1424.92" xml:space="preserve" y="495.14" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135525396482" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="125">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="125" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1425.39,513.473) scale(1,1) translate(0,0)" writing-mode="lr" x="1424.92" xml:space="preserve" y="518.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135525462018" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="29">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="29" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,336.599,542.25) scale(1,1) translate(-6.91149e-14,0)" writing-mode="lr" x="336.34" xml:space="preserve" y="546.92" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135532474370" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="28">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,335.599,495.694) scale(1,1) translate(-6.88929e-14,0)" writing-mode="lr" x="335.34" xml:space="preserve" y="500.36" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135532670978" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="638">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="638" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,336.599,520.028) scale(1,1) translate(-6.91149e-14,0)" writing-mode="lr" x="336.34" xml:space="preserve" y="524.6900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135532408834" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,336.599,590.028) scale(1,1) translate(-6.91149e-14,-2.57362e-13)" writing-mode="lr" x="336.34" xml:space="preserve" y="594.6900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135532867586" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="25">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,336.599,566.833) scale(1,1) translate(-6.91149e-14,0)" writing-mode="lr" x="336.34" xml:space="preserve" y="571.5" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135532539906" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="634">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="634" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,285.349,542.25) scale(1,1) translate(-5.77351e-14,0)" writing-mode="lr" x="285.09" xml:space="preserve" y="546.92" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135531950082" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="633">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="633" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,285.349,496.694) scale(1,1) translate(-5.77351e-14,0)" writing-mode="lr" x="285.09" xml:space="preserve" y="501.36" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135532146690" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="24">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="24" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,285.349,520.028) scale(1,1) translate(-5.77351e-14,0)" writing-mode="lr" x="285.09" xml:space="preserve" y="524.6900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135531884546" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,285.349,590.028) scale(1,1) translate(-5.77351e-14,-2.57362e-13)" writing-mode="lr" x="285.09" xml:space="preserve" y="594.6900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135532343298" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,285.349,566.833) scale(1,1) translate(-5.77351e-14,0)" writing-mode="lr" x="285.09" xml:space="preserve" y="571.5" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135532015618" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="623">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="623" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,235.349,542.25) scale(1,1) translate(-4.66329e-14,0)" writing-mode="lr" x="235.09" xml:space="preserve" y="546.92" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135521202178" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="21" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,235.349,496.694) scale(1,1) translate(-4.66329e-14,0)" writing-mode="lr" x="235.09" xml:space="preserve" y="501.36" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135521398786" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,235.349,520.028) scale(1,1) translate(-4.66329e-14,0)" writing-mode="lr" x="235.09" xml:space="preserve" y="524.6900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135521136642" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="616">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="616" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,235.349,590.028) scale(1,1) translate(-4.66329e-14,-2.57362e-13)" writing-mode="lr" x="235.09" xml:space="preserve" y="594.6900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135521595394" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="615">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="615" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,235.349,566.833) scale(1,1) translate(-4.66329e-14,0)" writing-mode="lr" x="235.09" xml:space="preserve" y="571.5" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135521267714" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="19" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,184.099,542.25) scale(1,1) translate(-3.52531e-14,0)" writing-mode="lr" x="183.84" xml:space="preserve" y="546.92" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135520677890" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="612">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="612" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,184.099,496.694) scale(1,1) translate(-3.52531e-14,0)" writing-mode="lr" x="183.84" xml:space="preserve" y="501.36" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135520874498" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="346">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="346" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,184.099,520.028) scale(1,1) translate(-3.52531e-14,0)" writing-mode="lr" x="183.84" xml:space="preserve" y="524.6900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135520612354" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,184.099,590.028) scale(1,1) translate(-3.52531e-14,-2.57362e-13)" writing-mode="lr" x="183.84" xml:space="preserve" y="594.6900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135521071106" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,184.099,566.833) scale(1,1) translate(-3.52531e-14,0)" writing-mode="lr" x="183.84" xml:space="preserve" y="571.5" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135520743426" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="16" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,135.349,542.25) scale(1,1) translate(-2.44284e-14,0)" writing-mode="lr" x="135.09" xml:space="preserve" y="546.92" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135526903810" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,135.349,496.694) scale(1,1) translate(-2.44284e-14,0)" writing-mode="lr" x="135.09" xml:space="preserve" y="501.36" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135526117378" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,135.349,520.028) scale(1,1) translate(-2.44284e-14,0)" writing-mode="lr" x="135.09" xml:space="preserve" y="524.6900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135526182914" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,135.349,566.833) scale(1,1) translate(-2.44284e-14,0)" writing-mode="lr" x="135.09" xml:space="preserve" y="571.5" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135526969346" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="11" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,87.8492,542.25) scale(1,1) translate(-1.38813e-14,0)" writing-mode="lr" x="87.59" xml:space="preserve" y="546.92" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135524282370" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(170,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(170,255,255)" text-anchor="middle" transform="rotate(0,87.8492,496.694) scale(1,1) translate(-1.38813e-14,0)" writing-mode="lr" x="87.59" xml:space="preserve" y="501.36" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135523495938" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,87.8492,520.028) scale(1,1) translate(-1.38813e-14,0)" writing-mode="lr" x="87.59" xml:space="preserve" y="524.6900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135523561474" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,87.8492,566.833) scale(1,1) translate(-1.38813e-14,0)" writing-mode="lr" x="87.59" xml:space="preserve" y="571.5" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135524347906" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="6" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,156.127,176.569) scale(1,1) translate(0,-3.70814e-14)" writing-mode="lr" x="155.74" xml:space="preserve" y="181.22" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135550234626" ObjectName="P"/>
   </metadata>
  </g>
  <g id="1069">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="1069" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,337.186,273.016) scale(1,1) translate(0,3.49742e-13)" writing-mode="lr" x="336.95" xml:space="preserve" y="279.18" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135525789698" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,337.186,253.25) scale(1,1) translate(-6.92453e-14,0)" writing-mode="lr" x="336.92" xml:space="preserve" y="257.92" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135532277762" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="1131">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="1131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,156.127,272.405) scale(1,1) translate(0,3.48927e-13)" writing-mode="lr" x="155.89" xml:space="preserve" y="278.57" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135523168258" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="1140">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="1140" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.186,176.569) scale(1,1) translate(0,-3.70814e-14)" writing-mode="lr" x="336.8" xml:space="preserve" y="181.22" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135550300162" ObjectName="P"/>
   </metadata>
  </g>
  <g id="1150">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1150" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,156.127,203.694) scale(1,1) translate(0,0)" writing-mode="lr" x="155.87" xml:space="preserve" y="208.36" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135523627010" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1151">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1151" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,337.186,205.758) scale(1,1) translate(0,0)" writing-mode="lr" x="336.92" xml:space="preserve" y="210.42" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135523627010" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1153">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1153" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,156.127,227.667) scale(1,1) translate(-2.9042e-14,0)" writing-mode="lr" x="155.86" xml:space="preserve" y="232.33" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135521529858" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,156.127,250.917) scale(1,1) translate(-2.9042e-14,0)" writing-mode="lr" x="155.86" xml:space="preserve" y="255.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135532802050" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1189">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1189" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,337.186,227.667) scale(1,1) translate(-6.92453e-14,0)" writing-mode="lr" x="336.92" xml:space="preserve" y="232.33" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135521005570" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,337.186,296.016) scale(1,1) translate(0,0)" writing-mode="lr" x="336.95" xml:space="preserve" y="302.18" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135525527554" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,156.127,295.405) scale(1,1) translate(0,0)" writing-mode="lr" x="155.89" xml:space="preserve" y="301.57" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135522906114" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="353">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="353" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,648.4,107.682) scale(1,1) translate(0,0)" writing-mode="lr" x="647.9299999999999" xml:space="preserve" y="112.35" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135536472066" ObjectName="P"/>
   </metadata>
  </g>
  <g id="355">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="355" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,648.4,130.682) scale(1,1) translate(0,0)" writing-mode="lr" x="647.9299999999999" xml:space="preserve" y="135.35" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135536537602" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="357">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="357" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,648.4,153.682) scale(1,1) translate(0,0)" writing-mode="lr" x="647.9299999999999" xml:space="preserve" y="158.35" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135536603138" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="359">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="359" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1814.72,72.8831) scale(1,1) translate(0,0)" writing-mode="lr" x="1814.25" xml:space="preserve" y="77.55" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135540928514" ObjectName="P"/>
   </metadata>
  </g>
  <g id="360">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="360" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1814.72,95.8831) scale(1,1) translate(0,0)" writing-mode="lr" x="1814.25" xml:space="preserve" y="100.55" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135540994050" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="361">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="361" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1814.72,118.883) scale(1,1) translate(0,0)" writing-mode="lr" x="1814.25" xml:space="preserve" y="123.55" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135541059586" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="183">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="183" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1073.93,150.455) scale(1,1) translate(0,0)" writing-mode="lr" x="1073.51" xml:space="preserve" y="155.1" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135530967042" ObjectName="P"/>
   </metadata>
  </g>
  <g id="182">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="182" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1073.93,170.455) scale(1,1) translate(0,0)" writing-mode="lr" x="1073.51" xml:space="preserve" y="175.1" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135531032578" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="181">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="181" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1073.93,190.455) scale(1,1) translate(0,0)" writing-mode="lr" x="1073.51" xml:space="preserve" y="195.1" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135531098114" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="280">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="280" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,438.49,1037.93) scale(1,1) translate(0,0)" writing-mode="lr" x="438.1" xml:space="preserve" y="1042.66" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135519891458" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="318">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="318" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,569.49,1037.93) scale(1,1) translate(0,0)" writing-mode="lr" x="569.1" xml:space="preserve" y="1042.66" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135543222274" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="319">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="319" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,705.645,1037.93) scale(1,1) translate(0,0)" writing-mode="lr" x="705.26" xml:space="preserve" y="1042.66" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135544795138" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="320">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="320" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1239.65,1037.93) scale(1,1) translate(0,0)" writing-mode="lr" x="1239.26" xml:space="preserve" y="1042.66" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135549513730" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="354">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="354" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1372,1037.93) scale(1,1) translate(0,0)" writing-mode="lr" x="1371.61" xml:space="preserve" y="1042.66" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135547940866" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="356">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="356" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1502.49,1037.93) scale(1,1) translate(0,0)" writing-mode="lr" x="1502.1" xml:space="preserve" y="1042.66" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135546368002" ObjectName="Ux"/>
   </metadata>
  </g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="StateClass">
  <g id="654">
   <use height="30" transform="rotate(0,326.482,342.5) scale(0.708333,0.665547) translate(130.059,167.098)" width="30" x="315.86" xlink:href="#State:红绿圆(方形)_0" y="332.52" zvalue="1243"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374926807041" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,326.482,342.5) scale(0.708333,0.665547) translate(130.059,167.098)" width="30" x="315.86" y="332.52"/></g>
  <g id="3">
   <use height="30" transform="rotate(0,230.857,341.5) scale(0.708333,0.665547) translate(90.6838,166.595)" width="30" x="220.23" xlink:href="#State:红绿圆(方形)_0" y="331.52" zvalue="1244"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562951343374340" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,230.857,341.5) scale(0.708333,0.665547) translate(90.6838,166.595)" width="30" x="220.23" y="331.52"/></g>
  <g id="146">
   <use height="30" transform="rotate(0,328.812,131.464) scale(1.27778,1.03333) translate(-58.981,-3.74077)" width="90" x="271.31" xlink:href="#State:全站检修_0" y="115.96" zvalue="1388"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549682765825" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,328.812,131.464) scale(1.27778,1.03333) translate(-58.981,-3.74077)" width="90" x="271.31" y="115.96"/></g>
 </g>
</svg>