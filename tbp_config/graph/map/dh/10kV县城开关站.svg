<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549581250562" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:传输线_0" viewBox="0,0,12,22">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="1"/>
   <path d="M 1.16667 1 L 10.8333 1 L 6 7.63889 L 1.16667 1 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="14.27777777777778" y2="7.638888888888888"/>
   <path d="M 1.16667 20.9167 L 10.8333 20.9167 L 6 14.2778 L 1.16667 20.9167 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷带壁雷器_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="28.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11.75" y1="17.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="14.75" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="12" y1="11.75" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="9" y1="12.75" y2="12.75"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.04,18.25) scale(1,1) translate(0,0)" width="3.25" x="9.42" y="14.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.5" y1="10.83333333333333" y2="10.83333333333333"/>
   <path d="M 5.025 2.775 L 4.16667 9.5 L 5.025 7.60833 L 5.91667 9.5 L 5.025 2.775" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 5 23.75 L 11 23.75 L 11 17.75 L 10.25 20.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="7.627914951989029" y2="28.23902606310013"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2019.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="Breaker:小车母联_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="17.58333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车母联_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="0.8333333333333304" y2="5.749999999999999"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车母联_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="14" y2="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.666666666666666" x2="7.25" y1="5.833333333333333" y2="14.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:线路PT11带避雷器_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="23.75" y2="23.75"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,11) scale(1,1) translate(0,0)" width="6" x="17" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="20" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="32" y1="17" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.13040845230975" x2="32.13040845230975" y1="16.76788570496156" y2="23.53635804601289"/>
   <rect fill-opacity="0" height="6.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,32.04,24.56) scale(1,1) translate(0,0)" width="15.34" x="24.37" y="21.53"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.98821300514558" x2="19.98821300514558" y1="1.029523490692871" y2="18.75"/>
   <path d="M 30.0147 23.4481 L 34.2865 23.4481 L 32.115 28.6155 L 30.0147 23.4481" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <ellipse cx="19.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.22179295459144" x2="32.22179295459144" y1="32.22550978083666" y2="36.2625308385742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.01788570496155" x2="35.42570020422134" y1="36.23023467011234" y2="36.23023467011234"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.26384963537316" x2="34.53572596821302" y1="37.52208140858838" y2="37.52208140858838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.15382387138148" x2="33.11176719059974" y1="38.49096646244535" y2="38.49096646244535"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="32.91666666666667" y2="32.91666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变带熔断器YY_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0235180220435" x2="10.0235180220435" y1="0.5833333333333091" y2="9.290410445610181"/>
   <rect fill-opacity="0" height="4.32" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.06,5.25) scale(1,1) translate(0,0)" width="3.43" x="8.34" y="3.09"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="29.16666666666667" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="10.8868007916835" y2="12.55855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="14.23031840279781" y2="12.55855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="3" y1="23" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="12" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="12" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0.3333333333333357" y2="12.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="12" y2="12"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id=":线路带避雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15.16666666666667" xlink:href="#terminal" y="39.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.16022336769755" x2="15.16022336769755" y1="39.75" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="6.5" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="8.5" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="7.5" y1="32.25" y2="32.25"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6.04,21.17) scale(1,1) translate(0,0)" width="6.08" x="3" y="14"/>
   <path d="M 15 9.25 L 6 9.25 L 6 21.25 L 6 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="7" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="5" y1="22" y2="16"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV县城开关站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="81" id="1" preserveAspectRatio="xMidYMid slice" width="266" x="53" xlink:href="logo.png" y="38"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,186,78.5) scale(1,1) translate(0,0)" writing-mode="lr" x="186" xml:space="preserve" y="83" zvalue="1439"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,217.088,74.1903) scale(1,1) translate(-3.04201e-14,0)" writing-mode="lr" x="217.0882352941178" xml:space="preserve" y="81.69033132177071" zvalue="1440">10kV县城开关站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="259" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,282.438,360.25) scale(1,1) translate(0,0)" width="72.88" x="246" y="348.25" zvalue="1827"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,282.438,360.25) scale(1,1) translate(0,0)" writing-mode="lr" x="282.44" xml:space="preserve" y="364.75" zvalue="1827">小电流接地</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="258" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,78.625,320.25) scale(1,1) translate(0,0)" width="72.88" x="42.19" y="308.25" zvalue="1828"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.625,320.25) scale(1,1) translate(0,0)" writing-mode="lr" x="78.63" xml:space="preserve" y="324.75" zvalue="1828">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="257" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,180.531,360.25) scale(1,1) translate(0,0)" width="72.88" x="144.09" y="348.25" zvalue="1829"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,180.531,360.25) scale(1,1) translate(0,0)" writing-mode="lr" x="180.53" xml:space="preserve" y="364.75" zvalue="1829">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="255" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,78.625,360.25) scale(1,1) translate(0,0)" width="72.88" x="42.19" y="348.25" zvalue="1831"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.625,360.25) scale(1,1) translate(0,0)" writing-mode="lr" x="78.63" xml:space="preserve" y="364.75" zvalue="1831">信号一览</text>
  <line fill="none" id="67" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382" x2="382" y1="11" y2="1041" zvalue="58"/>
  <line fill="none" id="666" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="146.8704926140824" y2="146.8704926140824" zvalue="1056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="159" y2="159"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="159" y2="159"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="277.5" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="254.75" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="254.75" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="277.5" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="254.75" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="254.75" y2="277.5"/>
  <line fill="none" id="613" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="616.8704926140824" y2="616.8704926140824" zvalue="1058"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.22211450029738" x2="59.56681450029737" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.22211450029738" x2="59.56681450029737" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.22211450029738" x2="11.22211450029738" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56681450029737" x2="59.56681450029737" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56711450029741" x2="193.0001145002974" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56711450029741" x2="193.0001145002974" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56711450029741" x2="59.56711450029741" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.0001145002974" x2="193.0001145002974" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.9999045002974" x2="368.9999045002974" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.9999045002974" x2="368.9999045002974" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.9999045002974" x2="192.9999045002974" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.9999045002974" x2="368.9999045002974" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.22211450029738" x2="59.56681450029737" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.22211450029738" x2="59.56681450029737" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.22211450029738" x2="11.22211450029738" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56681450029737" x2="59.56681450029737" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56711450029741" x2="193.0001145002974" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56711450029741" x2="193.0001145002974" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56711450029741" x2="59.56711450029741" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.0001145002974" x2="193.0001145002974" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.9999045002974" x2="368.9999045002974" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.9999045002974" x2="368.9999045002974" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.9999045002974" x2="192.9999045002974" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.9999045002974" x2="368.9999045002974" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.22211450029738" x2="59.56681450029737" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.22211450029738" x2="59.56681450029737" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.22211450029738" x2="11.22211450029738" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56681450029737" x2="59.56681450029737" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56711450029741" x2="193.0001145002974" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56711450029741" x2="193.0001145002974" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56711450029741" x2="59.56711450029741" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.0001145002974" x2="193.0001145002974" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.9999045002974" x2="368.9999045002974" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.9999045002974" x2="368.9999045002974" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.9999045002974" x2="192.9999045002974" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.9999045002974" x2="368.9999045002974" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.22211450029738" x2="59.56681450029737" y1="525.4940335058594" y2="525.4940335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.22211450029738" x2="59.56681450029737" y1="549.6626335058594" y2="549.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.22211450029738" x2="11.22211450029738" y1="525.4940335058594" y2="549.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56681450029737" x2="59.56681450029737" y1="525.4940335058594" y2="549.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56711450029741" x2="193.0001145002974" y1="525.4940335058594" y2="525.4940335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56711450029741" x2="193.0001145002974" y1="549.6626335058594" y2="549.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56711450029741" x2="59.56711450029741" y1="525.4940335058594" y2="549.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.0001145002974" x2="193.0001145002974" y1="525.4940335058594" y2="549.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.9999045002974" x2="368.9999045002974" y1="525.4940335058594" y2="525.4940335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.9999045002974" x2="368.9999045002974" y1="549.6626335058594" y2="549.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.9999045002974" x2="192.9999045002974" y1="525.4940335058594" y2="549.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.9999045002974" x2="368.9999045002974" y1="525.4940335058594" y2="549.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.22211450029738" x2="59.56681450029737" y1="549.6628435058594" y2="549.6628435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.22211450029738" x2="59.56681450029737" y1="573.8314435058594" y2="573.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.22211450029738" x2="11.22211450029738" y1="549.6628435058594" y2="573.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56681450029737" x2="59.56681450029737" y1="549.6628435058594" y2="573.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56711450029741" x2="193.0001145002974" y1="549.6628435058594" y2="549.6628435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56711450029741" x2="193.0001145002974" y1="573.8314435058594" y2="573.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56711450029741" x2="59.56711450029741" y1="549.6628435058594" y2="573.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.0001145002974" x2="193.0001145002974" y1="549.6628435058594" y2="573.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.9999045002974" x2="368.9999045002974" y1="549.6628435058594" y2="549.6628435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.9999045002974" x2="368.9999045002974" y1="573.8314435058594" y2="573.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.9999045002974" x2="192.9999045002974" y1="549.6628435058594" y2="573.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.9999045002974" x2="368.9999045002974" y1="549.6628435058594" y2="573.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.22211450029738" x2="59.56681450029737" y1="573.8314435058594" y2="573.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.22211450029738" x2="59.56681450029737" y1="598.0000435058594" y2="598.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.22211450029738" x2="11.22211450029738" y1="573.8314435058594" y2="598.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56681450029737" x2="59.56681450029737" y1="573.8314435058594" y2="598.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56711450029741" x2="193.0001145002974" y1="573.8314435058594" y2="573.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56711450029741" x2="193.0001145002974" y1="598.0000435058594" y2="598.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.56711450029741" x2="59.56711450029741" y1="573.8314435058594" y2="598.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.0001145002974" x2="193.0001145002974" y1="573.8314435058594" y2="598.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.9999045002974" x2="368.9999045002974" y1="573.8314435058594" y2="573.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.9999045002974" x2="368.9999045002974" y1="598.0000435058594" y2="598.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.9999045002974" x2="192.9999045002974" y1="573.8314435058594" y2="598.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.9999045002974" x2="368.9999045002974" y1="573.8314435058594" y2="598.0000435058594"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="999.0816" y2="1027"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="609" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,952) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="958" zvalue="1062">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="608" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50,986) scale(1,1) translate(0,0)" writing-mode="lr" x="50" xml:space="preserve" y="992" zvalue="1063">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="607" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232,986) scale(1,1) translate(0,0)" writing-mode="lr" x="232" xml:space="preserve" y="992" zvalue="1064">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="606" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="49" xml:space="preserve" y="1020" zvalue="1065">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="605" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="231" xml:space="preserve" y="1020" zvalue="1066">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="602" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.5,646.5) scale(1,1) translate(0,0)" writing-mode="lr" x="73.5" xml:space="preserve" y="651" zvalue="1069">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="599" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,123.333,457.5) scale(1,1) translate(0,0)" writing-mode="lr" x="123.3333333333333" xml:space="preserve" y="462" zvalue="1072">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="598" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,271.333,458.5) scale(1,1) translate(0,0)" writing-mode="lr" x="271.3333333333335" xml:space="preserve" y="463" zvalue="1073">10kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="596" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,39,489.5) scale(1,1) translate(0,0)" writing-mode="lr" x="39" xml:space="preserve" y="494" zvalue="1075">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="595" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,37,515) scale(1,1) translate(0,0)" writing-mode="lr" x="37" xml:space="preserve" y="519.5" zvalue="1076">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="594" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,37,540.5) scale(1,1) translate(0,0)" writing-mode="lr" x="37" xml:space="preserve" y="545" zvalue="1077">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="593" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,36,565) scale(1,1) translate(0,0)" writing-mode="lr" x="36" xml:space="preserve" y="569.5" zvalue="1078">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="592" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,37,591.5) scale(1,1) translate(0,0)" writing-mode="lr" x="37" xml:space="preserve" y="596" zvalue="1079">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.054,954) scale(1,1) translate(0,0)" writing-mode="lr" x="233.05" xml:space="preserve" y="960" zvalue="1080">XianCheng-01-2015</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,143.054,986) scale(1,1) translate(0,0)" writing-mode="lr" x="143.05" xml:space="preserve" y="992" zvalue="1081">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,323.054,986) scale(1,1) translate(0,0)" writing-mode="lr" x="323.05" xml:space="preserve" y="992" zvalue="1082">20210105</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47,173) scale(1,1) translate(0,0)" writing-mode="lr" x="47" xml:space="preserve" y="178.5" zvalue="1083">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227,173) scale(1,1) translate(0,0)" writing-mode="lr" x="227" xml:space="preserve" y="178.5" zvalue="1084">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.6875,196.25) scale(1,1) translate(0,0)" writing-mode="lr" x="54.69" xml:space="preserve" y="200.75" zvalue="1085">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.125,197.25) scale(1,1) translate(0,0)" writing-mode="lr" x="234.13" xml:space="preserve" y="201.75" zvalue="1086">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,466.125,498.75) scale(1,1) translate(0,0)" writing-mode="lr" x="466.13" xml:space="preserve" y="503.25" zvalue="1443">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1003.39,365.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1003.39" xml:space="preserve" y="369.64" zvalue="1482">10kVⅠ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,972.153,458.75) scale(1,1) translate(0,0)" writing-mode="lr" x="972.15" xml:space="preserve" y="463.25" zvalue="1484">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,926.957,796.861) scale(1,1) translate(0,0)" writing-mode="lr" x="926.96" xml:space="preserve" y="801.36" zvalue="1564">10kV县振线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,953.525,558.5) scale(1,1) translate(0,0)" writing-mode="lr" x="953.52" xml:space="preserve" y="563" zvalue="1572">037</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,974.5,637.083) scale(1,1) translate(0,0)" writing-mode="lr" x="974.5" xml:space="preserve" y="641.58" zvalue="1576">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1026.96,796.861) scale(1,1) translate(0,0)" writing-mode="lr" x="1026.96" xml:space="preserve" y="801.36" zvalue="1582">备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1053.52,558.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1053.52" xml:space="preserve" y="563" zvalue="1585">038</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1074.5,637.083) scale(1,1) translate(0,0)" writing-mode="lr" x="1074.5" xml:space="preserve" y="641.58" zvalue="1588">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,483.5,797.111) scale(1,1) translate(0,0)" writing-mode="lr" x="483.5" xml:space="preserve" y="801.61" zvalue="1593">备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,510.068,558.75) scale(1,1) translate(0,0)" writing-mode="lr" x="510.07" xml:space="preserve" y="563.25" zvalue="1596">033</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,531.043,637.333) scale(1,1) translate(0,0)" writing-mode="lr" x="531.04" xml:space="preserve" y="641.83" zvalue="1599">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,586.472,797.222) scale(1,1) translate(0,0)" writing-mode="lr" x="586.47" xml:space="preserve" y="801.72" zvalue="1604">10kV县曩线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,613.068,558.75) scale(1,1) translate(0,0)" writing-mode="lr" x="613.0700000000001" xml:space="preserve" y="563.25" zvalue="1607">034</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,634.043,637.333) scale(1,1) translate(0,0)" writing-mode="lr" x="634.04" xml:space="preserve" y="641.83" zvalue="1610">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,697.5,796.111) scale(1,1) translate(0,0)" writing-mode="lr" x="697.5" xml:space="preserve" y="800.61" zvalue="1626">10kV县南线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,725.068,558.75) scale(1,1) translate(0,0)" writing-mode="lr" x="725.0700000000001" xml:space="preserve" y="563.25" zvalue="1629">035</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,746.043,637.333) scale(1,1) translate(0,0)" writing-mode="lr" x="746.04" xml:space="preserve" y="641.83" zvalue="1632">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,809.5,796.111) scale(1,1) translate(0,0)" writing-mode="lr" x="809.5" xml:space="preserve" y="800.61" zvalue="1637">10kV县永线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,837.068,558.75) scale(1,1) translate(0,0)" writing-mode="lr" x="837.0700000000001" xml:space="preserve" y="563.25" zvalue="1640">036</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,858.043,637.333) scale(1,1) translate(0,0)" writing-mode="lr" x="858.04" xml:space="preserve" y="641.83" zvalue="1643">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1837.12,497.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1837.13" xml:space="preserve" y="502.25" zvalue="1648">10kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1776.39,364.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1776.39" xml:space="preserve" y="368.64" zvalue="1650">10kVⅡ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1747.81,464.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1747.81" xml:space="preserve" y="469.06" zvalue="1652">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1726.52,557.5) scale(1,1) translate(0,-1.22014e-13)" writing-mode="lr" x="1726.52" xml:space="preserve" y="562" zvalue="1659">047</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1747.5,636.083) scale(1,1) translate(0,0)" writing-mode="lr" x="1747.5" xml:space="preserve" y="640.58" zvalue="1662">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" x="1800.71875" xml:space="preserve" y="802.1788170072767" zvalue="1666">10kV县九线(丙盖</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1800.71875" xml:space="preserve" y="818.1788170072767" zvalue="1666">村光伏)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1826.52,557.5) scale(1,1) translate(0,-1.22014e-13)" writing-mode="lr" x="1826.52" xml:space="preserve" y="562" zvalue="1669">048</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1847.5,636.083) scale(1,1) translate(0,0)" writing-mode="lr" x="1847.5" xml:space="preserve" y="640.58" zvalue="1672">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1256.5,796.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1256.5" xml:space="preserve" y="800.61" zvalue="1676">10kV县红线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1283.07,557.75) scale(1,1) translate(0,1.22069e-13)" writing-mode="lr" x="1283.07" xml:space="preserve" y="562.25" zvalue="1679">043</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1304.04,636.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1304.04" xml:space="preserve" y="640.83" zvalue="1682">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1359.5,796.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1359.5" xml:space="preserve" y="800.61" zvalue="1686">10kV县三线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1386.07,557.75) scale(1,1) translate(0,1.22069e-13)" writing-mode="lr" x="1386.07" xml:space="preserve" y="562.25" zvalue="1689">044</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1407.04,636.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1407.04" xml:space="preserve" y="640.83" zvalue="1692">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1471.5,796.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1471.5" xml:space="preserve" y="800.61" zvalue="1696">备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1498.07,557.75) scale(1,1) translate(0,1.22069e-13)" writing-mode="lr" x="1498.07" xml:space="preserve" y="562.25" zvalue="1699">045</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1519.04,636.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1519.04" xml:space="preserve" y="640.83" zvalue="1702">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1583.5,796.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1583.5" xml:space="preserve" y="800.61" zvalue="1706">10kV县宾线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1610.07,557.75) scale(1,1) translate(0,1.22069e-13)" writing-mode="lr" x="1610.07" xml:space="preserve" y="562.25" zvalue="1709">046</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1631.04,636.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1631.04" xml:space="preserve" y="640.83" zvalue="1712">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="170" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1145.5,406) scale(1,1) translate(0,0)" writing-mode="lr" x="1145.5" xml:space="preserve" y="410.5" zvalue="1718">10kV分段012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1196.39,469.139) scale(1,1) translate(0,-2.04787e-13)" writing-mode="lr" x="1196.39" xml:space="preserve" y="473.64" zvalue="1722">0122</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="178" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,569.46,182.333) scale(1,1) translate(0,0)" writing-mode="lr" x="569.46" xml:space="preserve" y="186.83" zvalue="1725">10kV梁县Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,598.756,458.75) scale(1,1) translate(-1.30454e-13,0)" writing-mode="lr" x="598.76" xml:space="preserve" y="463.25" zvalue="1728">031</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="189" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,684.889,178.333) scale(1,1) translate(0,0)" writing-mode="lr" x="684.89" xml:space="preserve" y="182.83" zvalue="1736">备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,710.006,458.75) scale(1,1) translate(7.76059e-13,0)" writing-mode="lr" x="710.01" xml:space="preserve" y="463.25" zvalue="1738">032</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,860.111,304.778) scale(1,1) translate(0,0)" writing-mode="lr" x="860.11" xml:space="preserve" y="309.28" zvalue="1746">10kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,832.167,458.75) scale(1,1) translate(0,0)" writing-mode="lr" x="832.17" xml:space="preserve" y="463.25" zvalue="1748">0811</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="212" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1338.35,176.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1338.35" xml:space="preserve" y="180.61" zvalue="1755">10kV梁县Ⅱ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1366.4,456.528) scale(1,1) translate(0,0)" writing-mode="lr" x="1366.4" xml:space="preserve" y="461.03" zvalue="1757">041</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="210" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1453.78,176.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1453.78" xml:space="preserve" y="180.61" zvalue="1765">备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1478.4,456.528) scale(1,1) translate(0,0)" writing-mode="lr" x="1478.4" xml:space="preserve" y="461.03" zvalue="1767">042</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1629,302.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1629" xml:space="preserve" y="307.06" zvalue="1775">10kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1601.06,456.528) scale(1,1) translate(0,0)" writing-mode="lr" x="1601.06" xml:space="preserve" y="461.03" zvalue="1777">0822</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,164.97,319.699) scale(1,1) translate(0,0)" writing-mode="lr" x="164.97" xml:space="preserve" y="324.2" zvalue="1823">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,265.97,319.699) scale(1,1) translate(0,0)" writing-mode="lr" x="265.97" xml:space="preserve" y="324.2" zvalue="1824">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" x="1699.453125" xml:space="preserve" y="801" zvalue="1850">10kV芒县线(有</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1699.453125" xml:space="preserve" y="817" zvalue="1850">小电)</text>
 </g>
 <g id="ButtonClass">
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="246" y="348.25" zvalue="1827"/></g>
  <g href="全站公用20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="42.19" y="308.25" zvalue="1828"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="144.09" y="348.25" zvalue="1829"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="42.19" y="348.25" zvalue="1831"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="236">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,121.222,515.167) scale(1,1) translate(-2.09462e-14,0)" writing-mode="lr" x="121.34" xml:space="preserve" y="520.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123830956036" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="237">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="237" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,121.222,537.667) scale(1,1) translate(0,0)" writing-mode="lr" x="121.34" xml:space="preserve" y="542.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123831021572" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="238">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="238" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,120.222,563.167) scale(1,1) translate(-2.07242e-14,0)" writing-mode="lr" x="120.34" xml:space="preserve" y="568.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123831087108" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="235">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="235" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,121.222,489.667) scale(1,1) translate(-2.09462e-14,0)" writing-mode="lr" x="121.34" xml:space="preserve" y="494.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123831218180" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,272.222,515.167) scale(1,1) translate(-5.44749e-14,0)" writing-mode="lr" x="272.34" xml:space="preserve" y="520.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123885481988" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="241" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,272.222,537.667) scale(1,1) translate(0,0)" writing-mode="lr" x="272.34" xml:space="preserve" y="542.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123885547524" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="242">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="242" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,272.222,562.167) scale(1,1) translate(-5.44749e-14,0)" writing-mode="lr" x="272.34" xml:space="preserve" y="567.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123885613060" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="239">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="239" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,272.222,489.667) scale(1,1) translate(-5.44749e-14,0)" writing-mode="lr" x="272.34" xml:space="preserve" y="494.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123885744132" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,146.222,172.167) scale(1,1) translate(0,0)" writing-mode="lr" x="146.42" xml:space="preserve" y="177.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123897344006" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,325.222,173.167) scale(1,1) translate(0,0)" writing-mode="lr" x="325.42" xml:space="preserve" y="178.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123897409540" ObjectName="LOAD_QSUM"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="20" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,568.793,119.5) scale(1,1) translate(0,0)" writing-mode="lr" x="568.3200000000001" xml:space="preserve" y="124.28" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123886923780" ObjectName="P"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="21" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1335.68,117.278) scale(1,1) translate(0,0)" writing-mode="lr" x="1335.21" xml:space="preserve" y="122.06" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123892953092" ObjectName="P"/>
   </metadata>
  </g>
  <g id="44">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="44" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,568.793,140.5) scale(1,1) translate(0,0)" writing-mode="lr" x="568.3200000000001" xml:space="preserve" y="145.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123886989316" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="45">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="45" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1335.68,134.278) scale(1,1) translate(0,0)" writing-mode="lr" x="1335.21" xml:space="preserve" y="139.06" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123893018628" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="46">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="46" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,568.793,157.5) scale(1,1) translate(0,0)" writing-mode="lr" x="568.3200000000001" xml:space="preserve" y="162.28" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123887054852" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="47">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="47" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1335.68,150.278) scale(1,1) translate(0,0)" writing-mode="lr" x="1335.21" xml:space="preserve" y="155.06" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123893084164" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="48">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="48" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,580.083,830.833) scale(1,1) translate(0,0)" writing-mode="lr" x="579.61" xml:space="preserve" y="835.61" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123868835845" ObjectName="P"/>
   </metadata>
  </g>
  <g id="49">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="49" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,691.111,829.722) scale(1,1) translate(0,0)" writing-mode="lr" x="690.64" xml:space="preserve" y="834.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123874471940" ObjectName="P"/>
   </metadata>
  </g>
  <g id="50">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="50" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,803.111,830.722) scale(1,1) translate(0,0)" writing-mode="lr" x="802.64" xml:space="preserve" y="835.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123875913732" ObjectName="P"/>
   </metadata>
  </g>
  <g id="51">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="51" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,920.568,831.472) scale(1,1) translate(0,0)" writing-mode="lr" x="920.1" xml:space="preserve" y="836.25" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123832004612" ObjectName="P"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="52" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1250.11,830.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1249.64" xml:space="preserve" y="835.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123881680900" ObjectName="P"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="53" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1353.11,830.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1352.64" xml:space="preserve" y="835.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123880239108" ObjectName="P"/>
   </metadata>
  </g>
  <g id="123">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="123" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1577.11,830.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1576.64" xml:space="preserve" y="835.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123877355524" ObjectName="P"/>
   </metadata>
  </g>
  <g id="127">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="127" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1793.57,830.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1793.1" xml:space="preserve" y="835.25" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123883122692" ObjectName="P"/>
   </metadata>
  </g>
  <g id="129">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="129" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,580.083,848.833) scale(1,1) translate(0,0)" writing-mode="lr" x="579.61" xml:space="preserve" y="853.61" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123873095684" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="131">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="131" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,691.111,847.722) scale(1,1) translate(0,0)" writing-mode="lr" x="690.64" xml:space="preserve" y="852.5" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123874537476" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="132">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="132" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,803.111,847.722) scale(1,1) translate(0,0)" writing-mode="lr" x="802.64" xml:space="preserve" y="852.5" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123875979268" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="134" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,920.568,848.472) scale(1,1) translate(0,0)" writing-mode="lr" x="920.1" xml:space="preserve" y="853.25" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123832070148" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="167">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="167" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1250.11,847.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1249.64" xml:space="preserve" y="852.5" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123881746436" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="168">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="168" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1353.11,847.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1352.64" xml:space="preserve" y="852.5" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123880304644" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="172">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="172" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1577.11,847.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1576.64" xml:space="preserve" y="852.5" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123877421063" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="201">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="201" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1793.57,847.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1793.1" xml:space="preserve" y="852.25" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123883188228" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="202">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="202" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,580.083,865.833) scale(1,1) translate(0,0)" writing-mode="lr" x="579.61" xml:space="preserve" y="870.61" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123873161220" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="205">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="205" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,691.111,864.722) scale(1,1) translate(0,0)" writing-mode="lr" x="690.64" xml:space="preserve" y="869.5" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123874603012" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="233">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="233" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,802.111,865.722) scale(1,1) translate(0,0)" writing-mode="lr" x="801.64" xml:space="preserve" y="870.5" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123876044804" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,919.568,866.472) scale(1,1) translate(0,0)" writing-mode="lr" x="919.1" xml:space="preserve" y="871.25" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123832135684" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="243">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="243" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1249.11,865.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1248.64" xml:space="preserve" y="870.5" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123881811972" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="246" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1352.11,865.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1351.64" xml:space="preserve" y="870.5" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123880370180" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="247">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="247" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1576.11,865.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1575.64" xml:space="preserve" y="870.5" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123877486598" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="249">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="249" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1792.57,865.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1792.1" xml:space="preserve" y="870.25" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123883253764" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="251">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="251" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,120.222,587.167) scale(1,1) translate(-2.07242e-14,0)" writing-mode="lr" x="120.34" xml:space="preserve" y="592.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123831414788" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="252">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="252" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,272.222,586.167) scale(1,1) translate(-5.44749e-14,0)" writing-mode="lr" x="272.34" xml:space="preserve" y="591.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123885940740" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="253">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="253" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,145.222,197.167) scale(1,1) translate(0,0)" writing-mode="lr" x="145.42" xml:space="preserve" y="202.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123831349252" ObjectName="F"/>
   </metadata>
  </g>
  <g id="254">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="254" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,324.222,197.167) scale(1,1) translate(0,0)" writing-mode="lr" x="324.42" xml:space="preserve" y="202.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123885875204" ObjectName="F"/>
   </metadata>
  </g>
  <g id="72">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="72" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1000.25,343.583) scale(1,1) translate(0,0)" writing-mode="lr" x="999.78" xml:space="preserve" y="348.36" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123831218180" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="133">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="133" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1777,343.583) scale(1,1) translate(0,0)" writing-mode="lr" x="1776.53" xml:space="preserve" y="348.36" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123885744132" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="139">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="139" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1140,333.793) scale(1,1) translate(0,0)" writing-mode="lr" x="1139.53" xml:space="preserve" y="338.57" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123886006276" ObjectName="P"/>
   </metadata>
  </g>
  <g id="146">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="146" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1140,358.293) scale(1,1) translate(0,0)" writing-mode="lr" x="1139.53" xml:space="preserve" y="363.07" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123886071812" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="153">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="153" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1140,382.793) scale(1,1) translate(0,0)" writing-mode="lr" x="1139.53" xml:space="preserve" y="387.57" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123886137348" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="192">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="192" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1700.43,830.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1699.96" xml:space="preserve" y="835.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123884564484" ObjectName="P"/>
   </metadata>
  </g>
  <g id="219">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="219" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1700.43,847.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1699.96" xml:space="preserve" y="852.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123884630020" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="227">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="227" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1700.43,866) scale(1,1) translate(0,0)" writing-mode="lr" x="1699.96" xml:space="preserve" y="870.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123884695556" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="BusbarSectionClass">
  <g id="16">
   <path class="kv10" d="M 447.25 514.75 L 1091 514.75" stroke-width="4" zvalue="1442"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674232238084" ObjectName="10kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674232238084"/></metadata>
  <path d="M 447.25 514.75 L 1091 514.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv10" d="M 1195 513.75 L 1852.75 513.75" stroke-width="4" zvalue="1647"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674232303621" ObjectName="10kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674232303621"/></metadata>
  <path d="M 1195 513.75 L 1852.75 513.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="29">
   <use class="kv10" height="42" transform="rotate(180,991.306,403.222) scale(1.36111,-1.07143) translate(-257.583,-778.063)" width="30" x="970.888888888889" xlink:href="#Accessory:4卷PT带容断器_0" y="380.7222258779737" zvalue="1481"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449510244357" ObjectName="10kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(180,991.306,403.222) scale(1.36111,-1.07143) translate(-257.583,-778.063)" width="30" x="970.888888888889" y="380.7222258779737"/></g>
  <g id="165">
   <use class="kv10" height="42" transform="rotate(180,1764.31,402.222) scale(1.36111,-1.07143) translate(-462.664,-776.13)" width="30" x="1743.888888888889" xlink:href="#Accessory:4卷PT带容断器_0" y="379.7222258779737" zvalue="1649"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449512734726" ObjectName="10kVⅡ母电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(180,1764.31,402.222) scale(1.36111,-1.07143) translate(-462.664,-776.13)" width="30" x="1743.888888888889" y="379.7222258779737"/></g>
  <g id="184">
   <use class="kv10" height="22" transform="rotate(0,571.556,357.222) scale(1.11111,1.11111) translate(-56.4889,-34.5)" width="12" x="564.8888888888887" xlink:href="#Accessory:传输线_0" y="345" zvalue="1730"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449512931334" ObjectName="10kV梁县Ⅰ回线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,571.556,357.222) scale(1.11111,1.11111) translate(-56.4889,-34.5)" width="12" x="564.8888888888887" y="345"/></g>
  <g id="186">
   <use class="kv10" height="40" transform="rotate(0,548.222,321.333) scale(-1.11111,-1.11111) translate(-1039.4,-608.311)" width="40" x="526.0000000000001" xlink:href="#Accessory:线路PT11带避雷器_0" y="299.1111111111111" zvalue="1732"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449512996870" ObjectName="10kV梁县Ⅰ回线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,548.222,321.333) scale(-1.11111,-1.11111) translate(-1039.4,-608.311)" width="40" x="526.0000000000001" y="299.1111111111111"/></g>
  <g id="193">
   <use class="kv10" height="22" transform="rotate(0,683.556,358.222) scale(1.11111,1.11111) translate(-67.6889,-34.6)" width="12" x="676.8888888888889" xlink:href="#Accessory:传输线_0" y="346" zvalue="1741"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449513127942" ObjectName="10kV032备用线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,683.556,358.222) scale(1.11111,1.11111) translate(-67.6889,-34.6)" width="12" x="676.8888888888889" y="346"/></g>
  <g id="191">
   <use class="kv10" height="40" transform="rotate(0,660.222,321.333) scale(-1.11111,-1.11111) translate(-1252.2,-608.311)" width="40" x="638.0000000000001" xlink:href="#Accessory:线路PT11带避雷器_0" y="299.1111111111111" zvalue="1743"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449513062406" ObjectName="10kV032备用线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,660.222,321.333) scale(-1.11111,-1.11111) translate(-1252.2,-608.311)" width="40" x="638.0000000000001" y="299.1111111111111"/></g>
  <g id="228">
   <use class="kv10" height="22" transform="rotate(0,1340.44,356) scale(1.11111,1.11111) translate(-133.378,-34.3778)" width="12" x="1333.777777777778" xlink:href="#Accessory:传输线_0" y="343.7777777777778" zvalue="1760"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449513783302" ObjectName="10kV梁县Ⅱ回线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1340.44,356) scale(1.11111,1.11111) translate(-133.378,-34.3778)" width="12" x="1333.777777777778" y="343.7777777777778"/></g>
  <g id="226">
   <use class="kv10" height="40" transform="rotate(0,1317.11,319.111) scale(-1.11111,-1.11111) translate(-2500.29,-604.089)" width="40" x="1294.888888888889" xlink:href="#Accessory:线路PT11带避雷器_0" y="296.8888888888889" zvalue="1762"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449513717766" ObjectName="10kV梁县Ⅱ回线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1317.11,319.111) scale(-1.11111,-1.11111) translate(-2500.29,-604.089)" width="40" x="1294.888888888889" y="296.8888888888889"/></g>
  <g id="220">
   <use class="kv10" height="22" transform="rotate(0,1452.44,356) scale(1.11111,1.11111) translate(-144.578,-34.3778)" width="12" x="1445.777777777778" xlink:href="#Accessory:传输线_0" y="343.7777777777778" zvalue="1770"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449513586694" ObjectName="10kV042备用线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1452.44,356) scale(1.11111,1.11111) translate(-144.578,-34.3778)" width="12" x="1445.777777777778" y="343.7777777777778"/></g>
  <g id="218">
   <use class="kv10" height="40" transform="rotate(0,1429.11,319.111) scale(-1.11111,-1.11111) translate(-2713.09,-604.089)" width="40" x="1406.888888888889" xlink:href="#Accessory:线路PT11带避雷器_0" y="296.8888888888889" zvalue="1772"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449513521158" ObjectName="10kV042备用线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1429.11,319.111) scale(-1.11111,-1.11111) translate(-2713.09,-604.089)" width="40" x="1406.888888888889" y="296.8888888888889"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="24">
   <use class="kv10" height="26" transform="rotate(0,1003.89,466.913) scale(1.25,1.25) translate(-199.278,-90.1327)" width="12" x="996.3888888888889" xlink:href="#Disconnector:手车刀闸2020_0" y="450.6634559631348" zvalue="1483"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449510178821" ObjectName="10kVⅠ母电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449510178821"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1003.89,466.913) scale(1.25,1.25) translate(-199.278,-90.1327)" width="12" x="996.3888888888889" y="450.6634559631348"/></g>
  <g id="164">
   <use class="kv10" height="26" transform="rotate(0,1776.89,458.139) scale(1.25,1.25) translate(-353.878,-88.3778)" width="12" x="1769.388888888889" xlink:href="#Disconnector:手车刀闸2020_0" y="441.8888925446404" zvalue="1651"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449512669190" ObjectName="10kVⅡ母电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449512669190"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1776.89,458.139) scale(1.25,1.25) translate(-353.878,-88.3778)" width="12" x="1769.388888888889" y="441.8888925446404"/></g>
  <g id="173">
   <use class="kv10" height="26" transform="rotate(0,1219.89,470.139) scale(1.25,1.25) translate(-242.478,-90.7778)" width="12" x="1212.388888888889" xlink:href="#Disconnector:手车刀闸2020_0" y="453.8888925446404" zvalue="1721"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449512800262" ObjectName="10kV分段0122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449512800262"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1219.89,470.139) scale(1.25,1.25) translate(-242.478,-90.7778)" width="12" x="1212.388888888889" y="453.8888925446404"/></g>
  <g id="200">
   <use class="kv10" height="26" transform="rotate(0,857.222,466.913) scale(1.25,1.25) translate(-169.944,-90.1327)" width="12" x="849.7222222222223" xlink:href="#Disconnector:手车刀闸2020_0" y="450.6634561220804" zvalue="1747"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449513324550" ObjectName="10kV1号站用变0811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449513324550"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,857.222,466.913) scale(1.25,1.25) translate(-169.944,-90.1327)" width="12" x="849.7222222222223" y="450.6634561220804"/></g>
  <g id="215">
   <use class="kv10" height="26" transform="rotate(0,1626.11,464.691) scale(1.25,1.25) translate(-323.722,-89.6882)" width="12" x="1618.611111111111" xlink:href="#Disconnector:手车刀闸2020_0" y="448.4412338998582" zvalue="1776"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449513390086" ObjectName="10kV2号站用变0822隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449513390086"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1626.11,464.691) scale(1.25,1.25) translate(-323.722,-89.6882)" width="12" x="1618.611111111111" y="448.4412338998582"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="23">
   <path class="kv10" d="M 1004.26 425.05 L 1004.26 450.76" stroke-width="1" zvalue="1485"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="29@0" LinkObjectIDznd="24@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004.26 425.05 L 1004.26 450.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv10" d="M 1003.99 483.12 L 1003.99 514.75" stroke-width="1" zvalue="1486"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@0" LinkObjectIDznd="16@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1003.99 483.12 L 1003.99 514.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv10" d="M 927.6 537.84 L 927.6 514.75" stroke-width="1" zvalue="1573"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="16@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 927.6 537.84 L 927.6 514.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv10" d="M 927.62 741.73 L 927.6 580.57" stroke-width="1" zvalue="1578"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@0" LinkObjectIDznd="130@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 927.62 741.73 L 927.6 580.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="kv10" d="M 955.96 622.24 L 955.96 600 L 927.6 600" stroke-width="1" zvalue="1579"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="1" MaxPinNum="2"/>
   </metadata>
  <path d="M 955.96 622.24 L 955.96 600 L 927.6 600" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv10" d="M 1027.6 537.84 L 1027.6 514.75" stroke-width="1" zvalue="1586"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="16@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1027.6 537.84 L 1027.6 514.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv10" d="M 1027.62 741.73 L 1027.6 580.57" stroke-width="1" zvalue="1589"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="12@0" LinkObjectIDznd="9@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1027.62 741.73 L 1027.6 580.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv10" d="M 1055.96 622.24 L 1055.96 600 L 1027.6 600" stroke-width="1" zvalue="1590"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8@0" LinkObjectIDznd="7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1055.96 622.24 L 1055.96 600 L 1027.6 600" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv10" d="M 484.14 538.09 L 484.14 514.75" stroke-width="1" zvalue="1597"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31@0" LinkObjectIDznd="16@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 484.14 538.09 L 484.14 514.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv10" d="M 484.17 741.98 L 484.14 580.82" stroke-width="1" zvalue="1600"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="31@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 484.17 741.98 L 484.14 580.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv10" d="M 512.51 622.49 L 512.51 600.25 L 484.14 600.25" stroke-width="1" zvalue="1601"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="27" MaxPinNum="2"/>
   </metadata>
  <path d="M 512.51 622.49 L 512.51 600.25 L 484.14 600.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv10" d="M 587.14 538.09 L 587.14 514.75" stroke-width="1" zvalue="1608"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="16@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 587.14 538.09 L 587.14 514.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv10" d="M 587.14 742.09 L 587.14 580.82" stroke-width="1" zvalue="1611"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="41@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 587.14 742.09 L 587.14 580.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv10" d="M 615.51 622.49 L 615.51 600.25 L 587.14 600.25" stroke-width="1" zvalue="1612"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@0" LinkObjectIDznd="38" MaxPinNum="2"/>
   </metadata>
  <path d="M 615.51 622.49 L 615.51 600.25 L 587.14 600.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv10" d="M 699.14 538.09 L 699.14 514.75" stroke-width="1" zvalue="1630"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="16@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 699.14 538.09 L 699.14 514.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv10" d="M 698.17 740.98 L 698.17 580.82" stroke-width="1" zvalue="1633"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@0" LinkObjectIDznd="71@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 698.17 740.98 L 698.17 580.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv10" d="M 727.51 622.49 L 727.51 600.25 L 698.17 600.25" stroke-width="1" zvalue="1634"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="68" MaxPinNum="2"/>
   </metadata>
  <path d="M 727.51 622.49 L 727.51 600.25 L 698.17 600.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv10" d="M 811.14 538.09 L 811.14 514.75" stroke-width="1" zvalue="1641"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@0" LinkObjectIDznd="16@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 811.14 538.09 L 811.14 514.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv10" d="M 810.17 740.98 L 810.17 580.82" stroke-width="1" zvalue="1644"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@0" LinkObjectIDznd="81@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.17 740.98 L 810.17 580.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv10" d="M 839.51 622.49 L 839.51 600.25 L 810.17 600.25" stroke-width="1" zvalue="1645"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="78" MaxPinNum="2"/>
   </metadata>
  <path d="M 839.51 622.49 L 839.51 600.25 L 810.17 600.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv10" d="M 1777.26 424.05 L 1777.26 441.98" stroke-width="1" zvalue="1653"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="164@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1777.26 424.05 L 1777.26 441.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv10" d="M 1776.99 474.35 L 1776.99 513.75" stroke-width="1" zvalue="1654"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@0" LinkObjectIDznd="166@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1776.99 474.35 L 1776.99 513.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv10" d="M 1700.6 536.84 L 1700.6 513.75" stroke-width="1" zvalue="1660"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="166@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1700.6 536.84 L 1700.6 513.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv10" d="M 1700.6 739.33 L 1700.6 579.57" stroke-width="1" zvalue="1663"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160@0" LinkObjectIDznd="159@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1700.6 739.33 L 1700.6 579.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv10" d="M 1728.96 621.24 L 1728.96 599 L 1700.6 599" stroke-width="1" zvalue="1664"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="156" MaxPinNum="2"/>
   </metadata>
  <path d="M 1728.96 621.24 L 1728.96 599 L 1700.6 599" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv10" d="M 1800.6 536.84 L 1800.6 513.75" stroke-width="1" zvalue="1670"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="166@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1800.6 536.84 L 1800.6 513.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv10" d="M 1800.62 740.73 L 1800.6 579.57" stroke-width="1" zvalue="1673"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@0" LinkObjectIDznd="152@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1800.62 740.73 L 1800.6 579.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv10" d="M 1828.96 621.24 L 1828.96 599 L 1800.6 599" stroke-width="1" zvalue="1674"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="149" MaxPinNum="2"/>
   </metadata>
  <path d="M 1828.96 621.24 L 1828.96 599 L 1800.6 599" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv10" d="M 1257.14 537.09 L 1257.14 513.75" stroke-width="1" zvalue="1680"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="166@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1257.14 537.09 L 1257.14 513.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv10" d="M 1257.17 740.98 L 1257.14 579.82" stroke-width="1" zvalue="1683"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="147@0" LinkObjectIDznd="145@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1257.17 740.98 L 1257.14 579.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="kv10" d="M 1285.51 621.49 L 1285.51 599.25 L 1257.14 599.25" stroke-width="1" zvalue="1684"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="143@0" LinkObjectIDznd="142" MaxPinNum="2"/>
   </metadata>
  <path d="M 1285.51 621.49 L 1285.51 599.25 L 1257.14 599.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv10" d="M 1360.14 537.09 L 1360.14 513.75" stroke-width="1" zvalue="1690"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@0" LinkObjectIDznd="166@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1360.14 537.09 L 1360.14 513.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv10" d="M 1360.17 740.98 L 1360.14 579.82" stroke-width="1" zvalue="1693"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="138@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1360.17 740.98 L 1360.14 579.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv10" d="M 1388.51 621.49 L 1388.51 599.25 L 1360.14 599.25" stroke-width="1" zvalue="1694"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136@0" LinkObjectIDznd="120" MaxPinNum="2"/>
   </metadata>
  <path d="M 1388.51 621.49 L 1388.51 599.25 L 1360.14 599.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv10" d="M 1472.14 537.09 L 1472.14 513.75" stroke-width="1" zvalue="1700"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472.14 537.09 L 1472.14 513.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv10" d="M 1472.17 740.98 L 1472.14 579.82" stroke-width="1" zvalue="1703"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@0" LinkObjectIDznd="116@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472.17 740.98 L 1472.14 579.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv10" d="M 1500.51 621.49 L 1500.51 599.25 L 1472.14 599.25" stroke-width="1" zvalue="1704"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="113" MaxPinNum="2"/>
   </metadata>
  <path d="M 1500.51 621.49 L 1500.51 599.25 L 1472.14 599.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv10" d="M 1584.14 537.09 L 1584.14 513.75" stroke-width="1" zvalue="1710"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1584.14 537.09 L 1584.14 513.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv10" d="M 1584.17 740.98 L 1584.14 579.82" stroke-width="1" zvalue="1713"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="109@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1584.17 740.98 L 1584.14 579.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv10" d="M 1612.51 621.49 L 1612.51 599.25 L 1584.14 599.25" stroke-width="1" zvalue="1714"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="106" MaxPinNum="2"/>
   </metadata>
  <path d="M 1612.51 621.49 L 1612.51 599.25 L 1584.14 599.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv10" d="M 1066 514.75 L 1066 429 L 1122.51 429" stroke-width="1" zvalue="1718"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="16@7" LinkObjectIDznd="169@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1066 514.75 L 1066 429 L 1122.51 429" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="kv10" d="M 1164.07 429 L 1219.89 429 L 1219.89 453.98" stroke-width="1" zvalue="1722"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@0" LinkObjectIDznd="173@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1164.07 429 L 1219.89 429 L 1219.89 453.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv10" d="M 1219.99 486.35 L 1219.99 513.75" stroke-width="1" zvalue="1723"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="173@0" LinkObjectIDznd="166@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1219.99 486.35 L 1219.99 513.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="kv10" d="M 572.92 248.61 L 572.92 438.09" stroke-width="1" zvalue="1728"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@0" LinkObjectIDznd="180@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 572.92 248.61 L 572.92 438.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv10" d="M 572.69 480.82 L 572.69 514.75" stroke-width="1" zvalue="1729"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@1" LinkObjectIDznd="16@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 572.69 480.82 L 572.69 514.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv10" d="M 548.22 342.54 L 548.22 393.78 L 572.92 393.78" stroke-width="1" zvalue="1733"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="181" MaxPinNum="2"/>
   </metadata>
  <path d="M 548.22 342.54 L 548.22 393.78 L 572.92 393.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 684.28 242.24 L 684.28 438.09" stroke-width="1" zvalue="1739"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="195@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 684.28 242.24 L 684.28 438.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv10" d="M 684.69 480.82 L 684.69 514.75" stroke-width="1" zvalue="1740"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@1" LinkObjectIDznd="16@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 684.69 480.82 L 684.69 514.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv10" d="M 660.22 342.54 L 660.22 393.78 L 684.28 393.78" stroke-width="1" zvalue="1744"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="194" MaxPinNum="2"/>
   </metadata>
  <path d="M 660.22 342.54 L 660.22 393.78 L 684.28 393.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="203">
   <path class="kv10" d="M 857.33 483.12 L 857.33 514.75" stroke-width="1" zvalue="1750"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="200@0" LinkObjectIDznd="16@10" MaxPinNum="2"/>
   </metadata>
  <path d="M 857.33 483.12 L 857.33 514.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="206">
   <path class="kv10" d="M 857.11 409.59 L 857.11 450.76" stroke-width="1" zvalue="1752"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@0" LinkObjectIDznd="200@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 857.11 409.59 L 857.11 450.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="230">
   <path class="kv10" d="M 1341.81 242.38 L 1341.81 435.87" stroke-width="1" zvalue="1758"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="232@0" LinkObjectIDznd="231@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1341.81 242.38 L 1341.81 435.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="229">
   <path class="kv10" d="M 1341.58 478.6 L 1341.58 513.75" stroke-width="1" zvalue="1759"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@1" LinkObjectIDznd="166@10" MaxPinNum="2"/>
   </metadata>
  <path d="M 1341.58 478.6 L 1341.58 513.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv10" d="M 1317.11 340.31 L 1317.11 391.56 L 1341.58 391.56" stroke-width="1" zvalue="1763"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@0" LinkObjectIDznd="230" MaxPinNum="2"/>
   </metadata>
  <path d="M 1317.11 340.31 L 1317.11 391.56 L 1341.58 391.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="kv10" d="M 1453.17 240.02 L 1453.17 435.87" stroke-width="1" zvalue="1768"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="224@0" LinkObjectIDznd="223@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1453.17 240.02 L 1453.17 435.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="kv10" d="M 1453.58 478.6 L 1453.58 513.75" stroke-width="1" zvalue="1769"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@1" LinkObjectIDznd="166@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1453.58 478.6 L 1453.58 513.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv10" d="M 1429.11 340.31 L 1429.11 391.56 L 1453.17 391.56" stroke-width="1" zvalue="1773"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@0" LinkObjectIDznd="222" MaxPinNum="2"/>
   </metadata>
  <path d="M 1429.11 340.31 L 1429.11 391.56 L 1453.17 391.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv10" d="M 1626.22 480.9 L 1626.22 513.75" stroke-width="1" zvalue="1778"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@0" LinkObjectIDznd="166@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1626.22 480.9 L 1626.22 513.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv10" d="M 1626 407.37 L 1626 448.53" stroke-width="1" zvalue="1779"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="216@0" LinkObjectIDznd="215@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1626 407.37 L 1626 448.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="264">
   <path class="kv10" d="M 571.56 346.11 L 572.92 346.11" stroke-width="1" zvalue="1832"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="181" MaxPinNum="2"/>
   </metadata>
  <path d="M 571.56 346.11 L 572.92 346.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="265">
   <path class="kv10" d="M 683.56 347.11 L 684.28 347.11" stroke-width="1" zvalue="1833"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@0" LinkObjectIDznd="194" MaxPinNum="2"/>
   </metadata>
  <path d="M 683.56 347.11 L 684.28 347.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="kv10" d="M 1340.44 344.89 L 1341.58 344.89" stroke-width="1" zvalue="1834"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="228@0" LinkObjectIDznd="230" MaxPinNum="2"/>
   </metadata>
  <path d="M 1340.44 344.89 L 1341.58 344.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="267">
   <path class="kv10" d="M 1452.44 344.89 L 1453.17 344.89" stroke-width="1" zvalue="1835"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@0" LinkObjectIDznd="222" MaxPinNum="2"/>
   </metadata>
  <path d="M 1452.44 344.89 L 1453.17 344.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="135">
   <use class="kv10" height="30" transform="rotate(0,931.568,762.639) scale(1.57778,-1.57778) translate(-336.804,-1237.33)" width="15" x="919.7346271378954" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="738.9722222222222" zvalue="1563"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449510440965" ObjectName="10kV县振线"/>
   <cge:TPSR_Ref TObjectID="6192449510440965"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,931.568,762.639) scale(1.57778,-1.57778) translate(-336.804,-1237.33)" width="15" x="919.7346271378954" y="738.9722222222222"/></g>
  <g id="12">
   <use class="kv10" height="30" transform="rotate(0,1031.57,762.639) scale(1.57778,-1.57778) translate(-373.424,-1237.33)" width="15" x="1019.734627137895" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="738.9722222222222" zvalue="1581"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449510637573" ObjectName="10kV038备用线"/>
   <cge:TPSR_Ref TObjectID="6192449510637573"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1031.57,762.639) scale(1.57778,-1.57778) translate(-373.424,-1237.33)" width="15" x="1019.734627137895" y="738.9722222222222"/></g>
  <g id="33">
   <use class="kv10" height="30" transform="rotate(0,488.111,762.889) scale(1.57778,-1.57778) translate(-174.412,-1237.74)" width="15" x="476.2777777777778" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="739.2222222222222" zvalue="1592"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449510834181" ObjectName="10kV033备用线"/>
   <cge:TPSR_Ref TObjectID="6192449510834181"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,488.111,762.889) scale(1.57778,-1.57778) translate(-174.412,-1237.74)" width="15" x="476.2777777777778" y="739.2222222222222"/></g>
  <g id="43">
   <use class="kv10" height="30" transform="rotate(0,591.083,763) scale(1.57778,-1.57778) translate(-212.12,-1237.92)" width="15" x="579.2498814091132" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="739.3333333333333" zvalue="1603"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449511030790" ObjectName="10kV县曩线"/>
   <cge:TPSR_Ref TObjectID="6192449511030790"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,591.083,763) scale(1.57778,-1.57778) translate(-212.12,-1237.92)" width="15" x="579.2498814091132" y="739.3333333333333"/></g>
  <g id="73">
   <use class="kv10" height="30" transform="rotate(0,702.111,761.889) scale(1.57778,-1.57778) translate(-252.778,-1236.11)" width="15" x="690.2777777777778" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="738.2222222222222" zvalue="1625"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449511227398" ObjectName="10kV县南线"/>
   <cge:TPSR_Ref TObjectID="6192449511227398"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,702.111,761.889) scale(1.57778,-1.57778) translate(-252.778,-1236.11)" width="15" x="690.2777777777778" y="738.2222222222222"/></g>
  <g id="83">
   <use class="kv10" height="30" transform="rotate(0,814.111,761.889) scale(1.57778,-1.57778) translate(-293.792,-1236.11)" width="15" x="802.2777777777778" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="738.2222222222222" zvalue="1636"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449511424006" ObjectName="10kV县永线"/>
   <cge:TPSR_Ref TObjectID="6192449511424006"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,814.111,761.889) scale(1.57778,-1.57778) translate(-293.792,-1236.11)" width="15" x="802.2777777777778" y="738.2222222222222"/></g>
  <g id="154">
   <use class="kv10" height="30" transform="rotate(0,1804.57,761.639) scale(1.57778,-1.57778) translate(-656.494,-1235.7)" width="15" x="1792.734627137895" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="737.9722222222222" zvalue="1665"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449512407046" ObjectName="10kV县九线"/>
   <cge:TPSR_Ref TObjectID="6192449512407046"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1804.57,761.639) scale(1.57778,-1.57778) translate(-656.494,-1235.7)" width="15" x="1792.734627137895" y="737.9722222222222"/></g>
  <g id="147">
   <use class="kv10" height="30" transform="rotate(0,1261.11,761.889) scale(1.57778,-1.57778) translate(-457.482,-1236.11)" width="15" x="1249.277777777778" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="738.2222222222222" zvalue="1675"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449512210438" ObjectName="10kV县红线"/>
   <cge:TPSR_Ref TObjectID="6192449512210438"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1261.11,761.889) scale(1.57778,-1.57778) translate(-457.482,-1236.11)" width="15" x="1249.277777777778" y="738.2222222222222"/></g>
  <g id="140">
   <use class="kv10" height="30" transform="rotate(0,1364.11,761.889) scale(1.57778,-1.57778) translate(-495.2,-1236.11)" width="15" x="1352.277777777778" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="738.2222222222222" zvalue="1685"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449512013830" ObjectName="10kV县三线"/>
   <cge:TPSR_Ref TObjectID="6192449512013830"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1364.11,761.889) scale(1.57778,-1.57778) translate(-495.2,-1236.11)" width="15" x="1352.277777777778" y="738.2222222222222"/></g>
  <g id="118">
   <use class="kv10" height="30" transform="rotate(0,1476.11,761.889) scale(1.57778,-1.57778) translate(-536.214,-1236.11)" width="15" x="1464.277777777778" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="738.2222222222222" zvalue="1695"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449511817222" ObjectName="10kV045备用线"/>
   <cge:TPSR_Ref TObjectID="6192449511817222"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1476.11,761.889) scale(1.57778,-1.57778) translate(-536.214,-1236.11)" width="15" x="1464.277777777778" y="738.2222222222222"/></g>
  <g id="111">
   <use class="kv10" height="30" transform="rotate(0,1588.11,761.889) scale(1.57778,-1.57778) translate(-577.228,-1236.11)" width="15" x="1576.277777777778" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="738.2222222222222" zvalue="1705"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449511620614" ObjectName="10kV县宾线"/>
   <cge:TPSR_Ref TObjectID="6192449511620614"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1588.11,761.889) scale(1.57778,-1.57778) translate(-577.228,-1236.11)" width="15" x="1576.277777777778" y="738.2222222222222"/></g>
  <g id="196">
   <use class="kv10" height="30" transform="rotate(0,688.222,221.333) scale(1.57778,1.57778) translate(-247.692,-72.385)" width="15" x="676.3888888888888" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="197.6666666666667" zvalue="1735"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449513193478" ObjectName="10kV032备用线"/>
   <cge:TPSR_Ref TObjectID="6192449513193478"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,688.222,221.333) scale(1.57778,1.57778) translate(-247.692,-72.385)" width="15" x="676.3888888888888" y="197.6666666666667"/></g>
  <g id="198">
   <use class="kv10" height="30" transform="rotate(0,857.111,371.333) scale(2.72222,-2.7037) translate(-525.032,-483.12)" width="20" x="829.8888888888889" xlink:href="#EnergyConsumer:站用变带熔断器YY_0" y="330.7777777777777" zvalue="1745"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449513259014" ObjectName="10kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,857.111,371.333) scale(2.72222,-2.7037) translate(-525.032,-483.12)" width="20" x="829.8888888888889" y="330.7777777777777"/></g>
  <g id="224">
   <use class="kv10" height="30" transform="rotate(0,1457.11,219.111) scale(1.57778,1.57778) translate(-529.257,-71.5712)" width="15" x="1445.277777777778" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="195.4444444444445" zvalue="1764"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449513652230" ObjectName="10kV042备用线"/>
   <cge:TPSR_Ref TObjectID="6192449513652230"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1457.11,219.111) scale(1.57778,1.57778) translate(-529.257,-71.5712)" width="15" x="1445.277777777778" y="195.4444444444445"/></g>
  <g id="216">
   <use class="kv10" height="30" transform="rotate(0,1626,369.111) scale(2.72222,-2.7037) translate(-1011.47,-480.076)" width="20" x="1598.777777777778" xlink:href="#EnergyConsumer:站用变带熔断器YY_0" y="328.5555555555555" zvalue="1774"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449513455622" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1626,369.111) scale(2.72222,-2.7037) translate(-1011.47,-480.076)" width="20" x="1598.777777777778" y="328.5555555555555"/></g>
 </g>
 <g id="BreakerClass">
  <g id="130">
   <use class="kv10" height="20" transform="rotate(0,927.596,559.5) scale(2.34135,2.34135) translate(-524.708,-307.121)" width="10" x="915.8888888888889" xlink:href="#Breaker:小车断路器_0" y="536.0865325993962" zvalue="1570"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924467228677" ObjectName="10kV县振线037断路器"/>
   <cge:TPSR_Ref TObjectID="6473924467228677"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,927.596,559.5) scale(2.34135,2.34135) translate(-524.708,-307.121)" width="10" x="915.8888888888889" y="536.0865325993962"/></g>
  <g id="9">
   <use class="kv10" height="20" transform="rotate(0,1027.6,559.5) scale(2.34135,2.34135) translate(-581.998,-307.121)" width="10" x="1015.888888888889" xlink:href="#Breaker:小车断路器_0" y="536.0865325993962" zvalue="1584"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924467294213" ObjectName="10kV038备用线038断路器"/>
   <cge:TPSR_Ref TObjectID="6473924467294213"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1027.6,559.5) scale(2.34135,2.34135) translate(-581.998,-307.121)" width="10" x="1015.888888888889" y="536.0865325993962"/></g>
  <g id="31">
   <use class="kv10" height="20" transform="rotate(0,484.139,559.75) scale(2.34135,2.34135) translate(-270.654,-307.265)" width="10" x="472.4320395287713" xlink:href="#Breaker:小车断路器_0" y="536.3365325993962" zvalue="1595"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924467359749" ObjectName="10kV033备用线033断路器"/>
   <cge:TPSR_Ref TObjectID="6473924467359749"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,484.139,559.75) scale(2.34135,2.34135) translate(-270.654,-307.265)" width="10" x="472.4320395287713" y="536.3365325993962"/></g>
  <g id="41">
   <use class="kv10" height="20" transform="rotate(0,587.139,559.75) scale(2.34135,2.34135) translate(-329.662,-307.265)" width="10" x="575.4320395287714" xlink:href="#Breaker:小车断路器_0" y="536.3365325993962" zvalue="1606"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924467425285" ObjectName="10kV县曩线034断路器"/>
   <cge:TPSR_Ref TObjectID="6473924467425285"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,587.139,559.75) scale(2.34135,2.34135) translate(-329.662,-307.265)" width="10" x="575.4320395287714" y="536.3365325993962"/></g>
  <g id="71">
   <use class="kv10" height="20" transform="rotate(0,699.139,559.75) scale(2.34135,2.34135) translate(-393.827,-307.265)" width="10" x="687.4320395287714" xlink:href="#Breaker:小车断路器_0" y="536.3365325993962" zvalue="1628"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924467490821" ObjectName="10kV县南线035断路器"/>
   <cge:TPSR_Ref TObjectID="6473924467490821"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,699.139,559.75) scale(2.34135,2.34135) translate(-393.827,-307.265)" width="10" x="687.4320395287714" y="536.3365325993962"/></g>
  <g id="81">
   <use class="kv10" height="20" transform="rotate(0,811.139,559.75) scale(2.34135,2.34135) translate(-457.991,-307.265)" width="10" x="799.4320395287714" xlink:href="#Breaker:小车断路器_0" y="536.3365325993962" zvalue="1639"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924467556357" ObjectName="10kV县永线036断路器"/>
   <cge:TPSR_Ref TObjectID="6473924467556357"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,811.139,559.75) scale(2.34135,2.34135) translate(-457.991,-307.265)" width="10" x="799.4320395287714" y="536.3365325993962"/></g>
  <g id="159">
   <use class="kv10" height="20" transform="rotate(0,1700.6,558.5) scale(2.34135,2.34135) translate(-967.556,-306.549)" width="10" x="1688.888888888889" xlink:href="#Breaker:小车断路器_0" y="535.0865325993962" zvalue="1658"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924467949573" ObjectName="10kV芒县线047断路器"/>
   <cge:TPSR_Ref TObjectID="6473924467949573"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1700.6,558.5) scale(2.34135,2.34135) translate(-967.556,-306.549)" width="10" x="1688.888888888889" y="535.0865325993962"/></g>
  <g id="152">
   <use class="kv10" height="20" transform="rotate(0,1800.6,558.5) scale(2.34135,2.34135) translate(-1024.85,-306.549)" width="10" x="1788.888888888889" xlink:href="#Breaker:小车断路器_0" y="535.0865325993962" zvalue="1668"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924467884037" ObjectName="10kV县九线048断路器"/>
   <cge:TPSR_Ref TObjectID="6473924467884037"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1800.6,558.5) scale(2.34135,2.34135) translate(-1024.85,-306.549)" width="10" x="1788.888888888889" y="535.0865325993962"/></g>
  <g id="145">
   <use class="kv10" height="20" transform="rotate(0,1257.14,558.75) scale(2.34135,2.34135) translate(-713.502,-306.692)" width="10" x="1245.432039528771" xlink:href="#Breaker:小车断路器_0" y="535.3365325993962" zvalue="1678"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924467818501" ObjectName="10kV县红线043断路器"/>
   <cge:TPSR_Ref TObjectID="6473924467818501"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1257.14,558.75) scale(2.34135,2.34135) translate(-713.502,-306.692)" width="10" x="1245.432039528771" y="535.3365325993962"/></g>
  <g id="138">
   <use class="kv10" height="20" transform="rotate(0,1360.14,558.75) scale(2.34135,2.34135) translate(-772.51,-306.692)" width="10" x="1348.432039528771" xlink:href="#Breaker:小车断路器_0" y="535.3365325993962" zvalue="1688"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924467752965" ObjectName="10kV县三线044断路器"/>
   <cge:TPSR_Ref TObjectID="6473924467752965"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1360.14,558.75) scale(2.34135,2.34135) translate(-772.51,-306.692)" width="10" x="1348.432039528771" y="535.3365325993962"/></g>
  <g id="116">
   <use class="kv10" height="20" transform="rotate(0,1472.14,558.75) scale(2.34135,2.34135) translate(-836.675,-306.692)" width="10" x="1460.432039528771" xlink:href="#Breaker:小车断路器_0" y="535.3365325993962" zvalue="1698"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924467687429" ObjectName="10kV045备用线045断路器"/>
   <cge:TPSR_Ref TObjectID="6473924467687429"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1472.14,558.75) scale(2.34135,2.34135) translate(-836.675,-306.692)" width="10" x="1460.432039528771" y="535.3365325993962"/></g>
  <g id="109">
   <use class="kv10" height="20" transform="rotate(0,1584.14,558.75) scale(2.34135,2.34135) translate(-900.839,-306.692)" width="10" x="1572.432039528771" xlink:href="#Breaker:小车断路器_0" y="535.3365325993962" zvalue="1708"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924467621893" ObjectName="10kV县宾线046断路器"/>
   <cge:TPSR_Ref TObjectID="6473924467621893"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1584.14,558.75) scale(2.34135,2.34135) translate(-900.839,-306.692)" width="10" x="1572.432039528771" y="535.3365325993962"/></g>
  <g id="169">
   <use class="kv10" height="20" transform="rotate(90,1143,429) scale(-2.34135,2.34135) translate(-1624.47,-232.359)" width="10" x="1131.293269230769" xlink:href="#Breaker:小车母联_0" y="405.5865384615385" zvalue="1717"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924468015109" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473924468015109"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1143,429) scale(-2.34135,2.34135) translate(-1624.47,-232.359)" width="10" x="1131.293269230769" y="405.5865384615385"/></g>
  <g id="180">
   <use class="kv10" height="20" transform="rotate(0,572.694,459.75) scale(2.34135,2.34135) translate(-321.387,-249.975)" width="10" x="560.9875950843269" xlink:href="#Breaker:小车断路器_0" y="436.3365328862117" zvalue="1727"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924468080645" ObjectName="10kV梁县Ⅰ回线031断路器"/>
   <cge:TPSR_Ref TObjectID="6473924468080645"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,572.694,459.75) scale(2.34135,2.34135) translate(-321.387,-249.975)" width="10" x="560.9875950843269" y="436.3365328862117"/></g>
  <g id="195">
   <use class="kv10" height="20" transform="rotate(0,684.694,459.75) scale(2.34135,2.34135) translate(-385.551,-249.975)" width="10" x="672.9875950843269" xlink:href="#Breaker:小车断路器_0" y="436.3365328862117" zvalue="1737"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924468146181" ObjectName="10kV032备用线032断路器"/>
   <cge:TPSR_Ref TObjectID="6473924468146181"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,684.694,459.75) scale(2.34135,2.34135) translate(-385.551,-249.975)" width="10" x="672.9875950843269" y="436.3365328862117"/></g>
  <g id="231">
   <use class="kv10" height="20" transform="rotate(0,1341.58,457.528) scale(2.34135,2.34135) translate(-761.88,-248.702)" width="10" x="1329.876483973216" xlink:href="#Breaker:小车断路器_0" y="434.1143106639895" zvalue="1756"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924468277253" ObjectName="10kV梁县Ⅱ回线041断路器"/>
   <cge:TPSR_Ref TObjectID="6473924468277253"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1341.58,457.528) scale(2.34135,2.34135) translate(-761.88,-248.702)" width="10" x="1329.876483973216" y="434.1143106639895"/></g>
  <g id="223">
   <use class="kv10" height="20" transform="rotate(0,1453.58,457.528) scale(2.34135,2.34135) translate(-826.044,-248.702)" width="10" x="1441.876483973216" xlink:href="#Breaker:小车断路器_0" y="434.1143106639895" zvalue="1766"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924468211717" ObjectName="10kV042备用线042断路器"/>
   <cge:TPSR_Ref TObjectID="6473924468211717"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1453.58,457.528) scale(2.34135,2.34135) translate(-826.044,-248.702)" width="10" x="1441.876483973216" y="434.1143106639895"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="126">
   <use class="kv10" height="20" transform="rotate(0,955.889,638.083) scale(1.5,1.625) translate(-316.13,-239.167)" width="10" x="948.3888888888889" xlink:href="#GroundDisconnector:地刀_0" y="621.8333333333335" zvalue="1575"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449510375429" ObjectName="10kV县振线03767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449510375429"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,955.889,638.083) scale(1.5,1.625) translate(-316.13,-239.167)" width="10" x="948.3888888888889" y="621.8333333333335"/></g>
  <g id="8">
   <use class="kv10" height="20" transform="rotate(0,1055.89,638.083) scale(1.5,1.625) translate(-349.463,-239.167)" width="10" x="1048.388888888889" xlink:href="#GroundDisconnector:地刀_0" y="621.8333333333335" zvalue="1587"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449510572037" ObjectName="10kV038备用线03867接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449510572037"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1055.89,638.083) scale(1.5,1.625) translate(-349.463,-239.167)" width="10" x="1048.388888888889" y="621.8333333333335"/></g>
  <g id="28">
   <use class="kv10" height="20" transform="rotate(0,512.432,638.333) scale(1.5,1.625) translate(-168.311,-239.263)" width="10" x="504.9320395287714" xlink:href="#GroundDisconnector:地刀_0" y="622.0833333333335" zvalue="1598"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449510768646" ObjectName="10kV033备用线03367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449510768646"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,512.432,638.333) scale(1.5,1.625) translate(-168.311,-239.263)" width="10" x="504.9320395287714" y="622.0833333333335"/></g>
  <g id="39">
   <use class="kv10" height="20" transform="rotate(0,615.432,638.333) scale(1.5,1.625) translate(-202.644,-239.263)" width="10" x="607.9320395287714" xlink:href="#GroundDisconnector:地刀_0" y="622.0833333333335" zvalue="1609"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449510965254" ObjectName="10kV县曩线03467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449510965254"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,615.432,638.333) scale(1.5,1.625) translate(-202.644,-239.263)" width="10" x="607.9320395287714" y="622.0833333333335"/></g>
  <g id="69">
   <use class="kv10" height="20" transform="rotate(0,727.432,638.333) scale(1.5,1.625) translate(-239.977,-239.263)" width="10" x="719.9320395287714" xlink:href="#GroundDisconnector:地刀_0" y="622.0833333333335" zvalue="1631"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449511161862" ObjectName="10kV县南线03567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449511161862"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,727.432,638.333) scale(1.5,1.625) translate(-239.977,-239.263)" width="10" x="719.9320395287714" y="622.0833333333335"/></g>
  <g id="79">
   <use class="kv10" height="20" transform="rotate(0,839.432,638.333) scale(1.5,1.625) translate(-277.311,-239.263)" width="10" x="831.9320395287714" xlink:href="#GroundDisconnector:地刀_0" y="622.0833333333335" zvalue="1642"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449511358470" ObjectName="10kV县永线03667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449511358470"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,839.432,638.333) scale(1.5,1.625) translate(-277.311,-239.263)" width="10" x="831.9320395287714" y="622.0833333333335"/></g>
  <g id="157">
   <use class="kv10" height="20" transform="rotate(0,1728.89,637.083) scale(1.5,1.625) translate(-573.796,-238.782)" width="10" x="1721.388888888889" xlink:href="#GroundDisconnector:地刀_0" y="620.8333333333335" zvalue="1661"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449512538118" ObjectName="10kV芒县线04767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449512538118"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1728.89,637.083) scale(1.5,1.625) translate(-573.796,-238.782)" width="10" x="1721.388888888889" y="620.8333333333335"/></g>
  <g id="150">
   <use class="kv10" height="20" transform="rotate(0,1828.89,637.083) scale(1.5,1.625) translate(-607.13,-238.782)" width="10" x="1821.388888888889" xlink:href="#GroundDisconnector:地刀_0" y="620.8333333333335" zvalue="1671"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449512341510" ObjectName="10kV县九线04867接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449512341510"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1828.89,637.083) scale(1.5,1.625) translate(-607.13,-238.782)" width="10" x="1821.388888888889" y="620.8333333333335"/></g>
  <g id="143">
   <use class="kv10" height="20" transform="rotate(0,1285.43,637.333) scale(1.5,1.625) translate(-425.977,-238.878)" width="10" x="1277.932039528771" xlink:href="#GroundDisconnector:地刀_0" y="621.0833333333335" zvalue="1681"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449512144902" ObjectName="10kV县红线04367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449512144902"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1285.43,637.333) scale(1.5,1.625) translate(-425.977,-238.878)" width="10" x="1277.932039528771" y="621.0833333333335"/></g>
  <g id="136">
   <use class="kv10" height="20" transform="rotate(0,1388.43,637.333) scale(1.5,1.625) translate(-460.311,-238.878)" width="10" x="1380.932039528771" xlink:href="#GroundDisconnector:地刀_0" y="621.0833333333335" zvalue="1691"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449511948294" ObjectName="10kV县三线04467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449511948294"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1388.43,637.333) scale(1.5,1.625) translate(-460.311,-238.878)" width="10" x="1380.932039528771" y="621.0833333333335"/></g>
  <g id="114">
   <use class="kv10" height="20" transform="rotate(0,1500.43,637.333) scale(1.5,1.625) translate(-497.644,-238.878)" width="10" x="1492.932039528771" xlink:href="#GroundDisconnector:地刀_0" y="621.0833333333335" zvalue="1701"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449511751686" ObjectName="10kV045备用线04567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449511751686"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1500.43,637.333) scale(1.5,1.625) translate(-497.644,-238.878)" width="10" x="1492.932039528771" y="621.0833333333335"/></g>
  <g id="107">
   <use class="kv10" height="20" transform="rotate(0,1612.43,637.333) scale(1.5,1.625) translate(-534.977,-238.878)" width="10" x="1604.932039528771" xlink:href="#GroundDisconnector:地刀_0" y="621.0833333333335" zvalue="1711"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449511555078" ObjectName="10kV县宾线04667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449511555078"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1612.43,637.333) scale(1.5,1.625) translate(-534.977,-238.878)" width="10" x="1604.932039528771" y="621.0833333333335"/></g>
 </g>
 <g id="StateClass">
  <g id="261">
   <use height="30" transform="rotate(0,299.196,320.357) scale(0.708333,0.665547) translate(118.824,155.97)" width="30" x="288.57" xlink:href="#State:红绿圆(方形)_0" y="310.37" zvalue="1825"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374886567939" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,299.196,320.357) scale(0.708333,0.665547) translate(118.824,155.97)" width="30" x="288.57" y="310.37"/></g>
  <g id="10">
   <use height="30" transform="rotate(0,206,320.5) scale(0.708333,0.665547) translate(80.4485,156.042)" width="30" x="195.38" xlink:href="#State:红绿圆(方形)_0" y="310.52" zvalue="1837"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562951213219845" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,206,320.5) scale(0.708333,0.665547) translate(80.4485,156.042)" width="30" x="195.38" y="310.52"/></g>
 </g>
</svg>