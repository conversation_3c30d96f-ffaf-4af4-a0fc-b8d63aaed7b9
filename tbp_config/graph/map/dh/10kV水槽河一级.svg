<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549592326146" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Accessory:电缆1_0" viewBox="0,0,12,7">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.25"/>
   <path d="M 1.16667 0.25 L 10.8333 0.25 L 6 6.88889 L 1.16667 0.25 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV水槽河一级" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="50.21" xlink:href="logo.png" y="47.57"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.214,79.5714) scale(1,1) translate(0,0)" writing-mode="lr" x="199.21" xml:space="preserve" y="83.06999999999999" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,201.714,77.2618) scale(1,1) translate(0,0)" writing-mode="lr" x="201.71" xml:space="preserve" y="86.26000000000001" zvalue="5">10kV水槽河一级</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="43" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,79.625,205) scale(1,1) translate(0,0)" width="72.88" x="43.19" y="193" zvalue="88"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.625,205) scale(1,1) translate(0,0)" writing-mode="lr" x="79.63" xml:space="preserve" y="209.5" zvalue="88">信号一览</text>
  <line fill="none" id="37" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384.2142857142856" x2="384.2142857142856" y1="15.57142857142867" y2="1045.571428571429" zvalue="6"/>
  <line fill="none" id="35" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="151.441921185511" y2="151.441921185511" zvalue="8"/>
  <line fill="none" id="34" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="621.441921185511" y2="621.441921185511" zvalue="9"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="936.5714285714287" y2="936.5714285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="975.7347285714286" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="370.2142857142856" y1="936.5714285714287" y2="936.5714285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="370.2142857142856" y1="975.7347285714286" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142856" x2="190.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="190.2142857142857" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142857" x2="280.2142857142857" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="280.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142856" x2="190.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="190.2142857142857" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142857" x2="280.2142857142857" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="280.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.2143,956.571) scale(1,1) translate(0,0)" writing-mode="lr" x="55.21" xml:space="preserve" y="962.5700000000001" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.2143,990.571) scale(1,1) translate(0,0)" writing-mode="lr" x="52.21" xml:space="preserve" y="996.5700000000001" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.214,990.571) scale(1,1) translate(0,0)" writing-mode="lr" x="234.21" xml:space="preserve" y="996.5700000000001" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.2143,1018.57) scale(1,1) translate(0,0)" writing-mode="lr" x="51.21" xml:space="preserve" y="1024.57" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.214,1018.57) scale(1,1) translate(0,0)" writing-mode="lr" x="233.21" xml:space="preserve" y="1024.57" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,75.7143,651.071) scale(1,1) translate(0,2.10522e-13)" writing-mode="lr" x="75.71428571428555" xml:space="preserve" y="655.5714285714286" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.268,958.571) scale(1,1) translate(0,0)" writing-mode="lr" x="235.27" xml:space="preserve" y="964.5700000000001" zvalue="27">ShuiCaoHeYiJi-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,803.77,487.258) scale(1,1) translate(0,0)" writing-mode="lr" x="803.77" xml:space="preserve" y="491.76" zvalue="31">001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,687.686,595.352) scale(1,1) translate(0,0)" writing-mode="lr" x="687.6900000000001" xml:space="preserve" y="599.85" zvalue="33">#1主变2000kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" x="772.0703125" xml:space="preserve" y="877.4774740134186" zvalue="36">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="772.0703125" xml:space="preserve" y="893.4774740134186" zvalue="36">1800KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,794.402,372.869) scale(1,1) translate(0,0)" writing-mode="lr" x="794.4" xml:space="preserve" y="377.37" zvalue="39">0011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,768.971,134) scale(1,1) translate(0,0)" writing-mode="lr" x="768.97" xml:space="preserve" y="138.5" zvalue="42">10kV平原环西Ⅰ回线水槽河一级电站支线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,795.777,317.119) scale(1,1) translate(0,0)" writing-mode="lr" x="795.78" xml:space="preserve" y="321.62" zvalue="55">0511</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,788.373,248.258) scale(1,1) translate(0,0)" writing-mode="lr" x="788.37" xml:space="preserve" y="252.76" zvalue="61">051</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,878.625,640.75) scale(1,1) translate(0,0)" writing-mode="lr" x="878.63" xml:space="preserve" y="645.25" zvalue="64">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,630.934,521.869) scale(1,1) translate(0,0)" writing-mode="lr" x="630.9299999999999" xml:space="preserve" y="526.37" zvalue="67">0531</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,574.28,594.758) scale(1,1) translate(0,0)" writing-mode="lr" x="574.28" xml:space="preserve" y="599.26" zvalue="69">053</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,908.26,503.25) scale(1,1) translate(-1.98344e-13,0)" writing-mode="lr" x="908.26" xml:space="preserve" y="507.75" zvalue="70">0521</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,607.5,691.25) scale(1,1) translate(0,0)" writing-mode="lr" x="607.5" xml:space="preserve" y="695.75" zvalue="74">#1电容器组</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="266.75" y2="266.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="292.75" y2="292.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="266.75" y2="292.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="266.75" y2="292.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="266.75" y2="266.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="292.75" y2="292.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="266.75" y2="292.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="266.75" y2="292.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="292.75" y2="292.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="317" y2="317"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="292.75" y2="317"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="292.75" y2="317"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="292.75" y2="292.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="317" y2="317"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="292.75" y2="317"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="292.75" y2="317"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="317" y2="317"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="339.75" y2="339.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="317" y2="339.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="317" y2="339.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="317" y2="317"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="339.75" y2="339.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="317" y2="339.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="317" y2="339.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="339.75" y2="339.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="362.5" y2="362.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="339.75" y2="362.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="339.75" y2="362.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="339.75" y2="339.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="362.5" y2="362.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="339.75" y2="362.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="339.75" y2="362.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="362.5" y2="362.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="385.25" y2="385.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="362.5" y2="385.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="362.5" y2="385.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="362.5" y2="362.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="385.25" y2="385.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="362.5" y2="385.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="362.5" y2="385.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="385.25" y2="385.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="408" y2="408"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="385.25" y2="408"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="385.25" y2="408"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="385.25" y2="385.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="408" y2="408"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="385.25" y2="408"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="385.25" y2="408"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="408" y2="408"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="430.75" y2="430.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="408" y2="430.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="408" y2="430.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="408" y2="408"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="430.75" y2="430.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="408" y2="430.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="408" y2="430.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,195.399,205.591) scale(1,1) translate(0,0)" writing-mode="lr" x="195.4" xml:space="preserve" y="210.09" zvalue="79">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,300.399,205.591) scale(1,1) translate(0,0)" writing-mode="lr" x="300.4" xml:space="preserve" y="210.09" zvalue="80">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,52.5,279.75) scale(1,1) translate(0,0)" writing-mode="lr" x="10" xml:space="preserve" y="284.25" zvalue="81">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,233,279.75) scale(1,1) translate(0,0)" writing-mode="lr" x="190.5" xml:space="preserve" y="284.25" zvalue="82">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,353) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="357.5" zvalue="83">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,51.5,305.75) scale(1,1) translate(0,0)" writing-mode="lr" x="9" xml:space="preserve" y="310.25" zvalue="89">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,232,305.75) scale(1,1) translate(0,0)" writing-mode="lr" x="189.5" xml:space="preserve" y="310.25" zvalue="90">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.6875,399) scale(1,1) translate(0,0)" writing-mode="lr" x="52.69" xml:space="preserve" y="403.5" zvalue="93">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,220.688,398) scale(1,1) translate(0,0)" writing-mode="lr" x="220.69" xml:space="preserve" y="402.5" zvalue="94">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.6875,422) scale(1,1) translate(0,0)" writing-mode="lr" x="52.69" xml:space="preserve" y="426.5" zvalue="95">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,220.688,421) scale(1,1) translate(0,0)" writing-mode="lr" x="220.69" xml:space="preserve" y="425.5" zvalue="96">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,51.5,328.75) scale(1,1) translate(0,0)" writing-mode="lr" x="9" xml:space="preserve" y="333.25" zvalue="97">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,231.5,327.75) scale(1,1) translate(0,0)" writing-mode="lr" x="189" xml:space="preserve" y="332.25" zvalue="99">厂用电率</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="43.19" y="193" zvalue="88"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BreakerClass">
  <g id="418">
   <use class="kv10" height="20" transform="rotate(0,769.012,488.258) scale(1.22222,1.11111) translate(-138.709,-47.7147)" width="10" x="762.9008038365654" xlink:href="#Breaker:开关_0" y="477.1468253968253" zvalue="30"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924555177988" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924555177988"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,769.012,488.258) scale(1.22222,1.11111) translate(-138.709,-47.7147)" width="10" x="762.9008038365654" y="477.1468253968253"/></g>
  <g id="50">
   <use class="kv10" height="20" transform="rotate(0,769.012,249.258) scale(1.22222,1.11111) translate(-138.709,-23.8147)" width="10" x="762.9008038365654" xlink:href="#Breaker:开关_0" y="238.1468253968253" zvalue="60"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924555243524" ObjectName="10kV并网线水槽河一级电站支线051断路器"/>
   <cge:TPSR_Ref TObjectID="6473924555243524"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,769.012,249.258) scale(1.22222,1.11111) translate(-138.709,-23.8147)" width="10" x="762.9008038365654" y="238.1468253968253"/></g>
  <g id="57">
   <use class="kv10" height="20" transform="rotate(0,604.919,592.008) scale(1.22222,1.11111) translate(-108.874,-58.0897)" width="10" x="598.8074074074075" xlink:href="#Breaker:开关_0" y="580.8968253968253" zvalue="68"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924555309060" ObjectName="#1电容器组053断路器"/>
   <cge:TPSR_Ref TObjectID="6473924555309060"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,604.919,592.008) scale(1.22222,1.11111) translate(-108.874,-58.0897)" width="10" x="598.8074074074075" y="580.8968253968253"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="414">
   <g id="4140">
    <use class="kv10" height="60" transform="rotate(0,767.179,592.077) scale(1.6125,1.54462) translate(-279.159,-192.424)" width="40" x="734.9299999999999" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="545.74" zvalue="32"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874453745667" ObjectName="10"/>
    </metadata>
   </g>
   <g id="4141">
    <use class="v400" height="60" transform="rotate(0,767.179,592.077) scale(1.6125,1.54462) translate(-279.159,-192.424)" width="40" x="734.9299999999999" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="545.74" zvalue="32"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874453811203" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399458816003" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399458816003"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,767.179,592.077) scale(1.6125,1.54462) translate(-279.159,-192.424)" width="40" x="734.9299999999999" y="545.74"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="13">
   <path class="kv10" d="M 769.09 498.87 L 769.09 546.58" stroke-width="1" zvalue="34"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="418@1" LinkObjectIDznd="414@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 769.09 498.87 L 769.09 546.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="v400" d="M 767.18 637.76 L 767.27 795.25" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@1" LinkObjectIDznd="212@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 767.18 637.76 L 767.27 795.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv10" d="M 769 361.86 L 769 329.94" stroke-width="1" zvalue="57"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@1" LinkObjectIDznd="45@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 769 361.86 L 769 329.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv10" d="M 769.75 306.11 L 769.75 259.87" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@1" LinkObjectIDznd="50@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 769.75 306.11 L 769.75 259.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv10" d="M 768.97 238.63 L 768.97 195.27" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="156@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 768.97 238.63 L 768.97 195.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv10" d="M 768.97 385.69 L 768.97 477.63" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@0" LinkObjectIDznd="418@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 768.97 385.69 L 768.97 477.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv10" d="M 878.91 568.39 L 878.91 517.06" stroke-width="1" zvalue="71"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="58@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 878.91 568.39 L 878.91 517.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv10" d="M 879.11 485.19 L 879.11 433 L 768.97 433" stroke-width="1" zvalue="72"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="60" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.11 485.19 L 879.11 433 L 768.97 433" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv10" d="M 605 644.41 L 605 602.62" stroke-width="1" zvalue="74"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="57@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 605 644.41 L 605 602.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv10" d="M 604.88 581.38 L 604.88 534.69" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="56@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 604.88 581.38 L 604.88 534.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv10" d="M 604.91 510.86 L 604.91 434.25 L 768.97 434.25" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@1" LinkObjectIDznd="60" MaxPinNum="2"/>
   </metadata>
  <path d="M 604.91 510.86 L 604.91 434.25 L 768.97 434.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="212">
   <use class="v400" height="30" transform="rotate(0,767.271,822.672) scale(1.85899,1.85899) translate(-341.651,-367.25)" width="30" x="739.3863709131574" xlink:href="#Generator:发电机_0" y="794.7867361713461" zvalue="35"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450010939397" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192450010939397"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,767.271,822.672) scale(1.85899,1.85899) translate(-341.651,-367.25)" width="30" x="739.3863709131574" y="794.7867361713461"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="206">
   <use class="kv10" height="30" transform="rotate(0,769.069,373.869) scale(-1.11111,-0.814815) translate(-1460.4,-835.486)" width="15" x="760.7353709301144" xlink:href="#Disconnector:刀闸_0" y="361.6468390661572" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450010873861" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450010873861"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,769.069,373.869) scale(-1.11111,-0.814815) translate(-1460.4,-835.486)" width="15" x="760.7353709301144" y="361.6468390661572"/></g>
  <g id="45">
   <use class="kv10" height="30" transform="rotate(0,769.819,318.119) scale(-1.11111,-0.814815) translate(-1461.82,-711.316)" width="15" x="761.4853709301144" xlink:href="#Disconnector:刀闸_0" y="305.8968390661572" zvalue="54"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450011004933" ObjectName="10kV并网线水槽河一级电站支线0511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450011004933"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,769.819,318.119) scale(-1.11111,-0.814815) translate(-1461.82,-711.316)" width="15" x="761.4853709301144" y="305.8968390661572"/></g>
  <g id="56">
   <use class="kv10" height="30" transform="rotate(0,604.975,522.869) scale(-1.11111,-0.814815) translate(-1148.62,-1167.35)" width="15" x="596.6419745009564" xlink:href="#Disconnector:刀闸_0" y="510.6468390661572" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450011136005" ObjectName="#1电容器组0531隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450011136005"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,604.975,522.869) scale(-1.11111,-0.814815) translate(-1148.62,-1167.35)" width="15" x="596.6419745009564" y="510.6468390661572"/></g>
  <g id="58">
   <use class="kv10" height="30" transform="rotate(0,879.01,501.75) scale(1.25,1.25) translate(-173.927,-96.6)" width="15" x="869.6350219659528" xlink:href="#Disconnector:令克_0" y="483" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450011201541" ObjectName="#1站用变0521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450011201541"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,879.01,501.75) scale(1.25,1.25) translate(-173.927,-96.6)" width="15" x="869.6350219659528" y="483"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="156">
   <use class="kv10" height="30" transform="rotate(0,768.971,172.688) scale(2.32143,1.52083) translate(-433.097,-51.3271)" width="7" x="760.8461742069358" xlink:href="#ACLineSegment:线路_0" y="149.875" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV平原环西Ⅰ回线水槽河一级电站支线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,768.971,172.688) scale(2.32143,1.52083) translate(-433.097,-51.3271)" width="7" x="760.8461742069358" y="149.875"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="53">
   <use class="kv10" height="30" transform="rotate(0,878.75,593) scale(1.69643,1.70833) translate(-351,-235.253)" width="28" x="855" xlink:href="#EnergyConsumer:站用变DY接地_0" y="567.375" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450011070469" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,878.75,593) scale(1.69643,1.70833) translate(-351,-235.253)" width="28" x="855" y="567.375"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="65">
   <use class="kv10" height="7" transform="rotate(0,605,654.562) scale(3.125,3.125) translate(-398.65,-437.665)" width="12" x="586.25" xlink:href="#Accessory:电缆1_0" y="643.625" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450011267077" ObjectName="#1电容器组"/>
   </metadata>
  <rect fill="white" height="7" opacity="0" stroke="white" transform="rotate(0,605,654.562) scale(3.125,3.125) translate(-398.65,-437.665)" width="12" x="586.25" y="643.625"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="274">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="274" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,152.611,351.917) scale(1,1) translate(0,0)" writing-mode="lr" x="152.77" xml:space="preserve" y="356.83" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="273">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="273" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,152.611,279.917) scale(1,1) translate(0,0)" writing-mode="lr" x="152.77" xml:space="preserve" y="284.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126607388676" ObjectName="F"/>
   </metadata>
  </g>
  <g id="272">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="272" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,330.222,280.917) scale(1,1) translate(0,0)" writing-mode="lr" x="330.38" xml:space="preserve" y="285.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126607454212" ObjectName="F"/>
   </metadata>
  </g>
  <g id="260">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="260" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,152.611,304.917) scale(1,1) translate(0,0)" writing-mode="lr" x="152.77" xml:space="preserve" y="309.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126607257604" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,330.222,305.917) scale(1,1) translate(0,0)" writing-mode="lr" x="330.38" xml:space="preserve" y="310.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126607323140" ObjectName="F"/>
   </metadata>
  </g>
  <g id="209">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="209" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,152.611,328.917) scale(1,1) translate(0,0)" writing-mode="lr" x="152.77" xml:space="preserve" y="333.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126607257604" ObjectName="F"/>
   </metadata>
  </g>
  <g id="198">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,332.611,327.917) scale(1,1) translate(0,0)" writing-mode="lr" x="332.77" xml:space="preserve" y="332.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126607257604" ObjectName="F"/>
   </metadata>
  </g>
  <g id="287">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="287" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,152.611,395.917) scale(1,1) translate(0,0)" writing-mode="lr" x="152.77" xml:space="preserve" y="400.83" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="288">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="288" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,328.611,395.917) scale(1,1) translate(0,0)" writing-mode="lr" x="328.77" xml:space="preserve" y="400.83" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="雨量采集"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="271">
   <use height="30" transform="rotate(0,327.673,206.107) scale(0.708333,0.665547) translate(130.549,98.557)" width="30" x="317.05" xlink:href="#State:红绿圆(方形)_0" y="196.12" zvalue="87"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,327.673,206.107) scale(0.708333,0.665547) translate(130.549,98.557)" width="30" x="317.05" y="196.12"/></g>
  <g id="286">
   <use height="30" transform="rotate(0,232.048,206.107) scale(0.708333,0.665547) translate(91.174,98.557)" width="30" x="221.42" xlink:href="#State:红绿圆(方形)_0" y="196.12" zvalue="101"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,232.048,206.107) scale(0.708333,0.665547) translate(91.174,98.557)" width="30" x="221.42" y="196.12"/></g>
 </g>
</svg>