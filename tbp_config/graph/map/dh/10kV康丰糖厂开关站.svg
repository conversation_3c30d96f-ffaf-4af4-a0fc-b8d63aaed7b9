<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549588000770" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.45181617405996" x2="1.714850492606708" y1="5.276370517142858" y2="14.05696281619048"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.583333333333333" x2="8.583333333333334" y1="5.166666666666667" y2="14.16666666666667"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Accessory:PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <rect fill-opacity="0" height="8.92" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,6.54) scale(1,1) translate(0,0)" width="4" x="13" y="2.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.999999999999998" y2="12.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.083333333333332" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:三相刀闸_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="30"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="23.66666666666666" y2="29.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.25" x2="15.08333333333333" y1="5.999999999999998" y2="23.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.46666666666667" x2="16.43333333333333" y1="5.755662181544974" y2="5.755662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.750000000000002" y2="0.3554618309314215"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="4.583333333333332" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:腊撒线路PT_0" viewBox="0,0,12,32">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.6666666666666661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="3" y1="27" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="27" y2="30"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="13.66666666666667" y2="1"/>
   <path d="M 6 15 L 3 20 L 9 20 L 6 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="9" y1="27" y2="25"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,8) scale(1,1) translate(0,0)" width="4" x="4" y="5"/>
   <ellipse cx="5.9" cy="18.6" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.9" cy="26.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="1"/>
   <ellipse cx="14.99" cy="7.93" fill-opacity="0" rx="7.43" ry="6.85" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.93" cy="17.56" fill-opacity="0" rx="7.43" ry="6.85" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.9834232682896" x2="14.9834232682896" y1="14.95787681993586" y2="18.2114918126664"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.98342326828961" x2="11.56276000329148" y1="18.23128772350278" y2="20.61950731913853"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00726151837816" x2="18.51522958638884" y1="18.19641261556525" y2="20.42154821077479"/>
   <path d="M 11.6895 9.29402 L 18.7806 9.29402 L 15.2653 3.08877 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV康丰糖厂开关站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,197.046,95.3772) scale(1,1) translate(1.13583e-14,-5.83331e-14)" writing-mode="lr" x="197.05" xml:space="preserve" y="99.88" zvalue="3"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="1" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,236.875,39.3894) scale(1,1) translate(0,1.92964e-15)" writing-mode="lr" x="236.88" xml:space="preserve" y="46.89" zvalue="4">    10kV康丰糖厂开关站</text>
  <line fill="none" id="27" stroke="rgb(0,39,45)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="51.22222222222263" x2="348.8097708603561" y1="78.45055797356122" y2="78.45055797356122" zvalue="5"/>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="1" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,126.625,327) scale(1,1) translate(0,0)" width="72.88" x="90.19" y="315" zvalue="31"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,126.625,327) scale(1,1) translate(0,0)" writing-mode="lr" x="126.63" xml:space="preserve" y="331.5" zvalue="31">信号一览</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,256.596,61.9403) scale(1,1) translate(2.75763e-14,0)" writing-mode="lr" x="256.6" xml:space="preserve" y="70.94" zvalue="127">10kV康丰糖厂开关站</text>
  <image height="60" id="4" preserveAspectRatio="xMidYMid slice" width="278.36" x="76.75" xlink:href="logo.png" y="34"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,215.934,64) scale(1,1) translate(-1.70426e-14,0)" writing-mode="lr" x="215.93" xml:space="preserve" y="67.5" zvalue="128"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="5" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,210.537,61.1903) scale(1,1) translate(-3.37508e-14,0)" writing-mode="lr" x="210.54" xml:space="preserve" y="70.19" zvalue="129">10kV康丰糖厂开关站</text>
  <line fill="none" id="26" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="405" x2="405" y1="51.25" y2="1041.25" zvalue="6"/>
  <line fill="none" id="24" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="43.47692454998003" x2="348.0881846035992" y1="170.8779458827686" y2="170.8779458827686" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="71.57142857142844" x2="143.0114285714285" y1="929.8127909390723" y2="929.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="71.57142857142844" x2="143.0114285714285" y1="981.1522909390724" y2="981.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="71.57142857142844" x2="71.57142857142844" y1="929.8127909390723" y2="981.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="143.0114285714285" x2="143.0114285714285" y1="929.8127909390723" y2="981.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="143.0119285714285" x2="377.5719285714284" y1="929.8127909390723" y2="929.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="143.0119285714285" x2="377.5719285714284" y1="981.1522909390724" y2="981.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="143.0119285714285" x2="143.0119285714285" y1="929.8127909390723" y2="981.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="377.5719285714284" x2="377.5719285714284" y1="929.8127909390723" y2="981.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="71.57142857142844" x2="143.0114285714285" y1="981.1522709390724" y2="981.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="71.57142857142844" x2="143.0114285714285" y1="1008.629770939072" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="71.57142857142844" x2="71.57142857142844" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="143.0114285714285" x2="143.0114285714285" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="143.0119285714285" x2="214.0472285714285" y1="981.1522709390724" y2="981.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="143.0119285714285" x2="214.0472285714285" y1="1008.629770939072" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="143.0119285714285" x2="143.0119285714285" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="214.0472285714285" x2="214.0472285714285" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="214.0472285714285" x2="295.8093285714284" y1="981.1522709390724" y2="981.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="214.0472285714285" x2="295.8093285714284" y1="1008.629770939072" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="214.0472285714285" x2="214.0472285714285" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="295.8093285714284" x2="295.8093285714284" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="295.8092285714284" x2="377.5713285714285" y1="981.1522709390724" y2="981.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="295.8092285714284" x2="377.5713285714285" y1="1008.629770939072" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="295.8092285714284" x2="295.8092285714284" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="377.5713285714285" x2="377.5713285714285" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="71.57142857142844" x2="143.0114285714285" y1="1008.629690939072" y2="1008.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="71.57142857142844" x2="143.0114285714285" y1="1036.107190939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="71.57142857142844" x2="71.57142857142844" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="143.0114285714285" x2="143.0114285714285" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="143.0119285714285" x2="214.0472285714285" y1="1008.629690939072" y2="1008.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="143.0119285714285" x2="214.0472285714285" y1="1036.107190939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="143.0119285714285" x2="143.0119285714285" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="214.0472285714285" x2="214.0472285714285" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="214.0472285714285" x2="295.8093285714284" y1="1008.629690939072" y2="1008.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="214.0472285714285" x2="295.8093285714284" y1="1036.107190939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="214.0472285714285" x2="214.0472285714285" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="295.8093285714284" x2="295.8093285714284" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="295.8092285714284" x2="377.5713285714285" y1="1008.629690939072" y2="1008.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="295.8092285714284" x2="377.5713285714285" y1="1036.107190939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="295.8092285714284" x2="295.8092285714284" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="377.5713285714285" x2="377.5713285714285" y1="1008.629690939072" y2="1036.107190939072"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,218.624,959.386) scale(1,1) translate(-1.70811e-14,1.05311e-13)" writing-mode="lr" x="76.93000000000001" xml:space="preserve" y="965.39" zvalue="10">参考图号     KangFeng-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,149.149,996.755) scale(1,1) translate(-7.69641e-14,-1.53244e-12)" writing-mode="lr" x="86.65000000000001" xml:space="preserve" y="1002.75" zvalue="11">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,292.452,997.755) scale(1,1) translate(1.49047e-13,-1.53399e-12)" writing-mode="lr" x="223.75" xml:space="preserve" y="1003.75" zvalue="12">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,100.242,1026.31) scale(1,1) translate(-7.91269e-14,-1.57838e-12)" writing-mode="lr" x="100.24" xml:space="preserve" y="1032.31" zvalue="13">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,255.961,1024.31) scale(1,1) translate(0,1.1252e-13)" writing-mode="lr" x="223.92" xml:space="preserve" y="1030.31" zvalue="14">更新日期    </text>
  <line fill="none" id="17" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="70.85611355802337" x2="375.4673736116425" y1="625.6445306693694" y2="625.6445306693694" zvalue="15"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,113.921,642.686) scale(1,1) translate(9.60787e-15,-1.38553e-13)" writing-mode="lr" x="113.9207080620449" xml:space="preserve" y="647.1863811688671" zvalue="17">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.57142857142844" x2="213.5714285714284" y1="174.1071428571429" y2="174.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.57142857142844" x2="213.5714285714284" y1="200.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.57142857142844" x2="32.57142857142844" y1="174.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="213.5714285714284" y1="174.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="394.5714285714284" y1="174.1071428571429" y2="174.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="394.5714285714284" y1="200.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="213.5714285714284" y1="174.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="394.5714285714284" x2="394.5714285714284" y1="174.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.57142857142844" x2="213.5714285714284" y1="200.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.57142857142844" x2="213.5714285714284" y1="224.3571428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.57142857142844" x2="32.57142857142844" y1="200.1071428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="213.5714285714284" y1="200.1071428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="394.5714285714284" y1="200.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="394.5714285714284" y1="224.3571428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="213.5714285714284" y1="200.1071428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="394.5714285714284" x2="394.5714285714284" y1="200.1071428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.57142857142844" x2="213.5714285714284" y1="224.3571428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.57142857142844" x2="213.5714285714284" y1="247.1071428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.57142857142844" x2="32.57142857142844" y1="224.3571428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="213.5714285714284" y1="224.3571428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="394.5714285714284" y1="224.3571428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="394.5714285714284" y1="247.1071428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="213.5714285714284" y1="224.3571428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="394.5714285714284" x2="394.5714285714284" y1="224.3571428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.57142857142844" x2="213.5714285714284" y1="247.1071428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.57142857142844" x2="213.5714285714284" y1="269.8571428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.57142857142844" x2="32.57142857142844" y1="247.1071428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="213.5714285714284" y1="247.1071428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="394.5714285714284" y1="247.1071428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="394.5714285714284" y1="269.8571428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="213.5714285714284" y1="247.1071428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="394.5714285714284" x2="394.5714285714284" y1="247.1071428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.57142857142844" x2="213.5714285714284" y1="269.8571428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.57142857142844" x2="213.5714285714284" y1="292.6071428571429" y2="292.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="32.57142857142844" x2="32.57142857142844" y1="269.8571428571429" y2="292.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="213.5714285714284" y1="269.8571428571429" y2="292.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="394.5714285714284" y1="269.8571428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="394.5714285714284" y1="292.6071428571429" y2="292.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.5714285714284" x2="213.5714285714284" y1="269.8571428571429" y2="292.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="394.5714285714284" x2="394.5714285714284" y1="269.8571428571429" y2="292.6071428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229.97,328.949) scale(1,1) translate(0,0)" writing-mode="lr" x="229.97" xml:space="preserve" y="333.45" zvalue="21">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,334.97,328.949) scale(1,1) translate(0,0)" writing-mode="lr" x="334.97" xml:space="preserve" y="333.45" zvalue="22">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.5714,188.107) scale(1,1) translate(0,0)" writing-mode="lr" x="70.56999999999999" xml:space="preserve" y="193.61" zvalue="28">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,256.821,188.107) scale(1,1) translate(0,0)" writing-mode="lr" x="256.82" xml:space="preserve" y="193.61" zvalue="29">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,81.7589,217.357) scale(1,1) translate(0,0)" writing-mode="lr" x="81.76000000000001" xml:space="preserve" y="221.86" zvalue="30">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1634.29,465.532) scale(1,1) translate(0,-2.02519e-13)" writing-mode="lr" x="1634.29" xml:space="preserve" y="470.03" zvalue="33">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,750.41,394.937) scale(1,1) translate(0,0)" writing-mode="lr" x="750.41" xml:space="preserve" y="399.44" zvalue="35">031</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,787.143,212.651) scale(1,1) translate(0,0)" writing-mode="lr" x="787.14" xml:space="preserve" y="217.15" zvalue="38">10kV康丰糖厂线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" x="1208.234375" xml:space="preserve" y="186.640625" zvalue="43">#1发电机 </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1208.234375" xml:space="preserve" y="202.640625" zvalue="43">6000kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1179.21,382.967) scale(1,1) translate(0,0)" writing-mode="lr" x="1179.21" xml:space="preserve" y="387.47" zvalue="45">032</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1359,328.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1359" xml:space="preserve" y="332.72" zvalue="50">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,556.25,617.222) scale(1,1) translate(0,0)" writing-mode="lr" x="556.25" xml:space="preserve" y="621.72" zvalue="58">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,596.321,717.329) scale(1,1) translate(0,0)" writing-mode="lr" x="596.3200000000001" xml:space="preserve" y="721.83" zvalue="60">母线PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,686.546,717.329) scale(1,1) translate(0,0)" writing-mode="lr" x="686.55" xml:space="preserve" y="721.83" zvalue="63">站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,646.322,614.472) scale(1,1) translate(0,0)" writing-mode="lr" x="646.3200000000001" xml:space="preserve" y="618.97" zvalue="65">0811</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,733.894,622.064) scale(1,1) translate(0,0)" writing-mode="lr" x="733.89" xml:space="preserve" y="626.5599999999999" zvalue="69">033</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,776.927,717.329) scale(1,1) translate(0,0)" writing-mode="lr" x="776.9299999999999" xml:space="preserve" y="721.83" zvalue="70">备用</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,869.917,717.329) scale(1,1) translate(0,0)" writing-mode="lr" x="869.92" xml:space="preserve" y="721.83" zvalue="73">生活区变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,825.466,618.49) scale(1,1) translate(0,0)" writing-mode="lr" x="825.47" xml:space="preserve" y="622.99" zvalue="75">034</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,959.423,717.329) scale(1,1) translate(0,0)" writing-mode="lr" x="959.42" xml:space="preserve" y="721.83" zvalue="79">泵房变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,916.538,618.49) scale(1,1) translate(0,0)" writing-mode="lr" x="916.54" xml:space="preserve" y="622.99" zvalue="81">035</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1050.5,717.329) scale(1,1) translate(0,0)" writing-mode="lr" x="1050.5" xml:space="preserve" y="721.83" zvalue="86">检修变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1007.61,618.49) scale(1,1) translate(0,0)" writing-mode="lr" x="1007.61" xml:space="preserve" y="622.99" zvalue="88">036</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1141.57,717.329) scale(1,1) translate(0,0)" writing-mode="lr" x="1141.57" xml:space="preserve" y="721.83" zvalue="93">循环水泵变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1098.68,618.49) scale(1,1) translate(0,0)" writing-mode="lr" x="1098.68" xml:space="preserve" y="622.99" zvalue="95">037</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1232.64,717.329) scale(1,1) translate(0,0)" writing-mode="lr" x="1232.64" xml:space="preserve" y="721.83" zvalue="100">制炼变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1189.75,617.657) scale(1,1) translate(0,0)" writing-mode="lr" x="1189.75" xml:space="preserve" y="622.16" zvalue="102">038</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1280.83,612.113) scale(1,1) translate(0,0)" writing-mode="lr" x="1280.83" xml:space="preserve" y="616.61" zvalue="107">039</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1324.61,717.329) scale(1,1) translate(0,0)" writing-mode="lr" x="1324.61" xml:space="preserve" y="721.83" zvalue="108">压榨切撕机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1416.03,717.329) scale(1,1) translate(0,0)" writing-mode="lr" x="1416.03" xml:space="preserve" y="721.83" zvalue="113">压榨变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1371.4,619.323) scale(1,1) translate(0,0)" writing-mode="lr" x="1371.4" xml:space="preserve" y="623.8200000000001" zvalue="115">041</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1507.17,717.329) scale(1,1) translate(0,0)" writing-mode="lr" x="1507.17" xml:space="preserve" y="721.83" zvalue="120">锅炉变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1462.97,618.49) scale(1,1) translate(-3.86883e-12,0)" writing-mode="lr" x="1462.97" xml:space="preserve" y="622.99" zvalue="122">042</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,180.242,1026.31) scale(1,1) translate(-1.67945e-13,-1.57838e-12)" writing-mode="lr" x="180.24" xml:space="preserve" y="1032.31" zvalue="131">杨立超</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="90.19" y="315" zvalue="31"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="31">
   <path class="kv10" d="M 536.25 499.87 L 1622.5 499.87" stroke-width="4" zvalue="32"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674245935108" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674245935108"/></metadata>
  <path d="M 536.25 499.87 L 1622.5 499.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="33">
   <use class="kv10" height="20" transform="rotate(0,787.143,397.073) scale(2.1116,2.06488) translate(-408.813,-194.126)" width="10" x="776.5848766189026" xlink:href="#Breaker:手车开关_0" y="376.4246878996281" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924519067653" ObjectName="10kV康丰糖厂线031断路器"/>
   <cge:TPSR_Ref TObjectID="6473924519067653"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,787.143,397.073) scale(2.1116,2.06488) translate(-408.813,-194.126)" width="10" x="776.5848766189026" y="376.4246878996281"/></g>
  <g id="45">
   <use class="kv10" height="20" transform="rotate(0,1214.68,370.157) scale(2.22238,2.17321) translate(-662.003,-188.097)" width="10" x="1203.571424109595" xlink:href="#Breaker:手车开关_0" y="348.4246878996282" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924519133189" ObjectName="#1发电机032断路器"/>
   <cge:TPSR_Ref TObjectID="6473924519133189"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1214.68,370.157) scale(2.22238,2.17321) translate(-662.003,-188.097)" width="10" x="1203.571424109595" y="348.4246878996282"/></g>
  <g id="66">
   <use class="kv10" height="20" transform="rotate(0,775.844,600.472) scale(1.38273,1.35213) translate(-212.833,-152.859)" width="10" x="768.9300166668116" xlink:href="#Breaker:手车开关_0" y="586.9508835846968" zvalue="68"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924519198725" ObjectName="备用线033断路器"/>
   <cge:TPSR_Ref TObjectID="6473924519198725"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,775.844,600.472) scale(1.38273,1.35213) translate(-212.833,-152.859)" width="10" x="768.9300166668116" y="586.9508835846968"/></g>
  <g id="74">
   <use class="kv10" height="20" transform="rotate(0,866.768,600.472) scale(1.35315,1.32321) translate(-224.446,-143.44)" width="10" x="860.0020233335364" xlink:href="#Breaker:手车开关_0" y="587.2401217197169" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924519264261" ObjectName="生活区变034断路器"/>
   <cge:TPSR_Ref TObjectID="6473924519264261"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,866.768,600.472) scale(1.35315,1.32321) translate(-224.446,-143.44)" width="10" x="860.0020233335364" y="587.2401217197169"/></g>
  <g id="82">
   <use class="kv10" height="20" transform="rotate(0,957.84,600.472) scale(1.35315,1.32321) translate(-248.214,-143.44)" width="10" x="951.074030000261" xlink:href="#Breaker:手车开关_0" y="587.2401217197169" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924519329797" ObjectName="泵房变035断路器"/>
   <cge:TPSR_Ref TObjectID="6473924519329797"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,957.84,600.472) scale(1.35315,1.32321) translate(-248.214,-143.44)" width="10" x="951.074030000261" y="587.2401217197169"/></g>
  <g id="88">
   <use class="kv10" height="20" transform="rotate(0,1048.91,600.472) scale(1.35315,1.32321) translate(-271.982,-143.44)" width="10" x="1042.146036666986" xlink:href="#Breaker:手车开关_0" y="587.2401217197169" zvalue="87"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924519395333" ObjectName="检修变036断路器"/>
   <cge:TPSR_Ref TObjectID="6473924519395333"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1048.91,600.472) scale(1.35315,1.32321) translate(-271.982,-143.44)" width="10" x="1042.146036666986" y="587.2401217197169"/></g>
  <g id="94">
   <use class="kv10" height="20" transform="rotate(0,1139.98,600.472) scale(1.35315,1.32321) translate(-295.75,-143.44)" width="10" x="1133.21804333371" xlink:href="#Breaker:手车开关_0" y="587.2401217197169" zvalue="94"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924519460869" ObjectName="循环水泵变037断路器"/>
   <cge:TPSR_Ref TObjectID="6473924519460869"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1139.98,600.472) scale(1.35315,1.32321) translate(-295.75,-143.44)" width="10" x="1133.21804333371" y="587.2401217197169"/></g>
  <g id="100">
   <use class="kv10" height="20" transform="rotate(0,1231.06,600.472) scale(1.35315,1.32321) translate(-319.518,-143.44)" width="10" x="1224.290050000435" xlink:href="#Breaker:手车开关_0" y="587.2401214018254" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924519526405" ObjectName="制炼变038断路器"/>
   <cge:TPSR_Ref TObjectID="6473924519526405"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1231.06,600.472) scale(1.35315,1.32321) translate(-319.518,-143.44)" width="10" x="1224.290050000435" y="587.2401214018254"/></g>
  <g id="106">
   <use class="kv10" height="20" transform="rotate(0,1322.28,600.472) scale(1.38273,1.35213) translate(-364.081,-152.859)" width="10" x="1315.36205666716" xlink:href="#Breaker:手车开关_0" y="586.9508837436426" zvalue="106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924519591941" ObjectName="压榨切撕机039断路器"/>
   <cge:TPSR_Ref TObjectID="6473924519591941"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1322.28,600.472) scale(1.38273,1.35213) translate(-364.081,-152.859)" width="10" x="1315.36205666716" y="586.9508837436426"/></g>
  <g id="112">
   <use class="kv10" height="20" transform="rotate(0,1413.2,600.472) scale(1.35315,1.32321) translate(-367.055,-143.44)" width="10" x="1406.434063333884" xlink:href="#Breaker:手车开关_0" y="587.2401220376082" zvalue="114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924519657477" ObjectName="压榨变041断路器"/>
   <cge:TPSR_Ref TObjectID="6473924519657477"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1413.2,600.472) scale(1.35315,1.32321) translate(-367.055,-143.44)" width="10" x="1406.434063333884" y="587.2401220376082"/></g>
  <g id="118">
   <use class="kv10" height="20" transform="rotate(0,1504.27,600.472) scale(1.35315,1.32321) translate(-390.823,-143.44)" width="10" x="1497.506070000609" xlink:href="#Breaker:手车开关_0" y="587.2401217197169" zvalue="121"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924519723013" ObjectName="锅炉变042断路器"/>
   <cge:TPSR_Ref TObjectID="6473924519723013"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1504.27,600.472) scale(1.35315,1.32321) translate(-390.823,-143.44)" width="10" x="1497.506070000609" y="587.2401217197169"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="36">
   <use class="kv10" height="30" transform="rotate(0,868.571,374.508) scale(1.42857,1.42857) translate(-254.143,-105.924)" width="30" x="847.1428571428572" xlink:href="#Accessory:PT带熔断器_0" y="353.0793650793651" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449829339142" ObjectName="10kV康丰糖厂线线路PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,868.571,374.508) scale(1.42857,1.42857) translate(-254.143,-105.924)" width="30" x="847.1428571428572" y="353.0793650793651"/></g>
  <g id="47">
   <use class="kv10" height="30" transform="rotate(0,1298.57,412.508) scale(1.42857,1.42857) translate(-383.143,-117.324)" width="30" x="1277.142857142857" xlink:href="#Accessory:PT带熔断器_0" y="391.0793650793651" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449829535749" ObjectName="#1发电机PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1298.57,412.508) scale(1.42857,1.42857) translate(-383.143,-117.324)" width="30" x="1277.142857142857" y="391.0793650793651"/></g>
  <g id="48">
   <use class="kv10" height="30" transform="rotate(0,1357.57,415.508) scale(1.42857,1.42857) translate(-400.843,-118.224)" width="30" x="1336.142857142857" xlink:href="#Accessory:PT带熔断器_0" y="394.0793650793651" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449829601285" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1357.57,415.508) scale(1.42857,1.42857) translate(-400.843,-118.224)" width="30" x="1336.142857142857" y="394.0793650793651"/></g>
  <g id="60">
   <use class="kv10" height="30" transform="rotate(0,594.571,674.508) scale(1.42857,1.42857) translate(-171.943,-195.924)" width="30" x="573.1428571428573" xlink:href="#Accessory:PT带熔断器_0" y="653.0793639894516" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449829797893" ObjectName="10kV母线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,594.571,674.508) scale(1.42857,1.42857) translate(-171.943,-195.924)" width="30" x="573.1428571428573" y="653.0793639894516"/></g>
  <g id="35">
   <use class="kv10" height="32" transform="rotate(0,685.463,674.508) scale(1.25,1.25) translate(-135.593,-130.902)" width="12" x="677.9630940299814" xlink:href="#Accessory:腊撒线路PT_0" y="654.5079354974959" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449829863429" ObjectName="站用变"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,685.463,674.508) scale(1.25,1.25) translate(-135.593,-130.902)" width="12" x="677.9630940299814" y="654.5079354974959"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="39">
   <path class="kv10" d="M 787.14 377.97 L 787.14 277.13" stroke-width="1" zvalue="38"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="37@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 787.14 377.97 L 787.14 277.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv10" d="M 787.14 415.66 L 787.14 499.87" stroke-width="1" zvalue="39"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@1" LinkObjectIDznd="31@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 787.14 415.66 L 787.14 499.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv10" d="M 787.14 328.79 L 868.5 328.79 L 868.5 353.58" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 787.14 328.79 L 868.5 328.79 L 868.5 353.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv10" d="M 1214.68 389.72 L 1214.68 499.87" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@1" LinkObjectIDznd="31@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1214.68 389.72 L 1214.68 499.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv10" d="M 1214.68 350.05 L 1214.68 273.51" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="43@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1214.68 350.05 L 1214.68 273.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv10" d="M 1214.68 291.22 L 1326 291.22 L 1326 313.22" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1214.68 291.22 L 1326 291.22 L 1326 313.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv10" d="M 1298.5 391.58 L 1298.5 373.22 L 1357.5 373.22 L 1357.5 394.58" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1298.5 391.58 L 1298.5 373.22 L 1357.5 373.22 L 1357.5 394.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv10" d="M 1326 343.22 L 1326 373.22" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@1" LinkObjectIDznd="56" MaxPinNum="2"/>
   </metadata>
  <path d="M 1326 343.22 L 1326 373.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv10" d="M 594.39 616.68 L 594.39 653.58" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="60@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 594.39 616.68 L 594.39 653.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv10" d="M 594.29 584.32 L 594.29 499.87" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@1" LinkObjectIDznd="31@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 594.29 584.32 L 594.29 499.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv10" d="M 685.36 584.32 L 685.36 499.87" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@1" LinkObjectIDznd="31@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 685.36 584.32 L 685.36 499.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv10" d="M 685.46 616.68 L 685.46 655.34" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 685.46 616.68 L 685.46 655.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv10" d="M 775.84 660.63 L 775.84 612.64" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="66@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.84 660.63 L 775.84 612.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv10" d="M 775.84 587.96 L 775.84 499.87" stroke-width="1" zvalue="71"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="31@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.84 587.96 L 775.84 499.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv10" d="M 866.77 588.23 L 866.77 499.87" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="31@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.77 588.23 L 866.77 499.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv10" d="M 868.33 662.84 L 868.33 612.38" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@0" LinkObjectIDznd="74@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 868.33 662.84 L 868.33 612.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv10" d="M 957.84 588.23 L 957.84 499.87" stroke-width="1" zvalue="82"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="31@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 957.84 588.23 L 957.84 499.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv10" d="M 957.84 662.84 L 957.84 612.38" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@0" LinkObjectIDznd="82@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 957.84 662.84 L 957.84 612.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv10" d="M 1048.91 588.23 L 1048.91 499.87" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@0" LinkObjectIDznd="31@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1048.91 588.23 L 1048.91 499.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv10" d="M 1048.91 662.84 L 1048.91 612.38" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="88@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1048.91 662.84 L 1048.91 612.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv10" d="M 1139.98 588.23 L 1139.98 499.87" stroke-width="1" zvalue="96"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="31@11" MaxPinNum="2"/>
   </metadata>
  <path d="M 1139.98 588.23 L 1139.98 499.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv10" d="M 1139.98 662.84 L 1139.98 612.38" stroke-width="1" zvalue="97"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@0" LinkObjectIDznd="94@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1139.98 662.84 L 1139.98 612.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv10" d="M 1231.06 588.23 L 1231.06 499.87" stroke-width="1" zvalue="103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@0" LinkObjectIDznd="31@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1231.06 588.23 L 1231.06 499.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv10" d="M 1231.06 662.84 L 1231.06 612.38" stroke-width="1" zvalue="104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="100@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1231.06 662.84 L 1231.06 612.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv10" d="M 1322.28 660.63 L 1322.28 612.64" stroke-width="1" zvalue="109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="106@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.28 660.63 L 1322.28 612.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv10" d="M 1322.28 587.96 L 1322.28 499.87" stroke-width="1" zvalue="110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="31@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.28 587.96 L 1322.28 499.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv10" d="M 1413.2 588.23 L 1413.2 499.87" stroke-width="1" zvalue="116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="31@10" MaxPinNum="2"/>
   </metadata>
  <path d="M 1413.2 588.23 L 1413.2 499.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 1413.2 662.84 L 1413.2 612.38" stroke-width="1" zvalue="117"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="112@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1413.2 662.84 L 1413.2 612.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="kv10" d="M 1504.33 662.84 L 1504.27 612.38" stroke-width="1" zvalue="124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="118@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1504.33 662.84 L 1504.27 612.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv10" d="M 1504.27 588.23 L 1504.27 499.87" stroke-width="1" zvalue="125"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@0" LinkObjectIDznd="31@12" MaxPinNum="2"/>
   </metadata>
  <path d="M 1504.27 588.23 L 1504.27 499.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="43">
   <use class="kv10" height="30" transform="rotate(180,1214,251.389) scale(1.5,1.5) translate(-397.167,-76.2963)" width="30" x="1191.5" xlink:href="#Generator:发电机_0" y="228.8888888888889" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449829470213" ObjectName="#1发电机 6000kW"/>
   <cge:TPSR_Ref TObjectID="6192449829470213"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1214,251.389) scale(1.5,1.5) translate(-397.167,-76.2963)" width="30" x="1191.5" y="228.8888888888889"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="49">
   <use class="kv10" height="30" transform="rotate(0,1326,328.222) scale(1,1) translate(0,0)" width="30" x="1311" xlink:href="#Disconnector:三相刀闸_0" y="313.2222222222222" zvalue="49"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449829666821" ObjectName="#1发电机0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449829666821"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1326,328.222) scale(1,1) translate(0,0)" width="30" x="1311" y="313.2222222222222"/></g>
  <g id="58">
   <use class="kv10" height="26" transform="rotate(0,594.286,600.472) scale(1.25,1.25) translate(-117.357,-116.844)" width="12" x="586.7860033333623" xlink:href="#Disconnector:小车隔刀熔断器_0" y="584.2222222222222" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449829732357" ObjectName="10kV母线PT0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449829732357"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,594.286,600.472) scale(1.25,1.25) translate(-117.357,-116.844)" width="12" x="586.7860033333623" y="584.2222222222222"/></g>
  <g id="52">
   <use class="kv10" height="26" transform="rotate(0,685.358,600.472) scale(1.25,1.25) translate(-135.572,-116.844)" width="12" x="677.858010000087" xlink:href="#Disconnector:小车隔刀熔断器_0" y="584.2222219043308" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449829928965" ObjectName="站用变0811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449829928965"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,685.358,600.472) scale(1.25,1.25) translate(-135.572,-116.844)" width="12" x="677.858010000087" y="584.2222219043308"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="68">
   <use class="kv10" height="30" transform="rotate(0,775.844,674.508) scale(-1.04167,-1.02778) translate(-1520.4,-1330.37)" width="12" x="769.5936483331739" xlink:href="#EnergyConsumer:负荷_0" y="659.0912685924106" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449829994501" ObjectName="备用"/>
   <cge:TPSR_Ref TObjectID="6192449829994501"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,775.844,674.508) scale(-1.04167,-1.02778) translate(-1520.4,-1330.37)" width="12" x="769.5936483331739" y="659.0912685924106"/></g>
  <g id="72">
   <use class="kv10" height="30" transform="rotate(0,868.333,674.508) scale(0.833333,0.833333) translate(171.167,132.402)" width="30" x="855.8333333333334" xlink:href="#EnergyConsumer:站用变DY_0" y="662.007935418023" zvalue="72"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449830060037" ObjectName="生活区变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,868.333,674.508) scale(0.833333,0.833333) translate(171.167,132.402)" width="30" x="855.8333333333334" y="662.007935418023"/></g>
  <g id="83">
   <use class="kv10" height="30" transform="rotate(0,957.84,674.508) scale(0.833333,0.833333) translate(189.068,132.402)" width="30" x="945.3397705664963" xlink:href="#EnergyConsumer:站用变DY_0" y="662.007935418023" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449830125573" ObjectName="泵房变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,957.84,674.508) scale(0.833333,0.833333) translate(189.068,132.402)" width="30" x="945.3397705664963" y="662.007935418023"/></g>
  <g id="89">
   <use class="kv10" height="30" transform="rotate(0,1048.91,674.508) scale(0.833333,0.833333) translate(207.282,132.402)" width="30" x="1036.411777233221" xlink:href="#EnergyConsumer:站用变DY_0" y="662.007935418023" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449830191109" ObjectName="检修变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1048.91,674.508) scale(0.833333,0.833333) translate(207.282,132.402)" width="30" x="1036.411777233221" y="662.007935418023"/></g>
  <g id="95">
   <use class="kv10" height="30" transform="rotate(0,1139.98,674.508) scale(0.833333,0.833333) translate(225.497,132.402)" width="30" x="1127.483783899946" xlink:href="#EnergyConsumer:站用变DY_0" y="662.007935418023" zvalue="92"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449830256645" ObjectName="循环水泵变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1139.98,674.508) scale(0.833333,0.833333) translate(225.497,132.402)" width="30" x="1127.483783899946" y="662.007935418023"/></g>
  <g id="101">
   <use class="kv10" height="30" transform="rotate(0,1231.06,674.508) scale(0.833333,0.833333) translate(243.711,132.402)" width="30" x="1218.55579056667" xlink:href="#EnergyConsumer:站用变DY_0" y="662.0079354205066" zvalue="99"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449830322181" ObjectName="制炼变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1231.06,674.508) scale(0.833333,0.833333) translate(243.711,132.402)" width="30" x="1218.55579056667" y="662.0079354205066"/></g>
  <g id="105">
   <use class="kv10" height="30" transform="rotate(0,1322.28,674.508) scale(-1.04167,-1.02778) translate(-2591.41,-1330.37)" width="12" x="1316.025688333522" xlink:href="#EnergyConsumer:负荷_0" y="659.0912687513563" zvalue="107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449830387717" ObjectName="压榨切撕机"/>
   <cge:TPSR_Ref TObjectID="6192449830387717"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1322.28,674.508) scale(-1.04167,-1.02778) translate(-2591.41,-1330.37)" width="12" x="1316.025688333522" y="659.0912687513563"/></g>
  <g id="113">
   <use class="kv10" height="30" transform="rotate(0,1413.2,674.508) scale(0.833333,0.833333) translate(280.14,132.402)" width="30" x="1400.69980390012" xlink:href="#EnergyConsumer:站用变DY_0" y="662.007935497496" zvalue="112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449830453253" ObjectName="压榨变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1413.2,674.508) scale(0.833333,0.833333) translate(280.14,132.402)" width="30" x="1400.69980390012" y="662.007935497496"/></g>
  <g id="119">
   <use class="kv10" height="30" transform="rotate(0,1504.33,674.508) scale(0.833333,0.833333) translate(298.367,132.402)" width="30" x="1491.833333333333" xlink:href="#EnergyConsumer:站用变DY_0" y="662.007935418023" zvalue="119"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449830518789" ObjectName="锅炉变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1504.33,674.508) scale(0.833333,0.833333) translate(298.367,132.402)" width="30" x="1491.833333333333" y="662.007935418023"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,366.25,326.983) scale(0.708333,0.665547) translate(146.434,159.3)" width="30" x="355.63" xlink:href="#State:红绿圆(方形)_0" y="317" zvalue="135"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,366.25,326.983) scale(0.708333,0.665547) translate(146.434,159.3)" width="30" x="355.63" y="317"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,270.625,326.983) scale(0.708333,0.665547) translate(107.059,159.3)" width="30" x="260" xlink:href="#State:红绿圆(方形)_0" y="317" zvalue="136"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,270.625,326.983) scale(0.708333,0.665547) translate(107.059,159.3)" width="30" x="260" y="317"/></g>
 </g>
</svg>