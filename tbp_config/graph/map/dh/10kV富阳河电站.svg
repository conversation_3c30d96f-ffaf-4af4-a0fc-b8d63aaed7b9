<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549587673090" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV富阳河电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="43.29" xlink:href="logo.png" y="46.86"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,177.911,76.8571) scale(1,1) translate(0,0)" writing-mode="lr" x="177.91" xml:space="preserve" y="80.36" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,179.619,76.5475) scale(1,1) translate(6.80408e-15,0)" writing-mode="lr" x="179.62" xml:space="preserve" y="85.55" zvalue="3">10kV富阳河电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="6" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,64.4375,328) scale(1,1) translate(0,0)" width="72.88" x="28" y="316" zvalue="121"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,64.4375,328) scale(1,1) translate(0,0)" writing-mode="lr" x="64.44" xml:space="preserve" y="332.5" zvalue="121">信号一览</text>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.2857142857144" x2="377.2857142857144" y1="14.85714285714289" y2="1044.857142857143" zvalue="4"/>
  <line fill="none" id="28" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.285714285714675" x2="370.2857142857142" y1="150.7276354712253" y2="150.7276354712253" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="162.8571428571429" y2="162.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="188.8571428571429" y2="188.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="4.285714285714448" y1="162.8571428571429" y2="188.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="162.8571428571429" y2="188.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="162.8571428571429" y2="162.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="188.8571428571429" y2="188.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="162.8571428571429" y2="188.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.2857142857144" x2="366.2857142857144" y1="162.8571428571429" y2="188.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="188.8571428571429" y2="188.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="213.1071428571429" y2="213.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="4.285714285714448" y1="188.8571428571429" y2="213.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="188.8571428571429" y2="213.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="188.8571428571429" y2="188.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="213.1071428571429" y2="213.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="188.8571428571429" y2="213.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.2857142857144" x2="366.2857142857144" y1="188.8571428571429" y2="213.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="213.1071428571429" y2="213.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="235.8571428571429" y2="235.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="4.285714285714448" y1="213.1071428571429" y2="235.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="213.1071428571429" y2="235.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="213.1071428571429" y2="213.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="235.8571428571429" y2="235.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="213.1071428571429" y2="235.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.2857142857144" x2="366.2857142857144" y1="213.1071428571429" y2="235.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="235.8571428571429" y2="235.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="258.6071428571429" y2="258.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="4.285714285714448" y1="235.8571428571429" y2="258.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="235.8571428571429" y2="258.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="235.8571428571429" y2="235.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="258.6071428571429" y2="258.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="235.8571428571429" y2="258.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.2857142857144" x2="366.2857142857144" y1="235.8571428571429" y2="258.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="258.6071428571429" y2="258.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="281.3571428571429" y2="281.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="4.285714285714448" y1="258.6071428571429" y2="281.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="258.6071428571429" y2="281.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="258.6071428571429" y2="258.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="281.3571428571429" y2="281.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="258.6071428571429" y2="281.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.2857142857144" x2="366.2857142857144" y1="258.6071428571429" y2="281.3571428571429"/>
  <line fill="none" id="26" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.285714285714675" x2="370.2857142857142" y1="620.7276354712253" y2="620.7276354712253" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="93.28571428571445" y1="935.8571428571431" y2="935.8571428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="93.28571428571445" y1="975.0204428571431" y2="975.0204428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="3.285714285714448" y1="935.8571428571431" y2="975.0204428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="93.28571428571445" y1="935.8571428571431" y2="975.0204428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="363.2857142857144" y1="935.8571428571431" y2="935.8571428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="363.2857142857144" y1="975.0204428571431" y2="975.0204428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="93.28571428571445" y1="935.8571428571431" y2="975.0204428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.2857142857144" x2="363.2857142857144" y1="935.8571428571431" y2="975.0204428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="93.28571428571445" y1="975.0204128571431" y2="975.0204128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="93.28571428571445" y1="1002.938812857143" y2="1002.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="3.285714285714448" y1="975.0204128571431" y2="1002.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="93.28571428571445" y1="975.0204128571431" y2="1002.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="183.2857142857144" y1="975.0204128571431" y2="975.0204128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="183.2857142857144" y1="1002.938812857143" y2="1002.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="93.28571428571445" y1="975.0204128571431" y2="1002.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857144" x2="183.2857142857144" y1="975.0204128571431" y2="1002.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857146" x2="273.2857142857146" y1="975.0204128571431" y2="975.0204128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857146" x2="273.2857142857146" y1="1002.938812857143" y2="1002.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857146" x2="183.2857142857146" y1="975.0204128571431" y2="1002.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857146" x2="273.2857142857146" y1="975.0204128571431" y2="1002.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857144" x2="363.2857142857144" y1="975.0204128571431" y2="975.0204128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857144" x2="363.2857142857144" y1="1002.938812857143" y2="1002.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857144" x2="273.2857142857144" y1="975.0204128571431" y2="1002.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.2857142857144" x2="363.2857142857144" y1="975.0204128571431" y2="1002.938812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="93.28571428571445" y1="1002.938742857143" y2="1002.938742857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="93.28571428571445" y1="1030.857142857143" y2="1030.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="3.285714285714448" y1="1002.938742857143" y2="1030.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="93.28571428571445" y1="1002.938742857143" y2="1030.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="183.2857142857144" y1="1002.938742857143" y2="1002.938742857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="183.2857142857144" y1="1030.857142857143" y2="1030.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="93.28571428571445" y1="1002.938742857143" y2="1030.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857144" x2="183.2857142857144" y1="1002.938742857143" y2="1030.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857146" x2="273.2857142857146" y1="1002.938742857143" y2="1002.938742857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857146" x2="273.2857142857146" y1="1030.857142857143" y2="1030.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857146" x2="183.2857142857146" y1="1002.938742857143" y2="1030.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857146" x2="273.2857142857146" y1="1002.938742857143" y2="1030.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857144" x2="363.2857142857144" y1="1002.938742857143" y2="1002.938742857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857144" x2="363.2857142857144" y1="1030.857142857143" y2="1030.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857144" x2="273.2857142857144" y1="1002.938742857143" y2="1030.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.2857142857144" x2="363.2857142857144" y1="1002.938742857143" y2="1030.857142857143"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.2857,955.857) scale(1,1) translate(0,0)" writing-mode="lr" x="48.29" xml:space="preserve" y="961.86" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.2857,989.857) scale(1,1) translate(0,0)" writing-mode="lr" x="45.29" xml:space="preserve" y="995.86" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.286,989.857) scale(1,1) translate(0,0)" writing-mode="lr" x="227.29" xml:space="preserve" y="995.86" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.2857,1017.86) scale(1,1) translate(0,0)" writing-mode="lr" x="44.29" xml:space="preserve" y="1023.86" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.286,1017.86) scale(1,1) translate(0,0)" writing-mode="lr" x="226.29" xml:space="preserve" y="1023.86" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,68.7857,650.357) scale(1,1) translate(0,-2.80379e-13)" writing-mode="lr" x="68.78571428571445" xml:space="preserve" y="654.8571428571429" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.34,957.857) scale(1,1) translate(0,0)" writing-mode="lr" x="228.34" xml:space="preserve" y="963.86" zvalue="26">FuYangHe-01-2016</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,138.34,1017.86) scale(1,1) translate(0,0)" writing-mode="lr" x="138.34" xml:space="preserve" y="1023.86" zvalue="27">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,42.2857,176.857) scale(1,1) translate(0,0)" writing-mode="lr" x="42.29" xml:space="preserve" y="182.36" zvalue="29">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.286,176.857) scale(1,1) translate(0,0)" writing-mode="lr" x="222.29" xml:space="preserve" y="182.36" zvalue="30">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.4732,248.857) scale(1,1) translate(0,0)" writing-mode="lr" x="49.47" xml:space="preserve" y="253.36" zvalue="31">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.369,203.552) scale(1,1) translate(0,0)" writing-mode="lr" x="234.37" xml:space="preserve" y="208.05" zvalue="32">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,458.286,648.571) scale(1,1) translate(0,4.96658e-13)" writing-mode="lr" x="458.29" xml:space="preserve" y="653.0700000000001" zvalue="35">0.4kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1194.14,308.429) scale(1,1) translate(-5.17649e-13,0)" writing-mode="lr" x="1194.14" xml:space="preserve" y="312.93" zvalue="36">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,682.857,19.7143) scale(1,1) translate(0,0)" writing-mode="lr" x="682.86" xml:space="preserve" y="24.21" zvalue="37">10kV街富线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" x="569.5703125" xml:space="preserve" y="1022.426586075434" zvalue="41">#1发电机        </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="569.5703125" xml:space="preserve" y="1038.426586075434" zvalue="41">400KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,597.643,865.571) scale(1,1) translate(0,0)" writing-mode="lr" x="597.64" xml:space="preserve" y="870.0700000000001" zvalue="43">441</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,586.341,767) scale(1,1) translate(0,0)" writing-mode="lr" x="586.34" xml:space="preserve" y="771.5" zvalue="46">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,710.643,186.286) scale(1,1) translate(0,0)" writing-mode="lr" x="710.64" xml:space="preserve" y="190.79" zvalue="50">041</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,667.375,257) scale(1,1) translate(0,0)" writing-mode="lr" x="667.37" xml:space="preserve" y="261.5" zvalue="54">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,665.017,125.571) scale(1,1) translate(0,0)" writing-mode="lr" x="665.02" xml:space="preserve" y="130.07" zvalue="58">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,768.571,64.8571) scale(1,1) translate(0,0)" writing-mode="lr" x="768.5700000000001" xml:space="preserve" y="69.36" zvalue="61">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,666.143,370.571) scale(1,1) translate(0,0)" writing-mode="lr" x="666.14" xml:space="preserve" y="375.07" zvalue="64">001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" x="586.9375" xml:space="preserve" y="445.3194444444445" zvalue="67">#1主变      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="586.9375" xml:space="preserve" y="461.3194444444445" zvalue="67">800KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,665.429,537.714) scale(1,1) translate(0,0)" writing-mode="lr" x="665.4299999999999" xml:space="preserve" y="542.21" zvalue="74">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,622.913,612.714) scale(1,1) translate(0,0)" writing-mode="lr" x="622.91" xml:space="preserve" y="617.21" zvalue="78">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,994.718,370.571) scale(1,1) translate(0,0)" writing-mode="lr" x="994.72" xml:space="preserve" y="375.07" zvalue="82">002</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" x="917.5390625" xml:space="preserve" y="442.5399305555556" zvalue="84">#2主变      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="917.5390625" xml:space="preserve" y="458.5399305555556" zvalue="84">800KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,994.003,537.714) scale(1,1) translate(0,0)" writing-mode="lr" x="994" xml:space="preserve" y="542.21" zvalue="86">402</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,950.773,612.714) scale(1,1) translate(0,0)" writing-mode="lr" x="950.77" xml:space="preserve" y="617.21" zvalue="88">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" x="990.6484375" xml:space="preserve" y="1019.539433297657" zvalue="92">#2发电机     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="990.6484375" xml:space="preserve" y="1035.539433297657" zvalue="92">400KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1014.93,867.571) scale(1,1) translate(3.34542e-13,0)" writing-mode="lr" x="1014.93" xml:space="preserve" y="872.0700000000001" zvalue="94">442</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1003.63,769) scale(1,1) translate(0,0)" writing-mode="lr" x="1003.63" xml:space="preserve" y="773.5" zvalue="97">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" x="1324.1796875" xml:space="preserve" y="1024.14087178972" zvalue="102">#3发电机         </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1324.1796875" xml:space="preserve" y="1040.14087178972" zvalue="102">400KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1347.79,869) scale(1,1) translate(0,0)" writing-mode="lr" x="1347.79" xml:space="preserve" y="873.5" zvalue="104">443</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1336.48,770.429) scale(1,1) translate(0,0)" writing-mode="lr" x="1336.48" xml:space="preserve" y="774.9299999999999" zvalue="107">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.0714,201.786) scale(1,1) translate(0,0)" writing-mode="lr" x="52.07" xml:space="preserve" y="206.29" zvalue="111">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236.714,248.929) scale(1,1) translate(0,0)" writing-mode="lr" x="236.71" xml:space="preserve" y="253.43" zvalue="113">10kV#2变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,180.211,328.591) scale(1,1) translate(0,0)" writing-mode="lr" x="180.21" xml:space="preserve" y="333.09" zvalue="117">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,285.211,328.591) scale(1,1) translate(0,0)" writing-mode="lr" x="285.21" xml:space="preserve" y="333.09" zvalue="118">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="10kV富阳河电站.svg"><rect fill-opacity="0" height="24" width="72.88" x="28" y="316" zvalue="121"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="33">
   <path class="v400" d="M 469.14 670.57 L 1534 670.57" stroke-width="6" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674245148676" ObjectName="0.4kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674245148676"/></metadata>
  <path d="M 469.14 670.57 L 1534 670.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv10" d="M 498.57 309.43 L 1151.43 309.43" stroke-width="6" zvalue="35"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674245214212" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674245214212"/></metadata>
  <path d="M 498.57 309.43 L 1151.43 309.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="39">
   <use class="v400" height="30" transform="rotate(0,570.571,970.857) scale(2.14286,2.14286) translate(-287.162,-500.648)" width="30" x="538.4285714285716" xlink:href="#Generator:发电机_0" y="938.7142857142857" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449819049989" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449819049989"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,570.571,970.857) scale(2.14286,2.14286) translate(-287.162,-500.648)" width="30" x="538.4285714285716" y="938.7142857142857"/></g>
  <g id="93">
   <use class="v400" height="30" transform="rotate(0,987.857,972.857) scale(2.14286,2.14286) translate(-509.714,-501.714)" width="30" x="955.7142857142857" xlink:href="#Generator:发电机_0" y="940.7142857142857" zvalue="91"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449819639813" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449819639813"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,987.857,972.857) scale(2.14286,2.14286) translate(-509.714,-501.714)" width="30" x="955.7142857142857" y="940.7142857142857"/></g>
  <g id="102">
   <use class="v400" height="30" transform="rotate(0,1320.71,974.286) scale(2.14286,2.14286) translate(-687.238,-502.476)" width="30" x="1288.571428571428" xlink:href="#Generator:发电机_0" y="942.1428571428571" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449819770885" ObjectName="#3发电机"/>
   <cge:TPSR_Ref TObjectID="6192449819770885"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1320.71,974.286) scale(2.14286,2.14286) translate(-687.238,-502.476)" width="30" x="1288.571428571428" y="942.1428571428571"/></g>
 </g>
 <g id="BreakerClass">
  <g id="41">
   <use class="v400" height="20" transform="rotate(0,570.643,866.571) scale(2.14286,1.92857) translate(-298.629,-407.952)" width="10" x="559.9285714285714" xlink:href="#Breaker:开关_0" y="847.2857142857143" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924515856389" ObjectName="#1发电机441断路器"/>
   <cge:TPSR_Ref TObjectID="6473924515856389"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,570.643,866.571) scale(2.14286,1.92857) translate(-298.629,-407.952)" width="10" x="559.9285714285714" y="847.2857142857143"/></g>
  <g id="47">
   <use class="kv10" height="20" transform="rotate(0,682.929,187.286) scale(2.14286,1.92857) translate(-358.514,-80.8889)" width="10" x="672.2142857142856" xlink:href="#Breaker:开关_0" y="168.0000000000001" zvalue="49"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924515921925" ObjectName="10kV街富线041断路器"/>
   <cge:TPSR_Ref TObjectID="6473924515921925"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,682.929,187.286) scale(2.14286,1.92857) translate(-358.514,-80.8889)" width="10" x="672.2142857142856" y="168.0000000000001"/></g>
  <g id="58">
   <use class="kv10" height="20" transform="rotate(0,638.429,371.571) scale(2.14286,1.92857) translate(-334.781,-169.619)" width="10" x="627.7142857142856" xlink:href="#Breaker:开关_0" y="352.2857142857143" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924515987461" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924515987461"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,638.429,371.571) scale(2.14286,1.92857) translate(-334.781,-169.619)" width="10" x="627.7142857142856" y="352.2857142857143"/></g>
  <g id="67">
   <use class="v400" height="20" transform="rotate(0,638.429,538.714) scale(2.14286,1.92857) translate(-334.781,-250.095)" width="10" x="627.7142857142856" xlink:href="#Breaker:开关_0" y="519.4285714285714" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924516052997" ObjectName="#1主变0.4kV侧401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924516052997"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,638.429,538.714) scale(2.14286,1.92857) translate(-334.781,-250.095)" width="10" x="627.7142857142856" y="519.4285714285714"/></g>
  <g id="81">
   <use class="kv10" height="20" transform="rotate(0,966.289,371.571) scale(2.14286,1.92857) translate(-509.64,-169.619)" width="10" x="955.5746801082878" xlink:href="#Breaker:开关_0" y="352.2857142857143" zvalue="81"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924516184069" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473924516184069"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,966.289,371.571) scale(2.14286,1.92857) translate(-509.64,-169.619)" width="10" x="955.5746801082878" y="352.2857142857143"/></g>
  <g id="77">
   <use class="v400" height="20" transform="rotate(0,966.289,538.714) scale(2.14286,1.92857) translate(-509.64,-250.095)" width="10" x="955.5746801082878" xlink:href="#Breaker:开关_0" y="519.4285714285714" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924516118533" ObjectName="#2主变0.4kV侧402断路器"/>
   <cge:TPSR_Ref TObjectID="6473924516118533"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,966.289,538.714) scale(2.14286,1.92857) translate(-509.64,-250.095)" width="10" x="955.5746801082878" y="519.4285714285714"/></g>
  <g id="92">
   <use class="v400" height="20" transform="rotate(0,987.929,868.571) scale(2.14286,1.92857) translate(-521.181,-408.915)" width="10" x="977.2142857142857" xlink:href="#Breaker:开关_0" y="849.2857142857143" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924516249605" ObjectName="#2发电机442断路器"/>
   <cge:TPSR_Ref TObjectID="6473924516249605"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,987.929,868.571) scale(2.14286,1.92857) translate(-521.181,-408.915)" width="10" x="977.2142857142857" y="849.2857142857143"/></g>
  <g id="101">
   <use class="v400" height="20" transform="rotate(0,1320.79,870) scale(2.14286,1.92857) translate(-698.705,-409.603)" width="10" x="1310.071428571428" xlink:href="#Breaker:开关_0" y="850.7142857142858" zvalue="103"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924516315141" ObjectName="#3发电机443断路器"/>
   <cge:TPSR_Ref TObjectID="6473924516315141"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1320.79,870) scale(2.14286,1.92857) translate(-698.705,-409.603)" width="10" x="1310.071428571428" y="850.7142857142858"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="42">
   <path class="v400" d="M 570.57 939.25 L 570.57 884.99" stroke-width="1" zvalue="43"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@0" LinkObjectIDznd="41@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 570.57 939.25 L 570.57 884.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv10" d="M 683.07 205.7 L 683.07 242.81" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@1" LinkObjectIDznd="50@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 683.07 205.7 L 683.07 242.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv10" d="M 683.03 273.45 L 683.03 309.43" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@1" LinkObjectIDznd="34@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 683.03 273.45 L 683.03 309.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv10" d="M 682.86 58.61 L 682.86 111.38" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="53@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 682.86 58.61 L 682.86 111.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv10" d="M 682.82 142.02 L 682.86 168.84" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="47@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 682.82 142.02 L 682.86 168.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv10" d="M 751.79 89.36 L 682.86 89.36" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@0" LinkObjectIDznd="54" MaxPinNum="2"/>
   </metadata>
  <path d="M 751.79 89.36 L 682.86 89.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv10" d="M 638.36 309.43 L 638.36 353.12" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@1" LinkObjectIDznd="58@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 638.36 309.43 L 638.36 353.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv10" d="M 638.6 423.45 L 638.57 389.99" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="58@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 638.6 423.45 L 638.57 389.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="v400" d="M 638.57 472.65 L 638.57 520.26" stroke-width="1" zvalue="74"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@1" LinkObjectIDznd="67@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 638.57 472.65 L 638.57 520.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="v400" d="M 638.57 557.13 L 638.61 598.52" stroke-width="1" zvalue="78"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@1" LinkObjectIDznd="70@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 638.57 557.13 L 638.61 598.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="v400" d="M 638.57 629.16 L 638.57 670.57" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@1" LinkObjectIDznd="33@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 638.57 629.16 L 638.57 670.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv10" d="M 966.22 309.43 L 966.22 353.12" stroke-width="1" zvalue="82"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@2" LinkObjectIDznd="81@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.22 309.43 L 966.22 353.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv10" d="M 966.46 423.45 L 966.43 389.99" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="81@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.46 423.45 L 966.43 389.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="v400" d="M 966.43 472.65 L 966.43 520.26" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@1" LinkObjectIDznd="77@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.43 472.65 L 966.43 520.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="v400" d="M 966.43 557.13 L 966.47 598.52" stroke-width="1" zvalue="88"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@1" LinkObjectIDznd="75@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.43 557.13 L 966.47 598.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="v400" d="M 966.43 629.16 L 966.43 670.57" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@1" LinkObjectIDznd="33@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.43 629.16 L 966.43 670.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="v400" d="M 987.86 941.25 L 987.86 886.99" stroke-width="1" zvalue="95"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@0" LinkObjectIDznd="92@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 987.86 941.25 L 987.86 886.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="v400" d="M 1320.71 942.68 L 1320.71 888.42" stroke-width="1" zvalue="105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="101@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1320.71 942.68 L 1320.71 888.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="v400" d="M 570.57 848.12 L 570.57 783.45" stroke-width="1" zvalue="122"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="44@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 570.57 848.12 L 570.57 783.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="v400" d="M 570.61 752.81 L 570.61 670.57" stroke-width="1" zvalue="123"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="33@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 570.61 752.81 L 570.61 670.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="v400" d="M 987.86 850.12 L 987.86 785.45" stroke-width="1" zvalue="124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="90@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 987.86 850.12 L 987.86 785.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="v400" d="M 987.9 754.81 L 987.9 670.57" stroke-width="1" zvalue="125"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="33@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 987.9 754.81 L 987.9 670.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="v400" d="M 1320.75 756.23 L 1320.75 670.57" stroke-width="1" zvalue="126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="33@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1320.75 756.23 L 1320.75 670.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="v400" d="M 1320.71 851.55 L 1320.71 819.21 L 1320.71 819.21 L 1320.71 786.87" stroke-width="1" zvalue="127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="99@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1320.71 851.55 L 1320.71 819.21 L 1320.71 819.21 L 1320.71 786.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="44">
   <use class="v400" height="30" transform="rotate(0,570.484,768) scale(1.42857,1.04762) translate(-167.931,-34.1948)" width="15" x="559.7697651084203" xlink:href="#Disconnector:刀闸_0" y="752.2857142857142" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449819115525" ObjectName="#1发电机4411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449819115525"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,570.484,768) scale(1.42857,1.04762) translate(-167.931,-34.1948)" width="15" x="559.7697651084203" y="752.2857142857142"/></g>
  <g id="50">
   <use class="kv10" height="30" transform="rotate(0,682.946,258) scale(1.42857,1.04762) translate(-201.67,-11.013)" width="15" x="672.2317470701987" xlink:href="#Disconnector:刀闸_0" y="242.2857142857144" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449819181061" ObjectName="10kV街富线0411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449819181061"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,682.946,258) scale(1.42857,1.04762) translate(-201.67,-11.013)" width="15" x="672.2317470701987" y="242.2857142857144"/></g>
  <g id="53">
   <use class="kv10" height="30" transform="rotate(0,682.732,126.571) scale(1.42857,1.04762) translate(-201.605,-5.03896)" width="15" x="672.0174613559129" xlink:href="#Disconnector:刀闸_0" y="110.8571428571429" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449819246597" ObjectName="10kV街富线0416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449819246597"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,682.732,126.571) scale(1.42857,1.04762) translate(-201.605,-5.03896)" width="15" x="672.0174613559129" y="110.8571428571429"/></g>
  <g id="70">
   <use class="v400" height="30" transform="rotate(0,638.484,613.714) scale(1.42857,1.04762) translate(-188.331,-27.1818)" width="15" x="627.7697651084203" xlink:href="#Disconnector:刀闸_0" y="598" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449819443205" ObjectName="#1主变0.4kV侧4011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449819443205"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,638.484,613.714) scale(1.42857,1.04762) translate(-188.331,-27.1818)" width="15" x="627.7697651084203" y="598"/></g>
  <g id="75">
   <use class="v400" height="30" transform="rotate(0,966.344,613.714) scale(1.42857,1.04762) translate(-286.689,-27.1818)" width="15" x="955.6301595024224" xlink:href="#Disconnector:刀闸_0" y="598" zvalue="87"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449819508742" ObjectName="#2主变0.4kV侧4021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449819508742"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,966.344,613.714) scale(1.42857,1.04762) translate(-286.689,-27.1818)" width="15" x="955.6301595024224" y="598"/></g>
  <g id="90">
   <use class="v400" height="30" transform="rotate(0,987.77,770) scale(1.42857,1.04762) translate(-293.117,-34.2857)" width="15" x="977.0554793941345" xlink:href="#Disconnector:刀闸_0" y="754.2857142857142" zvalue="96"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449819574277" ObjectName="#2发电机4421隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449819574277"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,987.77,770) scale(1.42857,1.04762) translate(-293.117,-34.2857)" width="15" x="977.0554793941345" y="754.2857142857142"/></g>
  <g id="99">
   <use class="v400" height="30" transform="rotate(0,1320.63,771.429) scale(1.42857,1.04762) translate(-392.974,-34.3506)" width="15" x="1309.912622251277" xlink:href="#Disconnector:刀闸_0" y="755.7142857142857" zvalue="106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449819705349" ObjectName="#3发电机4431隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449819705349"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1320.63,771.429) scale(1.42857,1.04762) translate(-392.974,-34.3506)" width="15" x="1309.912622251277" y="755.7142857142857"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="56">
   <use class="kv10" height="20" transform="rotate(270,765.714,89.4286) scale(1.42857,1.42857) translate(-227.571,-22.5429)" width="10" x="758.5714285714286" xlink:href="#GroundDisconnector:地刀_0" y="75.14285714285734" zvalue="60"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449819377669" ObjectName="10kV街富线04167接地刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449819377669"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,765.714,89.4286) scale(1.42857,1.42857) translate(-227.571,-22.5429)" width="10" x="758.5714285714286" y="75.14285714285734"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="61">
   <g id="610">
    <use class="kv10" height="60" transform="rotate(0,638.568,448) scale(0.75,0.833333) translate(207.856,84.6)" width="40" x="623.5700000000001" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="423" zvalue="66"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874439786500" ObjectName="10"/>
    </metadata>
   </g>
   <g id="611">
    <use class="v400" height="60" transform="rotate(0,638.568,448) scale(0.75,0.833333) translate(207.856,84.6)" width="40" x="623.5700000000001" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="423" zvalue="66"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874439852036" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399452131331" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399452131331"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,638.568,448) scale(0.75,0.833333) translate(207.856,84.6)" width="40" x="623.5700000000001" y="423"/></g>
  <g id="79">
   <g id="790">
    <use class="kv10" height="60" transform="rotate(0,966.429,448) scale(0.75,0.833333) translate(317.143,84.6)" width="40" x="951.4299999999999" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="423" zvalue="83"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874439917572" ObjectName="10"/>
    </metadata>
   </g>
   <g id="791">
    <use class="v400" height="60" transform="rotate(0,966.429,448) scale(0.75,0.833333) translate(317.143,84.6)" width="40" x="951.4299999999999" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="423" zvalue="83"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874439983108" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399452196867" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399452196867"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,966.429,448) scale(0.75,0.833333) translate(317.143,84.6)" width="40" x="951.4299999999999" y="423"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,312.485,329.107) scale(0.708333,0.665547) translate(124.295,160.368)" width="30" x="301.86" xlink:href="#State:红绿圆(方形)_0" y="319.12" zvalue="119"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,312.485,329.107) scale(0.708333,0.665547) translate(124.295,160.368)" width="30" x="301.86" y="319.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,216.86,329.107) scale(0.708333,0.665547) translate(84.9203,160.368)" width="30" x="206.24" xlink:href="#State:红绿圆(方形)_0" y="319.12" zvalue="120"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,216.86,329.107) scale(0.708333,0.665547) translate(84.9203,160.368)" width="30" x="206.24" y="319.12"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="19" id="1" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,496,150) scale(1,1) translate(0,0)" writing-mode="lr" x="495.52" xml:space="preserve" y="156.71" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127152320516" ObjectName="P"/>
   </metadata>
  </g>
 </g>
</svg>