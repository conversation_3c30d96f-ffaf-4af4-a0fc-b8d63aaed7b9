<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" LineID="35kV昔那线那邦首部T线、厂变T线.svg" MapType="line" StationID="5066549596520450" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="1"/>
   <ellipse cx="14.99" cy="7.93" fill-opacity="0" rx="7.43" ry="6.85" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.93" cy="17.56" fill-opacity="0" rx="7.43" ry="6.85" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.9834232682896" x2="14.9834232682896" y1="14.95787681993586" y2="18.2114918126664"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.98342326828961" x2="11.56276000329148" y1="18.23128772350278" y2="20.61950731913853"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00726151837816" x2="18.51522958638884" y1="18.19641261556525" y2="20.42154821077479"/>
   <path d="M 11.6895 9.29402 L 18.7806 9.29402 L 15.2653 3.08877 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.08333333333333" x2="9.833333333333332" y1="16.5" y2="18.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14" y1="9.75" y2="16.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="11" y1="11.58333333333333" y2="9.666666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="9.75" y1="11.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="15.08333333333333" y1="8.416666666666668" y2="25.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_1" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.96,13) scale(1,1) translate(0,0)" width="4.42" x="12.75" y="9"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="5.416666666666668" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_2" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="20" y1="6" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="10.08333333333333" y1="6.000000000000004" y2="25.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Disconnector:20230605_0" viewBox="0,0,26,31">
   <use terminal-index="0" type="0" x="5.73834402750704" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="5.771825533811279" xlink:href="#terminal" y="30.56751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="5.75" y1="3.999999999999998" y2="11.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.25000000000001" x2="20.25000000000001" y1="14.83333333333333" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="9.75" y1="21.5" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.95" x2="17.95" y1="16.5" y2="16.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.25000000000001" x2="19.25000000000001" y1="16.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.25000000000001" x2="21.25000000000001" y1="16.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.25000000000001" x2="22.25000000000001" y1="15.25" y2="14.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.25000000000001" x2="23.25000000000001" y1="12.83333333333333" y2="15.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.25000000000001" x2="22.25000000000001" y1="12.75" y2="13.75"/>
   <ellipse cx="23" cy="14.25" fill-opacity="0" rx="2.24" ry="2.24" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.25,16.5) scale(1,1) translate(0,0)" width="3" x="12.75" y="16"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.668004115226341" x2="0.2096707818930099" y1="1.013815276003976" y2="5.824160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.751337448559672" x2="0.2930041152263385" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.75133744855967" x2="11.209670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.75133744855967" x2="11.209670781893" y1="0.9304819426706423" y2="5.740826770256849"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.751337448559672" x2="0.2930041152263385" y1="27.77315435646374" y2="22.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.751337448559672" x2="0.2930041152263385" y1="30.57918883922238" y2="25.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.75133744855967" x2="11.209670781893" y1="27.77315435646374" y2="22.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.75133744855967" x2="11.209670781893" y1="30.57918883922238" y2="25.76884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.75" x2="5.75" y1="21.33333333333333" y2="27.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.50000000000001" x2="23.50000000000001" y1="16.33333333333333" y2="17.75"/>
   <ellipse cx="23.42" cy="17.83" fill-opacity="0" rx="2.24" ry="2.24" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="20.25" cy="16.5" fill-opacity="0" rx="2.24" ry="2.24" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.50000000000001" x2="22.50000000000001" y1="17.75" y2="18.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.50000000000001" x2="24.50000000000001" y1="17.75" y2="18.75"/>
  </symbol>
  <symbol id="Disconnector:20230605_1" viewBox="0,0,26,31">
   <use terminal-index="0" type="0" x="5.73834402750704" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="5.771825533811279" xlink:href="#terminal" y="30.56751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.916666666666669" x2="17.95" y1="16.5" y2="16.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.25000000000001" x2="23.25000000000001" y1="12.83333333333333" y2="15.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.25000000000001" x2="19.25000000000001" y1="16.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.25000000000001" x2="21.25000000000001" y1="16.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.25000000000001" x2="20.25000000000001" y1="14.83333333333333" y2="16.25"/>
   <rect fill-opacity="0" height="1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.25,16.5) scale(1,1) translate(0,0)" width="3" x="12.75" y="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.25000000000001" x2="22.25000000000001" y1="12.75" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.25000000000001" x2="22.25000000000001" y1="15.25" y2="14.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="5.75" y1="3.999999999999998" y2="11.83333333333333"/>
   <ellipse cx="23" cy="14.25" fill-opacity="0" rx="2.24" ry="2.24" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.668004115226341" x2="0.2096707818930099" y1="1.013815276003976" y2="5.824160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.751337448559672" x2="0.2930041152263385" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.75133744855967" x2="11.209670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.75133744855967" x2="11.209670781893" y1="0.9304819426706423" y2="5.740826770256849"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.751337448559672" x2="0.2930041152263385" y1="27.77315435646374" y2="22.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.751337448559672" x2="0.2930041152263385" y1="30.57918883922238" y2="25.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.75133744855967" x2="11.209670781893" y1="27.77315435646374" y2="22.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.75133744855967" x2="11.209670781893" y1="30.57918883922238" y2="25.76884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.75" x2="5.75" y1="11.83333333333333" y2="27.66666666666666"/>
   <ellipse cx="23.42" cy="17.83" fill-opacity="0" rx="2.24" ry="2.24" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="20.25" cy="16.5" fill-opacity="0" rx="2.24" ry="2.24" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.50000000000001" x2="22.50000000000001" y1="17.75" y2="18.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.50000000000001" x2="24.50000000000001" y1="17.75" y2="18.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.50000000000001" x2="23.50000000000001" y1="16.33333333333333" y2="17.75"/>
  </symbol>
  <symbol id="Disconnector:20230605_2" viewBox="0,0,26,31">
   <use terminal-index="0" type="0" x="5.73834402750704" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="5.771825533811279" xlink:href="#terminal" y="30.56751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.25000000000001" x2="19.25000000000001" y1="16.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.25000000000001" x2="23.25000000000001" y1="12.83333333333333" y2="15.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.25000000000001" x2="20.25000000000001" y1="14.83333333333333" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="7.5" y1="21.5" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.25000000000001" x2="21.25000000000001" y1="16.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.95" x2="17.95" y1="16.5" y2="16.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.25000000000001" x2="22.25000000000001" y1="12.75" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="5.75" y1="3.999999999999998" y2="11.83333333333333"/>
   <rect fill-opacity="0" height="1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.25,16.5) scale(1,1) translate(0,0)" width="3" x="12.75" y="16"/>
   <ellipse cx="23" cy="14.25" fill-opacity="0" rx="2.24" ry="2.24" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.25000000000001" x2="22.25000000000001" y1="15.25" y2="14.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.668004115226341" x2="0.2096707818930099" y1="1.013815276003976" y2="5.824160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.751337448559672" x2="0.2930041152263385" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.75133744855967" x2="11.209670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.75133744855967" x2="11.209670781893" y1="0.9304819426706423" y2="5.740826770256849"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.751337448559672" x2="0.2930041152263385" y1="27.77315435646374" y2="22.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.751337448559672" x2="0.2930041152263385" y1="30.57918883922238" y2="25.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.75133744855967" x2="11.209670781893" y1="27.77315435646374" y2="22.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.75133744855967" x2="11.209670781893" y1="30.57918883922238" y2="25.76884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.75" x2="5.75" y1="21.33333333333333" y2="27.66666666666667"/>
   <ellipse cx="23.42" cy="17.83" fill-opacity="0" rx="2.24" ry="2.24" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="4" y1="21.5" y2="12.5"/>
   <ellipse cx="20.25" cy="16.5" fill-opacity="0" rx="2.24" ry="2.24" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.50000000000001" x2="22.50000000000001" y1="17.75" y2="18.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.50000000000001" x2="24.50000000000001" y1="17.75" y2="18.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.50000000000001" x2="23.50000000000001" y1="16.33333333333333" y2="17.75"/>
  </symbol>
  <symbol id="Breaker:dlq20230605_0" viewBox="0,0,10,22">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="21.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="13.91666666666667" y2="21.18130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="1.916666666666668" y2="5.500000000000003"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="2" y1="14.25" y2="7.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.149999999999999" x2="5.983333333333333" y1="5.05" y2="6.3"/>
   <path d="M 8.63215 18.9056 L 4.98274 21.2297 L 1.33333 18.9056" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.65358 L 5.06482 0.431096 L 1.45119 2.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 17.5723 L 4.98274 19.8963 L 1.33333 17.5723" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 3.90358 L 5.06482 1.6811 L 1.45119 3.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.983333333333333" x2="4.15" y1="5.05" y2="6.3"/>
  </symbol>
  <symbol id="Breaker:dlq20230605_1" viewBox="0,0,10,22">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="21.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.149999999999999" x2="5.983333333333333" y1="5.05" y2="6.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="5.66666666666667" y2="21.18130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="1.916666666666668" y2="5.500000000000003"/>
   <path d="M 8.63215 18.9056 L 4.98274 21.2297 L 1.33333 18.9056" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.65358 L 5.06482 0.431096 L 1.45119 2.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 17.5723 L 4.98274 19.8963 L 1.33333 17.5723" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 3.90358 L 5.06482 1.6811 L 1.45119 3.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.983333333333333" x2="4.15" y1="5.05" y2="6.3"/>
  </symbol>
  <symbol id="Breaker:dlq20230605_2" viewBox="0,0,10,22">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="21.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="13.91666666666667" y2="21.18130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.149999999999999" x2="5.983333333333333" y1="5.05" y2="6.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.5" x2="3.5" y1="14.25" y2="7.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="1.916666666666668" y2="5.500000000000003"/>
   <path d="M 8.63215 18.9056 L 4.98274 21.2297 L 1.33333 18.9056" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.65358 L 5.06482 0.431096 L 1.45119 2.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 17.5723 L 4.98274 19.8963 L 1.33333 17.5723" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 3.90358 L 5.06482 1.6811 L 1.45119 3.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="6.25" y1="14.3" y2="7.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.983333333333333" x2="4.15" y1="5.05" y2="6.3"/>
  </symbol>
  <symbol id="Accessory:中间电缆_0" viewBox="0,0,8,21">
   <use terminal-index="0" type="0" x="4" xlink:href="#terminal" y="10.5"/>
   <path d="M 1.08333 0.5 L 7 0.5 L 4 7.13889 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4" x2="4" y1="20.83333333333333" y2="0.5000000000000036"/>
   <path d="M 1.08333 20.6389 L 7 20.6389 L 4 14 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:T线20230605_0" viewBox="0,0,13,29">
   <use terminal-index="0" type="0" x="6.5" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="6.5" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.5" x2="6.5" y1="18.66666666666667" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.5" x2="6.5" y1="7" y2="0.5"/>
   <rect fill="rgb(0,0,0)" fill-opacity="1" height="3" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-30,2.85,14.75) scale(1,1) translate(0,0)" width="2" x="1.85" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.6" x2="0.2666666666666657" y1="19" y2="8.249999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.100000000000001" x2="8.016666666666666" y1="5.750000000000007" y2="8.333333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.016666666666666" x2="5.100000000000001" y1="5.750000000000007" y2="8.333333333333334"/>
  </symbol>
  <symbol id="Breaker:T线20230605_1" viewBox="0,0,13,29">
   <use terminal-index="0" type="0" x="6.5" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="6.5" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.5" x2="6.5" y1="7" y2="0.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.5" x2="6.5" y1="18.66666666666667" y2="28.5"/>
   <rect fill="rgb(0,0,0)" fill-opacity="1" height="3" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-2,5.43,15) scale(1,1) translate(0,0)" width="2" x="4.43" y="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.100000000000001" x2="8.016666666666666" y1="5.750000000000007" y2="8.333333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.5" x2="6.5" y1="19" y2="7.166666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.016666666666666" x2="5.100000000000001" y1="5.750000000000007" y2="8.333333333333334"/>
  </symbol>
  <symbol id="Breaker:T线20230605_2" viewBox="0,0,13,29">
   <use terminal-index="0" type="0" x="6.5" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="6.5" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.583333333333334" x2="8.583333333333334" y1="9.416666666666666" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.5" x2="6.5" y1="18.66666666666667" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.5" x2="6.5" y1="7" y2="0.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.583333333333334" x2="4.583333333333334" y1="9.416666666666666" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.100000000000001" x2="8.016666666666666" y1="5.750000000000007" y2="8.333333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.016666666666666" x2="5.100000000000001" y1="5.750000000000007" y2="8.333333333333334"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV昔那线那邦首部T线、厂变T线" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="375" x="147" xlink:href="logo.png" y="421.33"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,334.5,451.333) scale(1,1) translate(0,0)" writing-mode="lr" x="334.5" xml:space="preserve" y="454.83" zvalue="138"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,334.5,451.333) scale(1,1) translate(0,0)" writing-mode="lr" x="334.5" xml:space="preserve" y="460.33" zvalue="139">35kV昔那线那邦首部T线、厂变T线</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="41" id="29" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,744.5,327.5) scale(1,1) translate(0,0)" width="107" x="691" y="307" zvalue="704"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,744.5,327.5) scale(1,1) translate(0,0)" writing-mode="lr" x="744.5" xml:space="preserve" y="332" zvalue="704">110kV昔马变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="41" id="75" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1469.5,327.5) scale(1,1) translate(0,0)" width="107" x="1416" y="307" zvalue="706"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1469.5,327.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1469.5" xml:space="preserve" y="332" zvalue="706">35kV那邦变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,940,679) scale(1,1) translate(0,0)" writing-mode="lr" x="940" xml:space="preserve" y="683.5" zvalue="709">那邦水电首部变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1251,681) scale(1,1) translate(0,0)" writing-mode="lr" x="1251" xml:space="preserve" y="685.5" zvalue="711">那邦水电#3厂变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,975.811,491) scale(1,1) translate(0,0)" writing-mode="lr" x="975.8099999999999" xml:space="preserve" y="495.5" zvalue="714">304</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,976,551) scale(1,1) translate(0,0)" writing-mode="lr" x="976" xml:space="preserve" y="555.5" zvalue="715">3046</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1278,400) scale(1,1) translate(0,0)" writing-mode="lr" x="1278" xml:space="preserve" y="404.5" zvalue="722">3031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1303.5,477) scale(1,1) translate(0,0)" writing-mode="lr" x="1303.5" xml:space="preserve" y="481.5" zvalue="724">3036</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1281.5,537) scale(1,1) translate(0,0)" writing-mode="lr" x="1281.5" xml:space="preserve" y="541.5" zvalue="726">303</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,946,313) scale(1,1) translate(0,0)" writing-mode="lr" x="946" xml:space="preserve" y="317.5" zvalue="736">#07</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1255,313) scale(1,1) translate(0,0)" writing-mode="lr" x="1255" xml:space="preserve" y="317.5" zvalue="738">#54</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,868,309.5) scale(1,1) translate(0,0)" writing-mode="lr" x="868" xml:space="preserve" y="314" zvalue="739">35kV昔那线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1040.5,372.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1040.5" xml:space="preserve" y="377" zvalue="741">35kV昔那线那邦首部T线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1329.5,343.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1329.5" xml:space="preserve" y="348" zvalue="743">35kV昔那线那邦厂变T线</text>
 </g>
 <g id="ButtonClass">
  <g href="110kV昔马变.svg"><rect fill-opacity="0" height="41" width="107" x="691" y="307" zvalue="704"/></g>
  <g href="35kV那邦变.svg"><rect fill-opacity="0" height="41" width="107" x="1416" y="307" zvalue="706"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="76">
   <path class="kv35" d="M 800 326 L 1415 326" stroke-width="1" zvalue="707"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 800 326 L 1415 326" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv35" d="M 942.06 471.24 L 942.06 326" stroke-width="1" zvalue="715"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="76" MaxPinNum="2"/>
   </metadata>
  <path d="M 942.06 471.24 L 942.06 326" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv35" d="M 942.06 513.13 L 942.06 538.19" stroke-width="1" zvalue="716"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@1" LinkObjectIDznd="95@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 942.06 513.13 L 942.06 538.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv35" d="M 942.09 559.64 L 942.09 609.07" stroke-width="1" zvalue="717"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@0" LinkObjectIDznd="78@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 942.09 559.64 L 942.09 609.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv35" d="M 962.63 452.97 L 942.06 452.97" stroke-width="1" zvalue="718"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="106" MaxPinNum="2"/>
   </metadata>
  <path d="M 962.63 452.97 L 942.06 452.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv35" d="M 1254 606.07 L 1254 550.88" stroke-width="1" zvalue="726"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="164@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1254 606.07 L 1254 550.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv35" d="M 1254 519.75 L 1254 491.07" stroke-width="1" zvalue="727"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@0" LinkObjectIDznd="161@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1254 519.75 L 1254 491.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv35" d="M 1254.74 461.45 L 1254.74 413" stroke-width="1" zvalue="728"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="134@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1254.74 461.45 L 1254.74 413" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv35" d="M 1254 384 L 1254 326" stroke-width="1" zvalue="729"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@0" LinkObjectIDznd="76" MaxPinNum="2"/>
   </metadata>
  <path d="M 1254 384 L 1254 326" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv35" d="M 1281.63 369.97 L 1254 369.97" stroke-width="1" zvalue="730"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@0" LinkObjectIDznd="171" MaxPinNum="2"/>
   </metadata>
  <path d="M 1281.63 369.97 L 1254 369.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv35" d="M 1254 580 L 1254 580" stroke-width="1" zvalue="734"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="166" MaxPinNum="2"/>
   </metadata>
  <path d="M 1254 580 L 1254 580" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="kv35" d="M 1255 438 L 1254.74 438" stroke-width="1" zvalue="735"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="173@0" LinkObjectIDznd="169" MaxPinNum="2"/>
   </metadata>
  <path d="M 1255 438 L 1254.74 438" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="78">
   <use class="kv35" height="30" transform="rotate(0,941,638) scale(2.06667,2.06667) translate(-469.677,-313.29)" width="30" x="910" xlink:href="#EnergyConsumer:站用变DY_0" y="607" zvalue="708"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450280488965" ObjectName="那邦水电首部变"/>
   <cge:TPSR_Ref TObjectID="6192450280488965"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,941,638) scale(2.06667,2.06667) translate(-469.677,-313.29)" width="30" x="910" y="607"/></g>
  <g id="82">
   <use class="kv35" height="30" transform="rotate(0,1254,635) scale(2.06667,2.06667) translate(-631.226,-311.742)" width="30" x="1223" xlink:href="#EnergyConsumer:站用变DY_0" y="604" zvalue="710"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450280554501" ObjectName="那邦水电#3厂变"/>
   <cge:TPSR_Ref TObjectID="6192450280554501"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1254,635) scale(2.06667,2.06667) translate(-631.226,-311.742)" width="30" x="1223" y="604"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="84">
   <use class="kv35" height="26" transform="rotate(270,975,453) scale(1,1) translate(0,0)" width="12" x="969" xlink:href="#Accessory:避雷器1_0" y="440" zvalue="712"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450280620037" ObjectName="1避雷器"/>
   <cge:TPSR_Ref TObjectID="6192450280620037"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,975,453) scale(1,1) translate(0,0)" width="12" x="969" y="440"/></g>
  <g id="124">
   <use class="kv35" height="26" transform="rotate(270,1294,370) scale(1,1) translate(0,0)" width="12" x="1288" xlink:href="#Accessory:避雷器1_0" y="357" zvalue="720"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450280751109" ObjectName="2避雷器"/>
   <cge:TPSR_Ref TObjectID="6192450280751109"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1294,370) scale(1,1) translate(0,0)" width="12" x="1288" y="357"/></g>
  <g id="173">
   <use class="kv35" height="21" transform="rotate(0,1255,438) scale(1,1) translate(0,0)" width="8" x="1251" xlink:href="#Accessory:中间电缆_0" y="427.5" zvalue="731"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450280947717" ObjectName="电缆1"/>
   <cge:TPSR_Ref TObjectID="6192450280947717"/></metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,1255,438) scale(1,1) translate(0,0)" width="8" x="1251" y="427.5"/></g>
  <g id="174">
   <use class="kv35" height="21" transform="rotate(0,1254,580) scale(1,1) translate(0,0)" width="8" x="1250" xlink:href="#Accessory:中间电缆_0" y="569.5" zvalue="733"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450281013253" ObjectName="电缆2"/>
   <cge:TPSR_Ref TObjectID="6192450281013253"/></metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,1254,580) scale(1,1) translate(0,0)" width="8" x="1250" y="569.5"/></g>
 </g>
 <g id="BreakerClass">
  <g id="86">
   <use class="kv35" height="29" transform="rotate(0,942.061,492) scale(2.03846,1.48276) translate(-473.168,-153.186)" width="13" x="928.8111644241059" xlink:href="#Breaker:T线20230605_0" y="470.5" zvalue="713"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924590436356" ObjectName="35kV那邦首部变304断路器"/>
   <cge:TPSR_Ref TObjectID="6473924590436356"/></metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,942.061,492) scale(2.03846,1.48276) translate(-473.168,-153.186)" width="13" x="928.8111644241059" y="470.5"/></g>
  <g id="164">
   <use class="kv35" height="22" transform="rotate(0,1254,535.5) scale(1.5,1.5) translate(-415.5,-173)" width="10" x="1246.5" xlink:href="#Breaker:dlq20230605_0" y="519" zvalue="725"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924590501892" ObjectName="303"/>
   <cge:TPSR_Ref TObjectID="6473924590501892"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1254,535.5) scale(1.5,1.5) translate(-415.5,-173)" width="10" x="1246.5" y="519"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="95">
   <use class="kv35" height="30" transform="rotate(0,942,549) scale(1,-0.733333) translate(0,-1301.64)" width="15" x="934.5" xlink:href="#Disconnector:刀闸_0" y="538" zvalue="714"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450280685573" ObjectName="3046"/>
   <cge:TPSR_Ref TObjectID="6192450280685573"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,942,549) scale(1,-0.733333) translate(0,-1301.64)" width="15" x="934.5" y="538"/></g>
  <g id="134">
   <use class="kv35" height="30" transform="rotate(0,1254,399) scale(1,1) translate(0,0)" width="30" x="1239" xlink:href="#Disconnector:跌落刀闸_0" y="384" zvalue="721"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450280816645" ObjectName="3031"/>
   <cge:TPSR_Ref TObjectID="6192450280816645"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1254,399) scale(1,1) translate(0,0)" width="30" x="1239" y="384"/></g>
  <g id="161">
   <use class="kv35" height="31" transform="rotate(0,1262,476) scale(1,1) translate(0,0)" width="26" x="1249" xlink:href="#Disconnector:20230605_0" y="460.5" zvalue="723"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450280882181" ObjectName="3036"/>
   <cge:TPSR_Ref TObjectID="6192450280882181"/></metadata>
  <rect fill="white" height="31" opacity="0" stroke="white" transform="rotate(0,1262,476) scale(1,1) translate(0,0)" width="26" x="1249" y="460.5"/></g>
 </g>
</svg>