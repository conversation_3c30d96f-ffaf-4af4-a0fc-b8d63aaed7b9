<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549587476482" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-Y_0" viewBox="0,0,30,50">
   <ellipse cx="14.99" cy="14.99" fill-opacity="0" rx="14.76" ry="14.76" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.99138374485597" x2="10.08333333333333" y1="11.06149193548387" y2="16.08333333333334"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01984739368999" x2="20" y1="11.06354954865259" y2="16"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00235768175583" x2="15.00235768175583" y1="6.416666666666664" y2="11.06149193548386"/>
   <use terminal-index="0" type="1" x="14.99138374485597" xlink:href="#terminal" y="0.2276829173857209"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-Y_1" viewBox="0,0,30,50">
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00235768175583" x2="15.00235768175583" y1="32" y2="37.04670698924731"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00546553497943" x2="20" y1="37.04651063100137" y2="41.75"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00235768175583" x2="10" y1="37.04670698924731" y2="41.91666666666666"/>
   <ellipse cx="15.01" cy="35.25" fill-opacity="0" rx="14.76" ry="14.76" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="1" x="15.01333161865569" xlink:href="#terminal" y="50.00705645161292"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变13_0" viewBox="0,0,32,35">
   <use terminal-index="0" type="0" x="16" xlink:href="#terminal" y="3.5"/>
   <ellipse cx="16.04" cy="11.97" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.97" cy="24.14" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="20.85" y2="24.95894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="10.66666666666667" y1="24.98394833233988" y2="28.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="21.16666666666667" y1="24.93990500916851" y2="28.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="7.6" y2="11.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="12.05" y1="11.73394833233987" y2="14.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="20.15" y1="11.68990500916851" y2="14.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV勐板河一级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="41" xlink:href="logo.png" y="46.67"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,175.625,76.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="175.63" xml:space="preserve" y="80.17" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,177.333,76.357) scale(1,1) translate(6.55032e-15,0)" writing-mode="lr" x="177.33" xml:space="preserve" y="85.36" zvalue="3">10kV勐板河一级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="67" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,71.625,328.25) scale(1,1) translate(0,0)" width="72.88" x="35.19" y="316.25" zvalue="102"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,71.625,328.25) scale(1,1) translate(0,0)" writing-mode="lr" x="71.63" xml:space="preserve" y="332.75" zvalue="102">信号一览</text>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="375.0000000000001" x2="375.0000000000001" y1="14.66666666666674" y2="1044.666666666667" zvalue="4"/>
  <line fill="none" id="28" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.410605131648481e-13" x2="367.9999999999999" y1="150.5371592807491" y2="150.5371592807491" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="162.6666666666668" y2="162.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="2.000000000000114" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="162.6666666666668" y2="162.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.0000000000001" x2="364.0000000000001" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="2.000000000000114" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.0000000000001" x2="364.0000000000001" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="2.000000000000114" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.0000000000001" x2="364.0000000000001" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="2.000000000000114" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.0000000000001" x2="364.0000000000001" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="281.1666666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="2.000000000000114" y1="258.4166666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="258.4166666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="281.1666666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="258.4166666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.0000000000001" x2="364.0000000000001" y1="258.4166666666668" y2="281.1666666666668"/>
  <line fill="none" id="26" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.410605131648481e-13" x2="367.9999999999999" y1="620.5371592807492" y2="620.5371592807492" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="91.00000000000011" y1="935.666666666667" y2="935.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="91.00000000000011" y1="974.829966666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="1.000000000000114" y1="935.666666666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="91.00000000000011" y1="935.666666666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="361.0000000000001" y1="935.666666666667" y2="935.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="361.0000000000001" y1="974.829966666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="91.00000000000011" y1="935.666666666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.0000000000001" x2="361.0000000000001" y1="935.666666666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="91.00000000000011" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="91.00000000000011" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="1.000000000000114" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="91.00000000000011" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="181.0000000000001" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="181.0000000000001" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="91.00000000000011" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000001" x2="181.0000000000001" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000002" x2="271.0000000000002" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000002" x2="271.0000000000002" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000002" x2="181.0000000000002" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000002" x2="271.0000000000002" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="361.0000000000001" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="361.0000000000001" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="271.0000000000001" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.0000000000001" x2="361.0000000000001" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="91.00000000000011" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="91.00000000000011" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="1.000000000000114" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="91.00000000000011" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="181.0000000000001" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="181.0000000000001" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="91.00000000000011" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000001" x2="181.0000000000001" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000002" x2="271.0000000000002" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000002" x2="271.0000000000002" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000002" x2="181.0000000000002" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000002" x2="271.0000000000002" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="361.0000000000001" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="361.0000000000001" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="271.0000000000001" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.0000000000001" x2="361.0000000000001" y1="1002.748266666667" y2="1030.666666666667"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46,955.667) scale(1,1) translate(0,0)" writing-mode="lr" x="46" xml:space="preserve" y="961.67" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43,989.667) scale(1,1) translate(0,0)" writing-mode="lr" x="43" xml:space="preserve" y="995.67" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225,989.667) scale(1,1) translate(0,0)" writing-mode="lr" x="225" xml:space="preserve" y="995.67" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,42,1017.67) scale(1,1) translate(0,0)" writing-mode="lr" x="42" xml:space="preserve" y="1023.67" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224,1017.67) scale(1,1) translate(0,0)" writing-mode="lr" x="224" xml:space="preserve" y="1023.67" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,66.5,650.167) scale(1,1) translate(0,0)" writing-mode="lr" x="66.50000000000011" xml:space="preserve" y="654.6666666666667" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.054,957.667) scale(1,1) translate(0,0)" writing-mode="lr" x="226.05" xml:space="preserve" y="963.67" zvalue="26">MengBanHe-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,134.054,992.667) scale(1,1) translate(0,0)" writing-mode="lr" x="134.05" xml:space="preserve" y="998.67" zvalue="27">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,40,176.667) scale(1,1) translate(0,0)" writing-mode="lr" x="40" xml:space="preserve" y="182.17" zvalue="29">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,220,176.667) scale(1,1) translate(0,0)" writing-mode="lr" x="220" xml:space="preserve" y="182.17" zvalue="30">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.1875,248.667) scale(1,1) translate(0,0)" writing-mode="lr" x="47.19" xml:space="preserve" y="253.17" zvalue="31">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.083,203.361) scale(1,1) translate(0,0)" writing-mode="lr" x="232.08" xml:space="preserve" y="207.86" zvalue="32">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,481.286,682.714) scale(1,1) translate(0,0)" writing-mode="lr" x="481.29" xml:space="preserve" y="687.21" zvalue="35">0.4kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" x="916.8515625" xml:space="preserve" y="994.9618490134186" zvalue="38">#1发电机    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="916.8515625" xml:space="preserve" y="1010.961849013419" zvalue="38">500KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,892.578,744.505) scale(1,1) translate(0,0)" writing-mode="lr" x="892.58" xml:space="preserve" y="749" zvalue="40">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,937.184,822.926) scale(1,1) translate(2.05876e-13,0)" writing-mode="lr" x="937.1799999999999" xml:space="preserve" y="827.4299999999999" zvalue="44">441</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" x="1457.6328125" xml:space="preserve" y="1003.743099013419" zvalue="48">#2发电机    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1457.6328125" xml:space="preserve" y="1019.743099013419" zvalue="48">500KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1434.36,752.291) scale(1,1) translate(0,0)" writing-mode="lr" x="1434.36" xml:space="preserve" y="756.79" zvalue="50">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1478.97,830.712) scale(1,1) translate(0,0)" writing-mode="lr" x="1478.97" xml:space="preserve" y="835.21" zvalue="54">442</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,727,602.291) scale(1,1) translate(0,0)" writing-mode="lr" x="727" xml:space="preserve" y="606.79" zvalue="58">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,771.606,533.212) scale(1,1) translate(0,0)" writing-mode="lr" x="771.61" xml:space="preserve" y="537.71" zvalue="61">401</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" x="696.6875" xml:space="preserve" y="444.125" zvalue="66">#1主变          </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="696.6875" xml:space="preserve" y="460.125" zvalue="66">1250KVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,451.375,327) scale(1,1) translate(0,0)" writing-mode="lr" x="451.38" xml:space="preserve" y="331.5" zvalue="68">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1157.75,391.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.75" xml:space="preserve" y="395.75" zvalue="70">0811</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,894.188,272.59) scale(1,1) translate(0,-4.70007e-13)" writing-mode="lr" x="894.1900000000001" xml:space="preserve" y="277.09" zvalue="75">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,898.96,224.611) scale(1,1) translate(2.95916e-13,0)" writing-mode="lr" x="898.96" xml:space="preserve" y="229.11" zvalue="78">041</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,893.354,162.924) scale(1,1) translate(0,0)" writing-mode="lr" x="893.35" xml:space="preserve" y="167.42" zvalue="81">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,879.333,43.3333) scale(1,1) translate(0,0)" writing-mode="lr" x="879.33" xml:space="preserve" y="47.83" zvalue="83">10kV东勐线勐板支线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1340.44,279.507) scale(1,1) translate(0,0)" writing-mode="lr" x="1340.44" xml:space="preserve" y="284.01" zvalue="86">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.5,201.5) scale(1,1) translate(0,0)" writing-mode="lr" x="48.5" xml:space="preserve" y="206" zvalue="92">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1188.36,530) scale(1,1) translate(0,0)" writing-mode="lr" x="1188.36" xml:space="preserve" y="534.5" zvalue="96">站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,187.399,328.841) scale(1,1) translate(0,0)" writing-mode="lr" x="187.4" xml:space="preserve" y="333.34" zvalue="98">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,292.399,328.841) scale(1,1) translate(0,0)" writing-mode="lr" x="292.4" xml:space="preserve" y="333.34" zvalue="99">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="10kV勐板河一级电站.svg"><rect fill-opacity="0" height="24" width="72.88" x="35.19" y="316.25" zvalue="102"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="33">
   <path class="v400" d="M 520 683.71 L 1742.86 683.71" stroke-width="6" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674244755460" ObjectName="0.4kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674244755460"/></metadata>
  <path d="M 520 683.71 L 1742.86 683.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv10" d="M 485 326.75 L 1772.5 326.75" stroke-width="6" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674244820996" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674244820996"/></metadata>
  <path d="M 485 326.75 L 1772.5 326.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v400" height="30" transform="rotate(0,912.062,940.148) scale(1.85899,1.85899) translate(-408.555,-421.533)" width="30" x="884.1770967546338" xlink:href="#Generator:发电机_0" y="912.2629266475365" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449815969798" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449815969798"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,912.062,940.148) scale(1.85899,1.85899) translate(-408.555,-421.533)" width="30" x="884.1770967546338" y="912.2629266475365"/></g>
  <g id="49">
   <use class="v400" height="30" transform="rotate(0,1452.85,948.933) scale(1.85899,1.85899) translate(-658.438,-425.592)" width="30" x="1424.962811040348" xlink:href="#Generator:发电机_0" y="921.0486409332507" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449816100870" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449816100870"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1452.85,948.933) scale(1.85899,1.85899) translate(-658.438,-425.592)" width="30" x="1424.962811040348" y="921.0486409332507"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="165">
   <use class="v400" height="30" transform="rotate(0,911.797,745.505) scale(1.9625,1.2338) translate(-439.968,-137.761)" width="15" x="897.077807004066" xlink:href="#Disconnector:刀闸_0" y="726.9980158730157" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449815904262" ObjectName="#1发电机4411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449815904262"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,911.797,745.505) scale(1.9625,1.2338) translate(-439.968,-137.761)" width="15" x="897.077807004066" y="726.9980158730157"/></g>
  <g id="48">
   <use class="v400" height="30" transform="rotate(0,1453.58,753.291) scale(1.9625,1.2338) translate(-705.685,-139.237)" width="15" x="1438.86352128978" xlink:href="#Disconnector:刀闸_0" y="734.7837301587299" zvalue="49"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449816035334" ObjectName="#2发电机4421隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449816035334"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1453.58,753.291) scale(1.9625,1.2338) translate(-705.685,-139.237)" width="15" x="1438.86352128978" y="734.7837301587299"/></g>
  <g id="55">
   <use class="v400" height="30" transform="rotate(0,746.219,603.291) scale(1.9625,1.2338) translate(-358.761,-110.813)" width="15" x="731.5" xlink:href="#Disconnector:刀闸_0" y="584.7837301587299" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449816166406" ObjectName="#1主变4011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449816166406"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,746.219,603.291) scale(1.9625,1.2338) translate(-358.761,-110.813)" width="15" x="731.5" y="584.7837301587299"/></g>
  <g id="64">
   <use class="kv10" height="26" transform="rotate(0,1184.75,392.25) scale(1.25,1.25) translate(-235.45,-75.2)" width="12" x="1177.25" xlink:href="#Disconnector:单手车刀闸1212_0" y="376" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449816231942" ObjectName="站用变0811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449816231942"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1184.75,392.25) scale(1.25,1.25) translate(-235.45,-75.2)" width="12" x="1177.25" y="376"/></g>
  <g id="70">
   <use class="kv10" height="30" transform="rotate(0,879.469,274.257) scale(1.9625,-1.2338) translate(-424.113,-493.037)" width="15" x="864.75" xlink:href="#Disconnector:刀闸_0" y="255.7500000000001" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449816297478" ObjectName="10kV东勐线勐板支线0411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449816297478"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,879.469,274.257) scale(1.9625,-1.2338) translate(-424.113,-493.037)" width="15" x="864.75" y="255.7500000000001"/></g>
  <g id="74">
   <use class="kv10" height="30" transform="rotate(0,878.969,163.257) scale(1.9625,-1.2338) translate(-423.868,-292.071)" width="15" x="864.25" xlink:href="#Disconnector:刀闸_0" y="144.7500000000001" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449816363014" ObjectName="10kV东勐线勐板支线0416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449816363014"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,878.969,163.257) scale(1.9625,-1.2338) translate(-423.868,-292.071)" width="15" x="864.25" y="144.7500000000001"/></g>
  <g id="82">
   <use class="kv10" height="30" transform="rotate(0,1318.05,279.84) scale(1.9625,-1.2338) translate(-639.214,-503.146)" width="15" x="1303.333333333333" xlink:href="#Disconnector:刀闸_0" y="261.3333333333334" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449816494086" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449816494086"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1318.05,279.84) scale(1.9625,-1.2338) translate(-639.214,-503.146)" width="15" x="1303.333333333333" y="261.3333333333334"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="40">
   <path class="v400" d="M 911.97 727.61 L 911.97 683.71" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="33@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 911.97 727.61 L 911.97 683.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="v400" d="M 912.06 912.73 L 912.06 832.23" stroke-width="1" zvalue="43"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="170@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 912.06 912.73 L 912.06 832.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="v400" d="M 912.69 810.99 L 912.69 763.7" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="165@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 912.69 810.99 L 912.69 763.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="v400" d="M 1453.75 735.4 L 1453.75 683.71" stroke-width="1" zvalue="51"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@0" LinkObjectIDznd="33@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1453.75 735.4 L 1453.75 683.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="v400" d="M 1452.85 921.51 L 1452.85 840.02" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="46@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1452.85 921.51 L 1452.85 840.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="v400" d="M 1454.47 818.77 L 1454.47 771.48" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1454.47 818.77 L 1454.47 771.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="v400" d="M 746.34 621.48 L 746.34 683.71" stroke-width="1" zvalue="63"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@1" LinkObjectIDznd="33@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 746.34 621.48 L 746.34 683.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="v400" d="M 747.23 542.52 L 746.39 585.4" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="55@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 747.23 542.52 L 746.39 585.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="v400" d="M 747.52 468.01 L 747.11 521.27" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@1" LinkObjectIDznd="53@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 747.52 468.01 L 747.11 521.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv10" d="M 747.49 405.78 L 747.49 326.75" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="61@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 747.49 405.78 L 747.49 326.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv10" d="M 1184.75 376.09 L 1184.75 326.75" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@1" LinkObjectIDznd="61@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1184.75 376.09 L 1184.75 326.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv10" d="M 879.64 292.15 L 879.64 326.75" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="61@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.64 292.15 L 879.64 326.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv10" d="M 879.26 234.22 L 879.26 256.07" stroke-width="1" zvalue="78"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@1" LinkObjectIDznd="70@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.26 234.22 L 879.26 256.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv10" d="M 879.14 181.15 L 879.14 212.98" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="72@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.14 181.15 L 879.14 212.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv10" d="M 879.33 63.62 L 879.09 145.07" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@0" LinkObjectIDznd="74@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.33 63.62 L 879.09 145.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv10" d="M 1318.22 297.74 L 1318.22 326.75" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="61@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1318.22 297.74 L 1318.22 326.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv10" d="M 1318.17 261.65 L 1318.17 217.15" stroke-width="1" zvalue="88"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@1" LinkObjectIDznd="88@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1318.17 261.65 L 1318.17 217.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv10" d="M 1349.97 244.3 L 1318.17 244.3" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="85" MaxPinNum="2"/>
   </metadata>
  <path d="M 1349.97 244.3 L 1318.17 244.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv10" d="M 1184.86 468.31 L 1184.86 408.46" stroke-width="1" zvalue="96"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="64@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1184.86 468.31 L 1184.86 408.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="170">
   <use class="v400" height="20" transform="rotate(0,912.727,821.619) scale(1.22222,1.11111) translate(-164.839,-81.0508)" width="10" x="906.6154525852342" xlink:href="#Breaker:开关_0" y="810.507631257631" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924515135493" ObjectName="#1发电机441断路器"/>
   <cge:TPSR_Ref TObjectID="6473924515135493"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,912.727,821.619) scale(1.22222,1.11111) translate(-164.839,-81.0508)" width="10" x="906.6154525852342" y="810.507631257631"/></g>
  <g id="46">
   <use class="v400" height="20" transform="rotate(0,1454.51,829.404) scale(1.22222,1.11111) translate(-263.346,-81.8293)" width="10" x="1448.401166870948" xlink:href="#Breaker:开关_0" y="818.2933455433454" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924515201029" ObjectName="#2发电机442断路器"/>
   <cge:TPSR_Ref TObjectID="6473924515201029"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1454.51,829.404) scale(1.22222,1.11111) translate(-263.346,-81.8293)" width="10" x="1448.401166870948" y="818.2933455433454"/></g>
  <g id="53">
   <use class="v400" height="20" transform="rotate(0,747.149,531.904) scale(1.22222,1.11111) translate(-134.734,-52.0793)" width="10" x="741.0376455811681" xlink:href="#Breaker:开关_0" y="520.7933455433454" zvalue="60"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924515266565" ObjectName="#1主变401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924515266565"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,747.149,531.904) scale(1.22222,1.11111) translate(-134.734,-52.0793)" width="10" x="741.0376455811681" y="520.7933455433454"/></g>
  <g id="72">
   <use class="kv10" height="20" transform="rotate(0,879.182,223.611) scale(1.22222,1.11111) translate(-158.74,-21.25)" width="10" x="873.0706420919439" xlink:href="#Breaker:开关_0" y="212.5" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924515332101" ObjectName="10kV东勐线勐板支线041断路器"/>
   <cge:TPSR_Ref TObjectID="6473924515332101"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,879.182,223.611) scale(1.22222,1.11111) translate(-158.74,-21.25)" width="10" x="873.0706420919439" y="212.5"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="58">
   <g id="580">
    <use class="kv10" height="50" transform="rotate(0,747.5,436.75) scale(1.25,1.25) translate(-145.75,-81.1)" width="30" x="728.75" xlink:href="#PowerTransformer2:Y-Y_0" y="405.5" zvalue="65"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874439131140" ObjectName="10"/>
    </metadata>
   </g>
   <g id="581">
    <use class="v400" height="50" transform="rotate(0,747.5,436.75) scale(1.25,1.25) translate(-145.75,-81.1)" width="30" x="728.75" xlink:href="#PowerTransformer2:Y-Y_1" y="405.5" zvalue="65"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874439196676" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399451803651" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399451803651"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,747.5,436.75) scale(1.25,1.25) translate(-145.75,-81.1)" width="30" x="728.75" y="405.5"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="84">
   <use class="kv10" height="26" transform="rotate(270,1362.97,244.333) scale(1.05128,1.05128) translate(-66.1789,-11.252)" width="12" x="1356.666666666667" xlink:href="#Accessory:避雷器1_0" y="230.6666666666668" zvalue="87"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449816559621" ObjectName="10kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1362.97,244.333) scale(1.05128,1.05128) translate(-66.1789,-11.252)" width="12" x="1356.666666666667" y="230.6666666666668"/></g>
  <g id="88">
   <use class="kv10" height="18" transform="rotate(0,1320.11,197.333) scale(2.37037,-2.37037) translate(-752.911,-268.25)" width="15" x="1302.333333333333" xlink:href="#Accessory:PT8_0" y="176.0000000000001" zvalue="90"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449816625157" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1320.11,197.333) scale(2.37037,-2.37037) translate(-752.911,-268.25)" width="15" x="1302.333333333333" y="176.0000000000001"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="52">
   <use class="kv10" height="35" transform="rotate(0,1184.86,487.562) scale(1.375,1.375) translate(-317.142,-126.409)" width="32" x="1162.855084029894" xlink:href="#EnergyConsumer:站用变13_0" y="463.5" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449816690693" ObjectName="站用变"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1184.86,487.562) scale(1.375,1.375) translate(-317.142,-126.409)" width="32" x="1162.855084029894" y="463.5"/></g>
 </g>
 <g id="StateClass">
  <g id="203">
   <use height="30" transform="rotate(0,319.673,329.357) scale(0.708333,0.665547) translate(127.255,160.493)" width="30" x="309.05" xlink:href="#State:红绿圆(方形)_0" y="319.37" zvalue="100"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,319.673,329.357) scale(0.708333,0.665547) translate(127.255,160.493)" width="30" x="309.05" y="319.37"/></g>
  <g id="215">
   <use height="30" transform="rotate(0,224.048,329.357) scale(0.708333,0.665547) translate(87.8799,160.493)" width="30" x="213.42" xlink:href="#State:红绿圆(方形)_0" y="319.37" zvalue="101"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,224.048,329.357) scale(0.708333,0.665547) translate(87.8799,160.493)" width="30" x="213.42" y="319.37"/></g>
 </g>
</svg>