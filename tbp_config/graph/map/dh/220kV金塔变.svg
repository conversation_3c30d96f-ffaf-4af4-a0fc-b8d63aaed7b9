<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="ACLineSegment:220kV线路_0" viewBox="0,0,35,40">
   <use terminal-index="0" type="0" x="14.20833333333333" xlink:href="#terminal" y="39.00547521122963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.5" x2="30.5" y1="30.75" y2="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.5" x2="27.5" y1="25.75" y2="30.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.50545055364641" x2="21.50545055364641" y1="13.04138864447597" y2="19.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.5" x2="24.5" y1="23.75" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="5.5" y1="37" y2="37"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.91094883543335" x2="24.38823024054981" y1="12.92541949757221" y2="12.92541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.5" x2="18.5" y1="23.75" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.5" x2="30.5" y1="25.75" y2="27.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="6.5" y1="36" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="7.5" y1="35" y2="35"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.04,24.92) scale(1,1) translate(0,0)" width="6.08" x="2" y="17.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="32" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="4" y1="25.75" y2="19.75"/>
   <path d="M 14 13 L 5 13 L 5 25 L 5 25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="6" y1="25.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.5" x2="21.5" y1="20.75" y2="23.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.46058610156545" x2="14.15436235204273" y1="12.92541949757221" y2="12.92541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16022336769755" x2="14.16022336769755" y1="38.83333333333334" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.46383161512025" x2="31.41666666666666" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.35940244368076" x2="24.35940244368076" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.36598892707138" x2="31.36598892707138" y1="9.749999999999996" y2="16.24517624503405"/>
   <ellipse cx="21.51" cy="23.82" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="28.55" cy="28.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.76" cy="32.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="24.35940244368077" x2="24.35940244368077" y1="10.48325293741726" y2="15.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.51910080183274" x2="32.51910080183274" y1="11.01295093653441" y2="15.34306843322376"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="33.38393470790376" x2="33.38393470790376" y1="11.91505874834466" y2="13.89969593432727"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.57589728904158" x2="16.57589728904158" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="26.52148720885832" x2="26.52148720885832" y1="10.48325293741726" y2="15.5840919325617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="16.49256395570825" x2="16.49256395570825" y1="10.33333333333333" y2="15.43417232847779"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="18.73798205421914" x2="18.73798205421914" y1="10.48325293741726" y2="15.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.58333333333333" x2="18.58333333333333" y1="32.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.58333333333334" x2="24.58333333333334" y1="32.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.58333333333333" x2="21.58333333333333" y1="29.5" y2="32.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="220kV金塔变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="925.3099999999999" xlink:href="logo.png" y="126.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1050.15,167.714) scale(1,1) translate(-2.05459e-13,0)" writing-mode="lr" x="1050.15" xml:space="preserve" y="171.21" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,1073,160.69) scale(1,1) translate(0,0)" writing-mode="lr" x="1073" xml:space="preserve" y="169.69" zvalue="3">220kV金塔变</text>
  <line fill="none" id="38" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384" x2="384" y1="11" y2="1041" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,772.023,575.192) scale(1,1) translate(0,0)" writing-mode="lr" x="772.02" xml:space="preserve" y="579.6900000000001" zvalue="46">220kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,761.795,685.529) scale(1,1) translate(0,0)" writing-mode="lr" x="761.8" xml:space="preserve" y="690.03" zvalue="47">220kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="827" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1061.28,508.131) scale(1,1) translate(0,0)" writing-mode="lr" x="1061.28" xml:space="preserve" y="512.63" zvalue="269">281</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1118.72,581.649) scale(1,1) translate(0,0)" writing-mode="lr" x="1118.72" xml:space="preserve" y="586.15" zvalue="271">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1030.85,565.607) scale(1,1) translate(0,0)" writing-mode="lr" x="1030.85" xml:space="preserve" y="570.11" zvalue="273">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="464" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1059.25,452.083) scale(1,1) translate(0,0)" writing-mode="lr" x="1059.25" xml:space="preserve" y="456.58" zvalue="277">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="828" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1082.32,312.25) scale(1,1) translate(-2.28777e-13,0)" writing-mode="lr" x="1082.32" xml:space="preserve" y="316.75" zvalue="281">220kV金塔Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="378" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1133.62,527.484) scale(1,1) translate(0,0)" writing-mode="lr" x="1133.62" xml:space="preserve" y="531.98" zvalue="283">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="453" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1133.53,474.192) scale(1,1) translate(0,0)" writing-mode="lr" x="1133.53" xml:space="preserve" y="478.69" zvalue="1297">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="733" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1131.12,409.4) scale(1,1) translate(0,0)" writing-mode="lr" x="1131.12" xml:space="preserve" y="413.9" zvalue="1299">67</text>
 </g>
 <g id="ButtonClass"/>
 <g id="BusbarSectionClass">
  <g id="856">
   <path class="kv220" d="M 778 615.21 L 1607.25 615.21" stroke-width="4" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674266120197" ObjectName="220kVⅡ母"/>
   </metadata>
  <path d="M 778 615.21 L 1607.25 615.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="855">
   <path class="kv220" d="M 772 652.39 L 1607.25 652.39" stroke-width="4" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674266185733" ObjectName="220kVⅠ母"/>
   </metadata>
  <path d="M 772 652.39 L 1607.25 652.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="288">
   <use class="kv220" height="20" transform="rotate(0,1080.12,509.131) scale(1.46889,-1.322) translate(-342.445,-891.032)" width="10" x="1072.775678089911" xlink:href="#Breaker:开关_0" y="495.9109346757106" zvalue="268"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924607672325" ObjectName="220kV金塔Ⅰ回线281断路器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1080.12,509.131) scale(1.46889,-1.322) translate(-342.445,-891.032)" width="10" x="1072.775678089911" y="495.9109346757106"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="287">
   <use class="kv220" height="30" transform="rotate(0,1101.09,568.774) scale(0.839827,-0.615873) translate(208.8,-1498.06)" width="15" x="1094.789642290147" xlink:href="#Disconnector:刀闸_0" y="559.5357176235746" zvalue="269"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450421391365" ObjectName="220kV金塔Ⅰ回线2811隔离开关"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1101.09,568.774) scale(0.839827,-0.615873) translate(208.8,-1498.06)" width="15" x="1094.789642290147" y="559.5357176235746"/></g>
  <g id="284">
   <use class="kv220" height="30" transform="rotate(0,1056.9,566.274) scale(0.839827,-0.615873) translate(200.372,-1491.5)" width="15" x="1050.599166099671" xlink:href="#Disconnector:刀闸_0" y="557.0357176235743" zvalue="272"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450421325829" ObjectName="220kV金塔Ⅰ回线2812隔离开关"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1056.9,566.274) scale(0.839827,-0.615873) translate(200.372,-1491.5)" width="15" x="1050.599166099671" y="557.0357176235743"/></g>
  <g id="280">
   <use class="kv220" height="30" transform="rotate(0,1080.12,445.583) scale(0.839827,-0.615873) translate(204.801,-1174.84)" width="15" x="1073.821429027135" xlink:href="#Disconnector:刀闸_0" y="436.3452414330982" zvalue="276"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450421260293" ObjectName="220kV金塔Ⅰ回线2816隔离开关"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1080.12,445.583) scale(0.839827,-0.615873) translate(204.801,-1174.84)" width="15" x="1073.821429027135" y="436.3452414330982"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="276">
   <use class="kv220" height="40" transform="rotate(180,1077.82,351.131) scale(0.714286,-0.714286) translate(426.128,-848.429)" width="35" x="1065.320309669026" xlink:href="#ACLineSegment:220kV线路_0" y="336.8452382314772" zvalue="280"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450421194757" ObjectName="220kV金塔Ⅰ回线"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,1077.82,351.131) scale(0.714286,-0.714286) translate(426.128,-848.429)" width="35" x="1065.320309669026" y="336.8452382314772"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="274">
   <use class="kv220" height="20" transform="rotate(90,1134.66,547.161) scale(0.94375,-0.835887) translate(67.3475,-1203.39)" width="10" x="1129.941033121339" xlink:href="#GroundDisconnector:地刀_0" y="538.8018430004414" zvalue="282"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450421129221" ObjectName="220kV金塔Ⅰ回线28127接地开关"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1134.66,547.161) scale(0.94375,-0.835887) translate(67.3475,-1203.39)" width="10" x="1129.941033121339" y="538.8018430004414"/></g>
  <g id="389">
   <use class="kv220" height="20" transform="rotate(90,1109.16,478.036) scale(0.94375,-0.835887) translate(65.8276,-1051.57)" width="10" x="1104.441033121339" xlink:href="#GroundDisconnector:地刀_0" y="469.6768430004414" zvalue="1296"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450421522437" ObjectName="220kV金塔Ⅰ回线28160接地开关"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1109.16,478.036) scale(0.94375,-0.835887) translate(65.8276,-1051.57)" width="10" x="1104.441033121339" y="469.6768430004414"/></g>
  <g id="478">
   <use class="kv220" height="20" transform="rotate(90,1103.62,407.619) scale(0.94375,-0.835887) translate(65.4973,-896.909)" width="10" x="1098.899366454672" xlink:href="#GroundDisconnector:地刀_0" y="399.2601763337748" zvalue="1298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450421653509" ObjectName="220kV金塔Ⅰ回线28167接地开关"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1103.62,407.619) scale(0.94375,-0.835887) translate(65.4973,-896.909)" width="10" x="1098.899366454672" y="399.2601763337748"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="35" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1182.82,321.417) scale(1,1) translate(0,0)" writing-mode="lr" x="1182.35" xml:space="preserve" y="326.19" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="667">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="667" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1184.82,346.226) scale(1,1) translate(0,0)" writing-mode="lr" x="1184.35" xml:space="preserve" y="351" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="678">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="678" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1185.82,376.036) scale(1,1) translate(0,0)" writing-mode="lr" x="1185.35" xml:space="preserve" y="380.81" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="18" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,856,675.893) scale(1,1) translate(0,0)" writing-mode="lr" x="812.88" xml:space="preserve" y="680.25" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,837,594.714) scale(1,1) translate(0,0)" writing-mode="lr" x="793.88" xml:space="preserve" y="599.08" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="804">
   <path class="kv220" d="M 1101.16 577.71 L 1101.16 652.39" stroke-width="1" zvalue="1299"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="287@0" LinkObjectIDznd="855@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1101.16 577.71 L 1101.16 652.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="806">
   <path class="kv220" d="M 1056.97 575.21 L 1056.97 615.21" stroke-width="1" zvalue="1300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="284@0" LinkObjectIDznd="856@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1056.97 575.21 L 1056.97 615.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="854">
   <path class="kv220" d="M 1056.95 557.19 L 1056.95 543.5 L 1101.14 543.5 L 1101.14 559.69" stroke-width="1" zvalue="1301"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="284@1" LinkObjectIDznd="287@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1056.95 557.19 L 1056.95 543.5 L 1101.14 543.5 L 1101.14 559.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="865">
   <path class="kv220" d="M 1080.07 543.5 L 1080.07 521.78" stroke-width="1" zvalue="1302"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="854" LinkObjectIDznd="288@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.07 543.5 L 1080.07 521.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="869">
   <path class="kv220" d="M 1126.51 547.21 L 1101.14 547.21" stroke-width="1" zvalue="1303"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="854" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.51 547.21 L 1101.14 547.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="871">
   <path class="kv220" d="M 1080.22 496.51 L 1080.22 454.52" stroke-width="1" zvalue="1304"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="288@1" LinkObjectIDznd="280@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.22 496.51 L 1080.22 454.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="872">
   <path class="kv220" d="M 1080.17 436.5 L 1080.17 364.71" stroke-width="1" zvalue="1305"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@1" LinkObjectIDznd="276@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.17 436.5 L 1080.17 364.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="877">
   <path class="kv220" d="M 1095.47 407.67 L 1080.17 407.67" stroke-width="1" zvalue="1306"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="478@0" LinkObjectIDznd="872" MaxPinNum="2"/>
   </metadata>
  <path d="M 1095.47 407.67 L 1080.17 407.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="878">
   <path class="kv220" d="M 1101.01 478.08 L 1080.22 478.08" stroke-width="1" zvalue="1307"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="389@0" LinkObjectIDznd="871" MaxPinNum="2"/>
   </metadata>
  <path d="M 1101.01 478.08 L 1080.22 478.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
</svg>