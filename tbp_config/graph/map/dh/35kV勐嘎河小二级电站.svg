<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549584199682" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:RT1122_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="22.91666666666667" y2="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="18.16666666666666" y2="20.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="18.16666666666666" y2="20.16666666666666"/>
   <path d="M 13 11 L 17 11 L 15 8 L 13 11 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="15.41666666666666" y2="18.16666666666666"/>
   <ellipse cx="14.95" cy="18.02" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.95" cy="9.92" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1754.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2334.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_0" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3" x2="7" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.25" x2="5.083333333333332" y1="7.083333333333336" y2="19.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_1" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.833333333333333" x2="6.916666666666667" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.05" x2="5.05" y1="5.000000000000004" y2="22.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_2" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.166666666666668" x2="1.333333333333333" y1="5.083333333333332" y2="24.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1" x2="8.833333333333334" y1="4.999999999999998" y2="24.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Accessory:消谐装置_0" viewBox="0,0,20,29">
   <use terminal-index="0" type="0" x="10.15872800538976" xlink:href="#terminal" y="25.96480127873437"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.12399698478855" x2="10.12399698478855" y1="26.28498263464221" y2="23.3231983778117"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.938434782209253" x2="14.28859387834882" y1="21.48754948791521" y2="14.19403516164853"/>
   <rect fill-opacity="0" height="5.22" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,10.11,17.58) scale(1,1) translate(0,0)" width="11.46" x="4.38" y="14.97"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.945791858126634" x2="5.945791858126634" y1="23.57968743439848" y2="21.49582619832228"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.28123680243143" x2="14.28123680243143" y1="14.18575845124145" y2="12.10189721516526"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.15025549608711" x2="10.15025549608711" y1="23.41666666666667" y2="4.867706583258689"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.135856301213458" x2="8.135856301213458" y1="4.455199311740065" y2="4.455199311740065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.659702116379664" x2="8.659702116379664" y1="29.26065666272731" y2="29.26065666272731"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.35591008128069" x2="7.891898490280618" y1="4.703700522631296" y2="4.703700522631296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.57292939360264" x2="8.848534280964973" y1="3.398988163859496" y2="3.398988163859496"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.05089117767898" x2="9.544227599895001" y1="1.833333333333339" y2="1.833333333333339"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.986779448160899" x2="8.986779448160899" y1="29.68994241064946" y2="29.68994241064946"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="Accessory:带电容电阻接地_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.75"/>
   <rect fill-opacity="0" height="11.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.94,6.29) scale(1,1) translate(0,0)" width="6.08" x="2.9" y="0.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666663" x2="10.5" y1="20.49453511141348" y2="20.49453511141348"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.603429610654176" x2="7.693139016796801" y1="25.38116790988687" y2="25.38116790988687"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.965686274509802" x2="9.330882352941176" y1="22.90451817731685" y2="22.90451817731685"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.916666666666667" x2="5.916666666666667" y1="14.58333333333333" y2="11.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.083333333333333" x2="6.083333333333333" y1="20.58333333333334" y2="16.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.833333333333333" x2="9" y1="16.16120177808014" y2="16.16120177808014"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.833333333333333" x2="9.25" y1="14.66120177808014" y2="14.66120177808014"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_0" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.66666666666666" x2="27.83333333333334" y1="6.416666666666666" y2="22.08333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_1" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.5" x2="28.5" y1="3.833333333333332" y2="22"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_2" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.16666666666667" x2="35.5" y1="3.083333333333332" y2="24.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="35.5" x2="21.16666666666667" y1="3.166666666666664" y2="24.83333333333334"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV勐嘎河小二级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="50.21" xlink:href="logo.png" y="47.57"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.214,79.5714) scale(1,1) translate(0,0)" writing-mode="lr" x="199.21" xml:space="preserve" y="83.06999999999999" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,201.714,77.2618) scale(1,1) translate(0,0)" writing-mode="lr" x="201.71" xml:space="preserve" y="86.26000000000001" zvalue="3">35kV勐嘎河小二级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="80" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="328"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="328">信号一览</text>
  <line fill="none" id="112" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384.2142857142856" x2="384.2142857142856" y1="15.57142857142867" y2="1045.571428571429" zvalue="4"/>
  <line fill="none" id="110" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="151.441921185511" y2="151.441921185511" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,652.437,350.73) scale(1,1) translate(0,0)" writing-mode="lr" x="652.4400000000001" xml:space="preserve" y="355.23" zvalue="8">301</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,551.087,432.602) scale(1,1) translate(0,0)" writing-mode="lr" x="551.09" xml:space="preserve" y="437.1" zvalue="10">#1主变12.5MVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,505.191,589.591) scale(1,1) translate(0,0)" writing-mode="lr" x="505.19" xml:space="preserve" y="594.09" zvalue="12">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,627.827,916.385) scale(1,1) translate(0,2.98303e-13)" writing-mode="lr" x="627.8273662659592" xml:space="preserve" y="920.8846849061007" zvalue="14">#1发电机5MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,971.997,875.425) scale(1,1) translate(0,0)" writing-mode="lr" x="972" xml:space="preserve" y="879.92" zvalue="16">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,695.222,284.889) scale(1,1) translate(0,0)" writing-mode="lr" x="695.22" xml:space="preserve" y="289.39" zvalue="20">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,651.682,266.425) scale(1,1) translate(0,0)" writing-mode="lr" x="651.6799999999999" xml:space="preserve" y="270.92" zvalue="22">3016</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,626.28,115.25) scale(1,1) translate(0,0)" writing-mode="lr" x="626.28" xml:space="preserve" y="119.75" zvalue="32">35kV勐小T线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,598.046,219.675) scale(1,1) translate(0,0)" writing-mode="lr" x="598.05" xml:space="preserve" y="224.17" zvalue="50">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,502.778,172.778) scale(1,1) translate(0,0)" writing-mode="lr" x="502.78" xml:space="preserve" y="177.28" zvalue="55">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,503.889,270.778) scale(1,1) translate(0,0)" writing-mode="lr" x="503.89" xml:space="preserve" y="275.28" zvalue="58">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,978.265,382.875) scale(1,1) translate(0,0)" writing-mode="lr" x="978.27" xml:space="preserve" y="387.38" zvalue="161">6.3kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1000.63,549.544) scale(1,1) translate(-2.18565e-13,0)" writing-mode="lr" x="1000.63" xml:space="preserve" y="554.04" zvalue="163">6901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1029.42,475.806) scale(1,1) translate(0,0)" writing-mode="lr" x="1029.42" xml:space="preserve" y="480.31" zvalue="167">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1011.35,662.349) scale(1,1) translate(0,0)" writing-mode="lr" x="1011.35" xml:space="preserve" y="666.85" zvalue="173">6231</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="150" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1575.96,538.715) scale(1,1) translate(0,0)" writing-mode="lr" x="1575.96" xml:space="preserve" y="543.21" zvalue="190">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1558.14,410.103) scale(1,1) translate(0,0)" writing-mode="lr" x="1558.14" xml:space="preserve" y="414.6" zvalue="195">10kV近区变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="177" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1503,500.73) scale(1,1) translate(0,-1.09297e-13)" writing-mode="lr" x="1503" xml:space="preserve" y="505.23" zvalue="198">624</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="192" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1507.69,316.73) scale(1,1) translate(6.64084e-12,0)" writing-mode="lr" x="1507.69" xml:space="preserve" y="321.23" zvalue="202">021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1502.38,257.758) scale(1,1) translate(0,0)" writing-mode="lr" x="1502.38" xml:space="preserve" y="262.26" zvalue="204">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="171" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1476.75,115.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1476.75" xml:space="preserve" y="119.75" zvalue="207">10kV大坝线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1498.27,570.258) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.27" xml:space="preserve" y="574.76" zvalue="214">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="213" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,650.573,566.425) scale(1,1) translate(0,0)" writing-mode="lr" x="650.5700000000001" xml:space="preserve" y="570.92" zvalue="224">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,659.215,522.73) scale(1,1) translate(-1.58219e-12,0)" writing-mode="lr" x="659.22" xml:space="preserve" y="527.23" zvalue="226">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,650.24,663.147) scale(1,1) translate(0,0)" writing-mode="lr" x="650.24" xml:space="preserve" y="667.65" zvalue="239">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,651.074,733.175) scale(1,1) translate(-1.41907e-13,0)" writing-mode="lr" x="651.0700000000001" xml:space="preserve" y="737.67" zvalue="243">621</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,748.856,833.008) scale(1,1) translate(0,0)" writing-mode="lr" x="748.86" xml:space="preserve" y="837.51" zvalue="252">6912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,520.519,854.758) scale(1,1) translate(0,0)" writing-mode="lr" x="520.52" xml:space="preserve" y="859.26" zvalue="257">6911</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="245" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,486.176,950.882) scale(1,1) translate(0,0)" writing-mode="lr" x="486.18" xml:space="preserve" y="955.38" zvalue="259">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,998.421,734.993) scale(1,1) translate(0,0)" writing-mode="lr" x="998.42" xml:space="preserve" y="739.49" zvalue="270">623</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1487.83,920.385) scale(1,1) translate(0,2.99636e-13)" writing-mode="lr" x="1487.827366265959" xml:space="preserve" y="924.8846849061007" zvalue="274">#2发电机5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1509.52,663.147) scale(1,1) translate(0,0)" writing-mode="lr" x="1509.52" xml:space="preserve" y="667.65" zvalue="278">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1511.07,733.175) scale(1,1) translate(0,0)" writing-mode="lr" x="1511.07" xml:space="preserve" y="737.67" zvalue="282">622</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1608.86,833.008) scale(1,1) translate(0,0)" writing-mode="lr" x="1608.86" xml:space="preserve" y="837.51" zvalue="290">6922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1380.52,854.758) scale(1,1) translate(0,0)" writing-mode="lr" x="1380.52" xml:space="preserve" y="859.26" zvalue="295">6921</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1346.18,950.882) scale(1,1) translate(0,0)" writing-mode="lr" x="1346.18" xml:space="preserve" y="955.38" zvalue="299">机组PT</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="99" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="310"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="926" y2="926"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="965.1632999999999" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="926" y2="926"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="965.1632999999999" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="993.0816" y2="1021"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,946) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="952" zvalue="312">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.8889,978.889) scale(1,1) translate(0,0)" writing-mode="lr" x="52.89" xml:space="preserve" y="984.89" zvalue="313">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.889,978.889) scale(1,1) translate(0,0)" writing-mode="lr" x="234.89" xml:space="preserve" y="984.89" zvalue="314">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.8889,1006.89) scale(1,1) translate(0,0)" writing-mode="lr" x="51.89" xml:space="preserve" y="1012.89" zvalue="315">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.889,1006.89) scale(1,1) translate(0,0)" writing-mode="lr" x="233.89" xml:space="preserve" y="1012.89" zvalue="316">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,560.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="565" zvalue="318">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="319">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="320">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="321">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="322">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="323">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="329">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="330">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="384.75" zvalue="333">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="335">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="337">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="338">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="339">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="341">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,218.5,946) scale(1,1) translate(0,0)" writing-mode="lr" x="218.5" xml:space="preserve" y="952" zvalue="345">MengGaHe x2-01-2012</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="328"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BreakerClass">
  <g id="418">
   <use class="kv35" height="20" transform="rotate(0,627.541,351.73) scale(1.22222,1.11111) translate(-112.987,-34.0619)" width="10" x="621.4303734218488" xlink:href="#Breaker:开关_0" y="340.6190476190475" zvalue="7"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924495409157" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924495409157"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,627.541,351.73) scale(1.22222,1.11111) translate(-112.987,-34.0619)" width="10" x="621.4303734218488" y="340.6190476190475"/></g>
  <g id="163">
   <use class="v6300" height="20" transform="rotate(0,1482.39,501.73) scale(1.22222,1.11111) translate(-268.415,-49.0619)" width="10" x="1476.2826217389" xlink:href="#Breaker:开关_0" y="490.6190476190475" zvalue="197"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924495474693" ObjectName="10kV近区变6.3kV侧624断路器"/>
   <cge:TPSR_Ref TObjectID="6473924495474693"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1482.39,501.73) scale(1.22222,1.11111) translate(-268.415,-49.0619)" width="10" x="1476.2826217389" y="490.6190476190475"/></g>
  <g id="166">
   <use class="kv10" height="20" transform="rotate(0,1481.85,317.73) scale(1.22222,1.11111) translate(-268.315,-30.6619)" width="10" x="1475.734418171838" xlink:href="#Breaker:开关_0" y="306.6190476190475" zvalue="201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924495540229" ObjectName="10kV近区变10kV侧021断路器"/>
   <cge:TPSR_Ref TObjectID="6473924495540229"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1481.85,317.73) scale(1.22222,1.11111) translate(-268.315,-30.6619)" width="10" x="1475.734418171838" y="306.6190476190475"/></g>
  <g id="199">
   <use class="v6300" height="20" transform="rotate(0,627.541,519.73) scale(1.22222,1.11111) translate(-112.987,-50.8619)" width="10" x="621.4303734218488" xlink:href="#Breaker:开关_0" y="508.6190476190475" zvalue="225"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924495605765" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924495605765"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,627.541,519.73) scale(1.22222,1.11111) translate(-112.987,-50.8619)" width="10" x="621.4303734218488" y="508.6190476190475"/></g>
  <g id="224">
   <use class="v6300" height="20" transform="rotate(0,625.765,734.175) scale(1.22222,1.11111) translate(-112.664,-72.3063)" width="10" x="619.6536436593574" xlink:href="#Breaker:开关_0" y="723.0634920634918" zvalue="242"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924495671301" ObjectName="#1发电机621断路器"/>
   <cge:TPSR_Ref TObjectID="6473924495671301"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,625.765,734.175) scale(1.22222,1.11111) translate(-112.664,-72.3063)" width="10" x="619.6536436593574" y="723.0634920634918"/></g>
  <g id="262">
   <use class="v6300" height="20" transform="rotate(0,970.31,733.266) scale(1.22222,1.11111) translate(-175.309,-72.2154)" width="10" x="964.199098204812" xlink:href="#Breaker:开关_0" y="722.1544011544009" zvalue="269"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924495736837" ObjectName="#1站用变623断路器"/>
   <cge:TPSR_Ref TObjectID="6473924495736837"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,970.31,733.266) scale(1.22222,1.11111) translate(-175.309,-72.2154)" width="10" x="964.199098204812" y="722.1544011544009"/></g>
  <g id="68">
   <use class="v6300" height="20" transform="rotate(0,1485.76,734.175) scale(1.22222,1.11111) translate(-269.028,-72.3063)" width="10" x="1479.653643659357" xlink:href="#Breaker:开关_0" y="723.0634920634918" zvalue="281"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924495802373" ObjectName="#2发电机622断路器"/>
   <cge:TPSR_Ref TObjectID="6473924495802373"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1485.76,734.175) scale(1.22222,1.11111) translate(-269.028,-72.3063)" width="10" x="1479.653643659357" y="723.0634920634918"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="414">
   <g id="4140">
    <use class="kv35" height="60" transform="rotate(0,627.565,433.659) scale(1.46176,1.40023) translate(-189.008,-111.946)" width="40" x="598.33" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="391.65" zvalue="9"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874429759492" ObjectName="35"/>
    </metadata>
   </g>
   <g id="4141">
    <use class="v6300" height="60" transform="rotate(0,627.565,433.659) scale(1.46176,1.40023) translate(-189.008,-111.946)" width="40" x="598.33" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="391.65" zvalue="9"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874429825028" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399447150596" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399447150596"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,627.565,433.659) scale(1.46176,1.40023) translate(-189.008,-111.946)" width="40" x="598.33" y="391.65"/></g>
  <g id="161">
   <g id="1610">
    <use class="kv10" height="60" transform="rotate(0,1481.87,408.603) scale(1.36801,1.31042) translate(-391.278,-87.4804)" width="40" x="1454.51" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="369.29" zvalue="194"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874429890564" ObjectName="10"/>
    </metadata>
   </g>
   <g id="1611">
    <use class="v6300" height="60" transform="rotate(0,1481.87,408.603) scale(1.36801,1.31042) translate(-391.278,-87.4804)" width="40" x="1454.51" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="369.29" zvalue="194"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874429956100" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399447216132" ObjectName="10kV近区变"/>
   <cge:TPSR_Ref TObjectID="6755399447216132"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1481.87,408.603) scale(1.36801,1.31042) translate(-391.278,-87.4804)" width="40" x="1454.51" y="369.29"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="410">
   <path class="v6300" d="M 467.11 614.79 L 1700.11 614.79" stroke-width="6" zvalue="11"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674239512580" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674239512580"/></metadata>
  <path d="M 467.11 614.79 L 1700.11 614.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="212">
   <use class="v6300" height="30" transform="rotate(0,625.846,878.182) scale(1.58467,1.58467) translate(-222.139,-315.239)" width="30" x="602.0761427991616" xlink:href="#Generator:发电机_0" y="854.4117361713461" zvalue="13"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449717469189" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449717469189"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,625.846,878.182) scale(1.58467,1.58467) translate(-222.139,-315.239)" width="30" x="602.0761427991616" y="854.4117361713461"/></g>
  <g id="73">
   <use class="v6300" height="30" transform="rotate(0,1485.85,882.182) scale(1.58467,1.58467) translate(-539.44,-316.715)" width="30" x="1462.076142799162" xlink:href="#Generator:发电机_0" y="858.4117361713461" zvalue="273"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449720418310" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449720418310"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1485.85,882.182) scale(1.58467,1.58467) translate(-539.44,-316.715)" width="30" x="1462.076142799162" y="858.4117361713461"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="399">
   <use class="v6300" height="30" transform="rotate(0,969.872,832.925) scale(1.69643,1.70833) translate(-388.408,-334.734)" width="28" x="946.1219224784917" xlink:href="#EnergyConsumer:站用变DY接地_0" y="807.2996031746031" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449717403653" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,969.872,832.925) scale(1.69643,1.70833) translate(-388.408,-334.734)" width="28" x="946.1219224784917" y="807.2996031746031"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="207">
   <use class="kv35" height="30" transform="rotate(270,699.889,307) scale(-1.11111,1.11111) translate(-1329.23,-29.0333)" width="10" x="694.3333333333334" xlink:href="#GroundDisconnector:配网地刀_0" y="290.3333333333334" zvalue="19"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449717338118" ObjectName="#1主变35kV侧30160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449717338118"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,699.889,307) scale(-1.11111,1.11111) translate(-1329.23,-29.0333)" width="10" x="694.3333333333334" y="290.3333333333334"/></g>
  <g id="125">
   <use class="kv35" height="30" transform="rotate(90,505,194.333) scale(1.11111,1.11111) translate(-49.9444,-17.7667)" width="10" x="499.4444444444445" xlink:href="#GroundDisconnector:配网地刀_0" y="177.6666666666667" zvalue="54"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449716944901" ObjectName="35kV勐小T线PT39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449716944901"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,505,194.333) scale(1.11111,1.11111) translate(-49.9444,-17.7667)" width="10" x="499.4444444444445" y="177.6666666666667"/></g>
  <g id="126">
   <use class="kv35" height="30" transform="rotate(90,503.889,243.444) scale(1.11111,1.11111) translate(-49.8333,-22.6778)" width="10" x="498.3333333333334" xlink:href="#GroundDisconnector:配网地刀_0" y="226.7777777777777" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449716813829" ObjectName="35kV勐小T线PT39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449716813829"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,503.889,243.444) scale(1.11111,1.11111) translate(-49.8333,-22.6778)" width="10" x="498.3333333333334" y="226.7777777777777"/></g>
  <g id="124">
   <use class="v6300" height="30" transform="rotate(270,1029.42,505.361) scale(-1.11111,1.11111) translate(-1955.34,-48.8694)" width="10" x="1023.861111111111" xlink:href="#GroundDisconnector:配网地刀_0" y="488.6944444444445" zvalue="166"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449717731333" ObjectName="6.3kV母线电压互感器69017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449717731333"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1029.42,505.361) scale(-1.11111,1.11111) translate(-1955.34,-48.8694)" width="10" x="1023.861111111111" y="488.6944444444445"/></g>
  <g id="155">
   <use class="v6300" height="30" transform="rotate(270,1541.42,537.361) scale(-1.11111,1.11111) translate(-2928.14,-52.0694)" width="10" x="1535.861111111111" xlink:href="#GroundDisconnector:配网地刀_0" y="520.6944444444445" zvalue="188"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449718255622" ObjectName="10kV近区变6.3kV侧62417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449718255622"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1541.42,537.361) scale(-1.11111,1.11111) translate(-2928.14,-52.0694)" width="10" x="1535.861111111111" y="520.6944444444445"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="206">
   <use class="kv35" height="30" transform="rotate(0,626.348,267.425) scale(-1.11111,-0.814815) translate(-1189.23,-598.405)" width="15" x="618.0149405153977" xlink:href="#Disconnector:刀闸_0" y="255.2023946217127" zvalue="21"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449717207045" ObjectName="#1主变35kV侧3016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449717207045"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,626.348,267.425) scale(-1.11111,-0.814815) translate(-1189.23,-598.405)" width="15" x="618.0149405153977" y="255.2023946217127"/></g>
  <g id="119">
   <use class="kv35" height="30" transform="rotate(0,568.712,220.675) scale(-1.11111,-0.814815) translate(-1079.72,-494.28)" width="15" x="560.3790115379934" xlink:href="#Disconnector:刀闸_0" y="208.4523946217128" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449717075973" ObjectName="35kV勐小T线PT3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449717075973"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,568.712,220.675) scale(-1.11111,-0.814815) translate(-1079.72,-494.28)" width="15" x="560.3790115379934" y="208.4523946217128"/></g>
  <g id="121">
   <use class="v6300" height="30" transform="rotate(0,970.155,551.258) scale(-1.11111,-0.814815) translate(-1842.46,-1230.58)" width="15" x="961.8215440862398" xlink:href="#Disconnector:刀闸_0" y="539.0357279550461" zvalue="162"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449717600262" ObjectName="6.3kV母线电压互感器6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449717600262"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,970.155,551.258) scale(-1.11111,-0.814815) translate(-1842.46,-1230.58)" width="15" x="961.8215440862398" y="539.0357279550461"/></g>
  <g id="135">
   <use class="v6300" height="25" transform="rotate(0,970.125,655.849) scale(-0.37037,-0.977778) translate(-3603.63,-1326.88)" width="45" x="961.7919745009564" xlink:href="#Disconnector:特殊刀闸_0" y="643.6266370459552" zvalue="172"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449717862406" ObjectName="#1站用变6231隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449717862406"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,970.125,655.849) scale(-0.37037,-0.977778) translate(-3603.63,-1326.88)" width="45" x="961.7919745009564" y="643.6266370459552"/></g>
  <g id="167">
   <use class="kv10" height="30" transform="rotate(0,1481.9,258.758) scale(-1.11111,-0.814815) translate(-2814.78,-579.102)" width="15" x="1473.568985265387" xlink:href="#Disconnector:刀闸_0" y="246.5357279550461" zvalue="203"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449718321157" ObjectName="10kV近区变10kV侧0216隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449718321157"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1481.9,258.758) scale(-1.11111,-0.814815) translate(-2814.78,-579.102)" width="15" x="1473.568985265387" y="246.5357279550461"/></g>
  <g id="182">
   <use class="v6300" height="30" transform="rotate(0,1482.57,571.258) scale(-1.11111,-0.814815) translate(-2816.05,-1275.13)" width="15" x="1474.239411054672" xlink:href="#Disconnector:刀闸_0" y="559.035727955046" zvalue="213"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449718517766" ObjectName="10kV近区变6.3kV侧6241隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449718517766"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1482.57,571.258) scale(-1.11111,-0.814815) translate(-2816.05,-1275.13)" width="15" x="1474.239411054672" y="559.035727955046"/></g>
  <g id="195">
   <use class="v6300" height="30" transform="rotate(0,626.573,567.425) scale(-1.11111,-0.814815) translate(-1189.65,-1266.59)" width="15" x="618.2394110546715" xlink:href="#Disconnector:刀闸_0" y="555.2023946217128" zvalue="223"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449718583302" ObjectName="#1主变6.3kV侧6011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449718583302"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,626.573,567.425) scale(-1.11111,-0.814815) translate(-1189.65,-1266.59)" width="15" x="618.2394110546715" y="555.2023946217128"/></g>
  <g id="219">
   <use class="v6300" height="25" transform="rotate(0,626.543,660.147) scale(-0.37037,-0.977778) translate(-2332.38,-1335.57)" width="45" x="618.2098414693882" xlink:href="#Disconnector:特殊刀闸_0" y="647.924616843935" zvalue="238"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449718714374" ObjectName="#1发电机6211隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449718714374"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,626.543,660.147) scale(-0.37037,-0.977778) translate(-2332.38,-1335.57)" width="45" x="618.2098414693882" y="647.924616843935"/></g>
  <g id="237">
   <use class="v6300" height="25" transform="rotate(0,718.25,834.008) scale(-0.37037,-0.977778) translate(-2671.69,-1687.25)" width="45" x="709.9169745009565" xlink:href="#Disconnector:特殊刀闸_0" y="821.785727955046" zvalue="251"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449718976518" ObjectName="#1发电机6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449718976518"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,718.25,834.008) scale(-0.37037,-0.977778) translate(-2671.69,-1687.25)" width="45" x="709.9169745009565" y="821.785727955046"/></g>
  <g id="240">
   <use class="v6300" height="25" transform="rotate(0,488.75,855.758) scale(-0.37037,-0.977778) translate(-1822.54,-1731.24)" width="45" x="480.4169745009563" xlink:href="#Disconnector:特殊刀闸_0" y="843.535727955046" zvalue="256"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449719304198" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449719304198"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,488.75,855.758) scale(-0.37037,-0.977778) translate(-1822.54,-1731.24)" width="45" x="480.4169745009563" y="843.535727955046"/></g>
  <g id="70">
   <use class="v6300" height="25" transform="rotate(0,1485.82,660.147) scale(-0.37037,-0.977778) translate(-5511.71,-1335.57)" width="45" x="1477.488210752907" xlink:href="#Disconnector:特殊刀闸_0" y="647.924616843935" zvalue="277"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449720221702" ObjectName="#2发电机6221隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449720221702"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1485.82,660.147) scale(-0.37037,-0.977778) translate(-5511.71,-1335.57)" width="45" x="1477.488210752907" y="647.924616843935"/></g>
  <g id="27">
   <use class="v6300" height="25" transform="rotate(0,1578.25,834.008) scale(-0.37037,-0.977778) translate(-5853.69,-1687.25)" width="45" x="1569.916974500956" xlink:href="#Disconnector:特殊刀闸_0" y="821.785727955046" zvalue="289"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449719959558" ObjectName="#2发电机6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449719959558"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1578.25,834.008) scale(-0.37037,-0.977778) translate(-5853.69,-1687.25)" width="45" x="1569.916974500956" y="821.785727955046"/></g>
  <g id="25">
   <use class="v6300" height="25" transform="rotate(0,1348.75,855.758) scale(-0.37037,-0.977778) translate(-5004.54,-1731.24)" width="45" x="1340.416974500956" xlink:href="#Disconnector:特殊刀闸_0" y="843.535727955046" zvalue="293"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449719762950" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449719762950"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1348.75,855.758) scale(-0.37037,-0.977778) translate(-5004.54,-1731.24)" width="45" x="1340.416974500956" y="843.535727955046"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="101">
   <path class="kv35" d="M 627.5 341.1 L 627.5 279.24" stroke-width="1" zvalue="42"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="418@0" LinkObjectIDznd="206@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 627.5 341.1 L 627.5 279.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv35" d="M 626.28 255.41 L 626.28 163.17" stroke-width="1" zvalue="43"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@1" LinkObjectIDznd="156@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.28 255.41 L 626.28 163.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv35" d="M 683.97 306.99 L 627.5 306.99" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="101" MaxPinNum="2"/>
   </metadata>
  <path d="M 683.97 306.99 L 627.5 306.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv35" d="M 568.61 260.33 L 568.61 232.49" stroke-width="1" zvalue="51"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="119@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 568.61 260.33 L 568.61 232.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv35" d="M 519.8 243.43 L 568.61 243.43" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="95" MaxPinNum="2"/>
   </metadata>
  <path d="M 519.8 243.43 L 568.61 243.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="v6300" d="M 970.09 539.24 L 970.09 444.13" stroke-width="1" zvalue="163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@1" LinkObjectIDznd="117@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 970.09 539.24 L 970.09 444.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="v6300" d="M 970.06 563.08 L 970.06 614.79" stroke-width="1" zvalue="164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@0" LinkObjectIDznd="410@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 970.06 563.08 L 970.06 614.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="v6300" d="M 1013.5 505.35 L 970.09 505.35" stroke-width="1" zvalue="167"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@0" LinkObjectIDznd="122" MaxPinNum="2"/>
   </metadata>
  <path d="M 1013.5 505.35 L 970.09 505.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="v6300" d="M 931.39 485.35 L 970.09 485.35" stroke-width="1" zvalue="169"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@0" LinkObjectIDznd="122" MaxPinNum="2"/>
   </metadata>
  <path d="M 931.39 485.35 L 970.09 485.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="v6300" d="M 970.11 644.16 L 970.11 614.79" stroke-width="1" zvalue="176"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@1" LinkObjectIDznd="410@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 970.11 644.16 L 970.11 614.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="v6300" d="M 940.14 782.85 L 970.03 782.85" stroke-width="1" zvalue="180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="143@0" LinkObjectIDznd="263" MaxPinNum="2"/>
   </metadata>
  <path d="M 940.14 782.85 L 970.03 782.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv35" d="M 627.62 392.41 L 627.62 362.34" stroke-width="1" zvalue="181"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@0" LinkObjectIDznd="418@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 627.62 392.41 L 627.62 362.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv10" d="M 1481.93 370 L 1481.93 328.34" stroke-width="1" zvalue="204"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1481.93 370 L 1481.93 328.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv10" d="M 1481.8 307.1 L 1481.8 270.58" stroke-width="1" zvalue="205"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="167@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1481.8 307.1 L 1481.8 270.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv10" d="M 1482.69 163.41 L 1482.69 246.74" stroke-width="1" zvalue="207"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="167@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1482.69 163.41 L 1482.69 246.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv10" d="M 1533.47 216.44 L 1482.69 216.44" stroke-width="1" zvalue="209"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="172" MaxPinNum="2"/>
   </metadata>
  <path d="M 1533.47 216.44 L 1482.69 216.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="v6300" d="M 1481.87 447.36 L 1481.87 491.1" stroke-width="1" zvalue="214"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@1" LinkObjectIDznd="163@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1481.87 447.36 L 1481.87 491.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="v6300" d="M 1482.48 583.08 L 1482.48 614.79" stroke-width="1" zvalue="216"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="410@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1482.48 583.08 L 1482.48 614.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="v6300" d="M 1443.39 469.35 L 1481.87 469.35" stroke-width="1" zvalue="217"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@0" LinkObjectIDznd="183" MaxPinNum="2"/>
   </metadata>
  <path d="M 1443.39 469.35 L 1481.87 469.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="v6300" d="M 1482.48 512.34 L 1482.5 559.24" stroke-width="1" zvalue="219"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@1" LinkObjectIDznd="182@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1482.48 512.34 L 1482.5 559.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="v6300" d="M 1525.5 537.35 L 1482.49 537.35" stroke-width="1" zvalue="220"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="189" MaxPinNum="2"/>
   </metadata>
  <path d="M 1525.5 537.35 L 1482.49 537.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="v6300" d="M 627.57 475.07 L 627.5 509.1" stroke-width="1" zvalue="226"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@1" LinkObjectIDznd="199@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 627.57 475.07 L 627.5 509.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="v6300" d="M 627.62 530.34 L 627.62 555.41" stroke-width="1" zvalue="227"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@1" LinkObjectIDznd="195@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 627.62 530.34 L 627.62 555.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="203">
   <path class="v6300" d="M 626.48 579.24 L 626.48 614.79" stroke-width="1" zvalue="228"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="410@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.48 579.24 L 626.48 614.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="v6300" d="M 676.26 489.78 L 627.54 489.78" stroke-width="1" zvalue="231"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="200" MaxPinNum="2"/>
   </metadata>
  <path d="M 676.26 489.78 L 627.54 489.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="v6300" d="M 626.52 648.46 L 626.52 614.79" stroke-width="1" zvalue="243"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@1" LinkObjectIDznd="410@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.52 648.46 L 626.52 614.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="v6300" d="M 626.52 671.64 L 626.52 723.54" stroke-width="1" zvalue="244"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@0" LinkObjectIDznd="224@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.52 671.64 L 626.52 723.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="230">
   <path class="v6300" d="M 625.85 744.79 L 625.85 854.81" stroke-width="1" zvalue="245"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="224@1" LinkObjectIDznd="212@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 625.85 744.79 L 625.85 854.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="v6300" d="M 592.64 765.35 L 625.85 765.35" stroke-width="1" zvalue="249"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@0" LinkObjectIDznd="230" MaxPinNum="2"/>
   </metadata>
  <path d="M 592.64 765.35 L 625.85 765.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="247">
   <path class="v6300" d="M 625.85 779.18 L 718.18 779.18 L 718.23 822.32" stroke-width="1" zvalue="260"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230" LinkObjectIDznd="237@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 625.85 779.18 L 718.18 779.18 L 718.23 822.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="248">
   <path class="v6300" d="M 718.23 845.5 L 718.14 899.01" stroke-width="1" zvalue="261"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@0" LinkObjectIDznd="139@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 718.23 845.5 L 718.14 899.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="v6300" d="M 718.14 735.33 L 718.14 779.18" stroke-width="1" zvalue="263"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="247" MaxPinNum="2"/>
   </metadata>
  <path d="M 718.14 735.33 L 718.14 779.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="v6300" d="M 625.85 780.35 L 488.68 780.35 L 488.73 844.07" stroke-width="1" zvalue="264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230" LinkObjectIDznd="240@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 625.85 780.35 L 488.68 780.35 L 488.73 844.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="252">
   <path class="v6300" d="M 488.73 867.25 L 488.73 910.07" stroke-width="1" zvalue="265"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="240@0" LinkObjectIDznd="244@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 488.73 867.25 L 488.73 910.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="254">
   <path class="v6300" d="M 488.68 782.14 L 488.68 743.06" stroke-width="1" zvalue="267"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="251" LinkObjectIDznd="246@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 488.68 782.14 L 488.68 743.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="263">
   <path class="v6300" d="M 970.03 808.32 L 970.03 743.88" stroke-width="1" zvalue="270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="399@0" LinkObjectIDznd="262@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 970.03 808.32 L 970.03 743.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="264">
   <path class="v6300" d="M 970.27 722.64 L 970.27 667.34" stroke-width="1" zvalue="271"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="262@0" LinkObjectIDznd="135@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 970.27 722.64 L 970.27 667.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="v6300" d="M 1485.8 648.46 L 1485.8 614.79" stroke-width="1" zvalue="283"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@1" LinkObjectIDznd="410@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1485.8 648.46 L 1485.8 614.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="v6300" d="M 1485.8 671.64 L 1485.72 723.54" stroke-width="1" zvalue="284"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="68@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1485.8 671.64 L 1485.72 723.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="v6300" d="M 1485.85 744.79 L 1485.85 858.81" stroke-width="1" zvalue="285"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@1" LinkObjectIDznd="73@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1485.85 744.79 L 1485.85 858.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="v6300" d="M 1452.64 765.35 L 1485.85 765.35" stroke-width="1" zvalue="288"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="65" MaxPinNum="2"/>
   </metadata>
  <path d="M 1452.64 765.35 L 1485.85 765.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="v6300" d="M 1485.85 779.18 L 1578.18 779.18 L 1578.23 822.32" stroke-width="1" zvalue="300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65" LinkObjectIDznd="27@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1485.85 779.18 L 1578.18 779.18 L 1578.23 822.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="v6300" d="M 1578.23 845.5 L 1578.14 899.01" stroke-width="1" zvalue="301"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="27@0" LinkObjectIDznd="71@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1578.23 845.5 L 1578.14 899.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="v6300" d="M 1578.14 735.33 L 1578.14 779.18" stroke-width="1" zvalue="303"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@0" LinkObjectIDznd="17" MaxPinNum="2"/>
   </metadata>
  <path d="M 1578.14 735.33 L 1578.14 779.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="v6300" d="M 1485.85 780.35 L 1348.68 780.35 L 1348.73 844.07" stroke-width="1" zvalue="304"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65" LinkObjectIDznd="25@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1485.85 780.35 L 1348.68 780.35 L 1348.73 844.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="v6300" d="M 1348.73 867.25 L 1348.73 910.07" stroke-width="1" zvalue="305"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="25@0" LinkObjectIDznd="22@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1348.73 867.25 L 1348.73 910.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="v6300" d="M 1348.68 782.14 L 1348.68 743.06" stroke-width="1" zvalue="307"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="13" LinkObjectIDznd="21@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1348.68 782.14 L 1348.68 743.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv35" d="M 568.64 208.66 L 568.64 180.5 L 626.28 180.5" stroke-width="1" zvalue="350"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@1" LinkObjectIDznd="100" MaxPinNum="2"/>
   </metadata>
  <path d="M 568.64 208.66 L 568.64 180.5 L 626.28 180.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="kv35" d="M 520.92 194.32 L 568.64 194.32" stroke-width="1" zvalue="351"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="106" MaxPinNum="2"/>
   </metadata>
  <path d="M 520.92 194.32 L 568.64 194.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="120">
   <use class="kv35" height="30" transform="rotate(0,570.236,282) scale(1.43056,1.55556) translate(-165.166,-92.381)" width="30" x="548.7777777777778" xlink:href="#Accessory:避雷器PT带熔断器_0" y="258.6666666666666" zvalue="49"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449717010437" ObjectName="35kV勐小T线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,570.236,282) scale(1.43056,1.55556) translate(-165.166,-92.381)" width="30" x="548.7777777777778" y="258.6666666666666"/></g>
  <g id="137">
   <use class="v6300" height="30" transform="rotate(0,718.141,720.808) scale(1.32051,1.32051) translate(-169.498,-170.146)" width="30" x="698.3333333333335" xlink:href="#Accessory:RT1122_0" y="701" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449716682757" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,718.141,720.808) scale(1.32051,1.32051) translate(-169.498,-170.146)" width="30" x="698.3333333333335" y="701"/></g>
  <g id="139">
   <use class="v6300" height="30" transform="rotate(0,718.141,913.534) scale(1.32051,-1.32051) translate(-169.498,-1600.53)" width="30" x="698.3333333333335" xlink:href="#Accessory:RT1122_0" y="893.7259090778094" zvalue="68"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449716617222" ObjectName="#1发电机PT3"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,718.141,913.534) scale(1.32051,-1.32051) translate(-169.498,-1600.53)" width="30" x="698.3333333333335" y="893.7259090778094"/></g>
  <g id="117">
   <use class="v6300" height="18" transform="rotate(0,972.224,423) scale(2.52778,-2.52778) translate(-576.149,-576.591)" width="15" x="953.2652557645505" xlink:href="#Accessory:PT8_0" y="400.25" zvalue="160"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449717534726" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,972.224,423) scale(2.52778,-2.52778) translate(-576.149,-576.591)" width="15" x="953.2652557645505" y="400.25"/></g>
  <g id="128">
   <use class="v6300" height="29" transform="rotate(270,915.828,485.564) scale(1.35776,1.35776) translate(-237.736,-122.755)" width="20" x="902.25" xlink:href="#Accessory:消谐装置_0" y="465.8763842739978" zvalue="168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449717796870" ObjectName="6.3kV母线电压互感器消谐"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(270,915.828,485.564) scale(1.35776,1.35776) translate(-237.736,-122.755)" width="20" x="902.25" y="465.8763842739978"/></g>
  <g id="143">
   <use class="v6300" height="29" transform="rotate(270,924.578,783.064) scale(1.35776,1.35776) translate(-240.041,-201.144)" width="20" x="911" xlink:href="#Accessory:消谐装置_0" y="763.3763842739978" zvalue="179"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449718059013" ObjectName="#1站用变消谐"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(270,924.578,783.064) scale(1.35776,1.35776) translate(-240.041,-201.144)" width="20" x="911" y="763.3763842739978"/></g>
  <g id="153">
   <use class="v6300" height="29" transform="rotate(270,1427.83,469.564) scale(1.35776,1.35776) translate(-372.644,-118.539)" width="20" x="1414.25" xlink:href="#Accessory:消谐装置_0" y="449.8763842739978" zvalue="191"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449718124549" ObjectName="10kV近区变6.3kV侧消谐"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(270,1427.83,469.564) scale(1.35776,1.35776) translate(-372.644,-118.539)" width="20" x="1414.25" y="449.8763842739978"/></g>
  <g id="174">
   <use class="kv10" height="20" transform="rotate(270,1547.69,216.438) scale(2.03125,2.03125) translate(-775.437,-99.5712)" width="20" x="1527.375" xlink:href="#Accessory:线路PT3_0" y="196.125" zvalue="208"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449718452230" ObjectName="10kV大坝线避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1547.69,216.438) scale(2.03125,2.03125) translate(-775.437,-99.5712)" width="20" x="1527.375" y="196.125"/></g>
  <g id="205">
   <use class="v6300" height="29" transform="rotate(90,691.828,489.564) scale(1.35776,1.35776) translate(-178.713,-123.809)" width="20" x="678.25" xlink:href="#Accessory:消谐装置_0" y="469.8763842739978" zvalue="230"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449718648838" ObjectName="#1主变消谐"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(90,691.828,489.564) scale(1.35776,1.35776) translate(-178.713,-123.809)" width="20" x="678.25" y="469.8763842739978"/></g>
  <g id="235">
   <use class="v6300" height="29" transform="rotate(270,577.078,765.564) scale(1.35776,1.35776) translate(-148.478,-196.533)" width="20" x="563.5" xlink:href="#Accessory:消谐装置_0" y="745.8763842739978" zvalue="248"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449718910982" ObjectName="#1发电机消谐"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(270,577.078,765.564) scale(1.35776,1.35776) translate(-148.478,-196.533)" width="20" x="563.5" y="745.8763842739978"/></g>
  <g id="244">
   <use class="v6300" height="30" transform="rotate(0,488.529,922.706) scale(0.862745,0.862745) translate(75.6618,144.735)" width="30" x="475.5882352941178" xlink:href="#Accessory:PT789_0" y="909.7647058823529" zvalue="258"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449719369734" ObjectName="#1发电机机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,488.529,922.706) scale(0.862745,0.862745) translate(75.6618,144.735)" width="30" x="475.5882352941178" y="909.7647058823529"/></g>
  <g id="246">
   <use class="v6300" height="26" transform="rotate(0,489.353,728.049) scale(1.22549,-1.22549) translate(-88.688,-1319.21)" width="12" x="482.0000000000001" xlink:href="#Accessory:带电容电阻接地_0" y="712.1176470588235" zvalue="259"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449719435270" ObjectName="#1发电机电阻接地"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,489.353,728.049) scale(1.22549,-1.22549) translate(-88.688,-1319.21)" width="12" x="482.0000000000001" y="712.1176470588235"/></g>
  <g id="72">
   <use class="v6300" height="30" transform="rotate(0,1578.14,720.808) scale(1.32051,1.32051) translate(-378.236,-170.146)" width="30" x="1558.333333333333" xlink:href="#Accessory:RT1122_0" y="701" zvalue="275"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449720352774" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1578.14,720.808) scale(1.32051,1.32051) translate(-378.236,-170.146)" width="30" x="1558.333333333333" y="701"/></g>
  <g id="71">
   <use class="v6300" height="30" transform="rotate(0,1578.14,913.534) scale(1.32051,-1.32051) translate(-378.236,-1600.53)" width="30" x="1558.333333333333" xlink:href="#Accessory:RT1122_0" y="893.7259090778094" zvalue="276"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449720287238" ObjectName="#2发电机PT3"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1578.14,913.534) scale(1.32051,-1.32051) translate(-378.236,-1600.53)" width="30" x="1558.333333333333" y="893.7259090778094"/></g>
  <g id="63">
   <use class="v6300" height="29" transform="rotate(270,1437.08,765.564) scale(1.35776,1.35776) translate(-375.081,-196.533)" width="20" x="1423.5" xlink:href="#Accessory:消谐装置_0" y="745.8763842739978" zvalue="287"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449720025094" ObjectName="#2发电机消谐"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(270,1437.08,765.564) scale(1.35776,1.35776) translate(-375.081,-196.533)" width="20" x="1423.5" y="745.8763842739978"/></g>
  <g id="22">
   <use class="v6300" height="30" transform="rotate(0,1348.53,922.706) scale(0.862745,0.862745) translate(212.48,144.735)" width="30" x="1335.588235294118" xlink:href="#Accessory:PT789_0" y="909.7647058823529" zvalue="297"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449719566342" ObjectName="#2发电机机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1348.53,922.706) scale(0.862745,0.862745) translate(212.48,144.735)" width="30" x="1335.588235294118" y="909.7647058823529"/></g>
  <g id="21">
   <use class="v6300" height="26" transform="rotate(0,1349.35,728.049) scale(1.22549,-1.22549) translate(-246.928,-1319.21)" width="12" x="1342" xlink:href="#Accessory:带电容电阻接地_0" y="712.1176470588235" zvalue="298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449719500806" ObjectName="#2发电机电阻接地"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1349.35,728.049) scale(1.22549,-1.22549) translate(-246.928,-1319.21)" width="12" x="1342" y="712.1176470588235"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="170">
   <use class="kv10" height="30" transform="rotate(0,1482.69,147.351) scale(4.01786,1.08173) translate(-1103.1,-9.90722)" width="7" x="1468.625" xlink:href="#ACLineSegment:线路_0" y="131.1249999999999" zvalue="206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449718386694" ObjectName="10kV大坝线"/>
   <cge:TPSR_Ref TObjectID="6192449718386694_5066549584199682"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1482.69,147.351) scale(4.01786,1.08173) translate(-1103.1,-9.90722)" width="7" x="1468.625" y="131.1249999999999"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="222">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="222" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124808032261" ObjectName="GEN_PSUM"/>
   </metadata>
  </g>
  <g id="221">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="221" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124808097797" ObjectName="GEN_QSUM"/>
   </metadata>
  </g>
  <g id="77">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="77" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124807901189" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="76">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124807966725" ObjectName="LOAD_QSUM"/>
   </metadata>
  </g>
  <g id="74">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124808294405" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="160">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="160" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124808491013" ObjectName="雨量采集"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="140" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124807901189" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="136" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124807901189" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,403.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="408.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124808359941" ObjectName="坝下水位"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124798332936" ObjectName="F"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,399.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="404.3" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="入库流量"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="103" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,626.28,42.0097) scale(1,1) translate(0,-5.66214e-14)" writing-mode="lr" x="625.8099999999999" xml:space="preserve" y="46.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124795645957" ObjectName="P"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="104" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,626.28,63.4375) scale(1,1) translate(0,-9.46847e-14)" writing-mode="lr" x="625.8099999999999" xml:space="preserve" y="68.09999999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124795711493" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="105" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,626.28,84.8653) scale(1,1) translate(0,-1.32748e-13)" writing-mode="lr" x="625.8099999999999" xml:space="preserve" y="89.53" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124795777029" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="108">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="108" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,560.065,333.515) scale(1,1) translate(0,0)" writing-mode="lr" x="559.51" xml:space="preserve" y="338.19" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124798464005" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="109">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="109" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1405.87,289.834) scale(1,1) translate(0,6.13488e-14)" writing-mode="lr" x="1405.32" xml:space="preserve" y="294.55" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124801019909" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="115" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,560.065,357.011) scale(1,1) translate(0,0)" writing-mode="lr" x="559.51" xml:space="preserve" y="361.69" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124798529541" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="116" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1405.87,318.469) scale(1,1) translate(0,6.77071e-14)" writing-mode="lr" x="1405.32" xml:space="preserve" y="323.18" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124801085445" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="130">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="130" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,558.815,501.145) scale(1,1) translate(0,-1.08809e-13)" writing-mode="lr" x="558.26" xml:space="preserve" y="505.82" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124798595077" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="131">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="131" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1413.12,504.686) scale(1,1) translate(0,5.45277e-14)" writing-mode="lr" x="1412.57" xml:space="preserve" y="509.4" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124801150981" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="132">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="132" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,558.815,524.641) scale(1,1) translate(0,0)" writing-mode="lr" x="558.26" xml:space="preserve" y="529.3200000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124798660613" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="138" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1413.12,533.321) scale(1,1) translate(0,0)" writing-mode="lr" x="1412.57" xml:space="preserve" y="538.04" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124801216517" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="142" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,560.065,380.508) scale(1,1) translate(0,0)" writing-mode="lr" x="559.51" xml:space="preserve" y="385.18" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124798726149" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="146">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="146" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1405.87,347.105) scale(1,1) translate(0,7.40654e-14)" writing-mode="lr" x="1405.32" xml:space="preserve" y="351.82" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124801282053" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="147">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="147" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,558.815,548.137) scale(1,1) translate(0,0)" writing-mode="lr" x="558.26" xml:space="preserve" y="552.8099999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124799053829" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="148" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1413.12,561.956) scale(1,1) translate(0,-1.21772e-13)" writing-mode="lr" x="1412.57" xml:space="preserve" y="566.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124801609733" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="152">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="152" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,628.346,946.397) scale(1,1) translate(0,-8.28406e-13)" writing-mode="lr" x="627.79" xml:space="preserve" y="951.11" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124797284357" ObjectName="P"/>
   </metadata>
  </g>
  <g id="154">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="154" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1488.35,950.397) scale(1,1) translate(0,-8.31958e-13)" writing-mode="lr" x="1487.79" xml:space="preserve" y="955.11" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124807245829" ObjectName="P"/>
   </metadata>
  </g>
  <g id="157">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="157" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,628.346,975.351) scale(1,1) translate(0,-8.54122e-13)" writing-mode="lr" x="627.79" xml:space="preserve" y="980.0700000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124797349893" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="158">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="158" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1488.35,979.351) scale(1,1) translate(0,-8.57675e-13)" writing-mode="lr" x="1487.79" xml:space="preserve" y="984.0700000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124807311365" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="159">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="159" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,628.346,1004.31) scale(1,1) translate(0,-8.79839e-13)" writing-mode="lr" x="627.79" xml:space="preserve" y="1009.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124797415429" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="162">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="162" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1488.35,1008.31) scale(1,1) translate(0,-8.83392e-13)" writing-mode="lr" x="1487.79" xml:space="preserve" y="1013.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124807376901" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="36">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="36" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,988.107,275.286) scale(1,1) translate(0,-2.2896e-13)" writing-mode="lr" x="987.64" xml:space="preserve" y="280.06" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124797939717" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="37" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,988.107,312.286) scale(1,1) translate(0,0)" writing-mode="lr" x="987.64" xml:space="preserve" y="317.06" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124798005253" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="38" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,988.107,349.286) scale(1,1) translate(0,0)" writing-mode="lr" x="987.64" xml:space="preserve" y="354.06" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124798070789" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="39" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,518.107,636.786) scale(1,1) translate(0,0)" writing-mode="lr" x="517.64" xml:space="preserve" y="641.5599999999999" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124798201861" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="218">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="326"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374887354371" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="215">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="327"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950358433800" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
</svg>