<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549584789506" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="4.416666666666668" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.249999999999998" y2="20.16666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:四卷PT_0" viewBox="0,0,18,18">
   <use terminal-index="0" type="0" x="9" xlink:href="#terminal" y="0.2666666666666639"/>
   <ellipse cx="9.25" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.25" cy="9.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.08" cy="12.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.82" cy="9.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="11.75" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.25" x2="9.25" y1="7.500000000000001" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="15.25" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="12.31481481481481" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.94444444444444" x2="13.62962962962963" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14.94444444444444" y1="9.966124661246614" y2="9.966124661246614"/>
  </symbol>
  <symbol id="Breaker:小车开关带避雷器_0" viewBox="0,0,20,26">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="10" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982326301579349" x2="9.982326301579349" y1="18.5" y2="24.08333333333334"/>
   <rect fill-opacity="0" height="12.42" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.08,12.13) scale(1,1) translate(0,0)" width="6.67" x="6.75" y="5.92"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.06661748931881" x2="10.06661748931881" y1="2.083333333333329" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.75" x2="9.916666666666666" y1="21.25" y2="21.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.7" x2="17.7" y1="12.75" y2="21.08333333333334"/>
   <path d="M 13.6321 23.6556 L 9.98274 25.9797 L 6.33333 23.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 2.40358 L 10.0648 0.181096 L 6.45119 2.40358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.6321 21.8223 L 9.98274 24.1463 L 6.33333 21.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 4.15358 L 10.0648 1.9311 L 6.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438356" x2="17.20890410958904" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.44863013698632" x2="17.92808219178084" y1="8.036096718478305" y2="8.036096718478305"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438357" x2="18.1678082191781" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.9691780821918" x2="18.40753424657536" y1="8.42469775474782" y2="8.42469775474782"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.48972602739728" x2="18.88698630136988" y1="8.813298791017345" y2="8.813298791017345"/>
   <rect fill-opacity="0" height="5.57" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,17.71,12.73) scale(1,-1) translate(0,-1045.27)" width="2.42" x="16.5" y="9.949999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438358" x2="17.68835616438358" y1="9.979101899825864" y2="8.813298791017306"/>
  </symbol>
  <symbol id="Breaker:小车开关带避雷器_1" viewBox="0,0,20,26">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="10" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.75" x2="9.916666666666666" y1="21.25" y2="21.25"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="12.42" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.08,12.13) scale(1,1) translate(0,0)" width="6.67" x="6.75" y="5.92"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.7" x2="17.7" y1="12.75" y2="21.08333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.06661748931881" x2="10.06661748931881" y1="0.3333333333333321" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982326301579349" x2="9.982326301579349" y1="18.5" y2="26.08333333333333"/>
   <path d="M 13.6321 23.6556 L 9.98274 25.9797 L 6.33333 23.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 2.40358 L 10.0648 0.181096 L 6.45119 2.40358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.9691780821918" x2="18.40753424657536" y1="8.42469775474782" y2="8.42469775474782"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.48972602739728" x2="18.88698630136988" y1="8.813298791017345" y2="8.813298791017345"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438357" x2="18.1678082191781" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.44863013698632" x2="17.92808219178084" y1="8.036096718478305" y2="8.036096718478305"/>
   <rect fill-opacity="0" height="5.57" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,17.71,12.73) scale(1,-1) translate(0,-1045.27)" width="2.42" x="16.5" y="9.949999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438356" x2="17.20890410958904" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438358" x2="17.68835616438358" y1="9.979101899825864" y2="8.813298791017306"/>
  </symbol>
  <symbol id="Breaker:小车开关带避雷器_2" viewBox="0,0,20,26">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="10" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333334" x2="13.08333333333333" y1="6.083333333333334" y2="18.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="7" y1="6.166666666666669" y2="18"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.7" x2="17.7" y1="12.75" y2="21.08333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.75" x2="9.916666666666666" y1="21.25" y2="21.25"/>
   <rect fill-opacity="0" height="12.42" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.08,12.13) scale(1,1) translate(0,0)" width="6.67" x="6.75" y="5.92"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.06661748931881" x2="10.06661748931881" y1="2.083333333333329" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982326301579349" x2="9.982326301579349" y1="18.5" y2="24.08333333333334"/>
   <path d="M 13.6321 23.6556 L 9.98274 25.9797 L 6.33333 23.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 2.40358 L 10.0648 0.181096 L 6.45119 2.40358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.6321 21.8223 L 9.98274 24.1463 L 6.33333 21.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 4.15358 L 10.0648 1.9311 L 6.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.9691780821918" x2="18.40753424657536" y1="8.42469775474782" y2="8.42469775474782"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.48972602739728" x2="18.88698630136988" y1="8.813298791017345" y2="8.813298791017345"/>
   <rect fill-opacity="0" height="5.57" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,17.71,12.73) scale(1,-1) translate(0,-1045.27)" width="2.42" x="16.5" y="9.949999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.44863013698632" x2="17.92808219178084" y1="8.036096718478305" y2="8.036096718478305"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438357" x2="18.1678082191781" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438356" x2="17.20890410958904" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438358" x2="17.68835616438358" y1="9.979101899825864" y2="8.813298791017306"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1754.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2334.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="DollyBreaker:小车刀闸（规范制图）_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="10"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.983333333333333" x2="0.3833333333333329" y1="1.75766871165643" y2="9.17050102249488"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.983333333333335" x2="9.583333333333334" y1="1.75766871165643" y2="9.17050102249488"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.954562617091801" x2="0.3833333333333355" y1="9.231614386179753" y2="16.58333333333333"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.975776048421957" x2="9.468333333333348" y1="9.231614386179757" y2="16.58333333333333"/>
  </symbol>
  <symbol id="DollyBreaker:小车刀闸（规范制图）_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="10"/>
  </symbol>
  <symbol id=":风力发电1_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="6.600000000000001"/>
   <path d="M 15.3143 15.2093 A 3.15741 3.175 -180 0 0 9.00048 15.3207" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.34 15.3 A 3.055 3.25 0 0 0 21.45 15.3" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.3" cy="15.3" fill-opacity="0" rx="8.800000000000001" ry="8.800000000000001" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT1515_0" viewBox="0,0,15,23">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="0.2666666666666639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="3.916666666666664" y2="0.2499999999999982"/>
   <ellipse cx="7.67" cy="16.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.57" cy="8.85" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.583333333333334" x2="7.583333333333334" y1="14.63888888888889" y2="16.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333334" x2="7.583333333333334" y1="19.25" y2="16.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333334" x2="7.583333333333334" y1="14.63888888888889" y2="16.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.749999999999996" x2="9.694444444444443" y1="9.549457994579948" y2="9.549457994579948"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.750000000000003" x2="7.064814814814818" y1="9.549457994579944" y2="7.083333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.694444444444443" x2="8.379629629629626" y1="9.549457994579944" y2="7.083333333333332"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="Accessory:母线电压互感器11_0" viewBox="0,0,45,48">
   <use terminal-index="0" type="0" x="25.25" xlink:href="#terminal" y="47.78457520218115"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.5,30.95) scale(1,-1) translate(0,-1334.05)" width="6" x="10.5" y="23.95"/>
   <path d="M 8.64167 11.4096 L 4.64167 11.4096 L 6.50709 7.52624 L 7.67376 9.52624 L 8.64167 11.4096" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="13.5" y1="19.78457520218114" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.25" x2="25.25" y1="40.5" y2="47.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="37.25" x2="13.58333333333333" y1="40.5" y2="40.5"/>
   <ellipse cx="13.56" cy="5.42" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799663" x2="13.56620079799663" y1="3.155388266900378" y2="5.82957386674455"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799661" x2="10.88240159599323" y1="5.829573866744575" y2="7.166666666666657"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799662" x2="16.25" y1="5.829573866744575" y2="7.166666666666657"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,36.96,25.94) scale(1,-1) translate(0,-1133.72)" width="7.58" x="33.17" y="17.87"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="34.01790853551448" y2="24.11790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="40.43457520218114" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.7861111111111" x2="39.3" y1="11.60124186884782" y2="11.60124186884782"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.82777777777777" x2="39.93888888888888" y1="12.98282081621623" y2="12.98282081621623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.55" x2="41.21666666666666" y1="14.36439976358464" y2="14.36439976358464"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="34.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="18.01790853551448" y2="14.41790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="40.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <ellipse cx="13.59" cy="15.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.51" cy="10.09" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.67" cy="10.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490864" x2="10.91228003290526" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="13.59607923490865" y1="12.74178419246191" y2="15.41596979230608"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="23.19654510357869" y1="10.49930312563944" y2="11.83639592556152"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="20.51274590157532" y1="7.825117525795246" y2="10.49930312563942"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="16.27987843691203" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5127459015753" x2="17.82894669957193" y1="10.49930312563944" y2="11.83639592556152"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id="ACLineSegment:线路带壁雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="14.875" xlink:href="#terminal" y="39.83880854456296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.25" x2="6.25" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="5.75" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.25" x2="7.25" y1="32.25" y2="32.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="8.25" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="6.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="4.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.91022336769755" x2="14.91022336769755" y1="39.75" y2="0.8333333333333321"/>
   <path d="M 14.75 9.25 L 5.75 9.25 L 5.75 21.25 L 5.75 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.79,21.17) scale(1,1) translate(0,0)" width="6.08" x="2.75" y="14"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV万马河三级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="286" x="57.67" xlink:href="logo.png" y="48.33"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,200.667,78.3333) scale(1,1) translate(0,0)" writing-mode="lr" x="200.67" xml:space="preserve" y="81.83" zvalue="156"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,200.167,78.0237) scale(1,1) translate(0,0)" writing-mode="lr" x="200.17" xml:space="preserve" y="87.02" zvalue="157">110kV万马河三级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="140" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="182"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="182">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,966.623,223.448) scale(1,1) translate(0,0)" writing-mode="lr" x="966.62" xml:space="preserve" y="227.95" zvalue="3">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,930.956,288.448) scale(1,1) translate(0,0)" writing-mode="lr" x="930.96" xml:space="preserve" y="292.95" zvalue="5">121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,883.401,407.893) scale(1,1) translate(0,0)" writing-mode="lr" x="883.4" xml:space="preserve" y="412.39" zvalue="7">1010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1036.7,391.476) scale(1,1) translate(0,0)" writing-mode="lr" x="1036.69696295865" xml:space="preserve" y="395.9761904761906" zvalue="11">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1009.68,277.893) scale(1,1) translate(0,0)" writing-mode="lr" x="1009.68" xml:space="preserve" y="282.39" zvalue="14">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,955.179,95.3929) scale(1,1) translate(0,0)" writing-mode="lr" x="955.1799999999999" xml:space="preserve" y="99.89" zvalue="21">万三二线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1096.68,215.893) scale(1,1) translate(0,0)" writing-mode="lr" x="1096.68" xml:space="preserve" y="220.39" zvalue="25">97</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1046.18,144.782) scale(1,1) translate(0,0)" writing-mode="lr" x="1046.18" xml:space="preserve" y="149.28" zvalue="27">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,924.763,514.893) scale(1,1) translate(1.0115e-13,0)" writing-mode="lr" x="924.76" xml:space="preserve" y="519.39" zvalue="33">601</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,693.929,555.643) scale(1,1) translate(0,1.21046e-13)" writing-mode="lr" x="693.9299999999999" xml:space="preserve" y="560.14" zvalue="34">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,824.695,637.993) scale(1,1) translate(0,0)" writing-mode="lr" x="824.7" xml:space="preserve" y="642.49" zvalue="38">621</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1393.55,648.993) scale(1,1) translate(0,0)" writing-mode="lr" x="1393.55" xml:space="preserve" y="653.49" zvalue="42">623</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1498.05,648.993) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.05" xml:space="preserve" y="653.49" zvalue="46">624</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1375.68,771.893) scale(1,1) translate(0,0)" writing-mode="lr" x="1375.68" xml:space="preserve" y="776.39" zvalue="49">1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1473.68,769.893) scale(1,1) translate(0,0)" writing-mode="lr" x="1473.68" xml:space="preserve" y="774.39" zvalue="53">2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,908.179,733.893) scale(1,1) translate(0,0)" writing-mode="lr" x="908.1799999999999" xml:space="preserve" y="738.39" zvalue="59">6912</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,794.971,837.706) scale(1,1) translate(1.67737e-13,-1.4597e-12)" writing-mode="lr" x="794.9706959706959" xml:space="preserve" y="842.2060439560439" zvalue="61">#1发电机  7MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,751.942,730.893) scale(1,1) translate(0,0)" writing-mode="lr" x="751.9400000000001" xml:space="preserve" y="735.39" zvalue="65">6911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1168.7,637.993) scale(1,1) translate(0,0)" writing-mode="lr" x="1168.7" xml:space="preserve" y="642.49" zvalue="76">622</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1252.18,733.893) scale(1,1) translate(0,0)" writing-mode="lr" x="1252.18" xml:space="preserve" y="738.39" zvalue="79">6922</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1142.76,833.937) scale(1,1) translate(0,0)" writing-mode="lr" x="1142.763003663003" xml:space="preserve" y="838.4368131868131" zvalue="82">#2发电机 7MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1095.94,730.893) scale(1,1) translate(0,0)" writing-mode="lr" x="1095.94" xml:space="preserve" y="735.39" zvalue="86">6921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1356.18,549.893) scale(1,1) translate(0,0)" writing-mode="lr" x="1356.18" xml:space="preserve" y="554.39" zvalue="97">6901</text>
  <line fill="none" id="165" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="391.6666666666667" x2="391.6666666666667" y1="16.33333333333343" y2="1046.333333333333" zvalue="158"/>
  <line fill="none" id="162" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6666666666672" x2="384.6666666666667" y1="152.2038259474159" y2="152.2038259474159" zvalue="160"/>
  <line fill="none" id="161" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6666666666672" x2="379.6666666666667" y1="498.2038259474159" y2="498.2038259474159" zvalue="161"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12.66666666666697" x2="102.666666666667" y1="937.3333333333335" y2="937.3333333333335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12.66666666666697" x2="102.666666666667" y1="976.4966333333334" y2="976.4966333333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12.66666666666697" x2="12.66666666666697" y1="937.3333333333335" y2="976.4966333333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="102.666666666667" y1="937.3333333333335" y2="976.4966333333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="372.666666666667" y1="937.3333333333335" y2="937.3333333333335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="372.666666666667" y1="976.4966333333334" y2="976.4966333333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="102.666666666667" y1="937.3333333333335" y2="976.4966333333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372.666666666667" x2="372.666666666667" y1="937.3333333333335" y2="976.4966333333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12.66666666666697" x2="102.666666666667" y1="976.4966033333335" y2="976.4966033333335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12.66666666666697" x2="102.666666666667" y1="1004.415003333334" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12.66666666666697" x2="12.66666666666697" y1="976.4966033333335" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="102.666666666667" y1="976.4966033333335" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="192.666666666667" y1="976.4966033333335" y2="976.4966033333335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="192.666666666667" y1="1004.415003333334" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="102.666666666667" y1="976.4966033333335" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.666666666667" x2="192.666666666667" y1="976.4966033333335" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.6666666666671" x2="282.6666666666671" y1="976.4966033333335" y2="976.4966033333335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.6666666666671" x2="282.6666666666671" y1="1004.415003333334" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.6666666666671" x2="192.6666666666671" y1="976.4966033333335" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.6666666666671" x2="282.6666666666671" y1="976.4966033333335" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.666666666667" x2="372.666666666667" y1="976.4966033333335" y2="976.4966033333335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.666666666667" x2="372.666666666667" y1="1004.415003333334" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.666666666667" x2="282.666666666667" y1="976.4966033333335" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372.666666666667" x2="372.666666666667" y1="976.4966033333335" y2="1004.415003333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12.66666666666697" x2="102.666666666667" y1="1004.414933333333" y2="1004.414933333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12.66666666666697" x2="102.666666666667" y1="1032.333333333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12.66666666666697" x2="12.66666666666697" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="102.666666666667" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="192.666666666667" y1="1004.414933333333" y2="1004.414933333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="192.666666666667" y1="1032.333333333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102.666666666667" x2="102.666666666667" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.666666666667" x2="192.666666666667" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.6666666666671" x2="282.6666666666671" y1="1004.414933333333" y2="1004.414933333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.6666666666671" x2="282.6666666666671" y1="1032.333333333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.6666666666671" x2="192.6666666666671" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.6666666666671" x2="282.6666666666671" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.666666666667" x2="372.666666666667" y1="1004.414933333333" y2="1004.414933333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.666666666667" x2="372.666666666667" y1="1032.333333333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.666666666667" x2="282.666666666667" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372.666666666667" x2="372.666666666667" y1="1004.414933333333" y2="1032.333333333333"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.6667,957.333) scale(1,1) translate(0,0)" writing-mode="lr" x="57.67" xml:space="preserve" y="963.33" zvalue="163">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.6667,991.333) scale(1,1) translate(0,0)" writing-mode="lr" x="54.67" xml:space="preserve" y="997.33" zvalue="164">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236.667,991.333) scale(1,1) translate(0,0)" writing-mode="lr" x="236.67" xml:space="preserve" y="997.33" zvalue="165">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.6667,1019.33) scale(1,1) translate(0,0)" writing-mode="lr" x="53.67" xml:space="preserve" y="1025.33" zvalue="166">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.667,1019.33) scale(1,1) translate(0,0)" writing-mode="lr" x="235.67" xml:space="preserve" y="1025.33" zvalue="167">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.1667,571.833) scale(1,1) translate(0,0)" writing-mode="lr" x="78.16666666666697" xml:space="preserve" y="576.3333333333335" zvalue="169">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.721,959.333) scale(1,1) translate(0,0)" writing-mode="lr" x="237.72" xml:space="preserve" y="965.33" zvalue="170">WangmaHe3-01-2011</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="367.5" y2="390.25"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="150" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="172">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="173">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="148" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,64.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="22" xml:space="preserve" y="266.5" zvalue="174">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="175">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="176">6.3kV母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="183">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="184">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,309.75) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="314.25" zvalue="188">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="190">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,149.667,1019.33) scale(1,1) translate(0,0)" writing-mode="lr" x="149.67" xml:space="preserve" y="1025.33" zvalue="197">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1009.68,217.893) scale(1,1) translate(0,0)" writing-mode="lr" x="1009.68" xml:space="preserve" y="222.39" zvalue="202">90</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="182"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="186">
   <use class="kv110" height="30" transform="rotate(0,955.179,224.448) scale(1.11111,0.814815) translate(-94.6845,48.2332)" width="15" x="946.8452482677645" xlink:href="#Disconnector:刀闸_0" y="212.2261904761906" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449755676678" ObjectName="#1主变110kV侧1216隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449755676678"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,955.179,224.448) scale(1.11111,0.814815) translate(-94.6845,48.2332)" width="15" x="946.8452482677645" y="212.2261904761906"/></g>
  <g id="18">
   <use class="kv110" height="30" transform="rotate(90,1045.18,167.448) scale(1.11111,-0.814815) translate(-103.685,-375.731)" width="15" x="1036.845248267764" xlink:href="#Disconnector:刀闸_0" y="155.2261904761906" zvalue="26"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449755938822" ObjectName="#1主变110kV侧1219隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449755938822"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1045.18,167.448) scale(1.11111,-0.814815) translate(-103.685,-375.731)" width="15" x="1036.845248267764" y="155.2261904761906"/></g>
 </g>
 <g id="BreakerClass">
  <g id="185">
   <use class="kv110" height="20" transform="rotate(0,955.179,288.448) scale(1.22222,1.11111) translate(-172.558,-27.7337)" width="10" x="949.067470503232" xlink:href="#Breaker:开关_0" y="277.3373015873017" zvalue="4"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924500914181" ObjectName="#1主变110kV侧121断路器"/>
   <cge:TPSR_Ref TObjectID="6473924500914181"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,955.179,288.448) scale(1.22222,1.11111) translate(-172.558,-27.7337)" width="10" x="949.067470503232" y="277.3373015873017"/></g>
  <g id="26">
   <use class="v6300" height="26" transform="rotate(0,955.197,514.993) scale(1.275,1.7) translate(-203.273,-202.956)" width="20" x="942.4469629586504" xlink:href="#Breaker:小车开关带避雷器_0" y="492.8928571428571" zvalue="32"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924500979717" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924500979717"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,955.197,514.993) scale(1.275,1.7) translate(-203.273,-202.956)" width="20" x="942.4469629586504" y="492.8928571428571"/></g>
  <g id="32">
   <use class="v6300" height="26" transform="rotate(0,799.945,638.993) scale(1.275,1.7) translate(-169.787,-254.015)" width="20" x="787.195238095238" xlink:href="#Breaker:小车开关带避雷器_0" y="616.8928571428571" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924501045253" ObjectName="#1发电机621断路器"/>
   <cge:TPSR_Ref TObjectID="6473924501045253"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,799.945,638.993) scale(1.275,1.7) translate(-169.787,-254.015)" width="20" x="787.195238095238" y="616.8928571428571"/></g>
  <g id="34">
   <use class="v6300" height="26" transform="rotate(0,1368.3,649.993) scale(1.275,1.7) translate(-292.374,-258.544)" width="20" x="1355.553255668" xlink:href="#Breaker:小车开关带避雷器_0" y="627.8928571428571" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924501110789" ObjectName="1号站用变623断路器"/>
   <cge:TPSR_Ref TObjectID="6473924501110789"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1368.3,649.993) scale(1.275,1.7) translate(-292.374,-258.544)" width="20" x="1355.553255668" y="627.8928571428571"/></g>
  <g id="36">
   <use class="v6300" height="26" transform="rotate(0,1472.3,649.993) scale(1.275,1.7) translate(-314.806,-258.544)" width="20" x="1459.553255668" xlink:href="#Breaker:小车开关带避雷器_0" y="627.8928571428571" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924501176325" ObjectName="2号站用变624断路器"/>
   <cge:TPSR_Ref TObjectID="6473924501176325"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1472.3,649.993) scale(1.275,1.7) translate(-314.806,-258.544)" width="20" x="1459.553255668" y="627.8928571428571"/></g>
  <g id="90">
   <use class="v6300" height="26" transform="rotate(0,1143.95,638.993) scale(1.275,1.7) translate(-243.983,-254.015)" width="20" x="1131.195238095238" xlink:href="#Breaker:小车开关带避雷器_0" y="616.8928571428571" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924501241861" ObjectName="#2发电机622断路器"/>
   <cge:TPSR_Ref TObjectID="6473924501241861"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1143.95,638.993) scale(1.275,1.7) translate(-243.983,-254.015)" width="20" x="1131.195238095238" y="616.8928571428571"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="181">
   <use class="kv110" height="40" transform="rotate(0,839.623,408.893) scale(1.11111,-1.11111) translate(-81.7401,-774.674)" width="40" x="817.4007936507937" xlink:href="#GroundDisconnector:中性点地刀12_0" y="386.6706349206349" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449755611142" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449755611142"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,839.623,408.893) scale(1.11111,-1.11111) translate(-81.7401,-774.674)" width="40" x="817.4007936507937" y="386.6706349206349"/></g>
  <g id="202">
   <use class="kv110" height="30" transform="rotate(90,1005.18,257.893) scale(1,-1) translate(0,-515.786)" width="12" x="999.1785714285713" xlink:href="#GroundDisconnector:地刀12_0" y="242.8928571428571" zvalue="13"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449755480070" ObjectName="#1主变110kV侧12167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449755480070"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1005.18,257.893) scale(1,-1) translate(0,-515.786)" width="12" x="999.1785714285713" y="242.8928571428571"/></g>
  <g id="17">
   <use class="kv110" height="30" transform="rotate(180,1080.68,216.893) scale(1.08333,-1) translate(-82.6291,-433.786)" width="12" x="1074.178571428571" xlink:href="#GroundDisconnector:地刀12_0" y="201.8928571428571" zvalue="24"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449755873286" ObjectName="#1主变110kV侧12197接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449755873286"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1080.68,216.893) scale(1.08333,-1) translate(-82.6291,-433.786)" width="12" x="1074.178571428571" y="201.8928571428571"/></g>
  <g id="102">
   <use class="kv110" height="30" transform="rotate(180,993.679,217.893) scale(1.08333,-1) translate(-75.9368,-435.786)" width="12" x="987.1785714285713" xlink:href="#GroundDisconnector:地刀12_0" y="202.8928571428571" zvalue="201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449755348998" ObjectName="#1主变110kV侧12190接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449755348998"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,993.679,217.893) scale(1.08333,-1) translate(-75.9368,-435.786)" width="12" x="987.1785714285713" y="202.8928571428571"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="11">
   <path class="kv110" d="M 955.25 236.46 L 955.25 277.82" stroke-width="1" zvalue="8"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@1" LinkObjectIDznd="185@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 955.25 236.46 L 955.25 277.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv110" d="M 955.23 368.95 L 824.73 368.95 L 824.73 395.34" stroke-width="1" zvalue="9"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@2" LinkObjectIDznd="181@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 955.23 368.95 L 824.73 368.95 L 824.73 395.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="kv110" d="M 955.26 299.06 L 955.26 341.11" stroke-width="1" zvalue="12"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@1" LinkObjectIDznd="163@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 955.26 299.06 L 955.26 341.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv110" d="M 990.85 257.88 L 955.25 257.88" stroke-width="1" zvalue="19"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="11" MaxPinNum="2"/>
   </metadata>
  <path d="M 990.85 257.88 L 955.25 257.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv110" d="M 955.28 212.63 L 955.28 148.73" stroke-width="1" zvalue="22"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="13@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 955.28 212.63 L 955.28 148.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv110" d="M 1033.36 167.55 L 955.28 167.55" stroke-width="1" zvalue="28"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@0" LinkObjectIDznd="16" MaxPinNum="2"/>
   </metadata>
  <path d="M 1033.36 167.55 L 955.28 167.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv110" d="M 1122.18 219.16 L 1122.18 167.52 L 1057.19 167.52" stroke-width="1" zvalue="29"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="19@0" LinkObjectIDznd="18@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1122.18 219.16 L 1122.18 167.52 L 1057.19 167.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv110" d="M 1080.69 202.57 L 1080.69 167.52" stroke-width="1" zvalue="31"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="17@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.69 202.57 L 1080.69 167.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="v6300" d="M 955.2 537.09 L 955.2 586.89" stroke-width="1" zvalue="34"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@1" LinkObjectIDznd="28@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 955.2 537.09 L 955.2 586.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="v6300" d="M 955.2 492.89 L 955.2 437.7" stroke-width="1" zvalue="35"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@0" LinkObjectIDznd="163@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 955.2 492.89 L 955.2 437.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="v6300" d="M 1368.3 703.21 L 1368.3 672.09" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="34@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1368.3 703.21 L 1368.3 672.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="v6300" d="M 1368.3 627.89 L 1368.3 586.89" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@0" LinkObjectIDznd="28@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1368.3 627.89 L 1368.3 586.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="v6300" d="M 867.41 771.03 L 867.41 733.89" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="54@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 867.41 771.03 L 867.41 733.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="v6300" d="M 719.94 753.38 L 719.94 734.89" stroke-width="1" zvalue="67"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@0" LinkObjectIDznd="61@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 719.94 753.38 L 719.94 734.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="v6300" d="M 800.85 774.11 L 800.85 661.09" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="32@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 800.85 774.11 L 800.85 661.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="v6300" d="M 869.18 724.89 L 869.18 696.89 L 800.85 696.89" stroke-width="1" zvalue="71"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@0" LinkObjectIDznd="66" MaxPinNum="2"/>
   </metadata>
  <path d="M 869.18 724.89 L 869.18 696.89 L 800.85 696.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="v6300" d="M 647.43 740.06 L 647.43 695.75 L 800.85 695.75" stroke-width="1" zvalue="72"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="66" MaxPinNum="2"/>
   </metadata>
  <path d="M 647.43 740.06 L 647.43 695.75 L 800.85 695.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="v6300" d="M 719.94 725.89 L 719.94 695.75" stroke-width="1" zvalue="73"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="70" MaxPinNum="2"/>
   </metadata>
  <path d="M 719.94 725.89 L 719.94 695.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="v6300" d="M 1211.41 771.03 L 1211.41 733.89" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="88@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1211.41 771.03 L 1211.41 733.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="v6300" d="M 1063.94 753.38 L 1063.94 734.89" stroke-width="1" zvalue="88"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="84@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.94 753.38 L 1063.94 734.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="v6300" d="M 1143.95 768.11 L 1143.95 661.09" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@0" LinkObjectIDznd="90@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1143.95 768.11 L 1143.95 661.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="v6300" d="M 1213.18 724.89 L 1213.18 696.89 L 1143.95 696.89" stroke-width="1" zvalue="91"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@0" LinkObjectIDznd="80" MaxPinNum="2"/>
   </metadata>
  <path d="M 1213.18 724.89 L 1213.18 696.89 L 1143.95 696.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="v6300" d="M 991.43 740.06 L 991.43 695.98 L 1143.95 695.98" stroke-width="1" zvalue="92"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@0" LinkObjectIDznd="80" MaxPinNum="2"/>
   </metadata>
  <path d="M 991.43 740.06 L 991.43 695.98 L 1143.95 695.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="v6300" d="M 1063.94 725.89 L 1063.94 695.98" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="77" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.94 725.89 L 1063.94 695.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="v6300" d="M 1263.18 586.89 L 1263.18 551.89" stroke-width="1" zvalue="97"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@1" LinkObjectIDznd="92@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1263.18 586.89 L 1263.18 551.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="v6300" d="M 1263.99 511.57 L 1263.99 537.49" stroke-width="1" zvalue="103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="92@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1263.99 511.57 L 1263.99 537.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="v6300" d="M 1472.3 703.21 L 1472.3 672.09" stroke-width="1" zvalue="132"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472.3 703.21 L 1472.3 672.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="v6300" d="M 1472.3 627.89 L 1472.3 586.89" stroke-width="1" zvalue="133"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="28@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472.3 627.89 L 1472.3 586.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="v6300" d="M 799.95 616.89 L 799.95 601.89 L 799.95 601.89 L 799.95 586.89" stroke-width="1" zvalue="194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="28@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 799.95 616.89 L 799.95 601.89 L 799.95 601.89 L 799.95 586.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="v6300" d="M 1143.95 616.89 L 1143.95 586.89" stroke-width="1" zvalue="195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="28@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1143.95 616.89 L 1143.95 586.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv110" d="M 993.69 203.57 L 993.69 167.55" stroke-width="1" zvalue="203"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="20" MaxPinNum="2"/>
   </metadata>
  <path d="M 993.69 203.57 L 993.69 167.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="163">
   <g id="1630">
    <use class="kv110" height="50" transform="rotate(0,955.197,389.307) scale(1.91667,1.96323) translate(-443.083,-166.927)" width="30" x="926.45" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="340.23" zvalue="10"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874431922180" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1631">
    <use class="v6300" height="50" transform="rotate(0,955.197,389.307) scale(1.91667,1.96323) translate(-443.083,-166.927)" width="30" x="926.45" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="340.23" zvalue="10"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874454269955" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399448199172" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399448199172"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,955.197,389.307) scale(1.91667,1.96323) translate(-443.083,-166.927)" width="30" x="926.45" y="340.23"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="13">
   <use class="kv110" height="40" transform="rotate(0,955.179,128.893) scale(1,1) translate(0,0)" width="30" x="940.1785714285713" xlink:href="#ACLineSegment:线路带壁雷器_0" y="108.8928571428571" zvalue="20"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249315672070" ObjectName="110kV万三二线"/>
   <cge:TPSR_Ref TObjectID="8444249315672070_5066549584789506"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,955.179,128.893) scale(1,1) translate(0,0)" width="30" x="940.1785714285713" y="108.8928571428571"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="19">
   <use class="kv110" height="18" transform="rotate(0,1122.18,227.893) scale(1,1) translate(0,0)" width="18" x="1113.178571428571" xlink:href="#Accessory:四卷PT_0" y="218.8928571428571" zvalue="27"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449756004358" ObjectName="110kV万三二线PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1122.18,227.893) scale(1,1) translate(0,0)" width="18" x="1113.178571428571" y="218.8928571428571"/></g>
  <g id="53">
   <use class="v6300" height="30" transform="rotate(0,869.679,798.893) scale(2,2) translate(-419.839,-384.446)" width="30" x="839.6785714285713" xlink:href="#Accessory:避雷器PT带熔断器_0" y="768.8928571428571" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449756200966" ObjectName="#1发电机电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,869.679,798.893) scale(2,2) translate(-419.839,-384.446)" width="30" x="839.6785714285713" y="768.8928571428571"/></g>
  <g id="63">
   <use class="v6300" height="23" transform="rotate(0,647.429,768.143) scale(2.5,2.5) translate(-377.207,-443.636)" width="15" x="628.6785714285713" xlink:href="#Accessory:PT1515_0" y="739.3928571428571" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449756463110" ObjectName="#1发电机PT"/>
   </metadata>
  <rect fill="white" height="23" opacity="0" stroke="white" transform="rotate(0,647.429,768.143) scale(2.5,2.5) translate(-377.207,-443.636)" width="15" x="628.6785714285713" y="739.3928571428571"/></g>
  <g id="64">
   <use class="v6300" height="30" transform="rotate(0,720.012,773.893) scale(1.4,1.4) translate(-199.718,-215.112)" width="30" x="699.0119047619046" xlink:href="#Accessory:PT789_0" y="752.8928571428571" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449756528646" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,720.012,773.893) scale(1.4,1.4) translate(-199.718,-215.112)" width="30" x="699.0119047619046" y="752.8928571428571"/></g>
  <g id="89">
   <use class="v6300" height="30" transform="rotate(0,1213.68,798.893) scale(2,2) translate(-591.839,-384.446)" width="30" x="1183.678571428571" xlink:href="#Accessory:避雷器PT带熔断器_0" y="768.8928571428571" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449756921862" ObjectName="#2发电机电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1213.68,798.893) scale(2,2) translate(-591.839,-384.446)" width="30" x="1183.678571428571" y="768.8928571428571"/></g>
  <g id="83">
   <use class="v6300" height="23" transform="rotate(0,991.429,768.143) scale(2.5,2.5) translate(-583.607,-443.636)" width="15" x="972.6785714285713" xlink:href="#Accessory:PT1515_0" y="739.3928571428571" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449756659718" ObjectName="#2发电机PT"/>
   </metadata>
  <rect fill="white" height="23" opacity="0" stroke="white" transform="rotate(0,991.429,768.143) scale(2.5,2.5) translate(-583.607,-443.636)" width="15" x="972.6785714285713" y="739.3928571428571"/></g>
  <g id="82">
   <use class="v6300" height="30" transform="rotate(0,1064.01,773.893) scale(1.4,1.4) translate(-298.003,-215.112)" width="30" x="1043.011904761905" xlink:href="#Accessory:PT789_0" y="752.8928571428571" zvalue="87"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449756594182" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1064.01,773.893) scale(1.4,1.4) translate(-298.003,-215.112)" width="30" x="1043.011904761905" y="752.8928571428571"/></g>
  <g id="99">
   <use class="v6300" height="48" transform="rotate(0,1259.93,476.426) scale(1.47778,1.47778) translate(-396.595,-142.566)" width="45" x="1226.678571428571" xlink:href="#Accessory:母线电压互感器11_0" y="440.9595238095238" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449757052934" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,1259.93,476.426) scale(1.47778,1.47778) translate(-396.595,-142.566)" width="45" x="1226.678571428571" y="440.9595238095238"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="28">
   <path class="v6300" d="M 706.18 586.89 L 1542.32 586.89" stroke-width="4" zvalue="33"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674240692229" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674240692229"/></metadata>
  <path d="M 706.18 586.89 L 1542.32 586.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="43">
   <use class="v6300" height="30" transform="rotate(0,1368.18,722.893) scale(1.35714,1.36667) translate(-355.047,-188.447)" width="28" x="1349.178571428571" xlink:href="#EnergyConsumer:站用变DY接地_0" y="702.3928571428571" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449756069894" ObjectName="1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1368.18,722.893) scale(1.35714,1.36667) translate(-355.047,-188.447)" width="28" x="1349.178571428571" y="702.3928571428571"/></g>
  <g id="47">
   <use class="v6300" height="30" transform="rotate(0,1472.18,722.893) scale(1.35714,1.36667) translate(-382.415,-188.447)" width="28" x="1453.178571428572" xlink:href="#EnergyConsumer:站用变DY接地_0" y="702.3928571428571" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449756135430" ObjectName="2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1472.18,722.893) scale(1.35714,1.36667) translate(-382.415,-188.447)" width="28" x="1453.178571428572" y="702.3928571428571"/></g>
 </g>
 <g id="DollyBreakerClass">
  <g id="54">
   <use class="v6300" height="20" transform="rotate(0,869.179,733.893) scale(1,1) translate(0,0)" width="10" x="864.1785714285713" xlink:href="#DollyBreaker:小车刀闸（规范制图）_0" y="723.8928571428571" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449756266502" ObjectName="#1发电机6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449756266502"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,869.179,733.893) scale(1,1) translate(0,0)" width="10" x="864.1785714285713" y="723.8928571428571"/></g>
  <g id="61">
   <use class="v6300" height="20" transform="rotate(0,719.942,734.893) scale(1,1) translate(0,0)" width="10" x="714.9419047619047" xlink:href="#DollyBreaker:小车刀闸（规范制图）_0" y="724.8928571428571" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449756397574" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449756397574"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,719.942,734.893) scale(1,1) translate(0,0)" width="10" x="714.9419047619047" y="724.8928571428571"/></g>
  <g id="88">
   <use class="v6300" height="20" transform="rotate(0,1213.18,733.893) scale(1,1) translate(0,0)" width="10" x="1208.178571428571" xlink:href="#DollyBreaker:小车刀闸（规范制图）_0" y="723.8928571428571" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449756856326" ObjectName="#2发电机6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449756856326"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1213.18,733.893) scale(1,1) translate(0,0)" width="10" x="1208.178571428571" y="723.8928571428571"/></g>
  <g id="84">
   <use class="v6300" height="20" transform="rotate(0,1063.94,734.893) scale(1,1) translate(0,0)" width="10" x="1058.941904761905" xlink:href="#DollyBreaker:小车刀闸（规范制图）_0" y="724.8928571428571" zvalue="84"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449756725254" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449756725254"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1063.94,734.893) scale(1,1) translate(0,0)" width="10" x="1058.941904761905" y="724.8928571428571"/></g>
  <g id="92">
   <use class="v6300" height="20" transform="rotate(0,1263.18,551.893) scale(1.6,1.6) translate(-470.692,-200.96)" width="10" x="1255.178571428571" xlink:href="#DollyBreaker:小车刀闸（规范制图）_0" y="535.8928571428571" zvalue="96"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449756987398" ObjectName="6.3kV母线电压互感器6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449756987398"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1263.18,551.893) scale(1.6,1.6) translate(-470.692,-200.96)" width="10" x="1255.178571428571" y="535.8928571428571"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="7" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1088.2,280.976) scale(1,1) translate(0,2.04762e-13)" writing-mode="lr" x="1087.62" xml:space="preserve" y="287.25" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126608175108" ObjectName="P"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="8" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1090.7,312.976) scale(1,1) translate(0,0)" writing-mode="lr" x="1090.12" xml:space="preserve" y="319.25" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126608240644" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="15" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1093.2,348.726) scale(1,1) translate(0,0)" writing-mode="lr" x="1092.62" xml:space="preserve" y="355" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126608306180" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="33" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1039.45,454.137) scale(1,1) translate(0,0)" writing-mode="lr" x="1038.87" xml:space="preserve" y="460.42" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124939694085" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="35" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1038.2,484.887) scale(1,1) translate(0,0)" writing-mode="lr" x="1037.62" xml:space="preserve" y="491.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124939759621" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="37" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1040.7,520.637) scale(1,1) translate(0,0)" writing-mode="lr" x="1040.12" xml:space="preserve" y="526.92" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124940152837" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f3.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="38" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,796.345,892.143) scale(1,1) translate(0,0)" writing-mode="lr" x="795.77" xml:space="preserve" y="898.42" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124946968581" ObjectName="P"/>
   </metadata>
  </g>
  <g id="40">
   <text Format="f3.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="40" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,796.345,915.393) scale(1,1) translate(0,0)" writing-mode="lr" x="795.77" xml:space="preserve" y="921.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124947034117" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="49">
   <text Format="f3.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="49" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,796.345,936.143) scale(1,1) translate(0,0)" writing-mode="lr" x="795.77" xml:space="preserve" y="942.42" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124947099653" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="50">
   <text Format="f4.1" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="50" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,796.345,867.393) scale(1,1) translate(0,0)" writing-mode="lr" x="795.8200000000001" xml:space="preserve" y="873.67" zvalue="1">ddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124947165189" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="51">
   <text Format="f3.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="51" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1126.95,882.393) scale(1,1) translate(0,0)" writing-mode="lr" x="1126.37" xml:space="preserve" y="888.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124947492869" ObjectName="P"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f3.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="52" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1126.95,910.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1126.37" xml:space="preserve" y="916.92" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124947558405" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f3.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="56" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1126.95,935.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1126.37" xml:space="preserve" y="941.42" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124947623941" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="60">
   <text Format="f4.1" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="60" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1126.95,852.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1126.42" xml:space="preserve" y="858.92" zvalue="1">ddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124947689477" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="68" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1358.68,809.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1358.1" xml:space="preserve" y="815.42" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124945788933" ObjectName="P"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="85" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1363.68,837.393) scale(1,1) translate(0,0)" writing-mode="lr" x="1363.1" xml:space="preserve" y="843.67" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124945854469" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="91" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1367.43,868.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1366.85" xml:space="preserve" y="874.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124945920005" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="95" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1470.18,805.393) scale(1,1) translate(-6.28243e-13,0)" writing-mode="lr" x="1469.6" xml:space="preserve" y="811.67" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124946378757" ObjectName="P"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="96" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1476.43,833.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1475.85" xml:space="preserve" y="839.92" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124946444293" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="97" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1477.68,869.393) scale(1,1) translate(0,0)" writing-mode="lr" x="1477.1" xml:space="preserve" y="875.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124946509829" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="98" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1502.93,550.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1502.46" xml:space="preserve" y="554.92" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124943953925" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124944084997" ObjectName="F"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="144" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124948934661" ObjectName="GEN_PSUM"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="143" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124949000197" ObjectName="F"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="137" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124948803589" ObjectName="F"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="136" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124948869125" ObjectName="F"/>
   </metadata>
  </g>
  <g id="133">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="133" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124948803589" ObjectName="F"/>
   </metadata>
  </g>
  <g id="131">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="131" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124948803589" ObjectName="F"/>
   </metadata>
  </g>
  <g id="120">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="120" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,954.179,28.3929) scale(1,1) translate(0,-4.03447e-15)" writing-mode="lr" x="911.05" xml:space="preserve" y="32.81" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124942118917" ObjectName="P"/>
   </metadata>
  </g>
  <g id="121">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="121" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,955.179,54.2232) scale(1,1) translate(0,-9.76996e-15)" writing-mode="lr" x="912.05" xml:space="preserve" y="58.64" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124942184453" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="122">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="122" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,955.179,75.3929) scale(1,1) translate(0,-1.44706e-14)" writing-mode="lr" x="912.05" xml:space="preserve" y="79.81" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124942249989" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="StateClass">
  <g id="142">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="180"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374889320451" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="141">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="181"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562951077691397" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
  <g id="1">
   <use height="30" transform="rotate(0,330.812,131.464) scale(1.27778,1.03333) translate(-59.4158,-3.74077)" width="90" x="273.31" xlink:href="#State:全站检修_0" y="115.96" zvalue="205"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549584789506" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,330.812,131.464) scale(1.27778,1.03333) translate(-59.4158,-3.74077)" width="90" x="273.31" y="115.96"/></g>
 </g>
</svg>