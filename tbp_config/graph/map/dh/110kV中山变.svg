<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549681848321" height="1045" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1045" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1734.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2314.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="1.166666666666671"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.08333333333333" x2="15.08333333333333" y1="19.16666666666667" y2="9.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="17" y1="28" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="9.166666666666671" y2="1.166666666666671"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="17.5" y1="19.16666666666667" y2="19.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="23.41666666666667" y2="19.16666666666667"/>
   <rect fill-opacity="0" height="4.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(315,19.71,13.46) scale(1,-1) translate(0,-783.33)" width="10.25" x="14.58" y="11.42"/>
   <ellipse cx="15.15" cy="28.4" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="34" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="24.67" cy="31" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill="rgb(170,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.333333333333333" x2="9.75" y1="0.4166666666666696" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.666666666666668" x2="0.4166666666666661" y1="0.4999999999999982" y2="19.66666666666667"/>
   <rect fill-opacity="0" height="19.58" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.04,10.04) scale(1,1) translate(0,0)" width="9.58" x="0.25" y="0.25"/>
  </symbol>
  <symbol id="Accessory:PT1111_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="33.75" xlink:href="#terminal" y="11.95"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="27.02377700368697" x2="19.18246971500333" y1="12.01581866675157" y2="12.01581866675157"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.4317302644139" x2="33.73333333333333" y1="12.01581866675157" y2="12.01581866675157"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.30947355115262" x2="23.30947355115262" y1="12.14512912951974" y2="19.34675167759026"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.21102695297582" x2="12.70307369224894" y1="12.09628971045682" y2="12.09628971045682"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.22373975336484" x2="19.22373975336484" y1="12.09628971045682" y2="12.09628971045682"/>
   <ellipse cx="23.29" cy="23.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="19.22373975336483" x2="19.22373975336483" y1="8.716505874834564" y2="15.55654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.36665011096792" x2="30.36665011096792" y1="12.09628971045682" y2="12.09628971045682"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="30.3666501109679" x2="30.3666501109679" y1="8.716505874834564" y2="15.55654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="16.12848687625285" x2="16.12848687625285" y1="8.716505874834558" y2="15.55654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="27.27139723385593" x2="27.27139723385593" y1="8.716505874834564" y2="15.55654458978453"/>
   <ellipse cx="25.79" cy="27.85" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.79" cy="27.85" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.96296296296296" x2="8.779897241477752" y1="12.08795620351516" y2="12.08795620351516"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="8.711988705963254" x2="8.711988705963254" y1="14.28440670580408" y2="10.00759086112023"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="7.461988705963254" x2="7.461988705963254" y1="13.53440670580408" y2="10.9242575277869"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.961988705963256" x2="5.961988705963256" y1="13.03440670580408" y2="11.59092419445357"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变带熔断器YY_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0235180220435" x2="10.0235180220435" y1="0.5833333333333091" y2="9.290410445610181"/>
   <rect fill-opacity="0" height="4.32" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.06,5.25) scale(1,1) translate(0,0)" width="3.43" x="8.34" y="3.09"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="29.16666666666667" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="10.8868007916835" y2="12.55855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="14.23031840279781" y2="12.55855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2032.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2032.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2032.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2032.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2032.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="PowerTransformer3:三卷变带中性点_0" viewBox="0,0,50,50">
   <use terminal-index="0" type="1" x="14.75" xlink:href="#terminal" y="0.2499999999999929"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.33333333333333" x2="15.15740740740741" y1="10.58333333333333" y2="14.1754686785551"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.08333333333333" x2="15.17203932327389" y1="10.66666666666667" y2="14.1754686785551"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.16666666666666" x2="15.16666666666666" y1="14.16666666666666" y2="18.91666666666666"/>
   <ellipse cx="14.93" cy="15" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="2" x="15.08333333333334" xlink:href="#terminal" y="14.16666666666666"/>
  </symbol>
  <symbol id="PowerTransformer3:三卷变带中性点_1" viewBox="0,0,50,50">
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.66666666666666" x2="34.66666666666666" y1="29.41666666666667" y2="25.11796982167353"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.66598079561043" x2="29.75" y1="25.11796982167353" y2="20.66666666666667"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.67329675354367" x2="39.25" y1="25.11065386374029" y2="20.58333333333334"/>
   <ellipse cx="34.78" cy="25.08" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="1" x="49.50000000000001" xlink:href="#terminal" y="25.08333333333333"/>
  </symbol>
  <symbol id="PowerTransformer3:三卷变带中性点_2" viewBox="0,0,50,50">
   <path d="M 15.1667 32 L 10.0833 40 L 20.1667 40 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.12" cy="35.17" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="3" type="1" x="15.00000000000001" xlink:href="#terminal" y="49.83333333333334"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Accessory:PT7_0" viewBox="0,0,14,18">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1.416666666666664"/>
   <ellipse cx="7.15" cy="6.35" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.15" cy="12.03" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:间隔模板_0" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(85,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="State:间隔模板_1" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(185,185,185)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷带壁雷器_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="28.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11.75" y1="17.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="14.75" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="12" y1="11.75" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="9" y1="12.75" y2="12.75"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.04,18.25) scale(1,1) translate(0,0)" width="3.25" x="9.42" y="14.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.5" y1="10.83333333333333" y2="10.83333333333333"/>
   <path d="M 5.025 2.775 L 4.16667 9.5 L 5.025 7.60833 L 5.91667 9.5 L 5.025 2.775" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 5 23.75 L 11 23.75 L 11 17.75 L 10.25 20.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="7.627914951989029" y2="28.23902606310013"/>
  </symbol>
  <symbol id=":线路带避雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15.16666666666667" xlink:href="#terminal" y="39.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.16022336769755" x2="15.16022336769755" y1="39.75" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="6.5" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="8.5" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="7.5" y1="32.25" y2="32.25"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6.04,21.17) scale(1,1) translate(0,0)" width="6.08" x="3" y="14"/>
   <path d="M 15 9.25 L 6 9.25 L 6 21.25 L 6 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="7" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="5" y1="22" y2="16"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV中山变" InitShowingPlane="" fill="rgb(0,0,0)" height="1045" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="43.02" xlink:href="logo.png" y="28.07"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="383" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,167.868,69.3565) scale(1,1) translate(-9.55258e-15,0)" writing-mode="lr" x="167.87" xml:space="preserve" y="72.86" zvalue="906"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,196.714,69.3332) scale(1,1) translate(0,0)" writing-mode="lr" x="196.71" xml:space="preserve" y="78.33" zvalue="907">110kV中山变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="410" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,71.625,423.75) scale(1,1) translate(0,0)" width="72.88" x="35.19" y="411.75" zvalue="1115"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,71.625,423.75) scale(1,1) translate(0,0)" writing-mode="lr" x="71.63" xml:space="preserve" y="428.25" zvalue="1115">信号一览</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="418" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,72.5312,377.821) scale(1,1) translate(0,0)" width="72.88" x="36.09" y="365.82" zvalue="1117"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,72.5312,377.821) scale(1,1) translate(0,0)" writing-mode="lr" x="72.53" xml:space="preserve" y="382.32" zvalue="1117">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="411" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,173.625,423.75) scale(1,1) translate(0,0)" width="72.88" x="137.19" y="411.75" zvalue="1118"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,173.625,423.75) scale(1,1) translate(0,0)" writing-mode="lr" x="173.63" xml:space="preserve" y="428.25" zvalue="1118">AVC功能</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,516.082,379.877) scale(1,1) translate(0,0)" writing-mode="lr" x="516.08" xml:space="preserve" y="384.38" zvalue="33">110kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,655.889,524.205) scale(1,1) translate(0,0)" writing-mode="lr" x="655.89" xml:space="preserve" y="528.71" zvalue="35">1010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,462.79,690.214) scale(1,1) translate(3.29351e-13,0)" writing-mode="lr" x="462.79" xml:space="preserve" y="694.71" zvalue="39">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,706.983,428.175) scale(1,1) translate(0,0)" writing-mode="lr" x="706.98" xml:space="preserve" y="432.68" zvalue="41">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,703.374,471.974) scale(1,1) translate(0,0)" writing-mode="lr" x="703.37" xml:space="preserve" y="476.47" zvalue="43">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,705.555,383.18) scale(1,1) translate(0,0)" writing-mode="lr" x="705.55" xml:space="preserve" y="387.68" zvalue="46">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,753.788,463.413) scale(1,1) translate(0,0)" writing-mode="lr" x="753.79" xml:space="preserve" y="467.91" zvalue="48">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,753.788,416.413) scale(1,1) translate(0,0)" writing-mode="lr" x="753.79" xml:space="preserve" y="420.91" zvalue="50">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,563.73,306.248) scale(1,1) translate(0,0)" writing-mode="lr" x="563.73" xml:space="preserve" y="310.75" zvalue="54">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,605.246,332.038) scale(1,1) translate(0,0)" writing-mode="lr" x="605.25" xml:space="preserve" y="336.54" zvalue="56">19010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,606.579,280.704) scale(1,1) translate(0,0)" writing-mode="lr" x="606.58" xml:space="preserve" y="285.2" zvalue="60">19017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1532.86,295.365) scale(1,1) translate(0,0)" writing-mode="lr" x="1532.86" xml:space="preserve" y="299.86" zvalue="64">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1343.15,508.145) scale(1,1) translate(0,0)" writing-mode="lr" x="1343.15" xml:space="preserve" y="512.64" zvalue="66">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1278.39,509.213) scale(1,1) translate(0,0)" writing-mode="lr" x="1278.39" xml:space="preserve" y="513.71" zvalue="68">3016</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1247.57,377.769) scale(1,1) translate(0,0)" writing-mode="lr" x="1247.57" xml:space="preserve" y="382.27" zvalue="75">110kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,544.685,200.045) scale(1,1) translate(0,0)" writing-mode="lr" x="544.6799999999999" xml:space="preserve" y="204.55" zvalue="77">110kVⅠ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1216.04,307.359) scale(1,1) translate(0,0)" writing-mode="lr" x="1216.04" xml:space="preserve" y="311.86" zvalue="79">1902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1257.69,329.816) scale(1,1) translate(0,0)" writing-mode="lr" x="1257.69" xml:space="preserve" y="334.32" zvalue="81">19020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1255.47,282.399) scale(1,1) translate(0,0)" writing-mode="lr" x="1255.47" xml:space="preserve" y="286.9" zvalue="85">19027</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1236.02,204.187) scale(1,1) translate(0,0)" writing-mode="lr" x="1236.02" xml:space="preserve" y="208.69" zvalue="89">110kVⅡ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1414.13,344.311) scale(1,1) translate(0,0)" writing-mode="lr" x="1414.13" xml:space="preserve" y="348.81" zvalue="94">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="349" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1604.93,726.674) scale(1,1) translate(0,0)" writing-mode="lr" x="1604.93" xml:space="preserve" y="731.17" zvalue="96">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="186" stroke="rgb(255,255,255)" text-anchor="middle" x="799.8828125" xml:space="preserve" y="559.4623107910156" zvalue="97">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="186" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="799.8828125" xml:space="preserve" y="575.4623107910156" zvalue="97">10000kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,656.014,89.3122) scale(1,1) translate(0,0)" writing-mode="lr" x="656.01" xml:space="preserve" y="93.81" zvalue="118">110kV中弄线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,683.148,264.145) scale(1,1) translate(0,0)" writing-mode="lr" x="683.15" xml:space="preserve" y="268.64" zvalue="119">122</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,678.641,201.254) scale(1,1) translate(0,0)" writing-mode="lr" x="678.64" xml:space="preserve" y="205.75" zvalue="121">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,718.246,161.816) scale(1,1) translate(0,0)" writing-mode="lr" x="718.25" xml:space="preserve" y="166.32" zvalue="123">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,676.355,331.049) scale(1,1) translate(0,0)" writing-mode="lr" x="676.35" xml:space="preserve" y="335.55" zvalue="126">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,717.246,226.061) scale(1,1) translate(0,0)" writing-mode="lr" x="717.25" xml:space="preserve" y="230.56" zvalue="129">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,715.317,299.657) scale(1,1) translate(0,0)" writing-mode="lr" x="715.3200000000001" xml:space="preserve" y="304.16" zvalue="132">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1081.01,89.3122) scale(1,1) translate(0,0)" writing-mode="lr" x="1081.01" xml:space="preserve" y="93.81" zvalue="143">110kV万中线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1108.15,263.145) scale(1,1) translate(0,0)" writing-mode="lr" x="1108.15" xml:space="preserve" y="267.64" zvalue="144">123</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1107.64,201.254) scale(1,1) translate(0,0)" writing-mode="lr" x="1107.64" xml:space="preserve" y="205.75" zvalue="146">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1143.25,161.816) scale(1,1) translate(0,0)" writing-mode="lr" x="1143.25" xml:space="preserve" y="166.32" zvalue="148">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1107.58,331.049) scale(1,1) translate(0,0)" writing-mode="lr" x="1107.58" xml:space="preserve" y="335.55" zvalue="151">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1142.25,230.061) scale(1,1) translate(0,0)" writing-mode="lr" x="1142.25" xml:space="preserve" y="234.56" zvalue="154">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1143.36,299.212) scale(1,1) translate(0,0)" writing-mode="lr" x="1143.36" xml:space="preserve" y="303.71" zvalue="157">27</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,908.959,297.557) scale(1,1) translate(0,0)" writing-mode="lr" x="908.9590277648583" xml:space="preserve" y="302.0572508008422" zvalue="167">110kV分段112</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,833.666,323.267) scale(1,1) translate(0,0)" writing-mode="lr" x="833.67" xml:space="preserve" y="327.77" zvalue="169">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1001.98,323.267) scale(1,1) translate(0,0)" writing-mode="lr" x="1001.98" xml:space="preserve" y="327.77" zvalue="171">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1041.44,304.92) scale(1,1) translate(0,0)" writing-mode="lr" x="1041.44" xml:space="preserve" y="309.42" zvalue="173">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,878.889,312.726) scale(1,1) translate(0,0)" writing-mode="lr" x="878.89" xml:space="preserve" y="317.23" zvalue="175">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,752.343,642.712) scale(1,1) translate(0,5.63294e-13)" writing-mode="lr" x="752.34" xml:space="preserve" y="647.21" zvalue="184">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,752.243,609.372) scale(1,1) translate(0,0)" writing-mode="lr" x="752.24" xml:space="preserve" y="613.87" zvalue="186">0016</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,752.474,673.141) scale(1,1) translate(0,0)" writing-mode="lr" x="752.47" xml:space="preserve" y="677.64" zvalue="189">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1505,400.826) scale(1,1) translate(0,3.48455e-13)" writing-mode="lr" x="1505" xml:space="preserve" y="405.33" zvalue="203">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1807.1,452.326) scale(1,1) translate(0,0)" writing-mode="lr" x="1807.1" xml:space="preserve" y="456.83" zvalue="217">备用</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1807.1,556.326) scale(1,1) translate(0,0)" writing-mode="lr" x="1807.1" xml:space="preserve" y="560.83" zvalue="231">备用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,565.012,666.248) scale(1,1) translate(0,0)" writing-mode="lr" x="565.01" xml:space="preserve" y="670.75" zvalue="240">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,569.5,625.657) scale(1,1) translate(0,0)" writing-mode="lr" x="569.5" xml:space="preserve" y="630.16" zvalue="243">09017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,528.483,566.712) scale(1,1) translate(0,0)" writing-mode="lr" x="528.48" xml:space="preserve" y="571.21" zvalue="247">10kVⅠ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(180,1353.51,671.803) scale(-1,-1) translate(-2707.01,-1343.61)" writing-mode="lr" x="1353.51" xml:space="preserve" y="676.3" zvalue="249">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1389.47,641.371) scale(1,1) translate(0,0)" writing-mode="lr" x="1389.47" xml:space="preserve" y="645.87" zvalue="252">09027</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1325.68,562.116) scale(1,1) translate(0,0)" writing-mode="lr" x="1325.68" xml:space="preserve" y="566.62" zvalue="256">10kVⅡ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,765.072,937.778) scale(1,1) translate(0,0)" writing-mode="lr" x="765.0700000000001" xml:space="preserve" y="942.28" zvalue="261">10kV备用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,687.445,784.978) scale(1,1) translate(0,0)" writing-mode="lr" x="687.4400000000001" xml:space="preserve" y="789.48" zvalue="296">023</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,685.962,826.84) scale(1,1) translate(0,0)" writing-mode="lr" x="685.96" xml:space="preserve" y="831.34" zvalue="297">0236</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,685.311,873.357) scale(1,1) translate(0,0)" writing-mode="lr" x="685.3099999999999" xml:space="preserve" y="877.86" zvalue="299">0238</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,686.456,747.801) scale(1,1) translate(0,0)" writing-mode="lr" x="686.46" xml:space="preserve" y="752.3" zvalue="301">0231</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,663.102,937.778) scale(1,1) translate(0,0)" writing-mode="lr" x="663.1" xml:space="preserve" y="942.28" zvalue="303">10kV小街线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1810.62,341.345) scale(1,1) translate(0,0)" writing-mode="lr" x="1810.62" xml:space="preserve" y="345.84" zvalue="317">备用</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="250" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,473.991,937.778) scale(1,1) translate(0,0)" writing-mode="lr" x="473.99" xml:space="preserve" y="942.28" zvalue="334">10kV备用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,593.44,784.978) scale(1,1) translate(0,0)" writing-mode="lr" x="593.4400000000001" xml:space="preserve" y="789.48" zvalue="344">022</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,591.936,826.84) scale(1,1) translate(0,0)" writing-mode="lr" x="591.9400000000001" xml:space="preserve" y="831.34" zvalue="345">0226</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,591.311,873.357) scale(1,1) translate(0,0)" writing-mode="lr" x="591.3099999999999" xml:space="preserve" y="877.86" zvalue="347">0228</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,592.426,747.801) scale(1,1) translate(0,0)" writing-mode="lr" x="592.4299999999999" xml:space="preserve" y="752.3" zvalue="349">0221</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,566.602,937.778) scale(1,1) translate(0,0)" writing-mode="lr" x="566.6" xml:space="preserve" y="942.28" zvalue="351">10kV康丰糖厂线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,881.301,743.801) scale(1,1) translate(0,0)" writing-mode="lr" x="881.3" xml:space="preserve" y="748.3" zvalue="387">0261</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,896.246,807.101) scale(1,1) translate(0,0)" writing-mode="lr" x="896.25" xml:space="preserve" y="811.6" zvalue="390">02617</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="293" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,861,908) scale(1,1) translate(0,0)" writing-mode="lr" x="861" xml:space="preserve" y="912.5" zvalue="393">10kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="333" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1438.79,788.978) scale(1,1) translate(0,0)" writing-mode="lr" x="1438.79" xml:space="preserve" y="793.48" zvalue="396">031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="331" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1441.28,877.357) scale(1,1) translate(0,0)" writing-mode="lr" x="1441.28" xml:space="preserve" y="881.86" zvalue="397">0318</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="334" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1438.43,747.801) scale(1,1) translate(0,0)" writing-mode="lr" x="1438.43" xml:space="preserve" y="752.3" zvalue="398">0312</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1419.07,937.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1419.07" xml:space="preserve" y="942.28" zvalue="399">10kV黄家寨线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1254.56,784.978) scale(1,1) translate(0,0)" writing-mode="lr" x="1254.56" xml:space="preserve" y="789.48" zvalue="406">028</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1251.96,830.84) scale(1,1) translate(0,0)" writing-mode="lr" x="1251.96" xml:space="preserve" y="835.34" zvalue="407">0286</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1251.31,877.357) scale(1,1) translate(0,0)" writing-mode="lr" x="1251.31" xml:space="preserve" y="881.86" zvalue="409">0288</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1252.46,747.801) scale(1,1) translate(0,0)" writing-mode="lr" x="1252.46" xml:space="preserve" y="752.3" zvalue="411">0282</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1230.21,937.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1230.21" xml:space="preserve" y="942.28" zvalue="413">10kV中万线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="332" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1437.93,830.84) scale(1,1) translate(0,0)" writing-mode="lr" x="1437.93" xml:space="preserve" y="835.34" zvalue="420">0316</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="376" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1147.43,783.52) scale(1,1) translate(0,0)" writing-mode="lr" x="1147.43" xml:space="preserve" y="788.02" zvalue="422">027</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="464" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1150.15,877.357) scale(1,1) translate(0,0)" writing-mode="lr" x="1150.15" xml:space="preserve" y="881.86" zvalue="423">0278</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1150.15,743.801) scale(1,1) translate(0,0)" writing-mode="lr" x="1150.15" xml:space="preserve" y="748.3" zvalue="424">0272</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1129.1,937.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1129.1" xml:space="preserve" y="942.28" zvalue="425">10kV赛岗线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="462" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1150.15,830.84) scale(1,1) translate(0,0)" writing-mode="lr" x="1150.15" xml:space="preserve" y="835.34" zvalue="431">0276</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1347.44,784.978) scale(1,1) translate(0,0)" writing-mode="lr" x="1347.44" xml:space="preserve" y="789.48" zvalue="434">029</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1345.94,830.84) scale(1,1) translate(0,0)" writing-mode="lr" x="1345.94" xml:space="preserve" y="835.34" zvalue="435">0296</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1345.31,877.357) scale(1,1) translate(0,0)" writing-mode="lr" x="1345.31" xml:space="preserve" y="881.86" zvalue="437">0298</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1346.43,747.801) scale(1,1) translate(0,0)" writing-mode="lr" x="1346.43" xml:space="preserve" y="752.3" zvalue="439">0292</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1323.1,936.869) scale(1,1) translate(0,0)" writing-mode="lr" x="1323.1" xml:space="preserve" y="941.37" zvalue="441">10kV备用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1543.3,743.801) scale(1,1) translate(0,0)" writing-mode="lr" x="1543.3" xml:space="preserve" y="748.3" zvalue="449">0332</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1559.5,805.851) scale(1,1) translate(0,0)" writing-mode="lr" x="1559.5" xml:space="preserve" y="810.35" zvalue="451">03327</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1530.5,895.841) scale(1,1) translate(0,0)" writing-mode="lr" x="1530.5" xml:space="preserve" y="900.34" zvalue="454">10kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="337" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,989.473,658.08) scale(1,1) translate(0,0)" writing-mode="lr" x="989.4727114866691" xml:space="preserve" y="662.5799780735695" zvalue="458">10kV分段012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="336" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,925.666,669.296) scale(1,1) translate(1.01104e-13,0)" writing-mode="lr" x="925.67" xml:space="preserve" y="673.8" zvalue="460">0121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="348" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1090.7,668.973) scale(1,1) translate(0,0)" writing-mode="lr" x="1090.7" xml:space="preserve" y="673.47" zvalue="462">0122</text>
  <line fill="none" id="379" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.71428571428623" x2="383.7142857142858" y1="146.5133497569395" y2="146.5133497569395" zvalue="909"/>
  <line fill="none" id="378" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="388.7142857142856" x2="388.7142857142856" y1="4.642857142857224" y2="1034.642857142857" zvalue="910"/>
  <line fill="none" id="377" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.96428571428623" x2="384.9642857142858" y1="661.5133497569395" y2="661.5133497569395" zvalue="911"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.60358730158725" x2="58.72708730158729" y1="479.1150396825398" y2="479.1150396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.60358730158725" x2="58.72708730158729" y1="511.6607396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.60358730158725" x2="14.60358730158725" y1="479.1150396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72708730158729" x2="58.72708730158729" y1="479.1150396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72758730158716" x2="127.0585873015872" y1="479.1150396825398" y2="479.1150396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72758730158716" x2="127.0585873015872" y1="511.6607396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72758730158716" x2="58.72758730158716" y1="479.1150396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0585873015872" x2="127.0585873015872" y1="479.1150396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0584873015872" x2="191.5928873015872" y1="479.1150396825398" y2="479.1150396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0584873015872" x2="191.5928873015872" y1="511.6607396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0584873015872" x2="127.0584873015872" y1="479.1150396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928873015872" x2="191.5928873015872" y1="479.1150396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928573015873" x2="256.0615573015872" y1="479.1150396825398" y2="479.1150396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928573015873" x2="256.0615573015872" y1="511.6607396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928573015873" x2="191.5928573015873" y1="479.1150396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0615573015872" x2="256.0615573015872" y1="479.1150396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0619873015872" x2="320.5306873015872" y1="479.1150396825398" y2="479.1150396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0619873015872" x2="320.5306873015872" y1="511.6607396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0619873015872" x2="256.0619873015872" y1="479.1150396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5306873015872" x2="320.5306873015872" y1="479.1150396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5315873015873" x2="385.0002873015873" y1="479.1150396825398" y2="479.1150396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5315873015873" x2="385.0002873015873" y1="511.6607396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5315873015873" x2="320.5315873015873" y1="479.1150396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="385.0002873015873" x2="385.0002873015873" y1="479.1150396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.60358730158725" x2="58.72708730158729" y1="511.6607396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.60358730158725" x2="58.72708730158729" y1="538.2286396825398" y2="538.2286396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.60358730158725" x2="14.60358730158725" y1="511.6607396825398" y2="538.2286396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72708730158729" x2="58.72708730158729" y1="511.6607396825398" y2="538.2286396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72758730158716" x2="127.0585873015872" y1="511.6607396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72758730158716" x2="127.0585873015872" y1="538.2286396825398" y2="538.2286396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72758730158716" x2="58.72758730158716" y1="511.6607396825398" y2="538.2286396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0585873015872" x2="127.0585873015872" y1="511.6607396825398" y2="538.2286396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0584873015872" x2="191.5928873015872" y1="511.6607396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0584873015872" x2="191.5928873015872" y1="538.2286396825398" y2="538.2286396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0584873015872" x2="127.0584873015872" y1="511.6607396825398" y2="538.2286396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928873015872" x2="191.5928873015872" y1="511.6607396825398" y2="538.2286396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928573015873" x2="256.0615573015872" y1="511.6607396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928573015873" x2="256.0615573015872" y1="538.2286396825398" y2="538.2286396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928573015873" x2="191.5928573015873" y1="511.6607396825398" y2="538.2286396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0615573015872" x2="256.0615573015872" y1="511.6607396825398" y2="538.2286396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0619873015872" x2="320.5306873015872" y1="511.6607396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0619873015872" x2="320.5306873015872" y1="538.2286396825398" y2="538.2286396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0619873015872" x2="256.0619873015872" y1="511.6607396825398" y2="538.2286396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5306873015872" x2="320.5306873015872" y1="511.6607396825398" y2="538.2286396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5315873015873" x2="385.0002873015873" y1="511.6607396825398" y2="511.6607396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5315873015873" x2="385.0002873015873" y1="538.2286396825398" y2="538.2286396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5315873015873" x2="320.5315873015873" y1="511.6607396825398" y2="538.2286396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="385.0002873015873" x2="385.0002873015873" y1="511.6607396825398" y2="538.2286396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.60358730158725" x2="58.72708730158729" y1="538.2285396825398" y2="538.2285396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.60358730158725" x2="58.72708730158729" y1="564.7964396825397" y2="564.7964396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.60358730158725" x2="14.60358730158725" y1="538.2285396825398" y2="564.7964396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72708730158729" x2="58.72708730158729" y1="538.2285396825398" y2="564.7964396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72758730158716" x2="127.0585873015872" y1="538.2285396825398" y2="538.2285396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72758730158716" x2="127.0585873015872" y1="564.7964396825397" y2="564.7964396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72758730158716" x2="58.72758730158716" y1="538.2285396825398" y2="564.7964396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0585873015872" x2="127.0585873015872" y1="538.2285396825398" y2="564.7964396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0584873015872" x2="191.5928873015872" y1="538.2285396825398" y2="538.2285396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0584873015872" x2="191.5928873015872" y1="564.7964396825397" y2="564.7964396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0584873015872" x2="127.0584873015872" y1="538.2285396825398" y2="564.7964396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928873015872" x2="191.5928873015872" y1="538.2285396825398" y2="564.7964396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928573015873" x2="256.0615573015872" y1="538.2285396825398" y2="538.2285396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928573015873" x2="256.0615573015872" y1="564.7964396825397" y2="564.7964396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928573015873" x2="191.5928573015873" y1="538.2285396825398" y2="564.7964396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0615573015872" x2="256.0615573015872" y1="538.2285396825398" y2="564.7964396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0619873015872" x2="320.5306873015872" y1="538.2285396825398" y2="538.2285396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0619873015872" x2="320.5306873015872" y1="564.7964396825397" y2="564.7964396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0619873015872" x2="256.0619873015872" y1="538.2285396825398" y2="564.7964396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5306873015872" x2="320.5306873015872" y1="538.2285396825398" y2="564.7964396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5315873015873" x2="385.0002873015873" y1="538.2285396825398" y2="538.2285396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5315873015873" x2="385.0002873015873" y1="564.7964396825397" y2="564.7964396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5315873015873" x2="320.5315873015873" y1="538.2285396825398" y2="564.7964396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="385.0002873015873" x2="385.0002873015873" y1="538.2285396825398" y2="564.7964396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.60358730158725" x2="58.72708730158729" y1="564.7964196825397" y2="564.7964196825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.60358730158725" x2="58.72708730158729" y1="591.3643196825398" y2="591.3643196825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.60358730158725" x2="14.60358730158725" y1="564.7964196825397" y2="591.3643196825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72708730158729" x2="58.72708730158729" y1="564.7964196825397" y2="591.3643196825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72758730158716" x2="127.0585873015872" y1="564.7964196825397" y2="564.7964196825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72758730158716" x2="127.0585873015872" y1="591.3643196825398" y2="591.3643196825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72758730158716" x2="58.72758730158716" y1="564.7964196825397" y2="591.3643196825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0585873015872" x2="127.0585873015872" y1="564.7964196825397" y2="591.3643196825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0584873015872" x2="191.5928873015872" y1="564.7964196825397" y2="564.7964196825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0584873015872" x2="191.5928873015872" y1="591.3643196825398" y2="591.3643196825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0584873015872" x2="127.0584873015872" y1="564.7964196825397" y2="591.3643196825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928873015872" x2="191.5928873015872" y1="564.7964196825397" y2="591.3643196825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928573015873" x2="256.0615573015872" y1="564.7964196825397" y2="564.7964196825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928573015873" x2="256.0615573015872" y1="591.3643196825398" y2="591.3643196825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928573015873" x2="191.5928573015873" y1="564.7964196825397" y2="591.3643196825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0615573015872" x2="256.0615573015872" y1="564.7964196825397" y2="591.3643196825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0619873015872" x2="320.5306873015872" y1="564.7964196825397" y2="564.7964196825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0619873015872" x2="320.5306873015872" y1="591.3643196825398" y2="591.3643196825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0619873015872" x2="256.0619873015872" y1="564.7964196825397" y2="591.3643196825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5306873015872" x2="320.5306873015872" y1="564.7964196825397" y2="591.3643196825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5315873015873" x2="385.0002873015873" y1="564.7964196825397" y2="564.7964196825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5315873015873" x2="385.0002873015873" y1="591.3643196825398" y2="591.3643196825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5315873015873" x2="320.5315873015873" y1="564.7964196825397" y2="591.3643196825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="385.0002873015873" x2="385.0002873015873" y1="564.7964196825397" y2="591.3643196825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.60358730158725" x2="58.72708730158729" y1="591.3643396825397" y2="591.3643396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.60358730158725" x2="58.72708730158729" y1="617.9322396825397" y2="617.9322396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.60358730158725" x2="14.60358730158725" y1="591.3643396825397" y2="617.9322396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72708730158729" x2="58.72708730158729" y1="591.3643396825397" y2="617.9322396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72758730158716" x2="127.0585873015872" y1="591.3643396825397" y2="591.3643396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72758730158716" x2="127.0585873015872" y1="617.9322396825397" y2="617.9322396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72758730158716" x2="58.72758730158716" y1="591.3643396825397" y2="617.9322396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0585873015872" x2="127.0585873015872" y1="591.3643396825397" y2="617.9322396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0584873015872" x2="191.5928873015872" y1="591.3643396825397" y2="591.3643396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0584873015872" x2="191.5928873015872" y1="617.9322396825397" y2="617.9322396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0584873015872" x2="127.0584873015872" y1="591.3643396825397" y2="617.9322396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928873015872" x2="191.5928873015872" y1="591.3643396825397" y2="617.9322396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928573015873" x2="256.0615573015872" y1="591.3643396825397" y2="591.3643396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928573015873" x2="256.0615573015872" y1="617.9322396825397" y2="617.9322396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928573015873" x2="191.5928573015873" y1="591.3643396825397" y2="617.9322396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0615573015872" x2="256.0615573015872" y1="591.3643396825397" y2="617.9322396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0619873015872" x2="320.5306873015872" y1="591.3643396825397" y2="591.3643396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0619873015872" x2="320.5306873015872" y1="617.9322396825397" y2="617.9322396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0619873015872" x2="256.0619873015872" y1="591.3643396825397" y2="617.9322396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5306873015872" x2="320.5306873015872" y1="591.3643396825397" y2="617.9322396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5315873015873" x2="385.0002873015873" y1="591.3643396825397" y2="591.3643396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5315873015873" x2="385.0002873015873" y1="617.9322396825397" y2="617.9322396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5315873015873" x2="320.5315873015873" y1="591.3643396825397" y2="617.9322396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="385.0002873015873" x2="385.0002873015873" y1="591.3643396825397" y2="617.9322396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.60358730158725" x2="58.72708730158729" y1="617.9321396825397" y2="617.9321396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.60358730158725" x2="58.72708730158729" y1="644.5000396825398" y2="644.5000396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14.60358730158725" x2="14.60358730158725" y1="617.9321396825397" y2="644.5000396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72708730158729" x2="58.72708730158729" y1="617.9321396825397" y2="644.5000396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72758730158716" x2="127.0585873015872" y1="617.9321396825397" y2="617.9321396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72758730158716" x2="127.0585873015872" y1="644.5000396825398" y2="644.5000396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.72758730158716" x2="58.72758730158716" y1="617.9321396825397" y2="644.5000396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0585873015872" x2="127.0585873015872" y1="617.9321396825397" y2="644.5000396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0584873015872" x2="191.5928873015872" y1="617.9321396825397" y2="617.9321396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0584873015872" x2="191.5928873015872" y1="644.5000396825398" y2="644.5000396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="127.0584873015872" x2="127.0584873015872" y1="617.9321396825397" y2="644.5000396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928873015872" x2="191.5928873015872" y1="617.9321396825397" y2="644.5000396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928573015873" x2="256.0615573015872" y1="617.9321396825397" y2="617.9321396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928573015873" x2="256.0615573015872" y1="644.5000396825398" y2="644.5000396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5928573015873" x2="191.5928573015873" y1="617.9321396825397" y2="644.5000396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0615573015872" x2="256.0615573015872" y1="617.9321396825397" y2="644.5000396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0619873015872" x2="320.5306873015872" y1="617.9321396825397" y2="617.9321396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0619873015872" x2="320.5306873015872" y1="644.5000396825398" y2="644.5000396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="256.0619873015872" x2="256.0619873015872" y1="617.9321396825397" y2="644.5000396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5306873015872" x2="320.5306873015872" y1="617.9321396825397" y2="644.5000396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5315873015873" x2="385.0002873015873" y1="617.9321396825397" y2="617.9321396825397"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5315873015873" x2="385.0002873015873" y1="644.5000396825398" y2="644.5000396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.5315873015873" x2="320.5315873015873" y1="617.9321396825397" y2="644.5000396825398"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="385.0002873015873" x2="385.0002873015873" y1="617.9321396825397" y2="644.5000396825398"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.96428571428555" x2="103.9642857142856" y1="918.6428571428572" y2="918.6428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.96428571428555" x2="103.9642857142856" y1="970.8061571428573" y2="970.8061571428573"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.96428571428555" x2="13.96428571428555" y1="918.6428571428572" y2="970.8061571428573"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.9642857142856" x2="103.9642857142856" y1="918.6428571428572" y2="970.8061571428573"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.9642857142856" x2="373.9642857142856" y1="918.6428571428572" y2="918.6428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.9642857142856" x2="373.9642857142856" y1="970.8061571428573" y2="970.8061571428573"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.9642857142856" x2="103.9642857142856" y1="918.6428571428572" y2="970.8061571428573"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="373.9642857142856" x2="373.9642857142856" y1="918.6428571428572" y2="970.8061571428573"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.96428571428555" x2="103.9642857142856" y1="970.8061271428572" y2="970.8061271428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.96428571428555" x2="103.9642857142856" y1="998.7245271428571" y2="998.7245271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.96428571428555" x2="13.96428571428555" y1="970.8061271428572" y2="998.7245271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.9642857142856" x2="103.9642857142856" y1="970.8061271428572" y2="998.7245271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.9642857142856" x2="193.9642857142856" y1="970.8061271428572" y2="970.8061271428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.9642857142856" x2="193.9642857142856" y1="998.7245271428571" y2="998.7245271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.9642857142856" x2="103.9642857142856" y1="970.8061271428572" y2="998.7245271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.9642857142856" x2="193.9642857142856" y1="970.8061271428572" y2="998.7245271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.9642857142857" x2="283.9642857142857" y1="970.8061271428572" y2="970.8061271428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.9642857142857" x2="283.9642857142857" y1="998.7245271428571" y2="998.7245271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.9642857142857" x2="193.9642857142857" y1="970.8061271428572" y2="998.7245271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.9642857142857" x2="283.9642857142857" y1="970.8061271428572" y2="998.7245271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.9642857142856" x2="373.9642857142856" y1="970.8061271428572" y2="970.8061271428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.9642857142856" x2="373.9642857142856" y1="998.7245271428571" y2="998.7245271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.9642857142856" x2="283.9642857142856" y1="970.8061271428572" y2="998.7245271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="373.9642857142856" x2="373.9642857142856" y1="970.8061271428572" y2="998.7245271428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.96428571428555" x2="103.9642857142856" y1="998.7244571428572" y2="998.7244571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.96428571428555" x2="103.9642857142856" y1="1026.642857142857" y2="1026.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.96428571428555" x2="13.96428571428555" y1="998.7244571428572" y2="1026.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.9642857142856" x2="103.9642857142856" y1="998.7244571428572" y2="1026.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.9642857142856" x2="193.9642857142856" y1="998.7244571428572" y2="998.7244571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.9642857142856" x2="193.9642857142856" y1="1026.642857142857" y2="1026.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.9642857142856" x2="103.9642857142856" y1="998.7244571428572" y2="1026.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.9642857142856" x2="193.9642857142856" y1="998.7244571428572" y2="1026.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.9642857142857" x2="283.9642857142857" y1="998.7244571428572" y2="998.7244571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.9642857142857" x2="283.9642857142857" y1="1026.642857142857" y2="1026.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.9642857142857" x2="193.9642857142857" y1="998.7244571428572" y2="1026.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.9642857142857" x2="283.9642857142857" y1="998.7244571428572" y2="1026.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.9642857142856" x2="373.9642857142856" y1="998.7244571428572" y2="998.7244571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.9642857142856" x2="373.9642857142856" y1="1026.642857142857" y2="1026.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.9642857142856" x2="283.9642857142856" y1="998.7244571428572" y2="1026.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="373.9642857142856" x2="373.9642857142856" y1="998.7244571428572" y2="1026.642857142857"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="373" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,63.4821,946.893) scale(1,1) translate(0,0)" writing-mode="lr" x="30.96" xml:space="preserve" y="952.89" zvalue="915">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="372" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,108.589,985.643) scale(1,1) translate(0,0)" writing-mode="lr" x="42.96" xml:space="preserve" y="991.64" zvalue="916">制图              段勇</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="371" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,284.964,985.643) scale(1,1) translate(0,0)" writing-mode="lr" x="205.71" xml:space="preserve" y="991.64" zvalue="917">绘制日期      20200716</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="370" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.7143,1013.64) scale(1,1) translate(0,0)" writing-mode="lr" x="57.71" xml:space="preserve" y="1019.64" zvalue="918">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="369" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.357,1013.64) scale(1,1) translate(0,0)" writing-mode="lr" x="240.36" xml:space="preserve" y="1019.64" zvalue="919">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="360" stroke="rgb(255,255,255)" text-anchor="middle" x="223.3671875" xml:space="preserve" y="493.5426587301588" zvalue="920">35kV     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="360" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="223.3671875" xml:space="preserve" y="509.5426587301588" zvalue="920">Ⅰ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="357" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,29.9444,525.736) scale(1,1) translate(1.7323e-15,0)" writing-mode="lr" x="29.94444056919679" xml:space="preserve" y="530.2361111111112" zvalue="922">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="356" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,29.9444,554.115) scale(1,1) translate(1.7323e-15,6.02289e-14)" writing-mode="lr" x="29.94444028158023" xml:space="preserve" y="558.6150793650794" zvalue="923">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="355" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,29.9444,579.615) scale(1,1) translate(1.7323e-15,6.306e-14)" writing-mode="lr" x="29.94444028158023" xml:space="preserve" y="584.1150793650794" zvalue="924">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="351" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,29.9444,605.115) scale(1,1) translate(1.7323e-15,-1.31782e-13)" writing-mode="lr" x="29.94444028158023" xml:space="preserve" y="609.6150793650794" zvalue="925">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="347" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,29.9444,630.615) scale(1,1) translate(1.7323e-15,-1.37444e-13)" writing-mode="lr" x="29.94444028158023" xml:space="preserve" y="635.1150793650794" zvalue="926">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="343" stroke="rgb(255,255,255)" text-anchor="middle" x="95.2578125" xml:space="preserve" y="493.3125" zvalue="927">110kV   </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="343" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="95.2578125" xml:space="preserve" y="509.3125" zvalue="927">Ⅰ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="338" stroke="rgb(255,255,255)" text-anchor="middle" x="161.9453125" xml:space="preserve" y="493.546875" zvalue="928">110kV   </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="338" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="161.9453125" xml:space="preserve" y="509.546875" zvalue="928">Ⅱ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="335" stroke="rgb(255,255,255)" text-anchor="middle" x="288.3671875" xml:space="preserve" y="490.75" zvalue="929">10kV     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="335" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="288.3671875" xml:space="preserve" y="506.75" zvalue="929">Ⅰ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="277" stroke="rgb(255,255,255)" text-anchor="middle" x="349.6171875" xml:space="preserve" y="492.171875" zvalue="930">10kV      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="277" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="349.6171875" xml:space="preserve" y="508.171875" zvalue="930">Ⅱ母</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428555" x2="198.7142857142856" y1="155.6428571428572" y2="155.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428555" x2="198.7142857142856" y1="181.6428571428572" y2="181.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428555" x2="17.71428571428555" y1="155.6428571428572" y2="181.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="198.7142857142856" y1="155.6428571428572" y2="181.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="379.7142857142856" y1="155.6428571428572" y2="155.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="379.7142857142856" y1="181.6428571428572" y2="181.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="198.7142857142856" y1="155.6428571428572" y2="181.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.7142857142856" x2="379.7142857142856" y1="155.6428571428572" y2="181.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428555" x2="198.7142857142856" y1="181.6428571428572" y2="181.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428555" x2="198.7142857142856" y1="205.8928571428572" y2="205.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428555" x2="17.71428571428555" y1="181.6428571428572" y2="205.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="198.7142857142856" y1="181.6428571428572" y2="205.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="379.7142857142856" y1="181.6428571428572" y2="181.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="379.7142857142856" y1="205.8928571428572" y2="205.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="198.7142857142856" y1="181.6428571428572" y2="205.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.7142857142856" x2="379.7142857142856" y1="181.6428571428572" y2="205.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428555" x2="198.7142857142856" y1="205.8928571428572" y2="205.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428555" x2="198.7142857142856" y1="230.1428571428572" y2="230.1428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428555" x2="17.71428571428555" y1="205.8928571428572" y2="230.1428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="198.7142857142856" y1="205.8928571428572" y2="230.1428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="379.7142857142856" y1="205.8928571428572" y2="205.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="379.7142857142856" y1="230.1428571428572" y2="230.1428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="198.7142857142856" y1="205.8928571428572" y2="230.1428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.7142857142856" x2="379.7142857142856" y1="205.8928571428572" y2="230.1428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428555" x2="198.7142857142856" y1="230.1428571428572" y2="230.1428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428555" x2="198.7142857142856" y1="252.8928571428572" y2="252.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428555" x2="17.71428571428555" y1="230.1428571428572" y2="252.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="198.7142857142856" y1="230.1428571428572" y2="252.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="379.7142857142856" y1="230.1428571428572" y2="230.1428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="379.7142857142856" y1="252.8928571428572" y2="252.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="198.7142857142856" y1="230.1428571428572" y2="252.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.7142857142856" x2="379.7142857142856" y1="230.1428571428572" y2="252.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428555" x2="198.7142857142856" y1="252.8928571428572" y2="252.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428555" x2="198.7142857142856" y1="275.6428571428572" y2="275.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428555" x2="17.71428571428555" y1="252.8928571428572" y2="275.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="198.7142857142856" y1="252.8928571428572" y2="275.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="379.7142857142856" y1="252.8928571428572" y2="252.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="379.7142857142856" y1="275.6428571428572" y2="275.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="198.7142857142856" y1="252.8928571428572" y2="275.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.7142857142856" x2="379.7142857142856" y1="252.8928571428572" y2="275.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428555" x2="198.7142857142856" y1="275.6428571428572" y2="275.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428555" x2="198.7142857142856" y1="298.3928571428572" y2="298.3928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428555" x2="17.71428571428555" y1="275.6428571428572" y2="298.3928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="198.7142857142856" y1="275.6428571428572" y2="298.3928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="379.7142857142856" y1="275.6428571428572" y2="275.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="379.7142857142856" y1="298.3928571428572" y2="298.3928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142856" x2="198.7142857142856" y1="275.6428571428572" y2="298.3928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.7142857142856" x2="379.7142857142856" y1="275.6428571428572" y2="298.3928571428572"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="182" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.7143,169.643) scale(1,1) translate(0,0)" writing-mode="lr" x="55.71" xml:space="preserve" y="175.14" zvalue="932">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.714,169.643) scale(1,1) translate(0,0)" writing-mode="lr" x="235.71" xml:space="preserve" y="175.14" zvalue="933">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.4018,194.893) scale(1,1) translate(0,0)" writing-mode="lr" x="58.4" xml:space="preserve" y="199.39" zvalue="934">110kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241.464,194.643) scale(1,1) translate(0,0)" writing-mode="lr" x="241.46" xml:space="preserve" y="199.14" zvalue="935">110kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.4643,265.143) scale(1,1) translate(0,0)" writing-mode="lr" x="74.45999999999999" xml:space="preserve" y="269.64" zvalue="936">110kV#1主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.2143,288.143) scale(1,1) translate(0,0)" writing-mode="lr" x="73.20999999999999" xml:space="preserve" y="292.64" zvalue="937">110kV#1主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.4018,219.893) scale(1,1) translate(0,0)" writing-mode="lr" x="58.4" xml:space="preserve" y="224.39" zvalue="938">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6518,242.393) scale(1,1) translate(0,0)" writing-mode="lr" x="59.65" xml:space="preserve" y="246.89" zvalue="940">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241.464,243.393) scale(1,1) translate(0,0)" writing-mode="lr" x="241.46" xml:space="preserve" y="247.89" zvalue="941">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,198.113,332.984) scale(1,1) translate(0,0)" writing-mode="lr" x="198.11" xml:space="preserve" y="337.48" zvalue="942">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,303.113,332.984) scale(1,1) translate(0,0)" writing-mode="lr" x="303.11" xml:space="preserve" y="337.48" zvalue="943">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.9643,674.893) scale(1,1) translate(0,0)" writing-mode="lr" x="83.96428571428555" xml:space="preserve" y="679.3928571428572" zvalue="945">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="407" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241.857,948.643) scale(1,1) translate(5.61931e-14,0)" writing-mode="lr" x="241.86" xml:space="preserve" y="954.64" zvalue="1113">ZhongShan-01-2024</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="243" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1487.23,870.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1487.23" xml:space="preserve" y="875" zvalue="1145">0319</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="461" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,277.235,425.5) scale(1,1) translate(0,-1.83631e-13)" writing-mode="lr" x="277.2350158691406" xml:space="preserve" y="430" zvalue="1150">小电流接地</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="352" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.2857,337.039) scale(1,1) translate(0,3.61866e-13)" writing-mode="lr" x="74.28570556640625" xml:space="preserve" y="341.5388641357421" zvalue="1151">全站公用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="465" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,331.643,1013.5) scale(1,1) translate(0,0)" writing-mode="lr" x="331.64" xml:space="preserve" y="1019.5" zvalue="1153">20240424</text>
  <ellipse cx="637.66" cy="265.16" fill="rgb(255,0,0)" fill-opacity="1" id="466" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1155"/>
  <ellipse cx="1066.66" cy="265.16" fill="rgb(255,0,0)" fill-opacity="1" id="468" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1159"/>
  <ellipse cx="905.66" cy="314.16" fill="rgb(255,0,0)" fill-opacity="1" id="497" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1205"/>
  <ellipse cx="526.66" cy="305.16" fill="rgb(255,0,0)" fill-opacity="1" id="498" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1207"/>
  <ellipse cx="1179.66" cy="305.16" fill="rgb(255,0,0)" fill-opacity="1" id="500" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1209"/>
  <ellipse cx="744.66" cy="429.16" fill="rgb(255,0,0)" fill-opacity="1" id="501" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1211"/>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="35.19" y="411.75" zvalue="1115"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="36.09" y="365.82" zvalue="1117"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="137.19" y="411.75" zvalue="1118"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="219">
   <path class="kv110" d="M 494.89 358.59 L 883.81 358.59" stroke-width="4" zvalue="32"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674410823683" ObjectName="110kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674410823683"/></metadata>
  <path d="M 494.89 358.59 L 883.81 358.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv10" d="M 418.75 710.59 L 969.75 710.59" stroke-width="4" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674410758147" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674410758147"/></metadata>
  <path d="M 418.75 710.59 L 969.75 710.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="489">
   <path class="kv35" d="M 1553.97 311.5 L 1553.97 572.5" stroke-width="4" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674410692611" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674410692611"/></metadata>
  <path d="M 1553.97 311.5 L 1553.97 572.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv110" d="M 936 357.34 L 1264.89 357.34" stroke-width="4" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674410627075" ObjectName="110kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674410627075"/></metadata>
  <path d="M 936 357.34 L 1264.89 357.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="737">
   <path class="kv10" d="M 1002.25 710.59 L 1591.25 710.59" stroke-width="4" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674410561539" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674410561539"/></metadata>
  <path d="M 1002.25 710.59 L 1591.25 710.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="218">
   <use class="kv110" height="40" transform="rotate(0,656.333,498.867) scale(0.727779,1.1) translate(240.053,-43.3515)" width="40" x="641.7777370545689" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="476.8668508673329" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454330220547" ObjectName="#1主变110kV侧中性点1010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454330220547"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,656.333,498.867) scale(0.727779,1.1) translate(240.053,-43.3515)" width="40" x="641.7777370545689" y="476.8668508673329"/></g>
  <g id="268">
   <use class="kv110" height="20" transform="rotate(270,755.02,451.414) scale(-1.24619,1.0068) translate(-1359.65,-2.98116)" width="10" x="748.7892224286517" xlink:href="#GroundDisconnector:地刀_0" y="441.3463545646036" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454329892867" ObjectName="#1主变110kV侧10167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454329892867"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,755.02,451.414) scale(-1.24619,1.0068) translate(-1359.65,-2.98116)" width="10" x="748.7892224286517" y="441.3463545646036"/></g>
  <g id="267">
   <use class="kv110" height="20" transform="rotate(270,755.02,403.414) scale(-1.24619,1.0068) translate(-1359.65,-2.65694)" width="10" x="748.7892366212945" xlink:href="#GroundDisconnector:地刀_0" y="393.3463545646038" zvalue="49"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454329761795" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454329761795"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,755.02,403.414) scale(-1.24619,1.0068) translate(-1359.65,-2.65694)" width="10" x="748.7892366212945" y="393.3463545646038"/></g>
  <g id="212">
   <use class="kv110" height="20" transform="rotate(90,575.124,331.37) scale(1.24619,-1.0068) translate(-112.386,-660.434)" width="10" x="568.8932423998623" xlink:href="#GroundDisconnector:地刀_0" y="321.3020701240744" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454748274690" ObjectName="110kVⅠ段母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454748274690"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,575.124,331.37) scale(1.24619,-1.0068) translate(-112.386,-660.434)" width="10" x="568.8932423998623" y="321.3020701240744"/></g>
  <g id="209">
   <use class="kv110" height="20" transform="rotate(90,575.124,279.37) scale(1.24619,-1.0068) translate(-112.386,-556.785)" width="10" x="568.8932423998623" xlink:href="#GroundDisconnector:地刀_0" y="269.3020701240744" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454748143618" ObjectName="110kVⅠ段母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454748143618"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,575.124,279.37) scale(1.24619,-1.0068) translate(-112.386,-556.785)" width="10" x="568.8932423998623" y="269.3020701240744"/></g>
  <g id="198">
   <use class="kv110" height="20" transform="rotate(90,1223.12,331.37) scale(1.24619,-1.0068) translate(-240.4,-660.434)" width="10" x="1216.893242399862" xlink:href="#GroundDisconnector:地刀_0" y="321.3020701240744" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454747881474" ObjectName="110kVⅡ段母线电压互感器19020接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454747881474"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1223.12,331.37) scale(1.24619,-1.0068) translate(-240.4,-660.434)" width="10" x="1216.893242399862" y="321.3020701240744"/></g>
  <g id="194">
   <use class="kv110" height="20" transform="rotate(90,1223.12,280.62) scale(1.24619,-1.0068) translate(-240.4,-559.277)" width="10" x="1216.893242399862" xlink:href="#GroundDisconnector:地刀_0" y="270.5520701240744" zvalue="84"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454747750402" ObjectName="110kVⅡ段母线电压互感器19027接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454747750402"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1223.12,280.62) scale(1.24619,-1.0068) translate(-240.4,-559.277)" width="10" x="1216.893242399862" y="270.5520701240744"/></g>
  <g id="180">
   <use class="kv35" height="20" transform="rotate(270,1751.23,370.291) scale(-1.24619,1.0068) translate(-3155.27,-2.4332)" width="10" x="1744.99699227505" xlink:href="#GroundDisconnector:地刀_0" y="360.2231865649849" zvalue="108"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454328713219" ObjectName="35kV备用Ⅰ回线36117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454328713219"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1751.23,370.291) scale(-1.24619,1.0068) translate(-3155.27,-2.4332)" width="10" x="1744.99699227505" y="360.2231865649849"/></g>
  <g id="628">
   <use class="kv110" height="20" transform="rotate(90,685.013,163.815) scale(1.24619,-1.0068) translate(-134.095,-326.455)" width="10" x="678.782127897909" xlink:href="#GroundDisconnector:地刀_0" y="153.7465078087487" zvalue="122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454328451075" ObjectName="110kV中弄线12267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454328451075"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,685.013,163.815) scale(1.24619,-1.0068) translate(-134.095,-326.455)" width="10" x="678.782127897909" y="153.7465078087487"/></g>
  <g id="556">
   <use class="kv110" height="20" transform="rotate(90,684.013,228.06) scale(1.24619,-1.0068) translate(-133.897,-454.512)" width="10" x="677.782127897909" xlink:href="#GroundDisconnector:地刀_0" y="217.9922963829675" zvalue="128"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454328254467" ObjectName="110kV中弄线12260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454328254467"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,684.013,228.06) scale(1.24619,-1.0068) translate(-133.897,-454.512)" width="10" x="677.782127897909" y="217.9922963829675"/></g>
  <g id="550">
   <use class="kv110" height="20" transform="rotate(90,684.013,300.1) scale(1.24619,-1.0068) translate(-133.897,-598.105)" width="10" x="677.782127897909" xlink:href="#GroundDisconnector:地刀_0" y="290.0322286339442" zvalue="131"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454328123395" ObjectName="110kV中弄线12217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454328123395"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,684.013,300.1) scale(1.24619,-1.0068) translate(-133.897,-598.105)" width="10" x="677.782127897909" y="290.0322286339442"/></g>
  <g id="164">
   <use class="kv110" height="20" transform="rotate(90,1110.01,163.815) scale(1.24619,-1.0068) translate(-218.054,-326.455)" width="10" x="1103.782127897909" xlink:href="#GroundDisconnector:地刀_0" y="153.7465078087487" zvalue="147"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454327730179" ObjectName="110kV万中线12367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454327730179"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1110.01,163.815) scale(1.24619,-1.0068) translate(-218.054,-326.455)" width="10" x="1103.782127897909" y="153.7465078087487"/></g>
  <g id="160">
   <use class="kv110" height="20" transform="rotate(90,1109.01,228.06) scale(1.24619,-1.0068) translate(-217.857,-454.512)" width="10" x="1102.782127897909" xlink:href="#GroundDisconnector:地刀_0" y="217.9922963829675" zvalue="153"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454327533571" ObjectName="110kV万中线12360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454327533571"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1109.01,228.06) scale(1.24619,-1.0068) translate(-217.857,-454.512)" width="10" x="1102.782127897909" y="217.9922963829675"/></g>
  <g id="158">
   <use class="kv110" height="20" transform="rotate(90,1109.01,300.1) scale(1.24619,-1.0068) translate(-217.857,-598.105)" width="10" x="1102.782127897909" xlink:href="#GroundDisconnector:地刀_0" y="290.0322286339442" zvalue="156"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454327402499" ObjectName="110kV万中线12327接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454327402499"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1109.01,300.1) scale(1.24619,-1.0068) translate(-217.857,-598.105)" width="10" x="1102.782127897909" y="290.0322286339442"/></g>
  <g id="785">
   <use class="kv110" height="20" transform="rotate(90,1013.59,303.851) scale(1.24619,-1.0068) translate(-199.006,-605.582)" width="10" x="1007.359697782887" xlink:href="#GroundDisconnector:地刀_0" y="293.7832301863497" zvalue="172"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454327009283" ObjectName="110kV分段11227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454327009283"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1013.59,303.851) scale(1.24619,-1.0068) translate(-199.006,-605.582)" width="10" x="1007.359697782887" y="293.7832301863497"/></g>
  <g id="784">
   <use class="kv110" height="20" transform="rotate(270,849.139,305.407) scale(-1.24619,1.0068) translate(-1529.3,-1.99492)" width="10" x="842.9077631538703" xlink:href="#GroundDisconnector:地刀_0" y="295.3387861833148" zvalue="174"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454326878211" ObjectName="110kV分段11217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454326878211"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,849.139,305.407) scale(-1.24619,1.0068) translate(-1529.3,-1.99492)" width="10" x="842.9077631538703" y="295.3387861833148"/></g>
  <g id="443">
   <use class="kv35" height="20" transform="rotate(270,1757.23,482.291) scale(-1.24619,1.0068) translate(-3166.08,-3.18973)" width="10" x="1750.99699227505" xlink:href="#GroundDisconnector:地刀_0" y="472.2231865649849" zvalue="209"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454326419459" ObjectName="35kV备用Ⅱ回线36217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454326419459"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1757.23,482.291) scale(-1.24619,1.0068) translate(-3166.08,-3.18973)" width="10" x="1750.99699227505" y="472.2231865649849"/></g>
  <g id="509">
   <use class="kv35" height="20" transform="rotate(270,1751.23,586.291) scale(-1.24619,1.0068) translate(-3155.27,-3.89221)" width="10" x="1744.99699227505" xlink:href="#GroundDisconnector:地刀_0" y="576.2231865649849" zvalue="223"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454326026242" ObjectName="35kV备用Ⅲ回线36317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454326026242"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1751.23,586.291) scale(-1.24619,1.0068) translate(-3155.27,-3.89221)" width="10" x="1744.99699227505" y="576.2231865649849"/></g>
  <g id="716">
   <use class="kv10" height="20" transform="rotate(90,569.124,639.37) scale(1.24619,-1.0068) translate(-111.201,-1274.35)" width="10" x="562.8932423998623" xlink:href="#GroundDisconnector:地刀_0" y="629.3020701240744" zvalue="242"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454747357186" ObjectName="10kVⅠ段母线电压互感器09017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454747357186"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,569.124,639.37) scale(1.24619,-1.0068) translate(-111.201,-1274.35)" width="10" x="562.8932423998623" y="629.3020701240744"/></g>
  <g id="816">
   <use class="kv10" height="20" transform="rotate(90,1355.12,639.37) scale(1.24619,-1.0068) translate(-266.477,-1274.35)" width="10" x="1348.893242399862" xlink:href="#GroundDisconnector:地刀_0" y="629.3020701240744" zvalue="251"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454747095042" ObjectName="10kVⅡ段母线电压互感器09027接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454747095042"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1355.12,639.37) scale(1.24619,-1.0068) translate(-266.477,-1274.35)" width="10" x="1348.893242399862" y="629.3020701240744"/></g>
  <g id="291">
   <use class="kv10" height="20" transform="rotate(90,895.013,792.1) scale(1.24619,-1.0068) translate(-175.581,-1578.78)" width="10" x="888.782127897909" xlink:href="#GroundDisconnector:地刀_0" y="782.0322286339442" zvalue="389"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454330941443" ObjectName="10kV1号站用变02617接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454330941443"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,895.013,792.1) scale(1.24619,-1.0068) translate(-175.581,-1578.78)" width="10" x="888.782127897909" y="782.0322286339442"/></g>
  <g id="99">
   <use class="kv10" height="20" transform="rotate(90,1558.26,790.85) scale(1.24619,-1.0068) translate(-306.607,-1576.29)" width="10" x="1552.032127897909" xlink:href="#GroundDisconnector:地刀_0" y="780.7822286339442" zvalue="450"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454331203587" ObjectName="10kV2号站用变03327接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454331203587"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1558.26,790.85) scale(1.24619,-1.0068) translate(-306.607,-1576.29)" width="10" x="1552.032127897909" y="780.7822286339442"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="493">
   <use class="kv35" height="26" transform="rotate(180,828.756,505.37) scale(-0.838049,0.927421) translate(-1818.64,38.6062)" width="12" x="823.7281781392438" xlink:href="#Accessory:避雷器1_0" y="493.3134417441267" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454330089475" ObjectName="#1主变35kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,828.756,505.37) scale(-0.838049,0.927421) translate(-1818.64,38.6062)" width="12" x="823.7281781392438" y="493.3134417441267"/></g>
  <g id="200">
   <use class="kv110" height="30" transform="rotate(0,548.422,232.473) scale(1.14365,-1.14365) translate(-66.731,-433.591)" width="30" x="531.2674845245494" xlink:href="#Accessory:避雷器PT带熔断器_0" y="215.3181745563496" zvalue="76"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454329237507" ObjectName="110kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,548.422,232.473) scale(1.14365,-1.14365) translate(-66.731,-433.591)" width="30" x="531.2674845245494" y="215.3181745563496"/></g>
  <g id="191">
   <use class="kv110" height="30" transform="rotate(0,1196.42,232.473) scale(1.14365,-1.14365) translate(-148.125,-433.591)" width="30" x="1179.267484524549" xlink:href="#Accessory:避雷器PT带熔断器_0" y="215.3181745563496" zvalue="88"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454328844291" ObjectName="110kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1196.42,232.473) scale(1.14365,-1.14365) translate(-148.125,-433.591)" width="30" x="1179.267484524549" y="215.3181745563496"/></g>
  <g id="187">
   <use class="kv35" height="30" transform="rotate(270,1433.25,374.584) scale(1.13333,-1.4) translate(-166.618,-636.144)" width="30" x="1416.25" xlink:href="#Accessory:避雷器PT带熔断器_0" y="353.5838423645081" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454328778755" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1433.25,374.584) scale(1.13333,-1.4) translate(-166.618,-636.144)" width="30" x="1416.25" y="353.5838423645081"/></g>
  <g id="535">
   <use class="kv110" height="26" transform="rotate(90,631.286,133.857) scale(-0.838049,0.927421) translate(-1385.54,9.53198)" width="12" x="626.2574222408354" xlink:href="#Accessory:避雷器1_0" y="121.8004864882414" zvalue="137"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454327992323" ObjectName="110kV中弄线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,631.286,133.857) scale(-0.838049,0.927421) translate(-1385.54,9.53198)" width="12" x="626.2574222408354" y="121.8004864882414"/></g>
  <g id="534">
   <use class="kv110" height="40" transform="rotate(180,691.708,125.489) scale(1.03601,1.03601) translate(-23.3236,-3.64178)" width="40" x="670.9880952380953" xlink:href="#Accessory:PT1111_0" y="104.7688936871886" zvalue="138"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454327926787" ObjectName="110kV中弄线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,691.708,125.489) scale(1.03601,1.03601) translate(-23.3236,-3.64178)" width="40" x="670.9880952380953" y="104.7688936871886"/></g>
  <g id="153">
   <use class="kv110" height="26" transform="rotate(90,1056.29,133.857) scale(-0.838049,0.927421) translate(-2317.67,9.53198)" width="12" x="1051.257422240835" xlink:href="#Accessory:避雷器1_0" y="121.8004864882414" zvalue="162"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454327271427" ObjectName="110kV万中线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1056.29,133.857) scale(-0.838049,0.927421) translate(-2317.67,9.53198)" width="12" x="1051.257422240835" y="121.8004864882414"/></g>
  <g id="152">
   <use class="kv110" height="40" transform="rotate(180,1116.71,125.494) scale(1.03601,1.03601) translate(-38.0967,-3.64195)" width="40" x="1095.988095238095" xlink:href="#Accessory:PT1111_0" y="104.7738095238097" zvalue="163"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454327205891" ObjectName="110kV万中线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,1116.71,125.494) scale(1.03601,1.03601) translate(-38.0967,-3.64195)" width="40" x="1095.988095238095" y="104.7738095238097"/></g>
  <g id="123">
   <use class="kv35" height="40" transform="rotate(90,1708.16,278.75) scale(-1.25,-1.25) translate(-3070.94,-496.75)" width="30" x="1689.409118652343" xlink:href="#Accessory:带熔断器的线路PT_0" y="253.75" zvalue="205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454326550531" ObjectName="35kV备用Ⅰ回线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1708.16,278.75) scale(-1.25,-1.25) translate(-3070.94,-496.75)" width="30" x="1689.409118652343" y="253.75"/></g>
  <g id="310">
   <use class="kv35" height="26" transform="rotate(270,1756.53,428.973) scale(-0.838049,0.927421) translate(-3853.48,32.6275)" width="12" x="1751.504379279" xlink:href="#Accessory:避雷器1_0" y="416.9165281329889" zvalue="215"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454326288386" ObjectName="35kV备用Ⅱ回线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1756.53,428.973) scale(-0.838049,0.927421) translate(-3853.48,32.6275)" width="12" x="1751.504379279" y="416.9165281329889"/></g>
  <g id="251">
   <use class="kv35" height="40" transform="rotate(90,1708.16,391.659) scale(-1.25,-1.25) translate(-3070.94,-699.986)" width="30" x="1689.409118598158" xlink:href="#Accessory:带熔断器的线路PT_0" y="366.6590909090909" zvalue="219"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454326157314" ObjectName="35kV备用Ⅱ回线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1708.16,391.659) scale(-1.25,-1.25) translate(-3070.94,-699.986)" width="30" x="1689.409118598158" y="366.6590909090909"/></g>
  <g id="488">
   <use class="kv35" height="26" transform="rotate(270,1750.53,532.973) scale(-0.838049,0.927421) translate(-3840.32,40.7664)" width="12" x="1745.504379279" xlink:href="#Accessory:避雷器1_0" y="520.9165281329889" zvalue="229"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454325895170" ObjectName="35kV备用Ⅲ回线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1750.53,532.973) scale(-0.838049,0.927421) translate(-3840.32,40.7664)" width="12" x="1745.504379279" y="520.9165281329889"/></g>
  <g id="481">
   <use class="kv35" height="40" transform="rotate(90,1708.16,498.386) scale(-1.25,-1.25) translate(-3070.94,-892.095)" width="30" x="1689.409118652344" xlink:href="#Accessory:带熔断器的线路PT_0" y="473.3863636363636" zvalue="233"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454325764098" ObjectName="35kV备用Ⅲ回线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1708.16,498.386) scale(-1.25,-1.25) translate(-3070.94,-892.095)" width="30" x="1689.409118652344" y="473.3863636363636"/></g>
  <g id="511">
   <use class="kv10" height="26" transform="rotate(270,757.436,580.05) scale(-0.838049,0.927421) translate(-1662.22,44.4505)" width="12" x="752.4078409619566" xlink:href="#Accessory:避雷器1_0" y="567.9931045668395" zvalue="236"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454325633026" ObjectName="#1主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,757.436,580.05) scale(-0.838049,0.927421) translate(-1662.22,44.4505)" width="12" x="752.4078409619566" y="567.9931045668395"/></g>
  <g id="636">
   <use class="kv10" height="30" transform="rotate(0,542.422,592.473) scale(1.14365,-1.14365) translate(-65.9774,-1108.37)" width="30" x="525.2674845245494" xlink:href="#Accessory:避雷器PT带熔断器_0" y="575.3181745563495" zvalue="246"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454325370882" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,542.422,592.473) scale(1.14365,-1.14365) translate(-65.9774,-1108.37)" width="30" x="525.2674845245494" y="575.3181745563495"/></g>
  <g id="812">
   <use class="kv10" height="30" transform="rotate(0,1328.42,592.473) scale(1.14365,-1.14365) translate(-164.705,-1108.37)" width="30" x="1311.267484524549" xlink:href="#Accessory:避雷器PT带熔断器_0" y="575.3181745563495" zvalue="255"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454332579843" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1328.42,592.473) scale(1.14365,-1.14365) translate(-164.705,-1108.37)" width="30" x="1311.267484524549" y="575.3181745563495"/></g>
  <g id="925">
   <use class="kv35" height="26" transform="rotate(270,1750.44,317.266) scale(-0.838049,0.927421) translate(-3840.11,23.8854)" width="12" x="1745.406818303391" xlink:href="#Accessory:避雷器1_0" y="305.2092110598181" zvalue="315"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454324518914" ObjectName="35kV备用Ⅰ回线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1750.44,317.266) scale(-0.838049,0.927421) translate(-3840.11,23.8854)" width="12" x="1745.406818303391" y="305.2092110598181"/></g>
  <g id="350">
   <use class="kv10" height="18" transform="rotate(0,1464.88,905.659) scale(1.84951,1.68431) translate(-666.896,-361.796)" width="14" x="1451.928409090909" xlink:href="#Accessory:PT7_0" y="890.5" zvalue="471"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454332514307" ObjectName="10kV黄家寨线PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1464.88,905.659) scale(1.84951,1.68431) translate(-666.896,-361.796)" width="14" x="1451.928409090909" y="890.5"/></g>
 </g>
 <g id="BreakerClass">
  <g id="276">
   <use class="kv110" height="20" transform="rotate(180,728.588,427.8) scale(1.5542,1.35421) translate(-257.03,-108.354)" width="10" x="720.8167107229006" xlink:href="#Breaker:开关_0" y="414.2576053031424" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925158305795" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925158305795"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,728.588,427.8) scale(1.5542,1.35421) translate(-257.03,-108.354)" width="10" x="720.8167107229006" y="414.2576053031424"/></g>
  <g id="495">
   <use class="kv35" height="20" transform="rotate(90,1340.54,488.52) scale(2.36188,2.45) translate(-766.161,-274.624)" width="10" x="1328.734616224441" xlink:href="#Breaker:小车断路器_0" y="464.0200805664061" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925158240259" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925158240259"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1340.54,488.52) scale(2.36188,2.45) translate(-766.161,-274.624)" width="10" x="1328.734616224441" y="464.0200805664061"/></g>
  <g id="631">
   <use class="kv110" height="20" transform="rotate(0,656.544,264.52) scale(1.5542,1.35421) translate(-231.341,-65.6464)" width="10" x="648.7730095510258" xlink:href="#Breaker:开关_0" y="250.97797585463" zvalue="117"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925158174723" ObjectName="110kV中弄线122断路器"/>
   <cge:TPSR_Ref TObjectID="6473925158174723"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,656.544,264.52) scale(1.5542,1.35421) translate(-231.341,-65.6464)" width="10" x="648.7730095510258" y="250.97797585463"/></g>
  <g id="166">
   <use class="kv110" height="20" transform="rotate(0,1081.54,263.52) scale(1.5542,1.35421) translate(-382.888,-65.3848)" width="10" x="1073.773009551026" xlink:href="#Breaker:开关_0" y="249.97797585463" zvalue="142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925158109187" ObjectName="110kV万中线123断路器"/>
   <cge:TPSR_Ref TObjectID="6473925158109187"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1081.54,263.52) scale(1.5542,1.35421) translate(-382.888,-65.3848)" width="10" x="1073.773009551026" y="249.97797585463"/></g>
  <g id="791">
   <use class="kv110" height="20" transform="rotate(90,904.544,278.479) scale(1.5542,1.35421) translate(-319.773,-69.2975)" width="10" x="896.7730095510257" xlink:href="#Breaker:母联开关_0" y="264.9368984915924" zvalue="166"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925158043651" ObjectName="110kV分段112断路器"/>
   <cge:TPSR_Ref TObjectID="6473925158043651"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,904.544,278.479) scale(1.5542,1.35421) translate(-319.773,-69.2975)" width="10" x="896.7730095510257" y="264.9368984915924"/></g>
  <g id="142">
   <use class="kv10" height="20" transform="rotate(180,727.999,642.401) scale(1.5542,1.35421) translate(-256.82,-164.486)" width="10" x="720.2281084866613" xlink:href="#Breaker:开关_0" y="628.8590039045411" zvalue="183"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925157978115" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925157978115"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,727.999,642.401) scale(1.5542,1.35421) translate(-256.82,-164.486)" width="10" x="720.2281084866613" y="628.8590039045411"/></g>
  <g id="441">
   <use class="kv35" height="20" transform="rotate(90,1601.5,456.029) scale(2.36188,2.45) translate(-916.63,-255.395)" width="10" x="1589.690579810354" xlink:href="#Breaker:小车断路器_0" y="431.5294740754765" zvalue="210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925157912579" ObjectName="35kV备用Ⅱ回线362断路器"/>
   <cge:TPSR_Ref TObjectID="6473925157912579"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1601.5,456.029) scale(2.36188,2.45) translate(-916.63,-255.395)" width="10" x="1589.690579810354" y="431.5294740754765"/></g>
  <g id="508">
   <use class="kv35" height="20" transform="rotate(90,1601.5,560.029) scale(2.36188,2.45) translate(-916.63,-316.946)" width="10" x="1589.690579333517" xlink:href="#Breaker:小车断路器_0" y="535.5294740754765" zvalue="224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925157847043" ObjectName="35kV备用Ⅲ回线363断路器"/>
   <cge:TPSR_Ref TObjectID="6473925157847043"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1601.5,560.029) scale(2.36188,2.45) translate(-916.63,-316.946)" width="10" x="1589.690579333517" y="535.5294740754765"/></g>
  <g id="824">
   <use class="kv10" height="20" transform="rotate(0,762.519,784.52) scale(1.5542,1.35421) translate(-269.129,-201.659)" width="10" x="754.7477889531197" xlink:href="#Breaker:开关_0" y="770.9779758546299" zvalue="257"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925157781507" ObjectName="10kV备用Ⅱ线024断路器"/>
   <cge:TPSR_Ref TObjectID="6473925157781507"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,762.519,784.52) scale(1.5542,1.35421) translate(-269.129,-201.659)" width="10" x="754.7477889531197" y="770.9779758546299"/></g>
  <g id="917">
   <use class="kv10" height="20" transform="rotate(0,660.549,784.52) scale(1.5542,1.35421) translate(-232.769,-201.659)" width="10" x="652.7778025714097" xlink:href="#Breaker:开关_0" y="770.9779758546299" zvalue="294"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925157715971" ObjectName="10kV小街线023断路器"/>
   <cge:TPSR_Ref TObjectID="6473925157715971"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,660.549,784.52) scale(1.5542,1.35421) translate(-232.769,-201.659)" width="10" x="652.7778025714097" y="770.9779758546299"/></g>
  <g id="261">
   <use class="kv10" height="20" transform="rotate(0,472.544,784.52) scale(1.5542,1.35421) translate(-165.73,-201.659)" width="10" x="464.7730095510257" xlink:href="#Breaker:开关_0" y="770.9779758546299" zvalue="330"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925158371331" ObjectName="10kV备用Ⅰ线021断路器"/>
   <cge:TPSR_Ref TObjectID="6473925158371331"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,472.544,784.52) scale(1.5542,1.35421) translate(-165.73,-201.659)" width="10" x="464.7730095510257" y="770.9779758546299"/></g>
  <g id="281">
   <use class="kv10" height="20" transform="rotate(0,566.544,784.52) scale(1.5542,1.35421) translate(-199.248,-201.659)" width="10" x="558.7730095510258" xlink:href="#Breaker:开关_0" y="770.9779758546299" zvalue="342"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925158436867" ObjectName="10kV康丰糖厂线022断路器"/>
   <cge:TPSR_Ref TObjectID="6473925158436867"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,566.544,784.52) scale(1.5542,1.35421) translate(-199.248,-201.659)" width="10" x="558.7730095510258" y="770.9779758546299"/></g>
  <g id="330">
   <use class="kv10" height="20" transform="rotate(0,1416.52,784.52) scale(1.5542,1.35421) translate(-502.334,-201.659)" width="10" x="1408.74778895312" xlink:href="#Breaker:开关_0" y="770.9779758546299" zvalue="395"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925158699011" ObjectName="10kV黄家寨线031断路器"/>
   <cge:TPSR_Ref TObjectID="6473925158699011"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1416.52,784.52) scale(1.5542,1.35421) translate(-502.334,-201.659)" width="10" x="1408.74778895312" y="770.9779758546299"/></g>
  <g id="322">
   <use class="kv10" height="20" transform="rotate(0,1227.66,784.52) scale(1.5542,1.35421) translate(-434.99,-201.659)" width="10" x="1219.888913682521" xlink:href="#Breaker:开关_0" y="770.9779758546298" zvalue="404"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925158633475" ObjectName="10kV中万线028断路器"/>
   <cge:TPSR_Ref TObjectID="6473925158633475"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1227.66,784.52) scale(1.5542,1.35421) translate(-434.99,-201.659)" width="10" x="1219.888913682521" y="770.9779758546298"/></g>
  <g id="309">
   <use class="kv10" height="20" transform="rotate(0,1127.66,784.52) scale(1.5542,1.35421) translate(-399.33,-201.659)" width="10" x="1119.884120662137" xlink:href="#Breaker:开关_0" y="770.9779758546298" zvalue="421"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925158567939" ObjectName="10kV赛岗线027断路器"/>
   <cge:TPSR_Ref TObjectID="6473925158567939"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1127.66,784.52) scale(1.5542,1.35421) translate(-399.33,-201.659)" width="10" x="1119.884120662137" y="770.9779758546298"/></g>
  <g id="299">
   <use class="kv10" height="20" transform="rotate(0,1320.54,784.52) scale(1.5542,1.35421) translate(-468.111,-201.659)" width="10" x="1312.773009551026" xlink:href="#Breaker:开关_0" y="770.9779758546299" zvalue="432"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925158502403" ObjectName="10kV备用Ⅳ线029断路器"/>
   <cge:TPSR_Ref TObjectID="6473925158502403"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1320.54,784.52) scale(1.5542,1.35421) translate(-468.111,-201.659)" width="10" x="1312.773009551026" y="770.9779758546299"/></g>
  <g id="346">
   <use class="kv10" height="20" transform="rotate(90,985.544,637.979) scale(1.5542,1.35421) translate(-348.656,-163.329)" width="10" x="977.7730095510257" xlink:href="#Breaker:母联开关_0" y="624.4368984915924" zvalue="457"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925158764547" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473925158764547"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,985.544,637.979) scale(1.5542,1.35421) translate(-348.656,-163.329)" width="10" x="977.7730095510257" y="624.4368984915924"/></g>
  <g id="359">
   <use class="kv35" height="20" transform="rotate(90,1601.5,342.691) scale(2.36188,2.45) translate(-916.63,-188.317)" width="10" x="1589.690579359206" xlink:href="#Breaker:小车断路器_0" y="318.190579359206" zvalue="477"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925158830083" ObjectName="35kV备用Ⅰ回线361断路器"/>
   <cge:TPSR_Ref TObjectID="6473925158830083"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1601.5,342.691) scale(2.36188,2.45) translate(-916.63,-188.317)" width="10" x="1589.690579359206" y="318.190579359206"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="275">
   <use class="kv110" height="30" transform="rotate(180,728.582,470.141) scale(-0.947693,-0.6712) translate(-1497.77,-1175.52)" width="15" x="721.4738401871448" xlink:href="#Disconnector:刀闸_0" y="460.0730764122642" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454330023939" ObjectName="#1主变110kV侧1016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454330023939"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,728.582,470.141) scale(-0.947693,-0.6712) translate(-1497.77,-1175.52)" width="15" x="721.4738401871448" y="460.0730764122642"/></g>
  <g id="272">
   <use class="kv110" height="30" transform="rotate(0,728.588,382.872) scale(0.947693,0.6712) translate(39.8215,182.625)" width="15" x="721.4800105358408" xlink:href="#Disconnector:刀闸_0" y="372.8043387992516" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454329958403" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454329958403"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,728.588,382.872) scale(0.947693,0.6712) translate(39.8215,182.625)" width="15" x="721.4800105358408" y="372.8043387992516"/></g>
  <g id="1453">
   <use class="kv110" height="30" transform="rotate(180,547.76,305.555) scale(-0.947693,-0.6712) translate(-1126.15,-765.724)" width="15" x="540.6524010422278" xlink:href="#Disconnector:刀闸_0" y="295.48702175083" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454748340226" ObjectName="110kVⅠ段母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454748340226"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,547.76,305.555) scale(-0.947693,-0.6712) translate(-1126.15,-765.724)" width="15" x="540.6524010422278" y="295.48702175083"/></g>
  <g id="499">
   <use class="kv35" height="30" transform="rotate(270,1276.36,488.52) scale(-0.947693,0.6712) translate(-2623.56,234.378)" width="15" x="1269.254119097402" xlink:href="#Disconnector:刀闸_0" y="478.4520742734616" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454329303043" ObjectName="#1主变35kV侧3016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454329303043"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1276.36,488.52) scale(-0.947693,0.6712) translate(-2623.56,234.378)" width="15" x="1269.254119097402" y="478.4520742734616"/></g>
  <g id="199">
   <use class="kv110" height="30" transform="rotate(180,1196.07,305.555) scale(-0.947693,-0.6712) translate(-2458.55,-765.724)" width="15" x="1188.963799779359" xlink:href="#Disconnector:刀闸_0" y="295.48702175083" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454747947010" ObjectName="110kVⅡ段母线电压互感器1902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454747947010"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1196.07,305.555) scale(-0.947693,-0.6712) translate(-2458.55,-765.724)" width="15" x="1188.963799779359" y="295.48702175083"/></g>
  <g id="629">
   <use class="kv110" height="30" transform="rotate(0,656.556,199.088) scale(0.947693,0.6712) translate(35.8457,92.5947)" width="15" x="649.4479061407624" xlink:href="#Disconnector:刀闸_0" y="189.0197927208583" zvalue="120"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454328516611" ObjectName="110kV中弄线1226隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454328516611"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,656.556,199.088) scale(0.947693,0.6712) translate(35.8457,92.5947)" width="15" x="649.4479061407624" y="189.0197927208583"/></g>
  <g id="625">
   <use class="kv110" height="30" transform="rotate(180,656.544,330.357) scale(-0.947693,-0.6712) translate(-1349.72,-827.476)" width="15" x="649.4363094859999" xlink:href="#Disconnector:刀闸_0" y="320.288530513827" zvalue="125"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454328320003" ObjectName="110kV中弄线1221隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454328320003"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,656.544,330.357) scale(-0.947693,-0.6712) translate(-1349.72,-827.476)" width="15" x="649.4363094859999" y="320.288530513827"/></g>
  <g id="165">
   <use class="kv110" height="30" transform="rotate(0,1081.56,199.088) scale(0.947693,0.6712) translate(59.3032,92.5947)" width="15" x="1074.447906140762" xlink:href="#Disconnector:刀闸_0" y="189.0197927208583" zvalue="145"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454327795715" ObjectName="110kV万中线1236隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454327795715"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1081.56,199.088) scale(0.947693,0.6712) translate(59.3032,92.5947)" width="15" x="1074.447906140762" y="189.0197927208583"/></g>
  <g id="162">
   <use class="kv110" height="30" transform="rotate(180,1081.54,330.357) scale(-0.947693,-0.6712) translate(-2223.18,-827.476)" width="15" x="1074.436309486" xlink:href="#Disconnector:刀闸_0" y="320.288530513827" zvalue="150"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454327599107" ObjectName="110kV万中线1232隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454327599107"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1081.54,330.357) scale(-0.947693,-0.6712) translate(-2223.18,-827.476)" width="15" x="1074.436309486" y="320.288530513827"/></g>
  <g id="790">
   <use class="kv110" height="30" transform="rotate(0,821.916,322.877) scale(0.947693,0.6712) translate(44.9727,153.235)" width="15" x="814.8087777749481" xlink:href="#Disconnector:刀闸_0" y="312.8087331505459" zvalue="168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454327140355" ObjectName="110kV分段1121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454327140355"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,821.916,322.877) scale(0.947693,0.6712) translate(44.9727,153.235)" width="15" x="814.8087777749481" y="312.8087331505459"/></g>
  <g id="787">
   <use class="kv110" height="30" transform="rotate(0,989.146,322.877) scale(0.947693,0.6712) translate(54.2028,153.235)" width="15" x="982.0384899433236" xlink:href="#Disconnector:刀闸_0" y="312.8087360040722" zvalue="170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454327074819" ObjectName="110kV分段1122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454327074819"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,989.146,322.877) scale(0.947693,0.6712) translate(54.2028,153.235)" width="15" x="982.0384899433236" y="312.8087360040722"/></g>
  <g id="141">
   <use class="kv10" height="30" transform="rotate(180,728.351,610.372) scale(-0.947693,-0.6712) translate(-1497.29,-1524.68)" width="15" x="721.2430709563755" xlink:href="#Disconnector:刀闸_0" y="600.3038456430335" zvalue="185"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454326747139" ObjectName="#1主变10kV侧0016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454326747139"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,728.351,610.372) scale(-0.947693,-0.6712) translate(-1497.29,-1524.68)" width="15" x="721.2430709563755" y="600.3038456430335"/></g>
  <g id="140">
   <use class="kv10" height="30" transform="rotate(180,727.582,674.141) scale(-0.947693,-0.6712) translate(-1495.71,-1683.45)" width="15" x="720.4738401871448" xlink:href="#Disconnector:刀闸_0" y="664.0730764122643" zvalue="187"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454326681603" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454326681603"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,727.582,674.141) scale(-0.947693,-0.6712) translate(-1495.71,-1683.45)" width="15" x="720.4738401871448" y="664.0730764122643"/></g>
  <g id="126">
   <use class="kv35" height="26" transform="rotate(90,1504.5,375.826) scale(1.18462,1.107) translate(-233.361,-34.9369)" width="12" x="1497.392303114711" xlink:href="#Disconnector:单手车刀闸1212_0" y="361.4348314508171" zvalue="201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454326616067" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454326616067"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1504.5,375.826) scale(1.18462,1.107) translate(-233.361,-34.9369)" width="12" x="1497.392303114711" y="361.4348314508171"/></g>
  <g id="128">
   <use class="kv35" height="26" transform="rotate(180,1670.05,315.073) scale(-1.18462,-1.107) translate(-3078.72,-598.299)" width="12" x="1662.942412994037" xlink:href="#Disconnector:单手车刀闸1212_0" y="300.6818181818182" zvalue="206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454326484995" ObjectName="35kV备用Ⅰ回线3616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454326484995"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1670.05,315.073) scale(-1.18462,-1.107) translate(-3078.72,-598.299)" width="12" x="1662.942412994037" y="300.6818181818182"/></g>
  <g id="197">
   <use class="kv35" height="26" transform="rotate(180,1670.05,431.396) scale(-1.18462,-1.107) translate(-3078.72,-819.701)" width="12" x="1662.942412977992" xlink:href="#Disconnector:单手车刀闸1212_0" y="417.0048745117787" zvalue="221"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454326091778" ObjectName="35kV备用Ⅱ回线3626隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454326091778"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1670.05,431.396) scale(-1.18462,-1.107) translate(-3078.72,-819.701)" width="12" x="1662.942412977992" y="417.0048745117787"/></g>
  <g id="463">
   <use class="kv35" height="26" transform="rotate(180,1670.05,533.411) scale(-1.18462,-1.107) translate(-3078.72,-1013.87)" width="12" x="1662.942412853791" xlink:href="#Disconnector:单手车刀闸1212_0" y="519.0195803941316" zvalue="235"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454325698562" ObjectName="35kV备用Ⅲ回线3636隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454325698562"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1670.05,533.411) scale(-1.18462,-1.107) translate(-3078.72,-1013.87)" width="12" x="1662.942412853791" y="519.0195803941316"/></g>
  <g id="794">
   <use class="kv10" height="30" transform="rotate(180,541.043,665.555) scale(-0.947693,-0.6712) translate(-1112.34,-1662.08)" width="15" x="533.9352262884172" xlink:href="#Disconnector:刀闸_0" y="655.48702175083" zvalue="239"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454747422722" ObjectName="10kVⅠ段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454747422722"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,541.043,665.555) scale(-0.947693,-0.6712) translate(-1112.34,-1662.08)" width="15" x="533.9352262884172" y="655.48702175083"/></g>
  <g id="820">
   <use class="kv10" height="30" transform="rotate(180,1327.76,665.555) scale(-0.947693,-0.6712) translate(-2729.2,-1662.08)" width="15" x="1320.652401042228" xlink:href="#Disconnector:刀闸_0" y="655.48702175083" zvalue="248"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454747160578" ObjectName="10kVⅡ段母线电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454747160578"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1327.76,665.555) scale(-0.947693,-0.6712) translate(-2729.2,-1662.08)" width="15" x="1320.652401042228" y="655.48702175083"/></g>
  <g id="829">
   <use class="kv10" height="30" transform="rotate(180,762.514,874.357) scale(-0.947693,-0.6712) translate(-1567.51,-2181.96)" width="15" x="755.4062958677098" xlink:href="#Disconnector:刀闸_0" y="864.288530513827" zvalue="258"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454325043202" ObjectName="10kV备用Ⅱ线0248隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454325043202"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,762.514,874.357) scale(-0.947693,-0.6712) translate(-1567.51,-2181.96)" width="15" x="755.4062958677098" y="864.288530513827"/></g>
  <g id="832">
   <use class="kv10" height="30" transform="rotate(180,762.409,744.801) scale(-0.947693,-0.6712) translate(-1567.29,-1859.39)" width="15" x="755.3013171275894" xlink:href="#Disconnector:刀闸_0" y="734.7329749582715" zvalue="259"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454324977666" ObjectName="10kV备用Ⅱ线0241隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454324977666"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,762.409,744.801) scale(-0.947693,-0.6712) translate(-1567.29,-1859.39)" width="15" x="755.3013171275894" y="734.7329749582715"/></g>
  <g id="916">
   <use class="kv10" height="30" transform="rotate(180,660.569,827.84) scale(-0.947693,-0.6712) translate(-1357.99,-2066.15)" width="15" x="653.4615300839059" xlink:href="#Disconnector:刀闸_0" y="817.7724014815689" zvalue="295"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454324846594" ObjectName="10kV小街线0236隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454324846594"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,660.569,827.84) scale(-0.947693,-0.6712) translate(-1357.99,-2066.15)" width="15" x="653.4615300839059" y="817.7724014815689"/></g>
  <g id="914">
   <use class="kv10" height="30" transform="rotate(180,660.544,874.357) scale(-0.947693,-0.6712) translate(-1357.94,-2181.96)" width="15" x="653.4363094859999" xlink:href="#Disconnector:刀闸_0" y="864.288530513827" zvalue="298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454324781058" ObjectName="10kV小街线0238隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454324781058"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,660.544,874.357) scale(-0.947693,-0.6712) translate(-1357.94,-2181.96)" width="15" x="653.4363094859999" y="864.288530513827"/></g>
  <g id="912">
   <use class="kv10" height="30" transform="rotate(180,660.439,744.801) scale(-0.947693,-0.6712) translate(-1357.72,-1859.39)" width="15" x="653.3313307458794" xlink:href="#Disconnector:刀闸_0" y="734.7329749582715" zvalue="300"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454324715522" ObjectName="10kV小街线0231隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454324715522"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,660.439,744.801) scale(-0.947693,-0.6712) translate(-1357.72,-1859.39)" width="15" x="653.3313307458794" y="734.7329749582715"/></g>
  <g id="919">
   <use class="kv10" height="30" transform="rotate(180,762.539,827.84) scale(-0.947693,-0.6712) translate(-1567.56,-2066.15)" width="15" x="755.4315164656159" xlink:href="#Disconnector:刀闸_0" y="817.7724014815689" zvalue="309"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454324584450" ObjectName="10kV备用Ⅱ线0246隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454324584450"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,762.539,827.84) scale(-0.947693,-0.6712) translate(-1567.56,-2066.15)" width="15" x="755.4315164656159" y="817.7724014815689"/></g>
  <g id="260">
   <use class="kv10" height="30" transform="rotate(180,472.544,874.357) scale(-0.947693,-0.6712) translate(-971.562,-2181.96)" width="15" x="465.4363094859999" xlink:href="#Disconnector:刀闸_0" y="864.288530513827" zvalue="331"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454330482691" ObjectName="10kV备用Ⅰ线0218隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454330482691"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,472.544,874.357) scale(-0.947693,-0.6712) translate(-971.562,-2181.96)" width="15" x="465.4363094859999" y="864.288530513827"/></g>
  <g id="259">
   <use class="kv10" height="30" transform="rotate(180,472.409,744.801) scale(-0.947693,-0.6712) translate(-971.285,-1859.39)" width="15" x="465.3013171275894" xlink:href="#Disconnector:刀闸_0" y="734.7329749582715" zvalue="332"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454330417155" ObjectName="10kV备用Ⅰ线0211隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454330417155"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,472.409,744.801) scale(-0.947693,-0.6712) translate(-971.285,-1859.39)" width="15" x="465.3013171275894" y="734.7329749582715"/></g>
  <g id="253">
   <use class="kv10" height="30" transform="rotate(180,472.544,827.84) scale(-0.947693,-0.6712) translate(-971.562,-2066.15)" width="15" x="465.4363094859999" xlink:href="#Disconnector:刀闸_0" y="817.7724014815689" zvalue="339"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454330286083" ObjectName="10kV备用Ⅰ线0216隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454330286083"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,472.544,827.84) scale(-0.947693,-0.6712) translate(-971.562,-2066.15)" width="15" x="465.4363094859999" y="817.7724014815689"/></g>
  <g id="280">
   <use class="kv10" height="30" transform="rotate(180,566.544,827.84) scale(-0.947693,-0.6712) translate(-1164.75,-2066.15)" width="15" x="559.4363094859999" xlink:href="#Disconnector:刀闸_0" y="817.7724014815689" zvalue="343"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454330744835" ObjectName="10kV康丰糖厂线0226隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454330744835"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,566.544,827.84) scale(-0.947693,-0.6712) translate(-1164.75,-2066.15)" width="15" x="559.4363094859999" y="817.7724014815689"/></g>
  <g id="279">
   <use class="kv10" height="30" transform="rotate(180,566.544,874.357) scale(-0.947693,-0.6712) translate(-1164.75,-2181.96)" width="15" x="559.4363094859999" xlink:href="#Disconnector:刀闸_0" y="864.288530513827" zvalue="346"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454330679299" ObjectName="10kV康丰糖厂线0228隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454330679299"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,566.544,874.357) scale(-0.947693,-0.6712) translate(-1164.75,-2181.96)" width="15" x="559.4363094859999" y="864.288530513827"/></g>
  <g id="278">
   <use class="kv10" height="30" transform="rotate(180,566.409,744.801) scale(-0.947693,-0.6712) translate(-1164.47,-1859.39)" width="15" x="559.3013171275894" xlink:href="#Disconnector:刀闸_0" y="734.7329749582715" zvalue="348"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454330613763" ObjectName="10kV康丰糖厂线0221隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454330613763"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,566.409,744.801) scale(-0.947693,-0.6712) translate(-1164.47,-1859.39)" width="15" x="559.3013171275894" y="734.7329749582715"/></g>
  <g id="286">
   <use class="kv10" height="30" transform="rotate(180,856.409,744.801) scale(-0.947693,-0.6712) translate(-1760.48,-1859.39)" width="15" x="849.3013171275894" xlink:href="#Disconnector:刀闸_0" y="734.7329749582715" zvalue="386"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454330810371" ObjectName="10kV1号站用变0261隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454330810371"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,856.409,744.801) scale(-0.947693,-0.6712) translate(-1760.48,-1859.39)" width="15" x="849.3013171275894" y="734.7329749582715"/></g>
  <g id="329">
   <use class="kv10" height="30" transform="rotate(180,1416.51,878.357) scale(-0.947693,-0.6712) translate(-2911.6,-2191.92)" width="15" x="1409.40629586771" xlink:href="#Disconnector:刀闸_0" y="868.288530513827" zvalue="396"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454332317699" ObjectName="10kV黄家寨线0318隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454332317699"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1416.51,878.357) scale(-0.947693,-0.6712) translate(-2911.6,-2191.92)" width="15" x="1409.40629586771" y="868.288530513827"/></g>
  <g id="328">
   <use class="kv10" height="30" transform="rotate(180,1416.41,744.801) scale(-0.947693,-0.6712) translate(-2911.39,-1859.39)" width="15" x="1409.301317127589" xlink:href="#Disconnector:刀闸_0" y="734.7329749582715" zvalue="397"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454332252163" ObjectName="10kV黄家寨线0312隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454332252163"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1416.41,744.801) scale(-0.947693,-0.6712) translate(-2911.39,-1859.39)" width="15" x="1409.301317127589" y="734.7329749582715"/></g>
  <g id="321">
   <use class="kv10" height="30" transform="rotate(180,1226.57,831.84) scale(-0.947693,-0.6712) translate(-2521.23,-2076.1)" width="15" x="1219.461530083906" xlink:href="#Disconnector:刀闸_0" y="821.7724014815689" zvalue="405"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454332121091" ObjectName="10kV中万线0286隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454332121091"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1226.57,831.84) scale(-0.947693,-0.6712) translate(-2521.23,-2076.1)" width="15" x="1219.461530083906" y="821.7724014815689"/></g>
  <g id="320">
   <use class="kv10" height="30" transform="rotate(180,1226.54,878.357) scale(-0.947693,-0.6712) translate(-2521.18,-2191.92)" width="15" x="1219.436309486" xlink:href="#Disconnector:刀闸_0" y="868.288530513827" zvalue="408"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454332055555" ObjectName="10kV中万线0288隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454332055555"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1226.54,878.357) scale(-0.947693,-0.6712) translate(-2521.18,-2191.92)" width="15" x="1219.436309486" y="868.288530513827"/></g>
  <g id="319">
   <use class="kv10" height="30" transform="rotate(180,1226.44,744.801) scale(-0.947693,-0.6712) translate(-2520.96,-1859.39)" width="15" x="1219.331330745879" xlink:href="#Disconnector:刀闸_0" y="734.7329749582715" zvalue="410"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454331990019" ObjectName="10kV中万线0282隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454331990019"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1226.44,744.801) scale(-0.947693,-0.6712) translate(-2520.96,-1859.39)" width="15" x="1219.331330745879" y="734.7329749582715"/></g>
  <g id="312">
   <use class="kv10" height="30" transform="rotate(180,1416.54,831.84) scale(-0.947693,-0.6712) translate(-2911.66,-2076.1)" width="15" x="1409.431516465616" xlink:href="#Disconnector:刀闸_0" y="821.7724014815689" zvalue="419"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454331858947" ObjectName="10kV黄家寨线0316隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454331858947"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1416.54,831.84) scale(-0.947693,-0.6712) translate(-2911.66,-2076.1)" width="15" x="1409.431516465616" y="821.7724014815689"/></g>
  <g id="308">
   <use class="kv10" height="30" transform="rotate(180,1126.54,878.357) scale(-0.947693,-0.6712) translate(-2315.66,-2191.92)" width="15" x="1119.436309486" xlink:href="#Disconnector:刀闸_0" y="868.288530513827" zvalue="422"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454331793411" ObjectName="10kV赛岗线0278隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454331793411"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1126.54,878.357) scale(-0.947693,-0.6712) translate(-2315.66,-2191.92)" width="15" x="1119.436309486" y="868.288530513827"/></g>
  <g id="307">
   <use class="kv10" height="30" transform="rotate(180,1126.41,744.801) scale(-0.947693,-0.6712) translate(-2315.38,-1859.39)" width="15" x="1119.301317127589" xlink:href="#Disconnector:刀闸_0" y="734.7329749582715" zvalue="423"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454331727875" ObjectName="10kV赛岗线0272隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454331727875"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1126.41,744.801) scale(-0.947693,-0.6712) translate(-2315.38,-1859.39)" width="15" x="1119.301317127589" y="734.7329749582715"/></g>
  <g id="301">
   <use class="kv10" height="30" transform="rotate(180,1126.54,831.84) scale(-0.947693,-0.6712) translate(-2315.66,-2076.1)" width="15" x="1119.436309486" xlink:href="#Disconnector:刀闸_0" y="821.7724014815689" zvalue="430"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454331596803" ObjectName="10kV赛岗线0276隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454331596803"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1126.54,831.84) scale(-0.947693,-0.6712) translate(-2315.66,-2076.1)" width="15" x="1119.436309486" y="821.7724014815689"/></g>
  <g id="298">
   <use class="kv10" height="30" transform="rotate(180,1320.54,831.84) scale(-0.947693,-0.6712) translate(-2714.37,-2076.1)" width="15" x="1313.436309486" xlink:href="#Disconnector:刀闸_0" y="821.7724014815689" zvalue="433"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454331531267" ObjectName="10kV备用Ⅳ线0296隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454331531267"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1320.54,831.84) scale(-0.947693,-0.6712) translate(-2714.37,-2076.1)" width="15" x="1313.436309486" y="821.7724014815689"/></g>
  <g id="297">
   <use class="kv10" height="30" transform="rotate(180,1320.54,878.357) scale(-0.947693,-0.6712) translate(-2714.37,-2191.92)" width="15" x="1313.436309486" xlink:href="#Disconnector:刀闸_0" y="868.288530513827" zvalue="436"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454331465731" ObjectName="10kV备用Ⅳ线0298隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454331465731"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1320.54,878.357) scale(-0.947693,-0.6712) translate(-2714.37,-2191.92)" width="15" x="1313.436309486" y="868.288530513827"/></g>
  <g id="296">
   <use class="kv10" height="30" transform="rotate(180,1320.41,744.801) scale(-0.947693,-0.6712) translate(-2714.09,-1859.39)" width="15" x="1313.301317127589" xlink:href="#Disconnector:刀闸_0" y="734.7329749582715" zvalue="438"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454331400195" ObjectName="10kV备用Ⅳ线0292隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454331400195"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1320.41,744.801) scale(-0.947693,-0.6712) translate(-2714.09,-1859.39)" width="15" x="1313.301317127589" y="734.7329749582715"/></g>
  <g id="101">
   <use class="kv10" height="30" transform="rotate(180,1518.41,744.801) scale(-0.947693,-0.6712) translate(-3121.02,-1859.39)" width="15" x="1511.301317127589" xlink:href="#Disconnector:刀闸_0" y="734.7329749582715" zvalue="447"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454331269123" ObjectName="10kV2号站用变0332隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454331269123"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1518.41,744.801) scale(-0.947693,-0.6712) translate(-3121.02,-1859.39)" width="15" x="1511.301317127589" y="734.7329749582715"/></g>
  <g id="345">
   <use class="kv10" height="30" transform="rotate(0,901.916,668.905) scale(0.947693,0.6712) translate(49.3882,322.743)" width="15" x="894.808777774948" xlink:href="#Disconnector:刀闸_0" y="658.8373433800381" zvalue="459"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454332448771" ObjectName="10kV分段0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454332448771"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,901.916,668.905) scale(0.947693,0.6712) translate(49.3882,322.743)" width="15" x="894.808777774948" y="658.8373433800381"/></g>
  <g id="344">
   <use class="kv10" height="30" transform="rotate(0,1066.7,668.905) scale(0.947693,0.6712) translate(58.4834,322.743)" width="15" x="1059.594045498879" xlink:href="#Disconnector:刀闸_0" y="658.8373433800379" zvalue="461"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454332383235" ObjectName="10kV分段0122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454332383235"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1066.7,668.905) scale(0.947693,0.6712) translate(58.4834,322.743)" width="15" x="1059.594045498879" y="658.8373433800379"/></g>
  <g id="241">
   <use class="kv10" height="30" transform="rotate(0,1464.96,871.5) scale(1,1) translate(0,0)" width="15" x="1457.458333333333" xlink:href="#Disconnector:令克_0" y="856.5" zvalue="1144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450269347845" ObjectName="10kV黄家寨线0319隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450269347845"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1464.96,871.5) scale(1,1) translate(0,0)" width="15" x="1457.458333333333" y="856.5"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="215">
   <path class="kv110" d="M 728.64 440.75 L 728.66 460.41" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="275@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 728.64 440.75 L 728.66 460.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv110" d="M 745.2 403.48 L 728.48 403.48" stroke-width="1" zvalue="51"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@0" LinkObjectIDznd="189" MaxPinNum="2"/>
   </metadata>
  <path d="M 745.2 403.48 L 728.48 403.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv110" d="M 745.2 451.48 L 728.65 451.48" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="215" MaxPinNum="2"/>
   </metadata>
  <path d="M 745.2 451.48 L 728.65 451.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv110" d="M 547.82 315.45 L 547.82 358.59" stroke-width="1" zvalue="57"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@1" LinkObjectIDznd="219@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 547.82 315.45 L 547.82 358.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="kv110" d="M 565.31 331.43 L 547.82 331.43" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="211" MaxPinNum="2"/>
   </metadata>
  <path d="M 565.31 331.43 L 547.82 331.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv110" d="M 547.84 295.82 L 547.84 248.41" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@0" LinkObjectIDznd="200@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 547.84 295.82 L 547.84 248.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="207">
   <path class="kv110" d="M 565.31 279.43 L 547.84 279.43" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="209@0" LinkObjectIDznd="208" MaxPinNum="2"/>
   </metadata>
  <path d="M 565.31 279.43 L 547.84 279.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv110" d="M 1196.13 315.45 L 1196.13 357.34" stroke-width="1" zvalue="82"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@1" LinkObjectIDznd="201@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1196.13 315.45 L 1196.13 357.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv110" d="M 1213.31 331.43 L 1196.13 331.43" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@0" LinkObjectIDznd="196" MaxPinNum="2"/>
   </metadata>
  <path d="M 1213.31 331.43 L 1196.13 331.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv110" d="M 1196.15 295.82 L 1196.15 248.41" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@0" LinkObjectIDznd="191@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1196.15 295.82 L 1196.15 248.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="kv110" d="M 1213.31 280.68 L 1196.15 280.68" stroke-width="1" zvalue="87"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@0" LinkObjectIDznd="193" MaxPinNum="2"/>
   </metadata>
  <path d="M 1213.31 280.68 L 1196.15 280.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv110" d="M 728.48 414.87 L 728.48 392.77" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@1" LinkObjectIDznd="272@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 728.48 414.87 L 728.48 392.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv110" d="M 728.64 480.04 L 728.64 491.15" stroke-width="1" zvalue="91"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="275@1" LinkObjectIDznd="190@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 728.64 480.04 L 728.64 491.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="kv110" d="M 656.49 251.56 L 656.49 208.98" stroke-width="1" zvalue="124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="631@0" LinkObjectIDznd="629@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 656.49 251.56 L 656.49 208.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv110" d="M 656.64 189.35 L 656.56 116.92" stroke-width="1" zvalue="127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="629@0" LinkObjectIDznd="632@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 656.64 189.35 L 656.56 116.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="kv110" d="M 674.2 228.12 L 656.49 228.12" stroke-width="1" zvalue="130"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="556@0" LinkObjectIDznd="176" MaxPinNum="2"/>
   </metadata>
  <path d="M 674.2 228.12 L 656.49 228.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv110" d="M 656.6 340.25 L 656.6 358.59" stroke-width="1" zvalue="133"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="625@1" LinkObjectIDznd="219@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 656.6 340.25 L 656.6 358.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv110" d="M 656.65 277.45 L 656.63 320.62" stroke-width="1" zvalue="134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="631@1" LinkObjectIDznd="625@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 656.65 277.45 L 656.63 320.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv110" d="M 674.2 300.16 L 656.64 300.16" stroke-width="1" zvalue="135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="550@0" LinkObjectIDznd="172" MaxPinNum="2"/>
   </metadata>
  <path d="M 674.2 300.16 L 656.64 300.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv110" d="M 675.2 163.88 L 656.61 163.88" stroke-width="1" zvalue="136"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="628@0" LinkObjectIDznd="175" MaxPinNum="2"/>
   </metadata>
  <path d="M 675.2 163.88 L 656.61 163.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv110" d="M 677.46 133.83 L 656.58 133.83" stroke-width="1" zvalue="139"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="534@0" LinkObjectIDznd="175" MaxPinNum="2"/>
   </metadata>
  <path d="M 677.46 133.83 L 656.58 133.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv110" d="M 642.75 133.83 L 656.58 133.83" stroke-width="1" zvalue="140"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="535@0" LinkObjectIDznd="175" MaxPinNum="2"/>
   </metadata>
  <path d="M 642.75 133.83 L 656.58 133.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv110" d="M 1081.49 250.56 L 1081.49 208.98" stroke-width="1" zvalue="149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="165@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1081.49 250.56 L 1081.49 208.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="kv110" d="M 1081.64 189.35 L 1081.56 116.92" stroke-width="1" zvalue="152"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="167@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1081.64 189.35 L 1081.56 116.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="kv110" d="M 1099.2 228.12 L 1081.49 228.12" stroke-width="1" zvalue="155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160@0" LinkObjectIDznd="163" MaxPinNum="2"/>
   </metadata>
  <path d="M 1099.2 228.12 L 1081.49 228.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv110" d="M 1081.6 340.25 L 1081.6 357.34" stroke-width="1" zvalue="158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162@1" LinkObjectIDznd="201@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1081.6 340.25 L 1081.6 357.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv110" d="M 1081.65 276.45 L 1081.63 320.62" stroke-width="1" zvalue="159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@1" LinkObjectIDznd="162@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1081.65 276.45 L 1081.63 320.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv110" d="M 1099.2 300.16 L 1081.64 300.16" stroke-width="1" zvalue="160"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="156" MaxPinNum="2"/>
   </metadata>
  <path d="M 1099.2 300.16 L 1081.64 300.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv110" d="M 1100.2 163.88 L 1081.61 163.88" stroke-width="1" zvalue="161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@0" LinkObjectIDznd="161" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100.2 163.88 L 1081.61 163.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv110" d="M 1102.46 133.83 L 1081.58 133.83" stroke-width="1" zvalue="164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="161" MaxPinNum="2"/>
   </metadata>
  <path d="M 1102.46 133.83 L 1081.58 133.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv110" d="M 1067.75 133.83 L 1081.54 133.83" stroke-width="1" zvalue="165"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@0" LinkObjectIDznd="151" MaxPinNum="2"/>
   </metadata>
  <path d="M 1067.75 133.83 L 1081.54 133.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv110" d="M 822 313.14 L 822 278.63 L 891.14 278.63" stroke-width="1" zvalue="176"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="790@0" LinkObjectIDznd="791@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 822 313.14 L 822 278.63 L 891.14 278.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv110" d="M 989.23 313.14 L 989.23 278.56 L 917.84 278.56" stroke-width="1" zvalue="177"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="787@0" LinkObjectIDznd="791@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 989.23 313.14 L 989.23 278.56 L 917.84 278.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv110" d="M 821.97 332.77 L 821.97 358.59" stroke-width="1" zvalue="178"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="790@1" LinkObjectIDznd="219@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 821.97 332.77 L 821.97 358.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv110" d="M 989.2 332.77 L 989.2 357.34" stroke-width="1" zvalue="179"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="787@1" LinkObjectIDznd="201@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 989.2 332.77 L 989.2 357.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv110" d="M 839.32 305.47 L 822 305.5 L 822 290.67" stroke-width="1" zvalue="180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="784@0" LinkObjectIDznd="149" MaxPinNum="2"/>
   </metadata>
  <path d="M 839.32 305.47 L 822 305.5 L 822 290.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv110" d="M 1003.77 303.91 L 989.23 303.91" stroke-width="1" zvalue="181"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="785@0" LinkObjectIDznd="148" MaxPinNum="2"/>
   </metadata>
  <path d="M 1003.77 303.91 L 989.23 303.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv110" d="M 728.67 373.14 L 728.67 358.59" stroke-width="1" zvalue="182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="219@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 728.67 373.14 L 728.67 358.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv10" d="M 728.05 655.36 L 728.05 664.41" stroke-width="1" zvalue="188"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@0" LinkObjectIDznd="140@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 728.05 655.36 L 728.05 664.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="kv10" d="M 727.64 684.04 L 727.64 710.59" stroke-width="1" zvalue="190"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@1" LinkObjectIDznd="216@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 727.64 684.04 L 727.64 710.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv10" d="M 728.53 566.45 L 728.43 600.64" stroke-width="1" zvalue="191"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@3" LinkObjectIDznd="141@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 728.53 566.45 L 728.43 600.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv10" d="M 728.41 620.27 L 728.41 629.47" stroke-width="1" zvalue="192"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@1" LinkObjectIDznd="142@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 728.41 620.27 L 728.41 629.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv35" d="M 781.24 528.86 L 1024.24 528.86 L 1024.24 488.6 L 1266.63 488.6" stroke-width="1" zvalue="193"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@2" LinkObjectIDznd="499@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.24 528.86 L 1024.24 528.86 L 1024.24 488.6 L 1266.63 488.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv35" d="M 828.78 516.84 L 828.78 528.86" stroke-width="1" zvalue="194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="493@0" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 828.78 516.84 L 828.78 528.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv35" d="M 1518.81 375.83 L 1553.97 375.83" stroke-width="1" zvalue="204"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@1" LinkObjectIDznd="489@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1518.81 375.83 L 1553.97 375.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv35" d="M 1670.15 445.75 L 1670.15 456.03" stroke-width="1" zvalue="222"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="388" MaxPinNum="2"/>
   </metadata>
  <path d="M 1670.15 445.75 L 1670.15 456.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv10" d="M 745.97 580.08 L 728.49 580.08" stroke-width="1" zvalue="237"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="511@0" LinkObjectIDznd="137" MaxPinNum="2"/>
   </metadata>
  <path d="M 745.97 580.08 L 728.49 580.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv35" d="M 1670.15 547.77 L 1670.15 560.03" stroke-width="1" zvalue="238"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="463@0" LinkObjectIDznd="429" MaxPinNum="2"/>
   </metadata>
  <path d="M 1670.15 547.77 L 1670.15 560.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv10" d="M 541.1 675.45 L 541.1 710.59" stroke-width="1" zvalue="241"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="794@1" LinkObjectIDznd="216@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 541.1 675.45 L 541.1 710.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 541.13 655.82 L 541.13 608.41" stroke-width="1" zvalue="244"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="794@0" LinkObjectIDznd="636@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 541.13 655.82 L 541.13 608.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 559.31 639.43 L 541.13 639.43" stroke-width="1" zvalue="245"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="716@0" LinkObjectIDznd="110" MaxPinNum="2"/>
   </metadata>
  <path d="M 559.31 639.43 L 541.13 639.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv10" d="M 1327.82 675.45 L 1327.82 710.59" stroke-width="1" zvalue="250"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="820@1" LinkObjectIDznd="737@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1327.82 675.45 L 1327.82 710.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="kv10" d="M 1327.84 655.82 L 1327.84 608.41" stroke-width="1" zvalue="253"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="820@0" LinkObjectIDznd="812@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1327.84 655.82 L 1327.84 608.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv10" d="M 1345.31 639.43 L 1327.84 639.43" stroke-width="1" zvalue="254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="816@0" LinkObjectIDznd="107" MaxPinNum="2"/>
   </metadata>
  <path d="M 1345.31 639.43 L 1327.84 639.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv10" d="M 762.49 735.07 L 762.49 710.59" stroke-width="1" zvalue="262"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="832@0" LinkObjectIDznd="216@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 762.49 735.07 L 762.49 710.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv10" d="M 762.47 754.7 L 762.47 771.56" stroke-width="1" zvalue="263"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="832@1" LinkObjectIDznd="824@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 762.47 754.7 L 762.47 771.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv10" d="M 762.6 837.74 L 762.6 864.62" stroke-width="1" zvalue="264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="919@1" LinkObjectIDznd="829@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 762.6 837.74 L 762.6 864.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv10" d="M 762.57 884.25 L 762.57 895.04" stroke-width="1" zvalue="265"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="829@1" LinkObjectIDznd="834@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 762.57 884.25 L 762.57 895.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv10" d="M 660.52 735.07 L 660.52 710.59" stroke-width="1" zvalue="304"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="912@0" LinkObjectIDznd="216@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 660.52 735.07 L 660.52 710.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv10" d="M 660.5 754.7 L 660.5 771.56" stroke-width="1" zvalue="305"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="912@1" LinkObjectIDznd="917@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 660.5 754.7 L 660.5 771.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv10" d="M 660.63 837.74 L 660.63 864.62" stroke-width="1" zvalue="306"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="916@1" LinkObjectIDznd="914@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 660.63 837.74 L 660.63 864.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv10" d="M 660.6 884.25 L 660.6 895.04" stroke-width="1" zvalue="307"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="914@1" LinkObjectIDznd="907@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 660.6 884.25 L 660.6 895.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv10" d="M 660.65 818.11 L 660.65 797.45" stroke-width="1" zvalue="308"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="916@0" LinkObjectIDznd="917@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 660.65 818.11 L 660.65 797.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv10" d="M 762.62 818.11 L 762.62 797.45" stroke-width="1" zvalue="310"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="919@0" LinkObjectIDznd="824@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 762.62 818.11 L 762.62 797.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv35" d="M 1745.06 429 L 1737.66 429 L 1737.66 456.03" stroke-width="1" zvalue="325"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="310@0" LinkObjectIDznd="388" MaxPinNum="2"/>
   </metadata>
  <path d="M 1745.06 429 L 1737.66 429 L 1737.66 456.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv35" d="M 1747.41 482.35 L 1740.83 482.38 L 1740.83 456.03" stroke-width="1" zvalue="326"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="443@0" LinkObjectIDznd="388" MaxPinNum="2"/>
   </metadata>
  <path d="M 1747.41 482.35 L 1740.83 482.38 L 1740.83 456.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv35" d="M 1739.06 533 L 1731 533 L 1731 560.03" stroke-width="1" zvalue="327"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="488@0" LinkObjectIDznd="429" MaxPinNum="2"/>
   </metadata>
  <path d="M 1739.06 533 L 1731 533 L 1731 560.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv35" d="M 1741.41 586.35 L 1731 586.38 L 1731 557.76" stroke-width="1" zvalue="328"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="509@0" LinkObjectIDznd="71" MaxPinNum="2"/>
   </metadata>
  <path d="M 1741.41 586.35 L 1731 586.38 L 1731 557.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="257">
   <path class="kv10" d="M 472.49 735.07 L 472.49 710.59" stroke-width="1" zvalue="335"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@0" LinkObjectIDznd="216@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 472.49 735.07 L 472.49 710.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="256">
   <path class="kv10" d="M 472.47 754.7 L 472.49 771.56" stroke-width="1" zvalue="336"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@1" LinkObjectIDznd="261@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 472.47 754.7 L 472.49 771.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="255">
   <path class="kv10" d="M 472.6 837.74 L 472.63 864.62" stroke-width="1" zvalue="337"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@1" LinkObjectIDznd="260@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 472.6 837.74 L 472.63 864.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="254">
   <path class="kv10" d="M 472.6 884.25 L 472.6 895.04" stroke-width="1" zvalue="338"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="260@1" LinkObjectIDznd="258@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 472.6 884.25 L 472.6 895.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="252">
   <path class="kv10" d="M 472.63 818.11 L 472.65 797.45" stroke-width="1" zvalue="340"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@0" LinkObjectIDznd="261@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 472.63 818.11 L 472.65 797.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="273">
   <path class="kv10" d="M 566.49 735.07 L 566.49 710.59" stroke-width="1" zvalue="351"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="278@0" LinkObjectIDznd="216@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 566.49 735.07 L 566.49 710.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="271">
   <path class="kv10" d="M 566.47 754.7 L 566.49 771.56" stroke-width="1" zvalue="352"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="278@1" LinkObjectIDznd="281@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 566.47 754.7 L 566.49 771.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv10" d="M 566.6 837.74 L 566.63 864.62" stroke-width="1" zvalue="353"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@1" LinkObjectIDznd="279@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 566.6 837.74 L 566.63 864.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv10" d="M 566.6 884.25 L 566.69 893.16" stroke-width="1" zvalue="354"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="279@1" LinkObjectIDznd="274@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 566.6 884.25 L 566.69 893.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="kv10" d="M 566.63 818.11 L 566.65 797.45" stroke-width="1" zvalue="355"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@0" LinkObjectIDznd="281@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 566.63 818.11 L 566.65 797.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="287">
   <path class="kv10" d="M 856.49 735.07 L 856.49 710.59" stroke-width="1" zvalue="387"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="286@0" LinkObjectIDznd="216@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 856.49 735.07 L 856.49 710.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="290">
   <path class="kv10" d="M 885.2 792.16 L 856.49 792.16" stroke-width="1" zvalue="391"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="291@0" LinkObjectIDznd="294" MaxPinNum="2"/>
   </metadata>
  <path d="M 885.2 792.16 L 856.49 792.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="294">
   <path class="kv10" d="M 856.47 754.7 L 856.5 814.08" stroke-width="1" zvalue="393"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="286@1" LinkObjectIDznd="292@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 856.47 754.7 L 856.5 814.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="326">
   <path class="kv10" d="M 1416.49 735.07 L 1416.49 710.59" stroke-width="1" zvalue="400"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="328@0" LinkObjectIDznd="737@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1416.49 735.07 L 1416.49 710.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="325">
   <path class="kv10" d="M 1416.47 754.7 L 1416.47 771.56" stroke-width="1" zvalue="401"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="328@1" LinkObjectIDznd="330@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1416.47 754.7 L 1416.47 771.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="324">
   <path class="kv10" d="M 1416.6 841.74 L 1416.6 868.62" stroke-width="1" zvalue="402"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="312@1" LinkObjectIDznd="329@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1416.6 841.74 L 1416.6 868.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="323">
   <path class="kv10" d="M 1416.57 888.25 L 1416.57 898.04" stroke-width="1" zvalue="403"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="329@1" LinkObjectIDznd="327@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1416.57 888.25 L 1416.57 898.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="317">
   <path class="kv10" d="M 1226.52 735.07 L 1226.52 710.59" stroke-width="1" zvalue="414"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="319@0" LinkObjectIDznd="737@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1226.52 735.07 L 1226.52 710.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="316">
   <path class="kv10" d="M 1226.5 754.7 L 1226.5 771.56" stroke-width="1" zvalue="415"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="319@1" LinkObjectIDznd="322@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1226.5 754.7 L 1226.5 771.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="315">
   <path class="kv10" d="M 1226.63 841.74 L 1226.63 868.62" stroke-width="1" zvalue="416"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="321@1" LinkObjectIDznd="320@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1226.63 841.74 L 1226.63 868.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="314">
   <path class="kv10" d="M 1226.6 888.25 L 1226.6 898.04" stroke-width="1" zvalue="417"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@1" LinkObjectIDznd="318@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1226.6 888.25 L 1226.6 898.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="313">
   <path class="kv10" d="M 1226.65 822.11 L 1226.65 797.45" stroke-width="1" zvalue="418"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="321@0" LinkObjectIDznd="322@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1226.65 822.11 L 1226.65 797.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv10" d="M 1416.62 822.11 L 1416.62 797.45" stroke-width="1" zvalue="420"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="312@0" LinkObjectIDznd="330@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1416.62 822.11 L 1416.62 797.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="305">
   <path class="kv10" d="M 1126.49 735.07 L 1126.49 710.59" stroke-width="1" zvalue="426"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="307@0" LinkObjectIDznd="737@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.49 735.07 L 1126.49 710.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="304">
   <path class="kv10" d="M 1126.47 754.7 L 1126.47 771.56" stroke-width="1" zvalue="427"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="307@1" LinkObjectIDznd="309@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.47 754.7 L 1126.47 771.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="303">
   <path class="kv10" d="M 1126.6 841.74 L 1126.63 868.62" stroke-width="1" zvalue="428"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="301@1" LinkObjectIDznd="308@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.6 841.74 L 1126.63 868.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="302">
   <path class="kv10" d="M 1126.6 888.25 L 1126.6 898.04" stroke-width="1" zvalue="429"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="308@1" LinkObjectIDznd="306@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.6 888.25 L 1126.6 898.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="300">
   <path class="kv10" d="M 1126.63 822.11 L 1126.63 797.45" stroke-width="1" zvalue="431"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="301@0" LinkObjectIDznd="309@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.63 822.11 L 1126.63 797.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="285">
   <path class="kv10" d="M 1320.49 735.07 L 1320.49 710.59" stroke-width="1" zvalue="442"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="296@0" LinkObjectIDznd="737@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1320.49 735.07 L 1320.49 710.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="284">
   <path class="kv10" d="M 1320.47 754.7 L 1320.49 771.56" stroke-width="1" zvalue="443"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="296@1" LinkObjectIDznd="299@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1320.47 754.7 L 1320.49 771.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv10" d="M 1320.6 841.74 L 1320.63 868.62" stroke-width="1" zvalue="444"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@1" LinkObjectIDznd="297@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1320.6 841.74 L 1320.63 868.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv10" d="M 1320.6 888.25 L 1320.6 897.13" stroke-width="1" zvalue="445"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="297@1" LinkObjectIDznd="295@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1320.6 888.25 L 1320.6 897.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv10" d="M 1320.63 822.11 L 1320.65 797.45" stroke-width="1" zvalue="446"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@0" LinkObjectIDznd="299@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1320.63 822.11 L 1320.65 797.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv10" d="M 1518.47 754.7 L 1518.5 813.17" stroke-width="1" zvalue="455"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@1" LinkObjectIDznd="97@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1518.47 754.7 L 1518.5 813.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="342">
   <path class="kv10" d="M 902 659.17 L 902 638.13 L 972.14 638.13" stroke-width="1" zvalue="464"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="345@0" LinkObjectIDznd="346@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 902 659.17 L 902 638.13 L 972.14 638.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="341">
   <path class="kv10" d="M 1066.78 659.17 L 1066.78 638.06 L 998.84 638.06" stroke-width="1" zvalue="465"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="344@0" LinkObjectIDznd="346@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1066.78 659.17 L 1066.78 638.06 L 998.84 638.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="340">
   <path class="kv10" d="M 901.97 678.8 L 901.97 710.59" stroke-width="1" zvalue="466"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="345@1" LinkObjectIDznd="216@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 901.97 678.8 L 901.97 710.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="339">
   <path class="kv10" d="M 1066.76 678.8 L 1066.76 710.59" stroke-width="1" zvalue="467"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="344@1" LinkObjectIDznd="737@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1066.76 678.8 L 1066.76 710.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="353">
   <path class="kv10" d="M 1518.49 735.07 L 1518.49 710.59" stroke-width="1" zvalue="474"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="737@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1518.49 735.07 L 1518.49 710.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="354">
   <path class="kv10" d="M 1548.45 790.91 L 1518.49 790.91" stroke-width="1" zvalue="475"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 1548.45 790.91 L 1518.49 790.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="363">
   <path class="kv35" d="M 1670.15 329.43 L 1670.15 342.69" stroke-width="1" zvalue="485"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@0" LinkObjectIDznd="392" MaxPinNum="2"/>
   </metadata>
  <path d="M 1670.15 329.43 L 1670.15 342.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="364">
   <path class="kv35" d="M 1738.97 317.29 L 1730 317.29 L 1730 342.69" stroke-width="1" zvalue="486"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="925@0" LinkObjectIDznd="392" MaxPinNum="2"/>
   </metadata>
  <path d="M 1738.97 317.29 L 1730 317.29 L 1730 342.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="365">
   <path class="kv35" d="M 1741.41 370.35 L 1730.59 370.38 L 1730.59 342.69" stroke-width="1" zvalue="487"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@0" LinkObjectIDznd="392" MaxPinNum="2"/>
   </metadata>
  <path d="M 1741.41 370.35 L 1730.59 370.38 L 1730.59 342.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="380">
   <path class="kv35" d="M 1579.45 456.03 L 1553.97 456.03" stroke-width="1" zvalue="580"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="441@1" LinkObjectIDznd="489@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1579.45 456.03 L 1553.97 456.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="388">
   <path class="kv35" d="M 1624.16 456.03 L 1753.85 456.03" stroke-width="1" zvalue="581"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="441@0" LinkObjectIDznd="282@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1624.16 456.03 L 1753.85 456.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="391">
   <path class="kv35" d="M 1579.45 342.69 L 1553.97 342.69" stroke-width="1" zvalue="582"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="359@1" LinkObjectIDznd="489@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1579.45 342.69 L 1553.97 342.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="392">
   <path class="kv35" d="M 1624.16 342.69 L 1753.85 342.69" stroke-width="1" zvalue="583"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="359@0" LinkObjectIDznd="927@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1624.16 342.69 L 1753.85 342.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="429">
   <path class="kv35" d="M 1624.16 560.03 L 1753.85 560.03" stroke-width="1" zvalue="584"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="508@0" LinkObjectIDznd="487@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1624.16 560.03 L 1753.85 560.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="430">
   <path class="kv35" d="M 1579.45 560.03 L 1553.97 560.03" stroke-width="1" zvalue="585"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="508@1" LinkObjectIDznd="489@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1579.45 560.03 L 1553.97 560.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="434">
   <path class="kv35" d="M 1286.26 488.58 L 1318.49 488.52" stroke-width="1" zvalue="589"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="499@1" LinkObjectIDznd="495@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1286.26 488.58 L 1318.49 488.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="435">
   <path class="kv35" d="M 1363.21 488.52 L 1553.97 488.52" stroke-width="1" zvalue="590"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="495@0" LinkObjectIDznd="489@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1363.21 488.52 L 1553.97 488.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="437">
   <path class="kv35" d="M 1490.14 375.93 L 1452.76 375.87" stroke-width="1" zvalue="592"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="187@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1490.14 375.93 L 1452.76 375.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="439">
   <path class="kv35" d="M 1684.62 278.75 L 1670.05 278.75 L 1670.05 300.76" stroke-width="1" zvalue="594"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@0" LinkObjectIDznd="128@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1684.62 278.75 L 1670.05 278.75 L 1670.05 300.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="440">
   <path class="kv35" d="M 1684.62 391.66 L 1670.05 391.66 L 1670.05 417.09" stroke-width="1" zvalue="595"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="251@0" LinkObjectIDznd="197@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1684.62 391.66 L 1670.05 391.66 L 1670.05 417.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="442">
   <path class="kv35" d="M 1684.62 498.39 L 1670.05 498.39 L 1670.05 519.1" stroke-width="1" zvalue="596"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="481@0" LinkObjectIDznd="463@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1684.62 498.39 L 1670.05 498.39 L 1670.05 519.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv110" d="M 658.23 512.29 L 728.66 512.29" stroke-width="1" zvalue="1053"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@0" LinkObjectIDznd="190@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 658.23 512.29 L 728.66 512.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="245">
   <path class="kv10" d="M 1464.88 892.89 L 1464.88 883.75" stroke-width="1" zvalue="1145"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="350@0" LinkObjectIDznd="241@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1464.88 892.89 L 1464.88 883.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="460">
   <path class="kv10" d="M 1465.04 858.25 L 1465.04 849.5 L 1416.6 849.5" stroke-width="1" zvalue="1146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="241@0" LinkObjectIDznd="324" MaxPinNum="2"/>
   </metadata>
  <path d="M 1465.04 858.25 L 1465.04 849.5 L 1416.6 849.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer3Class">
  <g id="190">
   <g id="1900">
    <use class="kv110" height="50" transform="rotate(0,743.809,528.738) scale(1.52766,1.51857) translate(-243.722,-167.593)" width="50" x="705.62" xlink:href="#PowerTransformer3:三卷变带中性点_0" y="490.77" zvalue="92"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874579050498" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1901">
    <use class="kv35" height="50" transform="rotate(0,743.809,528.738) scale(1.52766,1.51857) translate(-243.722,-167.593)" width="50" x="705.62" xlink:href="#PowerTransformer3:三卷变带中性点_1" y="490.77" zvalue="92"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874579116034" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1902">
    <use class="kv10" height="50" transform="rotate(0,743.809,528.738) scale(1.52766,1.51857) translate(-243.722,-167.593)" width="50" x="705.62" xlink:href="#PowerTransformer3:三卷变带中性点_2" y="490.77" zvalue="92"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874579181570" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399528480770" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399528480770"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,743.809,528.738) scale(1.52766,1.51857) translate(-243.722,-167.593)" width="50" x="705.62" y="490.77"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="632">
   <use class="kv110" height="30" transform="rotate(0,656.556,109.156) scale(1.98323,0.522926) translate(-322.06,92.4286)" width="7" x="649.6143031190627" xlink:href="#ACLineSegment:线路_0" y="101.3122051076645" zvalue="116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249325436932" ObjectName="110kV中弄线"/>
   <cge:TPSR_Ref TObjectID="8444249325436932_5066549681848321"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,656.556,109.156) scale(1.98323,0.522926) translate(-322.06,92.4286)" width="7" x="649.6143031190627" y="101.3122051076645"/></g>
  <g id="167">
   <use class="kv110" height="30" transform="rotate(0,1081.56,109.156) scale(1.98323,0.522926) translate(-532.763,92.4286)" width="7" x="1074.614303119063" xlink:href="#ACLineSegment:线路_0" y="101.3122051076645" zvalue="141"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249310887942" ObjectName="110kV万中线"/>
   <cge:TPSR_Ref TObjectID="8444249310887942_5066549681848321"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1081.56,109.156) scale(1.98323,0.522926) translate(-532.763,92.4286)" width="7" x="1074.614303119063" y="101.3122051076645"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="282">
   <use class="kv35" height="30" transform="rotate(270,1770.72,455.076) scale(1.25,-1.25) translate(-352.644,-815.387)" width="12" x="1763.222107140787" xlink:href="#EnergyConsumer:负荷_0" y="436.3262880403203" zvalue="216"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454326222850" ObjectName="35kV备用Ⅱ回线"/>
   <cge:TPSR_Ref TObjectID="6192454326222850"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1770.72,455.076) scale(1.25,-1.25) translate(-352.644,-815.387)" width="12" x="1763.222107140787" y="436.3262880403203"/></g>
  <g id="487">
   <use class="kv35" height="30" transform="rotate(270,1770.72,559.076) scale(1.25,-1.25) translate(-352.644,-1002.59)" width="12" x="1763.222106932171" xlink:href="#EnergyConsumer:负荷_0" y="540.3262880403203" zvalue="230"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454325829634" ObjectName="35kV备用Ⅲ回线"/>
   <cge:TPSR_Ref TObjectID="6192454325829634"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1770.72,559.076) scale(1.25,-1.25) translate(-352.644,-1002.59)" width="12" x="1763.222106932171" y="540.3262880403203"/></g>
  <g id="834">
   <use class="kv10" height="30" transform="rotate(0,765.072,911.601) scale(1,-1.25) translate(5.04644e-13,-1637.13)" width="15" x="757.5719578445603" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="892.8505469982767" zvalue="260"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454324912130" ObjectName="10kV备用Ⅱ线"/>
   <cge:TPSR_Ref TObjectID="6192454324912130"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,765.072,911.601) scale(1,-1.25) translate(5.04644e-13,-1637.13)" width="15" x="757.5719578445603" y="892.8505469982767"/></g>
  <g id="907">
   <use class="kv10" height="30" transform="rotate(0,663.102,911.601) scale(1,-1.25) translate(4.36719e-13,-1637.13)" width="15" x="655.6019714628503" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="892.8505469982767" zvalue="302"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454324649986" ObjectName="10kV小街线"/>
   <cge:TPSR_Ref TObjectID="6192454324649986"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,663.102,911.601) scale(1,-1.25) translate(4.36719e-13,-1637.13)" width="15" x="655.6019714628503" y="892.8505469982767"/></g>
  <g id="927">
   <use class="kv35" height="30" transform="rotate(270,1770.72,342.881) scale(1.25,-1.25) translate(-352.644,-613.436)" width="12" x="1763.222106933594" xlink:href="#EnergyConsumer:负荷_0" y="324.1311660891008" zvalue="316"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454324453378" ObjectName="35kV备用Ⅰ回线"/>
   <cge:TPSR_Ref TObjectID="6192454324453378"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1770.72,342.881) scale(1.25,-1.25) translate(-352.644,-613.436)" width="12" x="1763.222106933594" y="324.1311660891008"/></g>
  <g id="258">
   <use class="kv10" height="30" transform="rotate(0,475.102,911.601) scale(1,-1.25) translate(3.11485e-13,-1637.13)" width="15" x="467.6019714628504" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="892.8505469982767" zvalue="333"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454330351619" ObjectName="10kV备用Ⅰ线"/>
   <cge:TPSR_Ref TObjectID="6192454330351619"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,475.102,911.601) scale(1,-1.25) translate(3.11485e-13,-1637.13)" width="15" x="467.6019714628504" y="892.8505469982767"/></g>
  <g id="292">
   <use class="kv10" height="30" transform="rotate(0,856.5,848.75) scale(2.45,2.45) translate(-492.408,-480.571)" width="20" x="832" xlink:href="#EnergyConsumer:站用变带熔断器YY_0" y="812" zvalue="392"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454331006979" ObjectName="10kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,856.5,848.75) scale(2.45,2.45) translate(-492.408,-480.571)" width="20" x="832" y="812"/></g>
  <g id="327">
   <use class="kv10" height="30" transform="rotate(0,1419.07,914.601) scale(1,-1.25) translate(9.40296e-13,-1642.53)" width="15" x="1411.57195784456" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="895.8505469982767" zvalue="398"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454332186627" ObjectName="10kV黄家寨线"/>
   <cge:TPSR_Ref TObjectID="6192454332186627"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1419.07,914.601) scale(1,-1.25) translate(9.40296e-13,-1642.53)" width="15" x="1411.57195784456" y="895.8505469982767"/></g>
  <g id="318">
   <use class="kv10" height="30" transform="rotate(0,1229.1,914.601) scale(1,-1.25) translate(8.1375e-13,-1642.53)" width="15" x="1221.60197146285" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="895.8505469982767" zvalue="412"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454331924483" ObjectName="10kV中万线"/>
   <cge:TPSR_Ref TObjectID="6192454331924483"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1229.1,914.601) scale(1,-1.25) translate(8.1375e-13,-1642.53)" width="15" x="1221.60197146285" y="895.8505469982767"/></g>
  <g id="306">
   <use class="kv10" height="30" transform="rotate(0,1129.1,914.601) scale(1,-1.25) translate(7.47137e-13,-1642.53)" width="15" x="1121.60197146285" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="895.8505469982767" zvalue="424"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454331662339" ObjectName="10kV赛岗线"/>
   <cge:TPSR_Ref TObjectID="6192454331662339"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1129.1,914.601) scale(1,-1.25) translate(7.47137e-13,-1642.53)" width="15" x="1121.60197146285" y="895.8505469982767"/></g>
  <g id="295">
   <use class="kv10" height="30" transform="rotate(0,1323.1,913.691) scale(1,-1.25) translate(8.76367e-13,-1640.89)" width="15" x="1315.60197146285" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="894.941456089186" zvalue="440"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454331334659" ObjectName="10kV备用Ⅳ线"/>
   <cge:TPSR_Ref TObjectID="6192454331334659"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1323.1,913.691) scale(1,-1.25) translate(8.76367e-13,-1640.89)" width="15" x="1315.60197146285" y="894.941456089186"/></g>
  <g id="97">
   <use class="kv10" height="30" transform="rotate(0,1518.5,847.841) scale(2.45,2.45) translate(-884.204,-480.033)" width="20" x="1494" xlink:href="#EnergyConsumer:站用变带熔断器YY_0" y="811.0909090909091" zvalue="453"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454331072515" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1518.5,847.841) scale(2.45,2.45) translate(-884.204,-480.033)" width="20" x="1494" y="811.0909090909091"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,93,526.5) scale(1,1) translate(0,0)" writing-mode="lr" x="92.66" xml:space="preserve" y="531.1900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134884651010" ObjectName=""/>
   </metadata>
  </g>
  <g id="521">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="521" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,93,552.079) scale(1,1) translate(0,5.99609e-14)" writing-mode="lr" x="92.66" xml:space="preserve" y="556.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134884388866" ObjectName=""/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="115" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,94,577.329) scale(1,1) translate(0,6.27642e-14)" writing-mode="lr" x="93.66" xml:space="preserve" y="582.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134884454402" ObjectName=""/>
   </metadata>
  </g>
  <g id="114">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="114" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,93,607.579) scale(1,1) translate(0,0)" writing-mode="lr" x="92.66" xml:space="preserve" y="612.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134884519938" ObjectName=""/>
   </metadata>
  </g>
  <g id="185">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="185" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,93,630.829) scale(1,1) translate(0,2.74815e-13)" writing-mode="lr" x="92.66" xml:space="preserve" y="635.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134884847618" ObjectName=""/>
   </metadata>
  </g>
  <g id="1040">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="1040" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,224.234,526.5) scale(1,1) translate(0,0)" writing-mode="lr" x="223.9" xml:space="preserve" y="531.1900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134883078146" ObjectName=""/>
   </metadata>
  </g>
  <g id="1041">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1041" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,224.234,552.079) scale(1,1) translate(0,5.99609e-14)" writing-mode="lr" x="223.9" xml:space="preserve" y="556.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134882816002" ObjectName=""/>
   </metadata>
  </g>
  <g id="1042">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="1042" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,224.234,577.329) scale(1,1) translate(0,-1.25528e-13)" writing-mode="lr" x="223.9" xml:space="preserve" y="582.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134882881538" ObjectName=""/>
   </metadata>
  </g>
  <g id="1043">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="1043" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,224.234,607.579) scale(1,1) translate(0,-1.32245e-13)" writing-mode="lr" x="223.9" xml:space="preserve" y="612.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134882947074" ObjectName=""/>
   </metadata>
  </g>
  <g id="1044">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="1044" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,224.234,630.829) scale(1,1) translate(0,0)" writing-mode="lr" x="223.9" xml:space="preserve" y="635.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134883274754" ObjectName=""/>
   </metadata>
  </g>
  <g id="1050">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="1050" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,287.202,526.5) scale(1,1) translate(0,0)" writing-mode="lr" x="286.87" xml:space="preserve" y="531.1900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134884126722" ObjectName=""/>
   </metadata>
  </g>
  <g id="1051">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1051" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,287.202,552.079) scale(1,1) translate(0,-1.19922e-13)" writing-mode="lr" x="286.87" xml:space="preserve" y="556.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134883864578" ObjectName=""/>
   </metadata>
  </g>
  <g id="1052">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="1052" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,287.202,577.329) scale(1,1) translate(0,-1.25528e-13)" writing-mode="lr" x="286.87" xml:space="preserve" y="582.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134883930114" ObjectName=""/>
   </metadata>
  </g>
  <g id="1053">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="1053" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,287.202,607.579) scale(1,1) translate(0,2.6449e-13)" writing-mode="lr" x="286.87" xml:space="preserve" y="612.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134883995650" ObjectName=""/>
   </metadata>
  </g>
  <g id="1054">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="1054" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,287.202,630.829) scale(1,1) translate(0,0)" writing-mode="lr" x="286.87" xml:space="preserve" y="635.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134884323330" ObjectName=""/>
   </metadata>
  </g>
  <g id="1060">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="1060" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,157.734,526.5) scale(1,1) translate(0,0)" writing-mode="lr" x="157.4" xml:space="preserve" y="531.1900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134882029570" ObjectName=""/>
   </metadata>
  </g>
  <g id="1061">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1061" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,157.734,552.079) scale(1,1) translate(0,5.99609e-14)" writing-mode="lr" x="157.4" xml:space="preserve" y="556.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134881767426" ObjectName=""/>
   </metadata>
  </g>
  <g id="1062">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="1062" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,157.734,577.329) scale(1,1) translate(0,-1.25528e-13)" writing-mode="lr" x="157.4" xml:space="preserve" y="582.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134881832962" ObjectName=""/>
   </metadata>
  </g>
  <g id="1063">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="1063" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,157.734,607.579) scale(1,1) translate(0,-1.32245e-13)" writing-mode="lr" x="157.4" xml:space="preserve" y="612.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134881898498" ObjectName=""/>
   </metadata>
  </g>
  <g id="1064">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="1064" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,157.734,630.829) scale(1,1) translate(0,0)" writing-mode="lr" x="157.4" xml:space="preserve" y="635.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134882226178" ObjectName=""/>
   </metadata>
  </g>
  <g id="1070">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="1070" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,351.937,526.5) scale(1,1) translate(0,0)" writing-mode="lr" x="351.6" xml:space="preserve" y="531.1900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134878883842" ObjectName=""/>
   </metadata>
  </g>
  <g id="1071">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1071" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,351.937,552.079) scale(1,1) translate(0,-1.19922e-13)" writing-mode="lr" x="351.6" xml:space="preserve" y="556.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134878621698" ObjectName=""/>
   </metadata>
  </g>
  <g id="1072">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="1072" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,352.937,577.329) scale(1,1) translate(0,6.27642e-14)" writing-mode="lr" x="352.6" xml:space="preserve" y="582.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134878687234" ObjectName=""/>
   </metadata>
  </g>
  <g id="1073">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="1073" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,351.937,607.579) scale(1,1) translate(0,2.6449e-13)" writing-mode="lr" x="351.6" xml:space="preserve" y="612.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134878752770" ObjectName=""/>
   </metadata>
  </g>
  <g id="1074">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="1074" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,351.937,630.829) scale(1,1) translate(0,0)" writing-mode="lr" x="351.6" xml:space="preserve" y="635.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134879080450" ObjectName=""/>
   </metadata>
  </g>
  <g id="184">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="184" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,161.714,169) scale(1,1) translate(0,0)" writing-mode="lr" x="161.35" xml:space="preserve" y="175.18" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134896185346" ObjectName=""/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="100" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,341.714,168.643) scale(1,1) translate(0,0)" writing-mode="lr" x="341.35" xml:space="preserve" y="174.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134896250882" ObjectName=""/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="16" id="98" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,161.714,192.729) scale(1,1) translate(0,0)" writing-mode="lr" x="161.35" xml:space="preserve" y="198.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134884782082" ObjectName=""/>
   </metadata>
  </g>
  <g id="83">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="16" id="83" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,342.714,193.643) scale(1,1) translate(0,0)" writing-mode="lr" x="342.35" xml:space="preserve" y="199.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134882160642" ObjectName=""/>
   </metadata>
  </g>
  <g id="82">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="16" id="82" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,161.714,218.457) scale(1,1) translate(0,0)" writing-mode="lr" x="161.35" xml:space="preserve" y="224.64" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134883209218" ObjectName=""/>
   </metadata>
  </g>
  <g id="1046">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="16" id="1046" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,160.714,263.914) scale(1,1) translate(0,-2.24189e-13)" writing-mode="lr" x="160.35" xml:space="preserve" y="270.1" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134880129026" ObjectName=""/>
   </metadata>
  </g>
  <g id="1049">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="16" id="1049" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,161.714,240.186) scale(1,1) translate(0,0)" writing-mode="lr" x="161.35" xml:space="preserve" y="246.37" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134884257794" ObjectName=""/>
   </metadata>
  </g>
  <g id="1055">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="16" id="1055" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,343.714,241.643) scale(1,1) translate(0,0)" writing-mode="lr" x="343.35" xml:space="preserve" y="247.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134879014914" ObjectName=""/>
   </metadata>
  </g>
  <g id="387">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="387" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1081.56,24.8122) scale(1,1) translate(0,0)" writing-mode="lr" x="1081.09" xml:space="preserve" y="29.48" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134876000258" ObjectName="P"/>
   </metadata>
  </g>
  <g id="389">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="389" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,656.556,24.8122) scale(1,1) translate(0,0)" writing-mode="lr" x="656.09" xml:space="preserve" y="29.48" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134877573122" ObjectName="P"/>
   </metadata>
  </g>
  <g id="390">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="390" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1081.56,43.8122) scale(1,1) translate(0,0)" writing-mode="lr" x="1081.09" xml:space="preserve" y="48.48" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134876065794" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="393">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="393" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,656.556,43.8122) scale(1,1) translate(0,0)" writing-mode="lr" x="656.09" xml:space="preserve" y="48.48" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134877638658" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="394">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="394" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1081.56,62.8122) scale(1,1) translate(0,0)" writing-mode="lr" x="1081.09" xml:space="preserve" y="67.48" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134876131330" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="395">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="395" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,656.556,62.8122) scale(1,1) translate(0,0)" writing-mode="lr" x="656.09" xml:space="preserve" y="67.48" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134877704194" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="397">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="397" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,663.102,960.351) scale(1,1) translate(0,0)" writing-mode="lr" x="662.63" xml:space="preserve" y="965.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134868267010" ObjectName="P"/>
   </metadata>
  </g>
  <g id="398">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="398" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,765.072,960.351) scale(1,1) translate(0,0)" writing-mode="lr" x="764.6" xml:space="preserve" y="965.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134869708802" ObjectName="P"/>
   </metadata>
  </g>
  <g id="401">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="401" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,475.102,960.351) scale(1,1) translate(0,0)" writing-mode="lr" x="474.63" xml:space="preserve" y="965.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134884913154" ObjectName="P"/>
   </metadata>
  </g>
  <g id="402">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="402" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,566.602,960.351) scale(1,1) translate(0,0)" writing-mode="lr" x="566.13" xml:space="preserve" y="965.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134886354946" ObjectName="P"/>
   </metadata>
  </g>
  <g id="403">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="403" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1323.1,962.441) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.63" xml:space="preserve" y="967.11" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134888976386" ObjectName="P"/>
   </metadata>
  </g>
  <g id="404">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="404" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1129.1,963.351) scale(1,1) translate(0,0)" writing-mode="lr" x="1128.63" xml:space="preserve" y="968.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134890418178" ObjectName="P"/>
   </metadata>
  </g>
  <g id="405">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="405" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1229.1,963.351) scale(1,1) translate(0,0)" writing-mode="lr" x="1228.63" xml:space="preserve" y="968.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134891859970" ObjectName="P"/>
   </metadata>
  </g>
  <g id="406">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="406" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1419.07,963.351) scale(1,1) translate(0,0)" writing-mode="lr" x="1418.6" xml:space="preserve" y="968.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134893301762" ObjectName="P"/>
   </metadata>
  </g>
  <g id="408">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="408" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,663.102,983.351) scale(1,1) translate(0,0)" writing-mode="lr" x="662.63" xml:space="preserve" y="988.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134868332546" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="409">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="409" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,765.072,983.351) scale(1,1) translate(0,0)" writing-mode="lr" x="764.6" xml:space="preserve" y="988.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134869774338" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="412">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="412" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,475.102,983.351) scale(1,1) translate(0,0)" writing-mode="lr" x="474.63" xml:space="preserve" y="988.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134884978690" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="413">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="413" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,566.602,983.351) scale(1,1) translate(0,0)" writing-mode="lr" x="566.13" xml:space="preserve" y="988.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134886420482" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="414">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="414" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1323.1,985.441) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.63" xml:space="preserve" y="990.11" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134889041922" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="415">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="415" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1129.1,986.351) scale(1,1) translate(0,0)" writing-mode="lr" x="1128.63" xml:space="preserve" y="991.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134890483714" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="416">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="416" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1229.1,986.351) scale(1,1) translate(0,0)" writing-mode="lr" x="1228.63" xml:space="preserve" y="991.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134891925506" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="417">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="417" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1419.07,986.351) scale(1,1) translate(0,0)" writing-mode="lr" x="1418.6" xml:space="preserve" y="991.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134893367298" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="419">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="419" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,663.102,1006.35) scale(1,1) translate(0,0)" writing-mode="lr" x="662.63" xml:space="preserve" y="1011.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134868398082" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="420">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="420" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,765.072,1006.35) scale(1,1) translate(0,0)" writing-mode="lr" x="764.6" xml:space="preserve" y="1011.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134869839874" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="423">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="423" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,475.102,1006.35) scale(1,1) translate(0,0)" writing-mode="lr" x="474.63" xml:space="preserve" y="1011.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134885044226" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="424">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="424" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,566.602,1006.35) scale(1,1) translate(0,0)" writing-mode="lr" x="566.13" xml:space="preserve" y="1011.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134886486018" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="425">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="425" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1323.1,1008.44) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.63" xml:space="preserve" y="1013.11" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134889107458" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="426">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="426" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1129.1,1009.35) scale(1,1) translate(0,0)" writing-mode="lr" x="1128.63" xml:space="preserve" y="1014.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134890549250" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="427">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="427" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1229.1,1009.35) scale(1,1) translate(0,0)" writing-mode="lr" x="1228.63" xml:space="preserve" y="1014.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134891991042" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="428">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="428" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1419.07,1009.35) scale(1,1) translate(0,0)" writing-mode="lr" x="1418.6" xml:space="preserve" y="1014.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134893432834" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="431">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="431" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,985.544,581.708) scale(1,1) translate(0,0)" writing-mode="lr" x="985.08" xml:space="preserve" y="586.37" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134894743554" ObjectName="P"/>
   </metadata>
  </g>
  <g id="432">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="432" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,985.544,600.708) scale(1,1) translate(0,0)" writing-mode="lr" x="985.08" xml:space="preserve" y="605.37" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134894809090" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="433">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="433" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,985.544,617.708) scale(1,1) translate(0,0)" writing-mode="lr" x="985.08" xml:space="preserve" y="622.37" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134894874626" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="436">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="436" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,904.544,226.208) scale(1,1) translate(0,0)" writing-mode="lr" x="904.08" xml:space="preserve" y="230.87" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134874558466" ObjectName="P"/>
   </metadata>
  </g>
  <g id="438">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="438" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,904.544,241.208) scale(1,1) translate(0,0)" writing-mode="lr" x="904.08" xml:space="preserve" y="245.87" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134874624002" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="444">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="444" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,904.544,258.208) scale(1,1) translate(0,0)" writing-mode="lr" x="904.08" xml:space="preserve" y="262.87" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134874689538" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="445">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="445" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,635.523,403.702) scale(1,1) translate(0,-2.61925e-13)" writing-mode="lr" x="635.05" xml:space="preserve" y="408.37" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134879145986" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="446">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="446" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,635.523,426.702) scale(1,1) translate(0,0)" writing-mode="lr" x="635.05" xml:space="preserve" y="431.37" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134879211522" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="447">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="447" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,635.523,449.702) scale(1,1) translate(0,0)" writing-mode="lr" x="635.05" xml:space="preserve" y="454.37" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134879277058" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="448">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="448" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,910.143,469.309) scale(1,1) translate(0,0)" writing-mode="lr" x="909.67" xml:space="preserve" y="473.98" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134879604738" ObjectName="MP"/>
   </metadata>
  </g>
  <g id="449">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="449" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,910.143,492.309) scale(1,1) translate(0,1.06983e-13)" writing-mode="lr" x="909.67" xml:space="preserve" y="496.98" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134879670274" ObjectName="MQ"/>
   </metadata>
  </g>
  <g id="450">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="450" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,910.143,515.309) scale(1,1) translate(0,0)" writing-mode="lr" x="909.67" xml:space="preserve" y="519.98" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134879932418" ObjectName="MIa"/>
   </metadata>
  </g>
  <g id="451">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="451" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,660.951,619.488) scale(1,1) translate(0,0)" writing-mode="lr" x="660.48" xml:space="preserve" y="624.15" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134879408130" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="452">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="452" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,660.951,573.488) scale(1,1) translate(0,0)" writing-mode="lr" x="660.48" xml:space="preserve" y="578.15" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134879735810" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="453">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="453" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,659.701,596.488) scale(1,1) translate(0,0)" writing-mode="lr" x="659.23" xml:space="preserve" y="601.15" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134879801346" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="421">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="421" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,550.234,171.5) scale(1,1) translate(0,7.08322e-14)" writing-mode="lr" x="549.9" xml:space="preserve" y="176.19" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134884651010" ObjectName=""/>
   </metadata>
  </g>
  <g id="422">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="422" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1231.23,177.5) scale(1,1) translate(0,7.34968e-14)" writing-mode="lr" x="1230.9" xml:space="preserve" y="182.19" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134882029570" ObjectName=""/>
   </metadata>
  </g>
  <g id="454">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="454" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1532.23,272.5) scale(1,1) translate(0,1.15685e-13)" writing-mode="lr" x="1531.9" xml:space="preserve" y="277.19" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134883078146" ObjectName=""/>
   </metadata>
  </g>
  <g id="456">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="456" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,538.234,542.5) scale(1,1) translate(0,0)" writing-mode="lr" x="537.9" xml:space="preserve" y="547.1900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134884126722" ObjectName=""/>
   </metadata>
  </g>
  <g id="457">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="457" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1328.23,538.5) scale(1,1) translate(0,5.84532e-14)" writing-mode="lr" x="1327.9" xml:space="preserve" y="543.1900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134878883842" ObjectName=""/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="1057">
   <use height="30" transform="rotate(0,333.339,332.984) scale(0.708333,0.665547) translate(132.882,162.316)" width="30" x="322.71" xlink:href="#State:红绿圆(方形)_0" y="323" zvalue="986"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374925365249" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,333.339,332.984) scale(0.708333,0.665547) translate(132.882,162.316)" width="30" x="322.71" y="323"/></g>
  <g id="1056">
   <use height="30" transform="rotate(0,238.714,332.984) scale(0.708333,0.665547) translate(93.9191,162.316)" width="30" x="228.09" xlink:href="#State:红绿圆(方形)_0" y="323" zvalue="987"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562954205134855" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,238.714,332.984) scale(0.708333,0.665547) translate(93.9191,162.316)" width="30" x="228.09" y="323"/></g>
  <g id="830">
   <use height="30" transform="rotate(0,318.812,121.964) scale(1.22222,1.03092) translate(-47.9659,-3.1944)" width="90" x="263.81" xlink:href="#State:全站检修_0" y="106.5" zvalue="1125"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549681848321" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,318.812,121.964) scale(1.22222,1.03092) translate(-47.9659,-3.1944)" width="90" x="263.81" y="106.5"/></g>
  <g id="1349">
   <use height="30" transform="rotate(0,276.235,425.5) scale(0.910937,0.8) translate(23.445,103.375)" width="80" x="239.8" xlink:href="#State:间隔模板_0" y="413.5" zvalue="1148"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629499717058563" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,276.235,425.5) scale(0.910937,0.8) translate(23.445,103.375)" width="80" x="239.8" y="413.5"/></g>
  <g id="1299">
   <use height="30" transform="rotate(0,74.2857,337.039) scale(0.910937,0.8) translate(3.70042,81.2597)" width="80" x="37.85" xlink:href="#State:间隔模板_0" y="325.04" zvalue="1149"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629500429172738" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,74.2857,337.039) scale(0.910937,0.8) translate(3.70042,81.2597)" width="80" x="37.85" y="325.04"/></g>
 </g>
</svg>