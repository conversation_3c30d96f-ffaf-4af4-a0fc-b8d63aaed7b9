<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="550" id="thSvg" source="NR-PCS9000" viewBox="0 0 1000 550" width="1000">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:PT2_0" viewBox="0,0,13,14">
   <use terminal-index="0" type="0" x="5.070083175780933" xlink:href="#terminal" y="0.6391120299271504"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.5" x2="2.5" y1="4" y2="4"/>
   <path d="M 2.05 2.65 L 2.05 0.65 L 8.05 0.65 L 8.05 2.65" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="7.6" cy="5.61" fill-opacity="0" rx="2.83" ry="2.96" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.6" cy="10.83" fill-opacity="0" rx="2.83" ry="2.96" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.65" cy="7.84" fill-opacity="0" rx="2.83" ry="2.96" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.08,6.24) scale(1,1) translate(0,0)" width="7.05" x="-1.44" y="4.72"/>
   <path d="M 1.0952 8.13174 L 3.18498 8.13174 L 2.12267 5.75754 L 1.0952 8.13174" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.119763607738507" x2="2.119763607738507" y1="6.066666666666667" y2="12.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.5524275179570539" x2="3.68709969751996" y1="12.28704961606615" y2="12.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.149066340209899" x2="3.238847793251836" y1="12.98364343374679" y2="12.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.603758628586885" x2="2.561575127897769" y1="13.50608879700729" y2="13.50608879700729"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV芒市变" InitShowingPlane="" fill="rgb(0,0,0)" height="550" width="1000" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="71.52" id="1" preserveAspectRatio="xMidYMid slice" width="228.79" x="27.88" xlink:href="logo.png" y="16.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,142.272,52.1863) scale(1,1) translate(-6.19019e-15,3.64757e-15)" writing-mode="lr" x="142.27" xml:space="preserve" y="55.69" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,168.704,52.1661) scale(1,1) translate(-2.01662e-14,9.78508e-15)" writing-mode="lr" x="168.7" xml:space="preserve" y="61.17" zvalue="3">110kV芒市变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,395.083,382.702) scale(1,1) translate(-8.05097e-14,0)" writing-mode="lr" x="395.08" xml:space="preserve" y="387.2" zvalue="7">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,567.912,126.384) scale(1,1) translate(0,0)" writing-mode="lr" x="567.91" xml:space="preserve" y="130.88" zvalue="11">110kV芒市II回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,543.145,302.645) scale(1,1) translate(0,0)" writing-mode="lr" x="543.15" xml:space="preserve" y="307.14" zvalue="12">131</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,584.623,233.921) scale(1,1) translate(0,0)" writing-mode="lr" x="584.62" xml:space="preserve" y="238.42" zvalue="14">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,534.343,188.982) scale(1,1) translate(0,0)" writing-mode="lr" x="534.34" xml:space="preserve" y="193.48" zvalue="16">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,583.928,365.383) scale(1,1) translate(0,0)" writing-mode="lr" x="583.9299999999999" xml:space="preserve" y="369.88" zvalue="18">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,534.954,252.713) scale(1,1) translate(0,0)" writing-mode="lr" x="534.95" xml:space="preserve" y="257.21" zvalue="20">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,533.732,325.435) scale(1,1) translate(0,0)" writing-mode="lr" x="533.73" xml:space="preserve" y="329.93" zvalue="22">17</text>
 </g>
 <g id="ButtonClass"/>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="48">
   <path class="kv110" d="M 405.33 397.09 L 742.33 397.09" stroke-width="6" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674266054661" ObjectName="110kV母线"/>
   </metadata>
  <path d="M 405.33 397.09 L 742.33 397.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="46">
   <use class="kv110" height="30" transform="rotate(0,568.454,143.415) scale(1.98323,0.522926) translate(-278.382,123.684)" width="7" x="561.5122674530712" xlink:href="#ACLineSegment:线路_0" y="135.5714185098223" zvalue="9"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450420998149" ObjectName="110kV芒市II回线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,568.454,143.415) scale(1.98323,0.522926) translate(-278.382,123.684)" width="7" x="561.5122674530712" y="135.5714185098223"/></g>
 </g>
 <g id="BreakerClass">
  <g id="45">
   <use class="kv110" height="20" transform="rotate(0,567.208,303.02) scale(1.5542,1.35421) translate(-199.485,-75.7165)" width="10" x="559.4368553761287" xlink:href="#Breaker:开关_0" y="289.4779758822602" zvalue="10"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924607606789" ObjectName="110kV芒市II回线131断路器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,567.208,303.02) scale(1.5542,1.35421) translate(-199.485,-75.7165)" width="10" x="559.4368553761287" y="289.4779758822602"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="44">
   <use class="kv110" height="30" transform="rotate(0,568.37,237.588) scale(-0.947693,-0.6712) translate(-1168.5,-596.494)" width="15" x="561.2626847865957" xlink:href="#Disconnector:刀闸_0" y="227.5197927208583" zvalue="13"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450420932613" ObjectName="110kV芒市II回线1316隔离开关"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,568.37,237.588) scale(-0.947693,-0.6712) translate(-1168.5,-596.494)" width="15" x="561.2626847865957" y="227.5197927208583"/></g>
  <g id="36">
   <use class="kv110" height="30" transform="rotate(180,567.228,368.857) scale(0.947693,0.6712) translate(30.9154,175.759)" width="15" x="560.120582888625" xlink:href="#Disconnector:刀闸_0" y="358.7885304928169" zvalue="17"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450420736005" ObjectName="110kV芒市II回线1311隔离开关"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,567.228,368.857) scale(0.947693,0.6712) translate(30.9154,175.759)" width="15" x="560.120582888625" y="358.7885304928169"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="38">
   <use class="kv110" height="20" transform="rotate(270,536.332,204.981) scale(-1.24619,-1.0068) translate(-965.48,-408.51)" width="10" x="530.1015604744066" xlink:href="#GroundDisconnector:地刀_0" y="194.9131744799927" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450420867077" ObjectName="110kV芒市II回线13167接地开关"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,536.332,204.981) scale(-1.24619,-1.0068) translate(-965.48,-408.51)" width="10" x="530.1015604744066" y="194.9131744799927"/></g>
  <g id="33">
   <use class="kv110" height="20" transform="rotate(270,537.332,268.045) scale(-1.24619,-1.0068) translate(-967.283,-534.212)" width="10" x="531.1015604744066" xlink:href="#GroundDisconnector:地刀_0" y="257.9772402522076" zvalue="19"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450420670469" ObjectName="110kV芒市II回线13160接地开关"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,537.332,268.045) scale(-1.24619,-1.0068) translate(-967.283,-534.212)" width="10" x="531.1015604744066" y="257.9772402522076"/></g>
  <g id="31">
   <use class="kv110" height="20" transform="rotate(90,535.999,341.267) scale(1.24619,1.0068) translate(-104.657,-2.23715)" width="10" x="529.7682271410732" xlink:href="#GroundDisconnector:地刀_0" y="331.1988952030086" zvalue="21"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450420539397" ObjectName="110kV芒市II回线13117接地开关"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,535.999,341.267) scale(1.24619,1.0068) translate(-104.657,-2.23715)" width="10" x="529.7682271410732" y="331.1988952030086"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="25">
   <path class="kv110" d="M 567.15 378.59 L 567.15 397.09" stroke-width="1" zvalue="23"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 567.15 378.59 L 567.15 397.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv110" d="M 568.45 151.18 L 568.45 227.69" stroke-width="1" zvalue="31"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="44@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 568.45 151.18 L 568.45 227.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv110" d="M 568.29 247.32 L 568.29 290.06" stroke-width="1" zvalue="32"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="45@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 568.29 247.32 L 568.29 290.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv110" d="M 567.31 315.95 L 567.31 358.96" stroke-width="1" zvalue="33"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@1" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 567.31 315.95 L 567.31 358.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv110" d="M 613.56 183.68 L 613.56 168.33 L 568.45 168.33" stroke-width="1" zvalue="39"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 613.56 183.68 L 613.56 168.33 L 568.45 168.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv110" d="M 546.15 205.04 L 568.45 205.04" stroke-width="1" zvalue="40"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 546.15 205.04 L 568.45 205.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv110" d="M 547.15 268.11 L 568.29 268.11" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="20" MaxPinNum="2"/>
   </metadata>
  <path d="M 547.15 268.11 L 568.29 268.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv110" d="M 545.82 341.33 L 567.31 341.33" stroke-width="1" zvalue="42"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 545.82 341.33 L 567.31 341.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="24">
   <use class="kv110" height="14" transform="rotate(0,617.331,199.268) scale(2.63919,2.45068) translate(-372.768,-107.802)" width="13" x="600.1765754336402" xlink:href="#Accessory:PT2_0" y="182.1134350719785" zvalue="24"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450420408325" ObjectName="110kV芒市II回线电压互感器"/>
   </metadata>
  <rect fill="white" height="14" opacity="0" stroke="white" transform="rotate(0,617.331,199.268) scale(2.63919,2.45068) translate(-372.768,-107.802)" width="13" x="600.1765754336402" y="182.1134350719785"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="4" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,568.454,40.0714) scale(1,1) translate(0,0)" writing-mode="lr" x="525.33" xml:space="preserve" y="44.43" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="5" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,568.454,77.0714) scale(1,1) translate(0,0)" writing-mode="lr" x="525.33" xml:space="preserve" y="81.43000000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="14" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,571.454,107.071) scale(1,1) translate(0,0)" writing-mode="lr" x="528.33" xml:space="preserve" y="111.43" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="15" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,358.333,295.591) scale(1,1) translate(0,0)" writing-mode="lr" x="315.21" xml:space="preserve" y="299.95" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="16" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,358.333,332.591) scale(1,1) translate(0,0)" writing-mode="lr" x="315.21" xml:space="preserve" y="336.95" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="17" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,358.333,369.591) scale(1,1) translate(0,0)" writing-mode="lr" x="315.21" xml:space="preserve" y="373.95" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
 </g>
</svg>