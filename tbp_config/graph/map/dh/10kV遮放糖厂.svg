<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549588525058" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="4.583333333333332" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:三相刀闸_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="30"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="23.66666666666666" y2="29.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.25" x2="15.08333333333333" y1="5.999999999999998" y2="23.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.46666666666667" x2="16.43333333333333" y1="5.755662181544974" y2="5.755662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.750000000000002" y2="0.3554618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="4.416666666666668" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.249999999999998" y2="20.16666666666667"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.45181617405996" x2="1.714850492606708" y1="5.276370517142858" y2="14.05696281619048"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.583333333333333" x2="8.583333333333334" y1="5.166666666666667" y2="14.16666666666667"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Accessory:腊撒线路PT_0" viewBox="0,0,12,32">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.6666666666666661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="3" y1="27" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="27" y2="30"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="13.66666666666667" y2="1"/>
   <path d="M 6 15 L 3 20 L 9 20 L 6 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="9" y1="27" y2="25"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,8) scale(1,1) translate(0,0)" width="4" x="4" y="5"/>
   <ellipse cx="5.9" cy="18.6" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.9" cy="26.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:线路PT11带避雷器_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="23.75" y2="23.75"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,11) scale(1,1) translate(0,0)" width="6" x="17" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="20" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="32" y1="17" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.13040845230975" x2="32.13040845230975" y1="16.76788570496156" y2="23.53635804601289"/>
   <rect fill-opacity="0" height="6.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,32.04,24.56) scale(1,1) translate(0,0)" width="15.34" x="24.37" y="21.53"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.98821300514558" x2="19.98821300514558" y1="1.029523490692871" y2="18.75"/>
   <path d="M 30.0147 23.4481 L 34.2865 23.4481 L 32.115 28.6155 L 30.0147 23.4481" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <ellipse cx="19.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.22179295459144" x2="32.22179295459144" y1="32.22550978083666" y2="36.2625308385742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.01788570496155" x2="35.42570020422134" y1="36.23023467011234" y2="36.23023467011234"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.26384963537316" x2="34.53572596821302" y1="37.52208140858838" y2="37.52208140858838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.15382387138148" x2="33.11176719059974" y1="38.49096646244535" y2="38.49096646244535"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="32.91666666666667" y2="32.91666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="1"/>
   <ellipse cx="14.99" cy="7.93" fill-opacity="0" rx="7.43" ry="6.85" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.93" cy="17.56" fill-opacity="0" rx="7.43" ry="6.85" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.9834232682896" x2="14.9834232682896" y1="14.95787681993586" y2="18.2114918126664"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.98342326828961" x2="11.56276000329148" y1="18.23128772350278" y2="20.61950731913853"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00726151837816" x2="18.51522958638884" y1="18.19641261556525" y2="20.42154821077479"/>
   <path d="M 11.6895 9.29402 L 18.7806 9.29402 L 15.2653 3.08877 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV遮放糖厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,173.046,95.3772) scale(1,1) translate(8.69379e-15,-5.83331e-14)" writing-mode="lr" x="173.05" xml:space="preserve" y="99.88" zvalue="34"/>
  <line fill="none" id="61" stroke="rgb(0,39,45)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.22222222222263" x2="324.8097708603561" y1="78.45055797356122" y2="78.45055797356122" zvalue="36"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="1" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,236.875,64.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="236.88" xml:space="preserve" y="73.69" zvalue="179">10kV遮放糖厂</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="277.25" x="57.75" xlink:href="logo.png" y="36.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.375,66.75) scale(1,1) translate(0,0)" writing-mode="lr" x="196.38" xml:space="preserve" y="70.25" zvalue="180"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,191,63.9403) scale(1,1) translate(0,0)" writing-mode="lr" x="191" xml:space="preserve" y="72.94" zvalue="181">10kV遮放糖厂</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="26" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,89.4375,372) scale(1,1) translate(0,0)" width="72.88" x="53" y="360" zvalue="188"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89.4375,372) scale(1,1) translate(0,0)" writing-mode="lr" x="89.44" xml:space="preserve" y="376.5" zvalue="188">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1454.5,421) scale(1,1) translate(0,0)" writing-mode="lr" x="1454.5" xml:space="preserve" y="425.5" zvalue="2">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1169.5,379) scale(1,1) translate(0,0)" writing-mode="lr" x="1169.5" xml:space="preserve" y="383.5" zvalue="3">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1005,288) scale(1,1) translate(0,0)" writing-mode="lr" x="1005" xml:space="preserve" y="292.5" zvalue="4">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1093,231) scale(1,1) translate(0,0)" writing-mode="lr" x="1093" xml:space="preserve" y="235.5" zvalue="5">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1179.5,272) scale(1,1) translate(0,0)" writing-mode="lr" x="1179.5" xml:space="preserve" y="276.5" zvalue="6">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1132,128.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1132" xml:space="preserve" y="133" zvalue="8">10kV遮放糖厂线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,569.384,529.028) scale(1,1) translate(0,0)" writing-mode="lr" x="569.38" xml:space="preserve" y="533.53" zvalue="16">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,710.394,706.278) scale(1,1) translate(-4.60228e-13,0)" writing-mode="lr" x="710.39" xml:space="preserve" y="710.78" zvalue="21">站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,666.013,531.694) scale(1,1) translate(0,0)" writing-mode="lr" x="666.01" xml:space="preserve" y="536.1900000000001" zvalue="24">0811</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,758.308,521.842) scale(1,1) translate(0,0)" writing-mode="lr" x="758.3099999999999" xml:space="preserve" y="526.34" zvalue="27">053</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,791.9,707.389) scale(1,1) translate(0,0)" writing-mode="lr" x="791.9" xml:space="preserve" y="711.89" zvalue="30">备用</text>
  <line fill="none" id="59" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="381" x2="381" y1="51.25" y2="1041.25" zvalue="37"/>
  <line fill="none" id="56" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.47692454998003" x2="324.0881846035992" y1="170.8779458827686" y2="170.8779458827686" zvalue="39"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.57142857142844" x2="119.0114285714285" y1="929.8127909390723" y2="929.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.57142857142844" x2="119.0114285714285" y1="981.1522909390724" y2="981.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.57142857142844" x2="47.57142857142844" y1="929.8127909390723" y2="981.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.0114285714285" x2="119.0114285714285" y1="929.8127909390723" y2="981.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.0119285714285" x2="353.5719285714284" y1="929.8127909390723" y2="929.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.0119285714285" x2="353.5719285714284" y1="981.1522909390724" y2="981.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.0119285714285" x2="119.0119285714285" y1="929.8127909390723" y2="981.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="353.5719285714284" x2="353.5719285714284" y1="929.8127909390723" y2="981.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.57142857142844" x2="119.0114285714285" y1="981.1522709390724" y2="981.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.57142857142844" x2="119.0114285714285" y1="1008.629770939072" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.57142857142844" x2="47.57142857142844" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.0114285714285" x2="119.0114285714285" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.0119285714285" x2="190.0472285714285" y1="981.1522709390724" y2="981.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.0119285714285" x2="190.0472285714285" y1="1008.629770939072" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.0119285714285" x2="119.0119285714285" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.0472285714285" x2="190.0472285714285" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.0472285714285" x2="271.8093285714284" y1="981.1522709390724" y2="981.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.0472285714285" x2="271.8093285714284" y1="1008.629770939072" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.0472285714285" x2="190.0472285714285" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.8093285714284" x2="271.8093285714284" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.8092285714284" x2="353.5713285714285" y1="981.1522709390724" y2="981.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.8092285714284" x2="353.5713285714285" y1="1008.629770939072" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.8092285714284" x2="271.8092285714284" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="353.5713285714285" x2="353.5713285714285" y1="981.1522709390724" y2="1008.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.57142857142844" x2="119.0114285714285" y1="1008.629690939072" y2="1008.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.57142857142844" x2="119.0114285714285" y1="1036.107190939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.57142857142844" x2="47.57142857142844" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.0114285714285" x2="119.0114285714285" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.0119285714285" x2="190.0472285714285" y1="1008.629690939072" y2="1008.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.0119285714285" x2="190.0472285714285" y1="1036.107190939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.0119285714285" x2="119.0119285714285" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.0472285714285" x2="190.0472285714285" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.0472285714285" x2="271.8093285714284" y1="1008.629690939072" y2="1008.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.0472285714285" x2="271.8093285714284" y1="1036.107190939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.0472285714285" x2="190.0472285714285" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.8093285714284" x2="271.8093285714284" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.8092285714284" x2="353.5713285714285" y1="1008.629690939072" y2="1008.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.8092285714284" x2="353.5713285714285" y1="1036.107190939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.8092285714284" x2="271.8092285714284" y1="1008.629690939072" y2="1036.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="353.5713285714285" x2="353.5713285714285" y1="1008.629690939072" y2="1036.107190939072"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="54" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,194.624,959.386) scale(1,1) translate(-1.17521e-14,1.05311e-13)" writing-mode="lr" x="52.93" xml:space="preserve" y="965.39" zvalue="41">参考图号     ZheFang-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="53" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,125.149,996.755) scale(1,1) translate(-5.56478e-14,-1.53244e-12)" writing-mode="lr" x="62.65" xml:space="preserve" y="1002.75" zvalue="42">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="51" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,268.452,997.755) scale(1,1) translate(1.3306e-13,-1.53399e-12)" writing-mode="lr" x="199.75" xml:space="preserve" y="1003.75" zvalue="43">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,76.2423,1026.31) scale(1,1) translate(-5.24815e-14,-1.57838e-12)" writing-mode="lr" x="76.23999999999999" xml:space="preserve" y="1032.31" zvalue="44">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="49" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,276.746,1024.31) scale(1,1) translate(0,1.1252e-13)" writing-mode="lr" x="199.92" xml:space="preserve" y="1030.31" zvalue="45">更新日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89.9207,642.686) scale(1,1) translate(4.2788e-15,-1.38553e-13)" writing-mode="lr" x="89.92070806204492" xml:space="preserve" y="647.1863811688671" zvalue="48">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.571428571428442" x2="189.5714285714284" y1="174.1071428571429" y2="174.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.571428571428442" x2="189.5714285714284" y1="200.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.571428571428442" x2="8.571428571428442" y1="174.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="189.5714285714284" y1="174.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="370.5714285714284" y1="174.1071428571429" y2="174.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="370.5714285714284" y1="200.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="189.5714285714284" y1="174.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370.5714285714284" x2="370.5714285714284" y1="174.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.571428571428442" x2="189.5714285714284" y1="200.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.571428571428442" x2="189.5714285714284" y1="224.3571428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.571428571428442" x2="8.571428571428442" y1="200.1071428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="189.5714285714284" y1="200.1071428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="370.5714285714284" y1="200.1071428571429" y2="200.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="370.5714285714284" y1="224.3571428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="189.5714285714284" y1="200.1071428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370.5714285714284" x2="370.5714285714284" y1="200.1071428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.571428571428442" x2="189.5714285714284" y1="224.3571428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.571428571428442" x2="189.5714285714284" y1="247.1071428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.571428571428442" x2="8.571428571428442" y1="224.3571428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="189.5714285714284" y1="224.3571428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="370.5714285714284" y1="224.3571428571429" y2="224.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="370.5714285714284" y1="247.1071428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="189.5714285714284" y1="224.3571428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370.5714285714284" x2="370.5714285714284" y1="224.3571428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.571428571428442" x2="189.5714285714284" y1="247.1071428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.571428571428442" x2="189.5714285714284" y1="269.8571428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.571428571428442" x2="8.571428571428442" y1="247.1071428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="189.5714285714284" y1="247.1071428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="370.5714285714284" y1="247.1071428571429" y2="247.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="370.5714285714284" y1="269.8571428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="189.5714285714284" y1="247.1071428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370.5714285714284" x2="370.5714285714284" y1="247.1071428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.571428571428442" x2="189.5714285714284" y1="269.8571428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.571428571428442" x2="189.5714285714284" y1="292.6071428571429" y2="292.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.571428571428442" x2="8.571428571428442" y1="269.8571428571429" y2="292.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="189.5714285714284" y1="269.8571428571429" y2="292.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="370.5714285714284" y1="269.8571428571429" y2="269.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="370.5714285714284" y1="292.6071428571429" y2="292.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.5714285714284" x2="189.5714285714284" y1="269.8571428571429" y2="292.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370.5714285714284" x2="370.5714285714284" y1="269.8571428571429" y2="292.6071428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46.5714,188.107) scale(1,1) translate(0,0)" writing-mode="lr" x="46.57" xml:space="preserve" y="193.61" zvalue="59">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.821,188.107) scale(1,1) translate(0,0)" writing-mode="lr" x="232.82" xml:space="preserve" y="193.61" zvalue="60">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.7589,217.357) scale(1,1) translate(0,0)" writing-mode="lr" x="57.76" xml:space="preserve" y="221.86" zvalue="61">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,618.333,701.833) scale(1,1) translate(0,0)" writing-mode="lr" x="618.33" xml:space="preserve" y="706.33" zvalue="64">母线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,776.344,609) scale(1,1) translate(0,0)" writing-mode="lr" x="776.34" xml:space="preserve" y="613.5" zvalue="67">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,837,582.698) scale(1,1) translate(0,0)" writing-mode="lr" x="837" xml:space="preserve" y="587.2" zvalue="68">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,878.182,521.842) scale(1,1) translate(0,0)" writing-mode="lr" x="878.1799999999999" xml:space="preserve" y="526.34" zvalue="74">052</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,898.017,708.5) scale(1,1) translate(0,0)" writing-mode="lr" x="898.02" xml:space="preserve" y="713" zvalue="76">老电力间联络</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,880.632,609) scale(1,1) translate(0,0)" writing-mode="lr" x="880.63" xml:space="preserve" y="613.5" zvalue="78">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,940,582.698) scale(1,1) translate(0,0)" writing-mode="lr" x="940" xml:space="preserve" y="587.2" zvalue="81">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1016.27,704.056) scale(1,1) translate(0,0)" writing-mode="lr" x="1016.27" xml:space="preserve" y="708.5599999999999" zvalue="87">#1F  3000k</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,982.384,521.842) scale(1,1) translate(1.0779e-13,0)" writing-mode="lr" x="982.38" xml:space="preserve" y="526.34" zvalue="89">054</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,989.272,609) scale(1,1) translate(0,0)" writing-mode="lr" x="989.27" xml:space="preserve" y="613.5" zvalue="93">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1043,582.698) scale(1,1) translate(0,0)" writing-mode="lr" x="1043" xml:space="preserve" y="587.2" zvalue="94">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1077.67,521.842) scale(1,1) translate(7.10213e-13,0)" writing-mode="lr" x="1077.67" xml:space="preserve" y="526.34" zvalue="101">055</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1118.06,705.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1118.06" xml:space="preserve" y="709.67" zvalue="103">二泵站</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1093.21,609) scale(1,1) translate(0,0)" writing-mode="lr" x="1093.21" xml:space="preserve" y="613.5" zvalue="105">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1148,582.698) scale(1,1) translate(0,0)" writing-mode="lr" x="1148" xml:space="preserve" y="587.2" zvalue="108">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1183.96,521.842) scale(1,1) translate(0,0)" writing-mode="lr" x="1183.96" xml:space="preserve" y="526.34" zvalue="114">056</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1216.26,701.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1216.26" xml:space="preserve" y="706.33" zvalue="116">一泵站</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1195.08,609) scale(1,1) translate(0,0)" writing-mode="lr" x="1195.08" xml:space="preserve" y="613.5" zvalue="118">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1256,582.698) scale(1,1) translate(0,0)" writing-mode="lr" x="1256" xml:space="preserve" y="587.2" zvalue="121">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1330.24,702.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1330.24" xml:space="preserve" y="707.4400000000001" zvalue="127">制炼车间</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1290.25,521.842) scale(1,1) translate(-5.67878e-13,0)" writing-mode="lr" x="1290.25" xml:space="preserve" y="526.34" zvalue="129">057</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1305.14,609) scale(1,1) translate(0,0)" writing-mode="lr" x="1305.14" xml:space="preserve" y="613.5" zvalue="132">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1374,578.766) scale(1,1) translate(0,0)" writing-mode="lr" x="1374" xml:space="preserve" y="583.27" zvalue="133">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1436.25,705.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1436.25" xml:space="preserve" y="709.67" zvalue="140">锅炉车间</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1396.54,521.842) scale(1,1) translate(-6.15079e-13,0)" writing-mode="lr" x="1396.54" xml:space="preserve" y="526.34" zvalue="142">058</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1411.42,609) scale(1,1) translate(0,0)" writing-mode="lr" x="1411.42" xml:space="preserve" y="613.5" zvalue="145">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1474,578.766) scale(1,1) translate(0,0)" writing-mode="lr" x="1474" xml:space="preserve" y="583.27" zvalue="146">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1547.85,705.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1547.85" xml:space="preserve" y="709.67" zvalue="153">压榨整流变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1502.82,521.842) scale(1,1) translate(-6.62281e-13,0)" writing-mode="lr" x="1502.82" xml:space="preserve" y="526.34" zvalue="155">059</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1517.71,609) scale(1,1) translate(0,0)" writing-mode="lr" x="1517.71" xml:space="preserve" y="613.5" zvalue="158">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1594,582.698) scale(1,1) translate(0,0)" writing-mode="lr" x="1594" xml:space="preserve" y="587.2" zvalue="159">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1658.5,698.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1658.5" xml:space="preserve" y="703" zvalue="166">压榨车间</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1608.11,521.842) scale(1,1) translate(0,0)" writing-mode="lr" x="1608.11" xml:space="preserve" y="526.34" zvalue="168">061</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1624,609) scale(1,1) translate(0,0)" writing-mode="lr" x="1624" xml:space="preserve" y="613.5" zvalue="171">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="166" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1698,582.698) scale(1,1) translate(0,0)" writing-mode="lr" x="1698" xml:space="preserve" y="587.2" zvalue="172">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,156.242,1026.31) scale(1,1) translate(-1.41299e-13,-1.57838e-12)" writing-mode="lr" x="156.24" xml:space="preserve" y="1032.31" zvalue="184">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,194.068,370.091) scale(1,1) translate(0,0)" writing-mode="lr" x="194.07" xml:space="preserve" y="374.59" zvalue="186">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,299.068,370.091) scale(1,1) translate(0,0)" writing-mode="lr" x="299.07" xml:space="preserve" y="374.59" zvalue="187">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="53" y="360" zvalue="188"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="1">
   <path class="kv10" d="M 508.57 451 L 1733 451" stroke-width="4" zvalue="1"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674246656004" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674246656004"/></metadata>
  <path d="M 508.57 451 L 1733 451" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="3">
   <use class="kv10" height="20" transform="rotate(0,1125,382) scale(2.2,2.2) translate(-607.636,-196.364)" width="10" x="1114" xlink:href="#Breaker:小车断路器_0" y="360" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924522344453" ObjectName="10kV遮放糖厂线051断路器"/>
   <cge:TPSR_Ref TObjectID="6473924522344453"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1125,382) scale(2.2,2.2) translate(-607.636,-196.364)" width="10" x="1114" y="360"/></g>
  <g id="66">
   <use class="kv10" height="20" transform="rotate(0,795.844,518.25) scale(1.38273,1.35213) translate(-218.369,-131.446)" width="10" x="788.9300166668116" xlink:href="#Breaker:手车开关_0" y="504.7286614123652" zvalue="26"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924522409989" ObjectName="备用线053断路器"/>
   <cge:TPSR_Ref TObjectID="6473924522409989"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,795.844,518.25) scale(1.38273,1.35213) translate(-218.369,-131.446)" width="10" x="788.9300166668116" y="504.7286614123652"/></g>
  <g id="90">
   <use class="kv10" height="20" transform="rotate(0,902.132,518.25) scale(1.38273,1.35213) translate(-247.788,-131.446)" width="10" x="895.2180470126808" xlink:href="#Breaker:手车开关_0" y="504.7286614220793" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924522475525" ObjectName="老电力间联络线052断路器"/>
   <cge:TPSR_Ref TObjectID="6473924522475525"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,902.132,518.25) scale(1.38273,1.35213) translate(-247.788,-131.446)" width="10" x="895.2180470126808" y="504.7286614220793"/></g>
  <g id="94">
   <use class="kv10" height="20" transform="rotate(0,1008.27,518.25) scale(1.35315,1.32321) translate(-261.376,-123.357)" width="10" x="1001.50607735855" xlink:href="#Breaker:手车开关_0" y="505.0178994974946" zvalue="88"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924522541061" ObjectName="#1F054断路器"/>
   <cge:TPSR_Ref TObjectID="6473924522541061"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1008.27,518.25) scale(1.35315,1.32321) translate(-261.376,-123.357)" width="10" x="1001.50607735855" y="505.0178994974946"/></g>
  <g id="116">
   <use class="kv10" height="20" transform="rotate(0,1114.71,518.25) scale(1.38273,1.35213) translate(-306.628,-131.446)" width="10" x="1107.794107704419" xlink:href="#Breaker:手车开关_0" y="504.7286613624747" zvalue="100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924522606597" ObjectName="二泵站线055断路器"/>
   <cge:TPSR_Ref TObjectID="6473924522606597"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1114.71,518.25) scale(1.38273,1.35213) translate(-306.628,-131.446)" width="10" x="1107.794107704419" y="504.7286613624747"/></g>
  <g id="129">
   <use class="kv10" height="20" transform="rotate(0,1221,518.25) scale(1.38273,1.35213) translate(-336.047,-131.446)" width="10" x="1214.082138050288" xlink:href="#Breaker:手车开关_0" y="504.7286613624747" zvalue="113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924522672133" ObjectName="一泵站线056断路器"/>
   <cge:TPSR_Ref TObjectID="6473924522672133"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1221,518.25) scale(1.38273,1.35213) translate(-336.047,-131.446)" width="10" x="1214.082138050288" y="504.7286613624747"/></g>
  <g id="140">
   <use class="kv10" height="20" transform="rotate(0,1327.14,518.25) scale(1.35315,1.32321) translate(-344.594,-123.357)" width="10" x="1320.370168396157" xlink:href="#Breaker:手车开关_0" y="505.0178994974946" zvalue="128"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924522737669" ObjectName="制炼车间057断路器"/>
   <cge:TPSR_Ref TObjectID="6473924522737669"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1327.14,518.25) scale(1.35315,1.32321) translate(-344.594,-123.357)" width="10" x="1320.370168396157" y="505.0178994974946"/></g>
  <g id="152">
   <use class="kv10" height="20" transform="rotate(0,1433.42,518.25) scale(1.35315,1.32321) translate(-372.333,-123.357)" width="10" x="1426.658198742027" xlink:href="#Breaker:手车开关_0" y="505.0178994974946" zvalue="141"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924522803205" ObjectName="锅炉车间058断路器"/>
   <cge:TPSR_Ref TObjectID="6473924522803205"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1433.42,518.25) scale(1.35315,1.32321) translate(-372.333,-123.357)" width="10" x="1426.658198742027" y="505.0178994974946"/></g>
  <g id="164">
   <use class="kv10" height="20" transform="rotate(0,1539.71,518.25) scale(1.35315,1.32321) translate(-400.072,-123.357)" width="10" x="1532.946229087896" xlink:href="#Breaker:手车开关_0" y="505.0178994974946" zvalue="154"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924522868741" ObjectName="压榨整流变059断路器"/>
   <cge:TPSR_Ref TObjectID="6473924522868741"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1539.71,518.25) scale(1.35315,1.32321) translate(-400.072,-123.357)" width="10" x="1532.946229087896" y="505.0178994974946"/></g>
  <g id="176">
   <use class="kv10" height="20" transform="rotate(0,1646,518.25) scale(1.35315,1.32321) translate(-427.812,-123.357)" width="10" x="1639.234259433765" xlink:href="#Breaker:手车开关_0" y="505.0178994974946" zvalue="167"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924522934277" ObjectName="压榨车间061断路器"/>
   <cge:TPSR_Ref TObjectID="6473924522934277"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1646,518.25) scale(1.35315,1.32321) translate(-427.812,-123.357)" width="10" x="1639.234259433765" y="505.0178994974946"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="5">
   <use class="kv10" height="26" transform="rotate(0,1028,289) scale(1,1) translate(0,0)" width="12" x="1022" xlink:href="#Disconnector:小车隔刀熔断器_0" y="276" zvalue="3"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449845723142" ObjectName="10kV遮放糖厂线0519隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449845723142"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1028,289) scale(1,1) translate(0,0)" width="12" x="1022" y="276"/></g>
  <g id="7">
   <use class="kv10" height="30" transform="rotate(0,1125,232) scale(1,1) translate(0,0)" width="30" x="1110" xlink:href="#Disconnector:三相刀闸_0" y="217" zvalue="4"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449845788678" ObjectName="10kV遮放糖厂线0516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449845788678"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1125,232) scale(1,1) translate(0,0)" width="30" x="1110" y="217"/></g>
  <g id="58">
   <use class="kv10" height="26" transform="rotate(0,614.895,533.25) scale(1.25,1.25) translate(-121.479,-103.4)" width="12" x="607.3949159701056" xlink:href="#Disconnector:小车隔刀熔断器_0" y="517" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449846312966" ObjectName="10kV母线PT0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449846312966"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,614.895,533.25) scale(1.25,1.25) translate(-121.479,-103.4)" width="12" x="607.3949159701056" y="517"/></g>
  <g id="52">
   <use class="kv10" height="26" transform="rotate(0,705.358,529.25) scale(1.25,1.25) translate(-139.572,-102.6)" width="12" x="697.858010000087" xlink:href="#Disconnector:小车隔刀熔断器_0" y="513.0000001589457" zvalue="22"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449846181894" ObjectName="站用变0811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449846181894"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,705.358,529.25) scale(1.25,1.25) translate(-139.572,-102.6)" width="12" x="697.858010000087" y="513.0000001589457"/></g>
  <g id="71">
   <use class="kv10" height="30" transform="rotate(0,795.844,609) scale(1,1) translate(0,0)" width="30" x="780.8436483331739" xlink:href="#Disconnector:三相刀闸_0" y="594" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449846444038" ObjectName="备用线0536隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449846444038"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,795.844,609) scale(1,1) translate(0,0)" width="30" x="780.8436483331739" y="594"/></g>
  <g id="88">
   <use class="kv10" height="30" transform="rotate(0,902.132,609) scale(1,1) translate(0,0)" width="30" x="887.131678679043" xlink:href="#Disconnector:三相刀闸_0" y="594" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449846771718" ObjectName="老电力间联络线0526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449846771718"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,902.132,609) scale(1,1) translate(0,0)" width="30" x="887.131678679043" y="594"/></g>
  <g id="96">
   <use class="kv10" height="30" transform="rotate(0,1008.27,609) scale(1,1) translate(0,0)" width="30" x="993.2718179247852" xlink:href="#Disconnector:三相刀闸_0" y="594" zvalue="92"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449846968326" ObjectName="#1F0546隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449846968326"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1008.27,609) scale(1,1) translate(0,0)" width="30" x="993.2718179247852" y="594"/></g>
  <g id="114">
   <use class="kv10" height="30" transform="rotate(0,1114.71,609) scale(1,1) translate(0,0)" width="30" x="1099.707739370781" xlink:href="#Disconnector:三相刀闸_0" y="594" zvalue="104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449847296006" ObjectName="二泵站线0546隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449847296006"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1114.71,609) scale(1,1) translate(0,0)" width="30" x="1099.707739370781" y="594"/></g>
  <g id="127">
   <use class="kv10" height="30" transform="rotate(0,1221.58,612.705) scale(1,1) translate(0,0)" width="30" x="1206.578125163295" xlink:href="#Disconnector:三相刀闸_0" y="597.7053565184275" zvalue="117"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449847558150" ObjectName="一泵站线0566隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449847558150"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1221.58,612.705) scale(1,1) translate(0,0)" width="30" x="1206.578125163295" y="597.7053565184275"/></g>
  <g id="139">
   <use class="kv10" height="30" transform="rotate(0,1327.14,609) scale(1,1) translate(0,0)" width="30" x="1312.135908962392" xlink:href="#Disconnector:三相刀闸_0" y="594" zvalue="130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449847820294" ObjectName="制炼车间0576隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449847820294"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1327.14,609) scale(1,1) translate(0,0)" width="30" x="1312.135908962392" y="594"/></g>
  <g id="151">
   <use class="kv10" height="30" transform="rotate(0,1433.42,609) scale(1,1) translate(0,0)" width="30" x="1418.423939308262" xlink:href="#Disconnector:三相刀闸_0" y="594" zvalue="143"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449848082438" ObjectName="锅炉车间0586隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449848082438"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1433.42,609) scale(1,1) translate(0,0)" width="30" x="1418.423939308262" y="594"/></g>
  <g id="163">
   <use class="kv10" height="30" transform="rotate(0,1539.71,609) scale(1,1) translate(0,0)" width="30" x="1524.711969654131" xlink:href="#Disconnector:三相刀闸_0" y="594" zvalue="156"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449848344582" ObjectName="压榨整流变0596隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449848344582"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1539.71,609) scale(1,1) translate(0,0)" width="30" x="1524.711969654131" y="594"/></g>
  <g id="175">
   <use class="kv10" height="30" transform="rotate(0,1646,609) scale(1,1) translate(0,0)" width="30" x="1631" xlink:href="#Disconnector:三相刀闸_0" y="594" zvalue="169"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449848606726" ObjectName="压榨车间0616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449848606726"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1646,609) scale(1,1) translate(0,0)" width="30" x="1631" y="594"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="9">
   <use class="kv10" height="30" transform="rotate(270,1175,295) scale(1,1) translate(0,0)" width="12" x="1169" xlink:href="#GroundDisconnector:地刀12_0" y="280" zvalue="5"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449845919750" ObjectName="10kV遮放糖厂线05167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449845919750"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1175,295) scale(1,1) translate(0,0)" width="12" x="1169" y="280"/></g>
  <g id="72">
   <use class="kv10" height="30" transform="rotate(270,841,561.698) scale(1,1) translate(0,0)" width="12" x="835" xlink:href="#GroundDisconnector:地刀12_0" y="546.6981353001919" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449846575110" ObjectName="备用线05367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449846575110"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,841,561.698) scale(1,1) translate(0,0)" width="12" x="835" y="546.6981353001919"/></g>
  <g id="87">
   <use class="kv10" height="30" transform="rotate(270,944,559.198) scale(1,1) translate(0,0)" width="12" x="938" xlink:href="#GroundDisconnector:地刀12_0" y="544.1981354492035" zvalue="79"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449846706182" ObjectName="老电力间联络线05267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449846706182"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,944,559.198) scale(1,1) translate(0,0)" width="12" x="938" y="544.1981354492035"/></g>
  <g id="97">
   <use class="kv10" height="30" transform="rotate(270,1037,566) scale(1,1) translate(0,0)" width="12" x="1031" xlink:href="#GroundDisconnector:地刀12_0" y="551" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449847099398" ObjectName="#1F05467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449847099398"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1037,566) scale(1,1) translate(0,0)" width="12" x="1031" y="551"/></g>
  <g id="113">
   <use class="kv10" height="30" transform="rotate(270,1153,566) scale(1,1) translate(0,0)" width="12" x="1147" xlink:href="#GroundDisconnector:地刀12_0" y="551" zvalue="106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449847230470" ObjectName="二泵站线05467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449847230470"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1153,566) scale(1,1) translate(0,0)" width="12" x="1147" y="551"/></g>
  <g id="126">
   <use class="kv10" height="30" transform="rotate(270,1261,566) scale(1,1) translate(0,0)" width="12" x="1255" xlink:href="#GroundDisconnector:地刀12_0" y="551" zvalue="119"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449847492614" ObjectName="一泵站线05667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449847492614"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1261,566) scale(1,1) translate(0,0)" width="12" x="1255" y="551"/></g>
  <g id="138">
   <use class="kv10" height="30" transform="rotate(270,1369,562.068) scale(1,1) translate(0,0)" width="12" x="1363" xlink:href="#GroundDisconnector:地刀12_0" y="547.0679781793476" zvalue="131"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449847754758" ObjectName="制炼车间05767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449847754758"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1369,562.068) scale(1,1) translate(0,0)" width="12" x="1363" y="547.0679781793476"/></g>
  <g id="150">
   <use class="kv10" height="30" transform="rotate(270,1469,562.068) scale(1,1) translate(0,0)" width="12" x="1463" xlink:href="#GroundDisconnector:地刀12_0" y="547.0679781793476" zvalue="144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449848016902" ObjectName="锅炉车间05867接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449848016902"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1469,562.068) scale(1,1) translate(0,0)" width="12" x="1463" y="547.0679781793476"/></g>
  <g id="162">
   <use class="kv10" height="30" transform="rotate(270,1589,566) scale(1,1) translate(0,0)" width="12" x="1583" xlink:href="#GroundDisconnector:地刀12_0" y="551" zvalue="157"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449848279046" ObjectName="压榨整流变05967接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449848279046"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1589,566) scale(1,1) translate(0,0)" width="12" x="1583" y="551"/></g>
  <g id="174">
   <use class="kv10" height="30" transform="rotate(270,1693,566) scale(1,1) translate(0,0)" width="12" x="1687" xlink:href="#GroundDisconnector:地刀12_0" y="551" zvalue="170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449848541190" ObjectName="压榨车间06167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449848541190"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1693,566) scale(1,1) translate(0,0)" width="12" x="1687" y="551"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="11">
   <use class="kv10" height="18" transform="rotate(0,1029.25,227.3) scale(2.36667,-2.36667) translate(-584.106,-311.042)" width="15" x="1011.5" xlink:href="#Accessory:PT8_0" y="206" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449845985286" ObjectName="10kV遮放糖厂线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1029.25,227.3) scale(2.36667,-2.36667) translate(-584.106,-311.042)" width="15" x="1011.5" y="206"/></g>
  <g id="35">
   <use class="kv10" height="32" transform="rotate(0,705.463,638) scale(1.25,1.25) translate(-139.593,-123.6)" width="12" x="697.9630940299814" xlink:href="#Accessory:腊撒线路PT_0" y="618.0000000794728" zvalue="20"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449846247430" ObjectName="站用变"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,705.463,638) scale(1.25,1.25) translate(-139.593,-123.6)" width="12" x="697.9630940299814" y="618.0000000794728"/></g>
  <g id="65">
   <use class="kv10" height="40" transform="rotate(0,614,641) scale(1,1) translate(0,0)" width="40" x="594" xlink:href="#Accessory:线路PT11带避雷器_0" y="621" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449846378502" ObjectName="10kV母线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,614,641) scale(1,1) translate(0,0)" width="40" x="594" y="621"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="14">
   <path class="kv10" d="M 1125 184.85 L 1125 217" stroke-width="1" zvalue="8"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="12@0" LinkObjectIDznd="7@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1125 184.85 L 1125 217" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv10" d="M 1125 247 L 1125 361.65" stroke-width="1" zvalue="9"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="7@1" LinkObjectIDznd="3@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1125 247 L 1125 361.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv10" d="M 1125 401.8 L 1125 451" stroke-width="1" zvalue="10"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="3@1" LinkObjectIDznd="1@11" MaxPinNum="2"/>
   </metadata>
  <path d="M 1125 401.8 L 1125 451" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv10" d="M 1028 276.08 L 1028 247.09" stroke-width="1" zvalue="11"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@1" LinkObjectIDznd="11@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1028 276.08 L 1028 247.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv10" d="M 1028.08 301.97 L 1028.08 322 L 1125 322" stroke-width="1" zvalue="12"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@0" LinkObjectIDznd="15" MaxPinNum="2"/>
   </metadata>
  <path d="M 1028.08 301.97 L 1028.08 322 L 1125 322" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv10" d="M 614.9 517.09 L 614.9 451" stroke-width="1" zvalue="19"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@1" LinkObjectIDznd="1@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 614.9 517.09 L 614.9 451" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv10" d="M 705.36 513.09 L 705.36 451" stroke-width="1" zvalue="23"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@1" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.36 513.09 L 705.36 451" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv10" d="M 705.46 545.46 L 705.46 618.83" stroke-width="1" zvalue="25"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.46 545.46 L 705.46 618.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv10" d="M 614 621.92 L 614 549.46" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="58@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 614 621.92 L 614 549.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv10" d="M 795.84 594 L 795.84 530.42" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="66@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 795.84 594 L 795.84 530.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 795.84 624 L 795.84 643.41" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@1" LinkObjectIDznd="68@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 795.84 624 L 795.84 643.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv10" d="M 826.68 561.71 L 795.84 561.71" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@0" LinkObjectIDznd="73" MaxPinNum="2"/>
   </metadata>
  <path d="M 826.68 561.71 L 795.84 561.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv10" d="M 795.84 505.74 L 795.84 451" stroke-width="1" zvalue="71"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="1@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 795.84 505.74 L 795.84 451" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv10" d="M 902.13 594 L 902.13 530.42" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@0" LinkObjectIDznd="90@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 902.13 594 L 902.13 530.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv10" d="M 902.13 624 L 902.13 643.41" stroke-width="1" zvalue="82"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@1" LinkObjectIDznd="89@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 902.13 624 L 902.13 643.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv10" d="M 929.68 559.21 L 902.13 559.21" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@0" LinkObjectIDznd="86" MaxPinNum="2"/>
   </metadata>
  <path d="M 929.68 559.21 L 902.13 559.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv10" d="M 902.13 505.74 L 902.13 451" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="1@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 902.13 505.74 L 902.13 451" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv10" d="M 1008.27 506.01 L 1008.27 451" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="1@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1008.27 506.01 L 1008.27 451" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv10" d="M 1008.27 594 L 1008.27 530.16" stroke-width="1" zvalue="95"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="94@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1008.27 594 L 1008.27 530.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv10" d="M 1022.68 566.01 L 1008.27 566.01 L 1008.27 562.08" stroke-width="1" zvalue="96"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="102" MaxPinNum="2"/>
   </metadata>
  <path d="M 1022.68 566.01 L 1008.27 566.01 L 1008.27 562.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv10" d="M 1008.27 624 L 1008.27 646.44" stroke-width="1" zvalue="97"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@1" LinkObjectIDznd="95@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1008.27 624 L 1008.27 646.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv10" d="M 1114.71 594 L 1114.71 530.42" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="116@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1114.71 594 L 1114.71 530.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv10" d="M 1114.71 624 L 1114.71 643.41" stroke-width="1" zvalue="109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@1" LinkObjectIDznd="115@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1114.71 624 L 1114.71 643.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 1138.68 566.01 L 1114.71 566.01" stroke-width="1" zvalue="110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="112" MaxPinNum="2"/>
   </metadata>
  <path d="M 1138.68 566.01 L 1114.71 566.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 1114.71 505.74 L 1114.71 451" stroke-width="1" zvalue="111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="1@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1114.71 505.74 L 1114.71 451" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv10" d="M 1221.58 597.71 L 1221.58 530.42" stroke-width="1" zvalue="120"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="129@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1221.58 597.71 L 1221.58 530.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv10" d="M 1221.58 627.71 L 1221.58 643.41" stroke-width="1" zvalue="122"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@1" LinkObjectIDznd="128@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1221.58 627.71 L 1221.58 643.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv10" d="M 1246.68 566.01 L 1221.58 566.01" stroke-width="1" zvalue="123"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="125" MaxPinNum="2"/>
   </metadata>
  <path d="M 1246.68 566.01 L 1221.58 566.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv10" d="M 1221 505.74 L 1221 451" stroke-width="1" zvalue="124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="1@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1221 505.74 L 1221 451" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv10" d="M 1327.14 506.01 L 1327.14 451" stroke-width="1" zvalue="134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="1@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1327.14 506.01 L 1327.14 451" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv10" d="M 1327.14 594 L 1327.14 530.16" stroke-width="1" zvalue="135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@0" LinkObjectIDznd="140@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1327.14 594 L 1327.14 530.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv10" d="M 1354.68 562.08 L 1327.14 562.08" stroke-width="1" zvalue="136"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@0" LinkObjectIDznd="136" MaxPinNum="2"/>
   </metadata>
  <path d="M 1354.68 562.08 L 1327.14 562.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv10" d="M 1327.14 624 L 1327.14 646.44" stroke-width="1" zvalue="137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@1" LinkObjectIDznd="141@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1327.14 624 L 1327.14 646.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv10" d="M 1433.42 506.01 L 1433.42 451" stroke-width="1" zvalue="147"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="1@10" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.42 506.01 L 1433.42 451" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv10" d="M 1433.42 594 L 1433.42 530.16" stroke-width="1" zvalue="148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@0" LinkObjectIDznd="152@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.42 594 L 1433.42 530.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv10" d="M 1454.68 562.08 L 1433.42 562.08" stroke-width="1" zvalue="149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="148" MaxPinNum="2"/>
   </metadata>
  <path d="M 1454.68 562.08 L 1433.42 562.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv10" d="M 1433.42 624 L 1433.42 647.55" stroke-width="1" zvalue="150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@1" LinkObjectIDznd="153@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.42 624 L 1433.42 647.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="kv10" d="M 1539.71 506.01 L 1539.71 451" stroke-width="1" zvalue="160"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@0" LinkObjectIDznd="1@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1539.71 506.01 L 1539.71 451" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv10" d="M 1539.71 594 L 1539.71 530.16" stroke-width="1" zvalue="161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@0" LinkObjectIDznd="164@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1539.71 594 L 1539.71 530.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="kv10" d="M 1574.68 566.01 L 1539.71 566.01" stroke-width="1" zvalue="162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162@0" LinkObjectIDznd="160" MaxPinNum="2"/>
   </metadata>
  <path d="M 1574.68 566.01 L 1539.71 566.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv10" d="M 1539.71 624 L 1539.71 646.44" stroke-width="1" zvalue="163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@1" LinkObjectIDznd="165@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1539.71 624 L 1539.71 646.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv10" d="M 1646 506.01 L 1646 451" stroke-width="1" zvalue="173"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@0" LinkObjectIDznd="1@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1646 506.01 L 1646 451" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv10" d="M 1646 594 L 1646 530.16" stroke-width="1" zvalue="174"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@0" LinkObjectIDznd="176@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1646 594 L 1646 530.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv10" d="M 1678.68 566.01 L 1646 566.01" stroke-width="1" zvalue="175"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="172" MaxPinNum="2"/>
   </metadata>
  <path d="M 1678.68 566.01 L 1646 566.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv10" d="M 1646 624 L 1646 646.44" stroke-width="1" zvalue="176"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@1" LinkObjectIDznd="177@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1646 624 L 1646 646.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv10" d="M 1160.68 295.01 L 1125 295.01" stroke-width="1" zvalue="177"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="15" MaxPinNum="2"/>
   </metadata>
  <path d="M 1160.68 295.01 L 1125 295.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="68">
   <use class="kv10" height="30" transform="rotate(0,796.844,657.286) scale(-1.04167,-1.02778) translate(-1561.56,-1296.39)" width="12" x="790.5936483331739" xlink:href="#EnergyConsumer:负荷_0" y="641.8690439860026" zvalue="28"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449846116358" ObjectName="备用"/>
   <cge:TPSR_Ref TObjectID="6192449846116358"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,796.844,657.286) scale(-1.04167,-1.02778) translate(-1561.56,-1296.39)" width="12" x="790.5936483331739" y="641.8690439860026"/></g>
  <g id="89">
   <use class="kv10" height="30" transform="rotate(0,900.844,657.286) scale(-1.04167,-1.02778) translate(-1765.4,-1296.39)" width="12" x="894.5936483331739" xlink:href="#EnergyConsumer:负荷_0" y="641.8690439860026" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449846837254" ObjectName="老电力间联络"/>
   <cge:TPSR_Ref TObjectID="6192449846837254"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,900.844,657.286) scale(-1.04167,-1.02778) translate(-1765.4,-1296.39)" width="12" x="894.5936483331739" y="641.8690439860026"/></g>
  <g id="95">
   <use class="kv10" height="30" transform="rotate(0,1008.27,659.119) scale(0.905556,0.905556) translate(103.741,67.3258)" width="30" x="994.6884845914519" xlink:href="#EnergyConsumer:站用变DY_0" y="645.5357106924057" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449846902790" ObjectName="#1F"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1008.27,659.119) scale(0.905556,0.905556) translate(103.741,67.3258)" width="30" x="994.6884845914519" y="645.5357106924057"/></g>
  <g id="115">
   <use class="kv10" height="30" transform="rotate(0,1114.71,657.286) scale(-1.04167,-1.02778) translate(-2184.58,-1296.39)" width="12" x="1108.457739370781" xlink:href="#EnergyConsumer:负荷_0" y="641.8690439860026" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449847361542" ObjectName="二泵站线"/>
   <cge:TPSR_Ref TObjectID="6192449847361542"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1114.71,657.286) scale(-1.04167,-1.02778) translate(-2184.58,-1296.39)" width="12" x="1108.457739370781" y="641.8690439860026"/></g>
  <g id="128">
   <use class="kv10" height="30" transform="rotate(0,1221.58,657.286) scale(-1.04167,-1.02778) translate(-2394.04,-1296.39)" width="12" x="1215.328125163295" xlink:href="#EnergyConsumer:负荷_0" y="641.8690439860026" zvalue="115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449847623686" ObjectName="一泵站"/>
   <cge:TPSR_Ref TObjectID="6192449847623686"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1221.58,657.286) scale(-1.04167,-1.02778) translate(-2394.04,-1296.39)" width="12" x="1215.328125163295" y="641.8690439860026"/></g>
  <g id="141">
   <use class="kv10" height="30" transform="rotate(0,1327.14,659.119) scale(0.905556,0.905556) translate(136.996,67.3258)" width="30" x="1313.552575629059" xlink:href="#EnergyConsumer:站用变DY_0" y="645.5357106924057" zvalue="126"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449847885830" ObjectName="制炼车间"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1327.14,659.119) scale(0.905556,0.905556) translate(136.996,67.3258)" width="30" x="1313.552575629059" y="645.5357106924057"/></g>
  <g id="153">
   <use class="kv10" height="30" transform="rotate(0,1433.42,660.23) scale(0.905556,0.905556) translate(148.082,67.4417)" width="30" x="1419.840605974929" xlink:href="#EnergyConsumer:站用变DY_0" y="646.6468218035168" zvalue="139"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449848147974" ObjectName="锅炉车间"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1433.42,660.23) scale(0.905556,0.905556) translate(148.082,67.4417)" width="30" x="1419.840605974929" y="646.6468218035168"/></g>
  <g id="165">
   <use class="kv10" height="30" transform="rotate(0,1539.71,659.119) scale(0.905556,0.905556) translate(159.167,67.3258)" width="30" x="1526.128636320797" xlink:href="#EnergyConsumer:站用变DY_0" y="645.5357106924057" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449848410118" ObjectName="压榨整流变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1539.71,659.119) scale(0.905556,0.905556) translate(159.167,67.3258)" width="30" x="1526.128636320797" y="645.5357106924057"/></g>
  <g id="177">
   <use class="kv10" height="30" transform="rotate(0,1646,659.119) scale(0.905556,0.905556) translate(170.252,67.3258)" width="30" x="1632.416666666667" xlink:href="#EnergyConsumer:站用变DY_0" y="645.5357106924057" zvalue="165"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449848672262" ObjectName="压榨车间"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1646,659.119) scale(0.905556,0.905556) translate(170.252,67.3258)" width="30" x="1632.416666666667" y="645.5357106924057"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,329.17,370.483) scale(0.708333,0.665547) translate(131.165,181.16)" width="30" x="318.54" xlink:href="#State:红绿圆(方形)_0" y="360.5" zvalue="189"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.17,370.483) scale(0.708333,0.665547) translate(131.165,181.16)" width="30" x="318.54" y="360.5"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,233.545,370.483) scale(0.708333,0.665547) translate(91.7904,181.16)" width="30" x="222.92" xlink:href="#State:红绿圆(方形)_0" y="360.5" zvalue="190"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,233.545,370.483) scale(0.708333,0.665547) translate(91.7904,181.16)" width="30" x="222.92" y="360.5"/></g>
 </g>
</svg>