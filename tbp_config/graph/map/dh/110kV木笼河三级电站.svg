<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549685452801" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:三卷PT带容断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,6.5) scale(1,1) translate(0,0)" width="4" x="13" y="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
  </symbol>
  <symbol id="Accessory:设备233_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.097643878606505" xlink:href="#terminal" y="23.59422303238503"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="4" y1="2" y2="4"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="8" y1="2" y2="4"/>
   <rect fill-opacity="0" height="12.18" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.04,12.51) scale(1,1) translate(0,0)" width="7.42" x="2.33" y="6.42"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.5" x2="8.611111111111114" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.5" x2="8.611111111111114" y1="22.41666666666666" y2="22.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="2.166666666666664" y2="6.599999999999997"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_0" viewBox="0,0,30,50">
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01666666666667" x2="15.01666666666667" y1="37.50000000000001" y2="32.91666666666667"/>
   <use terminal-index="1" type="1" x="15.00325153685922" xlink:href="#terminal" y="49.86055225321343"/>
   <use terminal-index="2" type="2" x="15" xlink:href="#terminal" y="37.75085860895189"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.25" x2="15" y1="43.08333333333334" y2="37.75000000000001"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15" x2="20" y1="37.74761215261902" y2="42.91666666666667"/>
   <ellipse cx="15" cy="35.25" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_1" viewBox="0,0,30,50">
   <path d="M 10 16 L 20.1667 16 L 15 7.83333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="0" type="1" x="15.00731595793324" xlink:href="#terminal" y="0.3902606310013752"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:四卷带壁雷器母线PT_0" viewBox="0,0,40,35">
   <use terminal-index="0" type="0" x="22.10905664884498" xlink:href="#terminal" y="34.51612485684674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="28.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,30,14.36) scale(1,-1) translate(0,-930.43)" width="7" x="26.5" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="16.27740325661302" y2="15.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="7.166666666666671" y1="16.27740325661302" y2="12.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="12.27740325661302" y2="13.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="30.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="22" y1="28.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="14" y1="21.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="30" y1="28.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="30" y1="28.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.92943360505483" x2="29.92943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="13.81" cy="17.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.95921744067709" x2="27.68240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.12588410734375" x2="28.51573492932657" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="30.54255077401042" x2="29.09906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599325" x2="16.18240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="13.78240159599324" y1="15.23154965466559" y2="17.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="11.38240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <ellipse cx="20.23" cy="13.99" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.48" cy="10.41" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="17.79906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="20.19906826265991" y1="11.73154965466559" y2="14.2066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="22.59906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="8.148216321332258" y2="10.62329629003646"/>
   <ellipse cx="6.64" cy="14.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器PT1_0" viewBox="0,0,38,38">
   <use terminal-index="0" type="0" x="18.77349690599886" xlink:href="#terminal" y="36.79316976066781"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.7559822394171" x2="18.7559822394171" y1="29.08333333333334" y2="36.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.88529270218527" x2="24.41666666666667" y1="21.56930997848709" y2="21.56930997848709"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.7559822394171" x2="18.7559822394171" y1="25.91666666666667" y2="17.4423061423378"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.83645328312235" x2="18.83645328312235" y1="17.48357618069931" y2="17.48357618069931"/>
   <ellipse cx="28.59" cy="21.55" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.4566694475001" x2="22.29670816245006" y1="17.4835761806993" y2="17.4835761806993"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.66978661645568" x2="18.66978661645568" y1="29.04315320496906" y2="29.04315320496906"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.45666944750009" x2="22.29670816245006" y1="14.38832330358733" y2="14.38832330358733"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.29000278083343" x2="22.1300414957834" y1="29.04315320496904" y2="29.04315320496904"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.29000278083343" x2="22.1300414957834" y1="25.94790032785706" y2="25.94790032785706"/>
   <ellipse cx="32.84" cy="24.05" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="32.84" cy="19.05" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.82811977618069" x2="18.82811977618069" y1="14.41666666666667" y2="7.039733668812222"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.92895180590147" x2="16.65213596121762" y1="6.971825133297724" y2="6.971825133297724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.09561847256814" x2="17.48546929455096" y1="5.596825133297724" y2="5.596825133297724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.25" x2="18.3310877671191" y1="4.221825133297724" y2="4.221825133297724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.883333333333329" x2="4.883333333333329" y1="7.049999999999994" y2="3.449999999999994"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.133333333333331" x2="6.333333333333336" y1="11.79999999999999" y2="15.58333333333334"/>
   <rect fill-opacity="0" height="12.12" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.12,13.11) scale(1,-1) translate(0,-820.33)" width="6.08" x="2.08" y="7.05"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.133333333333331" x2="3.750000000000004" y1="11.79999999999999" y2="15.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.133333333333329" x2="5.133333333333329" y1="21.5" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.218626035518611" x2="18.58333333333333" y1="21.48597664515375" y2="21.48597664515375"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.012285139234802" x2="2.735469294550956" y1="3.30515846663106" y2="3.30515846663106"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.178951805901468" x2="3.56880262788429" y1="1.93015846663106" y2="1.93015846663106"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.333333333333332" x2="4.414421100452426" y1="0.5551584666310596" y2="0.5551584666310596"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_0" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.416666666666625" x2="14.8889734851558" y1="30.74450652239035" y2="19.51308960898588"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_1" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.8889734851558" x2="14.8889734851558" y1="32.25" y2="19.51308960898588"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_2" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.88620689655174" x2="9.700000000000015" y1="19.49501474926254" y2="32.31404129793511"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.699999999999994" x2="20.24999999999999" y1="19.33333333333333" y2="32.38333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:35kVPT456_0" viewBox="0,0,40,30">
   <use terminal-index="0" type="0" x="1.850000000000001" xlink:href="#terminal" y="15"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="17.25" y1="15" y2="15"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.359556584393186" x2="7.359556584393186" y1="19.48802716924653" y2="19.48802716924653"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.94827268622592" x2="6.94827268622592" y1="19.80194186387092" y2="19.80194186387092"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.98218200993319" x2="1.727635318869368" y1="15.02857511364184" y2="15.02857511364184"/>
   <rect fill-opacity="0" height="10.1" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(90,6.87,15.07) scale(1,1) translate(0,0)" width="5.26" x="4.24" y="10.02"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.75" x2="28.75" y1="6" y2="15"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.75" x2="21.75" y1="6" y2="15"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.75" x2="21.75" y1="6" y2="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.75365537262993" x2="35.43495358050507" y1="6.035262583872679" y2="6.035262583872679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.82427590557425" x2="35.82427590557425" y1="4.101929250539339" y2="4.101929250539339"/>
   <ellipse cx="21.46" cy="15.18" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.51963465760507" x2="14.51963465760507" y1="11.32974530079943" y2="11.32974530079943"/>
   <ellipse cx="24.96" cy="21.43" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.58619544935289" x2="35.58619544935289" y1="8.148379752828268" y2="3.87156390814442"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="36.83619544935289" x2="36.83619544935289" y1="7.398379752828269" y2="4.78823057481109"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="38.33619544935289" x2="38.33619544935289" y1="6.898379752828266" y2="5.454897241477754"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.16666666666666" x2="25.56666666666666" y1="22.06919649193173" y2="23.30673647628384"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.26666666666667" x2="25.66666666666666" y1="20.95042649975577" y2="19.71288651540366"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.96666666666665" x2="25.96666666666665" y1="19.83333333333334" y2="23.18381314555852"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.61428617960369" x2="24.26741768080052" y1="15.01057405103871" y2="14.50818132594489"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.09047479452226" x2="21.61428617960368" y1="13.06018441966319" y2="15.0105740510387"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.61428617960368" x2="20.48496606348827" y1="15.01057405103871" y2="17.46335640750804"/>
   <ellipse cx="28.71" cy="15.18" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="20.76963465760507" x2="20.76963465760507" y1="11.32974530079943" y2="11.32974530079943"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.34047479452226" x2="28.86428617960368" y1="13.06018441966319" y2="15.0105740510387"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.86428617960369" x2="31.51741768080052" y1="15.01057405103871" y2="14.50818132594489"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.86428617960368" x2="27.73496606348827" y1="15.01057405103871" y2="17.46335640750804"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0.25" y2="25.75"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV木笼河三级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="43.75" xlink:href="logo.png" y="45.25"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,178.375,75.25) scale(1,1) translate(0,0)" writing-mode="lr" x="178.37" xml:space="preserve" y="78.75" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,180.083,74.9403) scale(1,1) translate(6.85563e-15,0)" writing-mode="lr" x="180.08" xml:space="preserve" y="83.94" zvalue="3">110kV木笼河三级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="212" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="403"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="403">信号一览</text>
  <line fill="none" id="35" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.75" x2="377.75" y1="13.25" y2="1043.25" zvalue="4"/>
  <line fill="none" id="33" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.750000000000227" x2="370.7499999999998" y1="149.1204926140824" y2="149.1204926140824" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,583.533,769.449) scale(1,1) translate(0,0)" writing-mode="lr" x="583.53" xml:space="preserve" y="773.95" zvalue="41">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,673.884,816.196) scale(1,1) translate(0,0)" writing-mode="lr" x="673.88" xml:space="preserve" y="820.7" zvalue="43">041</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,718.668,991.628) scale(1,1) translate(-3.03597e-13,-6.49062e-13)" writing-mode="lr" x="718.6684752281722" xml:space="preserve" y="996.1277576427464" zvalue="46">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1108.16,821.347) scale(1,1) translate(0,0)" writing-mode="lr" x="1108.16" xml:space="preserve" y="825.85" zvalue="63">042</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1152.92,990.45) scale(1,1) translate(0,0)" writing-mode="lr" x="1152.92" xml:space="preserve" y="994.95" zvalue="66">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1462.48,798.462) scale(1,1) translate(0,0)" writing-mode="lr" x="1462.48" xml:space="preserve" y="802.96" zvalue="125">044</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1589.86,936.286) scale(1,1) translate(0,0)" writing-mode="lr" x="1589.86" xml:space="preserve" y="940.79" zvalue="131">10kV立方石业线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,737.828,694.446) scale(1,1) translate(0,0)" writing-mode="lr" x="737.83" xml:space="preserve" y="698.95" zvalue="133">043</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,711.937,549.952) scale(1,1) translate(0,1.19782e-13)" writing-mode="lr" x="711.9400000000001" xml:space="preserve" y="554.45" zvalue="140">10kV木笼河三四级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1086.04,680.918) scale(1,1) translate(7.16123e-13,0)" writing-mode="lr" x="1086.04" xml:space="preserve" y="685.42" zvalue="149">001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1054.38,551.281) scale(1,1) translate(0,0)" writing-mode="lr" x="1054.38" xml:space="preserve" y="555.78" zvalue="155">#1主变50MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1148,494.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1148" xml:space="preserve" y="498.61" zvalue="162">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1190.61,442.511) scale(1,1) translate(0,0)" writing-mode="lr" x="1190.61" xml:space="preserve" y="447.01" zvalue="166">10117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1109.61,433.749) scale(1,1) translate(0,0)" writing-mode="lr" x="1109.61" xml:space="preserve" y="438.25" zvalue="169">1011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1729.86,375.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1729.86" xml:space="preserve" y="380.36" zvalue="173">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,887.556,448) scale(1,1) translate(0,0)" writing-mode="lr" x="887.5599999999999" xml:space="preserve" y="452.5" zvalue="176">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,910.056,424.071) scale(1,1) translate(0,0)" writing-mode="lr" x="910.0599999999999" xml:space="preserve" y="428.57" zvalue="180">19010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,916.111,499.25) scale(1,1) translate(0,0)" writing-mode="lr" x="916.11" xml:space="preserve" y="503.75" zvalue="191">19017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1003.67,335.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1003.67" xml:space="preserve" y="339.72" zvalue="194">1421</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1051.11,318.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1051.11" xml:space="preserve" y="323" zvalue="198">14217</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,997.972,269.611) scale(1,1) translate(0,0)" writing-mode="lr" x="997.97" xml:space="preserve" y="274.11" zvalue="201">142</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1051.11,252.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1051.11" xml:space="preserve" y="256.75" zvalue="203">14260</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1006.17,203.972) scale(1,1) translate(0,0)" writing-mode="lr" x="1006.17" xml:space="preserve" y="208.47" zvalue="208">1426</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1048.61,184.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1048.61" xml:space="preserve" y="189.25" zvalue="211">14267</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,981.123,122.571) scale(1,1) translate(0,0)" writing-mode="lr" x="981.12" xml:space="preserve" y="127.07" zvalue="215">110kV木笼河二三级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1463.81,335.666) scale(1,1) translate(2.73445e-12,0)" writing-mode="lr" x="1463.81" xml:space="preserve" y="340.17" zvalue="217">1431</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1510.25,317.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.25" xml:space="preserve" y="322.44" zvalue="221">14317</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1458.12,270.055) scale(1,1) translate(0,0)" writing-mode="lr" x="1458.12" xml:space="preserve" y="274.55" zvalue="223">143</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1511.25,252.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1511.25" xml:space="preserve" y="257.19" zvalue="225">14360</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1466.31,204.416) scale(1,1) translate(2.57627e-12,0)" writing-mode="lr" x="1466.31" xml:space="preserve" y="208.92" zvalue="229">1436</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1508.75,185.194) scale(1,1) translate(0,0)" writing-mode="lr" x="1508.75" xml:space="preserve" y="189.69" zvalue="232">14367</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="171" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1438.43,117.459) scale(1,1) translate(-6.08148e-13,0)" writing-mode="lr" x="1438.43" xml:space="preserve" y="121.96" zvalue="237">110kV木笼河三五级线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1440.62,930.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1440.62" xml:space="preserve" y="934.83" zvalue="257">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1241.67,580.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1241.67" xml:space="preserve" y="584.83" zvalue="266">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,728.032,338.151) scale(1,1) translate(0,0)" writing-mode="lr" x="728.03" xml:space="preserve" y="342.65" zvalue="269">1411</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,775.476,321.429) scale(1,1) translate(0,0)" writing-mode="lr" x="775.48" xml:space="preserve" y="325.93" zvalue="273">14117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,722.337,272.54) scale(1,1) translate(0,4.39714e-13)" writing-mode="lr" x="722.34" xml:space="preserve" y="277.04" zvalue="276">141</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,775.476,255.179) scale(1,1) translate(0,0)" writing-mode="lr" x="775.48" xml:space="preserve" y="259.68" zvalue="278">14160</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,730.532,206.901) scale(1,1) translate(0,0)" writing-mode="lr" x="730.53" xml:space="preserve" y="211.4" zvalue="283">1416</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,772.976,187.679) scale(1,1) translate(0,0)" writing-mode="lr" x="772.98" xml:space="preserve" y="192.18" zvalue="285">14167</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,703,122.571) scale(1,1) translate(0,0)" writing-mode="lr" x="703" xml:space="preserve" y="127.07" zvalue="292">110kV备用线</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="390.25" y2="413"/>
  <line fill="none" id="238" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="379"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="233" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="381">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="232" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="382">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="231" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="383">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="384">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="228" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="385">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="387">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="388">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="224" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="389">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="223" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241.527,956) scale(1,1) translate(0,0)" writing-mode="lr" x="241.53" xml:space="preserve" y="962" zvalue="390">MuLongHe3-01-2022</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="218" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,64.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="22" xml:space="preserve" y="266.5" zvalue="393">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="217" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="394">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="216" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="395">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="215" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,359.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="363.75" zvalue="396">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="404">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="210" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="405">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="408">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="409">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="410">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="411">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,309.75) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="314.25" zvalue="412">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="414">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,719.5,1009.5) scale(1,1) translate(0,0)" writing-mode="lr" x="719.5" xml:space="preserve" y="1014" zvalue="428">15MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1151.5,1008.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1151.5" xml:space="preserve" y="1013" zvalue="430">15MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1422,591.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1422" xml:space="preserve" y="596" zvalue="432">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1451.5,691) scale(1,1) translate(0,0)" writing-mode="lr" x="1451.5" xml:space="preserve" y="695.5" zvalue="434">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,147,1015) scale(1,1) translate(0,0)" writing-mode="lr" x="147" xml:space="preserve" y="1021" zvalue="439">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,326.5,1015) scale(1,1) translate(0,0)" writing-mode="lr" x="326.5" xml:space="preserve" y="1021" zvalue="441">20220906</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="403"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="253">
   <path class="kv10" d="M 588.25 747.43 L 1721.11 747.43" stroke-width="6" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674423930883" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674423930883"/></metadata>
  <path d="M 588.25 747.43 L 1721.11 747.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv110" d="M 582.14 376.86 L 1705 376.86" stroke-width="6" zvalue="172"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674423996419" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674423996419"/></metadata>
  <path d="M 582.14 376.86 L 1705 376.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="195">
   <use class="kv10" height="20" transform="rotate(0,717.669,811.56) scale(2.06048,2.06048) translate(-364.064,-407.085)" width="10" x="707.3663072734206" xlink:href="#Breaker:小车断路器_0" y="790.9550984421437" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925226594307" ObjectName="#1发电机041断路器"/>
   <cge:TPSR_Ref TObjectID="6473925226594307"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,717.669,811.56) scale(2.06048,2.06048) translate(-364.064,-407.085)" width="10" x="707.3663072734206" y="790.9550984421437"/></g>
  <g id="161">
   <use class="kv10" height="20" transform="rotate(0,1151.95,816.711) scale(2.06048,2.06048) translate(-587.578,-409.737)" width="10" x="1141.647650789404" xlink:href="#Breaker:小车断路器_0" y="796.1062946703197" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925226528771" ObjectName="#2发电机042断路器"/>
   <cge:TPSR_Ref TObjectID="6473925226528771"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1151.95,816.711) scale(2.06048,2.06048) translate(-587.578,-409.737)" width="10" x="1141.647650789404" y="796.1062946703197"/></g>
  <g id="52">
   <use class="kv10" height="20" transform="rotate(0,1441.96,799.462) scale(2.06048,2.06048) translate(-736.842,-400.859)" width="10" x="1431.661893257933" xlink:href="#Breaker:小车断路器_0" y="778.8571428571429" zvalue="124"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925226659843" ObjectName="#1站用变044断路器"/>
   <cge:TPSR_Ref TObjectID="6473925226659843"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1441.96,799.462) scale(2.06048,2.06048) translate(-736.842,-400.859)" width="10" x="1431.661893257933" y="778.8571428571429"/></g>
  <g id="63">
   <use class="kv10" height="20" transform="rotate(0,717.669,695.446) scale(2.06048,2.06048) translate(-364.064,-347.324)" width="10" x="707.3663072734206" xlink:href="#Breaker:小车断路器_0" y="674.8412698412699" zvalue="132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925226725379" ObjectName="10kV木笼河三四级线043断路器"/>
   <cge:TPSR_Ref TObjectID="6473925226725379"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,717.669,695.446) scale(2.06048,2.06048) translate(-364.064,-347.324)" width="10" x="707.3663072734206" y="674.8412698412699"/></g>
  <g id="102">
   <use class="kv10" height="20" transform="rotate(0,1129.05,682.712) scale(2.06048,2.06048) translate(-575.794,-340.771)" width="10" x="1118.75" xlink:href="#Breaker:小车断路器_0" y="662.1071428571429" zvalue="148"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925226790915" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925226790915"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1129.05,682.712) scale(2.06048,2.06048) translate(-575.794,-340.771)" width="10" x="1118.75" y="662.1071428571429"/></g>
  <g id="329">
   <use class="kv110" height="20" transform="rotate(0,1129.33,495.111) scale(1.22222,1.11111) translate(-204.222,-48.4)" width="10" x="1123.222222222222" xlink:href="#Breaker:开关_0" y="484" zvalue="161"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925226856451" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925226856451"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1129.33,495.111) scale(1.22222,1.11111) translate(-204.222,-48.4)" width="10" x="1123.222222222222" y="484"/></g>
  <g id="128">
   <use class="kv110" height="20" transform="rotate(0,977.361,270.611) scale(1.22222,1.11111) translate(-176.591,-25.95)" width="10" x="971.25" xlink:href="#Breaker:开关_0" y="259.5" zvalue="200"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925226921987" ObjectName="110kV木笼河二三级线142断路器"/>
   <cge:TPSR_Ref TObjectID="6473925226921987"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,977.361,270.611) scale(1.22222,1.11111) translate(-176.591,-25.95)" width="10" x="971.25" y="259.5"/></g>
  <g id="165">
   <use class="kv110" height="20" transform="rotate(0,1437.5,271.055) scale(1.22222,1.11111) translate(-260.253,-25.9944)" width="10" x="1431.392857142857" xlink:href="#Breaker:开关_0" y="259.943545534168" zvalue="222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925226987523" ObjectName="110kV木笼河三五级线143断路器"/>
   <cge:TPSR_Ref TObjectID="6473925226987523"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1437.5,271.055) scale(1.22222,1.11111) translate(-260.253,-25.9944)" width="10" x="1431.392857142857" y="259.943545534168"/></g>
  <g id="110">
   <use class="kv110" height="20" transform="rotate(0,701.726,273.54) scale(1.22222,1.11111) translate(-126.475,-26.2429)" width="10" x="695.6150793650793" xlink:href="#Breaker:开关_0" y="262.4285711560931" zvalue="275"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925227053059" ObjectName="110kV备用线141断路器"/>
   <cge:TPSR_Ref TObjectID="6473925227053059"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,701.726,273.54) scale(1.22222,1.11111) translate(-126.475,-26.2429)" width="10" x="695.6150793650793" y="262.4285711560931"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="80">
   <path class="kv10" d="M 717.64 921.19 L 717.67 830.1" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@0" LinkObjectIDznd="195@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 717.64 921.19 L 717.67 830.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv10" d="M 746.39 850.22 L 717.66 850.22" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="80" MaxPinNum="2"/>
   </metadata>
  <path d="M 746.39 850.22 L 717.66 850.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 717.67 792.5 L 717.67 747.43" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="253@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 717.67 792.5 L 717.67 747.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv10" d="M 1151.92 926.34 L 1151.95 835.26" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="161@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1151.92 926.34 L 1151.95 835.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv10" d="M 1203.43 946.81 L 1203.14 894.73" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="39" MaxPinNum="2"/>
   </metadata>
  <path d="M 1203.43 946.81 L 1203.14 894.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv10" d="M 1180.61 855.46 L 1151.94 855.46" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@0" LinkObjectIDznd="72" MaxPinNum="2"/>
   </metadata>
  <path d="M 1180.61 855.46 L 1151.94 855.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv10" d="M 1151.95 797.65 L 1151.95 747.43" stroke-width="1" zvalue="85"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="253@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1151.95 797.65 L 1151.95 747.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv10" d="M 1151.93 894.73 L 1230.22 894.73 L 1230.22 939.34" stroke-width="1" zvalue="116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72" LinkObjectIDznd="151@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1151.93 894.73 L 1230.22 894.73 L 1230.22 939.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv10" d="M 1261.95 945.68 L 1261.95 894.73 L 1229.33 894.73" stroke-width="1" zvalue="119"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="39" MaxPinNum="2"/>
   </metadata>
  <path d="M 1261.95 945.68 L 1261.95 894.73 L 1229.33 894.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv10" d="M 1441.96 780.4 L 1441.96 747.43" stroke-width="1" zvalue="125"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="253@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1441.96 780.4 L 1441.96 747.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv10" d="M 1591.43 747.43 L 1591.43 884.06" stroke-width="1" zvalue="129"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@1" LinkObjectIDznd="60@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1591.43 747.43 L 1591.43 884.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv10" d="M 717.67 713.99 L 717.67 747.43" stroke-width="1" zvalue="133"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@1" LinkObjectIDznd="253@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 717.67 713.99 L 717.67 747.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv10" d="M 1129.05 701.26 L 1129.05 747.43" stroke-width="1" zvalue="150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@1" LinkObjectIDznd="253@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1129.05 701.26 L 1129.05 747.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv10" d="M 1154.36 644.13 L 1129.05 644.13" stroke-width="1" zvalue="152"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@0" LinkObjectIDznd="103" MaxPinNum="2"/>
   </metadata>
  <path d="M 1154.36 644.13 L 1129.05 644.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv10" d="M 1129.05 663.65 L 1129.05 590.35" stroke-width="1" zvalue="153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="104@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1129.05 663.65 L 1129.05 590.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv110" d="M 1129.41 540.15 L 1210 540.15 L 1210 570.67" stroke-width="1" zvalue="159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@2" LinkObjectIDznd="2@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1129.41 540.15 L 1210 540.15 L 1210 570.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv110" d="M 1129.41 505.72 L 1129.41 523.87" stroke-width="1" zvalue="162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="329@1" LinkObjectIDznd="104@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1129.41 505.72 L 1129.41 523.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv110" d="M 854.9 456.82 L 854.9 512.26" stroke-width="1" zvalue="188"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="358@0" LinkObjectIDznd="116@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.9 456.82 L 854.9 512.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv110" d="M 902.78 484.61 L 854.9 484.61" stroke-width="1" zvalue="191"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@0" LinkObjectIDznd="117" MaxPinNum="2"/>
   </metadata>
  <path d="M 902.78 484.61 L 854.9 484.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv110" d="M 976.99 348.04 L 976.99 376.86" stroke-width="1" zvalue="194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@0" LinkObjectIDznd="70@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 976.99 348.04 L 976.99 376.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv110" d="M 977.02 324.21 L 977.02 281.22" stroke-width="1" zvalue="195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@1" LinkObjectIDznd="128@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 977.02 324.21 L 977.02 281.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv110" d="M 1040.28 303.86 L 977.02 303.86" stroke-width="1" zvalue="198"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="124" MaxPinNum="2"/>
   </metadata>
  <path d="M 1040.28 303.86 L 977.02 303.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv110" d="M 977.32 259.98 L 977.32 216.79" stroke-width="1" zvalue="204"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@0" LinkObjectIDznd="135@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 977.32 259.98 L 977.32 216.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv110" d="M 1037.78 236.99 L 977.32 236.99" stroke-width="1" zvalue="205"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="133" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.78 236.99 L 977.32 236.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv110" d="M 978.27 192.96 L 978.27 152.89" stroke-width="1" zvalue="208"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@1" LinkObjectIDznd="143@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 978.27 192.96 L 978.27 152.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="kv110" d="M 1037.78 171.99 L 978.27 171.99" stroke-width="1" zvalue="211"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@0" LinkObjectIDznd="137" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.78 171.99 L 978.27 171.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv110" d="M 901.1 171.99 L 978.27 171.99" stroke-width="1" zvalue="213"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@0" LinkObjectIDznd="137" MaxPinNum="2"/>
   </metadata>
  <path d="M 901.1 171.99 L 978.27 171.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv110" d="M 1437.16 324.65 L 1437.16 281.67" stroke-width="1" zvalue="218"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@1" LinkObjectIDznd="165@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1437.16 324.65 L 1437.16 281.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv110" d="M 1499.42 303.3 L 1437.16 303.3" stroke-width="1" zvalue="220"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="167@0" LinkObjectIDznd="168" MaxPinNum="2"/>
   </metadata>
  <path d="M 1499.42 303.3 L 1437.16 303.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv110" d="M 1437.46 260.43 L 1437.46 217.23" stroke-width="1" zvalue="226"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="160@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1437.46 260.43 L 1437.46 217.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv110" d="M 1497.92 237.44 L 1437.46 237.44" stroke-width="1" zvalue="227"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@0" LinkObjectIDznd="163" MaxPinNum="2"/>
   </metadata>
  <path d="M 1497.92 237.44 L 1437.46 237.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv110" d="M 1438.41 193.4 L 1438.43 161.04" stroke-width="1" zvalue="230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160@1" LinkObjectIDznd="153@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1438.41 193.4 L 1438.43 161.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv110" d="M 1497.92 172.44 L 1438.42 172.44" stroke-width="1" zvalue="233"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="158" MaxPinNum="2"/>
   </metadata>
  <path d="M 1497.92 172.44 L 1438.42 172.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv110" d="M 1361.24 172.44 L 1438.42 172.44" stroke-width="1" zvalue="235"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="156" MaxPinNum="2"/>
   </metadata>
  <path d="M 1361.24 172.44 L 1438.42 172.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv110" d="M 1437.13 348.48 L 1437.13 376.86" stroke-width="1" zvalue="237"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@0" LinkObjectIDznd="70@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1437.13 348.48 L 1437.13 376.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv10" d="M 718.87 599.41 L 718.87 676.39" stroke-width="1" zvalue="238"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="63@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 718.87 599.41 L 718.87 676.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="kv10" d="M 746.28 624.3 L 718.87 624.3" stroke-width="1" zvalue="240"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="172" MaxPinNum="2"/>
   </metadata>
  <path d="M 746.28 624.3 L 718.87 624.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv10" d="M 695.83 624.3 L 718.87 624.3" stroke-width="1" zvalue="241"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@0" LinkObjectIDznd="172" MaxPinNum="2"/>
   </metadata>
  <path d="M 695.83 624.3 L 718.87 624.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv110" d="M 1129.29 484.48 L 1129.29 446.76" stroke-width="1" zvalue="247"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="329@0" LinkObjectIDznd="230@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1129.29 484.48 L 1129.29 446.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="184">
   <path class="kv110" d="M 1179.78 469.12 L 1129.29 469.12" stroke-width="1" zvalue="249"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@0" LinkObjectIDznd="182" MaxPinNum="2"/>
   </metadata>
  <path d="M 1179.78 469.12 L 1129.29 469.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv110" d="M 1129.32 422.93 L 1129.32 376.86" stroke-width="1" zvalue="252"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="70@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1129.32 422.93 L 1129.32 376.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv110" d="M 854.93 432.99 L 854.93 376.86" stroke-width="1" zvalue="253"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="358@1" LinkObjectIDznd="70@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.93 432.99 L 854.93 376.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv110" d="M 897.78 409.57 L 854.93 409.57" stroke-width="1" zvalue="254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="351@0" LinkObjectIDznd="69" MaxPinNum="2"/>
   </metadata>
  <path d="M 897.78 409.57 L 854.93 409.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv10" d="M 1441.96 818.01 L 1441.96 872.55" stroke-width="1" zvalue="258"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@1" LinkObjectIDznd="16@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1441.96 818.01 L 1441.96 872.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv10" d="M 828.13 939.68 L 828.13 880.67 L 717.65 880.67" stroke-width="1" zvalue="262"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@0" LinkObjectIDznd="80" MaxPinNum="2"/>
   </metadata>
  <path d="M 828.13 939.68 L 828.13 880.67 L 717.65 880.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv10" d="M 795.94 934.19 L 795.94 880.67" stroke-width="1" zvalue="263"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@0" LinkObjectIDznd="91" MaxPinNum="2"/>
   </metadata>
  <path d="M 795.94 934.19 L 795.94 880.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv10" d="M 772.05 938.33 L 772.05 880.67" stroke-width="1" zvalue="264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="91" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.05 938.33 L 772.05 880.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv110" d="M 701.35 350.97 L 701.35 376.86" stroke-width="1" zvalue="270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="70@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 701.35 350.97 L 701.35 376.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv110" d="M 701.38 327.14 L 701.38 284.15" stroke-width="1" zvalue="271"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@1" LinkObjectIDznd="110@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 701.38 327.14 L 701.38 284.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv110" d="M 764.64 306.79 L 701.38 306.79" stroke-width="1" zvalue="274"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="114" MaxPinNum="2"/>
   </metadata>
  <path d="M 764.64 306.79 L 701.38 306.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv110" d="M 701.69 262.91 L 701.69 219.72" stroke-width="1" zvalue="279"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="105@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 701.69 262.91 L 701.69 219.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv110" d="M 762.14 239.92 L 701.69 239.92" stroke-width="1" zvalue="280"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="108" MaxPinNum="2"/>
   </metadata>
  <path d="M 762.14 239.92 L 701.69 239.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv110" d="M 702.63 195.89 L 702.63 165.32" stroke-width="1" zvalue="282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@1" LinkObjectIDznd="173@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 702.63 195.89 L 702.63 165.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv110" d="M 762.14 174.92 L 702.63 174.92" stroke-width="1" zvalue="286"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="97" MaxPinNum="2"/>
   </metadata>
  <path d="M 762.14 174.92 L 702.63 174.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv110" d="M 625.47 174.92 L 702.64 174.92" stroke-width="1" zvalue="288"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@0" LinkObjectIDznd="88" MaxPinNum="2"/>
   </metadata>
  <path d="M 625.47 174.92 L 702.64 174.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv10" d="M 1420.38 665.99 L 1420.38 680.03" stroke-width="1" zvalue="434"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8@0" LinkObjectIDznd="14@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1420.38 665.99 L 1420.38 680.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv10" d="M 1420 705.93 L 1420 747.43" stroke-width="1" zvalue="435"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="14@1" LinkObjectIDznd="253@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1420 705.93 L 1420 747.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv10" d="M 1442.25 721 L 1420 721" stroke-width="1" zvalue="437"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@0" LinkObjectIDznd="17" MaxPinNum="2"/>
   </metadata>
  <path d="M 1442.25 721 L 1420 721" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="193">
   <use class="kv10" height="30" transform="rotate(0,717.638,943.979) scale(1.54536,1.54536) translate(-245.075,-324.951)" width="30" x="694.4578529557438" xlink:href="#Generator:发电机_0" y="920.7988095053233" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454876856322" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192454876856322"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,717.638,943.979) scale(1.54536,1.54536) translate(-245.075,-324.951)" width="30" x="694.4578529557438" y="920.7988095053233"/></g>
  <g id="159">
   <use class="kv10" height="30" transform="rotate(0,1151.92,949.13) scale(1.54536,1.54536) translate(-398.333,-326.769)" width="30" x="1128.739196471727" xlink:href="#Generator:发电机_0" y="925.9500057334994" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454876659714" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192454876659714"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1151.92,949.13) scale(1.54536,1.54536) translate(-398.333,-326.769)" width="30" x="1128.739196471727" y="925.9500057334994"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="187">
   <use class="kv10" height="40" transform="rotate(0,795.936,953.251) scale(1.03024,-1.03024) translate(-22.9085,-1877.92)" width="30" x="780.4828299662868" xlink:href="#Accessory:带熔断器的线路PT1_0" y="932.6465608301284" zvalue="54"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454876790786" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,795.936,953.251) scale(1.03024,-1.03024) translate(-22.9085,-1877.92)" width="30" x="780.4828299662868" y="932.6465608301284"/></g>
  <g id="186">
   <use class="kv10" height="29" transform="rotate(0,772.05,953.009) scale(1.03024,-1.03024) translate(-22.2074,-1877.61)" width="30" x="756.5959806595417" xlink:href="#Accessory:PT12321_0" y="938.0702610846947" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454876725250" ObjectName="#1发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,772.05,953.009) scale(1.03024,-1.03024) translate(-22.2074,-1877.61)" width="30" x="756.5959806595417" y="938.0702610846947"/></g>
  <g id="151">
   <use class="kv10" height="40" transform="rotate(0,1230.22,958.403) scale(1.03024,-1.03024) translate(-35.6554,-1888.07)" width="30" x="1214.76417348227" xlink:href="#Accessory:带熔断器的线路PT1_0" y="937.7977570583048" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454876594178" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1230.22,958.403) scale(1.03024,-1.03024) translate(-35.6554,-1888.07)" width="30" x="1214.76417348227" y="937.7977570583048"/></g>
  <g id="150">
   <use class="kv10" height="29" transform="rotate(0,1203.43,961.493) scale(1.03024,-1.03024) translate(-34.8691,-1894.33)" width="30" x="1187.977953095754" xlink:href="#Accessory:PT12321_0" y="946.5547906462048" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454876528642" ObjectName="#2发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1203.43,961.493) scale(1.03024,-1.03024) translate(-34.8691,-1894.33)" width="30" x="1187.977953095754" y="946.5547906462048"/></g>
  <g id="85">
   <use class="kv10" height="30" transform="rotate(0,828.175,954.333) scale(1,1) translate(2.70842e-13,0)" width="30" x="813.175" xlink:href="#Accessory:三卷PT带容断器_0" y="939.3333333333333" zvalue="112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454876921858" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,828.175,954.333) scale(1,1) translate(2.70842e-13,0)" width="30" x="813.175" y="939.3333333333333"/></g>
  <g id="40">
   <use class="kv10" height="30" transform="rotate(0,1262,960.333) scale(1,1) translate(4.15334e-13,0)" width="30" x="1247" xlink:href="#Accessory:三卷PT带容断器_0" y="945.3333333333333" zvalue="118"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454876987394" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1262,960.333) scale(1,1) translate(4.15334e-13,0)" width="30" x="1247" y="945.3333333333333"/></g>
  <g id="47">
   <use class="kv10" height="26" transform="rotate(90,763.333,850.333) scale(-1.18006,1.5989) translate(-1409.11,-310.724)" width="12" x="756.2529604837298" xlink:href="#Accessory:设备233_0" y="829.547619047619" zvalue="120"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454877052930" ObjectName="1号机附属"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,763.333,850.333) scale(-1.18006,1.5989) translate(-1409.11,-310.724)" width="12" x="756.2529604837298" y="829.547619047619"/></g>
  <g id="48">
   <use class="kv10" height="26" transform="rotate(90,1197.55,855.571) scale(-1.18006,1.5989) translate(-2211.28,-312.686)" width="12" x="1190.467246198015" xlink:href="#Accessory:设备233_0" y="834.7857142857143" zvalue="122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454877118466" ObjectName="2号机附属"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1197.55,855.571) scale(-1.18006,1.5989) translate(-2211.28,-312.686)" width="12" x="1190.467246198015" y="834.7857142857143"/></g>
  <g id="78">
   <use class="kv10" height="26" transform="rotate(90,763.214,624.415) scale(-1.18006,1.5989) translate(-1408.89,-226.102)" width="12" x="756.1339128646821" xlink:href="#Accessory:设备233_0" y="603.6291147333915" zvalue="135"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454877249538" ObjectName="043断路器附属"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,763.214,624.415) scale(-1.18006,1.5989) translate(-1408.89,-226.102)" width="12" x="756.1339128646821" y="603.6291147333915"/></g>
  <g id="95">
   <use class="kv10" height="40" transform="rotate(90,675.029,624.3) scale(-1.23978,-1.12465) translate(-1215.91,-1176.91)" width="30" x="656.4320436507937" xlink:href="#Accessory:带熔断器的线路PT1_0" y="601.8065476190477" zvalue="144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454877380610" ObjectName="043断路器线路PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,675.029,624.3) scale(-1.23978,-1.12465) translate(-1215.91,-1176.91)" width="30" x="656.4320436507937" y="601.8065476190477"/></g>
  <g id="100">
   <use class="kv10" height="26" transform="rotate(90,1171.3,644.25) scale(-1.18006,1.5989) translate(-2162.79,-233.531)" width="12" x="1164.217246198015" xlink:href="#Accessory:设备233_0" y="623.4642857142858" zvalue="151"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454877446146" ObjectName="001断路器附属"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1171.3,644.25) scale(-1.18006,1.5989) translate(-2162.79,-233.531)" width="12" x="1164.217246198015" y="623.4642857142858"/></g>
  <g id="116">
   <use class="kv110" height="35" transform="rotate(0,852.134,534.594) scale(1.3125,-1.3125) translate(-196.639,-936.435)" width="40" x="825.8843330918792" xlink:href="#Accessory:四卷带壁雷器母线PT_0" y="511.625" zvalue="187"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454877970434" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,852.134,534.594) scale(1.3125,-1.3125) translate(-196.639,-936.435)" width="40" x="825.8843330918792" y="511.625"/></g>
  <g id="141">
   <use class="kv110" height="38" transform="rotate(0,901.385,149.751) scale(1.25,1.25) translate(-175.527,-25.2002)" width="38" x="877.6349206349206" xlink:href="#Accessory:避雷器PT1_0" y="126.0008989102764" zvalue="212"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454878691330" ObjectName="110kV木笼河二三级线避雷器PT1"/>
   </metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,901.385,149.751) scale(1.25,1.25) translate(-175.527,-25.2002)" width="38" x="877.6349206349206" y="126.0008989102764"/></g>
  <g id="155">
   <use class="kv110" height="38" transform="rotate(0,1361.53,150.194) scale(1.25,1.25) translate(-267.556,-25.2889)" width="38" x="1337.777777777778" xlink:href="#Accessory:避雷器PT1_0" y="126.4444444444445" zvalue="234"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454878887938" ObjectName="110kV木笼河三五级线避雷器PT1"/>
   </metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,1361.53,150.194) scale(1.25,1.25) translate(-267.556,-25.2889)" width="38" x="1337.777777777778" y="126.4444444444445"/></g>
  <g id="87">
   <use class="kv110" height="38" transform="rotate(0,625.75,152.679) scale(1.25,1.25) translate(-120.4,-25.7859)" width="38" x="602" xlink:href="#Accessory:避雷器PT1_0" y="128.9294700663695" zvalue="287"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454879739906" ObjectName="110kV备用线避雷器PT1"/>
   </metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,625.75,152.679) scale(1.25,1.25) translate(-120.4,-25.7859)" width="38" x="602" y="128.9294700663695"/></g>
  <g id="8">
   <use class="kv10" height="30" transform="rotate(270,1420.38,636.5) scale(1.625,1.625) translate(-533.798,-235.433)" width="40" x="1387.875" xlink:href="#Accessory:35kVPT456_0" y="612.125" zvalue="431"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450174189573" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1420.38,636.5) scale(1.625,1.625) translate(-533.798,-235.433)" width="40" x="1387.875" y="612.125"/></g>
  <g id="20">
   <use class="kv10" height="20" transform="rotate(270,1458,721) scale(1.4,1.8) translate(-412.571,-312.444)" width="20" x="1444" xlink:href="#Accessory:线路PT3_0" y="703" zvalue="436"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450174320645" ObjectName="10kV电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1458,721) scale(1.4,1.8) translate(-412.571,-312.444)" width="20" x="1444" y="703"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="60">
   <use class="kv10" height="30" transform="rotate(0,1591.43,900.714) scale(1.25,-1.23333) translate(-316.786,-1627.52)" width="12" x="1583.928571428571" xlink:href="#EnergyConsumer:负荷_0" y="882.2142857142856" zvalue="130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454877184002" ObjectName="10kV立方石业线"/>
   <cge:TPSR_Ref TObjectID="6192454877184002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1591.43,900.714) scale(1.25,-1.23333) translate(-316.786,-1627.52)" width="12" x="1583.928571428571" y="882.2142857142856"/></g>
  <g id="16">
   <use class="kv10" height="30" transform="rotate(0,1441.83,893.833) scale(1.46748,1.47778) translate(-452.764,-281.817)" width="28" x="1421.284749097668" xlink:href="#EnergyConsumer:站用变DY接地_0" y="871.6666666666667" zvalue="256"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454879477762" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1441.83,893.833) scale(1.46748,1.47778) translate(-452.764,-281.817)" width="28" x="1421.284749097668" y="871.6666666666667"/></g>
  <g id="173">
   <use class="kv110" height="30" transform="rotate(0,703,148.667) scale(1.25,1.23333) translate(-139.1,-24.6261)" width="12" x="695.5" xlink:href="#EnergyConsumer:负荷_0" y="130.1666666666666" zvalue="291"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454879674370" ObjectName="110kV备用线"/>
   <cge:TPSR_Ref TObjectID="6192454879674370"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,703,148.667) scale(1.25,1.23333) translate(-139.1,-24.6261)" width="12" x="695.5" y="130.1666666666666"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="104">
   <g id="1040">
    <use class="kv110" height="50" transform="rotate(0,1129.41,557.281) scale(1.34375,-1.34375) translate(-283.763,-963.408)" width="30" x="1109.25" xlink:href="#PowerTransformer2:D-Y_0" y="523.6900000000001" zvalue="154"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874596220930" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1041">
    <use class="kv10" height="50" transform="rotate(0,1129.41,557.281) scale(1.34375,-1.34375) translate(-283.763,-963.408)" width="30" x="1109.25" xlink:href="#PowerTransformer2:D-Y_1" y="523.6900000000001" zvalue="154"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874596286466" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399535165442" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399535165442"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1129.41,557.281) scale(1.34375,-1.34375) translate(-283.763,-963.408)" width="30" x="1109.25" y="523.6900000000001"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="177">
   <use class="kv110" height="20" transform="rotate(270,1190.61,469.066) scale(-1.11111,1.11111) translate(-2261.61,-45.7955)" width="10" x="1185.055555555556" xlink:href="#GroundDisconnector:地刀_0" y="457.9549555738863" zvalue="165"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454877642754" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454877642754"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1190.61,469.066) scale(-1.11111,1.11111) translate(-2261.61,-45.7955)" width="10" x="1185.055555555556" y="457.9549555738863"/></g>
  <g id="351">
   <use class="kv110" height="20" transform="rotate(270,908.611,409.516) scale(-1.11111,1.11111) translate(-1725.81,-39.8405)" width="10" x="903.0555555555554" xlink:href="#GroundDisconnector:地刀_0" y="398.4047619047619" zvalue="179"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454877839362" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454877839362"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,908.611,409.516) scale(-1.11111,1.11111) translate(-1725.81,-39.8405)" width="10" x="903.0555555555554" y="398.4047619047619"/></g>
  <g id="118">
   <use class="kv110" height="20" transform="rotate(270,913.611,484.556) scale(-1.11111,1.11111) translate(-1735.31,-47.3444)" width="10" x="908.0555555555555" xlink:href="#GroundDisconnector:地刀_0" y="473.4444444444444" zvalue="190"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454878101506" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454878101506"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,913.611,484.556) scale(-1.11111,1.11111) translate(-1735.31,-47.3444)" width="10" x="908.0555555555555" y="473.4444444444444"/></g>
  <g id="125">
   <use class="kv110" height="20" transform="rotate(270,1051.11,303.806) scale(-1.11111,1.11111) translate(-1996.56,-29.2694)" width="10" x="1045.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="292.6944444444443" zvalue="197"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454878298114" ObjectName="110kV木笼河二三级线14217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454878298114"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1051.11,303.806) scale(-1.11111,1.11111) translate(-1996.56,-29.2694)" width="10" x="1045.555555555556" y="292.6944444444443"/></g>
  <g id="130">
   <use class="kv110" height="20" transform="rotate(270,1048.61,236.931) scale(-1.23611,1.11111) translate(-1895.75,-22.5819)" width="10" x="1042.430555555556" xlink:href="#GroundDisconnector:地刀_0" y="225.8194444444443" zvalue="202"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454878429186" ObjectName="110kV木笼河二三级线14260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454878429186"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1048.61,236.931) scale(-1.23611,1.11111) translate(-1895.75,-22.5819)" width="10" x="1042.430555555556" y="225.8194444444443"/></g>
  <g id="138">
   <use class="kv110" height="20" transform="rotate(270,1048.61,171.931) scale(-1.23611,1.11111) translate(-1895.75,-16.0819)" width="10" x="1042.430555555556" xlink:href="#GroundDisconnector:地刀_0" y="160.8194444444443" zvalue="210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454878625794" ObjectName="110kV木笼河二三级线14267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454878625794"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1048.61,171.931) scale(-1.23611,1.11111) translate(-1895.75,-16.0819)" width="10" x="1042.430555555556" y="160.8194444444443"/></g>
  <g id="167">
   <use class="kv110" height="20" transform="rotate(270,1510.25,303.249) scale(-1.11111,1.11111) translate(-2868.93,-29.2138)" width="10" x="1504.698412698413" xlink:href="#GroundDisconnector:地刀_0" y="292.1379899786125" zvalue="219"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454879346690" ObjectName="110kV木笼河三五级线14317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454879346690"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1510.25,303.249) scale(-1.11111,1.11111) translate(-2868.93,-29.2138)" width="10" x="1504.698412698413" y="292.1379899786125"/></g>
  <g id="164">
   <use class="kv110" height="20" transform="rotate(270,1508.75,237.374) scale(-1.23611,1.11111) translate(-2728.14,-22.6263)" width="10" x="1502.573412698413" xlink:href="#GroundDisconnector:地刀_0" y="226.2629899786125" zvalue="224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454879215618" ObjectName="110kV木笼河三五级线14360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454879215618"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1508.75,237.374) scale(-1.23611,1.11111) translate(-2728.14,-22.6263)" width="10" x="1502.573412698413" y="226.2629899786125"/></g>
  <g id="157">
   <use class="kv110" height="20" transform="rotate(270,1508.75,172.374) scale(-1.23611,1.11111) translate(-2728.14,-16.1263)" width="10" x="1502.573412698413" xlink:href="#GroundDisconnector:地刀_0" y="161.2629899786125" zvalue="231"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454879019010" ObjectName="110kV木笼河三五级线14367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454879019010"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1508.75,172.374) scale(-1.23611,1.11111) translate(-2728.14,-16.1263)" width="10" x="1502.573412698413" y="161.2629899786125"/></g>
  <g id="2">
   <use class="kv110" height="45" transform="rotate(0,1215.33,581.333) scale(0.666667,-0.666667) translate(600.167,-1460.83)" width="45" x="1200.333333333333" xlink:href="#GroundDisconnector:12547_0" y="566.3333333333334" zvalue="265"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454879608834" ObjectName="#1主变110kV侧1010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454879608834"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,1215.33,581.333) scale(0.666667,-0.666667) translate(600.167,-1460.83)" width="45" x="1200.333333333333" y="566.3333333333334"/></g>
  <g id="113">
   <use class="kv110" height="20" transform="rotate(270,775.476,306.734) scale(-1.11111,1.11111) translate(-1472.85,-29.5623)" width="10" x="769.9206349206349" xlink:href="#GroundDisconnector:地刀_0" y="295.6230156005374" zvalue="272"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454880198658" ObjectName="110kV备用线14117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454880198658"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,775.476,306.734) scale(-1.11111,1.11111) translate(-1472.85,-29.5623)" width="10" x="769.9206349206349" y="295.6230156005374"/></g>
  <g id="109">
   <use class="kv110" height="20" transform="rotate(270,772.976,239.859) scale(-1.23611,1.11111) translate(-1397.12,-22.8748)" width="10" x="766.7956349206349" xlink:href="#GroundDisconnector:地刀_0" y="228.7480156005374" zvalue="277"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454880067586" ObjectName="110kV备用线14160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454880067586"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,772.976,239.859) scale(-1.23611,1.11111) translate(-1397.12,-22.8748)" width="10" x="766.7956349206349" y="228.7480156005374"/></g>
  <g id="96">
   <use class="kv110" height="20" transform="rotate(270,772.976,174.859) scale(-1.23611,1.11111) translate(-1397.12,-16.3748)" width="10" x="766.7956349206349" xlink:href="#GroundDisconnector:地刀_0" y="163.7480156005374" zvalue="284"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454879870978" ObjectName="110kV备用线14167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454879870978"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,772.976,174.859) scale(-1.23611,1.11111) translate(-1397.12,-16.3748)" width="10" x="766.7956349206349" y="163.7480156005374"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="230">
   <use class="kv110" height="30" transform="rotate(0,1129.22,434.749) scale(1.11111,0.814815) translate(-112.089,96.0287)" width="15" x="1120.891298788031" xlink:href="#Disconnector:刀闸_0" y="422.5263157894737" zvalue="168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454877708290" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454877708290"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1129.22,434.749) scale(1.11111,0.814815) translate(-112.089,96.0287)" width="15" x="1120.891298788031" y="422.5263157894737"/></g>
  <g id="358">
   <use class="kv110" height="30" transform="rotate(0,855,445) scale(-1.11111,-0.814815) translate(-1623.67,-993.914)" width="15" x="846.6666666666667" xlink:href="#Disconnector:刀闸_0" y="432.7777913411458" zvalue="175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454877904898" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454877904898"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,855,445) scale(-1.11111,-0.814815) translate(-1623.67,-993.914)" width="15" x="846.6666666666667" y="432.7777913411458"/></g>
  <g id="121">
   <use class="kv110" height="30" transform="rotate(0,977.083,336.222) scale(-1.11111,-0.814815) translate(-1855.62,-751.636)" width="15" x="968.75" xlink:href="#Disconnector:刀闸_0" y="324" zvalue="193"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454878167042" ObjectName="110kV木笼河二三级线1421隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454878167042"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,977.083,336.222) scale(-1.11111,-0.814815) translate(-1855.62,-751.636)" width="15" x="968.75" y="324"/></g>
  <g id="135">
   <use class="kv110" height="30" transform="rotate(0,978.333,204.972) scale(-1.11111,-0.814815) translate(-1858,-459.307)" width="15" x="970" xlink:href="#Disconnector:刀闸_0" y="192.7499999999999" zvalue="207"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454878494722" ObjectName="110kV木笼河二三级线1426隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454878494722"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,978.333,204.972) scale(-1.11111,-0.814815) translate(-1858,-459.307)" width="15" x="970" y="192.7499999999999"/></g>
  <g id="169">
   <use class="kv110" height="30" transform="rotate(0,1437.23,336.666) scale(-1.11111,-0.814815) translate(-2729.9,-752.624)" width="15" x="1428.892857142857" xlink:href="#Disconnector:刀闸_0" y="324.443545534168" zvalue="216"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454879412226" ObjectName="110kV木笼河三五级线1431隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454879412226"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1437.23,336.666) scale(-1.11111,-0.814815) translate(-2729.9,-752.624)" width="15" x="1428.892857142857" y="324.443545534168"/></g>
  <g id="160">
   <use class="kv110" height="30" transform="rotate(0,1438.48,205.416) scale(-1.11111,-0.814815) translate(-2732.27,-460.295)" width="15" x="1430.142857142857" xlink:href="#Disconnector:刀闸_0" y="193.1935455341681" zvalue="228"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454879084546" ObjectName="110kV木笼河三五级线1436隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454879084546"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1438.48,205.416) scale(-1.11111,-0.814815) translate(-2732.27,-460.295)" width="15" x="1430.142857142857" y="193.1935455341681"/></g>
  <g id="132">
   <use class="kv110" height="30" transform="rotate(0,701.448,339.151) scale(-1.11111,-0.814815) translate(-1331.92,-758.159)" width="15" x="693.1150793650793" xlink:href="#Disconnector:刀闸_0" y="326.9285711560931" zvalue="268"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454880264194" ObjectName="110kV备用线1411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454880264194"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,701.448,339.151) scale(-1.11111,-0.814815) translate(-1331.92,-758.159)" width="15" x="693.1150793650793" y="326.9285711560931"/></g>
  <g id="105">
   <use class="kv110" height="30" transform="rotate(0,702.698,207.901) scale(-1.11111,-0.814815) translate(-1334.29,-465.83)" width="15" x="694.3650793650793" xlink:href="#Disconnector:刀闸_0" y="195.6785711560931" zvalue="281"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454879936514" ObjectName="110kV备用线1416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454879936514"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,702.698,207.901) scale(-1.11111,-0.814815) translate(-1334.29,-465.83)" width="15" x="694.3650793650793" y="195.6785711560931"/></g>
  <g id="14">
   <use class="kv10" height="26" transform="rotate(0,1420,693) scale(1,-1) translate(0,-1386)" width="12" x="1414" xlink:href="#Disconnector:小车隔刀熔断器_0" y="680" zvalue="433"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450174255109" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450174255109"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1420,693) scale(1,-1) translate(0,-1386)" width="12" x="1414" y="680"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="178">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="178" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,721.091,463.627) scale(1,1) translate(2.25184e-13,4.99644e-14)" writing-mode="lr" x="720.62" xml:space="preserve" y="468.34" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136668082178" ObjectName="P"/>
   </metadata>
  </g>
  <g id="179">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="179" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1438.43,36.7342) scale(1,1) translate(-9.2821e-13,5.36608e-14)" writing-mode="lr" x="1437.96" xml:space="preserve" y="41.43" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136674308098" ObjectName="P"/>
   </metadata>
  </g>
  <g id="180">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="180" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,705.222,42.3306) scale(1,1) translate(2.19898e-13,-3.42319e-14)" writing-mode="lr" x="704.75" xml:space="preserve" y="47.01" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136676470786" ObjectName="P"/>
   </metadata>
  </g>
  <g id="183">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="183" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,721.091,492.353) scale(1,1) translate(2.25184e-13,5.31537e-14)" writing-mode="lr" x="720.62" xml:space="preserve" y="497.07" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136668147714" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="185">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="185" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1438.43,63.3056) scale(1,1) translate(-9.2821e-13,1.12661e-13)" writing-mode="lr" x="1437.96" xml:space="preserve" y="68.01000000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136674373634" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="188">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="188" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,705.222,66.6389) scale(1,1) translate(2.19898e-13,-6.12196e-14)" writing-mode="lr" x="704.75" xml:space="preserve" y="71.31999999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136676536322" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="190">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="190" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,721.091,521.08) scale(1,1) translate(2.25184e-13,5.6343e-14)" writing-mode="lr" x="720.62" xml:space="preserve" y="525.8" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136668213250" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="191">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="191" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1438.43,89.8769) scale(1,1) translate(-9.2821e-13,1.71661e-13)" writing-mode="lr" x="1437.96" xml:space="preserve" y="94.58" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136674439170" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="192">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="192" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,705.222,90.9472) scale(1,1) translate(2.19898e-13,-8.82073e-14)" writing-mode="lr" x="704.75" xml:space="preserve" y="95.63" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136676601858" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="194">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="194" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1279.91,448.025) scale(1,1) translate(0,1.93114e-13)" writing-mode="lr" x="1279.33" xml:space="preserve" y="452.73" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136669655042" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="196">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="196" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1279.91,475.872) scale(1,1) translate(0,5.13701e-14)" writing-mode="lr" x="1279.33" xml:space="preserve" y="480.58" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136669720578" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="197">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="197" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1273.24,644.724) scale(1,1) translate(0,0)" writing-mode="lr" x="1272.67" xml:space="preserve" y="649.4400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136669786114" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="198">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="198" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1273.24,673.007) scale(1,1) translate(0,0)" writing-mode="lr" x="1272.67" xml:space="preserve" y="677.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136669851650" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="199">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="199" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1279.91,503.718) scale(1,1) translate(0,5.44617e-14)" writing-mode="lr" x="1279.33" xml:space="preserve" y="508.43" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136669917186" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="200">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="200" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1273.24,701.29) scale(1,1) translate(0,0)" writing-mode="lr" x="1272.67" xml:space="preserve" y="706" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136670244866" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="201">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="201" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,979.376,37.4886) scale(1,1) translate(0,4.49024e-14)" writing-mode="lr" x="978.91" xml:space="preserve" y="42.18" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136673259522" ObjectName="P"/>
   </metadata>
  </g>
  <g id="202">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="202" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,979.376,63.3056) scale(1,1) translate(0,9.07626e-14)" writing-mode="lr" x="978.91" xml:space="preserve" y="68" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136673325058" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="203">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="203" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,979.376,89.1226) scale(1,1) translate(0,1.36623e-13)" writing-mode="lr" x="978.91" xml:space="preserve" y="93.81999999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136673390594" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="204">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="204" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,594.388,906.259) scale(1,1) translate(0,-1.58391e-12)" writing-mode="lr" x="593.8099999999999" xml:space="preserve" y="910.99" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136664281090" ObjectName="P"/>
   </metadata>
  </g>
  <g id="205">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="205" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,594.388,937.126) scale(1,1) translate(0,2.04842e-13)" writing-mode="lr" x="593.8099999999999" xml:space="preserve" y="941.86" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136664346626" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="206">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="206" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,594.388,967.992) scale(1,1) translate(0,-1.69357e-12)" writing-mode="lr" x="593.8099999999999" xml:space="preserve" y="972.72" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136664412162" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="207">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="207" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1047.42,910.556) scale(1,1) translate(0,-1.59154e-12)" writing-mode="lr" x="1046.84" xml:space="preserve" y="915.29" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136663101442" ObjectName="P"/>
   </metadata>
  </g>
  <g id="208">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="208" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1047.42,941.423) scale(1,1) translate(0,2.05796e-13)" writing-mode="lr" x="1046.84" xml:space="preserve" y="946.15" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136663166978" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="209">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="209" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1047.42,972.29) scale(1,1) translate(0,-1.7012e-12)" writing-mode="lr" x="1046.84" xml:space="preserve" y="977.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136663232514" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="214">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="214" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136672604162" ObjectName="F"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136695083009" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136695279617" ObjectName="F"/>
   </metadata>
  </g>
  <g id="247">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="247" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,357.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="362.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136665853954" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136695083009" ObjectName="F"/>
   </metadata>
  </g>
  <g id="189">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="189" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136695148545" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127181942791" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127181877253" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="278">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="278" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,380.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="385.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136694755329" ObjectName="F"/>
   </metadata>
  </g>
  <g id="280">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="280" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,400.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="405.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136694886401" ObjectName="F"/>
   </metadata>
  </g>
  <g id="281">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="281" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,331.611,380.389) scale(1,1) translate(0,0)" writing-mode="lr" x="331.77" xml:space="preserve" y="385.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136694820865" ObjectName="F"/>
   </metadata>
  </g>
  <g id="282">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="282" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,331.611,400.389) scale(1,1) translate(0,0)" writing-mode="lr" x="331.77" xml:space="preserve" y="405.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136695017473" ObjectName="F"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="5" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,867.143,578.357) scale(1,1) translate(0,0)" writing-mode="lr" x="866.67" xml:space="preserve" y="583.13" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136672210946" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="7" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,867.143,607.357) scale(1,1) translate(0,0)" writing-mode="lr" x="866.67" xml:space="preserve" y="612.13" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136672276482" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="9" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,867.143,636.357) scale(1,1) translate(0,0)" writing-mode="lr" x="866.67" xml:space="preserve" y="641.13" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136672342018" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="10" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,572.254,792.427) scale(1,1) translate(0,0)" writing-mode="lr" x="571.79" xml:space="preserve" y="797.21" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136665722882" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="11" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,588.143,401.857) scale(1,1) translate(0,1.70689e-13)" writing-mode="lr" x="587.67" xml:space="preserve" y="406.63" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136672473090" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="213">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="401"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374929690625" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="402"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562962206228481" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
</svg>