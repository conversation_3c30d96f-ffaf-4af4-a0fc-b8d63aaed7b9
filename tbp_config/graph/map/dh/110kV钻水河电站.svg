<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549581971458" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Accessory:四卷带壁雷器母线PT_0" viewBox="0,0,40,35">
   <use terminal-index="0" type="0" x="22.10905664884498" xlink:href="#terminal" y="34.51612485684674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="28.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,30,14.36) scale(1,-1) translate(0,-930.43)" width="7" x="26.5" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="16.27740325661302" y2="15.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="7.166666666666671" y1="16.27740325661302" y2="12.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="12.27740325661302" y2="13.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="30.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="22" y1="28.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="14" y1="21.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="30" y1="28.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="30" y1="28.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.92943360505483" x2="29.92943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="13.81" cy="17.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.95921744067709" x2="27.68240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.12588410734375" x2="28.51573492932657" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="30.54255077401042" x2="29.09906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599325" x2="16.18240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="13.78240159599324" y1="15.23154965466559" y2="17.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="11.38240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <ellipse cx="20.23" cy="13.99" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.48" cy="10.41" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="17.79906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="20.19906826265991" y1="11.73154965466559" y2="14.2066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="22.59906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="8.148216321332258" y2="10.62329629003646"/>
   <ellipse cx="6.64" cy="14.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:586_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.75" x2="10.08333333333333" y1="17.11381925541057" y2="17.11381925541057"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.98518194534241" x2="9.999999999999998" y1="9.386991192770131" y2="16.99800064973551"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00299079104462" x2="19.66666666666667" y1="9.301854138598824" y2="16.8218426122152"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:586_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="30.58333333333333" y2="36.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="21" y1="36.5" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="10" y1="36.5" y2="40.5"/>
  </symbol>
  <symbol id="Disconnector:20210316_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="6.66666666666667" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="6.727157046327149" y2="14.02109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="6.734473004260392" y2="14.02109851866368"/>
  </symbol>
  <symbol id="Disconnector:20210316_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="0.25" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="0.7271570463271502" y2="8.021098518663681"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="0.7344730042603906" y2="8.021098518663679"/>
  </symbol>
  <symbol id="Disconnector:20210316_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.750000000000004" y2="4.166666666666668"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="32"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="1" y1="27" y2="11"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="27" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Accessory:带熔断器四卷PT_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12" y2="1"/>
   <ellipse cx="15.25" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.08" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.25" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="18.82" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="11" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.25" x2="15.25" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25" x2="15.25" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="15.25" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="15" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="15" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="20.94444444444444" y1="21.46612466124661" y2="21.46612466124661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="18.31481481481482" y1="21.46612466124661" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.94444444444444" x2="19.62962962962963" y1="21.46612466124661" y2="19"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,6.21) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="2.5"/>
  </symbol>
  <symbol id="Accessory:四卷PT_0" viewBox="0,0,18,18">
   <use terminal-index="0" type="0" x="9" xlink:href="#terminal" y="0.2666666666666639"/>
   <ellipse cx="9.25" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.25" cy="9.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.08" cy="12.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.82" cy="9.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="11.75" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.25" x2="9.25" y1="7.500000000000001" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="15.25" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="12.31481481481481" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.94444444444444" x2="13.62962962962963" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14.94444444444444" y1="9.966124661246614" y2="9.966124661246614"/>
  </symbol>
  <symbol id="Accessory:20210316PT_0" viewBox="0,0,15,26">
   <use terminal-index="0" type="0" x="7.45" xlink:href="#terminal" y="0.09999999999999964"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="8.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="6.833333333333331" y2="0"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="4.916666666666666" y1="11.25" y2="13.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="10.5" y1="11.25" y2="13.25"/>
   <ellipse cx="7.53" cy="11.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.5" cy="20.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333333" x2="7.583333333333333" y1="17.5" y2="20.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333332" x2="4.999999999999998" y1="20.5" y2="22.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="10.58333333333334" y1="20.5" y2="22.5"/>
  </symbol>
  <symbol id="Disconnector:手车2021_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,17.5) scale(1,1) translate(0,0)" width="6" x="3" y="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="12.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:手车2021_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="4.583333333333332" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,17.5) scale(1,1) translate(0,0)" width="6" x="3" y="12"/>
  </symbol>
  <symbol id="Disconnector:手车2021_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0.25" y2="25.75"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Ground:大地_0" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6" x2="6" y1="13.08333333333334" y2="0.1666666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.5833333333333313" x2="11.25" y1="12.99453511141348" y2="12.99453511141348"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.083333333333333" x2="8" y1="17.63116790988687" y2="17.63116790988687"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666667" x2="10.33333333333333" y1="15.40451817731685" y2="15.40451817731685"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:隔离变_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="14.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86725" x2="11.63558333333333" y1="8.877992633517497" y2="11.52992633517496"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86725" x2="14.86725" y1="5.231583793738499" y2="8.877992633517501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86725" x2="18.32975" y1="8.87799263351749" y2="11.41942909760589"/>
   <ellipse cx="15.08" cy="19.29" fill-opacity="0" rx="6.93" ry="6.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.08" cy="8.550000000000001" fill-opacity="0" rx="6.93" ry="6.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86725" x2="14.86725" y1="15.94981583793738" y2="19.2647329650092"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86725000000001" x2="11.63558333333334" y1="19.26473296500919" y2="21.91666666666665"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86725" x2="18.32975" y1="19.26473296500921" y2="21.8061694290976"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:空挂线路_0" viewBox="0,0,11,13">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="-0.0833333333333357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="12.91666666666667" y2="0.2500000000000009"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV钻水河电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="47" xlink:href="logo.png" y="41.25"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,175,71.25) scale(1,1) translate(0,0)" writing-mode="lr" x="175" xml:space="preserve" y="74.75" zvalue="23"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,175.5,70.9403) scale(1,1) translate(0,0)" writing-mode="lr" x="175.5" xml:space="preserve" y="79.94" zvalue="24">110kV钻水河电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="159" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="814"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="814">信号一览</text>
  <line fill="none" id="50" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="381" x2="381" y1="9.25" y2="1039.25" zvalue="25"/>
  <line fill="none" id="48" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.000000000000455" x2="374" y1="145.1204926140824" y2="145.1204926140824" zvalue="27"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,473.03,683.686) scale(1,1) translate(0,0)" writing-mode="lr" x="473.03" xml:space="preserve" y="688.1900000000001" zvalue="62">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" x="1035.9765625" xml:space="preserve" y="964.4869357854257" zvalue="102">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1035.9765625" xml:space="preserve" y="980.4869357854257" zvalue="102">7MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1008.27,754.419) scale(1,1) translate(0,0)" writing-mode="lr" x="1008.27" xml:space="preserve" y="758.92" zvalue="114">032</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,512.097,903.722) scale(1,1) translate(0,0)" writing-mode="lr" x="512.1" xml:space="preserve" y="908.22" zvalue="136">#1站用变 315kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,544.297,771.875) scale(1,1) translate(0,0)" writing-mode="lr" x="544.3" xml:space="preserve" y="776.37" zvalue="144">033</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,570.972,645.417) scale(1,1) translate(0,0)" writing-mode="lr" x="570.97" xml:space="preserve" y="649.92" zvalue="156">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,532.412,546.528) scale(1,1) translate(0,0)" writing-mode="lr" x="532.41" xml:space="preserve" y="551.03" zvalue="164">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1260.5,669.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1260.5" xml:space="preserve" y="674" zvalue="168">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,494.614,297.409) scale(1,1) translate(0,0)" writing-mode="lr" x="494.61" xml:space="preserve" y="301.91" zvalue="265">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,833.528,365.972) scale(1,1) translate(-2.39883e-12,0)" writing-mode="lr" x="833.53" xml:space="preserve" y="370.47" zvalue="267">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,842.861,445.972) scale(1,1) translate(0,0)" writing-mode="lr" x="842.86" xml:space="preserve" y="450.47" zvalue="269">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,865.194,414.972) scale(1,1) translate(0,0)" writing-mode="lr" x="865.1900000000001" xml:space="preserve" y="419.47" zvalue="272">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="254" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,904.661,535.419) scale(1,1) translate(-1.94727e-13,5.77414e-14)" writing-mode="lr" x="904.66" xml:space="preserve" y="539.92" zvalue="275">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="273" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,848.313,650.052) scale(1,1) translate(0,5.69812e-13)" writing-mode="lr" x="848.3099999999999" xml:space="preserve" y="654.55" zvalue="284">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,904.813,555.419) scale(1,1) translate(0,1.2144e-13)" writing-mode="lr" x="904.8099999999999" xml:space="preserve" y="559.92" zvalue="297">20MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="293" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1598.75,420.861) scale(1,1) translate(0,0)" writing-mode="lr" x="1598.75" xml:space="preserve" y="425.36" zvalue="302">002</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1662.79,479.419) scale(1,1) translate(0,0)" writing-mode="lr" x="1662.79" xml:space="preserve" y="483.92" zvalue="308">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1602.19,610.052) scale(1,1) translate(0,0)" writing-mode="lr" x="1602.19" xml:space="preserve" y="614.55" zvalue="316">602</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="295" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1661.81,499.419) scale(1,1) translate(0,-1.09006e-13)" writing-mode="lr" x="1661.81" xml:space="preserve" y="503.92" zvalue="328">2MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,598.041,232.808) scale(1,1) translate(0,-4.47717e-13)" writing-mode="lr" x="598.04" xml:space="preserve" y="237.31" zvalue="407">110</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,585.968,284.94) scale(1,1) translate(0,3.37272e-13)" writing-mode="lr" x="585.97" xml:space="preserve" y="289.44" zvalue="409">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,590.308,166.546) scale(1,1) translate(0,-3.15299e-13)" writing-mode="lr" x="590.3099999999999" xml:space="preserve" y="171.05" zvalue="412">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,573.031,66.1472) scale(1,1) translate(0,0)" writing-mode="lr" x="573.03" xml:space="preserve" y="70.65000000000001" zvalue="417">备用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,628.494,275.196) scale(1,1) translate(0,3.25372e-13)" writing-mode="lr" x="628.49" xml:space="preserve" y="279.7" zvalue="419">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,633.039,212.249) scale(1,1) translate(6.93687e-13,-4.06634e-13)" writing-mode="lr" x="633.04" xml:space="preserve" y="216.75" zvalue="421">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,628.494,161.257) scale(1,1) translate(6.8864e-13,-3.0473e-13)" writing-mode="lr" x="628.49" xml:space="preserve" y="165.76" zvalue="423">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,752.284,287.205) scale(1,1) translate(0,-2.47466e-13)" writing-mode="lr" x="752.28" xml:space="preserve" y="291.71" zvalue="433">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,754.072,171.326) scale(1,1) translate(0,-1.44544e-13)" writing-mode="lr" x="754.0700000000001" xml:space="preserve" y="175.83" zvalue="436">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,734.447,72.5221) scale(1,1) translate(0,0)" writing-mode="lr" x="734.45" xml:space="preserve" y="77.02" zvalue="441">110kV平钻线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,791.446,277.668) scale(1,1) translate(0,-2.38995e-13)" writing-mode="lr" x="791.45" xml:space="preserve" y="282.17" zvalue="443">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,791.446,221.397) scale(1,1) translate(5.21849e-13,-1.89017e-13)" writing-mode="lr" x="791.45" xml:space="preserve" y="225.9" zvalue="445">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,791.446,162.981) scale(1,1) translate(5.21849e-13,-1.37132e-13)" writing-mode="lr" x="791.45" xml:space="preserve" y="167.48" zvalue="447">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="186" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,940.793,237.998) scale(1,1) translate(0,-2.03761e-13)" writing-mode="lr" x="940.79" xml:space="preserve" y="242.5" zvalue="456">133</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,931.374,289.023) scale(1,1) translate(0,-2.4908e-13)" writing-mode="lr" x="931.37" xml:space="preserve" y="293.52" zvalue="458">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,933.163,173.144) scale(1,1) translate(0,-1.46159e-13)" writing-mode="lr" x="933.16" xml:space="preserve" y="177.64" zvalue="461">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,913.538,74.3403) scale(1,1) translate(0,0)" writing-mode="lr" x="913.54" xml:space="preserve" y="78.84" zvalue="466">110kV钻允线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,970.537,279.486) scale(1,1) translate(0,-2.4061e-13)" writing-mode="lr" x="970.54" xml:space="preserve" y="283.99" zvalue="468">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,970.537,223.216) scale(1,1) translate(2.13716e-13,-1.90631e-13)" writing-mode="lr" x="970.54" xml:space="preserve" y="227.72" zvalue="470">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,970.537,164.799) scale(1,1) translate(-2.13716e-13,-1.38747e-13)" writing-mode="lr" x="970.54" xml:space="preserve" y="169.3" zvalue="472">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="305" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,548.306,390.258) scale(1,1) translate(0,0)" writing-mode="lr" x="548.3099999999999" xml:space="preserve" y="394.76" zvalue="480">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="272" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,558.333,458.785) scale(1,1) translate(0,0)" writing-mode="lr" x="558.33" xml:space="preserve" y="463.29" zvalue="482">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,565.833,364.952) scale(1,1) translate(0,0)" writing-mode="lr" x="565.83" xml:space="preserve" y="369.45" zvalue="484">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="341" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1014.13,661.905) scale(1,1) translate(3.33777e-13,0)" writing-mode="lr" x="1014.13" xml:space="preserve" y="666.4" zvalue="499">034</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="339" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1018.92,478.948) scale(1,1) translate(0,0)" writing-mode="lr" x="1018.92" xml:space="preserve" y="483.45" zvalue="509">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="333" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1019.4,524.992) scale(1,1) translate(-2.23688e-13,0)" writing-mode="lr" x="1019.4" xml:space="preserve" y="529.49" zvalue="511">035</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="407" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,990.663,377.778) scale(1,1) translate(0,0)" writing-mode="lr" x="990.66" xml:space="preserve" y="382.28" zvalue="522">至近区</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="413" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1146.35,658.572) scale(1,1) translate(0,0)" writing-mode="lr" x="1146.35" xml:space="preserve" y="663.0700000000001" zvalue="528">036</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="412" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1152.25,476.725) scale(1,1) translate(0,0)" writing-mode="lr" x="1152.25" xml:space="preserve" y="481.23" zvalue="531">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="411" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1149.14,522.769) scale(1,1) translate(0,0)" writing-mode="lr" x="1149.14" xml:space="preserve" y="527.27" zvalue="533">037</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="435" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1802.56,633.233) scale(1,1) translate(6.34673e-12,0)" writing-mode="lr" x="1802.56" xml:space="preserve" y="637.73" zvalue="554">6351</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="433" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1757.46,504.266) scale(1,1) translate(0,-1.09638e-13)" writing-mode="lr" x="1757.46" xml:space="preserve" y="508.77" zvalue="558">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="449" stroke="rgb(255,255,255)" text-anchor="middle" x="1447.1640625" xml:space="preserve" y="960.001396787621" zvalue="564">#3发电机 </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="449" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1447.1640625" xml:space="preserve" y="976.001396787621" zvalue="564">0.8MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="442" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1358.8,885.051) scale(1,1) translate(0,0)" writing-mode="lr" x="1358.8" xml:space="preserve" y="889.55" zvalue="583">6932</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="488" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1466.02,743.385) scale(1,1) translate(0,0)" writing-mode="lr" x="1466.02" xml:space="preserve" y="747.88" zvalue="604">633</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="492" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1354.98,816.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1354.98" xml:space="preserve" y="820.97" zvalue="609">6931</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="493" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1326.49,729.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1326.49" xml:space="preserve" y="734.14" zvalue="610">励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="494" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1327.57,956.071) scale(1,1) translate(0,0)" writing-mode="lr" x="1327.57" xml:space="preserve" y="960.5700000000001" zvalue="612">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="501" stroke="rgb(255,255,255)" text-anchor="middle" x="1717.8671875" xml:space="preserve" y="963.579521787621" zvalue="620">#4发电机 </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="501" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1717.8671875" xml:space="preserve" y="979.579521787621" zvalue="620">0.8MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="499" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1629.51,888.622) scale(1,1) translate(0,0)" writing-mode="lr" x="1629.51" xml:space="preserve" y="893.12" zvalue="624">6942</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="498" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1736.73,746.956) scale(1,1) translate(0,0)" writing-mode="lr" x="1736.73" xml:space="preserve" y="751.46" zvalue="629">634</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="497" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1624.69,820.044) scale(1,1) translate(0,0)" writing-mode="lr" x="1624.69" xml:space="preserve" y="824.54" zvalue="634">6941</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="496" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1597.2,733.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1597.2" xml:space="preserve" y="737.71" zvalue="635">励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="495" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1597.2,959.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1597.2" xml:space="preserve" y="964.14" zvalue="637">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="525" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1078.76,840.606) scale(1,1) translate(4.72404e-13,0)" writing-mode="lr" x="1078.76" xml:space="preserve" y="845.11" zvalue="653">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="539" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1140.34,832.784) scale(1,1) translate(0,0)" writing-mode="lr" x="1140.34" xml:space="preserve" y="837.28" zvalue="660">0922</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="545" stroke="rgb(255,255,255)" text-anchor="middle" x="731.5390625" xml:space="preserve" y="964.4869357854257" zvalue="670">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="545" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="731.5390625" xml:space="preserve" y="980.4869357854257" zvalue="670">7MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="544" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,695.17,756.419) scale(1,1) translate(0,0)" writing-mode="lr" x="695.17" xml:space="preserve" y="760.92" zvalue="672">031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="542" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,770.908,840.606) scale(1,1) translate(0,0)" writing-mode="lr" x="770.91" xml:space="preserve" y="845.11" zvalue="678">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="541" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,833.698,832.784) scale(1,1) translate(3.63131e-13,0)" writing-mode="lr" x="833.7" xml:space="preserve" y="837.28" zvalue="681">0912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="568" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1362.91,613.706) scale(1,1) translate(0,0)" writing-mode="lr" x="1362.91" xml:space="preserve" y="618.21" zvalue="694">6901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="569" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1444.11,617) scale(1,1) translate(0,0)" writing-mode="lr" x="1444.11" xml:space="preserve" y="621.5" zvalue="695">6801</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,941.25,593.75) scale(1,1) translate(0,0)" writing-mode="lr" x="941.25" xml:space="preserve" y="598.25" zvalue="729">近区变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1176.25,593.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1176.25" xml:space="preserve" y="598.25" zvalue="732">#1隔离变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,757.854,233.904) scale(1,1) translate(0,0)" writing-mode="lr" x="757.85" xml:space="preserve" y="238.4" zvalue="741">132</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="202" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="792"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="794">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="795">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="796">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="797">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="798">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="189" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="800">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="801">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="802">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="178" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="803">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="804">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="805">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="166" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.875,334) scale(1,1) translate(0,0)" writing-mode="lr" x="240.88" xml:space="preserve" y="338.5" zvalue="806">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,359.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="363.75" zvalue="807">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="815">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="816">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="819">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="821">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="823">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="824">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="825">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="827">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,207.5,952) scale(1,1) translate(0,0)" writing-mode="lr" x="207.5" xml:space="preserve" y="958" zvalue="832">ZuanShuiHe-01-2012</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="814"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="53">
   <path class="kv10" d="M 497.5 700.69 L 1141.19 700.69" stroke-width="6" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674234138628" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674234138628"/></metadata>
  <path d="M 497.5 700.69 L 1141.19 700.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="v6300" d="M 1271 703.5 L 1876 703.5" stroke-width="6" zvalue="167"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674234204165" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674234204165"/></metadata>
  <path d="M 1271 703.5 L 1876 703.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv110" d="M 496.36 323.41 L 1030.91 323.41" stroke-width="6" zvalue="264"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674234269701" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674234269701"/></metadata>
  <path d="M 496.36 323.41 L 1030.91 323.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="123">
   <use class="kv10" height="30" transform="rotate(0,1035.03,922.811) scale(1.43307,1.43307) translate(-306.285,-272.373)" width="30" x="1013.534397816687" xlink:href="#Generator:发电机_0" y="901.3152966276575" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449553367045" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449553367045"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1035.03,922.811) scale(1.43307,1.43307) translate(-306.285,-272.373)" width="30" x="1013.534397816687" y="901.3152966276575"/></g>
  <g id="470">
   <use class="v6300" height="30" transform="rotate(0,1446.28,920.957) scale(1.34761,1.34761) translate(-367.844,-232.341)" width="30" x="1426.062592791536" xlink:href="#Generator:发电机_0" y="900.7433376813401" zvalue="563"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449556709382" ObjectName="#3发电机"/>
   <cge:TPSR_Ref TObjectID="6192449556709382"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1446.28,920.957) scale(1.34761,1.34761) translate(-367.844,-232.341)" width="30" x="1426.062592791536" y="900.7433376813401"/></g>
  <g id="517">
   <use class="v6300" height="30" transform="rotate(0,1716.99,924.529) scale(1.34761,1.34761) translate(-437.673,-233.262)" width="30" x="1696.776878505822" xlink:href="#Generator:发电机_0" y="904.3147662527687" zvalue="619"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449557561350" ObjectName="#4发电机"/>
   <cge:TPSR_Ref TObjectID="6192449557561350"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1716.99,924.529) scale(1.34761,1.34761) translate(-437.673,-233.262)" width="30" x="1696.776878505822" y="904.3147662527687"/></g>
  <g id="559">
   <use class="kv10" height="30" transform="rotate(0,730.586,922.811) scale(1.43307,1.43307) translate(-214.283,-272.373)" width="30" x="709.0899533722422" xlink:href="#Generator:发电机_0" y="901.3152966276573" zvalue="669"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449558413317" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449558413317"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,730.586,922.811) scale(1.43307,1.43307) translate(-214.283,-272.373)" width="30" x="709.0899533722422" y="901.3152966276573"/></g>
 </g>
 <g id="BreakerClass">
  <g id="115">
   <use class="kv10" height="20" transform="rotate(0,1033.93,755.375) scale(1.59229,1.43307) translate(-381.634,-223.94)" width="10" x="1025.966147107888" xlink:href="#Breaker:小车断路器_0" y="741.0439675338483" zvalue="113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924473126917" ObjectName="#2发电机032断路器"/>
   <cge:TPSR_Ref TObjectID="6473924473126917"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1033.93,755.375) scale(1.59229,1.43307) translate(-381.634,-223.94)" width="10" x="1025.966147107888" y="741.0439675338483"/></g>
  <g id="129">
   <use class="kv10" height="20" transform="rotate(0,516.257,772.875) scale(1.59229,1.43307) translate(-189.073,-229.228)" width="10" x="508.2951602849556" xlink:href="#Breaker:小车断路器_0" y="758.5439675338482" zvalue="143"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924473192453" ObjectName="#1站用变033断路器"/>
   <cge:TPSR_Ref TObjectID="6473924473192453"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,516.257,772.875) scale(1.59229,1.43307) translate(-189.073,-229.228)" width="10" x="508.2951602849556" y="758.5439675338482"/></g>
  <g id="267">
   <use class="kv110" height="20" transform="rotate(0,822.083,446.972) scale(1.22222,1.11111) translate(-148.359,-43.5861)" width="10" x="815.9722324079938" xlink:href="#Breaker:开关_0" y="435.8611111111111" zvalue="268"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924473257989" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473924473257989"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,822.083,446.972) scale(1.22222,1.11111) translate(-148.359,-43.5861)" width="10" x="815.9722324079938" y="435.8611111111111"/></g>
  <g id="282">
   <use class="kv10" height="20" transform="rotate(0,822.098,651.052) scale(1.95807,1.78006) translate(-397.457,-277.505)" width="10" x="812.308077836856" xlink:href="#Breaker:小车断路器_0" y="633.2509481830512" zvalue="283"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924473323525" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924473323525"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,822.098,651.052) scale(1.95807,1.78006) translate(-397.457,-277.505)" width="10" x="812.308077836856" y="633.2509481830512"/></g>
  <g id="314">
   <use class="kv10" height="20" transform="rotate(0,1577.97,421.861) scale(1.95807,1.78006) translate(-767.301,-177.068)" width="10" x="1568.181877562507" xlink:href="#Breaker:小车断路器_0" y="404.0604659374998" zvalue="301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924473454597" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473924473454597"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1577.97,421.861) scale(1.95807,1.78006) translate(-767.301,-177.068)" width="10" x="1568.181877562507" y="404.0604659374998"/></g>
  <g id="304">
   <use class="v6300" height="20" transform="rotate(0,1575.98,611.052) scale(1.95807,1.78006) translate(-766.324,-259.976)" width="10" x="1566.185888943699" xlink:href="#Breaker:小车断路器_0" y="593.2509481830512" zvalue="315"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924473389061" ObjectName="#2主变6.3kV侧602断路器"/>
   <cge:TPSR_Ref TObjectID="6473924473389061"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1575.98,611.052) scale(1.95807,1.78006) translate(-766.324,-259.976)" width="10" x="1566.185888943699" y="593.2509481830512"/></g>
  <g id="76">
   <use class="kv110" height="20" transform="rotate(0,574.655,233.904) scale(1.33986,1.21805) translate(-144.063,-39.6924)" width="10" x="567.9552608652205" xlink:href="#Breaker:开关_0" y="221.7235143954651" zvalue="406"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924473520133" ObjectName="备用110断路器"/>
   <cge:TPSR_Ref TObjectID="6473924473520133"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,574.655,233.904) scale(1.33986,1.21805) translate(-144.063,-39.6924)" width="10" x="567.9552608652205" y="221.7235143954651"/></g>
  <g id="223">
   <use class="kv110" height="20" transform="rotate(0,917.903,239.071) scale(1.31139,1.19217) translate(-216.397,-36.6149)" width="10" x="911.3460170536998" xlink:href="#Breaker:开关_0" y="227.1496789241271" zvalue="455"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924473651205" ObjectName="110kV钻允线133断路器"/>
   <cge:TPSR_Ref TObjectID="6473924473651205"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,917.903,239.071) scale(1.31139,1.19217) translate(-216.397,-36.6149)" width="10" x="911.3460170536998" y="227.1496789241271"/></g>
  <g id="400">
   <use class="kv10" height="20" transform="rotate(0,987.668,658.688) scale(1.95807,1.78006) translate(-478.469,-280.851)" width="10" x="977.8780694251395" xlink:href="#Breaker:小车断路器_0" y="640.8874391719495" zvalue="498"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924473782277" ObjectName="近区变母线侧034断路器"/>
   <cge:TPSR_Ref TObjectID="6473924473782277"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,987.668,658.688) scale(1.95807,1.78006) translate(-478.469,-280.851)" width="10" x="977.8780694251395" y="640.8874391719495"/></g>
  <g id="391">
   <use class="kv10" height="20" transform="rotate(0,987.623,526.012) scale(1.2475,1.13409) translate(-194.702,-60.8519)" width="10" x="981.3854823328841" xlink:href="#Breaker:开关_0" y="514.6714055696024" zvalue="510"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924473716741" ObjectName="近区变10kV侧035断路器"/>
   <cge:TPSR_Ref TObjectID="6473924473716741"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,987.623,526.012) scale(1.2475,1.13409) translate(-194.702,-60.8519)" width="10" x="981.3854823328841" y="514.6714055696024"/></g>
  <g id="424">
   <use class="kv10" height="20" transform="rotate(0,1119.89,655.355) scale(1.95807,1.78006) translate(-543.165,-279.391)" width="10" x="1110.100291647362" xlink:href="#Breaker:小车断路器_0" y="637.5541058386161" zvalue="527"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924473913349" ObjectName="#1隔离变母线侧036断路器"/>
   <cge:TPSR_Ref TObjectID="6473924473913349"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1119.89,655.355) scale(1.95807,1.78006) translate(-543.165,-279.391)" width="10" x="1110.100291647362" y="637.5541058386161"/></g>
  <g id="421">
   <use class="kv10" height="20" transform="rotate(0,1120.96,523.79) scale(1.2475,1.13409) translate(-221.155,-60.5891)" width="10" x="1114.718815666217" xlink:href="#Breaker:开关_0" y="512.4491833473802" zvalue="532"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924473847813" ObjectName="#1隔离变10kV侧037断路器"/>
   <cge:TPSR_Ref TObjectID="6473924473847813"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1120.96,523.79) scale(1.2475,1.13409) translate(-221.155,-60.5891)" width="10" x="1114.718815666217" y="512.4491833473802"/></g>
  <g id="478">
   <use class="v6300" height="20" transform="rotate(0,1447.09,744.385) scale(1.95807,1.78006) translate(-703.26,-318.406)" width="10" x="1437.29700005481" xlink:href="#Breaker:小车断路器_0" y="726.5842815163846" zvalue="603"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924473978885" ObjectName="#3发电机633断路器"/>
   <cge:TPSR_Ref TObjectID="6473924473978885"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1447.09,744.385) scale(1.95807,1.78006) translate(-703.26,-318.406)" width="10" x="1437.29700005481" y="726.5842815163846"/></g>
  <g id="511">
   <use class="v6300" height="20" transform="rotate(0,1717.8,747.956) scale(1.95807,1.78006) translate(-835.718,-319.971)" width="10" x="1708.011285769096" xlink:href="#Breaker:小车断路器_0" y="730.1557100878131" zvalue="628"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924474044421" ObjectName="#4发电机634断路器"/>
   <cge:TPSR_Ref TObjectID="6473924474044421"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1717.8,747.956) scale(1.95807,1.78006) translate(-835.718,-319.971)" width="10" x="1708.011285769096" y="730.1557100878131"/></g>
  <g id="558">
   <use class="kv10" height="20" transform="rotate(0,729.483,755.375) scale(1.59229,1.43307) translate(-268.388,-223.94)" width="10" x="721.5217026634439" xlink:href="#Breaker:小车断路器_0" y="741.0439675338483" zvalue="671"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924474109957" ObjectName="#1发电机031断路器"/>
   <cge:TPSR_Ref TObjectID="6473924474109957"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,729.483,755.375) scale(1.59229,1.43307) translate(-268.388,-223.94)" width="10" x="721.5217026634439" y="741.0439675338483"/></g>
  <g id="69">
   <use class="kv110" height="20" transform="rotate(0,739.655,234.904) scale(1.33986,1.21805) translate(-185.916,-39.8714)" width="10" x="732.9552608652205" xlink:href="#Breaker:开关_0" y="222.7235143954651" zvalue="740"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924474175493" ObjectName="110kV平钻线132断路器"/>
   <cge:TPSR_Ref TObjectID="6473924474175493"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,739.655,234.904) scale(1.33986,1.21805) translate(-185.916,-39.8714)" width="10" x="732.9552608652205" y="222.7235143954651"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="108">
   <path class="kv10" d="M 1033.93 768.27 L 1033.93 901.67" stroke-width="1" zvalue="124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@1" LinkObjectIDznd="123@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1033.93 768.27 L 1033.93 901.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv10" d="M 516.26 785.77 L 516.26 835.12" stroke-width="1" zvalue="145"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@1" LinkObjectIDznd="205@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 516.26 785.77 L 516.26 835.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv10" d="M 540.22 668.52 L 540.22 700.69" stroke-width="1" zvalue="159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@1" LinkObjectIDznd="53@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 540.22 668.52 L 540.22 700.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv10" d="M 540.11 628.05 L 540.15 614.9" stroke-width="1" zvalue="160"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="146@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 540.11 628.05 L 540.15 614.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="kv110" d="M 822.15 378.99 L 822.15 436.34" stroke-width="1" zvalue="270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@1" LinkObjectIDznd="267@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 822.15 378.99 L 822.15 436.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="264">
   <path class="kv110" d="M 822.18 355.15 L 822.18 323.41" stroke-width="1" zvalue="273"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="251@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 822.18 355.15 L 822.18 323.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="262">
   <path class="kv110" d="M 822.16 457.58 L 822.16 507.59" stroke-width="1" zvalue="276"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@1" LinkObjectIDznd="263@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 822.16 457.58 L 822.16 507.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="261">
   <path class="kv110" d="M 854.36 402.81 L 822.15 402.81" stroke-width="1" zvalue="277"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265@0" LinkObjectIDznd="266" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.36 402.81 L 822.15 402.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="260">
   <path class="kv110" d="M 822.13 533.87 L 734.19 533.87 L 734.19 552.25" stroke-width="1" zvalue="278"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@2" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 822.13 533.87 L 734.19 533.87 L 734.19 552.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="309">
   <path class="kv10" d="M 1577.97 437.88 L 1577.97 451.59" stroke-width="1" zvalue="309"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="314@1" LinkObjectIDznd="310@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1577.97 437.88 L 1577.97 451.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv110" d="M 574.71 298.99 L 574.71 323.41" stroke-width="1" zvalue="411"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="251@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 574.71 298.99 L 574.71 323.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv110" d="M 574.74 272.87 L 574.74 245.54" stroke-width="1" zvalue="413"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@1" LinkObjectIDznd="76@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 574.74 272.87 L 574.74 245.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv110" d="M 574.61 222.25 L 574.61 180.6" stroke-width="1" zvalue="414"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@0" LinkObjectIDznd="72@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 574.61 222.25 L 574.61 180.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv110" d="M 574.64 154.47 L 574.64 132.07" stroke-width="1" zvalue="416"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@1" LinkObjectIDznd="65@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 574.64 154.47 L 574.64 132.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv110" d="M 615.03 145.36 L 574.64 145.36" stroke-width="1" zvalue="426"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="58" MaxPinNum="2"/>
   </metadata>
  <path d="M 615.03 145.36 L 574.64 145.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv110" d="M 619.58 196.35 L 574.61 196.35" stroke-width="1" zvalue="427"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@0" LinkObjectIDznd="66" MaxPinNum="2"/>
   </metadata>
  <path d="M 619.58 196.35 L 574.61 196.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv110" d="M 615.03 259.3 L 574.74 259.2" stroke-width="1" zvalue="428"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="67" MaxPinNum="2"/>
   </metadata>
  <path d="M 615.03 259.3 L 574.74 259.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv110" d="M 738.71 300.96 L 738.71 323.41" stroke-width="1" zvalue="435"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@0" LinkObjectIDznd="251@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 738.71 300.96 L 738.71 323.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv110" d="M 738.74 159.51 L 738.74 135.59" stroke-width="1" zvalue="440"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@1" LinkObjectIDznd="172@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 738.74 159.51 L 738.74 135.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="kv110" d="M 778.27 147.42 L 738.74 147.47" stroke-width="1" zvalue="450"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@0" LinkObjectIDznd="171" MaxPinNum="2"/>
   </metadata>
  <path d="M 778.27 147.42 L 738.74 147.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv110" d="M 778.27 205.84 L 739.61 206.45" stroke-width="1" zvalue="451"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@0" LinkObjectIDznd="73" MaxPinNum="2"/>
   </metadata>
  <path d="M 778.27 205.84 L 739.61 206.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv110" d="M 778.27 262.11 L 738.74 262.11" stroke-width="1" zvalue="452"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="71" MaxPinNum="2"/>
   </metadata>
  <path d="M 778.27 262.11 L 738.74 262.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv110" d="M 697.86 136.79 L 697.86 147.48 L 738.74 147.48" stroke-width="1" zvalue="453"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="167@0" LinkObjectIDznd="171" MaxPinNum="2"/>
   </metadata>
  <path d="M 697.86 136.79 L 697.86 147.48 L 738.74 147.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="320">
   <path class="kv110" d="M 522.9 379.24 L 522.9 323.41" stroke-width="1" zvalue="485"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="323@1" LinkObjectIDznd="251@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 522.9 379.24 L 522.9 323.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="319">
   <path class="kv110" d="M 555 351.45 L 522.9 351.45" stroke-width="1" zvalue="486"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="321@0" LinkObjectIDznd="320" MaxPinNum="2"/>
   </metadata>
  <path d="M 555 351.45 L 522.9 351.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="318">
   <path class="kv110" d="M 547.5 442.62 L 522.87 442.62 L 522.87 403.08" stroke-width="1" zvalue="487"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="322@0" LinkObjectIDznd="323@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 547.5 442.62 L 522.87 442.62 L 522.87 403.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="317">
   <path class="kv110" d="M 523.55 462.94 L 523.55 442.38" stroke-width="1" zvalue="488"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="316@0" LinkObjectIDznd="318" MaxPinNum="2"/>
   </metadata>
  <path d="M 523.55 462.94 L 523.55 442.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="324">
   <path class="kv10" d="M 822.1 700.69 L 822.1 667.07" stroke-width="1" zvalue="491"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="282@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 822.1 700.69 L 822.1 667.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="325">
   <path class="kv10" d="M 822.1 598.77 L 822.1 634.59" stroke-width="1" zvalue="492"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@1" LinkObjectIDznd="282@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 822.1 598.77 L 822.1 634.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="377">
   <path class="kv10" d="M 987.58 515.16 L 987.58 484.89" stroke-width="1" zvalue="515"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="391@0" LinkObjectIDznd="392@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 987.58 515.16 L 987.58 484.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="376">
   <path class="kv10" d="M 986.22 460.56 L 986.22 411.98" stroke-width="1" zvalue="516"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="392@1" LinkObjectIDznd="405@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 986.22 460.56 L 986.22 411.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="406">
   <path class="kv10" d="M 987.67 674.71 L 987.67 700.69" stroke-width="1" zvalue="522"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="400@1" LinkObjectIDznd="53@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 987.67 674.71 L 987.67 700.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="408">
   <path class="kv10" d="M 1020.79 439.72 L 986.22 439.72" stroke-width="1" zvalue="523"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="404@0" LinkObjectIDznd="376" MaxPinNum="2"/>
   </metadata>
  <path d="M 1020.79 439.72 L 986.22 439.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="419">
   <path class="kv10" d="M 1120.91 512.94 L 1120.91 482.66" stroke-width="1" zvalue="535"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="421@0" LinkObjectIDznd="422@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1120.91 512.94 L 1120.91 482.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="415">
   <path class="kv10" d="M 1119.89 671.38 L 1119.89 700.69" stroke-width="1" zvalue="540"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="424@1" LinkObjectIDznd="53@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1119.89 671.38 L 1119.89 700.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="428">
   <path class="v6300" d="M 1575.98 703.5 L 1575.98 627.07" stroke-width="1" zvalue="544"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@3" LinkObjectIDznd="304@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1575.98 703.5 L 1575.98 627.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="429">
   <path class="v6300" d="M 1575.98 542.77 L 1575.98 594.59" stroke-width="1" zvalue="545"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="310@1" LinkObjectIDznd="304@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1575.98 542.77 L 1575.98 594.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="432">
   <path class="v6300" d="M 1612.03 574.26 L 1575.98 574.26" stroke-width="1" zvalue="551"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="954@0" LinkObjectIDznd="429" MaxPinNum="2"/>
   </metadata>
  <path d="M 1612.03 574.26 L 1575.98 574.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="438">
   <path class="v6300" d="M 1758.23 568.66 L 1758.23 617.68" stroke-width="1" zvalue="559"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="439@0" LinkObjectIDznd="441@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1758.23 568.66 L 1758.23 617.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="437">
   <path class="v6300" d="M 1757.68 654.33 L 1757.68 703.5" stroke-width="1" zvalue="560"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="441@1" LinkObjectIDznd="153@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1757.68 654.33 L 1757.68 703.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="457">
   <path class="v6300" d="M 1386.74 915.54 L 1386.75 901.71" stroke-width="1" zvalue="584"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="459@0" LinkObjectIDznd="458@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1386.74 915.54 L 1386.75 901.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="455">
   <path class="v6300" d="M 1476.3 797.47 L 1446.28 797.47" stroke-width="1" zvalue="586"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="456@0" LinkObjectIDznd="481" MaxPinNum="2"/>
   </metadata>
  <path d="M 1476.3 797.47 L 1446.28 797.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="475">
   <path class="kv10" d="M 1204.13 387.03 L 1171.03 387.03" stroke-width="1" zvalue="599"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="474@0" LinkObjectIDznd="473@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1204.13 387.03 L 1171.03 387.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="480">
   <path class="v6300" d="M 1447.09 727.92 L 1447.09 703.5" stroke-width="1" zvalue="605"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="478@0" LinkObjectIDznd="153@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1447.09 727.92 L 1447.09 703.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="481">
   <path class="v6300" d="M 1446.28 901.08 L 1446.28 760.41" stroke-width="1" zvalue="606"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="470@0" LinkObjectIDznd="478@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1446.28 901.08 L 1446.28 760.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="485">
   <path class="v6300" d="M 1327.14 773.59 L 1327.14 800.02" stroke-width="1" zvalue="612"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="483@0" LinkObjectIDznd="482@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1327.14 773.59 L 1327.14 800.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="489">
   <path class="v6300" d="M 1328.2 841.99 L 1328.2 901.01" stroke-width="1" zvalue="615"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="482@1" LinkObjectIDznd="484@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1328.2 841.99 L 1328.2 901.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="490">
   <path class="v6300" d="M 1328.2 853.71 L 1446.28 853.71" stroke-width="1" zvalue="616"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="489" LinkObjectIDznd="481" MaxPinNum="2"/>
   </metadata>
  <path d="M 1328.2 853.71 L 1446.28 853.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="491">
   <path class="v6300" d="M 1386.65 859.74 L 1386.65 853.71" stroke-width="1" zvalue="617"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="458@1" LinkObjectIDznd="490" MaxPinNum="2"/>
   </metadata>
  <path d="M 1386.65 859.74 L 1386.65 853.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="514">
   <path class="v6300" d="M 1657.45 919.11 L 1657.47 905.28" stroke-width="1" zvalue="625"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="516@0" LinkObjectIDznd="515@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1657.45 919.11 L 1657.47 905.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="512">
   <path class="v6300" d="M 1747.01 801.04 L 1716.99 801.04" stroke-width="1" zvalue="627"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="513@0" LinkObjectIDznd="509" MaxPinNum="2"/>
   </metadata>
  <path d="M 1747.01 801.04 L 1716.99 801.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="510">
   <path class="v6300" d="M 1717.8 731.49 L 1717.8 703.5" stroke-width="1" zvalue="630"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="511@0" LinkObjectIDznd="153@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1717.8 731.49 L 1717.8 703.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="509">
   <path class="v6300" d="M 1716.99 904.65 L 1716.99 763.98" stroke-width="1" zvalue="631"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="517@0" LinkObjectIDznd="511@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1716.99 904.65 L 1716.99 763.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="505">
   <path class="v6300" d="M 1597.86 777.16 L 1597.86 803.6" stroke-width="1" zvalue="638"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="507@0" LinkObjectIDznd="508@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1597.86 777.16 L 1597.86 803.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="504">
   <path class="v6300" d="M 1597.92 845.56 L 1597.84 904.58" stroke-width="1" zvalue="639"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="508@1" LinkObjectIDznd="506@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1597.92 845.56 L 1597.84 904.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="503">
   <path class="v6300" d="M 1597.9 857.29 L 1716.99 857.29" stroke-width="1" zvalue="640"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="504" LinkObjectIDznd="509" MaxPinNum="2"/>
   </metadata>
  <path d="M 1597.9 857.29 L 1716.99 857.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="502">
   <path class="v6300" d="M 1657.36 863.31 L 1657.36 857.29" stroke-width="1" zvalue="641"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="515@1" LinkObjectIDznd="503" MaxPinNum="2"/>
   </metadata>
  <path d="M 1657.36 863.31 L 1657.36 857.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="519">
   <path class="kv10" d="M 1033.93 742.12 L 1033.93 700.69" stroke-width="1" zvalue="642"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="53@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1033.93 742.12 L 1033.93 700.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="534">
   <path class="kv10" d="M 1033.93 790.22 L 1170.57 790.22 L 1170.57 815.46" stroke-width="1" zvalue="662"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108" LinkObjectIDznd="532@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1033.93 790.22 L 1170.57 790.22 L 1170.57 815.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="535">
   <path class="kv10" d="M 1170.57 852.11 L 1170.5 903.23" stroke-width="1" zvalue="663"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="532@1" LinkObjectIDznd="531@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1170.57 852.11 L 1170.5 903.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="536">
   <path class="kv10" d="M 1109.64 867.95 L 1109.64 905.81" stroke-width="1" zvalue="664"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="529@0" LinkObjectIDznd="530@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1109.64 867.95 L 1109.64 905.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="537">
   <path class="kv10" d="M 1109.54 825.98 L 1109.54 790.22" stroke-width="1" zvalue="665"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="529@1" LinkObjectIDznd="534" MaxPinNum="2"/>
   </metadata>
  <path d="M 1109.54 825.98 L 1109.54 790.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="538">
   <path class="kv10" d="M 1218.9 905.17 L 1218.9 876.89 L 1170.54 876.89" stroke-width="1" zvalue="666"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="533@0" LinkObjectIDznd="535" MaxPinNum="2"/>
   </metadata>
  <path d="M 1218.9 905.17 L 1218.9 876.89 L 1170.54 876.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="540">
   <path class="kv10" d="M 516.26 700.69 L 516.26 759.62" stroke-width="1" zvalue="667"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@5" LinkObjectIDznd="129@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 516.26 700.69 L 516.26 759.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="557">
   <path class="kv10" d="M 729.48 768.27 L 729.48 901.67" stroke-width="1" zvalue="673"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="558@1" LinkObjectIDznd="559@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 729.48 768.27 L 729.48 901.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="556">
   <path class="kv10" d="M 729.48 742.12 L 729.48 700.69" stroke-width="1" zvalue="674"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="558@0" LinkObjectIDznd="53@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 729.48 742.12 L 729.48 700.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="550">
   <path class="kv10" d="M 729.48 790.22 L 867.13 790.22 L 867.13 815.46" stroke-width="1" zvalue="683"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="557" LinkObjectIDznd="552@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 729.48 790.22 L 867.13 790.22 L 867.13 815.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="549">
   <path class="kv10" d="M 867.13 852.11 L 867.06 904.23" stroke-width="1" zvalue="684"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="552@1" LinkObjectIDznd="553@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 867.13 852.11 L 867.06 904.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="548">
   <path class="kv10" d="M 805.2 863.5 L 805.2 905.81" stroke-width="1" zvalue="685"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="554@0" LinkObjectIDznd="555@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 805.2 863.5 L 805.2 905.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="547">
   <path class="kv10" d="M 805.09 821.53 L 805.09 790.22" stroke-width="1" zvalue="686"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="554@1" LinkObjectIDznd="550" MaxPinNum="2"/>
   </metadata>
  <path d="M 805.09 821.53 L 805.09 790.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="546">
   <path class="kv10" d="M 914.46 905.17 L 914.46 876.89 L 867.09 876.89" stroke-width="1" zvalue="687"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="551@0" LinkObjectIDznd="549" MaxPinNum="2"/>
   </metadata>
  <path d="M 914.46 905.17 L 914.46 876.89 L 867.09 876.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="564">
   <path class="v6300" d="M 1339.54 563.73 L 1339.54 593.69" stroke-width="1" zvalue="695"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="560@0" LinkObjectIDznd="562@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1339.54 563.73 L 1339.54 593.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="565">
   <path class="v6300" d="M 1339.54 635.66 L 1339.54 661.33 L 1378.22 661.33 L 1378.22 703.5" stroke-width="1" zvalue="696"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="562@1" LinkObjectIDznd="153@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1339.54 635.66 L 1339.54 661.33 L 1378.22 661.33 L 1378.22 703.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="566">
   <path class="v6300" d="M 1421.52 563.58 L 1421.52 603.59" stroke-width="1" zvalue="697"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="561@0" LinkObjectIDznd="563@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1421.52 563.58 L 1421.52 603.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="567">
   <path class="v6300" d="M 1421.56 632.36 L 1421.56 662.44 L 1378.22 662.44" stroke-width="1" zvalue="698"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="563@1" LinkObjectIDznd="565" MaxPinNum="2"/>
   </metadata>
  <path d="M 1421.56 632.36 L 1421.56 662.44 L 1378.22 662.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="570">
   <path class="kv10" d="M 1577.97 405.4 L 1577.97 328 L 1119.55 328 L 1119.55 458.34" stroke-width="1" zvalue="699"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="314@0" LinkObjectIDznd="422@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1577.97 405.4 L 1577.97 328 L 1119.55 328 L 1119.55 458.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="571">
   <path class="kv10" d="M 1172.03 435.37 L 1119.55 435.37" stroke-width="1" zvalue="700"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="471@0" LinkObjectIDznd="570" MaxPinNum="2"/>
   </metadata>
  <path d="M 1172.03 435.37 L 1119.55 435.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="572">
   <path class="kv10" d="M 1139.15 388.13 L 1119.55 388.13" stroke-width="1" zvalue="701"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="473@1" LinkObjectIDznd="570" MaxPinNum="2"/>
   </metadata>
  <path d="M 1139.15 388.13 L 1119.55 388.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="573">
   <path class="kv10" d="M 1613.01 371.94 L 1577.97 371.94" stroke-width="1" zvalue="702"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="430@0" LinkObjectIDznd="570" MaxPinNum="2"/>
   </metadata>
  <path d="M 1613.01 371.94 L 1577.97 371.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv10" d="M 1119.89 638.89 L 1119.89 534.62" stroke-width="1" zvalue="722"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="424@0" LinkObjectIDznd="421@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1119.89 638.89 L 1119.89 534.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv10" d="M 987.71 536.84 L 987.67 642.22" stroke-width="1" zvalue="727"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="391@1" LinkObjectIDznd="400@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 987.71 536.84 L 987.67 642.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv10" d="M 988.75 591.23 L 987.69 591.23" stroke-width="1" zvalue="729"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="59" MaxPinNum="2"/>
   </metadata>
  <path d="M 988.75 591.23 L 987.69 591.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv10" d="M 1121.25 591.23 L 1119.89 591.23" stroke-width="1" zvalue="732"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="82" MaxPinNum="2"/>
   </metadata>
  <path d="M 1121.25 591.23 L 1119.89 591.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv110" d="M 738.74 275.39 L 738.74 246.54" stroke-width="1" zvalue="741"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@1" LinkObjectIDznd="69@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 738.74 275.39 L 738.74 246.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv110" d="M 739.61 223.25 L 739.61 185.08" stroke-width="1" zvalue="742"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="176@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 739.61 223.25 L 739.61 185.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv110" d="M 917.83 137.41 L 917.83 161.33" stroke-width="1" zvalue="743"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="206@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 917.83 137.41 L 917.83 161.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv110" d="M 917.8 186.9 L 917.86 227.67" stroke-width="1" zvalue="744"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@0" LinkObjectIDznd="223@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 917.8 186.9 L 917.86 227.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv110" d="M 917.99 250.46 L 917.99 277.21" stroke-width="1" zvalue="745"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@1" LinkObjectIDznd="216@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 917.99 250.46 L 917.99 277.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv110" d="M 917.8 302.78 L 917.8 323.41" stroke-width="1" zvalue="747"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="216@0" LinkObjectIDznd="251@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 917.8 302.78 L 917.8 323.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv110" d="M 876.95 138.61 L 876.95 156 L 917.83 156" stroke-width="1" zvalue="748"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="78" MaxPinNum="2"/>
   </metadata>
  <path d="M 876.95 138.61 L 876.95 156 L 917.83 156" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv110" d="M 957.36 149.24 L 917.83 149.24" stroke-width="1" zvalue="749"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="78" MaxPinNum="2"/>
   </metadata>
  <path d="M 957.36 149.24 L 917.83 149.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv110" d="M 957.36 207.66 L 917.83 207.66" stroke-width="1" zvalue="750"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@0" LinkObjectIDznd="79" MaxPinNum="2"/>
   </metadata>
  <path d="M 957.36 207.66 L 917.83 207.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv110" d="M 957.36 263.93 L 917.99 263.93" stroke-width="1" zvalue="751"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@0" LinkObjectIDznd="81" MaxPinNum="2"/>
   </metadata>
  <path d="M 957.36 263.93 L 917.99 263.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="205">
   <use class="kv10" height="30" transform="rotate(0,515.972,859.722) scale(1.69643,1.70833) translate(-202.07,-345.845)" width="28" x="492.2222222222221" xlink:href="#EnergyConsumer:站用变DY接地_0" y="834.0972222222222" zvalue="135"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449553432581" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,515.972,859.722) scale(1.69643,1.70833) translate(-202.07,-345.845)" width="28" x="492.2222222222221" y="834.0972222222222"/></g>
  <g id="439">
   <use class="v6300" height="30" transform="rotate(0,1758.03,546.847) scale(2.13676,-1.5142) translate(-919.363,-900.279)" width="28" x="1728.117860945539" xlink:href="#EnergyConsumer:站用变DY接地_0" y="524.1336524313602" zvalue="556"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449556381702" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1758.03,546.847) scale(2.13676,-1.5142) translate(-919.363,-900.279)" width="28" x="1728.117860945539" y="524.1336524313602"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="152">
   <use class="kv10" height="26" transform="rotate(0,540.222,648.319) scale(-1.38889,-1.56303) translate(-926.849,-1055.78)" width="12" x="531.8888888888888" xlink:href="#Disconnector:20210316_0" y="628" zvalue="155"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449558020101" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449558020101"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,540.222,648.319) scale(-1.38889,-1.56303) translate(-926.849,-1055.78)" width="12" x="531.8888888888888" y="628"/></g>
  <g id="268">
   <use class="kv110" height="30" transform="rotate(0,822.083,366.972) scale(1.11111,0.814815) translate(-81.375,80.625)" width="15" x="813.7500101725263" xlink:href="#Disconnector:刀闸_0" y="354.75" zvalue="266"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449553629190" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449553629190"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,822.083,366.972) scale(1.11111,0.814815) translate(-81.375,80.625)" width="15" x="813.7500101725263" y="354.75"/></g>
  <g id="74">
   <use class="kv110" height="30" transform="rotate(0,574.818,286.037) scale(-1.21805,-0.893239) translate(-1045.1,-607.862)" width="15" x="565.6829802483705" xlink:href="#Disconnector:刀闸_0" y="272.63814497331" zvalue="408"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449554219014" ObjectName="备用1101隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449554219014"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,574.818,286.037) scale(-1.21805,-0.893239) translate(-1045.1,-607.862)" width="15" x="565.6829802483705" y="272.63814497331"/></g>
  <g id="72">
   <use class="kv110" height="30" transform="rotate(0,574.717,167.642) scale(-1.21805,-0.893239) translate(-1044.91,-356.922)" width="15" x="565.5814100076105" xlink:href="#Disconnector:刀闸_0" y="154.2433777858847" zvalue="410"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449554153478" ObjectName="备用1106隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449554153478"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,574.717,167.642) scale(-1.21805,-0.893239) translate(-1044.91,-356.922)" width="15" x="565.5814100076105" y="154.2433777858847"/></g>
  <g id="177">
   <use class="kv110" height="30" transform="rotate(0,738.812,288.278) scale(-1.19217,-0.874257) translate(-1357.09,-619.905)" width="15" x="729.8707695311986" xlink:href="#Disconnector:刀闸_0" y="275.1641848790654" zvalue="432"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449554874374" ObjectName="110kV平钻线1321隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449554874374"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,738.812,288.278) scale(-1.19217,-0.874257) translate(-1357.09,-619.905)" width="15" x="729.8707695311986" y="275.1641848790654"/></g>
  <g id="176">
   <use class="kv110" height="30" transform="rotate(0,738.812,172.399) scale(-1.19217,-0.874257) translate(-1357.09,-371.48)" width="15" x="729.8707695596222" xlink:href="#Disconnector:刀闸_0" y="159.2853225508464" zvalue="434"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449554808838" ObjectName="110kV平钻线1326隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449554808838"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,738.812,172.399) scale(-1.19217,-0.874257) translate(-1357.09,-371.48)" width="15" x="729.8707695596222" y="159.2853225508464"/></g>
  <g id="216">
   <use class="kv110" height="30" transform="rotate(0,917.903,290.096) scale(-1.19217,-0.874257) translate(-1686.41,-623.803)" width="15" x="908.9616786221077" xlink:href="#Disconnector:刀闸_0" y="276.9823666972472" zvalue="457"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449555529733" ObjectName="110kV钻允线1331隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449555529733"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,917.903,290.096) scale(-1.19217,-0.874257) translate(-1686.41,-623.803)" width="15" x="908.9616786221077" y="276.9823666972472"/></g>
  <g id="206">
   <use class="kv110" height="30" transform="rotate(0,917.903,174.217) scale(-1.19217,-0.874257) translate(-1686.41,-375.378)" width="15" x="908.9616786505313" xlink:href="#Disconnector:刀闸_0" y="161.1035043690283" zvalue="459"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449555464197" ObjectName="110kV钻允线1336隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449555464197"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,917.903,174.217) scale(-1.19217,-0.874257) translate(-1686.41,-375.378)" width="15" x="908.9616786505313" y="161.1035043690283"/></g>
  <g id="323">
   <use class="kv110" height="30" transform="rotate(0,522.972,391.258) scale(-1.11111,-0.814815) translate(-992.814,-874.215)" width="15" x="514.6388888888888" xlink:href="#Disconnector:刀闸_0" y="379.0353672046854" zvalue="479"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449555922950" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449555922950"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,522.972,391.258) scale(-1.11111,-0.814815) translate(-992.814,-874.215)" width="15" x="514.6388888888888" y="379.0353672046854"/></g>
  <g id="392">
   <use class="kv10" height="30" transform="rotate(0,986.288,472.824) scale(-1.13409,-0.831665) translate(-1854.96,-1043.88)" width="15" x="977.7824552940281" xlink:href="#Disconnector:刀闸_0" y="460.3485782949178" zvalue="508"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449555988486" ObjectName="近区变10kV侧0351隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449555988486"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,986.288,472.824) scale(-1.13409,-0.831665) translate(-1854.96,-1043.88)" width="15" x="977.7824552940281" y="460.3485782949178"/></g>
  <g id="422">
   <use class="kv10" height="30" transform="rotate(0,1119.62,470.601) scale(-1.13409,-0.831665) translate(-2105.86,-1038.98)" width="15" x="1111.115788627361" xlink:href="#Disconnector:刀闸_0" y="458.1263560726957" zvalue="530"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449556185094" ObjectName="#1隔离变10kV侧0376隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449556185094"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1119.62,470.601) scale(-1.13409,-0.831665) translate(-2105.86,-1038.98)" width="15" x="1111.115788627361" y="458.1263560726957"/></g>
  <g id="441">
   <use class="v6300" height="36" transform="rotate(0,1757.68,636.006) scale(1.8902,1.07811) translate(-821.559,-44.6751)" width="14" x="1744.451960567313" xlink:href="#Disconnector:手车刀闸_0" y="616.5998795265215" zvalue="553"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449556447238" ObjectName="#2站用变6351隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449556447238"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1757.68,636.006) scale(1.8902,1.07811) translate(-821.559,-44.6751)" width="14" x="1744.451960567313" y="616.5998795265215"/></g>
  <g id="458">
   <use class="v6300" height="26" transform="rotate(0,1386.65,880.686) scale(1.24778,1.62088) translate(-273.872,-329.276)" width="12" x="1379.159840968177" xlink:href="#Disconnector:手车2021_0" y="859.6149543652718" zvalue="582"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449556578310" ObjectName="#3发电机6932隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449556578310"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1386.65,880.686) scale(1.24778,1.62088) translate(-273.872,-329.276)" width="12" x="1379.159840968177" y="859.6149543652718"/></g>
  <g id="473">
   <use class="kv10" height="30" transform="rotate(90,1154.47,388.028) scale(-1.25,1.25) translate(-2076.16,-73.8556)" width="15" x="1145.090277777778" xlink:href="#Disconnector:令克_0" y="369.2777777777778" zvalue="596"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449556840454" ObjectName="外接高河一级站#1隔离变隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449556840454"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1154.47,388.028) scale(-1.25,1.25) translate(-2076.16,-73.8556)" width="15" x="1145.090277777778" y="369.2777777777778"/></g>
  <g id="482">
   <use class="v6300" height="26" transform="rotate(0,1328.2,821.044) scale(1.24778,-1.62088) translate(-262.266,-1319.51)" width="12" x="1320.714652598568" xlink:href="#Disconnector:手车2021_0" y="799.9720972224147" zvalue="608"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449556971526" ObjectName="#3发电机6931隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449556971526"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1328.2,821.044) scale(1.24778,-1.62088) translate(-262.266,-1319.51)" width="12" x="1320.714652598568" y="799.9720972224147"/></g>
  <g id="515">
   <use class="v6300" height="26" transform="rotate(0,1657.36,884.258) scale(1.24778,1.62088) translate(-327.63,-330.644)" width="12" x="1649.874126682462" xlink:href="#Disconnector:手车2021_0" y="863.1863829367004" zvalue="623"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449557430278" ObjectName="#4发电机6942隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449557430278"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1657.36,884.258) scale(1.24778,1.62088) translate(-327.63,-330.644)" width="12" x="1649.874126682462" y="863.1863829367004"/></g>
  <g id="508">
   <use class="v6300" height="26" transform="rotate(0,1597.92,824.615) scale(1.24778,-1.62088) translate(-315.826,-1325.29)" width="12" x="1590.428938312853" xlink:href="#Disconnector:手车2021_0" y="803.5435257938434" zvalue="632"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449557299206" ObjectName="#4发电机6941隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449557299206"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1597.92,824.615) scale(1.24778,-1.62088) translate(-315.826,-1325.29)" width="12" x="1590.428938312853" y="803.5435257938434"/></g>
  <g id="529">
   <use class="kv10" height="26" transform="rotate(0,1109.54,846.929) scale(1.24778,1.62088) translate(-218.844,-316.345)" width="12" x="1102.048728526302" xlink:href="#Disconnector:手车2021_0" y="825.8571428571428" zvalue="652"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449557626886" ObjectName="#2发电机0921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449557626886"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1109.54,846.929) scale(1.24778,1.62088) translate(-218.844,-316.345)" width="12" x="1102.048728526302" y="825.8571428571428"/></g>
  <g id="532">
   <use class="kv10" height="36" transform="rotate(0,1170.57,833.784) scale(1.8902,1.07811) translate(-545.056,-59.0049)" width="14" x="1157.340855244109" xlink:href="#Disconnector:手车刀闸_0" y="814.3776573042992" zvalue="659"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449557823494" ObjectName="#2发电机0922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449557823494"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1170.57,833.784) scale(1.8902,1.07811) translate(-545.056,-59.0049)" width="14" x="1157.340855244109" y="814.3776573042992"/></g>
  <g id="554">
   <use class="kv10" height="26" transform="rotate(0,805.091,842.484) scale(1.24778,1.62088) translate(-158.388,-314.643)" width="12" x="797.6042840818576" xlink:href="#Disconnector:手车2021_0" y="821.4126984126983" zvalue="677"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449558282245" ObjectName="#1发电机0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449558282245"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,805.091,842.484) scale(1.24778,1.62088) translate(-158.388,-314.643)" width="12" x="797.6042840818576" y="821.4126984126983"/></g>
  <g id="552">
   <use class="kv10" height="36" transform="rotate(0,867.128,833.784) scale(1.8902,1.07811) translate(-402.147,-59.0049)" width="14" x="853.8964107996642" xlink:href="#Disconnector:手车刀闸_0" y="814.3776573042992" zvalue="680"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449558151173" ObjectName="#1发电机0912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449558151173"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,867.128,833.784) scale(1.8902,1.07811) translate(-402.147,-59.0049)" width="14" x="853.8964107996642" y="814.3776573042992"/></g>
  <g id="562">
   <use class="v6300" height="26" transform="rotate(0,1339.54,614.706) scale(1.24778,-1.62088) translate(-264.517,-985.877)" width="12" x="1332.04872847332" xlink:href="#Disconnector:手车2021_0" y="593.6349206349205" zvalue="693"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449558609925" ObjectName="6.3kV母线电压互感器6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449558609925"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1339.54,614.706) scale(1.24778,-1.62088) translate(-264.517,-985.877)" width="12" x="1332.04872847332" y="593.6349206349205"/></g>
  <g id="563">
   <use class="v6300" height="26" transform="rotate(0,1421.56,618) scale(1.11111,-1.11111) translate(-141.489,-1172.76)" width="12" x="1414.888875325521" xlink:href="#Disconnector:小车隔刀熔断器_0" y="603.5555555555555" zvalue="694"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449558675461" ObjectName="6.3kV母线电压互感器6801隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449558675461"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1421.56,618) scale(1.11111,-1.11111) translate(-141.489,-1172.76)" width="12" x="1414.888875325521" y="603.5555555555555"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="146">
   <use class="kv10" height="35" transform="rotate(0,537.412,592.778) scale(1.3,1.3) translate(-118.018,-131.545)" width="40" x="511.4124881074951" xlink:href="#Accessory:四卷带壁雷器母线PT_0" y="570.0277777777778" zvalue="163"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449557954566" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,537.412,592.778) scale(1.3,1.3) translate(-118.018,-131.545)" width="40" x="511.4124881074951" y="570.0277777777778"/></g>
  <g id="167">
   <use class="kv110" height="18" transform="rotate(0,700.186,121.837) scale(2.50356,-1.78825) translate(-409.233,-182.874)" width="15" x="681.4090909090909" xlink:href="#Accessory:PT8_0" y="105.7426515516824" zvalue="448"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449554284550" ObjectName="110kV平钻线PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,700.186,121.837) scale(2.50356,-1.78825) translate(-409.233,-182.874)" width="15" x="681.4090909090909" y="105.7426515516824"/></g>
  <g id="191">
   <use class="kv110" height="18" transform="rotate(0,879.277,123.655) scale(2.50356,-1.78825) translate(-516.789,-185.709)" width="15" x="860.5" xlink:href="#Accessory:PT8_0" y="107.5608333698642" zvalue="473"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449554939910" ObjectName="110kV钻允线PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,879.277,123.655) scale(2.50356,-1.78825) translate(-516.789,-185.709)" width="15" x="860.5" y="107.5608333698642"/></g>
  <g id="316">
   <use class="kv110" height="35" transform="rotate(0,520.909,487.25) scale(1.25,-1.42857) translate(-99.1818,-820.825)" width="40" x="495.909090909091" xlink:href="#Accessory:四卷带壁雷器母线PT_0" y="462.25" zvalue="489"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449555595270" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,520.909,487.25) scale(1.25,-1.42857) translate(-99.1818,-820.825)" width="40" x="495.909090909091" y="462.25"/></g>
  <g id="404">
   <use class="kv10" height="18" transform="rotate(90,1035.74,437.392) scale(-2.50356,-1.78825) translate(-1438.17,-674.89)" width="15" x="1016.964646464646" xlink:href="#Accessory:PT8_0" y="421.2982071072381" zvalue="520"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449556054022" ObjectName="至近区PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(90,1035.74,437.392) scale(-2.50356,-1.78825) translate(-1438.17,-674.89)" width="15" x="1016.964646464646" y="421.2982071072381"/></g>
  <g id="405">
   <use class="kv10" height="13" transform="rotate(0,986.219,404.667) scale(1.11111,-1.11111) translate(-98.0108,-768.144)" width="11" x="980.1076402187016" xlink:href="#Accessory:空挂线路_0" y="397.4444444444443" zvalue="521"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449556119558" ObjectName="至近区"/>
   </metadata>
  <rect fill="white" height="13" opacity="0" stroke="white" transform="rotate(0,986.219,404.667) scale(1.11111,-1.11111) translate(-98.0108,-768.144)" width="11" x="980.1076402187016" y="397.4444444444443"/></g>
  <g id="430">
   <use class="kv10" height="18" transform="rotate(90,1627.96,369.615) scale(-2.50356,-1.78825) translate(-2266.95,-569.211)" width="15" x="1609.186868686869" xlink:href="#Accessory:PT8_0" y="353.5204293294603" zvalue="547"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449556250630" ObjectName="#2主变10kV侧PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(90,1627.96,369.615) scale(-2.50356,-1.78825) translate(-2266.95,-569.211)" width="15" x="1609.186868686869" y="353.5204293294603"/></g>
  <g id="954">
   <use class="v6300" height="26" transform="rotate(270,1623.5,574.228) scale(-0.838049,0.927421) translate(-3561.71,43.9949)" width="12" x="1618.47262537081" xlink:href="#Accessory:避雷器1_0" y="562.1713389116339" zvalue="550"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449556316166" ObjectName="#2主变6.3kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1623.5,574.228) scale(-0.838049,0.927421) translate(-3561.71,43.9949)" width="12" x="1618.47262537081" y="562.1713389116339"/></g>
  <g id="459">
   <use class="v6300" height="18" transform="rotate(0,1386.74,934.502) scale(2.17114,2.17114) translate(-737.486,-493.542)" width="18" x="1367.2001794532" xlink:href="#Accessory:四卷PT_0" y="914.9613501132415" zvalue="580"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449556643846" ObjectName="#3发电机PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1386.74,934.502) scale(2.17114,2.17114) translate(-737.486,-493.542)" width="18" x="1367.2001794532" y="914.9613501132415"/></g>
  <g id="456">
   <use class="v6300" height="26" transform="rotate(90,1487.93,797.502) scale(-0.940366,-0.940366) translate(-3070.57,-1646.35)" width="12" x="1482.28427005122" xlink:href="#Accessory:避雷器1_0" y="785.2776686647898" zvalue="585"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449556512774" ObjectName="#3发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1487.93,797.502) scale(-0.940366,-0.940366) translate(-3070.57,-1646.35)" width="12" x="1482.28427005122" y="785.2776686647898"/></g>
  <g id="471">
   <use class="kv10" height="26" transform="rotate(270,1183.5,435.339) scale(-0.838049,0.927421) translate(-2596.68,33.1256)" width="12" x="1178.47262537081" xlink:href="#Accessory:避雷器1_0" y="423.2824500227451" zvalue="593"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449556774918" ObjectName="10kV1号隔离变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1183.5,435.339) scale(-0.838049,0.927421) translate(-2596.68,33.1256)" width="12" x="1178.47262537081" y="423.2824500227451"/></g>
  <g id="474">
   <use class="kv10" height="30" transform="rotate(270,1223.52,387.033) scale(1.3853,1.3853) translate(-334.521,-101.867)" width="30" x="1202.739784425384" xlink:href="#Accessory:带熔断器四卷PT_0" y="366.2540415317848" zvalue="598"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449556905990" ObjectName="#1隔离变PT4"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1223.52,387.033) scale(1.3853,1.3853) translate(-334.521,-101.867)" width="30" x="1202.739784425384" y="366.2540415317848"/></g>
  <g id="483">
   <use class="v6300" height="26" transform="rotate(0,1327.2,758) scale(1.20879,-1.20879) translate(-227.678,-1382.36)" width="15" x="1318.135420916336" xlink:href="#Accessory:20210316PT_0" y="742.2857142857142" zvalue="609"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449557037062" ObjectName="#3发电机励磁PT"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1327.2,758) scale(1.20879,-1.20879) translate(-227.678,-1382.36)" width="15" x="1318.135420916336" y="742.2857142857142"/></g>
  <g id="484">
   <use class="v6300" height="26" transform="rotate(0,1328.28,920.5) scale(1.51099,1.51099) translate(-445.368,-304.654)" width="15" x="1316.947467337965" xlink:href="#Accessory:20210316PT_0" y="900.8571428571427" zvalue="611"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449557102598" ObjectName="#3发电机励磁变"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1328.28,920.5) scale(1.51099,1.51099) translate(-445.368,-304.654)" width="15" x="1316.947467337965" y="900.8571428571427"/></g>
  <g id="516">
   <use class="v6300" height="18" transform="rotate(0,1657.45,938.073) scale(2.17114,2.17114) translate(-883.513,-495.469)" width="18" x="1637.914465167486" xlink:href="#Accessory:四卷PT_0" y="918.5327786846699" zvalue="621"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449557495814" ObjectName="#4发电机PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1657.45,938.073) scale(2.17114,2.17114) translate(-883.513,-495.469)" width="18" x="1637.914465167486" y="918.5327786846699"/></g>
  <g id="513">
   <use class="v6300" height="26" transform="rotate(90,1758.64,801.074) scale(-0.940366,-0.940366) translate(-3629.16,-1653.72)" width="12" x="1752.998555765505" xlink:href="#Accessory:避雷器1_0" y="788.8490972362183" zvalue="626"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449557364742" ObjectName="#4发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1758.64,801.074) scale(-0.940366,-0.940366) translate(-3629.16,-1653.72)" width="12" x="1752.998555765505" y="788.8490972362183"/></g>
  <g id="507">
   <use class="v6300" height="26" transform="rotate(0,1597.92,761.571) scale(1.20879,-1.20879) translate(-274.438,-1388.88)" width="15" x="1588.849706630622" xlink:href="#Accessory:20210316PT_0" y="745.8571428571429" zvalue="633"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449557233670" ObjectName="#4发电机励磁PT"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1597.92,761.571) scale(1.20879,-1.20879) translate(-274.438,-1388.88)" width="15" x="1588.849706630622" y="745.8571428571429"/></g>
  <g id="506">
   <use class="v6300" height="26" transform="rotate(0,1597.92,924.071) scale(1.51099,1.51099) translate(-536.554,-305.861)" width="15" x="1586.583223138537" xlink:href="#Accessory:20210316PT_0" y="904.4285714285712" zvalue="636"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449557168134" ObjectName="#4发电机励磁变"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1597.92,924.071) scale(1.51099,1.51099) translate(-536.554,-305.861)" width="15" x="1586.583223138537" y="904.4285714285712"/></g>
  <g id="530">
   <use class="kv10" height="18" transform="rotate(0,1109.54,924.767) scale(2.17114,2.17114) translate(-587.958,-488.291)" width="18" x="1089.995137687034" xlink:href="#Accessory:四卷PT_0" y="905.2266642154143" zvalue="650"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449557692422" ObjectName="#2发电机PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1109.54,924.767) scale(2.17114,2.17114) translate(-587.958,-488.291)" width="18" x="1089.995137687034" y="905.2266642154143"/></g>
  <g id="531">
   <use class="kv10" height="30" transform="rotate(0,1170.57,923.528) scale(1.3853,1.3853) translate(-319.795,-251.084)" width="30" x="1149.792805790125" xlink:href="#Accessory:PT789_0" y="902.7483620833392" zvalue="657"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449557757958" ObjectName="#2发电机PT4"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1170.57,923.528) scale(1.3853,1.3853) translate(-319.795,-251.084)" width="30" x="1149.792805790125" y="902.7483620833392"/></g>
  <g id="533">
   <use class="kv10" height="26" transform="rotate(0,1218.98,924.664) scale(1.51099,1.51099) translate(-408.404,-306.062)" width="15" x="1207.646715202029" xlink:href="#Accessory:20210316PT_0" y="905.0215364940582" zvalue="661"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449557889030" ObjectName="#2发电机励磁变"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1218.98,924.664) scale(1.51099,1.51099) translate(-408.404,-306.062)" width="15" x="1207.646715202029" y="905.0215364940582"/></g>
  <g id="555">
   <use class="kv10" height="18" transform="rotate(0,805.091,924.767) scale(2.17114,2.17114) translate(-423.736,-488.291)" width="18" x="785.5506932425892" xlink:href="#Accessory:四卷PT_0" y="905.2266642154145" zvalue="675"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449558347781" ObjectName="#1发电机PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,805.091,924.767) scale(2.17114,2.17114) translate(-423.736,-488.291)" width="18" x="785.5506932425892" y="905.2266642154145"/></g>
  <g id="553">
   <use class="kv10" height="30" transform="rotate(0,867.128,924.528) scale(1.3853,1.3853) translate(-235.397,-251.362)" width="30" x="846.3483613456809" xlink:href="#Accessory:PT789_0" y="903.7483620833391" zvalue="679"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449558216709" ObjectName="#1发电机PT4"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,867.128,924.528) scale(1.3853,1.3853) translate(-235.397,-251.362)" width="30" x="846.3483613456809" y="903.7483620833391"/></g>
  <g id="551">
   <use class="kv10" height="26" transform="rotate(0,914.535,924.664) scale(1.51099,1.51099) translate(-305.447,-306.062)" width="15" x="903.2022707575844" xlink:href="#Accessory:20210316PT_0" y="905.0215364940582" zvalue="682"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449558085637" ObjectName="#1发电机励磁变"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,914.535,924.664) scale(1.51099,1.51099) translate(-305.447,-306.062)" width="15" x="903.2022707575844" y="905.0215364940582"/></g>
  <g id="560">
   <use class="v6300" height="18" transform="rotate(0,1339.54,544.767) scale(2.17114,-2.17114) translate(-712.023,-785.139)" width="18" x="1319.995137691441" xlink:href="#Accessory:四卷PT_0" y="525.2266642154143" zvalue="689"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449558478853" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1339.54,544.767) scale(2.17114,-2.17114) translate(-712.023,-785.139)" width="18" x="1319.995137691441" y="525.2266642154143"/></g>
  <g id="561">
   <use class="v6300" height="26" transform="rotate(0,1421.56,551.947) scale(-0.940366,-0.940366) translate(-2933.62,-1139.67)" width="12" x="1415.913343141243" xlink:href="#Accessory:避雷器1_0" y="539.7221131092342" zvalue="691"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449558544389" ObjectName="6.3kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1421.56,551.947) scale(-0.940366,-0.940366) translate(-2933.62,-1139.67)" width="12" x="1415.913343141243" y="539.7221131092342"/></g>
  <g id="60">
   <use class="kv10" height="29" transform="rotate(0,988.75,591.75) scale(1.25,1.25) translate(-194,-114.725)" width="30" x="970" xlink:href="#Accessory:隔离变_0" y="573.625" zvalue="728"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449558806533" ObjectName="近区变"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,988.75,591.75) scale(1.25,1.25) translate(-194,-114.725)" width="30" x="970" y="573.625"/></g>
  <g id="62">
   <use class="kv10" height="29" transform="rotate(0,1121.25,591.75) scale(1.25,1.25) translate(-220.5,-114.725)" width="30" x="1102.5" xlink:href="#Accessory:隔离变_0" y="573.625" zvalue="731"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449558872069" ObjectName="#1隔离变"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1121.25,591.75) scale(1.25,1.25) translate(-220.5,-114.725)" width="30" x="1102.5" y="573.625"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="265">
   <use class="kv110" height="20" transform="rotate(270,865.194,402.75) scale(-1.11111,1.11111) translate(-1643.31,-39.1639)" width="10" x="859.6388990614149" xlink:href="#GroundDisconnector:地刀_0" y="391.6388888888889" zvalue="271"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449553563653" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449553563653"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,865.194,402.75) scale(-1.11111,1.11111) translate(-1643.31,-39.1639)" width="10" x="859.6388990614149" y="391.6388888888889"/></g>
  <g id="57">
   <use class="kv110" height="20" transform="rotate(270,626.91,259.24) scale(-1.21805,1.21805) translate(-1140.5,-44.2279)" width="10" x="620.8201138638492" xlink:href="#GroundDisconnector:地刀_0" y="247.0590169492426" zvalue="418"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449554022406" ObjectName="备用11017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449554022406"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,626.91,259.24) scale(-1.21805,1.21805) translate(-1140.5,-44.2279)" width="10" x="620.8201138638492" y="247.0590169492426"/></g>
  <g id="56">
   <use class="kv110" height="20" transform="rotate(270,631.456,196.293) scale(-1.21805,1.21805) translate(-1148.78,-32.9594)" width="10" x="625.3655684093037" xlink:href="#GroundDisconnector:地刀_0" y="184.1123695457404" zvalue="420"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449553891334" ObjectName="备用11060接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449553891334"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,631.456,196.293) scale(-1.21805,1.21805) translate(-1148.78,-32.9594)" width="10" x="625.3655684093037" y="184.1123695457404"/></g>
  <g id="55">
   <use class="kv110" height="20" transform="rotate(270,626.91,145.3) scale(-1.21805,1.21805) translate(-1140.5,-23.8308)" width="10" x="620.8201139800119" xlink:href="#GroundDisconnector:地刀_0" y="133.1199310232135" zvalue="422"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449553760262" ObjectName="备用11067接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449553760262"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,626.91,145.3) scale(-1.21805,1.21805) translate(-1140.5,-23.8308)" width="10" x="620.8201139800119" y="133.1199310232135"/></g>
  <g id="170">
   <use class="kv110" height="20" transform="rotate(270,789.896,262.05) scale(-1.19217,1.19217) translate(-1451.51,-40.319)" width="10" x="783.9356434675476" xlink:href="#GroundDisconnector:地刀_0" y="250.1286167945024" zvalue="442"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449554677766" ObjectName="110kV平钻线13217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449554677766"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,789.896,262.05) scale(-1.19217,1.19217) translate(-1451.51,-40.319)" width="10" x="783.9356434675476" y="250.1286167945024"/></g>
  <g id="169">
   <use class="kv110" height="20" transform="rotate(270,789.896,205.78) scale(-1.19217,1.19217) translate(-1451.51,-31.2486)" width="10" x="783.9356434675477" xlink:href="#GroundDisconnector:地刀_0" y="193.8582298089308" zvalue="444"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449554546694" ObjectName="110kV平钻线13260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449554546694"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,789.896,205.78) scale(-1.19217,1.19217) translate(-1451.51,-31.2486)" width="10" x="783.9356434675477" y="193.8582298089308"/></g>
  <g id="168">
   <use class="kv110" height="20" transform="rotate(270,789.896,147.364) scale(-1.19217,1.19217) translate(-1451.51,-21.8323)" width="10" x="783.9356435812417" xlink:href="#GroundDisconnector:地刀_0" y="135.4419382349261" zvalue="446"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449554415622" ObjectName="110kV平钻线13267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449554415622"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,789.896,147.364) scale(-1.19217,1.19217) translate(-1451.51,-21.8323)" width="10" x="783.9356435812417" y="135.4419382349261"/></g>
  <g id="194">
   <use class="kv110" height="20" transform="rotate(270,968.987,263.868) scale(-1.19217,1.19217) translate(-1780.82,-40.612)" width="10" x="963.0265525584566" xlink:href="#GroundDisconnector:地刀_0" y="251.9467986126843" zvalue="467"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449555333126" ObjectName="110kV钻允线13317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449555333126"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,968.987,263.868) scale(-1.19217,1.19217) translate(-1780.82,-40.612)" width="10" x="963.0265525584566" y="251.9467986126843"/></g>
  <g id="193">
   <use class="kv110" height="20" transform="rotate(270,968.987,207.598) scale(-1.19217,1.19217) translate(-1780.82,-31.5416)" width="10" x="963.0265525584567" xlink:href="#GroundDisconnector:地刀_0" y="195.6764116271127" zvalue="469"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449555202053" ObjectName="110kV钻允线13360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449555202053"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,968.987,207.598) scale(-1.19217,1.19217) translate(-1780.82,-31.5416)" width="10" x="963.0265525584567" y="195.6764116271127"/></g>
  <g id="192">
   <use class="kv110" height="20" transform="rotate(270,968.987,149.182) scale(-1.19217,1.19217) translate(-1780.82,-22.1254)" width="10" x="963.0265526721508" xlink:href="#GroundDisconnector:地刀_0" y="137.2601200531078" zvalue="471"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449555070981" ObjectName="110kV钻允线13367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449555070981"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,968.987,149.182) scale(-1.19217,1.19217) translate(-1780.82,-22.1254)" width="10" x="963.0265526721508" y="137.2601200531078"/></g>
  <g id="322">
   <use class="kv110" height="20" transform="rotate(270,558.333,442.563) scale(-1.11111,1.11111) translate(-1060.28,-43.1452)" width="10" x="552.7777845594617" xlink:href="#GroundDisconnector:地刀_0" y="431.4520099235303" zvalue="481"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449555857414" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449555857414"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,558.333,442.563) scale(-1.11111,1.11111) translate(-1060.28,-43.1452)" width="10" x="552.7777845594617" y="431.4520099235303"/></g>
  <g id="321">
   <use class="kv110" height="20" transform="rotate(270,565.833,351.396) scale(-1.11111,1.11111) translate(-1074.53,-34.0285)" width="10" x="560.2777845594617" xlink:href="#GroundDisconnector:地刀_0" y="340.2853535353535" zvalue="483"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449555726342" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449555726342"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,565.833,351.396) scale(-1.11111,1.11111) translate(-1074.53,-34.0285)" width="10" x="560.2777845594617" y="340.2853535353535"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="263">
   <g id="2630">
    <use class="kv110" height="50" transform="rotate(0,822.098,553.089) scale(2.01667,1.85355) translate(-399.196,-233.355)" width="30" x="791.85" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="506.75" zvalue="274"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874420846596" ObjectName="110"/>
    </metadata>
   </g>
   <g id="2631">
    <use class="kv10" height="50" transform="rotate(0,822.098,553.089) scale(2.01667,1.85355) translate(-399.196,-233.355)" width="30" x="791.85" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="506.75" zvalue="274"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874420912132" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399442628612" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399442628612"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,822.098,553.089) scale(2.01667,1.85355) translate(-399.196,-233.355)" width="30" x="791.85" y="506.75"/></g>
  <g id="310">
   <g id="3100">
    <use class="kv10" height="50" transform="rotate(0,1575.98,497.089) scale(2,1.85355) translate(-772.988,-207.568)" width="30" x="1545.98" xlink:href="#PowerTransformer2:586_0" y="450.75" zvalue="307"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874420977668" ObjectName="10"/>
    </metadata>
   </g>
   <g id="3101">
    <use class="v6300" height="50" transform="rotate(0,1575.98,497.089) scale(2,1.85355) translate(-772.988,-207.568)" width="30" x="1545.98" xlink:href="#PowerTransformer2:586_1" y="450.75" zvalue="307"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874421043204" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399442694148" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399442694148"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1575.98,497.089) scale(2,1.85355) translate(-772.988,-207.568)" width="30" x="1545.98" y="450.75"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="172">
   <use class="kv110" height="30" transform="rotate(0,738.739,111.983) scale(6.8124,1.58956) translate(-609.955,-32.6905)" width="7" x="714.8957360186514" xlink:href="#ACLineSegment:线路_0" y="88.13950803467759" zvalue="439"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249309446150" ObjectName="110kV平钻线"/>
   <cge:TPSR_Ref TObjectID="8444249309446150_5066549581971458"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,738.739,111.983) scale(6.8124,1.58956) translate(-609.955,-32.6905)" width="7" x="714.8957360186514" y="88.13950803467759"/></g>
  <g id="196">
   <use class="kv110" height="30" transform="rotate(0,917.83,113.801) scale(6.8124,1.58956) translate(-762.757,-33.3648)" width="7" x="893.9866451095605" xlink:href="#ACLineSegment:线路_0" y="89.95768985285946" zvalue="464"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249309380614" ObjectName="110kV钻允线"/>
   <cge:TPSR_Ref TObjectID="8444249309380614_5066549581971458"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,917.83,113.801) scale(6.8124,1.58956) translate(-762.757,-33.3648)" width="7" x="893.9866451095605" y="89.95768985285946"/></g>
 </g>
 <g id="GroundClass">
  <g id="1">
   <use class="kv110" height="18" transform="rotate(0,734.188,561) scale(0.53125,1) translate(645,0)" width="12" x="731" xlink:href="#Ground:大地_0" y="552" zvalue="703"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449558740997" ObjectName="#1主变接地"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,734.188,561) scale(0.53125,1) translate(645,0)" width="12" x="731" y="552"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="3" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,733.739,11.9131) scale(1,1) translate(0,0)" writing-mode="lr" x="733.27" xml:space="preserve" y="16.59" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124115185668" ObjectName="P"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="12" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,907.83,17.3222) scale(1,1) translate(0,-1.05595e-14)" writing-mode="lr" x="907.36" xml:space="preserve" y="22.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124116758532" ObjectName="P"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="14" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,733.739,33.4131) scale(1,1) translate(0,0)" writing-mode="lr" x="733.27" xml:space="preserve" y="38.11" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124115251204" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="15" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,907.83,35.2667) scale(1,1) translate(0,7.11777e-14)" writing-mode="lr" x="907.36" xml:space="preserve" y="39.92" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124116824068" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="32" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,733.739,52.9131) scale(1,1) translate(-1.52931e-13,0)" writing-mode="lr" x="733.27" xml:space="preserve" y="57.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124115316740" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="83">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="83" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,907.83,54.3222) scale(1,1) translate(0,1.6532e-13)" writing-mode="lr" x="907.36" xml:space="preserve" y="58.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124116889604" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="92" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,745.654,438.431) scale(1,1) translate(0,2.36339e-13)" writing-mode="lr" x="745.1" xml:space="preserve" y="443.13" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124107452420" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="93" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1507.12,382.875) scale(1,1) translate(0,4.10752e-14)" writing-mode="lr" x="1506.56" xml:space="preserve" y="387.58" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124111056903" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="95" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,745.654,457.486) scale(1,1) translate(0,1.48576e-13)" writing-mode="lr" x="745.1" xml:space="preserve" y="462.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124107517956" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="96" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1507.12,403.597) scale(1,1) translate(0,-1.73824e-13)" writing-mode="lr" x="1506.56" xml:space="preserve" y="408.29" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124111122438" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="97" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,742.321,611.491) scale(1,1) translate(1.5306e-13,2.66397e-13)" writing-mode="lr" x="741.77" xml:space="preserve" y="616.1799999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124107583492" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="99" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1512.67,595.491) scale(1,1) translate(0,1.29103e-13)" writing-mode="lr" x="1512.12" xml:space="preserve" y="600.21" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124111187972" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="100" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,742.321,633.325) scale(1,1) translate(0,-2.7518e-13)" writing-mode="lr" x="741.77" xml:space="preserve" y="638.04" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124107649028" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="101" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1512.67,615.769) scale(1,1) translate(0,0)" writing-mode="lr" x="1512.12" xml:space="preserve" y="620.46" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124111253508" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="102" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,745.654,479.319) scale(1,1) translate(0,5.17827e-14)" writing-mode="lr" x="745.1" xml:space="preserve" y="484.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124107714564" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="104" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1507.12,424.875) scale(1,1) translate(0,4.58368e-14)" writing-mode="lr" x="1506.56" xml:space="preserve" y="429.57" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124111319044" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="105" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,742.321,653.491) scale(1,1) translate(0,-1.41981e-13)" writing-mode="lr" x="741.77" xml:space="preserve" y="658.21" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124108042244" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="106">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="106" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1512.67,634.825) scale(1,1) translate(0,-2.76068e-13)" writing-mode="lr" x="1512.12" xml:space="preserve" y="639.53" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124111646724" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="107" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,966.141,866.029) scale(1,1) translate(0,0)" writing-mode="lr" x="965.59" xml:space="preserve" y="870.8099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124104634372" ObjectName="P"/>
   </metadata>
  </g>
  <g id="109">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="109" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1500.72,870.672) scale(1,1) translate(6.42917e-13,0)" writing-mode="lr" x="1500.17" xml:space="preserve" y="875.45" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124121280518" ObjectName="P"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="111" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1775.88,880.91) scale(1,1) translate(0,0)" writing-mode="lr" x="1775.33" xml:space="preserve" y="885.6900000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124122984452" ObjectName="P"/>
   </metadata>
  </g>
  <g id="112">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="112" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,641.697,878.252) scale(1,1) translate(0,0)" writing-mode="lr" x="641.14" xml:space="preserve" y="883.03" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124124164100" ObjectName="P"/>
   </metadata>
  </g>
  <g id="114">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="114" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,966.141,889.141) scale(1,1) translate(0,0)" writing-mode="lr" x="965.59" xml:space="preserve" y="893.92" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124104699908" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="116" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1500.72,892.116) scale(1,1) translate(6.42917e-13,0)" writing-mode="lr" x="1500.17" xml:space="preserve" y="896.89" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124121346052" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="117" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1775.88,902.354) scale(1,1) translate(0,0)" writing-mode="lr" x="1775.33" xml:space="preserve" y="907.13" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124123049988" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="118">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="118" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,641.697,898.585) scale(1,1) translate(0,0)" writing-mode="lr" x="641.14" xml:space="preserve" y="903.36" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124124229636" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="119">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="119" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,966.141,912.252) scale(1,1) translate(0,-1.39072e-12)" writing-mode="lr" x="965.59" xml:space="preserve" y="917.03" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124104765444" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="120">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="120" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1500.72,913.56) scale(1,1) translate(6.42917e-13,1.49224e-12)" writing-mode="lr" x="1500.17" xml:space="preserve" y="918.34" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124121411588" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="121">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="121" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1775.88,923.799) scale(1,1) translate(0,0)" writing-mode="lr" x="1775.33" xml:space="preserve" y="928.58" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124123115524" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="122">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="122" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,641.697,918.918) scale(1,1) translate(0,1.50116e-12)" writing-mode="lr" x="641.14" xml:space="preserve" y="923.7" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124124295172" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="164">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="164" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124107321348" ObjectName="F"/>
   </metadata>
  </g>
  <g id="163">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="163" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127232667652" ObjectName="钻水河电站上网有功"/>
   </metadata>
  </g>
  <g id="162">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="162" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124120625159" ObjectName="F"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="246" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.222,335.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="340.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124103979012" ObjectName="F"/>
   </metadata>
  </g>
  <g id="151">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="151" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124120428548" ObjectName="F"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="148" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124120494084" ObjectName="F"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124128882692" ObjectName="F"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124129013764" ObjectName="F"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="137" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127183908869" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="135">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="135" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127183843333" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="134" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,157.222,355.167) scale(1,1) translate(0,0)" writing-mode="lr" x="157.38" xml:space="preserve" y="360.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124106797060" ObjectName="F"/>
   </metadata>
  </g>
  <g id="155">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="155" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,399.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="404.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124128948228" ObjectName="F"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="17" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,466.5,584.186) scale(1,1) translate(0,-1.2583e-13)" writing-mode="lr" x="466.03" xml:space="preserve" y="588.96" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124103585796" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="18" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1359,448) scale(1,1) translate(0,0)" writing-mode="lr" x="1358.53" xml:space="preserve" y="452.78" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124106403844" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,453.364,431.909) scale(1,1) translate(-2.72025e-13,0)" writing-mode="lr" x="452.89" xml:space="preserve" y="436.69" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124106928132" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="20" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,466.5,609.186) scale(1,1) translate(0,0)" writing-mode="lr" x="466.03" xml:space="preserve" y="613.96" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124103651332" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="21" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1359,473) scale(1,1) translate(0,0)" writing-mode="lr" x="1358.53" xml:space="preserve" y="477.78" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124106469380" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="22" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,453.364,460.909) scale(1,1) translate(-2.72025e-13,0)" writing-mode="lr" x="452.89" xml:space="preserve" y="465.69" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124106993668" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="23" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,466.5,634.186) scale(1,1) translate(0,0)" writing-mode="lr" x="466.03" xml:space="preserve" y="638.96" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124103716868" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="24">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="24" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1359,494) scale(1,1) translate(0,0)" writing-mode="lr" x="1358.53" xml:space="preserve" y="498.78" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124106534916" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="25">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="25" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,453.364,485.909) scale(1,1) translate(-2.72025e-13,0)" writing-mode="lr" x="452.89" xml:space="preserve" y="490.69" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124107059204" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="26">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="26" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,447.5,700.686) scale(1,1) translate(0,0)" writing-mode="lr" x="447.03" xml:space="preserve" y="705.46" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124103847940" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="27" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1226,704.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1225.53" xml:space="preserve" y="709.28" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124106665988" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="28">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="28" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,453.364,341.409) scale(1,1) translate(-2.72025e-13,0)" writing-mode="lr" x="452.89" xml:space="preserve" y="346.19" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124107190276" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="218">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="218" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1528.75,936.957) scale(1,1) translate(0,0)" writing-mode="lr" x="1471.96" xml:space="preserve" y="941.3200000000001" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124121870340" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="219">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="219" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,654.582,948.156) scale(1,1) translate(0,0)" writing-mode="lr" x="601.39" xml:space="preserve" y="952.58" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124124753924" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="220">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="220" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,979.026,937.811) scale(1,1) translate(0,0)" writing-mode="lr" x="925.84" xml:space="preserve" y="942.23" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124105224196" ObjectName="Ua"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="161">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="812"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374884732932" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="160">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="813"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950065225732" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
</svg>