<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549586231298" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:避雷器带电容_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="5" y2="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.83333333333333" x2="18.83333333333334" y1="4.75" y2="4.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.83333333333333" x2="18.83333333333334" y1="20.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.83333333333333" x2="10.83333333333333" y1="17.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.83333333333333" x2="10.83333333333333" y1="23.08333333333334" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.583333333333334" x2="12.58333333333333" y1="24.25" y2="24.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.08333333333333" x2="12.08333333333333" y1="25.25" y2="25.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.083333333333334" x2="13.08333333333333" y1="23.25" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75" x2="18.75" y1="20.83333333333334" y2="14"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.83333333333334" x2="18.83333333333334" y1="4.75" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.58333333333334" x2="21.66666666666666" y1="12" y2="12"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.83,13.25) scale(1,1) translate(0,0)" width="6" x="7.83" y="8.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.58333333333334" x2="21.66666666666666" y1="14" y2="14"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.83333333333333" x2="11.83333333333333" y1="14.75" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.83333333333333" x2="10.83333333333333" y1="4.75" y2="14.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.833333333333334" x2="10.83333333333333" y1="13.75" y2="14.75"/>
  </symbol>
  <symbol id="Accessory:腊撒线路PT_0" viewBox="0,0,12,32">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.6666666666666661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="3" y1="27" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="27" y2="30"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="13.66666666666667" y2="1"/>
   <path d="M 6 15 L 3 20 L 9 20 L 6 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="9" y1="27" y2="25"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,8) scale(1,1) translate(0,0)" width="4" x="4" y="5"/>
   <ellipse cx="5.9" cy="18.6" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.9" cy="26.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变带熔断器YY_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0235180220435" x2="10.0235180220435" y1="0.5833333333333091" y2="9.290410445610181"/>
   <rect fill-opacity="0" height="4.32" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.06,5.25) scale(1,1) translate(0,0)" width="3.43" x="8.34" y="3.09"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="29.16666666666667" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="10.8868007916835" y2="12.55855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="14.23031840279781" y2="12.55855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV盾中电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="41" xlink:href="logo.png" y="46.67"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,175.625,76.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="175.63" xml:space="preserve" y="80.17" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,177.333,76.357) scale(1,1) translate(6.55032e-15,0)" writing-mode="lr" x="177.33" xml:space="preserve" y="85.36" zvalue="3">10kV盾中电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="6" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,63.4375,362) scale(1,1) translate(0,0)" width="72.88" x="27" y="350" zvalue="105"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.4375,362) scale(1,1) translate(0,0)" writing-mode="lr" x="63.44" xml:space="preserve" y="366.5" zvalue="105">信号一览</text>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="375.0000000000001" x2="375.0000000000001" y1="14.66666666666674" y2="1044.666666666667" zvalue="4"/>
  <line fill="none" id="28" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.410605131648481e-13" x2="367.9999999999999" y1="150.5371592807491" y2="150.5371592807491" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="162.6666666666668" y2="162.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="2.000000000000114" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="162.6666666666668" y2="162.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.0000000000001" x2="364.0000000000001" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="2.000000000000114" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.0000000000001" x2="364.0000000000001" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="2.000000000000114" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.0000000000001" x2="364.0000000000001" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="2.000000000000114" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.0000000000001" x2="364.0000000000001" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="183.0000000000001" y1="281.1666666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.000000000000114" x2="2.000000000000114" y1="258.4166666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="258.4166666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="364.0000000000001" y1="281.1666666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="258.4166666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.0000000000001" x2="364.0000000000001" y1="258.4166666666668" y2="281.1666666666668"/>
  <line fill="none" id="26" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.410605131648481e-13" x2="367.9999999999999" y1="620.5371592807492" y2="620.5371592807492" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="91.00000000000011" y1="935.666666666667" y2="935.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="91.00000000000011" y1="974.829966666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="1.000000000000114" y1="935.666666666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="91.00000000000011" y1="935.666666666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="361.0000000000001" y1="935.666666666667" y2="935.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="361.0000000000001" y1="974.829966666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="91.00000000000011" y1="935.666666666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.0000000000001" x2="361.0000000000001" y1="935.666666666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="91.00000000000011" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="91.00000000000011" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="1.000000000000114" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="91.00000000000011" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="181.0000000000001" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="181.0000000000001" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="91.00000000000011" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000001" x2="181.0000000000001" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000002" x2="271.0000000000002" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000002" x2="271.0000000000002" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000002" x2="181.0000000000002" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000002" x2="271.0000000000002" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="361.0000000000001" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="361.0000000000001" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="271.0000000000001" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.0000000000001" x2="361.0000000000001" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="91.00000000000011" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="91.00000000000011" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1.000000000000114" x2="1.000000000000114" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="91.00000000000011" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="181.0000000000001" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="181.0000000000001" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91.00000000000011" x2="91.00000000000011" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000001" x2="181.0000000000001" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000002" x2="271.0000000000002" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000002" x2="271.0000000000002" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000002" x2="181.0000000000002" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000002" x2="271.0000000000002" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="361.0000000000001" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="361.0000000000001" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="271.0000000000001" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.0000000000001" x2="361.0000000000001" y1="1002.748266666667" y2="1030.666666666667"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46,955.667) scale(1,1) translate(0,0)" writing-mode="lr" x="46" xml:space="preserve" y="961.67" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43,989.667) scale(1,1) translate(0,0)" writing-mode="lr" x="43" xml:space="preserve" y="995.67" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225,989.667) scale(1,1) translate(0,0)" writing-mode="lr" x="225" xml:space="preserve" y="995.67" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,42,1017.67) scale(1,1) translate(0,0)" writing-mode="lr" x="42" xml:space="preserve" y="1023.67" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224,1017.67) scale(1,1) translate(0,0)" writing-mode="lr" x="224" xml:space="preserve" y="1023.67" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,66.5,650.167) scale(1,1) translate(0,0)" writing-mode="lr" x="66.50000000000011" xml:space="preserve" y="654.6666666666667" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.054,957.667) scale(1,1) translate(0,0)" writing-mode="lr" x="226.05" xml:space="preserve" y="963.67" zvalue="26">DunZhong-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,136.054,1017.67) scale(1,1) translate(0,0)" writing-mode="lr" x="136.05" xml:space="preserve" y="1023.67" zvalue="27">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,40,176.667) scale(1,1) translate(0,0)" writing-mode="lr" x="40" xml:space="preserve" y="182.17" zvalue="29">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,220,176.667) scale(1,1) translate(0,0)" writing-mode="lr" x="220" xml:space="preserve" y="182.17" zvalue="30">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.1875,248.667) scale(1,1) translate(0,0)" writing-mode="lr" x="47.19" xml:space="preserve" y="253.17" zvalue="31">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.083,203.361) scale(1,1) translate(0,0)" writing-mode="lr" x="232.08" xml:space="preserve" y="207.86" zvalue="32">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,501,632.75) scale(1,1) translate(0,0)" writing-mode="lr" x="501" xml:space="preserve" y="637.25" zvalue="35">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" x="655.46875" xml:space="preserve" y="1009.520088672638" zvalue="37">#1发电机         </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="655.46875" xml:space="preserve" y="1025.520088672638" zvalue="37">500KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,684.929,813.5) scale(1,1) translate(7.43691e-14,8.9024e-14)" writing-mode="lr" x="684.9299999999999" xml:space="preserve" y="818" zvalue="39">631</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,677.841,728.429) scale(1,1) translate(0,0)" writing-mode="lr" x="677.84" xml:space="preserve" y="732.9299999999999" zvalue="42">6311</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" x="1513.359375" xml:space="preserve" y="1007.516119805593" zvalue="46">#2发电机         </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1513.359375" xml:space="preserve" y="1023.516119805593" zvalue="46">500KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1530.81,812.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1530.81" xml:space="preserve" y="816.64" zvalue="47">632</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1529.35,735.476) scale(1,1) translate(0,0)" writing-mode="lr" x="1529.35" xml:space="preserve" y="739.98" zvalue="49">6321</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" x="948.25" xml:space="preserve" y="335.2418154761905" zvalue="52">#1主变        </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="948.25" xml:space="preserve" y="351.2418154761905" zvalue="52">1600KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,982.286,438.429) scale(1,1) translate(0,0)" writing-mode="lr" x="982.29" xml:space="preserve" y="442.93" zvalue="55">6016</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,983.714,555.571) scale(1,1) translate(1.0766e-13,0)" writing-mode="lr" x="983.71" xml:space="preserve" y="560.0700000000001" zvalue="59">6011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1044.14,490.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1044.14" xml:space="preserve" y="495.07" zvalue="63">601</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1020.48,62.3492) scale(1,1) translate(0,0)" writing-mode="lr" x="1020.48" xml:space="preserve" y="66.84999999999999" zvalue="66">10kV盾中联络线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1049.57,208.857) scale(1,1) translate(-2.29467e-13,5.76491e-13)" writing-mode="lr" x="1049.57" xml:space="preserve" y="213.36" zvalue="69">031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,862.198,695.571) scale(1,1) translate(0,0)" writing-mode="lr" x="862.2" xml:space="preserve" y="700.0700000000001" zvalue="75">6901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1173.46,697.357) scale(1,1) translate(8.96881e-13,0)" writing-mode="lr" x="1173.46" xml:space="preserve" y="701.86" zvalue="83">6811</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1672.91,691.603) scale(1,1) translate(0,0)" writing-mode="lr" x="1672.91" xml:space="preserve" y="696.1" zvalue="92">6331</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,179.211,362.591) scale(1,1) translate(0,0)" writing-mode="lr" x="179.21" xml:space="preserve" y="367.09" zvalue="101">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,284.211,362.591) scale(1,1) translate(0,0)" writing-mode="lr" x="284.21" xml:space="preserve" y="367.09" zvalue="102">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="27" y="350" zvalue="105"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="33">
   <path class="v6300" d="M 538.57 640.86 L 1698.57 640.86" stroke-width="6" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674242789381" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674242789381"/></metadata>
  <path d="M 538.57 640.86 L 1698.57 640.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="36">
   <use class="v6300" height="30" transform="rotate(0,653.214,944.071) scale(2.83333,2.83333) translate(-395.168,-583.37)" width="30" x="610.7142857142858" xlink:href="#Generator:发电机_0" y="901.5714285714287" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449794015238" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449794015238"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,653.214,944.071) scale(2.83333,2.83333) translate(-395.168,-583.37)" width="30" x="610.7142857142858" y="901.5714285714287"/></g>
  <g id="49">
   <use class="v6300" height="30" transform="rotate(0,1506.79,944.929) scale(2.83333,2.83333) translate(-947.479,-583.924)" width="30" x="1464.285714285714" xlink:href="#Generator:发电机_0" y="902.4285714285714" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449794211846" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449794211846"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1506.79,944.929) scale(2.83333,2.83333) translate(-947.479,-583.924)" width="30" x="1464.285714285714" y="902.4285714285714"/></g>
 </g>
 <g id="BreakerClass">
  <g id="38">
   <use class="v6300" height="20" transform="rotate(0,654.286,810.857) scale(2.14286,1.92857) translate(-343.238,-381.127)" width="10" x="643.5714285714284" xlink:href="#Breaker:开关_0" y="791.5714285714284" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924509761541" ObjectName="#1发电机631断路器"/>
   <cge:TPSR_Ref TObjectID="6473924509761541"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,654.286,810.857) scale(2.14286,1.92857) translate(-343.238,-381.127)" width="10" x="643.5714285714284" y="791.5714285714284"/></g>
  <g id="48">
   <use class="v6300" height="20" transform="rotate(0,1506.43,813.143) scale(2.14286,1.92857) translate(-797.714,-382.228)" width="10" x="1495.714285714286" xlink:href="#Breaker:开关_0" y="793.8571428571429" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924509827077" ObjectName="#2发电机632断路器"/>
   <cge:TPSR_Ref TObjectID="6473924509827077"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1506.43,813.143) scale(2.14286,1.92857) translate(-797.714,-382.228)" width="10" x="1495.714285714286" y="793.8571428571429"/></g>
  <g id="58">
   <use class="v6300" height="20" transform="rotate(0,1016.43,491.571) scale(2.14286,1.92857) translate(-536.381,-227.397)" width="10" x="1005.714285714286" xlink:href="#Breaker:开关_0" y="472.2857142857143" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924509892613" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924509892613"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1016.43,491.571) scale(2.14286,1.92857) translate(-536.381,-227.397)" width="10" x="1005.714285714286" y="472.2857142857143"/></g>
  <g id="63">
   <use class="kv10" height="20" transform="rotate(0,1016.43,208.714) scale(2.14286,1.92857) translate(-536.381,-91.2063)" width="10" x="1005.714285714286" xlink:href="#Breaker:开关_0" y="189.4285714285714" zvalue="68"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924509958149" ObjectName="#1主变10kV侧031断路器"/>
   <cge:TPSR_Ref TObjectID="6473924509958149"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1016.43,208.714) scale(2.14286,1.92857) translate(-536.381,-91.2063)" width="10" x="1005.714285714286" y="189.4285714285714"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="39">
   <path class="v6300" d="M 653.21 902.28 L 653.21 829.28" stroke-width="1" zvalue="39"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="38@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 653.21 902.28 L 653.21 829.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="v6300" d="M 654.21 792.41 L 654.21 746.3" stroke-width="1" zvalue="42"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="41@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 654.21 792.41 L 654.21 746.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="v6300" d="M 652.82 715.66 L 652.82 640.86" stroke-width="1" zvalue="43"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="33@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 652.82 715.66 L 652.82 640.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="v6300" d="M 1506.79 903.14 L 1506.79 831.56" stroke-width="1" zvalue="47"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1506.79 903.14 L 1506.79 831.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="v6300" d="M 1506.36 794.69 L 1506.36 748.59" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@0" LinkObjectIDznd="46@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1506.36 794.69 L 1506.36 748.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="v6300" d="M 1504.97 717.95 L 1504.97 640.86" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="33@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1504.97 717.95 L 1504.97 640.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="v6300" d="M 1017.71 391.23 L 1017.71 424.23" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@1" LinkObjectIDznd="52@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1017.71 391.23 L 1017.71 424.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="v6300" d="M 1016.52 572.02 L 1016.52 640.86" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@1" LinkObjectIDznd="33@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1016.52 572.02 L 1016.52 640.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="v6300" d="M 1016.52 454.87 L 1016.52 473.12" stroke-width="1" zvalue="63"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@1" LinkObjectIDznd="58@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1016.52 454.87 L 1016.52 473.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="v6300" d="M 1016.57 509.99 L 1016.55 541.38" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@1" LinkObjectIDznd="55@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1016.57 509.99 L 1016.55 541.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv10" d="M 1017.14 140.16 L 1017.14 190.26" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="63@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1017.14 140.16 L 1017.14 190.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv10" d="M 1016.57 227.13 L 1016.57 276.91" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@1" LinkObjectIDznd="50@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1016.57 227.13 L 1016.57 276.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="v6300" d="M 895.75 681.38 L 895.75 640.86" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="33@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 895.75 681.38 L 895.75 640.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="v6300" d="M 1206.61 684.81 L 1206.61 640.86" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="33@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1206.61 684.81 L 1206.61 640.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="v6300" d="M 1650.71 746.14 L 1650.71 708.05" stroke-width="1" zvalue="92"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@0" LinkObjectIDznd="83@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1650.71 746.14 L 1650.71 708.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="v6300" d="M 1650.75 677.41 L 1650.75 640.86" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@0" LinkObjectIDznd="33@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1650.75 677.41 L 1650.75 640.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="v6300" d="M 894.81 764.65 L 894.81 712.02" stroke-width="1" zvalue="96"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="68@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 894.81 764.65 L 894.81 712.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="v6300" d="M 1208 765.57 L 1208 715.45" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="78@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1208 765.57 L 1208 715.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="41">
   <use class="v6300" height="30" transform="rotate(0,652.698,730.857) scale(1.42857,1.04762) translate(-192.595,-32.5065)" width="15" x="641.984050822706" xlink:href="#Disconnector:刀闸_0" y="715.1428571428571" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449794080774" ObjectName="#1发电机6311隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449794080774"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,652.698,730.857) scale(1.42857,1.04762) translate(-192.595,-32.5065)" width="15" x="641.984050822706" y="715.1428571428571"/></g>
  <g id="46">
   <use class="v6300" height="30" transform="rotate(0,1504.84,733.143) scale(1.42857,1.04762) translate(-448.238,-32.6104)" width="15" x="1494.126907965563" xlink:href="#Disconnector:刀闸_0" y="717.4285714285713" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449794146310" ObjectName="#2发电机6321隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449794146310"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1504.84,733.143) scale(1.42857,1.04762) translate(-448.238,-32.6104)" width="15" x="1494.126907965563" y="717.4285714285713"/></g>
  <g id="52">
   <use class="v6300" height="30" transform="rotate(0,1016.43,439.429) scale(1.42857,1.04762) translate(-301.714,-19.2597)" width="15" x="1005.714285714286" xlink:href="#Disconnector:刀闸_0" y="423.7142857142857" zvalue="54"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449794277382" ObjectName="#1主变6.3kV侧6016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449794277382"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1016.43,439.429) scale(1.42857,1.04762) translate(-301.714,-19.2597)" width="15" x="1005.714285714286" y="423.7142857142857"/></g>
  <g id="55">
   <use class="v6300" height="30" transform="rotate(0,1016.43,556.571) scale(1.42857,1.04762) translate(-301.714,-24.5844)" width="15" x="1005.714285714286" xlink:href="#Disconnector:刀闸_0" y="540.8571428571429" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449794342918" ObjectName="#1主变6.3kV侧6011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449794342918"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1016.43,556.571) scale(1.42857,1.04762) translate(-301.714,-24.5844)" width="15" x="1005.714285714286" y="540.8571428571429"/></g>
  <g id="68">
   <use class="v6300" height="30" transform="rotate(0,895.627,696.571) scale(1.42857,1.04762) translate(-265.474,-30.9481)" width="15" x="884.9126222512774" xlink:href="#Disconnector:刀闸_0" y="680.8571428571428" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449794473990" ObjectName="#1站用变6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449794473990"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,895.627,696.571) scale(1.42857,1.04762) translate(-265.474,-30.9481)" width="15" x="884.9126222512774" y="680.8571428571428"/></g>
  <g id="78">
   <use class="v6300" height="30" transform="rotate(0,1206.48,700) scale(1.42857,1.04762) translate(-358.731,-31.1039)" width="15" x="1195.76976510842" xlink:href="#Disconnector:刀闸_0" y="684.2857142857142" zvalue="82"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449794539526" ObjectName="#2站用变6811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449794539526"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1206.48,700) scale(1.42857,1.04762) translate(-358.731,-31.1039)" width="15" x="1195.76976510842" y="684.2857142857142"/></g>
  <g id="83">
   <use class="v6300" height="30" transform="rotate(0,1650.63,692.603) scale(1.42857,1.04762) translate(-491.974,-30.7677)" width="15" x="1639.912622251277" xlink:href="#Disconnector:刀闸_0" y="676.8888888888889" zvalue="91"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449794670597" ObjectName="6.3kV母线电压互感器6331隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449794670597"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1650.63,692.603) scale(1.42857,1.04762) translate(-491.974,-30.7677)" width="15" x="1639.912622251277" y="676.8888888888889"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="50">
   <g id="500">
    <use class="kv10" height="60" transform="rotate(0,1017.71,333.952) scale(1.74286,1.93651) translate(-418.923,-133.406)" width="40" x="982.86" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="275.86" zvalue="51"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874435723268" ObjectName="10"/>
    </metadata>
   </g>
   <g id="501">
    <use class="v6300" height="60" transform="rotate(0,1017.71,333.952) scale(1.74286,1.93651) translate(-418.923,-133.406)" width="40" x="982.86" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="275.86" zvalue="51"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874435788804" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399450099716" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399450099716"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1017.71,333.952) scale(1.74286,1.93651) translate(-418.923,-133.406)" width="40" x="982.86" y="275.86"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="81">
   <use class="v6300" height="30" transform="rotate(0,1650.71,778.714) scale(2.71429,2.71429) translate(-1016.84,-466.105)" width="30" x="1610" xlink:href="#Accessory:避雷器带电容_0" y="738" zvalue="88"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449794605061" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1650.71,778.714) scale(2.71429,2.71429) translate(-1016.84,-466.105)" width="30" x="1610" y="738"/></g>
  <g id="35">
   <use class="v6300" height="32" transform="rotate(0,894.812,802.5) scale(2.46875,2.46875) translate(-523.544,-453.937)" width="12" x="880" xlink:href="#Accessory:腊撒线路PT_0" y="763" zvalue="94"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449794736133" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,894.812,802.5) scale(2.46875,2.46875) translate(-523.544,-453.937)" width="12" x="880" y="763"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="51">
   <use class="v6300" height="30" transform="rotate(0,1208,800) scale(2.45,2.43333) translate(-700.439,-449.733)" width="20" x="1183.5" xlink:href="#EnergyConsumer:站用变带熔断器YY_0" y="763.5" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449794801669" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1208,800) scale(2.45,2.43333) translate(-700.439,-449.733)" width="20" x="1183.5" y="763.5"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,311.485,363.107) scale(0.708333,0.665547) translate(123.884,177.453)" width="30" x="300.86" xlink:href="#State:红绿圆(方形)_0" y="353.12" zvalue="103"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,311.485,363.107) scale(0.708333,0.665547) translate(123.884,177.453)" width="30" x="300.86" y="353.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,215.86,363.107) scale(0.708333,0.665547) translate(84.5086,177.453)" width="30" x="205.24" xlink:href="#State:红绿圆(方形)_0" y="353.12" zvalue="104"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,215.86,363.107) scale(0.708333,0.665547) translate(84.5086,177.453)" width="30" x="205.24" y="353.12"/></g>
 </g>
</svg>