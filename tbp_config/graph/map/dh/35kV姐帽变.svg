<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549679030273" height="1045" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1045" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1734.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2314.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_0" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="3" y1="19" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="4.75" y2="8.75"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="18.75" y2="21.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_1" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="2" y2="24"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_2" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.08333333333333" x2="3.995614035087721" y1="9.083333333333332" y2="17.19627192982456"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.717927631578948" x2="10.16666666666667" y1="9.172423245614038" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="2" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="21.25" y2="24.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Breaker:小车母联_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="17.58333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车母联_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="0.8333333333333304" y2="5.749999999999999"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车母联_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="14" y2="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.666666666666666" x2="7.25" y1="5.833333333333333" y2="14.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Compensator:35kV并联电容电抗器111_0" viewBox="0,0,70,40">
   <use terminal-index="0" type="0" x="3.499999699592589" xlink:href="#terminal" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="45.85000010347366" x2="45.85000010347366" y1="17.89999997997284" y2="22.10000002002716"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="47.25000011682511" x2="47.25000011682511" y1="17.89999997997284" y2="22.10000002002716"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="67.55000031042098" x2="67.55000031042098" y1="9.033333228747058" y2="31.17773178018145"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="64.75000028371811" x2="67.55000031042098" y1="9.033333228747054" y2="9.033333228747054"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="43.40000008010864" x2="59.85000023698807" y1="36.45000015687943" y2="36.45000015687943"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="58.83888911623426" x2="61.97592618318841" y1="30.2018519491443" y2="30.2018519491443"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="57.05000021028519" x2="61.97592618318841" y1="20" y2="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="61.95000025701523" x2="61.95000025701523" y1="20" y2="30.16296305988453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="47.25000011682511" x2="55.65000019693375" y1="20" y2="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="43.75000008344651" x2="40.6648148688387" y1="30.15000009679795" y2="30.15000009679795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="51.45000015687943" x2="51.45000015687943" y1="20" y2="34.20740754289981"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="40.60000005340576" x2="40.60000005340576" y1="30.11111120753818" y2="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="67.55000031042098" x2="64.75000028371811" y1="31.17773178018145" y2="31.17773178018145"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="40.50925931179965" x2="45.85000010347366" y1="20" y2="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="61.95000025701523" x2="67.55000031042098" y1="20" y2="20"/>
   <path d="M 48.7884 30.15 A 4.14948 2.54445 -90 0 1 43.6995 30.15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 53.8773 30.15 A 4.14948 2.54445 -90 0 1 48.7884 30.15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 58.8262 30.15 A 4.14948 2.54445 -90 0 1 53.7373 30.15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.65000019693375" x2="55.65000019693375" y1="17.89999997997284" y2="22.10000002002716"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="57.05000021028519" x2="57.05000021028519" y1="17.89999997997284" y2="22.10000002002716"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.916666360696155" x2="8.924999751329416" y1="19.96499999966622" y2="19.96499999966622"/>
   <path d="M 15.8317 26.5917 A 6.88333 6.65 -180 1 0 8.94833 19.9417" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.8083 26.4983 L 15.925 19.965 L 40.8333 19.965" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:间隔模板_0" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(85,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="State:间隔模板_1" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(185,185,185)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.583333333333332" y2="1.050000000000001"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="26.41666666666667" y2="34.83333333333334"/>
   <rect fill-opacity="0" height="17" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,18) scale(1,1) translate(0,0)" width="8" x="3" y="9.5"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="35" y2="1.166666666666664"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <rect fill-opacity="0" height="17" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,18) scale(1,1) translate(0,0)" width="8" x="3" y="9.5"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="9.999999999999995" y2="1.299999999999994"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV姐帽变" InitShowingPlane="" fill="rgb(0,0,0)" height="1045" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="54.06" xlink:href="logo.png" y="22.93"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,178.903,64.2136) scale(1,1) translate(-1.2003e-14,0)" writing-mode="lr" x="178.9" xml:space="preserve" y="67.70999999999999" zvalue="620"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,207.75,64.1903) scale(1,1) translate(0,0)" writing-mode="lr" x="207.75" xml:space="preserve" y="73.19" zvalue="621">35kV姐帽变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="285" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,187.531,388.75) scale(1,1) translate(0,0)" width="72.88" x="151.09" y="376.75" zvalue="786"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,187.531,388.75) scale(1,1) translate(0,0)" writing-mode="lr" x="187.53" xml:space="preserve" y="393.25" zvalue="786">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="176" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.625,388.75) scale(1,1) translate(0,0)" width="72.88" x="49.19" y="376.75" zvalue="787"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.625,388.75) scale(1,1) translate(0,0)" writing-mode="lr" x="85.63" xml:space="preserve" y="393.25" zvalue="787">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="138" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.625,352.25) scale(1,1) translate(0,0)" width="72.88" x="49.19" y="340.25" zvalue="788"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.625,352.25) scale(1,1) translate(0,0)" writing-mode="lr" x="85.63" xml:space="preserve" y="356.75" zvalue="788">信号一览</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="27" id="6" stroke="rgb(0,0,0)" text-anchor="middle" x="491.5" xml:space="preserve" y="98" zvalue="812">停电调电程序化操</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="27" id="6" stroke="rgb(0,0,0)" text-anchor="middle" writing-mode="lr" x="491.5" xml:space="preserve" y="127" zvalue="812">作</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,440.076,318.341) scale(1,1) translate(-3.6422e-13,0)" writing-mode="lr" x="440.08" xml:space="preserve" y="322.84" zvalue="7">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,439.818,642.25) scale(1,1) translate(0,4.21163e-13)" writing-mode="lr" x="439.82" xml:space="preserve" y="646.75" zvalue="9">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,762.449,592.042) scale(1,1) translate(0,0)" writing-mode="lr" x="762.45" xml:space="preserve" y="596.54" zvalue="45">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1092.35,867.089) scale(1,1) translate(0,0)" writing-mode="lr" x="1092.35" xml:space="preserve" y="871.59" zvalue="47">10</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1056.89,157.238) scale(1,1) translate(-2.2624e-13,0)" writing-mode="lr" x="1056.89" xml:space="preserve" y="161.74" zvalue="60">35kV母线PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,626.114,200.409) scale(1,1) translate(0,0)" writing-mode="lr" x="626.11" xml:space="preserve" y="204.91" zvalue="62">35kV#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1420.98,411.084) scale(1,1) translate(0,0)" writing-mode="lr" x="1420.98" xml:space="preserve" y="415.58" zvalue="72">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1419.55,365.18) scale(1,1) translate(0,0)" writing-mode="lr" x="1419.55" xml:space="preserve" y="369.68" zvalue="74">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" x="1366.203125" xml:space="preserve" y="481.5" zvalue="85">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1366.203125" xml:space="preserve" y="498.5" zvalue="85">2500kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1196.11,822.245) scale(1,1) translate(-5.14306e-13,0)" writing-mode="lr" x="1196.11" xml:space="preserve" y="826.75" zvalue="92">10kVⅠ母PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,548.537,914.125) scale(1,1) translate(0,0)" writing-mode="lr" x="548.54" xml:space="preserve" y="918.63" zvalue="147">10kV姐冒线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1036.42,951.502) scale(1,1) translate(0,0)" writing-mode="lr" x="1036.42" xml:space="preserve" y="956" zvalue="167">10kV#1电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,582.143,297.123) scale(1,1) translate(0,0)" writing-mode="lr" x="582.14" xml:space="preserve" y="301.62" zvalue="172">3531</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1470.68,590.614) scale(1,1) translate(0,0)" writing-mode="lr" x="1470.68" xml:space="preserve" y="595.11" zvalue="284">002</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,866.086,68.5) scale(1,1) translate(0,0)" writing-mode="lr" x="866.09" xml:space="preserve" y="73" zvalue="299">35kV允姐线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,893.309,246.157) scale(1,1) translate(0,0)" writing-mode="lr" x="893.3099999999999" xml:space="preserve" y="250.66" zvalue="300">351</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,848.797,180.442) scale(1,1) translate(0,0)" writing-mode="lr" x="848.8" xml:space="preserve" y="184.94" zvalue="302">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,901.732,157.003) scale(1,1) translate(0,0)" writing-mode="lr" x="901.73" xml:space="preserve" y="161.5" zvalue="304">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,848.925,302.237) scale(1,1) translate(0,0)" writing-mode="lr" x="848.92" xml:space="preserve" y="306.74" zvalue="307">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,901.732,285.003) scale(1,1) translate(0,0)" writing-mode="lr" x="901.73" xml:space="preserve" y="289.5" zvalue="316">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,906.717,106.943) scale(1,1) translate(0,0)" writing-mode="lr" x="906.72" xml:space="preserve" y="111.44" zvalue="322">9</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1258.09,68.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1258.09" xml:space="preserve" y="73" zvalue="327">35kV姐弄线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1285.31,246.157) scale(1,1) translate(0,0)" writing-mode="lr" x="1285.31" xml:space="preserve" y="250.66" zvalue="328">352</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1240.8,180.442) scale(1,1) translate(0,0)" writing-mode="lr" x="1240.8" xml:space="preserve" y="184.94" zvalue="330">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1293.73,157.003) scale(1,1) translate(0,0)" writing-mode="lr" x="1293.73" xml:space="preserve" y="161.5" zvalue="332">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1240.92,302.237) scale(1,1) translate(0,0)" writing-mode="lr" x="1240.92" xml:space="preserve" y="306.74" zvalue="335">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1293.73,285.003) scale(1,1) translate(0,0)" writing-mode="lr" x="1293.73" xml:space="preserve" y="289.5" zvalue="343">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1298.72,106.943) scale(1,1) translate(0,0)" writing-mode="lr" x="1298.72" xml:space="preserve" y="111.44" zvalue="347">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1104.34,235.82) scale(1,1) translate(0,0)" writing-mode="lr" x="1104.34" xml:space="preserve" y="240.32" zvalue="354">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1102.34,303.82) scale(1,1) translate(0,0)" writing-mode="lr" x="1102.34" xml:space="preserve" y="308.32" zvalue="358">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1488.05,363.184) scale(1,1) translate(0,0)" writing-mode="lr" x="1488.05" xml:space="preserve" y="367.68" zvalue="362">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,710.599,415.37) scale(1,1) translate(0,0)" writing-mode="lr" x="710.6" xml:space="preserve" y="419.87" zvalue="366">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,707.983,368.465) scale(1,1) translate(0,0)" writing-mode="lr" x="707.98" xml:space="preserve" y="372.97" zvalue="368">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,777.483,367.47) scale(1,1) translate(0,0)" writing-mode="lr" x="777.48" xml:space="preserve" y="371.97" zvalue="372">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="189" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,575.772,726.264) scale(1,1) translate(0,0)" writing-mode="lr" x="575.77" xml:space="preserve" y="730.76" zvalue="385">052</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,526.031,797.28) scale(1,1) translate(0,0)" writing-mode="lr" x="526.03" xml:space="preserve" y="801.78" zvalue="393">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="241" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,600.167,797.505) scale(1,1) translate(0,0)" writing-mode="lr" x="600.17" xml:space="preserve" y="802" zvalue="397">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,860.537,914.125) scale(1,1) translate(0,0)" writing-mode="lr" x="860.54" xml:space="preserve" y="918.63" zvalue="401">10kV丙午线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,887.772,726.264) scale(1,1) translate(1.94238e-13,0)" writing-mode="lr" x="887.77" xml:space="preserve" y="730.76" zvalue="404">054</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,842.031,797.28) scale(1,1) translate(0,0)" writing-mode="lr" x="842.03" xml:space="preserve" y="801.78" zvalue="411">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,916.167,795.505) scale(1,1) translate(0,0)" writing-mode="lr" x="916.17" xml:space="preserve" y="800" zvalue="416">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1055.77,726.264) scale(1,1) translate(0,0)" writing-mode="lr" x="1055.77" xml:space="preserve" y="730.76" zvalue="432">055</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="210" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1010.03,797.28) scale(1,1) translate(0,0)" writing-mode="lr" x="1010.03" xml:space="preserve" y="801.78" zvalue="438">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="206" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1080.17,796.505) scale(1,1) translate(0,0)" writing-mode="lr" x="1080.17" xml:space="preserve" y="801" zvalue="442">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,431.637,866.835) scale(1,1) translate(0,0)" writing-mode="lr" x="431.64" xml:space="preserve" y="871.34" zvalue="465">10kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,453.299,726.264) scale(1,1) translate(0,0)" writing-mode="lr" x="453.3" xml:space="preserve" y="730.76" zvalue="472">0511</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,703.728,914.125) scale(1,1) translate(-6.12157e-13,0)" writing-mode="lr" x="703.73" xml:space="preserve" y="918.63" zvalue="482">备用</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1765,646.305) scale(1,1) translate(0,-4.24197e-13)" writing-mode="lr" x="1765" xml:space="preserve" y="650.8099999999999" zvalue="501">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="196" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1497.22,822.245) scale(1,1) translate(0,0)" writing-mode="lr" x="1497.22" xml:space="preserve" y="826.75" zvalue="503">10kVⅡ母PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="243" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1628.54,914.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1628.54" xml:space="preserve" y="918.63" zvalue="512">10kV芒线线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="242" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1655.77,726.264) scale(1,1) translate(0,0)" writing-mode="lr" x="1655.77" xml:space="preserve" y="730.76" zvalue="515">056</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="235" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1589.84,796.28) scale(1,1) translate(0,0)" writing-mode="lr" x="1589.84" xml:space="preserve" y="800.78" zvalue="522">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1696.17,796.505) scale(1,1) translate(0,0)" writing-mode="lr" x="1696.17" xml:space="preserve" y="801" zvalue="527">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1171.07,711.224) scale(1,1) translate(0,0)" writing-mode="lr" x="1171.07" xml:space="preserve" y="715.72" zvalue="529">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1472.43,711.224) scale(1,1) translate(0,0)" writing-mode="lr" x="1472.43" xml:space="preserve" y="715.72" zvalue="533">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1289.25,709.406) scale(1,1) translate(0,0)" writing-mode="lr" x="1289.25" xml:space="preserve" y="713.91" zvalue="535">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="281" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1371.28,721.534) scale(1,1) translate(0,0)" writing-mode="lr" x="1371.28" xml:space="preserve" y="726.03" zvalue="536">012</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" x="654.546875" xml:space="preserve" y="483.9375" zvalue="551">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="654.546875" xml:space="preserve" y="499.9375" zvalue="551">5000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,901.732,217.003) scale(1,1) translate(0,0)" writing-mode="lr" x="901.73" xml:space="preserve" y="221.5" zvalue="554">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="295" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1293.73,217.003) scale(1,1) translate(0,0)" writing-mode="lr" x="1293.73" xml:space="preserve" y="221.5" zvalue="558">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="307" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1033.62,273.878) scale(1,1) translate(0,0)" writing-mode="lr" x="1033.62" xml:space="preserve" y="278.38" zvalue="568">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1344.5,807.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1344.5" xml:space="preserve" y="812" zvalue="579">10kV分段</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1630.25,932) scale(1,1) translate(0,0)" writing-mode="lr" x="1630.25" xml:space="preserve" y="936.5" zvalue="581">（海螺水泥厂）</text>
  <line fill="none" id="276" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.750000000000455" x2="370.75" y1="149.3704926140824" y2="149.3704926140824" zvalue="623"/>
  <line fill="none" id="271" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="375.5" x2="375.5" y1="12.5" y2="1042.5" zvalue="624"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="161.5" y2="161.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="187.5" y2="187.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="161.5" y2="187.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="161.5" y2="187.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="161.5" y2="161.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="187.5" y2="187.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="161.5" y2="187.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="161.5" y2="187.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="187.5" y2="187.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="211.75" y2="211.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="187.5" y2="211.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="187.5" y2="211.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="187.5" y2="187.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="211.75" y2="211.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="187.5" y2="211.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="187.5" y2="211.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="211.75" y2="211.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="234.5" y2="234.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="211.75" y2="234.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="211.75" y2="234.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="211.75" y2="211.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="234.5" y2="234.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="211.75" y2="234.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="211.75" y2="234.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="234.5" y2="234.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="257.25" y2="257.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="234.5" y2="257.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="234.5" y2="257.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="234.5" y2="234.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="257.25" y2="257.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="234.5" y2="257.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="234.5" y2="257.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="257.25" y2="257.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="280" y2="280"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="257.25" y2="280"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="257.25" y2="280"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="257.25" y2="257.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="280" y2="280"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="257.25" y2="280"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="257.25" y2="280"/>
  <line fill="none" id="268" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.750000000000455" x2="370.75" y1="619.3704926140824" y2="619.3704926140824" zvalue="626"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.75" x2="101.5245" y1="441.5" y2="441.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.75" x2="101.5245" y1="479.7823" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.75" x2="55.75" y1="441.5" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="101.5245" y1="441.5" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="160.3309" y1="441.5" y2="441.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="160.3309" y1="479.7823" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="101.5245" y1="441.5" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="160.3309" y1="441.5" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="219.1373" y1="441.5" y2="441.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="219.1373" y1="479.7823" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="160.3309" y1="441.5" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1373" x2="219.1373" y1="441.5" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1372" x2="277.9436000000001" y1="441.5" y2="441.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1372" x2="277.9436000000001" y1="479.7823" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1372" x2="219.1372" y1="441.5" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="277.9436000000001" y1="441.5" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="336.75" y1="441.5" y2="441.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="336.75" y1="479.7823" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="277.9436000000001" y1="441.5" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="336.75" x2="336.75" y1="441.5" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.75" x2="101.5245" y1="479.7823" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.75" x2="101.5245" y1="504.4617" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.75" x2="55.75" y1="479.7823" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="101.5245" y1="479.7823" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="160.3309" y1="479.7823" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="160.3309" y1="504.4617" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="101.5245" y1="479.7823" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="160.3309" y1="479.7823" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="219.1373" y1="479.7823" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="219.1373" y1="504.4617" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="160.3309" y1="479.7823" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1373" x2="219.1373" y1="479.7823" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1372" x2="277.9436000000001" y1="479.7823" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1372" x2="277.9436000000001" y1="504.4617" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1372" x2="219.1372" y1="479.7823" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="277.9436000000001" y1="479.7823" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="336.75" y1="479.7823" y2="479.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="336.75" y1="504.4617" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="277.9436000000001" y1="479.7823" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="336.75" x2="336.75" y1="479.7823" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.75" x2="101.5245" y1="504.4617" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.75" x2="101.5245" y1="529.1411000000001" y2="529.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.75" x2="55.75" y1="504.4617" y2="529.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="101.5245" y1="504.4617" y2="529.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="160.3309" y1="504.4617" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="160.3309" y1="529.1411000000001" y2="529.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="101.5245" y1="504.4617" y2="529.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="160.3309" y1="504.4617" y2="529.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="219.1373" y1="504.4617" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="219.1373" y1="529.1411000000001" y2="529.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="160.3309" y1="504.4617" y2="529.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1373" x2="219.1373" y1="504.4617" y2="529.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1372" x2="277.9436000000001" y1="504.4617" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1372" x2="277.9436000000001" y1="529.1411000000001" y2="529.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1372" x2="219.1372" y1="504.4617" y2="529.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="277.9436000000001" y1="504.4617" y2="529.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="336.75" y1="504.4617" y2="504.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="336.75" y1="529.1411000000001" y2="529.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="277.9436000000001" y1="504.4617" y2="529.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="336.75" x2="336.75" y1="504.4617" y2="529.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.75" x2="101.5245" y1="529.1410999999999" y2="529.1410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.75" x2="101.5245" y1="553.8205" y2="553.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.75" x2="55.75" y1="529.1410999999999" y2="553.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="101.5245" y1="529.1410999999999" y2="553.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="160.3309" y1="529.1410999999999" y2="529.1410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="160.3309" y1="553.8205" y2="553.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="101.5245" y1="529.1410999999999" y2="553.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="160.3309" y1="529.1410999999999" y2="553.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="219.1373" y1="529.1410999999999" y2="529.1410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="219.1373" y1="553.8205" y2="553.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="160.3309" y1="529.1410999999999" y2="553.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1373" x2="219.1373" y1="529.1410999999999" y2="553.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1372" x2="277.9436000000001" y1="529.1410999999999" y2="529.1410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1372" x2="277.9436000000001" y1="553.8205" y2="553.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1372" x2="219.1372" y1="529.1410999999999" y2="553.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="277.9436000000001" y1="529.1410999999999" y2="553.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="336.75" y1="529.1410999999999" y2="529.1410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="336.75" y1="553.8205" y2="553.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="277.9436000000001" y1="529.1410999999999" y2="553.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="336.75" x2="336.75" y1="529.1410999999999" y2="553.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.75" x2="101.5245" y1="553.8206" y2="553.8206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.75" x2="101.5245" y1="578.5" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.75" x2="55.75" y1="553.8206" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="101.5245" y1="553.8206" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="160.3309" y1="553.8206" y2="553.8206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="160.3309" y1="578.5" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="101.5245" y1="553.8206" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="160.3309" y1="553.8206" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="219.1373" y1="553.8206" y2="553.8206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="219.1373" y1="578.5" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="160.3309" y1="553.8206" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1373" x2="219.1373" y1="553.8206" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1372" x2="277.9436000000001" y1="553.8206" y2="553.8206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1372" x2="277.9436000000001" y1="578.5" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1372" x2="219.1372" y1="553.8206" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="277.9436000000001" y1="553.8206" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="336.75" y1="553.8206" y2="553.8206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="336.75" y1="578.5" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="277.9436000000001" y1="553.8206" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="336.75" x2="336.75" y1="553.8206" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.75" x2="101.5245" y1="578.5" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.75" x2="101.5245" y1="603.1794" y2="603.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.75" x2="55.75" y1="578.5" y2="603.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="101.5245" y1="578.5" y2="603.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="160.3309" y1="578.5" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="160.3309" y1="603.1794" y2="603.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5245" x2="101.5245" y1="578.5" y2="603.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="160.3309" y1="578.5" y2="603.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="219.1373" y1="578.5" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="219.1373" y1="603.1794" y2="603.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.3309" x2="160.3309" y1="578.5" y2="603.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1373" x2="219.1373" y1="578.5" y2="603.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1372" x2="277.9436000000001" y1="578.5" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1372" x2="277.9436000000001" y1="603.1794" y2="603.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="219.1372" x2="219.1372" y1="578.5" y2="603.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="277.9436000000001" y1="578.5" y2="603.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="336.75" y1="578.5" y2="578.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="336.75" y1="603.1794" y2="603.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="277.9436000000001" x2="277.9436000000001" y1="578.5" y2="603.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="336.75" x2="336.75" y1="578.5" y2="603.1794"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="934.5" y2="934.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="973.6632999999999" y2="973.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="934.5" y2="973.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="934.5" y2="973.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="363.75" y1="934.5" y2="934.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="363.75" y1="973.6632999999999" y2="973.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="934.5" y2="973.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="934.5" y2="973.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="973.66327" y2="973.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1001.58167" y2="1001.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="973.66327" y2="1001.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="973.66327" y2="1001.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="973.66327" y2="973.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1001.58167" y2="1001.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="973.66327" y2="1001.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.75" x2="183.75" y1="973.66327" y2="1001.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="973.66327" y2="973.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1001.58167" y2="1001.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="183.7500000000001" y1="973.66327" y2="1001.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.7500000000001" x2="273.7500000000001" y1="973.66327" y2="1001.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="973.66327" y2="973.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1001.58167" y2="1001.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="273.75" y1="973.66327" y2="1001.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="973.66327" y2="1001.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1001.5816" y2="1001.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1029.5" y2="1029.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="1001.5816" y2="1029.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="1001.5816" y2="1029.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1001.5816" y2="1001.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1029.5" y2="1029.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="1001.5816" y2="1029.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.75" x2="183.75" y1="1001.5816" y2="1029.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1001.5816" y2="1001.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1029.5" y2="1029.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="183.7500000000001" y1="1001.5816" y2="1029.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.7500000000001" x2="273.7500000000001" y1="1001.5816" y2="1029.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1001.5816" y2="1001.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1029.5" y2="1029.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="273.75" y1="1001.5816" y2="1029.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="1001.5816" y2="1029.5"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="256" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.75,954.5) scale(1,1) translate(0,0)" writing-mode="lr" x="49.75" xml:space="preserve" y="960.5" zvalue="630">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46.75,988.5) scale(1,1) translate(0,0)" writing-mode="lr" x="46.75" xml:space="preserve" y="994.5" zvalue="631">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.75,988.5) scale(1,1) translate(0,0)" writing-mode="lr" x="228.75" xml:space="preserve" y="994.5" zvalue="632">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.75,1016.5) scale(1,1) translate(0,0)" writing-mode="lr" x="44.75" xml:space="preserve" y="1022.5" zvalue="633">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.75,1016.5) scale(1,1) translate(0,0)" writing-mode="lr" x="226.75" xml:space="preserve" y="1022.5" zvalue="634">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="250" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,128.278,461.889) scale(1,1) translate(0,-9.89949e-14)" writing-mode="lr" x="128.2778049045141" xml:space="preserve" y="466.3888914320205" zvalue="635">35kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="237" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.25,649) scale(1,1) translate(0,0)" writing-mode="lr" x="69.25" xml:space="preserve" y="653.5" zvalue="637">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="234" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.149,316.341) scale(1,1) translate(0,0)" writing-mode="lr" x="202.15" xml:space="preserve" y="320.84" zvalue="638">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="232" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.149,316.341) scale(1,1) translate(0,0)" writing-mode="lr" x="307.15" xml:space="preserve" y="320.84" zvalue="639">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="231" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,247.681,460.333) scale(1,1) translate(0,-9.86495e-14)" writing-mode="lr" x="247.6805758608714" xml:space="preserve" y="464.8333358830876" zvalue="640">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,306.75,460.333) scale(1,1) translate(0,0)" writing-mode="lr" x="306.75" xml:space="preserve" y="464.8333358764648" zvalue="641">10kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.75,492.25) scale(1,1) translate(0,0)" writing-mode="lr" x="80.75" xml:space="preserve" y="496.75" zvalue="642">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="227" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.75,517.75) scale(1,1) translate(0,0)" writing-mode="lr" x="80.75" xml:space="preserve" y="522.25" zvalue="643">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.75,540.75) scale(1,1) translate(0,0)" writing-mode="lr" x="80.75" xml:space="preserve" y="545.25" zvalue="644">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="223" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.75,563.75) scale(1,1) translate(0,0)" writing-mode="lr" x="80.75" xml:space="preserve" y="568.25" zvalue="645">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="222" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.75,590.75) scale(1,1) translate(0,0)" writing-mode="lr" x="80.75" xml:space="preserve" y="595.25" zvalue="646">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="221" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229.804,956.5) scale(1,1) translate(0,0)" writing-mode="lr" x="229.8" xml:space="preserve" y="962.5" zvalue="647">JieMao-01-2022</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="219" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,139.804,1016.5) scale(1,1) translate(0,0)" writing-mode="lr" x="139.8" xml:space="preserve" y="1022.5" zvalue="648">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="213" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,318.804,1016.5) scale(1,1) translate(0,0)" writing-mode="lr" x="318.8" xml:space="preserve" y="1022.5" zvalue="649">20220421</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,42.75,175.5) scale(1,1) translate(0,0)" writing-mode="lr" x="42.75" xml:space="preserve" y="180" zvalue="650">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.75,175.5) scale(1,1) translate(0,0)" writing-mode="lr" x="222.75" xml:space="preserve" y="180" zvalue="651">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50.4375,199.75) scale(1,1) translate(0,0)" writing-mode="lr" x="50.44" xml:space="preserve" y="204.25" zvalue="652">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.4375,247.5) scale(1,1) translate(0,0)" writing-mode="lr" x="44.44" xml:space="preserve" y="252" zvalue="653">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225.5,247) scale(1,1) translate(0,0)" writing-mode="lr" x="225.5" xml:space="preserve" y="251.5" zvalue="654">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.4375,270.5) scale(1,1) translate(0,0)" writing-mode="lr" x="44.44" xml:space="preserve" y="275" zvalue="655">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225.5,270) scale(1,1) translate(0,0)" writing-mode="lr" x="225.5" xml:space="preserve" y="274.5" zvalue="656">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="192" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.9375,223.75) scale(1,1) translate(0,0)" writing-mode="lr" x="49.94" xml:space="preserve" y="228.25" zvalue="657">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230.5,223.5) scale(1,1) translate(0,0)" writing-mode="lr" x="230.5" xml:space="preserve" y="228" zvalue="658">10kVⅡ母频率</text>
  <ellipse cx="849.66" cy="246.16" fill="rgb(255,0,0)" fill-opacity="1" id="356" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="823"/>
  <ellipse cx="1239.66" cy="246.16" fill="rgb(255,0,0)" fill-opacity="1" id="357" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="825"/>
  <ellipse cx="995.66" cy="796.16" fill="rgb(255,0,0)" fill-opacity="1" id="359" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="827"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="368" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,289.235,388.75) scale(1,1) translate(0,0)" writing-mode="lr" x="289.2350463867188" xml:space="preserve" y="393.25" zvalue="831">小电流接地</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="367" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.2857,318.039) scale(1,1) translate(0,3.40771e-13)" writing-mode="lr" x="86.28570556640625" xml:space="preserve" y="322.5388641357421" zvalue="832">全站公用</text>
 </g>
 <g id="ButtonClass">
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="151.09" y="376.75" zvalue="786"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="49.19" y="376.75" zvalue="787"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="49.19" y="340.25" zvalue="788"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="321">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="321" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,617.523,137.455) scale(1,1) translate(0,0)" writing-mode="lr" x="616.9400000000001" xml:space="preserve" y="142.23" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133024280580" ObjectName="P"/>
   </metadata>
  </g>
  <g id="322">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="322" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,617.523,154.455) scale(1,1) translate(0,0)" writing-mode="lr" x="616.9400000000001" xml:space="preserve" y="159.23" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133024346115" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="168">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="168" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,129.389,517.75) scale(1,1) translate(1.15494e-14,0)" writing-mode="lr" x="129.12" xml:space="preserve" y="522.4299999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133009141763" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="159">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="159" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,129.389,540.75) scale(1,1) translate(1.15494e-14,0)" writing-mode="lr" x="129.12" xml:space="preserve" y="545.4299999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133009207299" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="158">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="158" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,129.389,563.75) scale(1,1) translate(1.15494e-14,6.13121e-14)" writing-mode="lr" x="129.12" xml:space="preserve" y="568.4299999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133009272836" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="112">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="112" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,129.389,492) scale(1,1) translate(1.15494e-14,0)" writing-mode="lr" x="129.12" xml:space="preserve" y="496.68" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133009403908" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="118">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="118" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,146,199.611) scale(1,1) translate(0,0)" writing-mode="lr" x="145.58" xml:space="preserve" y="205.79" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133009534980" ObjectName="F"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="111" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,248.792,517.75) scale(1,1) translate(2.48058e-14,0)" writing-mode="lr" x="248.53" xml:space="preserve" y="522.4299999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133009666051" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="108">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="108" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,248.792,540.75) scale(1,1) translate(2.48058e-14,0)" writing-mode="lr" x="248.53" xml:space="preserve" y="545.4299999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133009731588" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,248.792,563.75) scale(1,1) translate(2.48058e-14,6.13121e-14)" writing-mode="lr" x="248.53" xml:space="preserve" y="568.4299999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133009797124" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="101" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,248.792,492.25) scale(1,1) translate(2.48058e-14,5.3374e-14)" writing-mode="lr" x="248.53" xml:space="preserve" y="496.93" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133009928196" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="95" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,146,224.5) scale(1,1) translate(0,0)" writing-mode="lr" x="145.58" xml:space="preserve" y="230.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133010059268" ObjectName="F"/>
   </metadata>
  </g>
  <g id="126">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="126" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,306.75,517.75) scale(1,1) translate(3.12404e-14,0)" writing-mode="lr" x="306.49" xml:space="preserve" y="522.4299999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133019430916" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="93" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,306.75,540.75) scale(1,1) translate(3.12404e-14,0)" writing-mode="lr" x="306.49" xml:space="preserve" y="545.4299999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133019496452" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,306.75,563.75) scale(1,1) translate(3.12404e-14,6.13121e-14)" writing-mode="lr" x="306.49" xml:space="preserve" y="568.4299999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133019561987" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,326.75,223.75) scale(1,1) translate(0,0)" writing-mode="lr" x="326.33" xml:space="preserve" y="229.92" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133019824132" ObjectName="F"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,306.75,492.25) scale(1,1) translate(3.12404e-14,5.3374e-14)" writing-mode="lr" x="306.49" xml:space="preserve" y="496.93" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133019693059" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,146,246.694) scale(1,1) translate(0,0)" writing-mode="lr" x="145.58" xml:space="preserve" y="252.87" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133022707716" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="47">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,146,269.694) scale(1,1) translate(0,2.01046e-13)" writing-mode="lr" x="145.58" xml:space="preserve" y="275.87" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133022773252" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="44">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,326.75,245.694) scale(1,1) translate(0,0)" writing-mode="lr" x="326.33" xml:space="preserve" y="251.87" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133006127107" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="40">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,326.75,268.694) scale(1,1) translate(0,3.43318e-13)" writing-mode="lr" x="326.33" xml:space="preserve" y="274.87" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133006192643" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,129.389,590.75) scale(1,1) translate(1.15494e-14,6.43097e-14)" writing-mode="lr" x="129.12" xml:space="preserve" y="595.4299999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133009600515" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="150" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,248.792,590.75) scale(1,1) translate(2.48058e-14,6.43097e-14)" writing-mode="lr" x="248.53" xml:space="preserve" y="595.4299999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133010124804" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,306.75,590.75) scale(1,1) translate(3.12404e-14,6.43097e-14)" writing-mode="lr" x="306.49" xml:space="preserve" y="595.4299999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133019889668" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="117" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,146,174.5) scale(1,1) translate(0,0)" writing-mode="lr" x="145.58" xml:space="preserve" y="180.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133061832707" ObjectName=""/>
   </metadata>
  </g>
  <g id="124">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="124" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,326.75,175) scale(1,1) translate(0,0)" writing-mode="lr" x="326.33" xml:space="preserve" y="181.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133061898243" ObjectName=""/>
   </metadata>
  </g>
  <g id="323">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="323" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,628.331,388.25) scale(1,1) translate(0,0)" writing-mode="lr" x="627.75" xml:space="preserve" y="392.95" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133022248963" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="324">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="324" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1341.23,387) scale(1,1) translate(1.42745e-13,0)" writing-mode="lr" x="1340.65" xml:space="preserve" y="391.7" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133005668356" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="325">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="325" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,628.331,415.25) scale(1,1) translate(0,0)" writing-mode="lr" x="627.75" xml:space="preserve" y="419.95" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133022314499" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="326">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="326" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1341.23,414) scale(1,1) translate(1.42745e-13,0)" writing-mode="lr" x="1340.65" xml:space="preserve" y="418.7" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133005733892" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="327">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="327" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,628.331,442.25) scale(1,1) translate(0,0)" writing-mode="lr" x="627.75" xml:space="preserve" y="446.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133022511107" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="328">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="328" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1341.23,441) scale(1,1) translate(1.42745e-13,0)" writing-mode="lr" x="1340.65" xml:space="preserve" y="445.7" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133005930499" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="329">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="329" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,640.831,546.25) scale(1,1) translate(0,0)" writing-mode="lr" x="640.25" xml:space="preserve" y="550.95" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133022380035" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="330">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="330" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,640.831,573.25) scale(1,1) translate(0,0)" writing-mode="lr" x="640.25" xml:space="preserve" y="577.95" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133022445571" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="331">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="331" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,640.831,600.25) scale(1,1) translate(0,0)" writing-mode="lr" x="640.25" xml:space="preserve" y="604.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133022838788" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="332">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="332" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1338.73,531.25) scale(1,1) translate(1.42467e-13,0)" writing-mode="lr" x="1338.15" xml:space="preserve" y="535.95" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133005799428" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="333">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="333" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1338.73,558.25) scale(1,1) translate(1.42467e-13,0)" writing-mode="lr" x="1338.15" xml:space="preserve" y="562.95" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133005864964" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="334">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="334" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1338.73,585.25) scale(1,1) translate(1.42467e-13,0)" writing-mode="lr" x="1338.15" xml:space="preserve" y="589.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133006258179" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="335">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="335" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,548.787,949.5) scale(1,1) translate(0,0)" writing-mode="lr" x="548.3200000000001" xml:space="preserve" y="954.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133789937667" ObjectName="P"/>
   </metadata>
  </g>
  <g id="336">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="336" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,860.787,949.5) scale(1,1) translate(0,0)" writing-mode="lr" x="860.3200000000001" xml:space="preserve" y="954.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133015433220" ObjectName="P"/>
   </metadata>
  </g>
  <g id="337">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="337" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,703.978,949.5) scale(1,1) translate(-1.46322e-13,0)" writing-mode="lr" x="703.51" xml:space="preserve" y="954.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133018513411" ObjectName="P"/>
   </metadata>
  </g>
  <g id="338">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="338" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1628.79,949.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1628.32" xml:space="preserve" y="954.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133020479492" ObjectName="P"/>
   </metadata>
  </g>
  <g id="339">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="339" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,548.787,972.5) scale(1,1) translate(0,0)" writing-mode="lr" x="548.3200000000001" xml:space="preserve" y="977.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133790003203" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="340">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="340" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,860.787,972.5) scale(1,1) translate(0,0)" writing-mode="lr" x="860.3200000000001" xml:space="preserve" y="977.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133015498756" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="341">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="341" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,703.978,972.5) scale(1,1) translate(-1.46322e-13,0)" writing-mode="lr" x="703.51" xml:space="preserve" y="977.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133018578948" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="342">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="342" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1628.79,972.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1628.32" xml:space="preserve" y="977.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133020545027" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="343">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="343" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,548.787,995.5) scale(1,1) translate(0,0)" writing-mode="lr" x="548.3200000000001" xml:space="preserve" y="1000.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133790068739" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="344">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="344" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,860.787,995.5) scale(1,1) translate(0,0)" writing-mode="lr" x="860.3200000000001" xml:space="preserve" y="1000.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133015564292" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="345">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="345" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,703.978,995.5) scale(1,1) translate(-1.46322e-13,0)" writing-mode="lr" x="703.51" xml:space="preserve" y="1000.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133018644484" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="346">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="346" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1628.79,995.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1628.32" xml:space="preserve" y="1000.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133020610563" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="347">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="347" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,775.378,31.1878) scale(1,1) translate(0,0)" writing-mode="lr" x="774.91" xml:space="preserve" y="35.85" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133011238916" ObjectName="P"/>
   </metadata>
  </g>
  <g id="348">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="348" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1348.88,31.1878) scale(1,1) translate(0,0)" writing-mode="lr" x="1348.41" xml:space="preserve" y="35.85" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133012811779" ObjectName="P"/>
   </metadata>
  </g>
  <g id="349">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="349" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,775.378,50.6878) scale(1,1) translate(0,0)" writing-mode="lr" x="774.91" xml:space="preserve" y="55.35" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133011304452" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="350">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="350" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1348.88,50.6878) scale(1,1) translate(0,0)" writing-mode="lr" x="1348.41" xml:space="preserve" y="55.35" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133012877315" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="351">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="351" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,775.378,70.1878) scale(1,1) translate(0,0)" writing-mode="lr" x="774.91" xml:space="preserve" y="74.84999999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133011369988" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="352">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="352" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1348.88,70.1878) scale(1,1) translate(0,0)" writing-mode="lr" x="1348.41" xml:space="preserve" y="74.84999999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133012942851" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="353">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="353" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,775.378,89.6878) scale(1,1) translate(0,0)" writing-mode="lr" x="774.91" xml:space="preserve" y="94.34999999999999" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133012090883" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="354">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="354" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1348.88,89.6878) scale(1,1) translate(0,0)" writing-mode="lr" x="1348.41" xml:space="preserve" y="94.34999999999999" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133013663748" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="134" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1030.43,966.788) scale(1,1) translate(0,0)" writing-mode="lr" x="1029.96" xml:space="preserve" y="971.45" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133004357635" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="135">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="135" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1027,983.048) scale(1,1) translate(0,0)" writing-mode="lr" x="1026.53" xml:space="preserve" y="987.83" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133004423171" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="136" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1341.28,833.764) scale(1,1) translate(0,0)" writing-mode="lr" x="1340.81" xml:space="preserve" y="838.54" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133021396995" ObjectName="P"/>
   </metadata>
  </g>
  <g id="267">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="267" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1341.28,850.764) scale(1,1) translate(0,0)" writing-mode="lr" x="1340.81" xml:space="preserve" y="855.54" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133021462531" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="315">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="315" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1340.28,866.764) scale(1,1) translate(0,0)" writing-mode="lr" x="1339.81" xml:space="preserve" y="871.54" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133021528068" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="318">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="318" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,423.88,887.695) scale(1,1) translate(0,0)" writing-mode="lr" x="423.41" xml:space="preserve" y="891.86" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133016875011" ObjectName="P"/>
   </metadata>
  </g>
  <g id="319">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="319" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,423.88,908.695) scale(1,1) translate(0,0)" writing-mode="lr" x="423.41" xml:space="preserve" y="912.86" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133016940547" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="320">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="320" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,423.88,929.695) scale(1,1) translate(0,0)" writing-mode="lr" x="423.41" xml:space="preserve" y="933.86" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133017006083" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="358">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="358" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,616.523,173.455) scale(1,1) translate(0,0)" writing-mode="lr" x="615.9400000000001" xml:space="preserve" y="178.23" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133024411651" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="185">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="185" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1059.36,134.5) scale(1,1) translate(1.14797e-13,9.90874e-14)" writing-mode="lr" x="1059.1" xml:space="preserve" y="139.11" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133009403908" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="186">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="186" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1196.36,849.5) scale(1,1) translate(-5.20028e-13,0)" writing-mode="lr" x="1196.1" xml:space="preserve" y="854.16" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133009928196" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="204">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="204" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1499.36,848) scale(1,1) translate(1.63647e-13,0)" writing-mode="lr" x="1499.1" xml:space="preserve" y="852.65" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133019693059" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="BusbarSectionClass">
  <g id="128">
   <path class="kv35" d="M 449.67 331.34 L 1532 331.34" stroke-width="6" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674399420419" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674399420419"/></metadata>
  <path d="M 449.67 331.34 L 1532 331.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv10" d="M 393.16 665.31 L 1305 665.31" stroke-width="6" zvalue="8"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674399485955" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674399485955"/></metadata>
  <path d="M 393.16 665.31 L 1305 665.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv10" d="M 1375 665.31 L 1806 665.31" stroke-width="6" zvalue="500"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674399551491" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674399551491"/></metadata>
  <path d="M 1375 665.31 L 1806 665.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="461">
   <use class="kv10" height="20" transform="rotate(180,732.113,595) scale(2.2,2.2) translate(-393.334,-312.545)" width="10" x="721.1126095940635" xlink:href="#Breaker:小车断路器_0" y="573" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925092507652" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925092507652"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,732.113,595) scale(2.2,2.2) translate(-393.334,-312.545)" width="10" x="721.1126095940635" y="573"/></g>
  <g id="816">
   <use class="kv35" height="20" transform="rotate(180,1442.59,410.709) scale(1.5542,1.35421) translate(-511.63,-103.884)" width="10" x="1434.816710722901" xlink:href="#Breaker:开关_0" y="397.1666962122333" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925092442116" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925092442116"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1442.59,410.709) scale(1.5542,1.35421) translate(-511.63,-103.884)" width="10" x="1434.816710722901" y="397.1666962122333"/></g>
  <g id="230">
   <use class="kv10" height="20" transform="rotate(180,1442.34,586.614) scale(2.2,2.2) translate(-780.731,-307.971)" width="10" x="1431.339769635882" xlink:href="#Breaker:小车断路器_0" y="564.6135332832048" zvalue="283"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925092573188" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473925092573188"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1442.34,586.614) scale(2.2,2.2) translate(-780.731,-307.971)" width="10" x="1431.339769635882" y="564.6135332832048"/></g>
  <g id="69">
   <use class="kv35" height="20" transform="rotate(0,866.705,243.708) scale(1.5542,1.35421) translate(-306.28,-60.2027)" width="10" x="858.9340345345652" xlink:href="#Breaker:开关_0" y="230.165750510661" zvalue="298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925092638724" ObjectName="35kV允姐线351断路器"/>
   <cge:TPSR_Ref TObjectID="6473925092638724"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,866.705,243.708) scale(1.5542,1.35421) translate(-306.28,-60.2027)" width="10" x="858.9340345345652" y="230.165750510661"/></g>
  <g id="99">
   <use class="kv35" height="20" transform="rotate(0,1258.71,243.708) scale(1.5542,1.35421) translate(-446.06,-60.2027)" width="10" x="1250.934034534565" xlink:href="#Breaker:开关_0" y="230.165750510661" zvalue="326"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925092704260" ObjectName="35kV姐弄线352断路器"/>
   <cge:TPSR_Ref TObjectID="6473925092704260"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1258.71,243.708) scale(1.5542,1.35421) translate(-446.06,-60.2027)" width="10" x="1250.934034534565" y="230.165750510661"/></g>
  <g id="170">
   <use class="kv35" height="20" transform="rotate(180,732.203,414.995) scale(1.5542,1.35421) translate(-258.319,-105.005)" width="10" x="724.4320810331599" xlink:href="#Breaker:开关_0" y="401.4524104979477" zvalue="365"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925092769796" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925092769796"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,732.203,414.995) scale(1.5542,1.35421) translate(-258.319,-105.005)" width="10" x="724.4320810331599" y="401.4524104979477"/></g>
  <g id="214">
   <use class="kv10" height="20" transform="rotate(180,547.435,722.264) scale(2.2,2.2) translate(-292.601,-381.962)" width="10" x="536.4350077311196" xlink:href="#Breaker:小车断路器_0" y="700.2643269339985" zvalue="384"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925117280259" ObjectName="10kV姐冒线052断路器"/>
   <cge:TPSR_Ref TObjectID="6473925117280259"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,547.435,722.264) scale(2.2,2.2) translate(-292.601,-381.962)" width="10" x="536.4350077311196" y="700.2643269339985"/></g>
  <g id="66">
   <use class="kv10" height="20" transform="rotate(180,859.435,722.264) scale(2.2,2.2) translate(-462.783,-381.962)" width="10" x="848.4350077311196" xlink:href="#Breaker:小车断路器_0" y="700.2643269339985" zvalue="403"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925092900867" ObjectName="10kV丙午线054断路器"/>
   <cge:TPSR_Ref TObjectID="6473925092900867"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,859.435,722.264) scale(2.2,2.2) translate(-462.783,-381.962)" width="10" x="848.4350077311196" y="700.2643269339985"/></g>
  <g id="233">
   <use class="kv10" height="20" transform="rotate(180,1027.44,722.264) scale(2.2,2.2) translate(-554.419,-381.962)" width="10" x="1016.43500773112" xlink:href="#Breaker:小车断路器_0" y="700.2643280029297" zvalue="431"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925092966404" ObjectName="10kV#1电容器055断路器"/>
   <cge:TPSR_Ref TObjectID="6473925092966404"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1027.44,722.264) scale(2.2,2.2) translate(-554.419,-381.962)" width="10" x="1016.43500773112" y="700.2643280029297"/></g>
  <g id="191">
   <use class="kv10" height="20" transform="rotate(180,703.435,722.264) scale(2.2,2.2) translate(-377.692,-381.962)" width="10" x="692.4350077311196" xlink:href="#Breaker:小车断路器_0" y="700.2643269339985" zvalue="485"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925093097476" ObjectName="10kV备用053断路器"/>
   <cge:TPSR_Ref TObjectID="6473925093097476"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,703.435,722.264) scale(2.2,2.2) translate(-377.692,-381.962)" width="10" x="692.4350077311196" y="700.2643269339985"/></g>
  <g id="254">
   <use class="kv10" height="20" transform="rotate(180,1627.44,722.264) scale(2.2,2.2) translate(-881.692,-381.962)" width="10" x="1616.43500773112" xlink:href="#Breaker:小车断路器_0" y="700.2643269339985" zvalue="514"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925093163012" ObjectName="10kV芒线线056断路器"/>
   <cge:TPSR_Ref TObjectID="6473925093163012"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1627.44,722.264) scale(2.2,2.2) translate(-881.692,-381.962)" width="10" x="1616.43500773112" y="700.2643269339985"/></g>
  <g id="274">
   <use class="kv10" height="20" transform="rotate(180,1396.28,722.264) scale(2.2,2.2) translate(-755.605,-381.962)" width="10" x="1385.276520208879" xlink:href="#Breaker:小车母联_0" y="700.264327916232" zvalue="535"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925093228548" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473925093228548"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1396.28,722.264) scale(2.2,2.2) translate(-755.605,-381.962)" width="10" x="1385.276520208879" y="700.264327916232"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="175">
   <use class="kv10" height="20" transform="rotate(0,1073.75,864.174) scale(-1.24619,1.0068) translate(-1934.15,-5.76923)" width="10" x="1067.522499048707" xlink:href="#GroundDisconnector:地刀_0" y="854.1056552300256" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453784043523" ObjectName="10kV#1电容器05510接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453784043523"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1073.75,864.174) scale(-1.24619,1.0068) translate(-1934.15,-5.76923)" width="10" x="1067.522499048707" y="854.1056552300256"/></g>
  <g id="67">
   <use class="kv35" height="20" transform="rotate(90,896.499,143.002) scale(1.24619,-1.0068) translate(-175.874,-284.971)" width="10" x="890.2682271410732" xlink:href="#GroundDisconnector:地刀_0" y="132.9342824715292" zvalue="303"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453784567811" ObjectName="35kV允姐线35167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453784567811"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,896.499,143.002) scale(1.24619,-1.0068) translate(-175.874,-284.971)" width="10" x="890.2682271410732" y="132.9342824715292"/></g>
  <g id="33">
   <use class="kv35" height="20" transform="rotate(90,896.499,271.002) scale(1.24619,-1.0068) translate(-175.874,-540.106)" width="10" x="890.2682271410732" xlink:href="#GroundDisconnector:地刀_0" y="260.9342824715292" zvalue="315"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453784829956" ObjectName="35kV允姐线35117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453784829956"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,896.499,271.002) scale(1.24619,-1.0068) translate(-175.874,-540.106)" width="10" x="890.2682271410732" y="260.9342824715292"/></g>
  <g id="92">
   <use class="kv35" height="20" transform="rotate(90,1288.5,143.002) scale(1.24619,-1.0068) translate(-253.315,-284.971)" width="10" x="1282.268227141073" xlink:href="#GroundDisconnector:地刀_0" y="132.9342824715292" zvalue="331"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453785485316" ObjectName="35kV姐弄线35267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453785485316"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1288.5,143.002) scale(1.24619,-1.0068) translate(-253.315,-284.971)" width="10" x="1282.268227141073" y="132.9342824715292"/></g>
  <g id="83">
   <use class="kv35" height="20" transform="rotate(90,1288.5,271.002) scale(1.24619,-1.0068) translate(-253.315,-540.106)" width="10" x="1282.268227141073" xlink:href="#GroundDisconnector:地刀_0" y="260.9342824715292" zvalue="342"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453785223172" ObjectName="35kV姐弄线35217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453785223172"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1288.5,271.002) scale(1.24619,-1.0068) translate(-253.315,-540.106)" width="10" x="1282.268227141073" y="260.9342824715292"/></g>
  <g id="103">
   <use class="kv35" height="20" transform="rotate(90,1081.32,235.82) scale(1.24619,-1.0068) translate(-212.386,-469.98)" width="10" x="1075.086408959255" xlink:href="#GroundDisconnector:地刀_0" y="225.752464289711" zvalue="353"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453785747460" ObjectName="35kV母线PT39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453785747460"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1081.32,235.82) scale(1.24619,-1.0068) translate(-212.386,-469.98)" width="10" x="1075.086408959255" y="225.752464289711"/></g>
  <g id="114">
   <use class="kv35" height="20" transform="rotate(90,1080.32,304.82) scale(1.24619,-1.0068) translate(-212.188,-607.514)" width="10" x="1074.086408959255" xlink:href="#GroundDisconnector:地刀_0" y="294.752464289711" zvalue="357"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453785878532" ObjectName="35kV母线PT39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453785878532"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1080.32,304.82) scale(1.24619,-1.0068) translate(-212.188,-607.514)" width="10" x="1074.086408959255" y="294.752464289711"/></g>
  <g id="115">
   <use class="kv35" height="20" transform="rotate(90,1489.41,381.184) scale(1.24619,-1.0068) translate(-293.005,-759.725)" width="10" x="1483.177318050164" xlink:href="#GroundDisconnector:地刀_0" y="371.1161006533474" zvalue="361"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453786009604" ObjectName="#2主变35kV侧30217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453786009604"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1489.41,381.184) scale(1.24619,-1.0068) translate(-293.005,-759.725)" width="10" x="1483.177318050164" y="371.1161006533474"/></g>
  <g id="129">
   <use class="kv35" height="20" transform="rotate(90,778.837,385.47) scale(1.24619,-1.0068) translate(-152.63,-768.268)" width="10" x="772.6058894787357" xlink:href="#GroundDisconnector:地刀_0" y="375.4018149390618" zvalue="371"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453786140676" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453786140676"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,778.837,385.47) scale(1.24619,-1.0068) translate(-152.63,-768.268)" width="10" x="772.6058894787357" y="375.4018149390618"/></g>
  <g id="239">
   <use class="kv10" height="20" transform="rotate(180,583.31,796.716) scale(1.24619,-1.0068) translate(-114.003,-1587.98)" width="10" x="577.0787695398509" xlink:href="#GroundDisconnector:地刀_0" y="786.6478042438745" zvalue="396"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453972197379" ObjectName="10kV姐冒线05217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453972197379"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,583.31,796.716) scale(1.24619,-1.0068) translate(-114.003,-1587.98)" width="10" x="577.0787695398509" y="786.6478042438745"/></g>
  <g id="34">
   <use class="kv10" height="20" transform="rotate(180,895.31,796.716) scale(1.24619,-1.0068) translate(-175.639,-1587.98)" width="10" x="889.0787695398509" xlink:href="#GroundDisconnector:地刀_0" y="786.6478042438745" zvalue="414"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453786796036" ObjectName="10kV丙午线05417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453786796036"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,895.31,796.716) scale(1.24619,-1.0068) translate(-175.639,-1587.98)" width="10" x="889.0787695398509" y="786.6478042438745"/></g>
  <g id="217">
   <use class="kv10" height="20" transform="rotate(180,1063.31,796.716) scale(1.24619,-1.0068) translate(-208.828,-1587.98)" width="10" x="1057.078769539851" xlink:href="#GroundDisconnector:地刀_0" y="786.6478042438745" zvalue="440"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453787254788" ObjectName="10kV#1电容器05517接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453787254788"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1063.31,796.716) scale(1.24619,-1.0068) translate(-208.828,-1587.98)" width="10" x="1057.078769539851" y="786.6478042438745"/></g>
  <g id="59">
   <use class="kv10" height="20" transform="rotate(180,739.31,796.716) scale(1.24619,-1.0068) translate(-144.821,-1587.98)" width="10" x="733.0787695398509" xlink:href="#GroundDisconnector:地刀_0" y="786.6478042438745" zvalue="496"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453787844612" ObjectName="10kV备用05317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453787844612"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,739.31,796.716) scale(1.24619,-1.0068) translate(-144.821,-1587.98)" width="10" x="733.0787695398509" y="786.6478042438745"/></g>
  <g id="245">
   <use class="kv10" height="20" transform="rotate(180,1663.31,796.716) scale(1.24619,-1.0068) translate(-327.359,-1587.98)" width="10" x="1657.078769539851" xlink:href="#GroundDisconnector:地刀_0" y="786.6478042438745" zvalue="525"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453788368900" ObjectName="10kV芒线线05617接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453788368900"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1663.31,796.716) scale(1.24619,-1.0068) translate(-327.359,-1587.98)" width="10" x="1657.078769539851" y="786.6478042438745"/></g>
  <g id="294">
   <use class="kv35" height="20" transform="rotate(90,896.499,203.002) scale(1.24619,-1.0068) translate(-175.874,-404.565)" width="10" x="890.2682271410732" xlink:href="#GroundDisconnector:地刀_0" y="192.9342824715292" zvalue="553"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453789155332" ObjectName="35kV允姐线35160接地刀闸"/>
   <cge:TPSR_Ref TObjectID="6192453789155332"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,896.499,203.002) scale(1.24619,-1.0068) translate(-175.874,-404.565)" width="10" x="890.2682271410732" y="192.9342824715292"/></g>
  <g id="297">
   <use class="kv35" height="20" transform="rotate(90,1288.5,203.002) scale(1.24619,-1.0068) translate(-253.315,-404.565)" width="10" x="1282.268227141073" xlink:href="#GroundDisconnector:地刀_0" y="192.9342824715292" zvalue="557"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453789286404" ObjectName="35kV姐弄线35260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453789286404"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1288.5,203.002) scale(1.24619,-1.0068) translate(-253.315,-404.565)" width="10" x="1282.268227141073" y="192.9342824715292"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="218">
   <use class="kv35" height="30" transform="rotate(0,1058.06,196.126) scale(-1.25,-1.25) translate(-1900.76,-349.277)" width="30" x="1039.308644621818" xlink:href="#Accessory:避雷器PT带熔断器_0" y="177.3761755485893" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453783846916" ObjectName="35kV母线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1058.06,196.126) scale(-1.25,-1.25) translate(-1900.76,-349.277)" width="30" x="1039.308644621818" y="177.3761755485893"/></g>
  <g id="855">
   <use class="kv10" height="30" transform="rotate(0,1194.86,788.995) scale(-1.25,1.25) translate(-2147.01,-154.049)" width="30" x="1176.11451266735" xlink:href="#Accessory:避雷器PT带熔断器_0" y="770.2454071156403" zvalue="91"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453787516932" ObjectName="10kVⅠ母PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1194.86,788.995) scale(-1.25,1.25) translate(-2147.01,-154.049)" width="30" x="1176.11451266735" y="770.2454071156403"/></g>
  <g id="157">
   <use class="kv10" height="26" transform="rotate(0,506.056,863.386) scale(-0.838049,0.927421) translate(-1110.88,66.6241)" width="12" x="501.0281809263651" xlink:href="#Accessory:避雷器1_0" y="851.3293000347344" zvalue="144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453972000771" ObjectName="10kV姐冒线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,506.056,863.386) scale(-0.838049,0.927421) translate(-1110.88,66.6241)" width="12" x="501.0281809263651" y="851.3293000347344"/></g>
  <g id="190">
   <use class="kv10" height="26" transform="rotate(0,990.238,864.174) scale(-0.838049,0.927421) translate(-2172.81,66.6858)" width="12" x="985.2099991081832" xlink:href="#Accessory:避雷器1_0" y="852.1171886942395" zvalue="158"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453783584772" ObjectName="10kV#1电容器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,990.238,864.174) scale(-0.838049,0.927421) translate(-2172.81,66.6858)" width="12" x="985.2099991081832" y="852.1171886942395"/></g>
  <g id="15">
   <use class="kv35" height="26" transform="rotate(90,832.306,110.466) scale(0.838049,0.927421) translate(159.87,7.70143)" width="12" x="827.2781809263649" xlink:href="#Accessory:避雷器1_0" y="98.4095937318383" zvalue="312"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453784371203" ObjectName="35kV允姐线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,832.306,110.466) scale(0.838049,0.927421) translate(159.87,7.70143)" width="12" x="827.2781809263649" y="98.4095937318383"/></g>
  <g id="35">
   <use class="kv35" height="40" transform="rotate(90,944.59,121.815) scale(1.12267,1.12267) translate(-101.372,-10.857)" width="30" x="927.7499999999998" xlink:href="#Accessory:带熔断器的线路PT1_0" y="99.36180124223597" zvalue="319"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453784895492" ObjectName="35kV允姐线3519PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,944.59,121.815) scale(1.12267,1.12267) translate(-101.372,-10.857)" width="30" x="927.7499999999998" y="99.36180124223597"/></g>
  <g id="85">
   <use class="kv35" height="26" transform="rotate(90,1224.31,110.466) scale(0.838049,0.927421) translate(235.623,7.70143)" width="12" x="1219.278180926365" xlink:href="#Accessory:避雷器1_0" y="98.4095937318383" zvalue="340"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453785288708" ObjectName="35kV姐弄线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1224.31,110.466) scale(0.838049,0.927421) translate(235.623,7.70143)" width="12" x="1219.278180926365" y="98.4095937318383"/></g>
  <g id="81">
   <use class="kv35" height="40" transform="rotate(90,1336.59,121.815) scale(1.12267,1.12267) translate(-144.205,-10.857)" width="30" x="1319.75" xlink:href="#Accessory:带熔断器的线路PT1_0" y="99.36180124223597" zvalue="345"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453785092100" ObjectName="35kV姐弄线3529PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1336.59,121.815) scale(1.12267,1.12267) translate(-144.205,-10.857)" width="30" x="1319.75" y="99.36180124223597"/></g>
  <g id="171">
   <use class="kv10" height="26" transform="rotate(270,770.878,551.895) scale(-0.838049,0.927421) translate(-1691.7,42.2472)" width="12" x="765.8496094977935" xlink:href="#Accessory:避雷器1_0" y="539.8381651604096" zvalue="375"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453786271748" ObjectName="#1主变低压侧001避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,770.878,551.895) scale(-0.838049,0.927421) translate(-1691.7,42.2472)" width="12" x="765.8496094977935" y="539.8381651604096"/></g>
  <g id="184">
   <use class="kv10" height="26" transform="rotate(270,1485.74,547.895) scale(-0.838049,0.927421) translate(-3259.56,41.9341)" width="12" x="1480.706752354936" xlink:href="#Accessory:避雷器1_0" y="535.8381651604096" zvalue="379"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453786337284" ObjectName="#2主变低压侧002避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1485.74,547.895) scale(-0.838049,0.927421) translate(-3259.56,41.9341)" width="12" x="1480.706752354936" y="535.8381651604096"/></g>
  <g id="106">
   <use class="kv10" height="26" transform="rotate(0,818.056,863.386) scale(-0.838049,0.927421) translate(-1795.17,66.6241)" width="12" x="813.0281809263649" xlink:href="#Accessory:避雷器1_0" y="851.3293000347344" zvalue="399"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453787123716" ObjectName="10kV丙午线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,818.056,863.386) scale(-0.838049,0.927421) translate(-1795.17,66.6241)" width="12" x="813.0281809263649" y="851.3293000347344"/></g>
  <g id="195">
   <use class="kv10" height="26" transform="rotate(0,662.056,863.386) scale(-0.838049,0.927421) translate(-1453.03,66.6241)" width="12" x="657.0281809263651" xlink:href="#Accessory:避雷器1_0" y="851.3293000347344" zvalue="480"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453788172292" ObjectName="10kV备用线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,662.056,863.386) scale(-0.838049,0.927421) translate(-1453.03,66.6241)" width="12" x="657.0281809263651" y="851.3293000347344"/></g>
  <g id="205">
   <use class="kv10" height="30" transform="rotate(0,1495.97,788.995) scale(-1.25,1.25) translate(-2689,-154.049)" width="30" x="1477.223684232814" xlink:href="#Accessory:避雷器PT带熔断器_0" y="770.2454071156403" zvalue="502"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453788237828" ObjectName="10kVⅡ母PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1495.97,788.995) scale(-1.25,1.25) translate(-2689,-154.049)" width="30" x="1477.223684232814" y="770.2454071156403"/></g>
  <g id="260">
   <use class="kv10" height="26" transform="rotate(0,1586.06,863.386) scale(-0.838049,0.927421) translate(-3479.59,66.6241)" width="12" x="1581.028180926365" xlink:href="#Accessory:避雷器1_0" y="851.3293000347344" zvalue="510"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453788696580" ObjectName="10kV芒线线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1586.06,863.386) scale(-0.838049,0.927421) translate(-3479.59,66.6241)" width="12" x="1581.028180926365" y="851.3293000347344"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="533">
   <use class="kv35" height="30" transform="rotate(0,617.023,238.919) scale(1.53571,-1.53571) translate(-207.74,-386.458)" width="28" x="595.5227270079906" xlink:href="#EnergyConsumer:站用变DY接地_0" y="215.8831160161404" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453789417476" ObjectName="35kV#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,617.023,238.919) scale(1.53571,-1.53571) translate(-207.74,-386.458)" width="28" x="595.5227270079906" y="215.8831160161404"/></g>
  <g id="155">
   <use class="kv10" height="30" transform="rotate(0,547.287,880.25) scale(1.25,-1.25) translate(-107.957,-1580.7)" width="12" x="539.7865295410156" xlink:href="#EnergyConsumer:负荷_0" y="861.5" zvalue="145"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453971935235" ObjectName="10kV姐冒线"/>
   <cge:TPSR_Ref TObjectID="6192453971935235"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,547.287,880.25) scale(1.25,-1.25) translate(-107.957,-1580.7)" width="12" x="539.7865295410156" y="861.5"/></g>
  <g id="105">
   <use class="kv10" height="30" transform="rotate(0,859.287,880.25) scale(1.25,-1.25) translate(-170.357,-1580.7)" width="12" x="851.7865295410157" xlink:href="#EnergyConsumer:负荷_0" y="861.5" zvalue="400"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453787058180" ObjectName="10kV丙午线"/>
   <cge:TPSR_Ref TObjectID="6192453787058180"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,859.287,880.25) scale(1.25,-1.25) translate(-170.357,-1580.7)" width="12" x="851.7865295410157" y="861.5"/></g>
  <g id="263">
   <use class="kv10" height="30" transform="rotate(0,423.38,829.159) scale(1.53571,1.53571) translate(-140.191,-281.206)" width="28" x="401.8798702738509" xlink:href="#EnergyConsumer:站用变DY接地_0" y="806.1233766233765" zvalue="464"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453787582468" ObjectName="10kV#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,423.38,829.159) scale(1.53571,1.53571) translate(-140.191,-281.206)" width="28" x="401.8798702738509" y="806.1233766233765"/></g>
  <g id="193">
   <use class="kv10" height="30" transform="rotate(0,702.478,880.25) scale(1.25,-1.25) translate(-138.996,-1580.7)" width="12" x="694.9775321903489" xlink:href="#EnergyConsumer:负荷_0" y="861.5000000000001" zvalue="481"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453788106756" ObjectName="10kV备用线"/>
   <cge:TPSR_Ref TObjectID="6192453788106756"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,702.478,880.25) scale(1.25,-1.25) translate(-138.996,-1580.7)" width="12" x="694.9775321903489" y="861.5000000000001"/></g>
  <g id="258">
   <use class="kv10" height="30" transform="rotate(0,1627.29,880.25) scale(1.25,-1.25) translate(-323.957,-1580.7)" width="12" x="1619.786529541016" xlink:href="#EnergyConsumer:负荷_0" y="861.5" zvalue="511"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453788631044" ObjectName="10kV芒线线"/>
   <cge:TPSR_Ref TObjectID="6192453788631044"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1627.29,880.25) scale(1.25,-1.25) translate(-323.957,-1580.7)" width="12" x="1619.786529541016" y="861.5"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="815">
   <use class="kv35" height="30" transform="rotate(0,1442.59,364.872) scale(0.947693,-0.6712) translate(79.2301,-913.416)" width="15" x="1435.480010535841" xlink:href="#Disconnector:刀闸_0" y="354.8043387992516" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453783781380" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453783781380"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1442.59,364.872) scale(0.947693,-0.6712) translate(79.2301,-913.416)" width="15" x="1435.480010535841" y="354.8043387992516"/></g>
  <g id="53">
   <use class="kv35" height="30" transform="rotate(0,617,296.123) scale(1,1) translate(0,0)" width="15" x="609.5000000000001" xlink:href="#Disconnector:令克_0" y="281.1233757564002" zvalue="171"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453789351940" ObjectName="35kV#1站用变3531隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453789351940"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,617,296.123) scale(1,1) translate(0,0)" width="15" x="609.5000000000001" y="281.1233757564002"/></g>
  <g id="68">
   <use class="kv35" height="30" transform="rotate(0,866.711,178.276) scale(-0.947693,0.6712) translate(-1781.65,82.3995)" width="15" x="859.6034928921569" xlink:href="#Disconnector:刀闸_0" y="168.2075673790615" zvalue="301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453784633347" ObjectName="35kV允姐线3516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453784633347"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,866.711,178.276) scale(-0.947693,0.6712) translate(-1781.65,82.3995)" width="15" x="859.6034928921569" y="168.2075673790615"/></g>
  <g id="20">
   <use class="kv35" height="30" transform="rotate(180,866.892,301.544) scale(0.947693,-0.6712) translate(47.455,-755.738)" width="15" x="859.7841334259955" xlink:href="#Disconnector:刀闸_0" y="291.4763051659212" zvalue="306"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453784436739" ObjectName="35kV允姐线3511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453784436739"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,866.892,301.544) scale(0.947693,-0.6712) translate(47.455,-755.738)" width="15" x="859.7841334259955" y="291.4763051659212"/></g>
  <g id="46">
   <use class="kv35" height="30" transform="rotate(90,908.022,122.225) scale(0.947693,0.6712) translate(49.7252,54.9419)" width="15" x="900.914042245145" xlink:href="#Disconnector:刀闸_0" y="112.1566313297521" zvalue="321"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453784961028" ObjectName="35kV允姐线3519隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453784961028"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,908.022,122.225) scale(0.947693,0.6712) translate(49.7252,54.9419)" width="15" x="900.914042245145" y="112.1566313297521"/></g>
  <g id="94">
   <use class="kv35" height="30" transform="rotate(0,1258.71,178.276) scale(-0.947693,0.6712) translate(-2587.29,82.3995)" width="15" x="1251.603492892157" xlink:href="#Disconnector:刀闸_0" y="168.2075673790615" zvalue="329"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453785550852" ObjectName="35kV姐弄线3526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453785550852"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1258.71,178.276) scale(-0.947693,0.6712) translate(-2587.29,82.3995)" width="15" x="1251.603492892157" y="168.2075673790615"/></g>
  <g id="90">
   <use class="kv35" height="30" transform="rotate(180,1258.89,301.544) scale(0.947693,-0.6712) translate(69.0911,-755.738)" width="15" x="1251.784133425996" xlink:href="#Disconnector:刀闸_0" y="291.4763051659212" zvalue="334"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453785354244" ObjectName="35kV姐弄线3521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453785354244"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1258.89,301.544) scale(0.947693,-0.6712) translate(69.0911,-755.738)" width="15" x="1251.784133425996" y="291.4763051659212"/></g>
  <g id="80">
   <use class="kv35" height="30" transform="rotate(90,1300.02,122.225) scale(0.947693,0.6712) translate(71.3613,54.9419)" width="15" x="1292.914042245145" xlink:href="#Disconnector:刀闸_0" y="112.1566313297521" zvalue="346"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453785026564" ObjectName="35kV姐弄线3529隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453785026564"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1300.02,122.225) scale(0.947693,0.6712) translate(71.3613,54.9419)" width="15" x="1292.914042245145" y="112.1566313297521"/></g>
  <g id="133">
   <use class="kv35" height="30" transform="rotate(0,731.016,368.158) scale(0.947693,-0.6712) translate(39.9555,-921.597)" width="15" x="723.9085819644126" xlink:href="#Disconnector:刀闸_0" y="358.0900530849659" zvalue="367"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453786206212" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453786206212"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,731.016,368.158) scale(0.947693,-0.6712) translate(39.9555,-921.597)" width="15" x="723.9085819644126" y="358.0900530849659"/></g>
  <g id="216">
   <use class="kv10" height="30" transform="rotate(180,547.394,798.28) scale(-0.947693,0.6712) translate(-1125.39,386.12)" width="15" x="540.2866496155923" xlink:href="#Disconnector:刀闸_0" y="788.2119653011532" zvalue="392"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453972066307" ObjectName="10kV姐冒线0526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453972066307"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,547.394,798.28) scale(-0.947693,0.6712) translate(-1125.39,386.12)" width="15" x="540.2866496155923" y="788.2119653011532"/></g>
  <g id="43">
   <use class="kv10" height="30" transform="rotate(180,859.394,798.28) scale(-0.947693,0.6712) translate(-1766.61,386.12)" width="15" x="852.2866496155923" xlink:href="#Disconnector:刀闸_0" y="788.2119653011532" zvalue="410"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453786861572" ObjectName="10kV丙午线0546隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453786861572"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,859.394,798.28) scale(-0.947693,0.6712) translate(-1766.61,386.12)" width="15" x="852.2866496155923" y="788.2119653011532"/></g>
  <g id="220">
   <use class="kv10" height="30" transform="rotate(180,1027.39,798.28) scale(-0.947693,0.6712) translate(-2111.89,386.12)" width="15" x="1020.286649615592" xlink:href="#Disconnector:刀闸_0" y="788.2119653011532" zvalue="437"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453787320324" ObjectName="10kV#1电容器0556隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453787320324"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1027.39,798.28) scale(-0.947693,0.6712) translate(-2111.89,386.12)" width="15" x="1020.286649615592" y="788.2119653011532"/></g>
  <g id="269">
   <use class="kv10" height="36" transform="rotate(180,423.521,719.382) scale(1.36556,1.0621) translate(-110.817,-40.9451)" width="14" x="413.9620420677569" xlink:href="#Disconnector:联体小车刀闸2_0" y="700.2643269339985" zvalue="471"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450329968645" ObjectName="10kV#2站用变0511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450329968645"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(180,423.521,719.382) scale(1.36556,1.0621) translate(-110.817,-40.9451)" width="14" x="413.9620420677569" y="700.2643269339985"/></g>
  <g id="121">
   <use class="kv10" height="30" transform="rotate(180,702.394,797.28) scale(-0.947693,0.6712) translate(-1443.95,385.63)" width="15" x="695.2866496155923" xlink:href="#Disconnector:刀闸_0" y="787.2119653011532" zvalue="492"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453787910148" ObjectName="10kV备用0536隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453787910148"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,702.394,797.28) scale(-0.947693,0.6712) translate(-1443.95,385.63)" width="15" x="695.2866496155923" y="787.2119653011532"/></g>
  <g id="248">
   <use class="kv10" height="30" transform="rotate(180,1627.2,797.28) scale(-0.947693,0.6712) translate(-3344.61,385.63)" width="15" x="1620.095646966259" xlink:href="#Disconnector:刀闸_0" y="787.2119653011532" zvalue="521"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453788434436" ObjectName="10kV芒线线0566隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453788434436"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1627.2,797.28) scale(-0.947693,0.6712) translate(-3344.61,385.63)" width="15" x="1620.095646966259" y="787.2119653011532"/></g>
  <g id="188">
   <use class="kv10" height="26" transform="rotate(0,1196.25,712.224) scale(1.42857,1.42857) translate(-355.875,-208.096)" width="14" x="1186.25" xlink:href="#Disconnector:联体手车刀闸_0" y="693.6525974025974" zvalue="528"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453788762116" ObjectName="10kVⅠ母PT0901手车"/>
   <cge:TPSR_Ref TObjectID="6192453788762116"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1196.25,712.224) scale(1.42857,1.42857) translate(-355.875,-208.096)" width="14" x="1186.25" y="693.6525974025974"/></g>
  <g id="272">
   <use class="kv10" height="26" transform="rotate(0,1498.07,712.224) scale(1.42857,1.42857) translate(-446.42,-208.096)" width="14" x="1488.068181818182" xlink:href="#Disconnector:联体手车刀闸_0" y="693.6525974025974" zvalue="532"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453788827652" ObjectName="10kVⅡ母PT0902手车"/>
   <cge:TPSR_Ref TObjectID="6192453788827652"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1498.07,712.224) scale(1.42857,1.42857) translate(-446.42,-208.096)" width="14" x="1488.068181818182" y="693.6525974025974"/></g>
  <g id="273">
   <use class="kv10" height="26" transform="rotate(0,1272.25,711.315) scale(1.42857,1.42857) translate(-378.675,-207.823)" width="14" x="1262.25" xlink:href="#Disconnector:联体手车刀闸_0" y="692.7435064935065" zvalue="534"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453788893188" ObjectName="10kV分段0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453788893188"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1272.25,711.315) scale(1.42857,1.42857) translate(-378.675,-207.823)" width="14" x="1262.25" y="692.7435064935065"/></g>
  <g id="304">
   <use class="kv35" height="30" transform="rotate(180,1054.39,274.878) scale(0.947693,-0.6712) translate(57.8039,-689.341)" width="15" x="1047.284133425996" xlink:href="#Disconnector:刀闸_0" y="264.8096384992546" zvalue="567"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453789483012" ObjectName="35kV母线PT3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453789483012"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1054.39,274.878) scale(0.947693,-0.6712) translate(57.8039,-689.341)" width="15" x="1047.284133425996" y="264.8096384992546"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="98">
   <path class="kv35" d="M 1442.65 354.98 L 1442.65 331.34" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="815@1" LinkObjectIDznd="128@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1442.65 354.98 L 1442.65 331.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv35" d="M 1442.48 397.78 L 1442.48 374.61" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="816@1" LinkObjectIDznd="815@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1442.48 397.78 L 1442.48 374.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv10" d="M 1027.48 836.62 L 1073.66 836.62 L 1073.69 854.36" stroke-width="1" zvalue="169"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139" LinkObjectIDznd="175@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1027.48 836.62 L 1073.66 836.62 L 1073.69 854.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv10" d="M 1027.48 838.29 L 990.21 838.29 L 990.21 852.7" stroke-width="1" zvalue="170"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139" LinkObjectIDznd="190@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1027.48 838.29 L 990.21 838.29 L 990.21 852.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv35" d="M 616.92 331.34 L 616.92 308.37" stroke-width="1" zvalue="172"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@2" LinkObjectIDznd="53@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 616.92 331.34 L 616.92 308.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv35" d="M 617.08 282.87 L 617.16 261.04" stroke-width="1" zvalue="173"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="533@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 617.08 282.87 L 617.16 261.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv35" d="M 866.65 230.75 L 866.65 188.17" stroke-width="1" zvalue="305"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="68@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.65 230.75 L 866.65 188.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv35" d="M 866.63 168.54 L 866.63 96.3" stroke-width="1" zvalue="308"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="70@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.63 168.54 L 866.63 96.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv35" d="M 843.78 110.49 L 866.63 110.49" stroke-width="1" zvalue="309"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 843.78 110.49 L 866.63 110.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv35" d="M 866.81 256.64 L 866.81 291.81" stroke-width="1" zvalue="310"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@1" LinkObjectIDznd="20@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.81 256.64 L 866.81 291.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv35" d="M 886.68 143.06 L 866.63 143.06" stroke-width="1" zvalue="311"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 886.68 143.06 L 866.63 143.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv35" d="M 866.83 311.44 L 866.83 331.34" stroke-width="1" zvalue="313"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@1" LinkObjectIDznd="128@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.83 311.44 L 866.83 331.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv35" d="M 886.68 271.06 L 866.81 271.06" stroke-width="1" zvalue="317"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="17" MaxPinNum="2"/>
   </metadata>
  <path d="M 886.68 271.06 L 866.81 271.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv35" d="M 917.76 122.31 L 923.82 122.31" stroke-width="1" zvalue="322"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 917.76 122.31 L 923.82 122.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv35" d="M 898.13 122.28 L 866.63 122.28" stroke-width="1" zvalue="323"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@1" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.13 122.28 L 866.63 122.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv35" d="M 1258.65 230.75 L 1258.65 188.17" stroke-width="1" zvalue="333"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="94@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1258.65 230.75 L 1258.65 188.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv35" d="M 1258.63 168.54 L 1258.63 96.3" stroke-width="1" zvalue="336"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="100@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1258.63 168.54 L 1258.63 96.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv35" d="M 1235.78 110.49 L 1258.63 110.49" stroke-width="1" zvalue="337"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@0" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 1235.78 110.49 L 1258.63 110.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv35" d="M 1258.81 256.64 L 1258.81 291.81" stroke-width="1" zvalue="338"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@1" LinkObjectIDznd="90@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1258.81 256.64 L 1258.81 291.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv35" d="M 1278.68 143.06 L 1258.63 143.06" stroke-width="1" zvalue="339"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 1278.68 143.06 L 1258.63 143.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv35" d="M 1258.83 311.44 L 1258.83 331.34" stroke-width="1" zvalue="341"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@1" LinkObjectIDznd="128@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1258.83 311.44 L 1258.83 331.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv35" d="M 1278.68 271.06 L 1258.81 271.06" stroke-width="1" zvalue="344"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@0" LinkObjectIDznd="87" MaxPinNum="2"/>
   </metadata>
  <path d="M 1278.68 271.06 L 1258.81 271.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv35" d="M 1309.76 122.31 L 1315.82 122.31" stroke-width="1" zvalue="348"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="81@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1309.76 122.31 L 1315.82 122.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv35" d="M 1290.13 122.28 L 1258.63 122.28" stroke-width="1" zvalue="349"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@1" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 1290.13 122.28 L 1258.63 122.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv35" d="M 1071.5 235.88 L 1054.31 235.88" stroke-width="1" zvalue="355"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="305" MaxPinNum="2"/>
   </metadata>
  <path d="M 1071.5 235.88 L 1054.31 235.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv35" d="M 1070.5 304.88 L 1054.33 304.88" stroke-width="1" zvalue="359"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="306" MaxPinNum="2"/>
   </metadata>
  <path d="M 1070.5 304.88 L 1054.33 304.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="kv35" d="M 1479.59 381.25 L 1442.48 381.25" stroke-width="1" zvalue="362"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="97" MaxPinNum="2"/>
   </metadata>
  <path d="M 1479.59 381.25 L 1442.48 381.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="kv35" d="M 731.07 358.26 L 731.07 331.34" stroke-width="1" zvalue="369"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@1" LinkObjectIDznd="128@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 731.07 358.26 L 731.07 331.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv35" d="M 732.1 402.06 L 732.1 377.89" stroke-width="1" zvalue="370"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@1" LinkObjectIDznd="133@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 732.1 402.06 L 732.1 377.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv35" d="M 769.02 385.53 L 732.1 385.53" stroke-width="1" zvalue="373"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="131" MaxPinNum="2"/>
   </metadata>
  <path d="M 769.02 385.53 L 732.1 385.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv10" d="M 506.03 851.92 L 506.03 834.72 L 547.48 834.72" stroke-width="1" zvalue="390"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="224" MaxPinNum="2"/>
   </metadata>
  <path d="M 506.03 851.92 L 506.03 834.72 L 547.48 834.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="kv10" d="M 547.48 808.02 L 547.48 863.38" stroke-width="1" zvalue="394"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="216@0" LinkObjectIDznd="155@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 547.48 808.02 L 547.48 863.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv10" d="M 583.25 786.9 L 583.22 773.68 L 547.45 773.68" stroke-width="1" zvalue="397"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="239@0" LinkObjectIDznd="298" MaxPinNum="2"/>
   </metadata>
  <path d="M 583.25 786.9 L 583.22 773.68 L 547.45 773.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv10" d="M 818.03 851.92 L 818.03 834.72 L 859.48 834.72" stroke-width="1" zvalue="409"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="39" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.03 851.92 L 818.03 834.72 L 859.48 834.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv10" d="M 859.48 808.02 L 859.48 863.38" stroke-width="1" zvalue="413"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="105@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 859.48 808.02 L 859.48 863.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv10" d="M 895.25 786.9 L 895.22 773.68 L 859.45 773.68" stroke-width="1" zvalue="415"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@0" LinkObjectIDznd="301" MaxPinNum="2"/>
   </metadata>
  <path d="M 895.25 786.9 L 895.22 773.68 L 859.45 773.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="kv10" d="M 1063.25 786.9 L 1063.22 773.68 L 1027.45 773.68" stroke-width="1" zvalue="441"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@0" LinkObjectIDznd="310" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.25 786.9 L 1063.22 773.68 L 1027.45 773.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="kv10" d="M 1027.5 909.25 L 1027.5 933.45 L 1052 933.45 L 1052 836.62" stroke-width="1" zvalue="444"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="55" MaxPinNum="2"/>
   </metadata>
  <path d="M 1027.5 909.25 L 1027.5 933.45 L 1052 933.45 L 1052 836.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv10" d="M 662.03 851.92 L 662.03 835.2 L 702.48 835.2" stroke-width="1" zvalue="491"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="60" MaxPinNum="2"/>
   </metadata>
  <path d="M 662.03 851.92 L 662.03 835.2 L 702.48 835.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv10" d="M 703.44 742.61 L 703.44 787.38" stroke-width="1" zvalue="494"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="121@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 703.44 742.61 L 703.44 787.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv10" d="M 702.48 807.02 L 702.48 863.38" stroke-width="1" zvalue="495"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@0" LinkObjectIDznd="193@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 702.48 807.02 L 702.48 863.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv10" d="M 739.25 786.9 L 739.22 773.68 L 703.44 773.68" stroke-width="1" zvalue="498"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="119" MaxPinNum="2"/>
   </metadata>
  <path d="M 739.25 786.9 L 739.22 773.68 L 703.44 773.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv10" d="M 1497.39 771.58 L 1497.39 728.39" stroke-width="1" zvalue="506"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="272@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1497.39 771.58 L 1497.39 728.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv10" d="M 1498.05 696.08 L 1498.05 665.31" stroke-width="1" zvalue="508"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="183@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1498.05 696.08 L 1498.05 665.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="249">
   <path class="kv10" d="M 1586.03 851.92 L 1586.03 835.2 L 1627.29 835.2" stroke-width="1" zvalue="520"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="260@0" LinkObjectIDznd="246" MaxPinNum="2"/>
   </metadata>
  <path d="M 1586.03 851.92 L 1586.03 835.2 L 1627.29 835.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="246">
   <path class="kv10" d="M 1627.29 807.02 L 1627.29 863.38" stroke-width="1" zvalue="524"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@0" LinkObjectIDznd="258@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1627.29 807.02 L 1627.29 863.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv10" d="M 1663.25 786.9 L 1663.22 773.68 L 1627.44 773.68" stroke-width="1" zvalue="526"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="245@0" LinkObjectIDznd="312" MaxPinNum="2"/>
   </metadata>
  <path d="M 1663.25 786.9 L 1663.22 773.68 L 1627.44 773.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="261">
   <path class="kv10" d="M 1196.23 696.08 L 1196.23 665.31" stroke-width="1" zvalue="529"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@0" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1196.23 696.08 L 1196.23 665.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="262">
   <path class="kv10" d="M 1196.28 771.58 L 1196.28 728.39" stroke-width="1" zvalue="530"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="855@0" LinkObjectIDznd="188@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1196.28 771.58 L 1196.28 728.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="286">
   <path class="kv10" d="M 1272.23 695.17 L 1272.23 665.31" stroke-width="1" zvalue="548"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@0" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1272.23 695.17 L 1272.23 665.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="293">
   <path class="kv35" d="M 886.68 203.06 L 866.65 203.06" stroke-width="1" zvalue="555"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="294@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 886.68 203.06 L 866.65 203.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="296">
   <path class="kv35" d="M 1278.68 203.06 L 1258.65 203.06" stroke-width="1" zvalue="559"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="297@0" LinkObjectIDznd="91" MaxPinNum="2"/>
   </metadata>
  <path d="M 1278.68 203.06 L 1258.65 203.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="305">
   <path class="kv35" d="M 1054.31 213.54 L 1054.31 265.14" stroke-width="1" zvalue="568"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@0" LinkObjectIDznd="304@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1054.31 213.54 L 1054.31 265.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="306">
   <path class="kv35" d="M 1054.33 284.77 L 1054.33 331.34" stroke-width="1" zvalue="569"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="304@1" LinkObjectIDznd="128@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1054.33 284.77 L 1054.33 331.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="257">
   <path class="kv10" d="M 732.11 615.35 L 732.11 665.31" stroke-width="1" zvalue="601"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="461@0" LinkObjectIDznd="166@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 732.11 615.35 L 732.11 665.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="kv10" d="M 1442.34 606.96 L 1442.34 665.31" stroke-width="1" zvalue="605"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="183@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1442.34 606.96 L 1442.34 665.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="282">
   <path class="kv10" d="M 423.52 701.33 L 423.52 665.31" stroke-width="1" zvalue="606"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="269@1" LinkObjectIDznd="166@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 423.52 701.33 L 423.52 665.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="283">
   <path class="kv10" d="M 423.52 737.44 L 423.52 807.04" stroke-width="1" zvalue="607"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="269@0" LinkObjectIDznd="263@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 423.52 737.44 L 423.52 807.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="291">
   <path class="kv10" d="M 547.44 702.46 L 547.44 665.31" stroke-width="1" zvalue="608"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@1" LinkObjectIDznd="166@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 547.44 702.46 L 547.44 665.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="298">
   <path class="kv10" d="M 547.44 742.61 L 547.45 788.38" stroke-width="1" zvalue="609"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@0" LinkObjectIDznd="216@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 547.44 742.61 L 547.45 788.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="300">
   <path class="kv10" d="M 703.44 702.46 L 703.44 665.31" stroke-width="1" zvalue="610"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@1" LinkObjectIDznd="166@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 703.44 702.46 L 703.44 665.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="301">
   <path class="kv10" d="M 859.44 742.61 L 859.45 788.38" stroke-width="1" zvalue="611"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="43@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 859.44 742.61 L 859.45 788.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="302">
   <path class="kv10" d="M 859.44 702.46 L 859.44 665.31" stroke-width="1" zvalue="612"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@1" LinkObjectIDznd="166@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 859.44 702.46 L 859.44 665.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="303">
   <path class="kv10" d="M 1027.44 702.46 L 1027.44 665.31" stroke-width="1" zvalue="613"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@1" LinkObjectIDznd="166@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1027.44 702.46 L 1027.44 665.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="310">
   <path class="kv10" d="M 1027.44 742.61 L 1027.45 788.38" stroke-width="1" zvalue="614"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@0" LinkObjectIDznd="220@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1027.44 742.61 L 1027.45 788.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv10" d="M 1627.44 702.46 L 1627.44 665.31" stroke-width="1" zvalue="615"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="254@1" LinkObjectIDznd="183@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1627.44 702.46 L 1627.44 665.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="kv10" d="M 1627.44 742.61 L 1627.44 787.38" stroke-width="1" zvalue="616"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="254@0" LinkObjectIDznd="248@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1627.44 742.61 L 1627.44 787.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="313">
   <path class="kv10" d="M 1396.28 742.06 L 1396.28 777.95 L 1272.28 777.95 L 1272.28 727.48" stroke-width="1" zvalue="617"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="273@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1396.28 742.06 L 1396.28 777.95 L 1272.28 777.95 L 1272.28 727.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="314">
   <path class="kv10" d="M 1396.28 665.31 L 1396.28 703.01" stroke-width="1" zvalue="618"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@3" LinkObjectIDznd="274@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1396.28 665.31 L 1396.28 703.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv10" d="M 1027.48 808.02 L 1027.48 868.45" stroke-width="1" zvalue="740"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@0" LinkObjectIDznd="202@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1027.48 808.02 L 1027.48 868.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="360">
   <path class="kv35" d="M 1442.65 436.34 L 1442.64 423.66" stroke-width="1" zvalue="804"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="822@0" LinkObjectIDznd="816@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1442.65 436.34 L 1442.64 423.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="361">
   <path class="kv10" d="M 1442.61 525.14 L 1442.61 566.81" stroke-width="1" zvalue="805"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="822@1" LinkObjectIDznd="230@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1442.61 525.14 L 1442.61 566.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="362">
   <path class="kv10" d="M 1474.27 547.92 L 1442.61 547.92" stroke-width="1" zvalue="806"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="361" MaxPinNum="2"/>
   </metadata>
  <path d="M 1474.27 547.92 L 1442.61 547.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="363">
   <path class="kv10" d="M 732.11 575.2 L 732.11 533.47" stroke-width="1" zvalue="807"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="461@1" LinkObjectIDznd="290@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 732.11 575.2 L 732.11 533.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="364">
   <path class="kv10" d="M 759.41 551.92 L 732.11 551.92" stroke-width="1" zvalue="808"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="363" MaxPinNum="2"/>
   </metadata>
  <path d="M 759.41 551.92 L 732.11 551.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="365">
   <path class="kv35" d="M 732.25 444.67 L 732.25 427.95" stroke-width="1" zvalue="809"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="290@0" LinkObjectIDznd="170@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 732.25 444.67 L 732.25 427.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="822">
   <g id="8220">
    <use class="kv35" height="30" transform="rotate(0,1442.61,480.545) scale(3.17827,3.175) translate(-962.576,-296.567)" width="24" x="1404.47" xlink:href="#PowerTransformer2:可调不带中性点_0" y="432.92" zvalue="84"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874565025794" ObjectName="35"/>
    </metadata>
   </g>
   <g id="8221">
    <use class="kv10" height="30" transform="rotate(0,1442.61,480.545) scale(3.17827,3.175) translate(-962.576,-296.567)" width="24" x="1404.47" xlink:href="#PowerTransformer2:可调不带中性点_1" y="432.92" zvalue="84"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874565091330" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399523696642" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399523696642"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1442.61,480.545) scale(3.17827,3.175) translate(-962.576,-296.567)" width="24" x="1404.47" y="432.92"/></g>
  <g id="290">
   <g id="2900">
    <use class="kv35" height="30" transform="rotate(0,732.22,488.875) scale(3.17827,3.175) translate(-475.698,-302.274)" width="24" x="694.08" xlink:href="#PowerTransformer2:可调不带中性点_0" y="441.25" zvalue="550"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874565156866" ObjectName="35"/>
    </metadata>
   </g>
   <g id="2901">
    <use class="kv10" height="30" transform="rotate(0,732.22,488.875) scale(3.17827,3.175) translate(-475.698,-302.274)" width="24" x="694.08" xlink:href="#PowerTransformer2:可调不带中性点_1" y="441.25" zvalue="550"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874565222402" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399523762178" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399523762178"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,732.22,488.875) scale(3.17827,3.175) translate(-475.698,-302.274)" width="24" x="694.08" y="441.25"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="202">
   <use class="kv10" height="40" transform="rotate(270,1027,887.918) scale(-0.617996,0.790693) translate(-2702.18,230.858)" width="70" x="1005.365800865801" xlink:href="#Compensator:35kV并联电容电抗器111_0" y="872.1038961038956" zvalue="165"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453783519236" ObjectName="10kV#1电容器"/>
   <cge:TPSR_Ref TObjectID="6192453783519236"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,1027,887.918) scale(-0.617996,0.790693) translate(-2702.18,230.858)" width="70" x="1005.365800865801" y="872.1038961038956"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="70">
   <use class="kv35" height="30" transform="rotate(0,866.628,88.5317) scale(1.98323,0.522926) translate(-426.208,73.6126)" width="7" x="859.6867041796982" xlink:href="#ACLineSegment:线路_0" y="80.68776459659705" zvalue="297"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249321242629" ObjectName="35kV允姐线"/>
   <cge:TPSR_Ref TObjectID="8444249321242629_5066549679030273"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,866.628,88.5317) scale(1.98323,0.522926) translate(-426.208,73.6126)" width="7" x="859.6867041796982" y="80.68776459659705"/></g>
  <g id="100">
   <use class="kv35" height="30" transform="rotate(0,1258.63,88.5317) scale(1.98323,0.522926) translate(-620.551,73.6126)" width="7" x="1251.686704179698" xlink:href="#ACLineSegment:线路_0" y="80.68776459659705" zvalue="325"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249301581829" ObjectName="35kV姐弄线汇流T线(姐帽)"/>
   <cge:TPSR_Ref TObjectID="8444249301581829_5066549679030273"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1258.63,88.5317) scale(1.98323,0.522926) translate(-620.551,73.6126)" width="7" x="1251.686704179698" y="80.68776459659705"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,340.375,317) scale(0.708333,0.665547) translate(135.779,154.283)" width="30" x="329.75" xlink:href="#State:红绿圆(方形)_0" y="307.02" zvalue="678"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374923333633" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,340.375,317) scale(0.708333,0.665547) translate(135.779,154.283)" width="30" x="329.75" y="307.02"/></g>
  <g id="1035">
   <use height="30" transform="rotate(0,244.75,317) scale(0.708333,0.665547) translate(96.4044,154.283)" width="30" x="234.13" xlink:href="#State:红绿圆(方形)_0" y="307.02" zvalue="679"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562957610975239" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,244.75,317) scale(0.708333,0.665547) translate(96.4044,154.283)" width="30" x="234.13" y="307.02"/></g>
  <g id="1125">
   <use height="30" transform="rotate(0,289.235,388.75) scale(0.910937,0.8) translate(24.7161,94.1875)" width="80" x="252.8" xlink:href="#State:间隔模板_0" y="376.75" zvalue="829"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629500525707266" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,289.235,388.75) scale(0.910937,0.8) translate(24.7161,94.1875)" width="80" x="252.8" y="376.75"/></g>
  <g id="1131">
   <use height="30" transform="rotate(0,86.2857,318.039) scale(0.910937,0.8) translate(4.87367,76.5097)" width="80" x="49.85" xlink:href="#State:间隔模板_0" y="306.04" zvalue="830"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629500369797122" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,86.2857,318.039) scale(0.910937,0.8) translate(4.87367,76.5097)" width="80" x="49.85" y="306.04"/></g>
 </g>
</svg>