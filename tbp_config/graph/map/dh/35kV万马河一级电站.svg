<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549585772546" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.45181617405996" x2="1.714850492606708" y1="5.276370517142858" y2="14.05696281619048"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.583333333333333" x2="8.583333333333334" y1="5.166666666666667" y2="14.16666666666667"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:20210316_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="6.66666666666667" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="6.727157046327149" y2="14.02109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="6.734473004260392" y2="14.02109851866368"/>
  </symbol>
  <symbol id="Disconnector:20210316_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="0.25" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="0.7271570463271502" y2="8.021098518663681"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="0.7344730042603906" y2="8.021098518663679"/>
  </symbol>
  <symbol id="Disconnector:20210316_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:五卷PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="25.16666666666667" y2="1"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,14) scale(1,1) translate(0,0)" width="6" x="7" y="7"/>
   <path d="M 5.11667 33.0667 L 5.11667 37.0667 L 8.11667 35.0667 L 5.11667 33.0667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.66666666666667" x2="9.999999999999998" y1="4" y2="4"/>
   <ellipse cx="13.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <ellipse cx="13.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.44906826265991" x2="6.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659912" x2="8.849068262659907" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659907" x2="4.049068262659912" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="36.96252026861409" y2="34.48744029990989"/>
   <ellipse cx="6.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.11944444444444" x2="29.63333333333333" y1="28.26666666666667" y2="28.26666666666667"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,27.29,13.93) scale(1,1) translate(0,0)" width="7.58" x="23.5" y="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="3.93333333333333" y2="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="5.85" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="24.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="30.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.16111111111111" x2="30.27222222222222" y1="26.88508771929826" y2="26.88508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="21.85" y2="25.45"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.88333333333333" x2="31.55" y1="25.50350877192984" y2="25.50350877192984"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV万马河一级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="52.25" xlink:href="logo.png" y="34.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.25,64.75) scale(1,1) translate(0,0)" writing-mode="lr" x="201.25" xml:space="preserve" y="68.25" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,197.75,64.4403) scale(1,1) translate(0,0)" writing-mode="lr" x="197.75" xml:space="preserve" y="73.44" zvalue="3">35kV万马河一级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="3" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.125,397.5) scale(1,1) translate(0,0)" width="72.88" x="48.69" y="385.5" zvalue="171"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.125,397.5) scale(1,1) translate(0,0)" writing-mode="lr" x="85.13" xml:space="preserve" y="402" zvalue="171">信号一览</text>
  <line fill="none" id="29" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="390.25" x2="390.25" y1="14.75" y2="1044.75" zvalue="4"/>
  <line fill="none" id="27" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.25000000000045" x2="387.25" y1="138.6204926140824" y2="138.6204926140824" zvalue="6"/>
  <line fill="none" id="25" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.25000000000068" x2="387.2500000000002" y1="475.3347783283681" y2="475.3347783283681" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="20.25" x2="110.25" y1="923.75" y2="923.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="20.25" x2="110.25" y1="962.9132999999999" y2="962.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="20.25" x2="20.25" y1="923.75" y2="962.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="110.25" x2="110.25" y1="923.75" y2="962.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="110.25" x2="380.25" y1="923.75" y2="923.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="110.25" x2="380.25" y1="962.9132999999999" y2="962.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="110.25" x2="110.25" y1="923.75" y2="962.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="380.25" x2="380.25" y1="923.75" y2="962.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="20.25" x2="110.25" y1="962.91327" y2="962.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="20.25" x2="110.25" y1="990.83167" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="20.25" x2="20.25" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="110.25" x2="110.25" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="110.25" x2="200.25" y1="962.91327" y2="962.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="110.25" x2="200.25" y1="990.83167" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="110.25" x2="110.25" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="200.25" x2="200.25" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="200.2500000000001" x2="290.2500000000001" y1="962.91327" y2="962.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="200.2500000000001" x2="290.2500000000001" y1="990.83167" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="200.2500000000001" x2="200.2500000000001" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="290.2500000000001" x2="290.2500000000001" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="290.25" x2="380.25" y1="962.91327" y2="962.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="290.25" x2="380.25" y1="990.83167" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="290.25" x2="290.25" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="380.25" x2="380.25" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="20.25" x2="110.25" y1="990.8316" y2="990.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="20.25" x2="110.25" y1="1018.75" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="20.25" x2="20.25" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="110.25" x2="110.25" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="110.25" x2="200.25" y1="990.8316" y2="990.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="110.25" x2="200.25" y1="1018.75" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="110.25" x2="110.25" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="200.25" x2="200.25" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="200.2500000000001" x2="290.2500000000001" y1="990.8316" y2="990.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="200.2500000000001" x2="290.2500000000001" y1="1018.75" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="200.2500000000001" x2="200.2500000000001" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="290.2500000000001" x2="290.2500000000001" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="290.25" x2="380.25" y1="990.8316" y2="990.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="290.25" x2="380.25" y1="1018.75" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="290.25" x2="290.25" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="380.25" x2="380.25" y1="990.8316" y2="1018.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,65.25,943.75) scale(1,1) translate(0,0)" writing-mode="lr" x="65.25" xml:space="preserve" y="949.75" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.25,977.75) scale(1,1) translate(0,0)" writing-mode="lr" x="62.25" xml:space="preserve" y="983.75" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,244.25,977.75) scale(1,1) translate(0,0)" writing-mode="lr" x="244.25" xml:space="preserve" y="983.75" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,61.25,1005.75) scale(1,1) translate(0,0)" writing-mode="lr" x="61.25" xml:space="preserve" y="1011.75" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,243.25,1005.75) scale(1,1) translate(0,0)" writing-mode="lr" x="243.25" xml:space="preserve" y="1011.75" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.75,638.25) scale(1,1) translate(0,0)" writing-mode="lr" x="85.75" xml:space="preserve" y="642.75" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,536.75,607) scale(1,1) translate(0,0)" writing-mode="lr" x="536.75" xml:space="preserve" y="611.5" zvalue="34">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,741.31,688.615) scale(1,1) translate(0,0)" writing-mode="lr" x="741.3099999999999" xml:space="preserve" y="693.12" zvalue="37">641</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" x="719.3515625" xml:space="preserve" y="930.1962240134186" zvalue="41">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="719.3515625" xml:space="preserve" y="946.1962240134186" zvalue="41">5000KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,594.25,825.625) scale(1,1) translate(0,0)" writing-mode="lr" x="594.25" xml:space="preserve" y="830.13" zvalue="61">6911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,759.837,820.625) scale(1,1) translate(0,0)" writing-mode="lr" x="759.84" xml:space="preserve" y="825.13" zvalue="64">6912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1464.06,688.615) scale(1,1) translate(0,0)" writing-mode="lr" x="1464.06" xml:space="preserve" y="693.12" zvalue="70">642</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" x="1441.8203125" xml:space="preserve" y="930.1962240134186" zvalue="74">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1441.8203125" xml:space="preserve" y="946.1962240134186" zvalue="74">5000KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1318.25,825.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1318.25" xml:space="preserve" y="830.13" zvalue="80">6921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1483.84,820.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1483.84" xml:space="preserve" y="825.13" zvalue="84">6922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1092.51,724.032) scale(1,1) translate(0,0)" writing-mode="lr" x="1092.51" xml:space="preserve" y="728.53" zvalue="89">6901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1064.14,832.385) scale(1,1) translate(0,0)" writing-mode="lr" x="1064.14" xml:space="preserve" y="836.88" zvalue="96">6.3kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,736.938,430.75) scale(1,1) translate(0,0)" writing-mode="lr" x="736.9400000000001" xml:space="preserve" y="435.25" zvalue="99">站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,705.5,557.743) scale(1,1) translate(0,-1.21956e-13)" writing-mode="lr" x="705.5" xml:space="preserve" y="562.24" zvalue="104">681</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,977.744,283.972) scale(1,1) translate(0,0)" writing-mode="lr" x="977.74" xml:space="preserve" y="288.47" zvalue="111">3416</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,980.488,334.556) scale(1,1) translate(0,0)" writing-mode="lr" x="980.49" xml:space="preserve" y="339.06" zvalue="113">341</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1080.91,247.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1080.91" xml:space="preserve" y="252.06" zvalue="116">34167</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" x="1069" xml:space="preserve" y="480.5013440860215" zvalue="119">#1主变                  </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1069" xml:space="preserve" y="496.5013440860215" zvalue="119">12.5MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,970.732,581.472) scale(1,1) translate(0,0)" writing-mode="lr" x="970.73" xml:space="preserve" y="585.97" zvalue="125">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,981.375,385.472) scale(1,1) translate(0,0)" writing-mode="lr" x="981.38" xml:space="preserve" y="389.97" zvalue="132">3411</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1007.5,114.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1007.5" xml:space="preserve" y="119.33" zvalue="141">35kV万一二线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,837.278,194.111) scale(1,1) translate(0,0)" writing-mode="lr" x="837.28" xml:space="preserve" y="198.61" zvalue="147">39017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,880.083,220.5) scale(1,1) translate(0,0)" writing-mode="lr" x="880.08" xml:space="preserve" y="225" zvalue="148">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1616.65,455.336) scale(1,1) translate(0,0)" writing-mode="lr" x="1616.65" xml:space="preserve" y="459.84" zvalue="154">首部变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1578.9,570.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1578.9" xml:space="preserve" y="575.11" zvalue="156">603</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1560.31,355.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1560.31" xml:space="preserve" y="360.28" zvalue="163">041</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1543.83,249.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1543.83" xml:space="preserve" y="254.33" zvalue="165">10kV首部线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,187.899,394.091) scale(1,1) translate(0,0)" writing-mode="lr" x="187.9" xml:space="preserve" y="398.59" zvalue="167">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,295.899,394.091) scale(1,1) translate(0,0)" writing-mode="lr" x="295.9" xml:space="preserve" y="398.59" zvalue="168">通道</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="201.5" y1="176" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="201.5" y1="202" y2="202"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="20.5" y1="176" y2="202"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="201.5" y1="176" y2="202"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="382.5" y1="176" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="382.5" y1="202" y2="202"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="201.5" y1="176" y2="202"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="382.5" x2="382.5" y1="176" y2="202"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="201.5" y1="202" y2="202"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="201.5" y1="226.25" y2="226.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="20.5" y1="202" y2="226.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="201.5" y1="202" y2="226.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="382.5" y1="202" y2="202"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="382.5" y1="226.25" y2="226.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="201.5" y1="202" y2="226.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="382.5" x2="382.5" y1="202" y2="226.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="201.5" y1="226.25" y2="226.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="201.5" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="20.5" y1="226.25" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="201.5" y1="226.25" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="382.5" y1="226.25" y2="226.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="382.5" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="201.5" y1="226.25" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="382.5" x2="382.5" y1="226.25" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="201.5" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="201.5" y1="271.75" y2="271.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="20.5" y1="249" y2="271.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="201.5" y1="249" y2="271.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="382.5" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="382.5" y1="271.75" y2="271.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="201.5" y1="249" y2="271.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="382.5" x2="382.5" y1="249" y2="271.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="201.5" y1="271.75" y2="271.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="201.5" y1="294.5" y2="294.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="20.5" y1="271.75" y2="294.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="201.5" y1="271.75" y2="294.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="382.5" y1="271.75" y2="271.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="382.5" y1="294.5" y2="294.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="201.5" y1="271.75" y2="294.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="382.5" x2="382.5" y1="271.75" y2="294.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="201.5" y1="294.5" y2="294.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="201.5" y1="317.25" y2="317.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="20.5" y1="294.5" y2="317.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="201.5" y1="294.5" y2="317.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="382.5" y1="294.5" y2="294.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="382.5" y1="317.25" y2="317.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="201.5" y1="294.5" y2="317.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="382.5" x2="382.5" y1="294.5" y2="317.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="201.5" y1="317.25" y2="317.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="201.5" y1="340" y2="340"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.5" x2="20.5" y1="317.25" y2="340"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="201.5" y1="317.25" y2="340"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="382.5" y1="317.25" y2="317.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="382.5" y1="340" y2="340"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.5" x2="201.5" y1="317.25" y2="340"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="382.5" x2="382.5" y1="317.25" y2="340"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,73.5,192) scale(1,1) translate(0,0)" writing-mode="lr" x="31" xml:space="preserve" y="196.5" zvalue="178">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,249,192) scale(1,1) translate(0,0)" writing-mode="lr" x="206.5" xml:space="preserve" y="196.5" zvalue="179">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,265.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="269.75" zvalue="180">6.3kV母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,67.5,218) scale(1,1) translate(0,0)" writing-mode="lr" x="25" xml:space="preserve" y="222.5" zvalue="184">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,248,218) scale(1,1) translate(0,0)" writing-mode="lr" x="205.5" xml:space="preserve" y="222.5" zvalue="185">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,67.5,239.75) scale(1,1) translate(0,0)" writing-mode="lr" x="25" xml:space="preserve" y="244.25" zvalue="188">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,247.5,240) scale(1,1) translate(0,0)" writing-mode="lr" x="205" xml:space="preserve" y="244.5" zvalue="190">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,157.25,1005.75) scale(1,1) translate(0,0)" writing-mode="lr" x="157.25" xml:space="preserve" y="1011.75" zvalue="209">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,246,944) scale(1,1) translate(0,0)" writing-mode="lr" x="246" xml:space="preserve" y="950" zvalue="243">WanMaHeYiJi-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,335,1006) scale(1,1) translate(0,0)" writing-mode="lr" x="335" xml:space="preserve" y="1012" zvalue="245">20240614</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="48.69" y="385.5" zvalue="171"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="32">
   <path class="v6300" d="M 528.5 626.75 L 1737.25 626.75" stroke-width="6" zvalue="33"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674241806340" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674241806340"/></metadata>
  <path d="M 528.5 626.75 L 1737.25 626.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="341">
   <use class="v6300" height="20" transform="rotate(0,714.297,689.696) scale(2.16108,2.16108) translate(-377.964,-358.942)" width="10" x="703.4912202553933" xlink:href="#Breaker:手车开关_0" y="668.084974364315" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924506157061" ObjectName="#1发电机641断路器"/>
   <cge:TPSR_Ref TObjectID="6473924506157061"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,714.297,689.696) scale(2.16108,2.16108) translate(-377.964,-358.942)" width="10" x="703.4912202553933" y="668.084974364315"/></g>
  <g id="76">
   <use class="v6300" height="20" transform="rotate(0,1437.05,689.696) scale(2.16108,2.16108) translate(-766.276,-358.942)" width="10" x="1426.241220255393" xlink:href="#Breaker:手车开关_0" y="668.084974364315" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924506222597" ObjectName="#2发电机642断路器"/>
   <cge:TPSR_Ref TObjectID="6473924506222597"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1437.05,689.696) scale(2.16108,2.16108) translate(-766.276,-358.942)" width="10" x="1426.241220255393" y="668.084974364315"/></g>
  <g id="141">
   <use class="v6300" height="20" transform="rotate(0,735.969,558.743) scale(2.94375,-2.72569) translate(-476.239,-746.477)" width="10" x="721.2500000000001" xlink:href="#Breaker:手车开关_0" y="531.486111111111" zvalue="103"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924506288133" ObjectName="站用变681断路器"/>
   <cge:TPSR_Ref TObjectID="6473924506288133"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,735.969,558.743) scale(2.94375,-2.72569) translate(-476.239,-746.477)" width="10" x="721.2500000000001" y="531.486111111111"/></g>
  <g id="144">
   <use class="kv35" height="20" transform="rotate(0,1008.6,332.222) scale(1.22222,1.11111) translate(-182.271,-32.1111)" width="10" x="1002.488270797253" xlink:href="#Breaker:开关_0" y="321.111111111111" zvalue="112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924506419206" ObjectName="#1主变35kV侧341断路器"/>
   <cge:TPSR_Ref TObjectID="6473924506419206"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1008.6,332.222) scale(1.22222,1.11111) translate(-182.271,-32.1111)" width="10" x="1002.488270797253" y="321.111111111111"/></g>
  <g id="191">
   <use class="v6300" height="20" transform="rotate(0,1011.16,580.611) scale(2,2) translate(-500.581,-280.306)" width="10" x="1001.162506230206" xlink:href="#Breaker:手车开关_0" y="560.6111128065321" zvalue="124"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924506353669" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924506353669"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1011.16,580.611) scale(2,2) translate(-500.581,-280.306)" width="10" x="1001.162506230206" y="560.6111128065321"/></g>
  <g id="117">
   <use class="v6300" height="20" transform="rotate(0,1546.28,569.944) scale(2,2) translate(-768.14,-274.972)" width="10" x="1536.279569585283" xlink:href="#Breaker:手车开关_0" y="549.9444461398655" zvalue="155"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924506484742" ObjectName="首部变6.3kV侧603断路器"/>
   <cge:TPSR_Ref TObjectID="6473924506484742"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1546.28,569.944) scale(2,2) translate(-768.14,-274.972)" width="10" x="1536.279569585283" y="549.9444461398655"/></g>
  <g id="121">
   <use class="kv10" height="20" transform="rotate(0,1543.44,356.778) scale(1.22222,1.11111) translate(-279.515,-34.5667)" width="10" x="1537.333333333333" xlink:href="#Breaker:开关_0" y="345.6666666666667" zvalue="162"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924506550278" ObjectName="首部变10kV侧041断路器"/>
   <cge:TPSR_Ref TObjectID="6473924506550278"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1543.44,356.778) scale(1.22222,1.11111) translate(-279.515,-34.5667)" width="10" x="1537.333333333333" y="345.6666666666667"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="49">
   <path class="v6300" d="M 714.3 669.71 L 714.3 626.75" stroke-width="1" zvalue="38"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@0" LinkObjectIDznd="32@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 714.3 669.71 L 714.3 626.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="v6300" d="M 713.05 847.97 L 713.05 709.15" stroke-width="1" zvalue="39"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="341@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 713.05 847.97 L 713.05 709.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="v6300" d="M 794.25 872.06 L 794.25 841.46" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@0" LinkObjectIDznd="54@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 794.25 872.06 L 794.25 841.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="v6300" d="M 621.91 839.09 L 622.19 872.19" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="84@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 621.91 839.09 L 622.19 872.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="v6300" d="M 621.83 812.33 L 621.83 785.88 L 713.05 785.88" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@1" LinkObjectIDznd="48" MaxPinNum="2"/>
   </metadata>
  <path d="M 621.83 812.33 L 621.83 785.88 L 713.05 785.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="v6300" d="M 795.15 812.33 L 795.15 787.13 L 713.05 787.13" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@1" LinkObjectIDznd="48" MaxPinNum="2"/>
   </metadata>
  <path d="M 795.15 812.33 L 795.15 787.13 L 713.05 787.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="v6300" d="M 841.88 872.06 L 841.88 787.75 L 795.15 787.75" stroke-width="1" zvalue="67"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="56" MaxPinNum="2"/>
   </metadata>
  <path d="M 841.88 872.06 L 841.88 787.75 L 795.15 787.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="v6300" d="M 1437.05 669.71 L 1437.05 626.75" stroke-width="1" zvalue="71"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@0" LinkObjectIDznd="32@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1437.05 669.71 L 1437.05 626.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="v6300" d="M 1437.05 847.97 L 1437.05 709.15" stroke-width="1" zvalue="72"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@0" LinkObjectIDznd="76@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1437.05 847.97 L 1437.05 709.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="v6300" d="M 1518.25 872.06 L 1518.25 841.46" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@0" LinkObjectIDznd="66@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1518.25 872.06 L 1518.25 841.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="v6300" d="M 1345.91 839.09 L 1346.19 872.19" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="70@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1345.91 839.09 L 1346.19 872.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="v6300" d="M 1345.83 812.33 L 1345.83 785.88 L 1437.05 785.88" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@1" LinkObjectIDznd="74" MaxPinNum="2"/>
   </metadata>
  <path d="M 1345.83 812.33 L 1345.83 785.88 L 1437.05 785.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="v6300" d="M 1519.15 812.33 L 1519.15 787.13 L 1437.05 787.13" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@1" LinkObjectIDznd="74" MaxPinNum="2"/>
   </metadata>
  <path d="M 1519.15 812.33 L 1519.15 787.13 L 1437.05 787.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="v6300" d="M 1565.88 872.06 L 1565.88 787.75 L 1519.15 787.75" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@0" LinkObjectIDznd="65" MaxPinNum="2"/>
   </metadata>
  <path d="M 1565.88 872.06 L 1565.88 787.75 L 1519.15 787.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="v6300" d="M 1062.53 695.96 L 1062.53 626.75" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@1" LinkObjectIDznd="32@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.53 695.96 L 1062.53 626.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="v6300" d="M 1062.41 777.67 L 1062.41 736.43" stroke-width="1" zvalue="96"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="152@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.41 777.67 L 1062.41 736.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="v6300" d="M 735.97 583.96 L 735.97 626.75" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@0" LinkObjectIDznd="32@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 735.97 583.96 L 735.97 626.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="v6300" d="M 735.97 492.61 L 735.97 534.21" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="141@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 735.97 492.61 L 735.97 534.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="v6300" d="M 1009.63 530.02 L 1009.63 562.11" stroke-width="1" zvalue="126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@1" LinkObjectIDznd="191@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1009.63 530.02 L 1009.63 562.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="v6300" d="M 1011.16 598.61 L 1011.16 626.75" stroke-width="1" zvalue="127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@1" LinkObjectIDznd="32@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1011.16 598.61 L 1011.16 626.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv35" d="M 1008.65 398.49 L 1009.7 438.84" stroke-width="1" zvalue="132"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@1" LinkObjectIDznd="138@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1008.65 398.49 L 1009.7 438.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv35" d="M 1008.68 342.83 L 1008.68 374.65" stroke-width="1" zvalue="133"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="144@1" LinkObjectIDznd="90@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1008.68 342.83 L 1008.68 374.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv35" d="M 1008.03 297.74 L 1008.56 321.59" stroke-width="1" zvalue="135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@1" LinkObjectIDznd="144@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1008.03 297.74 L 1008.56 321.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv35" d="M 1008.33 148.35 L 1008.06 273.9" stroke-width="1" zvalue="143"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@0" LinkObjectIDznd="145@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1008.33 148.35 L 1008.06 273.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv35" d="M 1071.33 229.14 L 1008.16 229.14" stroke-width="1" zvalue="144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@0" LinkObjectIDznd="104" MaxPinNum="2"/>
   </metadata>
  <path d="M 1071.33 229.14 L 1008.16 229.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv35" d="M 913.38 228.01 L 913.38 250.48 L 1008.11 250.48" stroke-width="1" zvalue="148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@1" LinkObjectIDznd="104" MaxPinNum="2"/>
   </metadata>
  <path d="M 913.38 228.01 L 913.38 250.48 L 1008.11 250.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv35" d="M 911.88 164.6 L 913.41 210.14" stroke-width="1" zvalue="150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="107@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 911.88 164.6 L 913.41 210.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv35" d="M 850.61 177.94 L 912.32 177.94" stroke-width="1" zvalue="151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="111" MaxPinNum="2"/>
   </metadata>
  <path d="M 850.61 177.94 L 912.32 177.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="v6300" d="M 1544.75 499.36 L 1544.75 551.44" stroke-width="1" zvalue="157"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@1" LinkObjectIDznd="117@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1544.75 499.36 L 1544.75 551.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="v6300" d="M 1546.28 587.94 L 1546.28 626.75" stroke-width="1" zvalue="158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@1" LinkObjectIDznd="32@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1546.28 587.94 L 1546.28 626.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv10" d="M 1543.53 367.39 L 1544.82 408.17" stroke-width="1" zvalue="163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@1" LinkObjectIDznd="118@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1543.53 367.39 L 1544.82 408.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv10" d="M 1541.33 299.21 L 1543.4 346.15" stroke-width="1" zvalue="165"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@0" LinkObjectIDznd="121@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1541.33 299.21 L 1543.4 346.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v6300" height="30" transform="rotate(0,713.047,875.386) scale(1.85899,1.85899) translate(-316.595,-391.608)" width="30" x="685.161789750568" xlink:href="#Generator:发电机_0" y="847.5010218856319" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449774354438" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449774354438"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,713.047,875.386) scale(1.85899,1.85899) translate(-316.595,-391.608)" width="30" x="685.161789750568" y="847.5010218856319"/></g>
  <g id="73">
   <use class="v6300" height="30" transform="rotate(0,1437.05,875.386) scale(1.85899,1.85899) translate(-651.136,-391.608)" width="30" x="1409.161789750568" xlink:href="#Generator:发电机_0" y="847.5010218856319" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449774944262" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449774944262"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1437.05,875.386) scale(1.85899,1.85899) translate(-651.136,-391.608)" width="30" x="1409.161789750568" y="847.5010218856319"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="185">
   <use class="v6300" height="29" transform="rotate(0,794.25,889.875) scale(1.25,-1.25) translate(-155.1,-1598.15)" width="30" x="775.5" xlink:href="#Accessory:PT12321_0" y="871.75" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449774288902" ObjectName="#1发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,794.25,889.875) scale(1.25,-1.25) translate(-155.1,-1598.15)" width="30" x="775.5" y="871.75"/></g>
  <g id="84">
   <use class="v6300" height="30" transform="rotate(0,622.25,890.5) scale(1.25,1.25) translate(-120.7,-174.35)" width="30" x="603.5" xlink:href="#Accessory:PT789_0" y="871.75" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449774223366" ObjectName="#1机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,622.25,890.5) scale(1.25,1.25) translate(-120.7,-174.35)" width="30" x="603.5" y="871.75"/></g>
  <g id="57">
   <use class="v6300" height="29" transform="rotate(0,841.875,889.875) scale(1.25,-1.25) translate(-164.625,-1598.15)" width="30" x="823.125" xlink:href="#Accessory:PT12321_0" y="871.75" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449774551046" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,841.875,889.875) scale(1.25,-1.25) translate(-164.625,-1598.15)" width="30" x="823.125" y="871.75"/></g>
  <g id="72">
   <use class="v6300" height="29" transform="rotate(0,1518.25,889.875) scale(1.25,-1.25) translate(-299.9,-1598.15)" width="30" x="1499.5" xlink:href="#Accessory:PT12321_0" y="871.75" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449774878726" ObjectName="#2发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1518.25,889.875) scale(1.25,-1.25) translate(-299.9,-1598.15)" width="30" x="1499.5" y="871.75"/></g>
  <g id="70">
   <use class="v6300" height="30" transform="rotate(0,1346.25,890.5) scale(1.25,1.25) translate(-265.5,-174.35)" width="30" x="1327.5" xlink:href="#Accessory:PT789_0" y="871.75" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449774813190" ObjectName="#2机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1346.25,890.5) scale(1.25,1.25) translate(-265.5,-174.35)" width="30" x="1327.5" y="871.75"/></g>
  <g id="64">
   <use class="v6300" height="29" transform="rotate(0,1565.88,889.875) scale(1.25,-1.25) translate(-309.425,-1598.15)" width="30" x="1547.125" xlink:href="#Accessory:PT12321_0" y="871.75" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449774616582" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1565.88,889.875) scale(1.25,-1.25) translate(-309.425,-1598.15)" width="30" x="1547.125" y="871.75"/></g>
  <g id="41">
   <use class="v6300" height="18" transform="rotate(0,1064.4,795.538) scale(2.13675,2.13675) translate(-557.735,-412.996)" width="15" x="1048.374515232209" xlink:href="#Accessory:PT8_0" y="776.3076923076924" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449775075334" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1064.4,795.538) scale(2.13675,2.13675) translate(-557.735,-412.996)" width="15" x="1048.374515232209" y="776.3076923076924"/></g>
  <g id="110">
   <use class="kv35" height="40" transform="rotate(0,923.333,142.833) scale(1.14583,-1.14583) translate(-114.598,-264.571)" width="40" x="900.4166666666667" xlink:href="#Accessory:五卷PT_0" y="119.9166666666667" zvalue="149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449775730694" ObjectName="#1主变35kV侧电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,923.333,142.833) scale(1.14583,-1.14583) translate(-114.598,-264.571)" width="40" x="900.4166666666667" y="119.9166666666667"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="50">
   <use class="v6300" height="26" transform="rotate(0,621.827,825.688) scale(1.03365,1.03365) translate(-20.0436,-26.4453)" width="12" x="615.625" xlink:href="#Disconnector:单手车刀闸1212_0" y="812.25" zvalue="60"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449774419974" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449774419974"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,621.827,825.688) scale(1.03365,1.03365) translate(-20.0436,-26.4453)" width="12" x="615.625" y="812.25"/></g>
  <g id="54">
   <use class="v6300" height="26" transform="rotate(0,795.15,826.875) scale(1.125,1.125) translate(-87.6,-90.25)" width="12" x="788.3999353616197" xlink:href="#Disconnector:单手车刀闸1212_0" y="812.25" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449774485510" ObjectName="#1发电机6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449774485510"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,795.15,826.875) scale(1.125,1.125) translate(-87.6,-90.25)" width="12" x="788.3999353616197" y="812.25"/></g>
  <g id="69">
   <use class="v6300" height="26" transform="rotate(0,1345.83,825.688) scale(1.03365,1.03365) translate(-43.6157,-26.4453)" width="12" x="1339.625" xlink:href="#Disconnector:单手车刀闸1212_0" y="812.25" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449774747654" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449774747654"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1345.83,825.688) scale(1.03365,1.03365) translate(-43.6157,-26.4453)" width="12" x="1339.625" y="812.25"/></g>
  <g id="66">
   <use class="v6300" height="26" transform="rotate(0,1519.15,826.875) scale(1.125,1.125) translate(-168.044,-90.25)" width="12" x="1512.39993536162" xlink:href="#Disconnector:单手车刀闸1212_0" y="812.25" zvalue="82"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449774682118" ObjectName="#2发电机6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449774682118"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1519.15,826.875) scale(1.125,1.125) translate(-168.044,-90.25)" width="12" x="1512.39993536162" y="812.25"/></g>
  <g id="152">
   <use class="v6300" height="26" transform="rotate(0,1062.53,716.166) scale(-1.38889,1.56303) translate(-1825.22,-250.657)" width="12" x="1054.196581196581" xlink:href="#Disconnector:20210316_0" y="695.8461538461538" zvalue="88"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449775009798" ObjectName="6.3kV母线电压互感器6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449775009798"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1062.53,716.166) scale(-1.38889,1.56303) translate(-1825.22,-250.657)" width="12" x="1054.196581196581" y="695.8461538461538"/></g>
  <g id="145">
   <use class="kv35" height="30" transform="rotate(0,1007.97,285.722) scale(1.11111,0.814815) translate(-99.9633,62.1591)" width="15" x="999.6329468174489" xlink:href="#Disconnector:刀闸_0" y="273.5" zvalue="110"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449775337478" ObjectName="#1主变35kV侧3416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449775337478"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1007.97,285.722) scale(1.11111,0.814815) translate(-99.9633,62.1591)" width="15" x="999.6329468174489" y="273.5"/></g>
  <g id="90">
   <use class="kv35" height="30" transform="rotate(0,1008.58,386.472) scale(1.11111,0.814815) translate(-100.025,85.0568)" width="15" x="1000.25" xlink:href="#Disconnector:刀闸_0" y="374.25" zvalue="131"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449775403014" ObjectName="#1主变35kV侧3411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449775403014"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1008.58,386.472) scale(1.11111,0.814815) translate(-100.025,85.0568)" width="15" x="1000.25" y="374.25"/></g>
  <g id="107">
   <use class="kv35" height="30" transform="rotate(0,913.333,219) scale(0.833333,0.611111) translate(181.417,133.53)" width="15" x="907.0833333333334" xlink:href="#Disconnector:刀闸_0" y="209.8333333333334" zvalue="147"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449775665158" ObjectName="#1主变35kV侧3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449775665158"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,913.333,219) scale(0.833333,0.611111) translate(181.417,133.53)" width="15" x="907.0833333333334" y="209.8333333333334"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="205">
   <use class="v6300" height="30" transform="rotate(0,735.813,468) scale(1.69643,-1.70833) translate(-292.321,-731.326)" width="28" x="712.062894700714" xlink:href="#EnergyConsumer:站用变DY接地_0" y="442.375" zvalue="98"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449775140870" ObjectName="站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,735.813,468) scale(1.69643,-1.70833) translate(-292.321,-731.326)" width="28" x="712.062894700714" y="442.375"/></g>
  <g id="124">
   <use class="kv10" height="30" transform="rotate(0,1541.33,285.333) scale(1.04167,1.02778) translate(-61.4033,-7.29505)" width="12" x="1535.083333333333" xlink:href="#EnergyConsumer:负荷_0" y="269.9166666666666" zvalue="164"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449775796230" ObjectName="10kV首部线"/>
   <cge:TPSR_Ref TObjectID="6192449775796230"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1541.33,285.333) scale(1.04167,1.02778) translate(-61.4033,-7.29505)" width="12" x="1535.083333333333" y="269.9166666666666"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="142">
   <use class="kv35" height="20" transform="rotate(270,1082.16,229.083) scale(-1.11111,1.11111) translate(-2055.55,-21.7972)" width="10" x="1076.605169039671" xlink:href="#GroundDisconnector:地刀_0" y="217.9722222222222" zvalue="115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449775271942" ObjectName="#1主变35kV侧34167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449775271942"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1082.16,229.083) scale(-1.11111,1.11111) translate(-2055.55,-21.7972)" width="10" x="1076.605169039671" y="217.9722222222222"/></g>
  <g id="106">
   <use class="kv35" height="20" transform="rotate(90,839.778,177.889) scale(1.11111,1.11111) translate(-83.4222,-16.6778)" width="10" x="834.2222222222223" xlink:href="#GroundDisconnector:地刀_0" y="166.7777777777777" zvalue="146"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449775599622" ObjectName="#1主变35kV侧39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449775599622"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,839.778,177.889) scale(1.11111,1.11111) translate(-83.4222,-16.6778)" width="10" x="834.2222222222223" y="166.7777777777777"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="138">
   <g id="1380">
    <use class="kv35" height="60" transform="rotate(0,1009.63,484.339) scale(1.6125,1.54462) translate(-371.254,-154.436)" width="40" x="977.38" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="438" zvalue="118"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874433822724" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1381">
    <use class="v6300" height="60" transform="rotate(0,1009.63,484.339) scale(1.6125,1.54462) translate(-371.254,-154.436)" width="40" x="977.38" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="438" zvalue="118"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874433888260" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399449182212" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399449182212"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1009.63,484.339) scale(1.6125,1.54462) translate(-371.254,-154.436)" width="40" x="977.38" y="438"/></g>
  <g id="118">
   <g id="1180">
    <use class="kv10" height="50" transform="rotate(0,1544.75,453.672) scale(2.15,1.85355) translate(-809.012,-187.575)" width="30" x="1512.5" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="407.33" zvalue="153"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874433953796" ObjectName="10"/>
    </metadata>
   </g>
   <g id="1181">
    <use class="v6300" height="50" transform="rotate(0,1544.75,453.672) scale(2.15,1.85355) translate(-809.012,-187.575)" width="30" x="1512.5" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="407.33" zvalue="153"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874434019332" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399449247748" ObjectName="首部变"/>
   <cge:TPSR_Ref TObjectID="6755399449247748"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1544.75,453.672) scale(2.15,1.85355) translate(-809.012,-187.575)" width="30" x="1512.5" y="407.33"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="100">
   <use class="kv35" height="30" transform="rotate(0,1008.33,142.167) scale(1.54762,0.416667) translate(-354.878,190.283)" width="7" x="1002.916666666667" xlink:href="#ACLineSegment:线路_0" y="135.9166666666667" zvalue="140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249315737606" ObjectName="35kV万一二线"/>
   <cge:TPSR_Ref TObjectID="8444249315737606_5066549585772546"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1008.33,142.167) scale(1.54762,0.416667) translate(-354.878,190.283)" width="7" x="1002.916666666667" y="135.9166666666667"/></g>
 </g>
 <g id="StateClass">
  <g id="16">
   <use height="30" transform="rotate(0,331.173,394.607) scale(0.708333,0.665547) translate(131.99,193.283)" width="30" x="320.55" xlink:href="#State:红绿圆(方形)_0" y="384.62" zvalue="169"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374888140803" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,331.173,394.607) scale(0.708333,0.665547) translate(131.99,193.283)" width="30" x="320.55" y="384.62"/></g>
  <g id="82">
   <use height="30" transform="rotate(0,227.548,394.607) scale(0.708333,0.665547) translate(89.3211,193.283)" width="30" x="216.92" xlink:href="#State:红绿圆(方形)_0" y="384.62" zvalue="170"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562951097221125" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,227.548,394.607) scale(0.708333,0.665547) translate(89.3211,193.283)" width="30" x="216.92" y="384.62"/></g>
  <g id="8">
   <use height="30" transform="rotate(0,329.812,120.464) scale(1.27778,1.03333) translate(-59.1984,-3.38593)" width="90" x="272.31" xlink:href="#State:全站检修_0" y="104.96" zvalue="219"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549585772546" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.812,120.464) scale(1.27778,1.03333) translate(-59.1984,-3.38593)" width="90" x="272.31" y="104.96"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="98" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,148.611,264.167) scale(1,1) translate(0,0)" writing-mode="lr" x="148.77" xml:space="preserve" y="269.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125089640452" ObjectName="F"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="97" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,148.611,192.167) scale(1,1) translate(0,0)" writing-mode="lr" x="148.77" xml:space="preserve" y="197.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125171036165" ObjectName="GEN_PSUM"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="143" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,326.222,193.167) scale(1,1) translate(0,0)" writing-mode="lr" x="326.38" xml:space="preserve" y="198.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125171101702" ObjectName="F"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="137" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,148.611,217.167) scale(1,1) translate(0,0)" writing-mode="lr" x="148.77" xml:space="preserve" y="222.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125170905095" ObjectName="F"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="136" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,326.222,218.167) scale(1,1) translate(0,0)" writing-mode="lr" x="326.38" xml:space="preserve" y="223.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125170970630" ObjectName="F"/>
   </metadata>
  </g>
  <g id="133">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="133" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,148.611,241.167) scale(1,1) translate(0,0)" writing-mode="lr" x="148.77" xml:space="preserve" y="246.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127188103173" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="131">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="131" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,328.611,240.167) scale(1,1) translate(0,0)" writing-mode="lr" x="328.77" xml:space="preserve" y="245.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127188037637" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,713.547,956.193) scale(1,1) translate(-1.46116e-13,-2.09646e-13)" writing-mode="lr" x="712.97" xml:space="preserve" y="960.88" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125089771524" ObjectName="P"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="2" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,713.547,981.635) scale(1,1) translate(-1.46116e-13,-2.15295e-13)" writing-mode="lr" x="712.97" xml:space="preserve" y="986.33" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125089837063" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,713.547,1007.08) scale(1,1) translate(-1.46116e-13,-2.20944e-13)" writing-mode="lr" x="712.97" xml:space="preserve" y="1011.77" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125089902599" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="5" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1437.55,956.193) scale(1,1) translate(4.60314e-13,-2.09646e-13)" writing-mode="lr" x="1436.97" xml:space="preserve" y="960.88" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125090951172" ObjectName="P"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="6" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1437.55,981.635) scale(1,1) translate(4.60314e-13,-2.15295e-13)" writing-mode="lr" x="1436.97" xml:space="preserve" y="986.33" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125091016708" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="7" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1437.55,1007.08) scale(1,1) translate(4.60314e-13,-2.20944e-13)" writing-mode="lr" x="1436.97" xml:space="preserve" y="1011.77" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125091082244" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="11" prefix="F:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1063.99,948.264) scale(1,1) translate(-2.2626e-13,0)" writing-mode="lr" x="1063.52" xml:space="preserve" y="953" zvalue="1">F:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125089640452" ObjectName="F"/>
   </metadata>
  </g>
  <g id="127">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="127" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1008.33,52.4167) scale(1,1) translate(0,0)" writing-mode="lr" x="1007.86" xml:space="preserve" y="57.19" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125096325125" ObjectName="P"/>
   </metadata>
  </g>
  <g id="128">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="128" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1008.33,77.4167) scale(1,1) translate(0,0)" writing-mode="lr" x="1007.86" xml:space="preserve" y="82.19" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125096390661" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="129">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="129" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1008.33,98.4167) scale(1,1) translate(0,0)" writing-mode="lr" x="1007.86" xml:space="preserve" y="103.19" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125096456197" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="12" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1068.64,869.25) scale(1,1) translate(1.13647e-13,0)" writing-mode="lr" x="1068.17" xml:space="preserve" y="874.03" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125089247236" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="13" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1067.21,898.25) scale(1,1) translate(1.13489e-13,0)" writing-mode="lr" x="1066.75" xml:space="preserve" y="903.03" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125089312772" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="23" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1066.07,924.679) scale(1,1) translate(-2.26723e-13,0)" writing-mode="lr" x="1065.6" xml:space="preserve" y="929.46" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125089378308" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="160">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="160" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1136.13,320.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1081.03" xml:space="preserve" y="326.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125093769221" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="161">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="161" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1136.13,357.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1081.03" xml:space="preserve" y="363.36" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125093834757" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="162">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="162" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1136.13,528.177) scale(1,1) translate(0,0)" writing-mode="lr" x="1081.03" xml:space="preserve" y="534.04" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125093900293" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="163">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="163" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1136.13,565.177) scale(1,1) translate(0,0)" writing-mode="lr" x="1081.03" xml:space="preserve" y="571.04" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125093965829" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="164">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="164" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1136.13,394.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1081.03" xml:space="preserve" y="400.36" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125094031365" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="165">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="165" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1136.13,602.177) scale(1,1) translate(0,0)" writing-mode="lr" x="1081.03" xml:space="preserve" y="608.04" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125094359045" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="168">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="168" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1637.25,519.511) scale(1,1) translate(0,0)" writing-mode="lr" x="1584.06" xml:space="preserve" y="525.37" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125098029061" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="169">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="169" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1637.25,556.511) scale(1,1) translate(0,0)" writing-mode="lr" x="1584.06" xml:space="preserve" y="562.37" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125098094597" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="172">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="172" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1637.25,593.511) scale(1,1) translate(0,0)" writing-mode="lr" x="1584.06" xml:space="preserve" y="599.37" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125098487813" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="167">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="167" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,481.5,588.75) scale(1,1) translate(0,0)" writing-mode="lr" x="438.38" xml:space="preserve" y="593.11" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125089509380" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
</svg>