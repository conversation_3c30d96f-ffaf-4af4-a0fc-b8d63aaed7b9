<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549682503681" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="ACLineSegment:线路电压互感器_0" viewBox="0,0,35,40">
   <use terminal-index="0" type="0" x="14.20833333333333" xlink:href="#terminal" y="38.6908407585088"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.50545055364641" x2="21.50545055364641" y1="13.04138864447597" y2="19.16666666666667"/>
   <path d="M 14 13 L 5 13 L 5 25 L 5 25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.91094883543335" x2="24.38823024054981" y1="12.92541949757221" y2="12.92541949757221"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.04,24.92) scale(1,1) translate(0,0)" width="6.08" x="2" y="17.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="4" y1="25.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="5.5" y1="37" y2="37"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="6" y1="25.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="6.5" y1="36" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="32" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="7.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.46058610156545" x2="14.15436235204273" y1="12.92541949757221" y2="12.92541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16022336769755" x2="14.16022336769755" y1="38.83333333333334" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.46383161512025" x2="31.41666666666666" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.36598892707138" x2="31.36598892707138" y1="9.749999999999996" y2="16.24517624503405"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.35940244368076" x2="24.35940244368076" y1="12.99758812251703" y2="12.99758812251703"/>
   <ellipse cx="21.51" cy="23.82" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="28.55" cy="28.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.76" cy="32.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="24.35940244368077" x2="24.35940244368077" y1="10.48325293741726" y2="15.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.51910080183274" x2="32.51910080183274" y1="11.01295093653441" y2="15.34306843322376"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="33.38393470790376" x2="33.38393470790376" y1="11.91505874834466" y2="13.89969593432727"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.57589728904158" x2="16.57589728904158" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="26.52148720885832" x2="26.52148720885832" y1="10.48325293741726" y2="15.5840919325617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="16.49256395570825" x2="16.49256395570825" y1="10.33333333333333" y2="15.43417232847779"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="18.73798205421914" x2="18.73798205421914" y1="10.48325293741726" y2="15.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.91666666666667" x2="23.91666666666667" y1="24.08333333333334" y2="24.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.33333333333334" x2="31.33333333333334" y1="28.08333333333334" y2="28.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="24.58333333333334" y1="32" y2="32"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(130,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(130,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT232_0" viewBox="0,0,25,35">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0.2666666666666657"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="12.5" y1="15.16666666666667" y2="0.5"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12.5,7.5) scale(1,1) translate(0,0)" width="6" x="9.5" y="2.5"/>
   <ellipse cx="12.75" cy="20" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.75" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.67" cy="29.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.82" cy="24.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333336" x2="5.583333333333336" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="5.583333333333334" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="5.5" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.75" x2="12.75" y1="22.25" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="12.75" y1="17.63888888888889" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="12.75" y1="17.63888888888889" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="32" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="12.58333333333333" y1="27.38888888888889" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="12.58333333333333" y1="27.38888888888889" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="19.31481481481482" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.94444444444444" x2="20.62962962962963" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21.94444444444444" y1="24.96612466124661" y2="24.96612466124661"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:带熔断器35kVPT11_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="1.1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="26.25" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="18.06481481481482" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="20.69444444444444" y1="24.96612466124661" y2="24.96612466124661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69444444444444" x2="19.37962962962963" y1="24.96612466124661" y2="22.5"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,5.71) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="13" y2="1"/>
   <ellipse cx="15.03" cy="18.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.67" cy="23.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="18.5" cy="23.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="20.5" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.750000000000004" y2="4.166666666666668"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="32"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="1" y1="27" y2="11"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="27" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Accessory:PT7_0" viewBox="0,0,14,18">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1.416666666666664"/>
   <ellipse cx="7.15" cy="6.35" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.15" cy="12.03" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:熔断器_0" viewBox="0,0,10,18">
   <use terminal-index="0" type="0" x="5.016666666666667" xlink:href="#terminal" y="1.083333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1" y2="17"/>
   <rect fill-opacity="0" height="16.08" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.06,9.06) scale(1,1) translate(0,0)" width="9.58" x="0.27" y="1.02"/>
  </symbol>
  <symbol id="Accessory:五卷PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="25.16666666666667" y2="1"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,14) scale(1,1) translate(0,0)" width="6" x="7" y="7"/>
   <path d="M 5.11667 33.0667 L 5.11667 37.0667 L 8.11667 35.0667 L 5.11667 33.0667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.66666666666667" x2="9.999999999999998" y1="4" y2="4"/>
   <ellipse cx="13.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <ellipse cx="13.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.44906826265991" x2="6.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659912" x2="8.849068262659907" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659907" x2="4.049068262659912" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="36.96252026861409" y2="34.48744029990989"/>
   <ellipse cx="6.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.11944444444444" x2="29.63333333333333" y1="28.26666666666667" y2="28.26666666666667"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,27.29,13.93) scale(1,1) translate(0,0)" width="7.58" x="23.5" y="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="3.93333333333333" y2="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="5.85" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="24.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="30.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.16111111111111" x2="30.27222222222222" y1="26.88508771929826" y2="26.88508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="21.85" y2="25.45"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.88333333333333" x2="31.55" y1="25.50350877192984" y2="25.50350877192984"/>
  </symbol>
  <symbol id="Accessory:厂用变2020_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <path d="M 13 13.5 L 17 13.5 L 15 10.5 L 13 13.5 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="20.66666666666666" y2="22.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="17.91666666666666" y2="20.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="25.41666666666667" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="20.66666666666666" y2="22.66666666666666"/>
   <ellipse cx="14.95" cy="20.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.95" cy="12.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0833 3.5 L 14.0833 4.5 L 16.0833 4.5 L 15.0833 3.5 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="1" y2="7.333333333333331"/>
  </symbol>
  <symbol id="Accessory:空挂线路_0" viewBox="0,0,11,13">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="-0.0833333333333357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="12.91666666666667" y2="0.2500000000000009"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id="EnergyConsumer:站用变YD2022_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="24" y1="24" y2="18"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.98980556533989" x2="12.83661970177291" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.9898055653399" x2="15.14299142890688" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.83661970177294" x2="15.1429914289069" y1="27.75505857643125" y2="27.75505857643125"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV芒里电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="52" xlink:href="logo.png" y="45"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,180,75) scale(1,1) translate(0,0)" writing-mode="lr" x="180" xml:space="preserve" y="78.5" zvalue="54"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,180.5,74.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="180.5" xml:space="preserve" y="83.69" zvalue="55">110kV芒里电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="202" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="1618"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="1618">信号一览</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="284" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.4375,225) scale(1,1) translate(0,0)" width="72.88" x="49" y="213" zvalue="1704"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.4375,225) scale(1,1) translate(0,0)" writing-mode="lr" x="85.44" xml:space="preserve" y="229.5" zvalue="1704">AGC</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1683.53,354.257) scale(1,1) translate(0,0)" writing-mode="lr" x="1683.53" xml:space="preserve" y="358.76" zvalue="7">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,891.134,631.376) scale(1,1) translate(0,0)" writing-mode="lr" x="891.13" xml:space="preserve" y="635.88" zvalue="16">10.5kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,947.622,268.726) scale(1,1) translate(0,0)" writing-mode="lr" x="947.62" xml:space="preserve" y="273.23" zvalue="17">131</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,940.022,309.904) scale(1,1) translate(0,0)" writing-mode="lr" x="940.02" xml:space="preserve" y="314.4" zvalue="18">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,941.465,216.388) scale(1,1) translate(0,0)" writing-mode="lr" x="941.46" xml:space="preserve" y="220.89" zvalue="20">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,922.32,138.265) scale(1,1) translate(0,0)" writing-mode="lr" x="922.3200000000001" xml:space="preserve" y="142.77" zvalue="25">110kV芒遮线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,896.501,300.923) scale(1,1) translate(0,0)" writing-mode="lr" x="896.5" xml:space="preserve" y="305.42" zvalue="27">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,896.501,256.943) scale(1,1) translate(3.94574e-13,0)" writing-mode="lr" x="896.5" xml:space="preserve" y="261.44" zvalue="29">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,896.501,211.407) scale(1,1) translate(0,0)" writing-mode="lr" x="896.5" xml:space="preserve" y="215.91" zvalue="32">67</text>
  <line fill="none" id="67" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="386" x2="386" y1="13" y2="1043" zvalue="58"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1643.12,299.513) scale(1,1) translate(0,0)" writing-mode="lr" x="1643.12" xml:space="preserve" y="304.01" zvalue="94">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1592.92,291.961) scale(1,1) translate(0,0)" writing-mode="lr" x="1592.92" xml:space="preserve" y="296.46" zvalue="96">19017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1609.55,372.729) scale(1,1) translate(0,0)" writing-mode="lr" x="1609.55" xml:space="preserve" y="377.23" zvalue="102">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1617.63,407.364) scale(1,1) translate(0,3.54263e-13)" writing-mode="lr" x="1617.63" xml:space="preserve" y="411.86" zvalue="103">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1537.49,542.828) scale(1,1) translate(0,0)" writing-mode="lr" x="1537.49" xml:space="preserve" y="547.33" zvalue="106">1020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1573.3,397.84) scale(1,1) translate(0,0)" writing-mode="lr" x="1573.3" xml:space="preserve" y="402.34" zvalue="115">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="309" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1065.99,691.28) scale(1,1) translate(0,0)" writing-mode="lr" x="1065.99" xml:space="preserve" y="695.78" zvalue="613">031</text>
  <line fill="none" id="666" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="148.8704926140824" y2="148.8704926140824" zvalue="1056"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="609" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="1062">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="608" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="1063">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="607" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="1064">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="606" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="1065">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="605" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="1066">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="602" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,648.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="653" zvalue="1069">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1616.87,207.152) scale(1,1) translate(0,0)" writing-mode="lr" x="1616.87" xml:space="preserve" y="211.65" zvalue="1094">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1592.92,328.347) scale(1,1) translate(0,0)" writing-mode="lr" x="1592.92" xml:space="preserve" y="332.85" zvalue="1096">19010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1666.67,528.613) scale(1,1) translate(9.07764e-13,0)" writing-mode="lr" x="1666.671253576488" xml:space="preserve" y="533.1130365270244" zvalue="1098">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="851" stroke="rgb(255,255,255)" text-anchor="middle" x="1045.171875" xml:space="preserve" y="888.0505213395815" zvalue="1261">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="851" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1045.171875" xml:space="preserve" y="904.0505213395815" zvalue="1261">18MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1110.12,818.469) scale(1,1) translate(0,0)" writing-mode="lr" x="1110.12" xml:space="preserve" y="822.97" zvalue="1264">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,763.765,758.916) scale(1,1) translate(0,0)" writing-mode="lr" x="763.77" xml:space="preserve" y="763.42" zvalue="1306">035</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1464.21,138.265) scale(1,1) translate(0,0)" writing-mode="lr" x="1464.21" xml:space="preserve" y="142.77" zvalue="1443">110kV备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1158.61,827.994) scale(1,1) translate(0,0)" writing-mode="lr" x="1158.61" xml:space="preserve" y="832.49" zvalue="1455">0912</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1170.73,896.832) scale(1,1) translate(0,0)" writing-mode="lr" x="1170.73" xml:space="preserve" y="901.33" zvalue="1456">#1励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,751.643,685.315) scale(1,1) translate(0,0)" writing-mode="lr" x="751.64" xml:space="preserve" y="689.8099999999999" zvalue="1463">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,737.648,882.978) scale(1,1) translate(0,0)" writing-mode="lr" x="737.65" xml:space="preserve" y="887.48" zvalue="1466">坝区变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,900.576,758.916) scale(1,1) translate(0,0)" writing-mode="lr" x="900.58" xml:space="preserve" y="763.42" zvalue="1473">033</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,888.453,685.315) scale(1,1) translate(0,0)" writing-mode="lr" x="888.45" xml:space="preserve" y="689.8099999999999" zvalue="1476">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="171" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,874.458,883.112) scale(1,1) translate(0,0)" writing-mode="lr" x="874.46" xml:space="preserve" y="887.61" zvalue="1480">#1厂用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,782.165,599.399) scale(1,1) translate(0,0)" writing-mode="lr" x="782.17" xml:space="preserve" y="603.9" zvalue="1487">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" x="760.515625" xml:space="preserve" y="503.6065070058751" zvalue="1489">10.5kVⅠ段母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="760.515625" xml:space="preserve" y="519.6065070058751" zvalue="1489">互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1267.83,660.493) scale(1,1) translate(0,0)" writing-mode="lr" x="1267.83" xml:space="preserve" y="664.99" zvalue="1494">10.5kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1035.46,373.595) scale(1,1) translate(0,0)" writing-mode="lr" x="1035.46" xml:space="preserve" y="378.09" zvalue="1496">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1043.54,408.23) scale(1,1) translate(0,0)" writing-mode="lr" x="1043.54" xml:space="preserve" y="412.73" zvalue="1498">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,963.401,543.694) scale(1,1) translate(0,0)" writing-mode="lr" x="963.4" xml:space="preserve" y="548.1900000000001" zvalue="1502">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,995.22,402.706) scale(1,1) translate(0,0)" writing-mode="lr" x="995.22" xml:space="preserve" y="407.21" zvalue="1507">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1642.64,692.146) scale(1,1) translate(0,0)" writing-mode="lr" x="1642.64" xml:space="preserve" y="696.65" zvalue="1513">032</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1095.32,529.479) scale(1,1) translate(-2.36239e-13,0)" writing-mode="lr" x="1095.31530573845" xml:space="preserve" y="533.9789258607308" zvalue="1523">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" x="1621.84375" xml:space="preserve" y="888.0505213395815" zvalue="1526">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1621.84375" xml:space="preserve" y="904.0505213395815" zvalue="1526">18MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1686.8,818.469) scale(1,1) translate(0,0)" writing-mode="lr" x="1686.8" xml:space="preserve" y="822.97" zvalue="1529">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1735.29,827.994) scale(1,1) translate(0,0)" writing-mode="lr" x="1735.29" xml:space="preserve" y="832.49" zvalue="1536">0922</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1747.42,896.832) scale(1,1) translate(0,0)" writing-mode="lr" x="1747.42" xml:space="preserve" y="901.33" zvalue="1538">#2励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1477.26,758.916) scale(1,1) translate(0,0)" writing-mode="lr" x="1477.26" xml:space="preserve" y="763.42" zvalue="1553">034</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1465.14,685.315) scale(1,1) translate(0,0)" writing-mode="lr" x="1465.14" xml:space="preserve" y="689.8099999999999" zvalue="1556">2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1446.97,884.978) scale(1,1) translate(0,0)" writing-mode="lr" x="1446.97" xml:space="preserve" y="889.48" zvalue="1560">#2厂用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1379.63,599.399) scale(1,1) translate(0,0)" writing-mode="lr" x="1379.63" xml:space="preserve" y="603.9" zvalue="1566">0902</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" x="1357.96875" xml:space="preserve" y="503.6065070058751" zvalue="1568">10.5kVⅡ段母线电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1357.96875" xml:space="preserve" y="519.6065070058751" zvalue="1568">压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1285.17,969.053) scale(1,1) translate(0,0)" writing-mode="lr" x="1285.17" xml:space="preserve" y="973.55" zvalue="1580">#3厂用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" x="1286.8984375" xml:space="preserve" y="852.1117170603263" zvalue="1583">外接110kV遮放变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1286.8984375" xml:space="preserve" y="868.1117170603263" zvalue="1583">10kV线路</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="212" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="1606">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="1607">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="210" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="1608">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="1609">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="1610">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.875,358) scale(1,1) translate(0,0)" writing-mode="lr" x="77.88" xml:space="preserve" y="362.5" zvalue="1611">10.5kVⅠ段母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="1619">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="1620">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="1623">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="1627">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="1629">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="1631">厂用电率</text>
  <line fill="none" id="214" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="1636"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,256,356.5) scale(1,1) translate(0,0)" writing-mode="lr" x="256" xml:space="preserve" y="361" zvalue="1641">10.5kVⅡ段母线频率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226,954) scale(1,1) translate(0,0)" writing-mode="lr" x="226" xml:space="preserve" y="960" zvalue="1653">MangLi-01-2011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,153,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="153" xml:space="preserve" y="1022" zvalue="1655">杨立超</text>
 </g>
 <g id="ButtonClass">
  <g href="户撒四级电站_110kV母线.svg"><rect fill-opacity="0" height="34.64" width="10.39" x="1615.8" y="277.29" zvalue="1053"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="1618"/></g>
  <g href="德宏AGC_PLC和机组控制.svg"><rect fill-opacity="0" height="24" width="72.88" x="49" y="213" zvalue="1704"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="89">
   <use class="kv110" height="30" transform="rotate(0,1621.19,300.379) scale(-0.962099,-0.705539) translate(-3306.53,-730.54)" width="15" x="1613.97226662786" xlink:href="#Disconnector:刀闸_0" y="289.7959596306422" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454483312643" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454483312643"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1621.19,300.379) scale(-0.962099,-0.705539) translate(-3306.53,-730.54)" width="15" x="1613.97226662786" y="289.7959596306422"/></g>
  <g id="21">
   <use class="kv110" height="30" transform="rotate(0,929.15,310.77) scale(-0.962099,-0.705539) translate(-1895.19,-755.658)" width="15" x="921.9342691445484" xlink:href="#Disconnector:刀闸_0" y="300.1866315433654" zvalue="17"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454482722819" ObjectName="110kV芒遮线1311隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454482722819"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,929.15,310.77) scale(-0.962099,-0.705539) translate(-1895.19,-755.658)" width="15" x="921.9342691445484" y="300.1866315433654"/></g>
  <g id="22">
   <use class="kv110" height="30" transform="rotate(0,929.15,217.254) scale(-0.962099,-0.705539) translate(-1895.19,-529.596)" width="15" x="921.9342691674865" xlink:href="#Disconnector:刀闸_0" y="206.6705717587032" zvalue="19"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454482788355" ObjectName="110kV芒遮线1316隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454482788355"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,929.15,217.254) scale(-0.962099,-0.705539) translate(-1895.19,-529.596)" width="15" x="921.9342691674865" y="206.6705717587032"/></g>
  <g id="99">
   <use class="kv110" height="30" transform="rotate(0,1599.64,373.595) scale(0.962099,0.705539) translate(62.7315,151.505)" width="15" x="1592.421252019451" xlink:href="#Disconnector:刀闸_0" y="363.0117014556841" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454483509251" ObjectName="#2主变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454483509251"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1599.64,373.595) scale(0.962099,0.705539) translate(62.7315,151.505)" width="15" x="1592.421252019451" y="363.0117014556841"/></g>
  <g id="9">
   <use class="v10500" height="26" transform="rotate(0,1091.07,819.335) scale(0.865889,0.865889) translate(168.183,125.157)" width="12" x="1085.875982992951" xlink:href="#Disconnector:单手车刀闸1212_0" y="808.0788189807497" zvalue="1263"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454484230147" ObjectName="#1发电机0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454484230147"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1091.07,819.335) scale(0.865889,0.865889) translate(168.183,125.157)" width="12" x="1085.875982992951" y="808.0788189807497"/></g>
  <g id="268">
   <use class="kv110" height="30" transform="rotate(0,1471.2,310.77) scale(-0.962099,-0.705539) translate(-3000.63,-755.658)" width="15" x="1463.980992044726" xlink:href="#Disconnector:刀闸_0" y="300.1866315433654" zvalue="1433"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454484951043" ObjectName="110kV备用线1321隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454484951043"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1471.2,310.77) scale(-0.962099,-0.705539) translate(-3000.63,-755.658)" width="15" x="1463.980992044726" y="300.1866315433654"/></g>
  <g id="267">
   <use class="kv110" height="30" transform="rotate(0,1471.2,217.254) scale(-0.962099,-0.705539) translate(-3000.63,-529.596)" width="15" x="1463.980992067664" xlink:href="#Disconnector:刀闸_0" y="206.6705717587032" zvalue="1436"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454484885507" ObjectName="110kV备用线1326隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454484885507"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1471.2,217.254) scale(-0.962099,-0.705539) translate(-3000.63,-529.596)" width="15" x="1463.980992067664" y="206.6705717587032"/></g>
  <g id="12">
   <use class="v10500" height="36" transform="rotate(0,1139.56,828.86) scale(0.865889,0.865889) translate(175.559,125.962)" width="14" x="1133.499896346801" xlink:href="#Disconnector:手车刀闸_0" y="813.2741549829885" zvalue="1454"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454485082115" ObjectName="#1发电机0912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454485082115"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1139.56,828.86) scale(0.865889,0.865889) translate(175.559,125.962)" width="14" x="1133.499896346801" y="813.2741549829885"/></g>
  <g id="85">
   <use class="v10500" height="30" transform="rotate(0,741.541,686.181) scale(0.962099,0.705539) translate(28.9278,281.964)" width="15" x="734.3249135081712" xlink:href="#Disconnector:刀闸_0" y="675.5977509236782" zvalue="1462"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454485213187" ObjectName="坝区变0351隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454485213187"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,741.541,686.181) scale(0.962099,0.705539) translate(28.9278,281.964)" width="15" x="734.3249135081712" y="675.5977509236782"/></g>
  <g id="230">
   <use class="v10500" height="30" transform="rotate(0,878.351,686.181) scale(0.962099,0.705539) translate(34.3173,281.964)" width="15" x="871.1354282337754" xlink:href="#Disconnector:刀闸_0" y="675.5977509236782" zvalue="1475"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454485540867" ObjectName="#1厂用变0331隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454485540867"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,878.351,686.181) scale(0.962099,0.705539) translate(34.3173,281.964)" width="15" x="871.1354282337754" y="675.5977509236782"/></g>
  <g id="250">
   <use class="v10500" height="36" transform="rotate(0,755.972,600.265) scale(0.865889,-0.865889) translate(116.148,-1295.92)" width="14" x="749.9109215148861" xlink:href="#Disconnector:手车刀闸_0" y="584.6793708845109" zvalue="1486"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454485671939" ObjectName="10.5kVⅠ段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454485671939"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,755.972,600.265) scale(0.865889,-0.865889) translate(116.148,-1295.92)" width="14" x="749.9109215148861" y="584.6793708845109"/></g>
  <g id="186">
   <use class="kv110" height="30" transform="rotate(0,1025.55,374.461) scale(0.962099,0.705539) translate(40.1161,151.866)" width="15" x="1018.336623772138" xlink:href="#Disconnector:刀闸_0" y="363.8775907893905" zvalue="1495"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454486982659" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454486982659"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1025.55,374.461) scale(0.962099,0.705539) translate(40.1161,151.866)" width="15" x="1018.336623772138" y="363.8775907893905"/></g>
  <g id="158">
   <use class="v10500" height="26" transform="rotate(0,1667.75,819.335) scale(0.865889,0.865889) translate(257.5,125.157)" width="12" x="1662.558279241383" xlink:href="#Disconnector:单手车刀闸1212_0" y="808.0788189807497" zvalue="1528"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454486392835" ObjectName="#2发电机0921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454486392835"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1667.75,819.335) scale(0.865889,0.865889) translate(257.5,125.157)" width="12" x="1662.558279241383" y="808.0788189807497"/></g>
  <g id="151">
   <use class="v10500" height="36" transform="rotate(0,1716.24,828.86) scale(0.865889,0.865889) translate(264.876,125.962)" width="14" x="1710.182192595233" xlink:href="#Disconnector:手车刀闸_0" y="813.2741549829885" zvalue="1535"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454486261763" ObjectName="#2发电机0922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454486261763"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1716.24,828.86) scale(0.865889,0.865889) translate(264.876,125.962)" width="14" x="1710.182192595233" y="813.2741549829885"/></g>
  <g id="129">
   <use class="v10500" height="30" transform="rotate(0,1455.03,686.181) scale(0.962099,0.705539) translate(57.035,281.964)" width="15" x="1447.817724482207" xlink:href="#Disconnector:刀闸_0" y="675.5977509236782" zvalue="1555"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454486065155" ObjectName="#2厂用变0342隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454486065155"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1455.03,686.181) scale(0.962099,0.705539) translate(57.035,281.964)" width="15" x="1447.817724482207" y="675.5977509236782"/></g>
  <g id="121">
   <use class="v10500" height="36" transform="rotate(0,1353.44,600.265) scale(0.865889,-0.865889) translate(208.684,-1295.92)" width="14" x="1347.37456177227" xlink:href="#Disconnector:手车刀闸_0" y="584.6793708845109" zvalue="1565"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454485868547" ObjectName="10.5kVⅡ段母线电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454485868547"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1353.44,600.265) scale(0.865889,-0.865889) translate(208.684,-1295.92)" width="14" x="1347.37456177227" y="584.6793708845109"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="54">
   <path class="kv110" d="M 1640.94 262.02 L 1640.94 277.96 L 1620.45 277.96" stroke-width="1" zvalue="1430"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="98" MaxPinNum="2"/>
   </metadata>
  <path d="M 1640.94 262.02 L 1640.94 277.96 L 1620.45 277.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv110" d="M 929.07 321 L 929.07 339.34" stroke-width="1" zvalue="20"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@0" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 929.07 321 L 929.07 339.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv110" d="M 929.09 300.37 L 929.09 278.78" stroke-width="1" zvalue="21"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@1" LinkObjectIDznd="19@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 929.09 300.37 L 929.09 278.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv110" d="M 929.11 260.39 L 929.07 227.49" stroke-width="1" zvalue="22"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="19@0" LinkObjectIDznd="22@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 929.11 260.39 L 929.07 227.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv110" d="M 929.09 206.85 L 929.09 188.09" stroke-width="1" zvalue="25"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@1" LinkObjectIDznd="27@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 929.09 206.85 L 929.09 188.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv110" d="M 904.63 244.39 L 929.09 244.39" stroke-width="1" zvalue="34"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8@0" LinkObjectIDznd="25" MaxPinNum="2"/>
   </metadata>
  <path d="M 904.63 244.39 L 929.09 244.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv110" d="M 904.63 198.85 L 929.09 198.85" stroke-width="1" zvalue="35"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 904.63 198.85 L 929.09 198.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv110" d="M 1621.1 310.61 L 1621.1 339.34" stroke-width="1" zvalue="98"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="6@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1621.1 310.61 L 1621.1 339.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv110" d="M 1621.13 289.98 L 1621.13 265.45" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@1" LinkObjectIDznd="68@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1621.13 289.98 L 1621.13 265.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv110" d="M 1602.3 277.96 L 1621.13 277.96" stroke-width="1" zvalue="100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="97" MaxPinNum="2"/>
   </metadata>
  <path d="M 1602.3 277.96 L 1621.13 277.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv110" d="M 1599.7 384 L 1599.6 399.03" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@1" LinkObjectIDznd="101@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1599.7 384 L 1599.6 399.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv110" d="M 1599.59 509.11 L 1486.69 509.11 L 1486.69 531.96" stroke-width="1" zvalue="112"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@2" LinkObjectIDznd="105@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1599.59 509.11 L 1486.69 509.11 L 1486.69 531.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv110" d="M 1582.69 387.3 L 1599.68 387.3" stroke-width="1" zvalue="117"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="108" MaxPinNum="2"/>
   </metadata>
  <path d="M 1582.69 387.3 L 1599.68 387.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="295">
   <path class="v10500" d="M 1599.56 568.63 L 1599.56 650.39" stroke-width="1" zvalue="609"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@1" LinkObjectIDznd="187@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1599.56 568.63 L 1599.56 650.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="296">
   <path class="kv110" d="M 1599.72 363.36 L 1599.72 339.34" stroke-width="1" zvalue="610"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="6@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1599.72 363.36 L 1599.72 339.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="334">
   <path class="v10500" d="M 1044.34 676.13 L 1044.34 650.39" stroke-width="1" zvalue="623"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@0" LinkObjectIDznd="15@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1044.34 676.13 L 1044.34 650.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv110" d="M 1602.3 316.66 L 1621.1 316.66" stroke-width="1" zvalue="1096"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 1602.3 316.66 L 1621.1 316.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="v10500" d="M 1091.14 830.56 L 1091.07 839.91" stroke-width="1" zvalue="1265"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="4@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1091.14 830.56 L 1091.07 839.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="kv110" d="M 1471.11 321 L 1471.11 339.34" stroke-width="1" zvalue="1437"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="6@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1471.11 321 L 1471.11 339.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="265">
   <path class="kv110" d="M 1471.14 300.37 L 1471.14 278.78" stroke-width="1" zvalue="1439"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@1" LinkObjectIDznd="269@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1471.14 300.37 L 1471.14 278.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="264">
   <path class="kv110" d="M 1471.16 260.39 L 1471.11 227.49" stroke-width="1" zvalue="1440"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="269@0" LinkObjectIDznd="267@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1471.16 260.39 L 1471.11 227.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="262">
   <path class="kv110" d="M 1471.14 206.85 L 1471.14 187.43" stroke-width="1" zvalue="1442"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@1" LinkObjectIDznd="263@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1471.14 206.85 L 1471.14 187.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="258">
   <path class="kv110" d="M 1446.68 288.37 L 1471.14 288.37" stroke-width="1" zvalue="1450"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@0" LinkObjectIDznd="265" MaxPinNum="2"/>
   </metadata>
  <path d="M 1446.68 288.37 L 1471.14 288.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="v10500" d="M 1091.07 808.14 L 1091.07 787.58 L 1044.32 787.61" stroke-width="1" zvalue="1456"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@1" LinkObjectIDznd="81" MaxPinNum="2"/>
   </metadata>
  <path d="M 1091.07 808.14 L 1091.07 787.58 L 1044.32 787.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="v10500" d="M 1139.56 856.3 L 1139.56 843.58" stroke-width="1" zvalue="1459"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@0" LinkObjectIDznd="12@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1139.56 856.3 L 1139.56 843.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="v10500" d="M 1169.87 866.45 L 1169.87 849.94 L 1139.56 849.94" stroke-width="1" zvalue="1460"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@0" LinkObjectIDznd="83" MaxPinNum="2"/>
   </metadata>
  <path d="M 1169.87 866.45 L 1169.87 849.94 L 1139.56 849.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="v10500" d="M 741.63 675.95 L 741.63 650.39" stroke-width="1" zvalue="1466"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@0" LinkObjectIDznd="15@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 741.63 675.95 L 741.63 650.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="v10500" d="M 741.6 696.58 L 741.6 743.76" stroke-width="1" zvalue="1467"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@1" LinkObjectIDznd="152@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 741.6 696.58 L 741.6 743.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="v10500" d="M 742.12 809.34 L 742.12 775.37" stroke-width="1" zvalue="1468"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@0" LinkObjectIDznd="152@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 742.12 809.34 L 742.12 775.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="v10500" d="M 878.44 675.95 L 878.44 650.39" stroke-width="1" zvalue="1479"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="15@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 878.44 675.95 L 878.44 650.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="v10500" d="M 878.41 696.58 L 878.41 743.76" stroke-width="1" zvalue="1481"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@1" LinkObjectIDznd="249@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 878.41 696.58 L 878.41 743.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="223">
   <path class="v10500" d="M 878.93 809.47 L 878.93 775.37" stroke-width="1" zvalue="1482"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="228@0" LinkObjectIDznd="249@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 878.93 809.47 L 878.93 775.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="v10500" d="M 755.97 614.99 L 755.97 650.39" stroke-width="1" zvalue="1489"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@0" LinkObjectIDznd="15@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.97 614.99 L 755.97 650.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="v10500" d="M 755.97 585.55 L 755.97 573.53" stroke-width="1" zvalue="1490"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@1" LinkObjectIDznd="3@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.97 585.55 L 755.97 573.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="v10500" d="M 1139.56 814.14 L 1139.56 787.58 L 1091.07 787.58" stroke-width="1" zvalue="1491"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="12@0" LinkObjectIDznd="80" MaxPinNum="2"/>
   </metadata>
  <path d="M 1139.56 814.14 L 1139.56 787.58 L 1091.07 787.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv110" d="M 1025.61 384.86 L 1025.52 399.89" stroke-width="1" zvalue="1503"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@1" LinkObjectIDznd="185@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1025.61 384.86 L 1025.52 399.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv110" d="M 1025.64 509.98 L 912.6 509.98 L 912.6 532.82" stroke-width="1" zvalue="1505"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@2" LinkObjectIDznd="181@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1025.64 509.98 L 912.6 509.98 L 912.6 532.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="v10500" d="M 1025.61 569.5 L 1025.61 650.39" stroke-width="1" zvalue="1510"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@1" LinkObjectIDznd="15@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1025.61 569.5 L 1025.61 650.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv110" d="M 1025.64 364.23 L 1025.64 339.34" stroke-width="1" zvalue="1511"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="6@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1025.64 364.23 L 1025.64 339.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="v10500" d="M 1621 676.99 L 1621 650.39" stroke-width="1" zvalue="1517"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@0" LinkObjectIDznd="187@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1621 676.99 L 1621 650.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="v10500" d="M 1667.83 830.56 L 1667.75 839.91" stroke-width="1" zvalue="1530"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="160@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1667.83 830.56 L 1667.75 839.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="v10500" d="M 1716.24 854.4 L 1716.24 843.58" stroke-width="1" zvalue="1540"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@0" LinkObjectIDznd="151@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1716.24 854.4 L 1716.24 843.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="v10500" d="M 1746.55 866.45 L 1746.55 849.58 L 1716.24 849.58" stroke-width="1" zvalue="1541"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="149@0" LinkObjectIDznd="147" MaxPinNum="2"/>
   </metadata>
  <path d="M 1746.55 866.45 L 1746.55 849.58 L 1716.24 849.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="v10500" d="M 1455.12 675.95 L 1455.12 650.39" stroke-width="1" zvalue="1559"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="187@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455.12 675.95 L 1455.12 650.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="v10500" d="M 1455.09 696.58 L 1455.09 743.76" stroke-width="1" zvalue="1561"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@1" LinkObjectIDznd="131@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455.09 696.58 L 1455.09 743.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="v10500" d="M 1455.61 814.84 L 1455.61 775.37" stroke-width="1" zvalue="1562"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="131@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455.61 814.84 L 1455.61 775.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="v10500" d="M 1455.63 709.75 L 1455.63 709.62" stroke-width="1" zvalue="1564"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@0" LinkObjectIDznd="125" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455.63 709.75 L 1455.63 709.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="v10500" d="M 1353.44 614.99 L 1353.44 650.39" stroke-width="1" zvalue="1569"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@0" LinkObjectIDznd="187@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1353.44 614.99 L 1353.44 650.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="v10500" d="M 1353.44 585.55 L 1353.44 573.53" stroke-width="1" zvalue="1570"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@1" LinkObjectIDznd="112@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1353.44 585.55 L 1353.44 573.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="v10500" d="M 1044.34 707.73 L 1044.31 831.35" stroke-width="1" zvalue="1572"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@1" LinkObjectIDznd="850@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1044.34 707.73 L 1044.31 831.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="v10500" d="M 1018.37 735.03 L 1018.37 715.78 L 1044.34 715.78" stroke-width="1" zvalue="1575"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="81" MaxPinNum="2"/>
   </metadata>
  <path d="M 1018.37 735.03 L 1018.37 715.78 L 1044.34 715.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv10" d="M 1284.3 912.73 L 1284.3 889.03" stroke-width="1" zvalue="1581"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="196@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1284.3 912.73 L 1284.3 889.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv110" d="M 1025.62 418.28 L 1025.67 485.87" stroke-width="1" zvalue="1602"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@1" LinkObjectIDznd="163@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1025.62 418.28 L 1025.67 485.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv110" d="M 1599.71 417.42 L 1599.62 485" stroke-width="1" zvalue="1603"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@1" LinkObjectIDznd="73@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1599.71 417.42 L 1599.62 485" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="v10500" d="M 1599.4 731.66 L 1599.4 720.22 L 1621 720.22" stroke-width="1" zvalue="1646"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@0" LinkObjectIDznd="139" MaxPinNum="2"/>
   </metadata>
  <path d="M 1599.4 731.66 L 1599.4 720.22 L 1621 720.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="v10500" d="M 1716.24 814.14 L 1716.24 786.95 L 1621 786.98" stroke-width="1" zvalue="1647"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@0" LinkObjectIDznd="139" MaxPinNum="2"/>
   </metadata>
  <path d="M 1716.24 814.14 L 1716.24 786.95 L 1621 786.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="v10500" d="M 1667.76 808.14 L 1667.76 786.97" stroke-width="1" zvalue="1648"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@1" LinkObjectIDznd="60" MaxPinNum="2"/>
   </metadata>
  <path d="M 1667.76 808.14 L 1667.76 786.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="v10500" d="M 742.14 709.75 L 742.14 709.62" stroke-width="1" zvalue="1649"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@0" LinkObjectIDznd="119" MaxPinNum="2"/>
   </metadata>
  <path d="M 742.14 709.75 L 742.14 709.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="v10500" d="M 878.95 709.75 L 878.95 709.62" stroke-width="1" zvalue="1650"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@0" LinkObjectIDznd="224" MaxPinNum="2"/>
   </metadata>
  <path d="M 878.95 709.75 L 878.95 709.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="v10500" d="M 1621 708.6 L 1621 831.35" stroke-width="1" zvalue="1651"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@1" LinkObjectIDznd="161@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1621 708.6 L 1621 831.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="289">
   <path class="kv110" d="M 904.63 288.37 L 929.09 288.37" stroke-width="1" zvalue="1710"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="24" MaxPinNum="2"/>
   </metadata>
  <path d="M 904.63 288.37 L 929.09 288.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="290">
   <path class="kv110" d="M 1446.68 194.85 L 1471.14 194.85" stroke-width="1" zvalue="1711"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@0" LinkObjectIDznd="262" MaxPinNum="2"/>
   </metadata>
  <path d="M 1446.68 194.85 L 1471.14 194.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="291">
   <path class="kv110" d="M 1446.68 244.39 L 1458.91 244.39 L 1458.91 244.39 L 1471.14 244.39" stroke-width="1" zvalue="1712"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="260@0" LinkObjectIDznd="264" MaxPinNum="2"/>
   </metadata>
  <path d="M 1446.68 244.39 L 1458.91 244.39 L 1458.91 244.39 L 1471.14 244.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="292">
   <path class="v10500" d="M 725.12 787.37 L 742.12 787.37" stroke-width="1" zvalue="1713"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="136" MaxPinNum="2"/>
   </metadata>
  <path d="M 725.12 787.37 L 742.12 787.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="293">
   <path class="v10500" d="M 861.93 787.37 L 878.93 787.37" stroke-width="1" zvalue="1714"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@0" LinkObjectIDznd="223" MaxPinNum="2"/>
   </metadata>
  <path d="M 861.93 787.37 L 878.93 787.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="294">
   <path class="v10500" d="M 1438.61 787.37 L 1455.61 787.37" stroke-width="1" zvalue="1715"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="124" MaxPinNum="2"/>
   </metadata>
  <path d="M 1438.61 787.37 L 1455.61 787.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="297">
   <path class="kv110" d="M 1004.6 392.17 L 1015.08 392.17 L 1015.08 392.18 L 1025.57 392.18" stroke-width="1" zvalue="1716"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@0" LinkObjectIDznd="180" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004.6 392.17 L 1015.08 392.17 L 1015.08 392.18 L 1025.57 392.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="6">
   <path class="kv110" d="M 779.35 339.34 L 1699.79 339.34" stroke-width="4" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674414297091" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674414297091"/></metadata>
  <path d="M 779.35 339.34 L 1699.79 339.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="v10500" d="M 698.07 650.39 L 1163.81 650.39" stroke-width="4" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674414362627" ObjectName="10.5kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674414362627"/></metadata>
  <path d="M 698.07 650.39 L 1163.81 650.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="v10500" d="M 1274.75 650.39 L 1792.44 650.39" stroke-width="4" zvalue="1493"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674414428163" ObjectName="10.5kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674414428163"/></metadata>
  <path d="M 1274.75 650.39 L 1792.44 650.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="19">
   <use class="kv110" height="20" transform="rotate(0,929.15,269.592) scale(1.05831,0.962099) translate(-50.9014,10.2412)" width="10" x="923.8584676638959" xlink:href="#Breaker:开关_0" y="259.9708707446281" zvalue="16"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925176852483" ObjectName="110kV芒遮线131断路器"/>
   <cge:TPSR_Ref TObjectID="6473925176852483"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,929.15,269.592) scale(1.05831,0.962099) translate(-50.9014,10.2412)" width="10" x="923.8584676638959" y="259.9708707446281"/></g>
  <g id="101">
   <use class="kv110" height="20" transform="rotate(0,1599.64,408.23) scale(1.05831,0.962099) translate(-87.8429,15.7027)" width="10" x="1594.345450550267" xlink:href="#Breaker:开关_0" y="398.6093740636119" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925176918019" ObjectName="#2主变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473925176918019"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1599.64,408.23) scale(1.05831,0.962099) translate(-87.8429,15.7027)" width="10" x="1594.345450550267" y="398.6093740636119"/></g>
  <g id="341">
   <use class="v10500" height="20" transform="rotate(0,1044.34,692.146) scale(1.73178,1.73178) translate(-437.636,-285.155)" width="10" x="1035.680005626482" xlink:href="#Breaker:小车断路器_0" y="674.828072983986" zvalue="612"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925176983555" ObjectName="#1发电机031断路器"/>
   <cge:TPSR_Ref TObjectID="6473925176983555"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1044.34,692.146) scale(1.73178,1.73178) translate(-437.636,-285.155)" width="10" x="1035.680005626482" y="674.828072983986"/></g>
  <g id="152">
   <use class="v10500" height="20" transform="rotate(0,742.118,759.781) scale(1.73178,1.73178) translate(-309.93,-313.735)" width="10" x="733.4590241744654" xlink:href="#Breaker:小车断路器_0" y="742.463652407096" zvalue="1305"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925177049091" ObjectName="坝区变035断路器"/>
   <cge:TPSR_Ref TObjectID="6473925177049091"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,742.118,759.781) scale(1.73178,1.73178) translate(-309.93,-313.735)" width="10" x="733.4590241744654" y="742.463652407096"/></g>
  <g id="269">
   <use class="kv110" height="20" transform="rotate(0,1471.2,269.592) scale(1.05831,0.962099) translate(-80.7663,10.2412)" width="10" x="1465.905190564074" xlink:href="#Breaker:开关_0" y="259.9708707446281" zvalue="1432"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925177114627" ObjectName="110kV备用线132断路器"/>
   <cge:TPSR_Ref TObjectID="6473925177114627"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1471.2,269.592) scale(1.05831,0.962099) translate(-80.7663,10.2412)" width="10" x="1465.905190564074" y="259.9708707446281"/></g>
  <g id="249">
   <use class="v10500" height="20" transform="rotate(0,878.928,759.781) scale(1.73178,1.73178) translate(-367.74,-313.735)" width="10" x="870.2695389000694" xlink:href="#Breaker:小车断路器_0" y="742.463652407096" zvalue="1472"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925177180163" ObjectName="#1厂用变033断路器"/>
   <cge:TPSR_Ref TObjectID="6473925177180163"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,878.928,759.781) scale(1.73178,1.73178) translate(-367.74,-313.735)" width="10" x="870.2695389000694" y="742.463652407096"/></g>
  <g id="185">
   <use class="kv110" height="20" transform="rotate(0,1025.55,409.096) scale(1.05831,0.962099) translate(-56.2129,15.7368)" width="10" x="1020.260822302954" xlink:href="#Breaker:开关_0" y="399.4752633973184" zvalue="1497"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925177376771" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925177376771"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1025.55,409.096) scale(1.05831,0.962099) translate(-56.2129,15.7368)" width="10" x="1020.260822302954" y="399.4752633973184"/></g>
  <g id="172">
   <use class="v10500" height="20" transform="rotate(0,1621,693.012) scale(1.73178,1.73178) translate(-681.307,-285.521)" width="10" x="1612.336697886415" xlink:href="#Breaker:小车断路器_0" y="675.6939623176925" zvalue="1512"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925177311235" ObjectName="#2发电机032断路器"/>
   <cge:TPSR_Ref TObjectID="6473925177311235"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1621,693.012) scale(1.73178,1.73178) translate(-681.307,-285.521)" width="10" x="1612.336697886415" y="675.6939623176925"/></g>
  <g id="131">
   <use class="v10500" height="20" transform="rotate(0,1455.61,759.781) scale(1.73178,1.73178) translate(-611.423,-313.735)" width="10" x="1446.951835148501" xlink:href="#Breaker:小车断路器_0" y="742.463652407096" zvalue="1552"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925177245699" ObjectName="#2厂用变034断路器"/>
   <cge:TPSR_Ref TObjectID="6473925177245699"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1455.61,759.781) scale(1.73178,1.73178) translate(-611.423,-313.735)" width="10" x="1446.951835148501" y="742.463652407096"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="27">
   <use class="kv110" height="40" transform="rotate(0,932.71,170.111) scale(1.09954,0.962099) translate(-82.6967,5.9433)" width="35" x="913.4685079608061" xlink:href="#ACLineSegment:线路电压互感器_0" y="150.868814697627" zvalue="24"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249327337474" ObjectName="110kV芒遮线"/>
   <cge:TPSR_Ref TObjectID="8444249327337474_5066549682503681"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,932.71,170.111) scale(1.09954,0.962099) translate(-82.6967,5.9433)" width="35" x="913.4685079608061" y="150.868814697627"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="1">
   <use class="kv110" height="20" transform="rotate(90,895.251,288.319) scale(0.962099,0.962099) translate(35.0778,10.979)" width="10" x="890.4400946266946" xlink:href="#GroundDisconnector:地刀_0" y="278.698490715218" zvalue="26"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454482984963" ObjectName="110kV芒遮线13117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454482984963"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,895.251,288.319) scale(0.962099,0.962099) translate(35.0778,10.979)" width="10" x="890.4400946266946" y="278.698490715218"/></g>
  <g id="8">
   <use class="kv110" height="20" transform="rotate(90,895.251,244.34) scale(0.962099,0.962099) translate(35.0778,9.24646)" width="10" x="890.4400946266945" xlink:href="#GroundDisconnector:地刀_0" y="234.7186828640989" zvalue="28"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454483116035" ObjectName="110kV芒遮线13160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454483116035"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,895.251,244.34) scale(0.962099,0.962099) translate(35.0778,9.24646)" width="10" x="890.4400946266945" y="234.7186828640989"/></g>
  <g id="32">
   <use class="kv110" height="20" transform="rotate(90,895.251,198.803) scale(0.962099,0.962099) translate(35.0778,7.45261)" width="10" x="890.4400945349417" xlink:href="#GroundDisconnector:地刀_0" y="189.1824426749308" zvalue="31"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454483247107" ObjectName="110kV芒遮线13167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454483247107"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,895.251,198.803) scale(0.962099,0.962099) translate(35.0778,7.45261)" width="10" x="890.4400945349417" y="189.1824426749308"/></g>
  <g id="91">
   <use class="kv110" height="20" transform="rotate(90,1592.92,277.914) scale(0.962099,0.962099) translate(62.5615,10.5691)" width="10" x="1588.106378559533" xlink:href="#GroundDisconnector:地刀_0" y="268.293020440771" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454483443715" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454483443715"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1592.92,277.914) scale(0.962099,0.962099) translate(62.5615,10.5691)" width="10" x="1588.106378559533" y="268.293020440771"/></g>
  <g id="105">
   <use class="kv110" height="40" transform="rotate(0,1499.58,543.694) scale(0.962099,-0.962099) translate(58.3161,-1109.56)" width="40" x="1480.336679459179" xlink:href="#GroundDisconnector:中性点地刀12_0" y="524.4519572289355" zvalue="105"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454483705859" ObjectName="#2主变110kV侧1020中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454483705859"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1499.58,543.694) scale(0.962099,-0.962099) translate(58.3161,-1109.56)" width="40" x="1480.336679459179" y="524.4519572289355"/></g>
  <g id="114">
   <use class="kv110" height="20" transform="rotate(90,1573.3,387.257) scale(0.962099,0.962099) translate(61.7889,14.8765)" width="10" x="1568.494132742991" xlink:href="#GroundDisconnector:地刀_0" y="377.6356102027247" zvalue="114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454483836931" ObjectName="#2主变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454483836931"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1573.3,387.257) scale(0.962099,0.962099) translate(61.7889,14.8765)" width="10" x="1568.494132742991" y="377.6356102027247"/></g>
  <g id="70">
   <use class="kv110" height="20" transform="rotate(90,1592.92,316.61) scale(0.962099,0.962099) translate(62.5615,12.0935)" width="10" x="1588.106378559533" xlink:href="#GroundDisconnector:地刀_0" y="306.9888546126027" zvalue="1095"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454484033539" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454484033539"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1592.92,316.61) scale(0.962099,0.962099) translate(62.5615,12.0935)" width="10" x="1588.106378559533" y="306.9888546126027"/></g>
  <g id="261">
   <use class="kv110" height="20" transform="rotate(90,1437.3,288.319) scale(0.962099,0.962099) translate(56.4311,10.979)" width="10" x="1432.486817526873" xlink:href="#GroundDisconnector:地刀_0" y="278.6985024595934" zvalue="1444"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454484754435" ObjectName="110kV备用线13217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454484754435"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1437.3,288.319) scale(0.962099,0.962099) translate(56.4311,10.979)" width="10" x="1432.486817526873" y="278.6985024595934"/></g>
  <g id="260">
   <use class="kv110" height="20" transform="rotate(90,1437.3,244.34) scale(0.962099,0.962099) translate(56.4311,9.24646)" width="10" x="1432.486817526873" xlink:href="#GroundDisconnector:地刀_0" y="234.7186828640989" zvalue="1446"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454484623363" ObjectName="110kV备用线13260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454484623363"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1437.3,244.34) scale(0.962099,0.962099) translate(56.4311,9.24646)" width="10" x="1432.486817526873" y="234.7186828640989"/></g>
  <g id="259">
   <use class="kv110" height="20" transform="rotate(90,1437.3,194.803) scale(0.962099,0.962099) translate(56.4311,7.29504)" width="10" x="1432.48681743512" xlink:href="#GroundDisconnector:地刀_0" y="185.1824426749312" zvalue="1448"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454484492291" ObjectName="110kV备用线13267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454484492291"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1437.3,194.803) scale(0.962099,0.962099) translate(56.4311,7.29504)" width="10" x="1432.48681743512" y="185.1824426749312"/></g>
  <g id="181">
   <use class="kv110" height="40" transform="rotate(0,925.494,544.56) scale(0.962099,-0.962099) translate(35.7007,-1111.33)" width="40" x="906.2520512118666" xlink:href="#GroundDisconnector:中性点地刀12_0" y="525.3178465626419" zvalue="1501"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454486851587" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454486851587"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,925.494,544.56) scale(0.962099,-0.962099) translate(35.7007,-1111.33)" width="40" x="906.2520512118666" y="525.3178465626419"/></g>
  <g id="177">
   <use class="kv110" height="20" transform="rotate(90,995.22,392.122) scale(0.962099,0.962099) translate(39.016,15.0682)" width="10" x="990.409504495678" xlink:href="#GroundDisconnector:地刀_0" y="382.5014995364311" zvalue="1506"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454486720515" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454486720515"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,995.22,392.122) scale(0.962099,0.962099) translate(39.016,15.0682)" width="10" x="990.409504495678" y="382.5014995364311"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="657">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="657" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,932.71,72.0918) scale(1,1) translate(0,9.79217e-14)" writing-mode="lr" x="932.88" xml:space="preserve" y="76.95" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135400026114" ObjectName="P"/>
   </metadata>
  </g>
  <g id="659">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="659" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,932.71,89.6759) scale(1,1) translate(0,1.25253e-13)" writing-mode="lr" x="932.88" xml:space="preserve" y="94.53" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135400091650" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="661">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="661" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,932.71,107.26) scale(1,1) translate(0,1.52584e-13)" writing-mode="lr" x="932.88" xml:space="preserve" y="112.12" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135400157186" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="663">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="663" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,932.71,124.844) scale(1,1) translate(0,1.79915e-13)" writing-mode="lr" x="932.88" xml:space="preserve" y="129.7" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135400878082" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="690">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="690" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1620.32,118.109) scale(1,1) translate(3.51132e-13,1.69447e-13)" writing-mode="lr" x="1620.49" xml:space="preserve" y="122.96" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135398453250" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="695">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="695" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1620.32,133.262) scale(1,1) translate(3.51132e-13,1.93e-13)" writing-mode="lr" x="1620.49" xml:space="preserve" y="138.12" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135398518786" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="700">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="700" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1620.32,148.415) scale(1,1) translate(3.51132e-13,2.16552e-13)" writing-mode="lr" x="1620.49" xml:space="preserve" y="153.27" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135398584322" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="705">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="705" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1620.32,163.569) scale(1,1) translate(3.51132e-13,2.40105e-13)" writing-mode="lr" x="1620.49" xml:space="preserve" y="168.42" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135398715394" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="710">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="710" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1620.32,178.722) scale(1,1) translate(3.51132e-13,2.63658e-13)" writing-mode="lr" x="1620.49" xml:space="preserve" y="183.58" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135398912002" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="216">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="216" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1044.75,922.254) scale(1,1) translate(1.10655e-13,1.01189e-13)" writing-mode="lr" x="1044.95" xml:space="preserve" y="928.6799999999999" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135404154882" ObjectName="P"/>
   </metadata>
  </g>
  <g id="218">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="218" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1044.75,940.437) scale(1,1) translate(1.10655e-13,1.03208e-13)" writing-mode="lr" x="1044.95" xml:space="preserve" y="946.86" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135404220418" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="220">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="220" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1044.75,958.621) scale(1,1) translate(1.10655e-13,1.05227e-13)" writing-mode="lr" x="1044.95" xml:space="preserve" y="965.04" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135404285954" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" SignFlag="down" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="143" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1682.45,391.153) scale(1,1) translate(-3.62908e-13,4.22251e-14)" writing-mode="lr" x="1682.66" xml:space="preserve" y="397.58" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135402123266" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" SignFlag="down" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="145" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1683.31,413.666) scale(1,1) translate(-3.631e-13,4.47245e-14)" writing-mode="lr" x="1683.52" xml:space="preserve" y="420.09" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135402188802" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="156">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="156" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1683.31,437.045) scale(1,1) translate(-3.631e-13,4.73201e-14)" writing-mode="lr" x="1683.52" xml:space="preserve" y="443.47" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135402385410" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="42">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="42" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1094.45,395.483) scale(1,1) translate(1.16173e-13,4.27057e-14)" writing-mode="lr" x="1094.66" xml:space="preserve" y="401.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135411363842" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="43">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="43" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1094.45,418.862) scale(1,1) translate(1.16173e-13,4.53013e-14)" writing-mode="lr" x="1094.66" xml:space="preserve" y="425.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135411429378" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="46">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="46" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1094.45,582.654) scale(1,1) translate(1.16173e-13,6.3486e-14)" writing-mode="lr" x="1094.66" xml:space="preserve" y="589.08" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135411494914" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="50">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="50" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1094.45,606.033) scale(1,1) translate(1.16173e-13,6.60816e-14)" writing-mode="lr" x="1094.66" xml:space="preserve" y="612.46" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135411560450" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="132">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="132" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1094.45,443.106) scale(1,1) translate(1.16173e-13,4.7993e-14)" writing-mode="lr" x="1094.66" xml:space="preserve" y="449.53" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135411625986" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="133">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="133" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1094.45,629.412) scale(1,1) translate(1.16173e-13,-4.12063e-13)" writing-mode="lr" x="1094.66" xml:space="preserve" y="635.84" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135411953666" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="134" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1621.43,922.254) scale(1,1) translate(-3.49359e-13,1.01189e-13)" writing-mode="lr" x="1621.64" xml:space="preserve" y="928.6799999999999" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135410708482" ObjectName="P"/>
   </metadata>
  </g>
  <g id="135">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="135" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1621.43,940.437) scale(1,1) translate(-3.49359e-13,1.03208e-13)" writing-mode="lr" x="1621.64" xml:space="preserve" y="946.86" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135410774018" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="138" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1621.43,958.621) scale(1,1) translate(-3.49359e-13,1.05227e-13)" writing-mode="lr" x="1621.64" xml:space="preserve" y="965.04" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135410839554" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="141" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,760.417,417.899) scale(1,1) translate(1.60194e-13,0)" writing-mode="lr" x="760.59" xml:space="preserve" y="422.75" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135398977538" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="142" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,760.417,437.815) scale(1,1) translate(1.60194e-13,0)" writing-mode="lr" x="760.59" xml:space="preserve" y="442.67" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135399043074" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,760.417,457.73) scale(1,1) translate(1.60194e-13,0)" writing-mode="lr" x="760.59" xml:space="preserve" y="462.59" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135399108610" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="154">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="154" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,760.417,473.749) scale(1,1) translate(1.60194e-13,0)" writing-mode="lr" x="760.59" xml:space="preserve" y="478.6" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135399239682" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="155">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="155" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1353.55,425.692) scale(1,1) translate(2.91897e-13,0)" writing-mode="lr" x="1353.72" xml:space="preserve" y="430.55" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135414444034" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="162">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="162" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1353.55,445.608) scale(1,1) translate(2.91897e-13,0)" writing-mode="lr" x="1353.72" xml:space="preserve" y="450.46" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135414509570" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="164">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="164" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1353.55,465.523) scale(1,1) translate(2.91897e-13,0)" writing-mode="lr" x="1353.72" xml:space="preserve" y="470.38" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135414575106" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="165">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="165" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1353.55,480.676) scale(1,1) translate(2.91897e-13,0)" writing-mode="lr" x="1353.72" xml:space="preserve" y="485.53" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135414706178" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="205">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="205" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135447474178" ObjectName="F"/>
   </metadata>
  </g>
  <g id="221">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="221" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135447539714" ObjectName="F"/>
   </metadata>
  </g>
  <g id="204">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="204" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,166.222,359.167) scale(1,1) translate(0,0)" writing-mode="lr" x="166.38" xml:space="preserve" y="364.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135399370754" ObjectName="F"/>
   </metadata>
  </g>
  <g id="199">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="199" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135447343106" ObjectName="F"/>
   </metadata>
  </g>
  <g id="190">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="190" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135447408642" ObjectName="F"/>
   </metadata>
  </g>
  <g id="179">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="179" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135501213698" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="120">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124912562181" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124912496647" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="103" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,403.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="408.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135501279234" ObjectName="坝下水位"/>
   </metadata>
  </g>
  <g id="217">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="217" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1697.3,564.834) scale(1,1) translate(-1.83716e-12,1.23288e-13)" writing-mode="lr" x="1696.86" xml:space="preserve" y="570.99" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135402254338" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="219">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="219" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1697.3,591.313) scale(1,1) translate(-1.83716e-12,-1.29167e-13)" writing-mode="lr" x="1696.86" xml:space="preserve" y="597.47" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135402319874" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="235">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="235" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1697.3,615.351) scale(1,1) translate(-1.83716e-12,-4.03515e-13)" writing-mode="lr" x="1696.86" xml:space="preserve" y="621.5" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135402713090" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,344.347,356.667) scale(1,1) translate(0,0)" writing-mode="lr" x="344.5" xml:space="preserve" y="361.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135414837250" ObjectName="F"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,335.5) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="340.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135398846466" ObjectName="F"/>
   </metadata>
  </g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="AccessoryClass">
  <g id="68">
   <use class="kv110" height="30" transform="rotate(0,1621.2,247.199) scale(1.50087,-1.31327) translate(-533.518,-430.732)" width="30" x="1598.691085894151" xlink:href="#Accessory:带熔断器35kVPT11_0" y="227.5000207306394" zvalue="1093"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454483902467" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1621.2,247.199) scale(1.50087,-1.31327) translate(-533.518,-430.732)" width="30" x="1598.691085894151" y="227.5000207306394"/></g>
  <g id="4">
   <use class="v10500" height="35" transform="rotate(0,1091.07,854.837) scale(0.865889,0.865889) translate(167.311,130.052)" width="25" x="1080.24770232386" xlink:href="#Accessory:PT232_0" y="839.6837796610321" zvalue="1262"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454484164611" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1091.07,854.837) scale(0.865889,0.865889) translate(167.311,130.052)" width="25" x="1080.24770232386" y="839.6837796610321"/></g>
  <g id="150">
   <use class="v10500" height="26" transform="rotate(90,714.409,787.396) scale(-0.865889,0.865889) translate(-1540.27,120.21)" width="12" x="709.2141228306871" xlink:href="#Accessory:避雷器1_0" y="776.1397487275448" zvalue="1309"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454484295683" ObjectName="坝区变避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,714.409,787.396) scale(-0.865889,0.865889) translate(-1540.27,120.21)" width="12" x="709.2141228306871" y="776.1397487275448"/></g>
  <g id="52">
   <use class="kv110" height="26" transform="rotate(0,1640.91,251.312) scale(0.865889,-0.865889) translate(253.343,-543.291)" width="12" x="1635.715709896486" xlink:href="#Accessory:避雷器1_0" y="240.0554160693815" zvalue="1429"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454484361219" ObjectName="110kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1640.91,251.312) scale(0.865889,-0.865889) translate(253.343,-543.291)" width="12" x="1635.715709896486" y="240.0554160693815"/></g>
  <g id="10">
   <use class="v10500" height="40" transform="rotate(0,1139.56,872.318) scale(0.865889,-0.865889) translate(174.486,-1882.42)" width="30" x="1126.57278167715" xlink:href="#Accessory:带熔断器的线路PT1_0" y="854.9998011755858" zvalue="1453"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454485016579" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1139.56,872.318) scale(0.865889,-0.865889) translate(174.486,-1882.42)" width="30" x="1126.57278167715" y="854.9998011755858"/></g>
  <g id="77">
   <use class="v10500" height="18" transform="rotate(0,1169.87,873.021) scale(0.865889,0.865889) translate(180.253,134.008)" width="14" x="1163.806023026523" xlink:href="#Accessory:PT7_0" y="865.2275150053695" zvalue="1455"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454485147651" ObjectName="#1励磁变"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1169.87,873.021) scale(0.865889,0.865889) translate(180.253,134.008)" width="14" x="1163.806023026523" y="865.2275150053695"/></g>
  <g id="88">
   <use class="v10500" height="18" transform="rotate(0,742.118,718.892) scale(1.29883,1.15452) translate(-169.251,-94.8248)" width="10" x="735.6237475087311" xlink:href="#Accessory:熔断器_0" y="708.5015456045196" zvalue="1463"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454485278723" ObjectName="坝区变融断器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,742.118,718.892) scale(1.29883,1.15452) translate(-169.251,-94.8248)" width="10" x="735.6237475087311" y="708.5015456045196"/></g>
  <g id="246">
   <use class="v10500" height="26" transform="rotate(90,851.22,787.396) scale(-0.865889,0.865889) translate(-1835.08,120.21)" width="12" x="846.0246375562913" xlink:href="#Accessory:避雷器1_0" y="776.139748727545" zvalue="1474"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454485606403" ObjectName="#1厂用变避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,851.22,787.396) scale(-0.865889,0.865889) translate(-1835.08,120.21)" width="12" x="846.0246375562913" y="776.139748727545"/></g>
  <g id="229">
   <use class="v10500" height="18" transform="rotate(0,878.928,718.892) scale(1.29883,1.15452) translate(-200.729,-94.8248)" width="10" x="872.4342622343352" xlink:href="#Accessory:熔断器_0" y="708.5015456045196" zvalue="1477"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454485475331" ObjectName="#1厂用变融断器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,878.928,718.892) scale(1.29883,1.15452) translate(-200.729,-94.8248)" width="10" x="872.4342622343352" y="708.5015456045196"/></g>
  <g id="3">
   <use class="v10500" height="40" transform="rotate(0,768.095,550.91) scale(1.1906,-1.1906) translate(-119.149,-1009.81)" width="40" x="744.2826408457946" xlink:href="#Accessory:五卷PT_0" y="527.0977301930383" zvalue="1488"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454485737475" ObjectName="10.5kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,768.095,550.91) scale(1.1906,-1.1906) translate(-119.149,-1009.81)" width="40" x="744.2826408457946" y="527.0977301930383"/></g>
  <g id="169">
   <use class="v10500" height="26" transform="rotate(0,1599.37,742.367) scale(0.865889,0.865889) translate(246.91,113.236)" width="12" x="1594.17862586708" xlink:href="#Accessory:避雷器1_0" y="731.110879674899" zvalue="1516"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454486589443" ObjectName="#2发电机避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1599.37,742.367) scale(0.865889,0.865889) translate(246.91,113.236)" width="12" x="1594.17862586708" y="731.110879674899"/></g>
  <g id="160">
   <use class="v10500" height="35" transform="rotate(0,1667.75,854.837) scale(0.865889,0.865889) translate(256.629,130.052)" width="25" x="1656.929998572292" xlink:href="#Accessory:PT232_0" y="839.6837796610321" zvalue="1527"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454486458371" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1667.75,854.837) scale(0.865889,0.865889) translate(256.629,130.052)" width="25" x="1656.929998572292" y="839.6837796610321"/></g>
  <g id="153">
   <use class="v10500" height="40" transform="rotate(0,1716.24,870.423) scale(0.865889,-0.865889) translate(263.804,-1878.34)" width="30" x="1703.255077925582" xlink:href="#Accessory:带熔断器的线路PT1_0" y="853.1050643334806" zvalue="1534"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454486327299" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1716.24,870.423) scale(0.865889,-0.865889) translate(263.804,-1878.34)" width="30" x="1703.255077925582" y="853.1050643334806"/></g>
  <g id="149">
   <use class="v10500" height="18" transform="rotate(0,1746.55,873.021) scale(0.865889,0.865889) translate(269.57,134.008)" width="14" x="1740.488319274955" xlink:href="#Accessory:PT7_0" y="865.2275150053695" zvalue="1537"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454486196227" ObjectName="#2励磁变"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1746.55,873.021) scale(0.865889,0.865889) translate(269.57,134.008)" width="14" x="1740.488319274955" y="865.2275150053695"/></g>
  <g id="130">
   <use class="v10500" height="26" transform="rotate(90,1427.9,787.396) scale(-0.865889,0.865889) translate(-3077.77,120.21)" width="12" x="1422.706933804723" xlink:href="#Accessory:避雷器1_0" y="776.1397487275448" zvalue="1554"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454486130691" ObjectName="#2厂用变避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1427.9,787.396) scale(-0.865889,0.865889) translate(-3077.77,120.21)" width="12" x="1422.706933804723" y="776.1397487275448"/></g>
  <g id="128">
   <use class="v10500" height="18" transform="rotate(0,1455.61,718.892) scale(1.29883,1.15452) translate(-333.411,-94.8248)" width="10" x="1449.116558482767" xlink:href="#Accessory:熔断器_0" y="708.5015456045196" zvalue="1557"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454485999619" ObjectName="#2厂用变融断器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1455.61,718.892) scale(1.29883,1.15452) translate(-333.411,-94.8248)" width="10" x="1449.116558482767" y="708.5015456045196"/></g>
  <g id="112">
   <use class="v10500" height="40" transform="rotate(0,1365.56,550.91) scale(1.1906,-1.1906) translate(-214.795,-1009.81)" width="40" x="1341.746281103179" xlink:href="#Accessory:五卷PT_0" y="527.0977301930383" zvalue="1567"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454485803011" ObjectName="10.5kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1365.56,550.91) scale(1.1906,-1.1906) translate(-214.795,-1009.81)" width="40" x="1341.746281103179" y="527.0977301930383"/></g>
  <g id="82">
   <use class="v10500" height="26" transform="rotate(0,1018.34,745.735) scale(0.865889,0.865889) translate(156.917,113.757)" width="12" x="1013.141278961618" xlink:href="#Accessory:避雷器1_0" y="734.4782256157102" zvalue="1574"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454487048195" ObjectName="#1发电机避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1018.34,745.735) scale(0.865889,0.865889) translate(156.917,113.757)" width="12" x="1013.141278961618" y="734.4782256157102"/></g>
  <g id="192">
   <use class="kv10" height="29" transform="rotate(0,1284.3,934.937) scale(1.5586,-1.5586) translate(-451.914,-1526.69)" width="30" x="1260.925643103131" xlink:href="#Accessory:厂用变2020_0" y="912.3367985780937" zvalue="1579"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454487113731" ObjectName="#3厂用变"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1284.3,934.937) scale(1.5586,-1.5586) translate(-451.914,-1526.69)" width="30" x="1260.925643103131" y="912.3367985780937"/></g>
  <g id="196">
   <use class="kv10" height="13" transform="rotate(0,1284.3,883.33) scale(0.865889,-0.865889) translate(198.178,-1904.34)" width="11" x="1279.542263777817" xlink:href="#Accessory:空挂线路_0" y="877.7012252298398" zvalue="1580"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454487179267" ObjectName="1"/>
   </metadata>
  <rect fill="white" height="13" opacity="0" stroke="white" transform="rotate(0,1284.3,883.33) scale(0.865889,-0.865889) translate(198.178,-1904.34)" width="11" x="1279.542263777817" y="877.7012252298398"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="73">
   <g id="730">
    <use class="kv110" height="50" transform="rotate(0,1599.56,526.735) scale(1.71735,1.69994) translate(-657.389,-199.381)" width="30" x="1573.8" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="484.24" zvalue="1097"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874583441410" ObjectName="110"/>
    </metadata>
   </g>
   <g id="731">
    <use class="v10500" height="50" transform="rotate(0,1599.56,526.735) scale(1.71735,1.69994) translate(-657.389,-199.381)" width="30" x="1573.8" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="484.24" zvalue="1097"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874583506946" ObjectName="10.5"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399529725954" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399529725954"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1599.56,526.735) scale(1.71735,1.69994) translate(-657.389,-199.381)" width="30" x="1573.8" y="484.24"/></g>
  <g id="163">
   <g id="1630">
    <use class="kv110" height="50" transform="rotate(0,1025.61,527.601) scale(1.71735,1.69994) translate(-417.644,-199.737)" width="30" x="999.85" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="485.1" zvalue="1522"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874583572482" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1631">
    <use class="v10500" height="50" transform="rotate(0,1025.61,527.601) scale(1.71735,1.69994) translate(-417.644,-199.737)" width="30" x="999.85" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="485.1" zvalue="1522"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874583638018" ObjectName="10.5"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399529791490" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399529791490"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1025.61,527.601) scale(1.71735,1.69994) translate(-417.644,-199.737)" width="30" x="999.85" y="485.1"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v10500" height="30" transform="rotate(0,1044.31,850.507) scale(1.29883,1.29883) translate(-235.792,-191.201)" width="30" x="1024.830784966653" xlink:href="#Generator:发电机_0" y="831.0248863239683" zvalue="1260"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454484099075" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192454484099075"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1044.31,850.507) scale(1.29883,1.29883) translate(-235.792,-191.201)" width="30" x="1024.830784966653" y="831.0248863239683"/></g>
  <g id="161">
   <use class="v10500" height="30" transform="rotate(0,1621,850.507) scale(1.29883,1.29883) translate(-368.474,-191.201)" width="30" x="1601.513081215085" xlink:href="#Generator:发电机_0" y="831.0248863239683" zvalue="1525"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454486523907" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192454486523907"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1621,850.507) scale(1.29883,1.29883) translate(-368.474,-191.201)" width="30" x="1601.513081215085" y="831.0248863239683"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="263">
   <use class="kv110" height="30" transform="rotate(0,1471.14,170.111) scale(3.207,1.2828) translate(-999.168,-33.2597)" width="12" x="1451.895905074592" xlink:href="#EnergyConsumer:负荷_0" y="150.868814697627" zvalue="1441"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454484819971" ObjectName="110kV备用线"/>
   <cge:TPSR_Ref TObjectID="6192454484819971"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1471.14,170.111) scale(3.207,1.2828) translate(-999.168,-33.2597)" width="12" x="1451.895905074592" y="150.868814697627"/></g>
  <g id="95">
   <use class="v10500" height="30" transform="rotate(0,741.977,840.766) scale(1.5292,2.18192) translate(-249.362,-437.704)" width="28" x="720.5686551349361" xlink:href="#EnergyConsumer:站用变YD2022_0" y="808.0373294859526" zvalue="1465"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454485344259" ObjectName="坝区变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,741.977,840.766) scale(1.5292,2.18192) translate(-249.362,-437.704)" width="28" x="720.5686551349361" y="808.0373294859526"/></g>
  <g id="228">
   <use class="v10500" height="30" transform="rotate(0,878.788,840.9) scale(1.5292,2.18192) translate(-296.707,-437.777)" width="28" x="857.3791698605401" xlink:href="#EnergyConsumer:站用变YD2022_0" y="808.1714401522465" zvalue="1478"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454485409795" ObjectName="#1厂用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,878.788,840.9) scale(1.5292,2.18192) translate(-296.707,-437.777)" width="28" x="857.3791698605401" y="808.1714401522465"/></g>
  <g id="127">
   <use class="v10500" height="30" transform="rotate(0,1455.47,846.271) scale(1.5292,2.18192) translate(-496.275,-440.686)" width="28" x="1434.061466108972" xlink:href="#EnergyConsumer:站用变YD2022_0" y="813.5423763155754" zvalue="1558"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454485934083" ObjectName="#2厂用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1455.47,846.271) scale(1.5292,2.18192) translate(-496.275,-440.686)" width="28" x="1434.061466108972" y="813.5423763155754"/></g>
 </g>
 <g id="StateClass">
  <g id="203">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="1616"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374926282753" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="215">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="1617"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562960520052739" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
  <g id="283">
   <use height="30" transform="rotate(0,324.812,126.464) scale(1.27778,1.03333) translate(-58.1114,-3.57948)" width="90" x="267.31" xlink:href="#State:全站检修_0" y="110.96" zvalue="1702"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549682503681" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,324.812,126.464) scale(1.27778,1.03333) translate(-58.1114,-3.57948)" width="90" x="267.31" y="110.96"/></g>
 </g>
</svg>