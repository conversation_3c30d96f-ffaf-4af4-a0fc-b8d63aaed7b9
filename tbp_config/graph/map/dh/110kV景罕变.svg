<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549682241537" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:母联开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill="rgb(170,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.333333333333333" x2="9.75" y1="0.4166666666666696" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.666666666666668" x2="0.4166666666666661" y1="0.4999999999999982" y2="19.66666666666667"/>
   <rect fill-opacity="0" height="19.58" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.04,10.04) scale(1,1) translate(0,0)" width="9.58" x="0.25" y="0.25"/>
  </symbol>
  <symbol id="PowerTransformer3:可调三卷变YDY11_0" viewBox="0,0,50,50">
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.33333333333333" x2="15.15740740740741" y1="8.583333333333332" y2="12.1754686785551"/>
   <use terminal-index="0" type="1" x="15.08687700045725" xlink:href="#terminal" y="0.1329446730681312"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.16666666666666" x2="15.16666666666666" y1="12.16666666666666" y2="16.91666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.08333333333333" x2="15.17203932327389" y1="8.666666666666668" y2="12.1754686785551"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.91666666666666" x2="0.5833333333333286" y1="2.249999999999989" y2="25"/>
   <path d="M 28.6667 1 L 25.1667 2.25 L 26.6667 3.83333 z" fill="rgb(255,0,0)" fill-opacity="1" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="14.93" cy="15" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="2" x="15.1368541380887" xlink:href="#terminal" y="12.15974508459076"/>
  </symbol>
  <symbol id="PowerTransformer3:可调三卷变YDY11_1" viewBox="0,0,50,50">
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.75663008687701" x2="40.33333333333334" y1="24.88934613625973" y2="29.41666666666668"/>
   <use terminal-index="3" type="1" x="49.9" xlink:href="#terminal" y="25"/>
   <use terminal-index="2" type="2" x="35.75" xlink:href="#terminal" y="25"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.74931412894377" x2="30.83333333333334" y1="24.88203017832648" y2="29.33333333333333"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.75" x2="35.75" y1="20.58333333333334" y2="24.88203017832647"/>
   <ellipse cx="35.12" cy="25.58" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer3:可调三卷变YDY11_2" viewBox="0,0,50,50">
   <use terminal-index="4" type="1" x="15.08687700045725" xlink:href="#terminal" y="49.95"/>
   <path d="M 12.4167 29.5 L 12.4167 41.5 L 20.9167 35.1667 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.87" cy="35.17" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT7_0" viewBox="0,0,14,18">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1.416666666666664"/>
   <ellipse cx="7.15" cy="6.35" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.15" cy="12.03" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_0" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="3" y1="19" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="4.75" y2="8.75"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="18.75" y2="21.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_1" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="2" y2="24"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_2" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.08333333333333" x2="3.995614035087721" y1="9.083333333333332" y2="17.19627192982456"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.717927631578948" x2="10.16666666666667" y1="9.172423245614038" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="2" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="21.25" y2="24.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="GroundDisconnector:接地刀_0" viewBox="0,0,20,25">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="5.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="13" y1="14.33333333333333" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.927164662603633" x2="5.927164662603633" y1="21.23783949080951" y2="18.88156647584608"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.627658815132046" x2="9.601962883654533" y1="21.31657844392075" y2="21.31657844392075"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.411384725196287" x2="8.342862241256245" y1="23.11600124906283" y2="23.11600124906283"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.281031073506858" x2="7.626181375820934" y1="24.83429284309519" y2="24.83429284309519"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.6833333333333336" x2="5.927164662603635" y1="9.222919382464234" y2="18.88156647584608"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.83108493932905" x2="8.028087080656674" y1="9.101131074690123" y2="9.101131074690123"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.97259166003632" x2="5.97259166003632" y1="9.059451081670424" y2="6.027110967746054"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.57716466260364" x2="15.57716466260364" y1="21.12739518973012" y2="18.77112217476669"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.27765881513205" x2="19.25196288365454" y1="21.20613414284136" y2="21.20613414284136"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.06138472519629" x2="17.99286224125624" y1="23.00555694798344" y2="23.00555694798344"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.93103107350686" x2="17.27618137582093" y1="24.7238485420158" y2="24.7238485420158"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.33333333333333" x2="15.57716466260364" y1="9.112475081384845" y2="18.77112217476669"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.62259166003632" x2="15.62259166003632" y1="8.949006780591038" y2="0.08333333333333393"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.48108493932905" x2="17.67808708065667" y1="8.990686773610733" y2="8.990686773610733"/>
  </symbol>
  <symbol id="GroundDisconnector:接地刀_1" viewBox="0,0,20,25">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="5.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.916666666666667" x2="15.41666666666667" y1="14.33333333333333" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.927164662603633" x2="5.927164662603633" y1="21.23783949080951" y2="9.083333333333336"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.627658815132046" x2="9.601962883654533" y1="21.31657844392075" y2="21.31657844392075"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.411384725196287" x2="8.342862241256245" y1="23.11600124906283" y2="23.11600124906283"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.281031073506858" x2="7.626181375820934" y1="24.83429284309519" y2="24.83429284309519"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.83108493932905" x2="8.028087080656674" y1="9.101131074690123" y2="9.101131074690123"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.97259166003632" x2="5.97259166003632" y1="9.059451081670424" y2="6.027110967746054"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.57716466260364" x2="15.57716466260364" y1="21.12739518973012" y2="8.916666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.27765881513205" x2="19.25196288365454" y1="21.20613414284136" y2="21.20613414284136"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.06138472519629" x2="17.99286224125624" y1="23.00555694798344" y2="23.00555694798344"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.93103107350686" x2="17.27618137582093" y1="24.7238485420158" y2="24.7238485420158"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.62259166003632" x2="15.62259166003632" y1="8.949006780591038" y2="0.08333333333333393"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.48108493932905" x2="17.67808708065667" y1="8.990686773610733" y2="8.990686773610733"/>
  </symbol>
  <symbol id="GroundDisconnector:接地刀_2" viewBox="0,0,20,25">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="5.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.927164662603633" x2="5.927164662603633" y1="21.23783949080951" y2="18.88156647584608"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.627658815132046" x2="9.601962883654533" y1="21.31657844392075" y2="21.31657844392075"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.411384725196287" x2="8.342862241256245" y1="23.11600124906283" y2="23.11600124906283"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.281031073506858" x2="7.626181375820934" y1="24.83429284309519" y2="24.83429284309519"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.510497995936969" x2="3.266666666666667" y1="9.306252715797566" y2="18.96489980917941"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.83108493932905" x2="8.028087080656674" y1="9.101131074690123" y2="9.101131074690123"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.97259166003632" x2="5.97259166003632" y1="9.059451081670424" y2="6.027110967746054"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.57716466260364" x2="15.57716466260364" y1="21.12739518973012" y2="18.77112217476669"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.27765881513205" x2="19.25196288365454" y1="21.20613414284136" y2="21.20613414284136"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.06138472519629" x2="17.99286224125624" y1="23.00555694798344" y2="23.00555694798344"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.93103107350686" x2="17.27618137582093" y1="24.7238485420158" y2="24.7238485420158"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="8.660497995936966" y1="9.279141748051511" y2="18.93778884143336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.62259166003632" x2="15.62259166003632" y1="8.949006780591038" y2="0.08333333333333393"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.48108493932905" x2="17.67808708065667" y1="8.990686773610733" y2="8.990686773610733"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.0938313292703" x2="12.85" y1="9.056252715797566" y2="18.71489980917941"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13" x2="18.2438313292703" y1="9.029141748051511" y2="18.68778884143336"/>
  </symbol>
  <symbol id="Compensator:电容20200722_0" viewBox="0,0,25,50">
   <use terminal-index="0" type="0" x="15.16666666666667" xlink:href="#terminal" y="0.4166666666666643"/>
   <path d="M 15.3333 45.75 L 15.3333 48.75 L 1.33333 48.75 L 1.33333 0.0833333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25" x2="22.25" y1="37.91666666666667" y2="35.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25" x2="23.25" y1="32.25" y2="29.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.75" x2="23.75" y1="37.91666666666667" y2="37.91666666666667"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,22.25,31.42) scale(1,1) translate(0,0)" width="4" x="20.25" y="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.75" x2="22.75" y1="39.91666666666667" y2="39.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25" x2="21.25" y1="32.25" y2="29.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.25" x2="23.25" y1="38.91666666666667" y2="38.91666666666667"/>
   <rect fill-opacity="0" height="4" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.33,32.33) scale(1,1) translate(0,0)" width="2" x="14.33" y="30.33"/>
   <path d="M 15.25 23.75 L 22.25 23.75 L 22.25 32.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.35833333333333" x2="15.35833333333333" y1="40.10833333333333" y2="43.62685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.321296296296296" x2="9.321296296296296" y1="41.38611111111112" y2="43.62685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.10833333333333" x2="7.290905947441217" y1="46.02500000000001" y2="46.02500000000001"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25" x2="9.333333333333332" y1="43.60833333333333" y2="43.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.35833333333333" x2="15.35833333333333" y1="43.69166666666667" y2="46.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.374239280774555" x2="7.374239280774555" y1="46.10833333333333" y2="44.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.19166666666666" x2="23.19166666666666" y1="44.66666666666667" y2="46.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.358333333333334" x2="9.358333333333334" y1="30.60833333333334" y2="28.40462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.35833333333333" x2="15.35833333333333" y1="21.5" y2="39.10833333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333332" x2="15.35833333333334" y1="28.35833333333333" y2="28.35833333333333"/>
   <path d="M 9.35833 34.2072 A 2.96392 1.81747 -180 0 1 9.35833 30.5723" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 9.35833 37.8421 A 2.96392 1.81747 -180 0 1 9.35833 34.2072" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 9.35833 41.3771 A 2.96392 1.81747 -180 0 1 9.35833 37.7421" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.85833333333333" x2="13.85833333333334" y1="40.10833333333333" y2="40.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.85833333333333" x2="13.85833333333334" y1="39.10833333333333" y2="39.10833333333333"/>
   <path d="M 22.5 14.7417 A 6.84167 7.10597 -270 1 0 15.394 21.5833" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 22.4547 14.749 L 15.3041 14.749 L 15.3041 0.416667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.998992968246015" x2="4.998992968246015" y1="14.41666666666666" y2="17.66666666666666"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="1.08333333333333" y2="5.749999999999999"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="6" y2="14"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="14" y2="6"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:4绕组PT带接地_0" viewBox="0,0,30,35">
   <use terminal-index="0" type="0" x="14.94238998217831" xlink:href="#terminal" y="31.09945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="13" y1="18.19406992327969" y2="17.19406992327969"/>
   <rect fill-opacity="0" height="14.44" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,4.46,16.81) scale(1,-1) translate(0,-1028.21)" width="5.58" x="1.67" y="9.58"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="13" y1="14.19406992327969" y2="15.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.583333333333325" x2="4.583333333333325" y1="30.69406992327969" y2="16.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.19406992327969" y2="14.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.583333333333325" x2="3.583333333333325" y1="15.69406992327969" y2="20.69406992327969"/>
   <path d="M 20.6667 26.75 L 20.6667 30.75 L 4.66667 30.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.583333333333325" x2="5.583333333333325" y1="15.69406992327969" y2="20.69406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5" x2="26.5" y1="16.5" y2="16.5"/>
   <path d="M 20.75 22.8333 L 26.5833 22.8333 L 26.5833 6.83333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.59610027172149" x2="4.59610027172149" y1="9.527403256613018" y2="6.379186935280762"/>
   <ellipse cx="20.73" cy="22.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.625884107343747" x2="2.349068262659902" y1="6.31127839976627" y2="6.31127839976627"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.792550774010413" x2="3.182401595993234" y1="5.06127839976627" y2="5.06127839976627"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.209217440677081" x2="3.765734929326568" y1="3.81127839976627" y2="3.81127839976627"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="23.09906826265991" y1="22.70662962336982" y2="23.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="18.29906826265991" y1="22.70662962336982" y2="23.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="20.69906826265991" y1="20.23154965466559" y2="22.7066296233698"/>
   <ellipse cx="14.48" cy="22.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.73" cy="16.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.44906826265991" x2="16.84906826265991" y1="22.70662962336982" y2="23.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.44906826265991" x2="14.44906826265991" y1="20.23154965466559" y2="22.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="18.29906826265991" y1="16.45662962336982" y2="17.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="23.09906826265991" y1="16.45662962336982" y2="17.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="20.69906826265991" y1="13.9815496546656" y2="16.4566296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.44906826265991" x2="12.04906826265991" y1="22.70662962336982" y2="23.94416960772192"/>
   <ellipse cx="14.48" cy="16.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="28.79255077401042" x2="24.51573492932657" y1="6.727945066432934" y2="6.727945066432934"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.95921744067708" x2="25.34906826265991" y1="5.477945066432934" y2="5.477945066432934"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.37588410734375" x2="25.93240159599324" y1="4.227945066432941" y2="4.227945066432941"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2027.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="ACLineSegment:单相PT线路_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="14.95833333333333" xlink:href="#terminal" y="0.9221418778962942"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="6.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="5.75" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.5" x2="25.5" y1="28.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="8.25" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.25" x2="6.25" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="25" y1="20" y2="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.25" x2="7.25" y1="32.25" y2="32.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="4.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.91022336769755" x2="14.91022336769755" y1="38.83333333333334" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.33333333333334" x2="14.98769568537606" y1="9.258752830905545" y2="9.258752830905545"/>
   <path d="M 14.75 9.25 L 5.75 9.25 L 5.75 21.25 L 5.75 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.25545055364641" x2="22.25545055364641" y1="9.291388644475965" y2="15.41666666666666"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.79,21.17) scale(1,1) translate(0,0)" width="6.08" x="2.75" y="14"/>
   <ellipse cx="22.26" cy="20.07" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="22.51" cy="28.33" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.32589728904158" x2="17.32589728904158" y1="9.247588122517026" y2="9.247588122517026"/>
  </symbol>
  <symbol id="ACLineSegment:线路带壁雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="14.875" xlink:href="#terminal" y="39.83880854456296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.25" x2="6.25" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="5.75" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.25" x2="7.25" y1="32.25" y2="32.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="8.25" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="6.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="4.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.91022336769755" x2="14.91022336769755" y1="39.75" y2="0.8333333333333321"/>
   <path d="M 14.75 9.25 L 5.75 9.25 L 5.75 21.25 L 5.75 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.79,21.17) scale(1,1) translate(0,0)" width="6.08" x="2.75" y="14"/>
  </symbol>
  <symbol id="Accessory:5卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2027.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
   <ellipse cx="8.710000000000001" cy="0.47" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.10887049225937" x2="11.10887049225937" y1="1.654102266954478" y2="1.654102266954478"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.60887049225937" x2="11.60887049225937" y1="1.154102266954478" y2="1.154102266954478"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.289699242498646" x2="7.787306517404822" y1="0.309450744955857" y2="-2.343680756240978"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.289699242498656" x2="10.74248159896798" y1="0.3094507449558748" y2="1.438770861071294"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.339309611123124" x2="8.289699242498626" y1="1.833262130037291" y2="0.3094507449558748"/>
  </symbol>
  <symbol id="Accessory:接地变接地设备_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.75"/>
   <rect fill-opacity="0" height="13.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.94,7.54) scale(1,1) translate(0,0)" width="6.08" x="2.9" y="0.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.2499999999999956" x2="11.58333333333334" y1="20.49453511141348" y2="20.49453511141348"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.833333333333332" x2="10" y1="22.90451817731685" y2="22.90451817731685"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6" x2="6" y1="20.66666666666667" y2="14.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.934588934424228" x2="7.898744398909104" y1="25.38116790988687" y2="25.38116790988687"/>
  </symbol>
  <symbol id=":线路带避雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15.16666666666667" xlink:href="#terminal" y="39.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.16022336769755" x2="15.16022336769755" y1="39.75" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="6.5" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="8.5" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="7.5" y1="32.25" y2="32.25"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6.04,21.17) scale(1,1) translate(0,0)" width="6.08" x="3" y="14"/>
   <path d="M 15 9.25 L 6 9.25 L 6 21.25 L 6 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="7" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="5" y1="22" y2="16"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id="State:间隔模板_0" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(85,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="State:间隔模板_1" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(185,185,185)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变13_0" viewBox="0,0,32,35">
   <use terminal-index="0" type="0" x="16" xlink:href="#terminal" y="3.5"/>
   <ellipse cx="16.04" cy="11.97" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.97" cy="24.14" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="20.85" y2="24.95894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="10.66666666666667" y1="24.98394833233988" y2="28.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="21.16666666666667" y1="24.93990500916851" y2="28.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="7.6" y2="11.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="12.05" y1="11.73394833233987" y2="14.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="20.15" y1="11.68990500916851" y2="14.5"/>
  </symbol>
  <symbol id=":单相PT带避雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="39"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.25" x2="26.25" y1="14.08333333333333" y2="14.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.75" x2="25.75" y1="22.58333333333333" y2="22.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.16022336769755" x2="15.16022336769755" y1="0.9999999999999929" y2="39"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.08333333333334" x2="15.73769568537606" y1="33.32458050242779" y2="33.32458050242779"/>
   <path d="M 15.5 33.3333 L 6.5 33.3333 L 6.5 21.3333 L 6.5 21.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.00545055364642" x2="23.00545055364642" y1="33.29194468885736" y2="27.16666666666666"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6.54,21.42) scale(1,-1) translate(0,-1112.67)" width="6.08" x="3.5" y="14.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="7" y1="9.33333333333333" y2="9.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="9" y1="11.33333333333333" y2="11.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="8" y1="10.33333333333333" y2="10.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.5" x2="5.5" y1="20.58333333333333" y2="26.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.5" x2="6.5" y1="14.33333333333333" y2="11.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.5" x2="7.5" y1="20.58333333333333" y2="26.58333333333333"/>
   <ellipse cx="23.01" cy="22.51" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="23.26" cy="14.25" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.07589728904158" x2="18.07589728904158" y1="33.33574521081631" y2="33.33574521081631"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV景罕变" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="36.31" xlink:href="logo.png" y="33.76"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="215" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,161.153,75.0469) scale(1,1) translate(-8.06171e-15,0)" writing-mode="lr" x="161.15" xml:space="preserve" y="78.55" zvalue="10200"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,190,75.0237) scale(1,1) translate(0,0)" writing-mode="lr" x="190" xml:space="preserve" y="84.02" zvalue="10201">110kV景罕变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="604" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,162.375,404.25) scale(1,1) translate(0,0)" width="72.88" x="125.94" y="392.25" zvalue="10760"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,162.375,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="162.38" xml:space="preserve" y="408.75" zvalue="10760">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="464" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,62.625,404.25) scale(1,1) translate(0,0)" width="72.88" x="26.19" y="392.25" zvalue="10761"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.625,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.63" xml:space="preserve" y="408.75" zvalue="10761">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="141" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,61.625,368.75) scale(1,1) translate(0,0)" width="72.88" x="25.19" y="356.75" zvalue="10762"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,61.625,368.75) scale(1,1) translate(0,0)" writing-mode="lr" x="61.63" xml:space="preserve" y="373.25" zvalue="10762">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1065.63,70.7289) scale(1,1) translate(0,0)" writing-mode="lr" x="1065.63" xml:space="preserve" y="75.23" zvalue="7516">110kV凤景线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1043.77,239.978) scale(1,1) translate(0,0)" writing-mode="lr" x="1043.77" xml:space="preserve" y="244.48" zvalue="7517">172</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1047.26,179.088) scale(1,1) translate(0,0)" writing-mode="lr" x="1047.26" xml:space="preserve" y="183.59" zvalue="7519">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1097.86,156.649) scale(1,1) translate(0,0)" writing-mode="lr" x="1097.86" xml:space="preserve" y="161.15" zvalue="7521">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1047.2,308.883) scale(1,1) translate(0,0)" writing-mode="lr" x="1047.2" xml:space="preserve" y="313.38" zvalue="7533">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1097.86,219.895) scale(1,1) translate(0,0)" writing-mode="lr" x="1097.86" xml:space="preserve" y="224.39" zvalue="7569">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1097.86,291.935) scale(1,1) translate(0,0)" writing-mode="lr" x="1097.86" xml:space="preserve" y="296.43" zvalue="7573">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,978.758,357.746) scale(1,1) translate(0,0)" writing-mode="lr" x="978.76" xml:space="preserve" y="362.25" zvalue="7580">110kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1182.33,618.753) scale(1,1) translate(0,0)" writing-mode="lr" x="1182.33" xml:space="preserve" y="623.25" zvalue="7587">1010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1517.81,679.146) scale(1,1) translate(0,0)" writing-mode="lr" x="1517.81" xml:space="preserve" y="683.65" zvalue="7719">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1212.6,406.918) scale(1,1) translate(1.60152e-12,3.53866e-13)" writing-mode="lr" x="1212.6" xml:space="preserve" y="411.42" zvalue="7806">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1208.99,449.808) scale(1,1) translate(0,0)" writing-mode="lr" x="1208.99" xml:space="preserve" y="454.31" zvalue="7808">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="250" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1211.17,361.013) scale(1,1) translate(0,0)" writing-mode="lr" x="1211.17" xml:space="preserve" y="365.51" zvalue="7813">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="248" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1259.41,443.003) scale(1,1) translate(0,0)" writing-mode="lr" x="1259.41" xml:space="preserve" y="447.5" zvalue="7819">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1259.41,394.247) scale(1,1) translate(0,0)" writing-mode="lr" x="1259.41" xml:space="preserve" y="398.75" zvalue="7821">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="403" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,507.333,576.039) scale(1,1) translate(0,0)" writing-mode="lr" x="507.33" xml:space="preserve" y="580.54" zvalue="7956">1020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1309.35,284.081) scale(1,1) translate(0,0)" writing-mode="lr" x="1309.35" xml:space="preserve" y="288.58" zvalue="8019">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1357.98,323.204) scale(1,1) translate(0,0)" writing-mode="lr" x="1357.98" xml:space="preserve" y="327.7" zvalue="8033">19010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1357.98,271.204) scale(1,1) translate(0,0)" writing-mode="lr" x="1357.98" xml:space="preserve" y="275.7" zvalue="8038">19017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="491" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1684.86,100.007) scale(1,1) translate(0,0)" writing-mode="lr" x="1684.86" xml:space="preserve" y="104.51" zvalue="8107">35kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="492" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1684.54,760.801) scale(1,1) translate(0,0)" writing-mode="lr" x="1684.54" xml:space="preserve" y="765.3" zvalue="8109">35kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="494" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1638.65,128.645) scale(1,1) translate(0,0)" writing-mode="lr" x="1638.65" xml:space="preserve" y="133.14" zvalue="8111">301</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,862.619,351.781) scale(1,1) translate(1.8399e-13,0)" writing-mode="lr" x="862.62" xml:space="preserve" y="356.28" zvalue="8776">110kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,663.374,284.081) scale(1,1) translate(0,0)" writing-mode="lr" x="663.37" xml:space="preserve" y="288.58" zvalue="8860">1902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,711.69,323.204) scale(1,1) translate(0,0)" writing-mode="lr" x="711.6900000000001" xml:space="preserve" y="327.7" zvalue="8862">19020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,711.69,272.454) scale(1,1) translate(0,-4.68877e-13)" writing-mode="lr" x="711.6900000000001" xml:space="preserve" y="276.95" zvalue="8866">19027</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="271" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1391.2,96.4971) scale(1,1) translate(0,0)" writing-mode="lr" x="1391.2" xml:space="preserve" y="101" zvalue="8911">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="488" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,566.317,406.918) scale(1,1) translate(0,0)" writing-mode="lr" x="566.3200000000001" xml:space="preserve" y="411.42" zvalue="9169">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="487" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,562.707,449.808) scale(1,1) translate(0,0)" writing-mode="lr" x="562.71" xml:space="preserve" y="454.31" zvalue="9171">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="486" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,564.701,361.013) scale(1,1) translate(0,0)" writing-mode="lr" x="564.7" xml:space="preserve" y="365.51" zvalue="9174">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="485" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,617.121,441.247) scale(1,1) translate(0,0)" writing-mode="lr" x="617.12" xml:space="preserve" y="445.75" zvalue="9176">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="484" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,617.121,394.247) scale(1,1) translate(0,0)" writing-mode="lr" x="617.12" xml:space="preserve" y="398.75" zvalue="9178">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="517" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,611.075,634.875) scale(1,1) translate(0,0)" writing-mode="lr" x="611.08" xml:space="preserve" y="639.38" zvalue="9191">002</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="738" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,476.223,674.103) scale(1,1) translate(0,0)" writing-mode="lr" x="476.22" xml:space="preserve" y="678.6" zvalue="9204">10kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="441" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1259.35,631.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1259.35" xml:space="preserve" y="636.38" zvalue="9226">001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1309.94,508.284) scale(1,1) translate(0,0)" writing-mode="lr" x="1309.94" xml:space="preserve" y="512.78" zvalue="9331">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,494.084,512.284) scale(1,1) translate(0,0)" writing-mode="lr" x="494.08" xml:space="preserve" y="516.78" zvalue="9333">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="458" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1417.49,829.808) scale(1,1) translate(0,0)" writing-mode="lr" x="1417.49" xml:space="preserve" y="834.3099999999999" zvalue="9391">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="444" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1374.12,823.997) scale(1,1) translate(0,0)" writing-mode="lr" x="1374.12" xml:space="preserve" y="828.5" zvalue="9393">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="442" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1370.46,734.451) scale(1,1) translate(0,0)" writing-mode="lr" x="1370.46" xml:space="preserve" y="738.95" zvalue="9397">033</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="553" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1397.82,933.958) scale(1,1) translate(0,0)" writing-mode="lr" x="1397.82" xml:space="preserve" y="938.46" zvalue="9425">10kV景罕镇线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="556" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,518.347,70.1456) scale(1,1) translate(0,0)" writing-mode="lr" x="518.35" xml:space="preserve" y="74.65000000000001" zvalue="9721">110kV瑞景线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="551" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,497.482,241.978) scale(1,1) translate(0,0)" writing-mode="lr" x="497.48" xml:space="preserve" y="246.48" zvalue="9722">173</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="550" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,500.975,179.088) scale(1,1) translate(0,0)" writing-mode="lr" x="500.97" xml:space="preserve" y="183.59" zvalue="9724">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="547" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,551.579,156.649) scale(1,1) translate(0,0)" writing-mode="lr" x="551.58" xml:space="preserve" y="161.15" zvalue="9726">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="539" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,501.127,308.883) scale(1,1) translate(0,0)" writing-mode="lr" x="501.13" xml:space="preserve" y="313.38" zvalue="9729">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="538" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,551.579,219.895) scale(1,1) translate(0,0)" writing-mode="lr" x="551.58" xml:space="preserve" y="224.39" zvalue="9732">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="536" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,551.579,291.935) scale(1,1) translate(0,0)" writing-mode="lr" x="551.58" xml:space="preserve" y="296.43" zvalue="9735">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="820" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1259.41,477.247) scale(1,1) translate(0,0)" writing-mode="lr" x="1259.41" xml:space="preserve" y="481.75" zvalue="9746">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="823" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,618.371,475.997) scale(1,1) translate(0,0)" writing-mode="lr" x="618.37" xml:space="preserve" y="480.5" zvalue="9749">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1218.04,824.43) scale(1,1) translate(0,0)" writing-mode="lr" x="1218.04" xml:space="preserve" y="828.9299999999999" zvalue="9763">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1215.46,734.451) scale(1,1) translate(0,0)" writing-mode="lr" x="1215.46" xml:space="preserve" y="738.95" zvalue="9764">031</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1242.12,910.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1242.12" xml:space="preserve" y="915.33" zvalue="9786">10kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1458.12,823.997) scale(1,1) translate(0,0)" writing-mode="lr" x="1458.12" xml:space="preserve" y="828.5" zvalue="9807">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1454.46,734.451) scale(1,1) translate(0,0)" writing-mode="lr" x="1454.46" xml:space="preserve" y="738.95" zvalue="9809">035</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1481.82,933.958) scale(1,1) translate(0,0)" writing-mode="lr" x="1481.82" xml:space="preserve" y="938.46" zvalue="9817">10kV弄冒线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1538.12,823.997) scale(1,1) translate(0,0)" writing-mode="lr" x="1538.12" xml:space="preserve" y="828.5" zvalue="9828">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1534.46,734.451) scale(1,1) translate(0,0)" writing-mode="lr" x="1534.46" xml:space="preserve" y="738.95" zvalue="9830">036</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1559.6,936.181) scale(1,1) translate(0,0)" writing-mode="lr" x="1559.6" xml:space="preserve" y="940.6799999999999" zvalue="9838">10kV芒面线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="841" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1611.86,824.43) scale(1,1) translate(0,0)" writing-mode="lr" x="1611.86" xml:space="preserve" y="828.9299999999999" zvalue="9847">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="840" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1610.46,734.451) scale(1,1) translate(0,0)" writing-mode="lr" x="1610.46" xml:space="preserve" y="738.95" zvalue="9849">038</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="885" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,927.201,837.808) scale(1,1) translate(0,0)" writing-mode="lr" x="927.2" xml:space="preserve" y="842.3099999999999" zvalue="9863">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="884" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,882.839,823.997) scale(1,1) translate(0,0)" writing-mode="lr" x="882.84" xml:space="preserve" y="828.5" zvalue="9865">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="882" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,883.671,734.451) scale(1,1) translate(0,0)" writing-mode="lr" x="883.67" xml:space="preserve" y="738.95" zvalue="9869">052</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="880" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,766.85,831.093) scale(1,1) translate(0,0)" writing-mode="lr" x="766.85" xml:space="preserve" y="835.59" zvalue="9883">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="879" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,730.411,822.568) scale(1,1) translate(0,0)" writing-mode="lr" x="730.41" xml:space="preserve" y="827.0700000000001" zvalue="9885">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="878" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,733.171,734.451) scale(1,1) translate(0,0)" writing-mode="lr" x="733.17" xml:space="preserve" y="738.95" zvalue="9887">054</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="873" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,957.572,828.43) scale(1,1) translate(0,0)" writing-mode="lr" x="957.5700000000001" xml:space="preserve" y="832.9299999999999" zvalue="9901">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="872" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,957.171,734.451) scale(1,1) translate(0,0)" writing-mode="lr" x="957.17" xml:space="preserve" y="738.95" zvalue="9903">051</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="871" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,983.833,910.833) scale(1,1) translate(0,0)" writing-mode="lr" x="983.83" xml:space="preserve" y="915.33" zvalue="9914">10kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="870" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,692.344,828.522) scale(1,1) translate(0,0)" writing-mode="lr" x="692.34" xml:space="preserve" y="833.02" zvalue="9924">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="869" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,659.839,823.997) scale(1,1) translate(0,0)" writing-mode="lr" x="659.84" xml:space="preserve" y="828.5" zvalue="9926">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="868" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,657.171,734.451) scale(1,1) translate(0,0)" writing-mode="lr" x="657.17" xml:space="preserve" y="738.95" zvalue="9928">055</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="867" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,679.537,929.958) scale(1,1) translate(0,0)" writing-mode="lr" x="679.54" xml:space="preserve" y="934.46" zvalue="9936">10kV弄贯线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="859" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,425.572,824.43) scale(1,1) translate(0,0)" writing-mode="lr" x="425.57" xml:space="preserve" y="828.9299999999999" zvalue="9964">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="858" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,421.171,734.451) scale(1,1) translate(0,0)" writing-mode="lr" x="421.17" xml:space="preserve" y="738.95" zvalue="9966">058</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1726.48,150.311) scale(1,1) translate(0,0)" writing-mode="lr" x="1726.48" xml:space="preserve" y="154.81" zvalue="9995">372</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1794.09,195.135) scale(1,1) translate(0,0)" writing-mode="lr" x="1794.09" xml:space="preserve" y="199.64" zvalue="10004">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1811.92,150.913) scale(1,1) translate(0,0)" writing-mode="lr" x="1811.92" xml:space="preserve" y="155.41" zvalue="10006">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1862.08,127.763) scale(1,1) translate(0,0)" writing-mode="lr" x="1862.08" xml:space="preserve" y="132.26" zvalue="10007">35kV景勐线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1726.48,274.311) scale(1,1) translate(0,4.42665e-13)" writing-mode="lr" x="1726.48" xml:space="preserve" y="278.81" zvalue="10019">371</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1793.09,318.135) scale(1,1) translate(0,0)" writing-mode="lr" x="1793.09" xml:space="preserve" y="322.64" zvalue="10026">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1811.92,274.913) scale(1,1) translate(0,-4.73245e-13)" writing-mode="lr" x="1811.92" xml:space="preserve" y="279.41" zvalue="10028">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1862.08,255.056) scale(1,1) translate(0,0)" writing-mode="lr" x="1862.08" xml:space="preserve" y="259.56" zvalue="10030">35kV景陇线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="220" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1638.65,651.66) scale(1,1) translate(0,0)" writing-mode="lr" x="1638.65" xml:space="preserve" y="656.16" zvalue="10056">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="217" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1554.45,620.747) scale(1,1) translate(0,0)" writing-mode="lr" x="1554.45" xml:space="preserve" y="625.25" zvalue="10058">30267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="274" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1626.2,328.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1626.2" xml:space="preserve" y="333.33" zvalue="10078">3121</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="281" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1640.79,386.286) scale(1,1) translate(0,0)" writing-mode="lr" x="1640.79" xml:space="preserve" y="390.79" zvalue="10081">35kV分段</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1726.48,426.311) scale(1,1) translate(0,0)" writing-mode="lr" x="1726.48" xml:space="preserve" y="430.81" zvalue="10085">381</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1793.09,469.135) scale(1,1) translate(0,0)" writing-mode="lr" x="1793.09" xml:space="preserve" y="473.64" zvalue="10092">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1813.03,428.024) scale(1,1) translate(0,0)" writing-mode="lr" x="1813.03" xml:space="preserve" y="432.52" zvalue="10094">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1861.94,413.131) scale(1,1) translate(0,0)" writing-mode="lr" x="1861.94" xml:space="preserve" y="417.63" zvalue="10096">35kV景安线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="361" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1726.48,550.311) scale(1,1) translate(0,0)" writing-mode="lr" x="1726.48" xml:space="preserve" y="554.8099999999999" zvalue="10108">382</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="359" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1792.09,594.135) scale(1,1) translate(0,0)" writing-mode="lr" x="1792.09" xml:space="preserve" y="598.64" zvalue="10115">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="356" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1813.03,552.024) scale(1,1) translate(0,0)" writing-mode="lr" x="1813.03" xml:space="preserve" y="556.52" zvalue="10117">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="352" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1863.19,528.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1863.19" xml:space="preserve" y="532.67" zvalue="10119">35kV景城线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="463" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1726.48,694.311) scale(1,1) translate(0,0)" writing-mode="lr" x="1726.48" xml:space="preserve" y="698.8099999999999" zvalue="10131">383</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="460" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1790.09,738.135) scale(1,1) translate(0,0)" writing-mode="lr" x="1790.09" xml:space="preserve" y="742.64" zvalue="10138">27</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="454" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1862.69,671.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1862.69" xml:space="preserve" y="676.33" zvalue="10142">35kV章景线</text>
  <line fill="none" id="212" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.000000000000341" x2="376.9999999999999" y1="152.2038259474158" y2="152.2038259474158" zvalue="10203"/>
  <line fill="none" id="211" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="378.0000000000001" x2="378.0000000000001" y1="10.33333333333326" y2="1040.333333333333" zvalue="10204"/>
  <line fill="none" id="193" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.25000000000034" x2="378.2499999999999" y1="611.2038259474158" y2="611.2038259474158" zvalue="10205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="60.85709999999995" y1="430.5833333333333" y2="430.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="60.85709999999995" y1="463.2500333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="430.5833333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85709999999995" x2="60.85709999999995" y1="430.5833333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85699999999997" x2="112.7141" y1="430.5833333333333" y2="430.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85699999999997" x2="112.7141" y1="463.2500333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85699999999997" x2="60.85699999999997" y1="430.5833333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7141" x2="112.7141" y1="430.5833333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7143" x2="164.5714" y1="430.5833333333333" y2="430.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7143" x2="164.5714" y1="463.2500333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7143" x2="112.7143" y1="430.5833333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="164.5714" y1="430.5833333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="216.4285" y1="430.5833333333333" y2="430.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="216.4285" y1="463.2500333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="164.5714" y1="430.5833333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4285" x2="216.4285" y1="430.5833333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4286" x2="268.2857" y1="430.5833333333333" y2="430.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4286" x2="268.2857" y1="463.2500333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4286" x2="216.4286" y1="430.5833333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="268.2857" y1="430.5833333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="320.1428000000001" y1="430.5833333333333" y2="430.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="320.1428000000001" y1="463.2500333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="268.2857" y1="430.5833333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.1428000000001" x2="320.1428000000001" y1="430.5833333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.143" x2="372.0001" y1="430.5833333333333" y2="430.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.143" x2="372.0001" y1="463.2500333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.143" x2="320.143" y1="430.5833333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.0001" x2="372.0001" y1="430.5833333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="60.85709999999995" y1="463.2500333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="60.85709999999995" y1="489.9167333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="463.2500333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85709999999995" x2="60.85709999999995" y1="463.2500333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85699999999997" x2="112.7141" y1="463.2500333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85699999999997" x2="112.7141" y1="489.9167333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85699999999997" x2="60.85699999999997" y1="463.2500333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7141" x2="112.7141" y1="463.2500333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7143" x2="164.5714" y1="463.2500333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7143" x2="164.5714" y1="489.9167333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7143" x2="112.7143" y1="463.2500333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="164.5714" y1="463.2500333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="216.4285" y1="463.2500333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="216.4285" y1="489.9167333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="164.5714" y1="463.2500333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4285" x2="216.4285" y1="463.2500333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4286" x2="268.2857" y1="463.2500333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4286" x2="268.2857" y1="489.9167333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4286" x2="216.4286" y1="463.2500333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="268.2857" y1="463.2500333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="320.1428000000001" y1="463.2500333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="320.1428000000001" y1="489.9167333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="268.2857" y1="463.2500333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.1428000000001" x2="320.1428000000001" y1="463.2500333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.143" x2="372.0001" y1="463.2500333333333" y2="463.2500333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.143" x2="372.0001" y1="489.9167333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.143" x2="320.143" y1="463.2500333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.0001" x2="372.0001" y1="463.2500333333333" y2="489.9167333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="60.85709999999995" y1="489.9166333333333" y2="489.9166333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="60.85709999999995" y1="516.5833333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="489.9166333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85709999999995" x2="60.85709999999995" y1="489.9166333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85699999999997" x2="112.7141" y1="489.9166333333333" y2="489.9166333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85699999999997" x2="112.7141" y1="516.5833333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85699999999997" x2="60.85699999999997" y1="489.9166333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7141" x2="112.7141" y1="489.9166333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7143" x2="164.5714" y1="489.9166333333333" y2="489.9166333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7143" x2="164.5714" y1="516.5833333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7143" x2="112.7143" y1="489.9166333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="164.5714" y1="489.9166333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="216.4285" y1="489.9166333333333" y2="489.9166333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="216.4285" y1="516.5833333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="164.5714" y1="489.9166333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4285" x2="216.4285" y1="489.9166333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4286" x2="268.2857" y1="489.9166333333333" y2="489.9166333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4286" x2="268.2857" y1="516.5833333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4286" x2="216.4286" y1="489.9166333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="268.2857" y1="489.9166333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="320.1428000000001" y1="489.9166333333333" y2="489.9166333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="320.1428000000001" y1="516.5833333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="268.2857" y1="489.9166333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.1428000000001" x2="320.1428000000001" y1="489.9166333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.143" x2="372.0001" y1="489.9166333333333" y2="489.9166333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.143" x2="372.0001" y1="516.5833333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.143" x2="320.143" y1="489.9166333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.0001" x2="372.0001" y1="489.9166333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="60.85709999999995" y1="516.5833333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="60.85709999999995" y1="543.2500333333334" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="516.5833333333333" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85709999999995" x2="60.85709999999995" y1="516.5833333333333" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85699999999997" x2="112.7141" y1="516.5833333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85699999999997" x2="112.7141" y1="543.2500333333334" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85699999999997" x2="60.85699999999997" y1="516.5833333333333" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7141" x2="112.7141" y1="516.5833333333333" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7143" x2="164.5714" y1="516.5833333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7143" x2="164.5714" y1="543.2500333333334" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7143" x2="112.7143" y1="516.5833333333333" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="164.5714" y1="516.5833333333333" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="216.4285" y1="516.5833333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="216.4285" y1="543.2500333333334" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="164.5714" y1="516.5833333333333" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4285" x2="216.4285" y1="516.5833333333333" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4286" x2="268.2857" y1="516.5833333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4286" x2="268.2857" y1="543.2500333333334" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4286" x2="216.4286" y1="516.5833333333333" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="268.2857" y1="516.5833333333333" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="320.1428000000001" y1="516.5833333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="320.1428000000001" y1="543.2500333333334" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="268.2857" y1="516.5833333333333" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.1428000000001" x2="320.1428000000001" y1="516.5833333333333" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.143" x2="372.0001" y1="516.5833333333333" y2="516.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.143" x2="372.0001" y1="543.2500333333334" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.143" x2="320.143" y1="516.5833333333333" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.0001" x2="372.0001" y1="516.5833333333333" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="60.85709999999995" y1="543.2500333333334" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="60.85709999999995" y1="569.9167333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="543.2500333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85709999999995" x2="60.85709999999995" y1="543.2500333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85699999999997" x2="112.7141" y1="543.2500333333334" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85699999999997" x2="112.7141" y1="569.9167333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85699999999997" x2="60.85699999999997" y1="543.2500333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7141" x2="112.7141" y1="543.2500333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7143" x2="164.5714" y1="543.2500333333334" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7143" x2="164.5714" y1="569.9167333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7143" x2="112.7143" y1="543.2500333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="164.5714" y1="543.2500333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="216.4285" y1="543.2500333333334" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="216.4285" y1="569.9167333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="164.5714" y1="543.2500333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4285" x2="216.4285" y1="543.2500333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4286" x2="268.2857" y1="543.2500333333334" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4286" x2="268.2857" y1="569.9167333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4286" x2="216.4286" y1="543.2500333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="268.2857" y1="543.2500333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="320.1428000000001" y1="543.2500333333334" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="320.1428000000001" y1="569.9167333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="268.2857" y1="543.2500333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.1428000000001" x2="320.1428000000001" y1="543.2500333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.143" x2="372.0001" y1="543.2500333333334" y2="543.2500333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.143" x2="372.0001" y1="569.9167333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.143" x2="320.143" y1="543.2500333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.0001" x2="372.0001" y1="543.2500333333334" y2="569.9167333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="60.85709999999995" y1="569.9166333333333" y2="569.9166333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="60.85709999999995" y1="596.5833333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="569.9166333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85709999999995" x2="60.85709999999995" y1="569.9166333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85699999999997" x2="112.7141" y1="569.9166333333333" y2="569.9166333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85699999999997" x2="112.7141" y1="596.5833333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="60.85699999999997" x2="60.85699999999997" y1="569.9166333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7141" x2="112.7141" y1="569.9166333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7143" x2="164.5714" y1="569.9166333333333" y2="569.9166333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7143" x2="164.5714" y1="596.5833333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.7143" x2="112.7143" y1="569.9166333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="164.5714" y1="569.9166333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="216.4285" y1="569.9166333333333" y2="569.9166333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="216.4285" y1="596.5833333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.5714" x2="164.5714" y1="569.9166333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4285" x2="216.4285" y1="569.9166333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4286" x2="268.2857" y1="569.9166333333333" y2="569.9166333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4286" x2="268.2857" y1="596.5833333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.4286" x2="216.4286" y1="569.9166333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="268.2857" y1="569.9166333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="320.1428000000001" y1="569.9166333333333" y2="569.9166333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="320.1428000000001" y1="596.5833333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="268.2857" x2="268.2857" y1="569.9166333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.1428000000001" x2="320.1428000000001" y1="569.9166333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.143" x2="372.0001" y1="569.9166333333333" y2="569.9166333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.143" x2="372.0001" y1="596.5833333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.143" x2="320.143" y1="569.9166333333333" y2="596.5833333333333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.0001" x2="372.0001" y1="569.9166333333333" y2="596.5833333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="7.250000000000114" x2="97.25000000000011" y1="924.3333333333335" y2="924.3333333333335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="7.250000000000114" x2="97.25000000000011" y1="976.4966333333334" y2="976.4966333333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="7.250000000000114" x2="7.250000000000114" y1="924.3333333333335" y2="976.4966333333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.25000000000011" x2="97.25000000000011" y1="924.3333333333335" y2="976.4966333333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.25000000000011" x2="367.2500000000001" y1="924.3333333333335" y2="924.3333333333335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.25000000000011" x2="367.2500000000001" y1="976.4966333333334" y2="976.4966333333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.25000000000011" x2="97.25000000000011" y1="924.3333333333335" y2="976.4966333333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="367.2500000000001" x2="367.2500000000001" y1="924.3333333333335" y2="976.4966333333334"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="7.250000000000114" x2="97.25000000000011" y1="976.4966033333335" y2="976.4966033333335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="7.250000000000114" x2="97.25000000000011" y1="1004.415003333333" y2="1004.415003333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="7.250000000000114" x2="7.250000000000114" y1="976.4966033333335" y2="1004.415003333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.25000000000011" x2="97.25000000000011" y1="976.4966033333335" y2="1004.415003333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.25000000000011" x2="187.2500000000001" y1="976.4966033333335" y2="976.4966033333335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.25000000000011" x2="187.2500000000001" y1="1004.415003333333" y2="1004.415003333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.25000000000011" x2="97.25000000000011" y1="976.4966033333335" y2="1004.415003333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="187.2500000000001" x2="187.2500000000001" y1="976.4966033333335" y2="1004.415003333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="187.2500000000002" x2="277.2500000000002" y1="976.4966033333335" y2="976.4966033333335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="187.2500000000002" x2="277.2500000000002" y1="1004.415003333333" y2="1004.415003333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="187.2500000000002" x2="187.2500000000002" y1="976.4966033333335" y2="1004.415003333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="277.2500000000002" x2="277.2500000000002" y1="976.4966033333335" y2="1004.415003333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="277.2500000000001" x2="367.2500000000001" y1="976.4966033333335" y2="976.4966033333335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="277.2500000000001" x2="367.2500000000001" y1="1004.415003333333" y2="1004.415003333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="277.2500000000001" x2="277.2500000000001" y1="976.4966033333335" y2="1004.415003333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="367.2500000000001" x2="367.2500000000001" y1="976.4966033333335" y2="1004.415003333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="7.250000000000114" x2="97.25000000000011" y1="1004.414933333333" y2="1004.414933333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="7.250000000000114" x2="97.25000000000011" y1="1032.333333333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="7.250000000000114" x2="7.250000000000114" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.25000000000011" x2="97.25000000000011" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.25000000000011" x2="187.2500000000001" y1="1004.414933333333" y2="1004.414933333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.25000000000011" x2="187.2500000000001" y1="1032.333333333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.25000000000011" x2="97.25000000000011" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="187.2500000000001" x2="187.2500000000001" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="187.2500000000002" x2="277.2500000000002" y1="1004.414933333333" y2="1004.414933333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="187.2500000000002" x2="277.2500000000002" y1="1032.333333333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="187.2500000000002" x2="187.2500000000002" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="277.2500000000002" x2="277.2500000000002" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="277.2500000000001" x2="367.2500000000001" y1="1004.414933333333" y2="1004.414933333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="277.2500000000001" x2="367.2500000000001" y1="1032.333333333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="277.2500000000001" x2="277.2500000000001" y1="1004.414933333333" y2="1032.333333333333"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="367.2500000000001" x2="367.2500000000001" y1="1004.414933333333" y2="1032.333333333333"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="186" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,57.625,952.583) scale(1,1) translate(0,0)" writing-mode="lr" x="24.25" xml:space="preserve" y="958.58" zvalue="10209">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="185" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,101.875,991.333) scale(1,1) translate(0,0)" writing-mode="lr" x="36.25" xml:space="preserve" y="997.33" zvalue="10210">制图 </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="183" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,278.25,991.333) scale(1,1) translate(0,0)" writing-mode="lr" x="199" xml:space="preserve" y="997.33" zvalue="10211">绘制日期      20200716</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="178" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,108.5,1019.33) scale(1,1) translate(0,0)" writing-mode="lr" x="36" xml:space="preserve" y="1025.33" zvalue="10212">更新              许方杰</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="174" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,280,1019.33) scale(1,1) translate(0,0)" writing-mode="lr" x="197" xml:space="preserve" y="1025.33" zvalue="10213">更新日期      20231219</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" x="190.015625" xml:space="preserve" y="445.09375" zvalue="10214">35kV     Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="190.015625" xml:space="preserve" y="461.09375" zvalue="10214">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" x="240.671875" xml:space="preserve" y="444.59375" zvalue="10215">35kV    Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="240.671875" xml:space="preserve" y="460.59375" zvalue="10215">母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,35,480.083) scale(1,1) translate(0,0)" writing-mode="lr" x="35.00000000000011" xml:space="preserve" y="484.5833333333333" zvalue="10216">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,35,505.583) scale(1,1) translate(0,-1.09042e-13)" writing-mode="lr" x="35.00000000000011" xml:space="preserve" y="510.0833333333333" zvalue="10217">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,35,531.083) scale(1,1) translate(0,0)" writing-mode="lr" x="35.00000000000011" xml:space="preserve" y="535.5833333333333" zvalue="10218">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,35,556.583) scale(1,1) translate(0,6.01833e-14)" writing-mode="lr" x="35.00000000000011" xml:space="preserve" y="561.0833333333333" zvalue="10219">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,35,582.083) scale(1,1) translate(0,1.26029e-13)" writing-mode="lr" x="35.00000000000011" xml:space="preserve" y="586.5833333333333" zvalue="10220">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" x="86.015625" xml:space="preserve" y="445.09375" zvalue="10221">110kV   Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="86.015625" xml:space="preserve" y="461.09375" zvalue="10221">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" x="136.671875" xml:space="preserve" y="444.59375" zvalue="10222">110kV   Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="136.671875" xml:space="preserve" y="460.59375" zvalue="10222">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" x="295.015625" xml:space="preserve" y="445.09375" zvalue="10223">10kV     Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="295.015625" xml:space="preserve" y="461.09375" zvalue="10223">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" x="345.671875" xml:space="preserve" y="444.59375" zvalue="10224">10kV      Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="345.671875" xml:space="preserve" y="460.59375" zvalue="10224">母</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.00000000000011" x2="192.0000000000001" y1="161.3333333333334" y2="161.3333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.00000000000011" x2="192.0000000000001" y1="187.3333333333334" y2="187.3333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.00000000000011" x2="11.00000000000011" y1="161.3333333333334" y2="187.3333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="161.3333333333334" y2="187.3333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="373.0000000000001" y1="161.3333333333334" y2="161.3333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="373.0000000000001" y1="187.3333333333334" y2="187.3333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="161.3333333333334" y2="187.3333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373.0000000000001" x2="373.0000000000001" y1="161.3333333333334" y2="187.3333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.00000000000011" x2="192.0000000000001" y1="187.3333333333334" y2="187.3333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.00000000000011" x2="192.0000000000001" y1="211.5833333333334" y2="211.5833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.00000000000011" x2="11.00000000000011" y1="187.3333333333334" y2="211.5833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="187.3333333333334" y2="211.5833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="373.0000000000001" y1="187.3333333333334" y2="187.3333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="373.0000000000001" y1="211.5833333333334" y2="211.5833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="187.3333333333334" y2="211.5833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373.0000000000001" x2="373.0000000000001" y1="187.3333333333334" y2="211.5833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.00000000000011" x2="192.0000000000001" y1="211.5833333333334" y2="211.5833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.00000000000011" x2="192.0000000000001" y1="235.8333333333334" y2="235.8333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.00000000000011" x2="11.00000000000011" y1="211.5833333333334" y2="235.8333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="211.5833333333334" y2="235.8333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="373.0000000000001" y1="211.5833333333334" y2="211.5833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="373.0000000000001" y1="235.8333333333334" y2="235.8333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="211.5833333333334" y2="235.8333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373.0000000000001" x2="373.0000000000001" y1="211.5833333333334" y2="235.8333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.00000000000011" x2="192.0000000000001" y1="235.8333333333334" y2="235.8333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.00000000000011" x2="192.0000000000001" y1="258.5833333333334" y2="258.5833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.00000000000011" x2="11.00000000000011" y1="235.8333333333334" y2="258.5833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="235.8333333333334" y2="258.5833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="373.0000000000001" y1="235.8333333333334" y2="235.8333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="373.0000000000001" y1="258.5833333333334" y2="258.5833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="235.8333333333334" y2="258.5833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373.0000000000001" x2="373.0000000000001" y1="235.8333333333334" y2="258.5833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.00000000000011" x2="192.0000000000001" y1="258.5833333333334" y2="258.5833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.00000000000011" x2="192.0000000000001" y1="281.3333333333334" y2="281.3333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.00000000000011" x2="11.00000000000011" y1="258.5833333333334" y2="281.3333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="258.5833333333334" y2="281.3333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="373.0000000000001" y1="258.5833333333334" y2="258.5833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="373.0000000000001" y1="281.3333333333334" y2="281.3333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="258.5833333333334" y2="281.3333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373.0000000000001" x2="373.0000000000001" y1="258.5833333333334" y2="281.3333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.00000000000011" x2="192.0000000000001" y1="281.3333333333334" y2="281.3333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.00000000000011" x2="192.0000000000001" y1="304.0833333333334" y2="304.0833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.00000000000011" x2="11.00000000000011" y1="281.3333333333334" y2="304.0833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="281.3333333333334" y2="304.0833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="373.0000000000001" y1="281.3333333333334" y2="281.3333333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="373.0000000000001" y1="304.0833333333334" y2="304.0833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="281.3333333333334" y2="304.0833333333334"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373.0000000000001" x2="373.0000000000001" y1="281.3333333333334" y2="304.0833333333334"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49,175.333) scale(1,1) translate(0,0)" writing-mode="lr" x="49" xml:space="preserve" y="180.83" zvalue="10226">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229,175.333) scale(1,1) translate(0,0)" writing-mode="lr" x="229" xml:space="preserve" y="180.83" zvalue="10227">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.6875,200.583) scale(1,1) translate(0,0)" writing-mode="lr" x="51.69" xml:space="preserve" y="205.08" zvalue="10228">110kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.75,200.333) scale(1,1) translate(0,0)" writing-mode="lr" x="234.75" xml:space="preserve" y="204.83" zvalue="10229">110kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,64.1875,271.333) scale(1,1) translate(0,0)" writing-mode="lr" x="64.19" xml:space="preserve" y="275.83" zvalue="10230">110kV#1主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,247.75,270.833) scale(1,1) translate(0,0)" writing-mode="lr" x="247.75" xml:space="preserve" y="275.33" zvalue="10231">110kV#2主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,66.1875,294.333) scale(1,1) translate(0,0)" writing-mode="lr" x="66.19" xml:space="preserve" y="298.83" zvalue="10232">110kV#1主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,246.5,293.833) scale(1,1) translate(0,0)" writing-mode="lr" x="246.5" xml:space="preserve" y="298.33" zvalue="10233">110kV#2主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.6875,225.583) scale(1,1) translate(0,0)" writing-mode="lr" x="51.69" xml:space="preserve" y="230.08" zvalue="10234">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.75,225.333) scale(1,1) translate(0,0)" writing-mode="lr" x="234.75" xml:space="preserve" y="229.83" zvalue="10235">35kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.9375,248.083) scale(1,1) translate(0,0)" writing-mode="lr" x="52.94" xml:space="preserve" y="252.58" zvalue="10236">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.75,249.083) scale(1,1) translate(0,0)" writing-mode="lr" x="234.75" xml:space="preserve" y="253.58" zvalue="10237">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,191.399,338.675) scale(1,1) translate(0,0)" writing-mode="lr" x="191.4" xml:space="preserve" y="343.17" zvalue="10238">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,296.399,338.675) scale(1,1) translate(0,0)" writing-mode="lr" x="296.4" xml:space="preserve" y="343.17" zvalue="10239">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.25,632.583) scale(1,1) translate(0,0)" writing-mode="lr" x="77.25000000000011" xml:space="preserve" y="637.0833333333334" zvalue="10241">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,913.566,310.62) scale(1,1) translate(0,0)" writing-mode="lr" x="913.5661706220012" xml:space="preserve" y="315.120235703969" zvalue="10290">112</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="224" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,830.69,298.494) scale(1,1) translate(0,0)" writing-mode="lr" x="830.6900000000001" xml:space="preserve" y="302.99" zvalue="10292">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="223" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,996.003,299.605) scale(1,1) translate(0,0)" writing-mode="lr" x="996" xml:space="preserve" y="304.1" zvalue="10294">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="222" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1001.25,249.936) scale(1,1) translate(0,0)" writing-mode="lr" x="1001.25" xml:space="preserve" y="254.44" zvalue="10296">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="221" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,828.19,249.936) scale(1,1) translate(0,0)" writing-mode="lr" x="828.1900000000001" xml:space="preserve" y="254.44" zvalue="10298">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="286" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(180,1140.37,743.032) scale(-1,-1) translate(-2280.75,-1486.06)" writing-mode="lr" x="1140.37" xml:space="preserve" y="747.53" zvalue="10314">0121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="310" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1293.48,829.981) scale(1,1) translate(0,0)" writing-mode="lr" x="1293.48" xml:space="preserve" y="834.48" zvalue="10322">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="309" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1285.76,733.091) scale(1,1) translate(0,0)" writing-mode="lr" x="1285.76" xml:space="preserve" y="737.59" zvalue="10324">032</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="308" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1301.79,842.177) scale(1,1) translate(0,0)" writing-mode="lr" x="1301.79" xml:space="preserve" y="846.6799999999999" zvalue="10330">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="305" stroke="rgb(255,255,255)" text-anchor="middle" x="1316.90625" xml:space="preserve" y="986.265625" zvalue="10336">10kV1号电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="305" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1316.90625" xml:space="preserve" y="1002.265625" zvalue="10336">容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="304" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1292.98,857.926) scale(1,1) translate(0,0)" writing-mode="lr" x="1292.98" xml:space="preserve" y="862.4299999999999" zvalue="10338">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="339" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1080.75,743.071) scale(1,1) translate(0,0)" writing-mode="lr" x="1080.75" xml:space="preserve" y="747.5700000000001" zvalue="10348">012</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="344" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1114.49,770.357) scale(1,1) translate(0,-6.68989e-13)" writing-mode="lr" x="1114.49" xml:space="preserve" y="774.86" zvalue="10351">10kV分段</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="345" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,916.821,250.107) scale(1,1) translate(0,-3.62099e-13)" writing-mode="lr" x="916.8200000000001" xml:space="preserve" y="254.61" zvalue="10353">110kV分段</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="347" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1638.26,444.929) scale(1,1) translate(0,0)" writing-mode="lr" x="1638.26" xml:space="preserve" y="449.43" zvalue="10356">312</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="366" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1463.6,637.161) scale(1,1) translate(0,0)" writing-mode="lr" x="1463.6" xml:space="preserve" y="641.66" zvalue="10359">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="365" stroke="rgb(255,255,255)" text-anchor="middle" x="1440.984375" xml:space="preserve" y="529.4285714285714" zvalue="10363">10kVⅠ母电压互</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="365" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1440.984375" xml:space="preserve" y="545.4129464285714" zvalue="10363">感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="368" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,814.107,640.812) scale(1,1) translate(0,0)" writing-mode="lr" x="814.11" xml:space="preserve" y="645.3099999999999" zvalue="10365">0902</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="367" stroke="rgb(255,255,255)" text-anchor="middle" x="786.796875" xml:space="preserve" y="535.1428584309367" zvalue="10369">10kVⅡ母电压互</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="367" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="786.796875" xml:space="preserve" y="551.1272334309367" zvalue="10369">感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="381" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1330.57,175.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1330.57" xml:space="preserve" y="180.21" zvalue="10371">110kVⅠ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="383" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,684.286,177) scale(1,1) translate(0,0)" writing-mode="lr" x="684.29" xml:space="preserve" y="181.5" zvalue="10373">110kVⅡ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="396" stroke="rgb(255,255,255)" text-anchor="middle" x="909.125" xml:space="preserve" y="986.75" zvalue="10382">10kV2号电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="396" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="909.125" xml:space="preserve" y="1002.75" zvalue="10382">容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="392" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,883.981,860.286) scale(1,1) translate(0,0)" writing-mode="lr" x="883.98" xml:space="preserve" y="864.79" zvalue="10384">10</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="416" stroke="rgb(255,255,255)" text-anchor="middle" x="1578.1171875" xml:space="preserve" y="262.8469689419201" zvalue="10396">35kVⅠ母电压互</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="416" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1578.1171875" xml:space="preserve" y="278.8469689419201" zvalue="10396">感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="420" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1638.2,217.802) scale(1,1) translate(0,0)" writing-mode="lr" x="1638.2" xml:space="preserve" y="222.3" zvalue="10399">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="422" stroke="rgb(255,255,255)" text-anchor="middle" x="1568.8359375" xml:space="preserve" y="476.6339285714286" zvalue="10403">35kVⅡ母电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="422" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1568.8359375" xml:space="preserve" y="492.6339285714286" zvalue="10403">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="421" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1638.2,513.516) scale(1,1) translate(0,0)" writing-mode="lr" x="1638.2" xml:space="preserve" y="518.02" zvalue="10405">3902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="438" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,877.409,513.024) scale(1,1) translate(0,0)" writing-mode="lr" x="877.41" xml:space="preserve" y="517.52" zvalue="10411">3020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="439" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,963.409,513.024) scale(1,1) translate(0,0)" writing-mode="lr" x="963.41" xml:space="preserve" y="517.52" zvalue="10415">3010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="450" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,397.175,891.78) scale(1,1) translate(0,0)" writing-mode="lr" x="397.18" xml:space="preserve" y="896.28" zvalue="10424">0020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="451" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1687.77,893.923) scale(1,1) translate(0,0)" writing-mode="lr" x="1687.77" xml:space="preserve" y="898.42" zvalue="10431">0010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="290" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1639.57,959.357) scale(1,1) translate(0,0)" writing-mode="lr" x="1639.57" xml:space="preserve" y="963.86" zvalue="10487">10kV1号接地变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="315" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,438.207,955.161) scale(1,1) translate(0,0)" writing-mode="lr" x="438.2071285617835" xml:space="preserve" y="959.6607136726379" zvalue="10489">10kV2号接地变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="428" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,846.201,830.093) scale(1,1) translate(0,0)" writing-mode="lr" x="846.2" xml:space="preserve" y="834.59" zvalue="10521">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="415" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,804.411,822.568) scale(1,1) translate(0,0)" writing-mode="lr" x="804.41" xml:space="preserve" y="827.0700000000001" zvalue="10523">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="405" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,803.171,734.451) scale(1,1) translate(0,0)" writing-mode="lr" x="803.17" xml:space="preserve" y="738.95" zvalue="10525">053</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="395" stroke="rgb(255,255,255)" text-anchor="middle" x="824.6875" xml:space="preserve" y="935.5989583333334" zvalue="10528">10kV陇川机场</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="395" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="824.6875" xml:space="preserve" y="952.5989583333334" zvalue="10528">Ⅰ回线</text>
  <rect fill="none" fill-opacity="0" height="91" id="57" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,921,478.5) scale(1,1) translate(0,0)" width="138" x="852" y="433" zvalue="10598"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" x="920.203125" xml:space="preserve" y="456.6001408793194" zvalue="10600">35kV消弧线圈成套装</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="920.203125" xml:space="preserve" y="472.6001408793194" zvalue="10600">置</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="161" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235.125,947.583) scale(1,1) translate(0,0)" writing-mode="lr" x="171.25" xml:space="preserve" y="953.58" zvalue="10765">JingHan-03-2023</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="566" stroke="rgb(255,255,255)" text-anchor="middle" x="754.25" xml:space="preserve" y="936.75" zvalue="10773">10kV景罕糖厂</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="566" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="754.25" xml:space="preserve" y="952.75" zvalue="10773">Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="388" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,496.111,534.583) scale(1,1) translate(0,0)" writing-mode="lr" x="496.11" xml:space="preserve" y="539.08" zvalue="10784">31500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="389" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1314.11,528.583) scale(1,1) translate(0,0)" writing-mode="lr" x="1314.11" xml:space="preserve" y="533.08" zvalue="10786">31500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="613" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,500.839,823.997) scale(1,1) translate(0,0)" writing-mode="lr" x="500.84" xml:space="preserve" y="828.5" zvalue="10882">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="612" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,498.171,734.451) scale(1,1) translate(0,0)" writing-mode="lr" x="498.17" xml:space="preserve" y="738.95" zvalue="10884">057</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="611" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,520.537,929.958) scale(1,1) translate(0,0)" writing-mode="lr" x="520.54" xml:space="preserve" y="934.46" zvalue="10887">10kV芒洪线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="630" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,581.839,823.997) scale(1,1) translate(0,0)" writing-mode="lr" x="581.84" xml:space="preserve" y="828.5" zvalue="10899">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="629" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,579.171,734.451) scale(1,1) translate(0,0)" writing-mode="lr" x="579.17" xml:space="preserve" y="738.95" zvalue="10901">056</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="627" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,601.537,929.958) scale(1,1) translate(0,0)" writing-mode="lr" x="601.54" xml:space="preserve" y="934.46" zvalue="10904">10kV广母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="660" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,265.235,405) scale(1,1) translate(0,-1.74527e-13)" writing-mode="lr" x="265.2350158691406" xml:space="preserve" y="409.5" zvalue="10924">小电流接地</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="659" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.2857,331.539) scale(1,1) translate(0,3.55759e-13)" writing-mode="lr" x="62.28570556640625" xml:space="preserve" y="336.0388641357421" zvalue="10925">全站公用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="634" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,612.311,828.224) scale(1,1) translate(0,0)" writing-mode="lr" x="612.3099999999999" xml:space="preserve" y="832.72" zvalue="10936">6</text>
  <ellipse cx="536" cy="243" fill="rgb(255,0,0)" fill-opacity="1" id="593" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10952"/>
  <ellipse cx="1082" cy="243" fill="rgb(255,0,0)" fill-opacity="1" id="594" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10954"/>
  <ellipse cx="913" cy="295" fill="rgb(255,0,0)" fill-opacity="1" id="620" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10956"/>
  <ellipse cx="607" cy="407" fill="rgb(255,0,0)" fill-opacity="1" id="642" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10962"/>
  <ellipse cx="1253" cy="407" fill="rgb(255,0,0)" fill-opacity="1" id="649" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10964"/>
  <ellipse cx="1729.66" cy="184.66" fill="rgb(255,0,0)" fill-opacity="1" id="623" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10966"/>
  <ellipse cx="1729.66" cy="308.66" fill="rgb(255,0,0)" fill-opacity="1" id="635" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10968"/>
  <ellipse cx="1729.66" cy="462.66" fill="rgb(255,0,0)" fill-opacity="1" id="650" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10970"/>
  <ellipse cx="1729.66" cy="585.66" fill="rgb(255,0,0)" fill-opacity="1" id="652" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10972"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="667" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1154,240.832) scale(1,1) translate(0,0)" writing-mode="lr" x="1154" xml:space="preserve" y="245.33" zvalue="10976">171</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="666" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1157.49,179.942) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.49" xml:space="preserve" y="184.44" zvalue="10978">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="665" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1208.09,157.503) scale(1,1) translate(0,0)" writing-mode="lr" x="1208.09" xml:space="preserve" y="162" zvalue="10980">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="655" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1157.42,309.737) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.42" xml:space="preserve" y="314.24" zvalue="10983">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="654" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1208.09,220.749) scale(1,1) translate(0,0)" writing-mode="lr" x="1208.09" xml:space="preserve" y="225.25" zvalue="10985">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="653" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1208.09,292.789) scale(1,1) translate(0,0)" writing-mode="lr" x="1208.09" xml:space="preserve" y="297.29" zvalue="10988">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="690" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1169.32,69.7289) scale(1,1) translate(0,0)" writing-mode="lr" x="1169.32" xml:space="preserve" y="74.23" zvalue="11001">110kV景琪线</text>
  <ellipse cx="1193.03" cy="241.03" fill="rgb(255,0,0)" fill-opacity="1" id="668" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="11008"/>
 </g>
 <g id="ButtonClass">
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="125.94" y="392.25" zvalue="10760"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="26.19" y="392.25" zvalue="10761"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="25.19" y="356.75" zvalue="10762"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="1430">
   <use class="kv110" height="40" transform="rotate(0,1065.17,104.573) scale(1.23333,-1.07136) translate(-198.02,-200.753)" width="30" x="1046.674650646391" xlink:href="#ACLineSegment:单相PT线路_0" y="83.14553907678066" zvalue="7515"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249329565699" ObjectName="110kV凤景线"/>
   <cge:TPSR_Ref TObjectID="8444249329565699_5066549682241537"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1065.17,104.573) scale(1.23333,-1.07136) translate(-198.02,-200.753)" width="30" x="1046.674650646391" y="83.14553907678066"/></g>
  <g id="819">
   <use class="kv110" height="40" transform="rotate(0,518.5,104.573) scale(1.23333,-1.07136) translate(-94.5946,-200.753)" width="30" x="500" xlink:href="#ACLineSegment:单相PT线路_0" y="83.14553844099777" zvalue="9719"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249319866373" ObjectName="110kV瑞景线"/>
   <cge:TPSR_Ref TObjectID="8444249319866373_5066549682241537"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,518.5,104.573) scale(1.23333,-1.07136) translate(-94.5946,-200.753)" width="30" x="500" y="83.14553844099777"/></g>
  <g id="38">
   <use class="kv35" height="40" transform="rotate(90,1862.98,165.025) scale(1.29527,1.24567) translate(-420.25,-27.633)" width="30" x="1843.546466931057" xlink:href="#ACLineSegment:线路带壁雷器_0" y="140.1119581038268" zvalue="10006"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249321897988" ObjectName="35kV景勐线"/>
   <cge:TPSR_Ref TObjectID="8444249321897988_5066549682241537"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1862.98,165.025) scale(1.29527,1.24567) translate(-420.25,-27.633)" width="30" x="1843.546466931057" y="140.1119581038268"/></g>
  <g id="176">
   <use class="kv35" height="40" transform="rotate(90,1862.98,288.806) scale(1.29527,1.24567) translate(-420.25,-52.0451)" width="30" x="1843.54646707702" xlink:href="#ACLineSegment:线路带壁雷器_0" y="263.8920848448484" zvalue="10029"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249324978182" ObjectName="35kV景陇线"/>
   <cge:TPSR_Ref TObjectID="8444249324978182_5066549682241537"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1862.98,288.806) scale(1.29527,1.24567) translate(-420.25,-52.0451)" width="30" x="1843.54646707702" y="263.8920848448484"/></g>
  <g id="410">
   <use class="kv35" height="40" transform="rotate(90,1864.09,565.917) scale(1.29527,1.24567) translate(-420.503,-106.697)" width="30" x="1844.657578188131" xlink:href="#ACLineSegment:线路带壁雷器_0" y="541.0031959559594" zvalue="10118"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249303678982" ObjectName="35kV景城线"/>
   <cge:TPSR_Ref TObjectID="8444249303678982_5066549682241537"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1864.09,565.917) scale(1.29527,1.24567) translate(-420.503,-106.697)" width="30" x="1844.657578188131" y="541.0031959559594"/></g>
  <g id="518">
   <use class="kv35" height="40" transform="rotate(90,1864.09,709.917) scale(1.29527,1.24567) translate(-420.503,-135.097)" width="30" x="1844.657578188131" xlink:href="#ACLineSegment:线路带壁雷器_0" y="685.0031959559595" zvalue="10141"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249327075332" ObjectName="35kV章景线"/>
   <cge:TPSR_Ref TObjectID="8444249327075332_5066549682241537"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1864.09,709.917) scale(1.29527,1.24567) translate(-420.503,-135.097)" width="30" x="1844.657578188131" y="685.0031959559595"/></g>
 </g>
 <g id="BreakerClass">
  <g id="1429">
   <use class="kv110" height="20" transform="rotate(0,1065.17,240.353) scale(1.5542,1.35421) translate(-377.049,-59.3253)" width="10" x="1057.397495402218" xlink:href="#Breaker:开关_0" y="226.8113091879633" zvalue="7516"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924596727812" ObjectName="110kV凤景线172断路器"/>
   <cge:TPSR_Ref TObjectID="6473924596727812"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1065.17,240.353) scale(1.5542,1.35421) translate(-377.049,-59.3253)" width="10" x="1057.397495402218" y="226.8113091879633"/></g>
  <g id="276">
   <use class="kv110" height="20" transform="rotate(180,1234.21,406.542) scale(1.5542,1.35421) translate(-437.325,-102.794)" width="10" x="1226.435758341948" xlink:href="#Breaker:开关_0" y="393.0000295455666" zvalue="7804"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925170364419" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925170364419"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1234.21,406.542) scale(1.5542,1.35421) translate(-437.325,-102.794)" width="10" x="1226.435758341948" y="393.0000295455666"/></g>
  <g id="495">
   <use class="kv35" height="20" transform="rotate(90,1640.04,145.02) scale(2.00844,1.75) translate(-818.425,-54.6515)" width="10" x="1630.001873289985" xlink:href="#Breaker:小车断路器_0" y="127.5200805664061" zvalue="8110"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925170429955" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925170429955"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1640.04,145.02) scale(2.00844,1.75) translate(-818.425,-54.6515)" width="10" x="1630.001873289985" y="127.5200805664061"/></g>
  <g id="512">
   <use class="kv110" height="20" transform="rotate(180,587.921,406.542) scale(1.5542,1.35421) translate(-206.871,-102.794)" width="10" x="580.1500320553865" xlink:href="#Breaker:开关_0" y="393.0000295455666" zvalue="9168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925170495491" ObjectName="#2主变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473925170495491"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,587.921,406.542) scale(1.5542,1.35421) translate(-206.871,-102.794)" width="10" x="580.1500320553865" y="393.0000295455666"/></g>
  <g id="721">
   <use class="kv10" height="20" transform="rotate(180,587.921,630.875) scale(2.00844,1.75) translate(-290.153,-262.875)" width="10" x="577.8788469860815" xlink:href="#Breaker:小车断路器_0" y="613.3754380451095" zvalue="9190"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925170561027" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473925170561027"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,587.921,630.875) scale(2.00844,1.75) translate(-290.153,-262.875)" width="10" x="577.8788469860815" y="613.3754380451095"/></g>
  <g id="461">
   <use class="kv10" height="20" transform="rotate(180,1233.28,631.833) scale(2.00844,1.75) translate(-614.191,-263.286)" width="10" x="1223.241576288122" xlink:href="#Breaker:小车断路器_0" y="614.3333333333333" zvalue="9225"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925170626563" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925170626563"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1233.28,631.833) scale(2.00844,1.75) translate(-614.191,-263.286)" width="10" x="1223.241576288122" y="614.3333333333333"/></g>
  <g id="546">
   <use class="kv10" height="20" transform="rotate(180,1396.62,731.701) scale(2.00844,1.75) translate(-696.201,-306.086)" width="10" x="1386.57685352622" xlink:href="#Breaker:小车断路器_0" y="714.2013397470844" zvalue="9396"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925170692099" ObjectName="10kV景罕镇线033断路器"/>
   <cge:TPSR_Ref TObjectID="6473925170692099"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1396.62,731.701) scale(2.00844,1.75) translate(-696.201,-306.086)" width="10" x="1386.57685352622" y="714.2013397470844"/></g>
  <g id="816">
   <use class="kv110" height="20" transform="rotate(0,518.877,242.353) scale(1.5542,1.35421) translate(-182.251,-59.8484)" width="10" x="511.1063428843589" xlink:href="#Breaker:开关_0" y="228.8113091879632" zvalue="9720"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925170757635" ObjectName="110kV瑞景线173断路器"/>
   <cge:TPSR_Ref TObjectID="6473925170757635"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,518.877,242.353) scale(1.5542,1.35421) translate(-182.251,-59.8484)" width="10" x="511.1063428843589" y="228.8113091879632"/></g>
  <g id="33">
   <use class="kv10" height="20" transform="rotate(180,1241.62,731.701) scale(2.00844,1.75) translate(-618.376,-306.086)" width="10" x="1231.57685352622" xlink:href="#Breaker:小车断路器_0" y="714.2013397216795" zvalue="9763"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925170823171" ObjectName="10kV1号站用变031断路器"/>
   <cge:TPSR_Ref TObjectID="6473925170823171"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1241.62,731.701) scale(2.00844,1.75) translate(-618.376,-306.086)" width="10" x="1231.57685352622" y="714.2013397216795"/></g>
  <g id="92">
   <use class="kv10" height="20" transform="rotate(180,1480.62,731.701) scale(2.00844,1.75) translate(-738.378,-306.086)" width="10" x="1470.57685352622" xlink:href="#Breaker:小车断路器_0" y="714.2013397470844" zvalue="9808"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924575756292" ObjectName="10kV弄冒线035断路器"/>
   <cge:TPSR_Ref TObjectID="6473924575756292"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1480.62,731.701) scale(2.00844,1.75) translate(-738.378,-306.086)" width="10" x="1470.57685352622" y="714.2013397470844"/></g>
  <g id="795">
   <use class="kv10" height="20" transform="rotate(180,1560.62,731.701) scale(2.00844,1.75) translate(-778.546,-306.086)" width="10" x="1550.57685352622" xlink:href="#Breaker:小车断路器_0" y="714.2013397470844" zvalue="9829"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925170954243" ObjectName="10kV芒面线036断路器"/>
   <cge:TPSR_Ref TObjectID="6473925170954243"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1560.62,731.701) scale(2.00844,1.75) translate(-778.546,-306.086)" width="10" x="1550.57685352622" y="714.2013397470844"/></g>
  <g id="854">
   <use class="kv10" height="20" transform="rotate(180,1636.62,731.701) scale(2.00844,1.75) translate(-816.706,-306.086)" width="10" x="1626.57685352622" xlink:href="#Breaker:小车断路器_0" y="714.2013397470844" zvalue="9848"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925171019779" ObjectName="10kV1号接地变038断路器"/>
   <cge:TPSR_Ref TObjectID="6473925171019779"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1636.62,731.701) scale(2.00844,1.75) translate(-816.706,-306.086)" width="10" x="1626.57685352622" y="714.2013397470844"/></g>
  <g id="992">
   <use class="kv10" height="20" transform="rotate(180,907.333,731.701) scale(2.00844,1.75) translate(-450.531,-306.086)" width="10" x="897.2911392405061" xlink:href="#Breaker:小车断路器_0" y="714.2013397216795" zvalue="9868"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925176197123" ObjectName="10kV2号电容器052断路器"/>
   <cge:TPSR_Ref TObjectID="6473925176197123"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,907.333,731.701) scale(2.00844,1.75) translate(-450.531,-306.086)" width="10" x="897.2911392405061" y="714.2013397216795"/></g>
  <g id="978">
   <use class="kv10" height="20" transform="rotate(180,755.333,731.701) scale(2.00844,1.75) translate(-374.211,-306.086)" width="10" x="745.291139240506" xlink:href="#Breaker:小车断路器_0" y="714.2013397216795" zvalue="9886"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925171281923" ObjectName="10kV景罕糖厂Ⅰ回线054断路器"/>
   <cge:TPSR_Ref TObjectID="6473925171281923"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,755.333,731.701) scale(2.00844,1.75) translate(-374.211,-306.086)" width="10" x="745.291139240506" y="714.2013397216795"/></g>
  <g id="962">
   <use class="kv10" height="20" transform="rotate(180,983.333,731.701) scale(2.00844,1.75) translate(-488.69,-306.086)" width="10" x="973.2911392405063" xlink:href="#Breaker:小车断路器_0" y="714.2013397216795" zvalue="9902"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925171216387" ObjectName="10kV2号站用变051断路器"/>
   <cge:TPSR_Ref TObjectID="6473925171216387"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,983.333,731.701) scale(2.00844,1.75) translate(-488.69,-306.086)" width="10" x="973.2911392405063" y="714.2013397216795"/></g>
  <g id="932">
   <use class="kv10" height="20" transform="rotate(180,679.333,731.701) scale(2.00844,1.75) translate(-336.052,-306.086)" width="10" x="669.291139240506" xlink:href="#Breaker:小车断路器_0" y="714.2013397216795" zvalue="9927"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924575821828" ObjectName="10kV弄贯线055断路器"/>
   <cge:TPSR_Ref TObjectID="6473924575821828"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,679.333,731.701) scale(2.00844,1.75) translate(-336.052,-306.086)" width="10" x="669.291139240506" y="714.2013397216795"/></g>
  <g id="898">
   <use class="kv10" height="20" transform="rotate(180,447.333,731.701) scale(2.00844,1.75) translate(-219.564,-306.086)" width="10" x="437.2911392405061" xlink:href="#Breaker:小车断路器_0" y="714.2013397216795" zvalue="9965"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925176262659" ObjectName="10kV2号接地变058断路器"/>
   <cge:TPSR_Ref TObjectID="6473925176262659"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,447.333,731.701) scale(2.00844,1.75) translate(-219.564,-306.086)" width="10" x="437.2911392405061" y="714.2013397216795"/></g>
  <g id="22">
   <use class="kv35" height="20" transform="rotate(90,1727.88,166.687) scale(2.00844,1.75) translate(-862.526,-63.9372)" width="10" x="1717.835176105741" xlink:href="#Breaker:小车断路器_0" y="149.1867472330729" zvalue="9994"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925171412995" ObjectName="35kV景勐线372断路器"/>
   <cge:TPSR_Ref TObjectID="6473925171412995"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1727.88,166.687) scale(2.00844,1.75) translate(-862.526,-63.9372)" width="10" x="1717.835176105741" y="149.1867472330729"/></g>
  <g id="194">
   <use class="kv35" height="20" transform="rotate(90,1727.88,290.687) scale(2.00844,1.75) translate(-862.526,-117.08)" width="10" x="1717.835176105741" xlink:href="#Breaker:小车断路器_0" y="273.1867472330729" zvalue="10018"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925171478531" ObjectName="35kV景陇线371断路器"/>
   <cge:TPSR_Ref TObjectID="6473925171478531"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1727.88,290.687) scale(2.00844,1.75) translate(-862.526,-117.08)" width="10" x="1717.835176105741" y="273.1867472330729"/></g>
  <g id="235">
   <use class="kv35" height="20" transform="rotate(90,1640.04,668.036) scale(2.00844,1.75) translate(-818.425,-278.801)" width="10" x="1630.001873289985" xlink:href="#Breaker:小车断路器_0" y="650.5357131958008" zvalue="10055"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925171544067" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925171544067"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1640.04,668.036) scale(2.00844,1.75) translate(-818.425,-278.801)" width="10" x="1630.001873289985" y="650.5357131958008"/></g>
  <g id="349">
   <use class="kv35" height="20" transform="rotate(90,1727.88,442.687) scale(2.00844,1.75) translate(-862.526,-182.223)" width="10" x="1717.835176105741" xlink:href="#Breaker:小车断路器_0" y="425.1867472330729" zvalue="10084"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925171609603" ObjectName="35kV景安线381断路器"/>
   <cge:TPSR_Ref TObjectID="6473925171609603"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1727.88,442.687) scale(2.00844,1.75) translate(-862.526,-182.223)" width="10" x="1717.835176105741" y="425.1867472330729"/></g>
  <g id="452">
   <use class="kv35" height="20" transform="rotate(90,1727.88,566.687) scale(2.00844,1.75) translate(-862.526,-235.366)" width="10" x="1717.835176105741" xlink:href="#Breaker:小车断路器_0" y="549.1867472330729" zvalue="10107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925171675140" ObjectName="35kV景城线382断路器"/>
   <cge:TPSR_Ref TObjectID="6473925171675140"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1727.88,566.687) scale(2.00844,1.75) translate(-862.526,-235.366)" width="10" x="1717.835176105741" y="549.1867472330729"/></g>
  <g id="562">
   <use class="kv35" height="20" transform="rotate(90,1727.88,710.687) scale(2.00844,1.75) translate(-862.526,-297.08)" width="10" x="1717.835176105741" xlink:href="#Breaker:小车断路器_0" y="693.1867472330729" zvalue="10130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925176000515" ObjectName="35kV章景线383断路器"/>
   <cge:TPSR_Ref TObjectID="6473925176000515"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1727.88,710.687) scale(2.00844,1.75) translate(-862.526,-297.08)" width="10" x="1717.835176105741" y="693.1867472330729"/></g>
  <g id="270">
   <use class="kv110" height="20" transform="rotate(270,912.401,279.448) scale(1.5542,-1.35421) translate(-322.575,-482.262)" width="10" x="904.6301524081686" xlink:href="#Breaker:母联开关_0" y="265.906378589666" zvalue="10289"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925171806212" ObjectName="110kV分段112断路器"/>
   <cge:TPSR_Ref TObjectID="6473925171806212"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,912.401,279.448) scale(1.5542,-1.35421) translate(-322.575,-482.262)" width="10" x="904.6301524081686" y="265.906378589666"/></g>
  <g id="322">
   <use class="kv10" height="20" transform="rotate(0,1315.19,731.02) scale(2.18182,1.81818) translate(-706.486,-320.777)" width="10" x="1304.282338008027" xlink:href="#Breaker:小车断路器_0" y="712.837703376064" zvalue="10323"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925171871748" ObjectName="10kV1号电容器032断路器"/>
   <cge:TPSR_Ref TObjectID="6473925171871748"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1315.19,731.02) scale(2.18182,1.81818) translate(-706.486,-320.777)" width="10" x="1304.282338008027" y="712.837703376064"/></g>
  <g id="338">
   <use class="kv10" height="20" transform="rotate(0,1055.46,745.629) scale(2.11429,1.95571) translate(-550.687,-354.815)" width="10" x="1044.892857142857" xlink:href="#Breaker:母联小车开关_0" y="726.0714285714286" zvalue="10347"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925171937284" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473925171937284"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1055.46,745.629) scale(2.11429,1.95571) translate(-550.687,-354.815)" width="10" x="1044.892857142857" y="726.0714285714286"/></g>
  <g id="346">
   <use class="kv35" height="20" transform="rotate(270,1640.04,422.214) scale(2.00844,1.75) translate(-818.425,-173.449)" width="10" x="1630.001873426224" xlink:href="#Breaker:母联小车开关_0" y="404.7142857142857" zvalue="10355"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925176066051" ObjectName="35kV分段312断路器"/>
   <cge:TPSR_Ref TObjectID="6473925176066051"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1640.04,422.214) scale(2.00844,1.75) translate(-818.425,-173.449)" width="10" x="1630.001873426224" y="404.7142857142857"/></g>
  <g id="471">
   <use class="kv10" height="20" transform="rotate(180,829.333,731.701) scale(2.00844,1.75) translate(-411.367,-306.086)" width="10" x="819.291139240506" xlink:href="#Breaker:小车断路器_0" y="714.2013397216795" zvalue="10524"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925176131587" ObjectName="10kV陇川机场Ⅰ回线053断路器"/>
   <cge:TPSR_Ref TObjectID="6473925176131587"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,829.333,731.701) scale(2.00844,1.75) translate(-411.367,-306.086)" width="10" x="819.291139240506" y="714.2013397216795"/></g>
  <g id="624">
   <use class="kv10" height="20" transform="rotate(180,520.333,731.701) scale(2.00844,1.75) translate(-256.218,-306.086)" width="10" x="510.2911392405061" xlink:href="#Breaker:小车断路器_0" y="714.2013397216795" zvalue="10883"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924575887364" ObjectName="10kV芒洪线057断路器"/>
   <cge:TPSR_Ref TObjectID="6473924575887364"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,520.333,731.701) scale(2.00844,1.75) translate(-256.218,-306.086)" width="10" x="510.2911392405061" y="714.2013397216795"/></g>
  <g id="647">
   <use class="kv10" height="20" transform="rotate(180,601.333,731.701) scale(2.00844,1.75) translate(-296.888,-306.086)" width="10" x="591.291139240506" xlink:href="#Breaker:小车断路器_0" y="714.2013397216795" zvalue="10900"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924575952900" ObjectName="10kV广母线056断路器"/>
   <cge:TPSR_Ref TObjectID="6473924575952900"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,601.333,731.701) scale(2.00844,1.75) translate(-296.888,-306.086)" width="10" x="591.291139240506" y="714.2013397216795"/></g>
  <g id="681">
   <use class="kv110" height="20" transform="rotate(0,1175.4,241.208) scale(1.5542,1.35421) translate(-416.354,-59.5488)" width="10" x="1167.624689369681" xlink:href="#Breaker:开关_0" y="227.6657701111827" zvalue="10975"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924598038532" ObjectName="110kV景琪线171断路器"/>
   <cge:TPSR_Ref TObjectID="6473924598038532"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1175.4,241.208) scale(1.5542,1.35421) translate(-416.354,-59.5488)" width="10" x="1167.624689369681" y="227.6657701111827"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="1428">
   <use class="kv110" height="30" transform="rotate(0,1065.17,176.921) scale(-0.947693,0.6712) translate(-2189.53,81.736)" width="15" x="1058.06695375981" xlink:href="#Disconnector:刀闸_0" y="166.8531260541916" zvalue="7518"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450334949381" ObjectName="110kV凤景线1726隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450334949381"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1065.17,176.921) scale(-0.947693,0.6712) translate(-2189.53,81.736)" width="15" x="1058.06695375981" y="166.8531260541916"/></g>
  <g id="1397">
   <use class="kv110" height="30" transform="rotate(180,1065.16,308.19) scale(0.947693,-0.6712) translate(58.3984,-772.284)" width="15" x="1058.055357105048" xlink:href="#Disconnector:刀闸_0" y="298.1218638471603" zvalue="7532"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450334752773" ObjectName="110kV凤景线1721隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450334752773"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1065.16,308.19) scale(0.947693,-0.6712) translate(58.3984,-772.284)" width="15" x="1058.055357105048" y="298.1218638471603"/></g>
  <g id="275">
   <use class="kv110" height="30" transform="rotate(180,1234.2,447.974) scale(-0.947693,0.6712) translate(-2536.91,214.516)" width="15" x="1227.092887806192" xlink:href="#Disconnector:刀闸_0" y="437.9064097455976" zvalue="7807"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454431670275" ObjectName="#1主变110kV侧1016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454431670275"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1234.2,447.974) scale(-0.947693,0.6712) translate(-2536.91,214.516)" width="15" x="1227.092887806192" y="437.9064097455976"/></g>
  <g id="272">
   <use class="kv110" height="30" transform="rotate(0,1234.21,360.706) scale(0.947693,-0.6712) translate(67.7287,-903.042)" width="15" x="1227.099058154889" xlink:href="#Disconnector:刀闸_0" y="350.637672132585" zvalue="7812"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454431604739" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454431604739"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1234.21,360.706) scale(0.947693,-0.6712) translate(67.7287,-903.042)" width="15" x="1227.099058154889" y="350.637672132585"/></g>
  <g id="1453">
   <use class="kv110" height="30" transform="rotate(180,1329.38,283.388) scale(0.947693,-0.6712) translate(72.9816,-710.532)" width="15" x="1322.271448661275" xlink:href="#Disconnector:刀闸_0" y="273.3203550841632" zvalue="8018"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454477217795" ObjectName="110kVⅠ母电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454477217795"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1329.38,283.388) scale(0.947693,-0.6712) translate(72.9816,-710.532)" width="15" x="1322.271448661275" y="273.3203550841632"/></g>
  <g id="118">
   <use class="kv110" height="30" transform="rotate(180,683.405,283.388) scale(0.947693,-0.6712) translate(37.3276,-710.532)" width="15" x="676.2971331126927" xlink:href="#Disconnector:刀闸_0" y="273.3203550841632" zvalue="8859"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454477545475" ObjectName="110kVⅡ母电压互感器1902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454477545475"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,683.405,283.388) scale(0.947693,-0.6712) translate(37.3276,-710.532)" width="15" x="676.2971331126927" y="273.3203550841632"/></g>
  <g id="511">
   <use class="kv110" height="30" transform="rotate(180,587.915,447.974) scale(-0.947693,0.6712) translate(-1208.67,214.516)" width="15" x="580.8071735204782" xlink:href="#Disconnector:刀闸_0" y="437.9064097455976" zvalue="9170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454433112067" ObjectName="#2主变110kV侧1026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454433112067"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,587.915,447.974) scale(-0.947693,0.6712) translate(-1208.67,214.516)" width="15" x="580.8071735204782" y="437.9064097455976"/></g>
  <g id="509">
   <use class="kv110" height="30" transform="rotate(0,587.734,360.706) scale(0.947693,-0.6712) translate(32.0472,-903.042)" width="15" x="580.6265329866395" xlink:href="#Disconnector:刀闸_0" y="350.6376721325851" zvalue="9173"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454433046531" ObjectName="#2主变110kV侧1022隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454433046531"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,587.734,360.706) scale(0.947693,-0.6712) translate(32.0472,-903.042)" width="15" x="580.6265329866395" y="350.6376721325851"/></g>
  <g id="549">
   <use class="kv10" height="30" transform="rotate(180,1396.57,829.224) scale(-0.947693,0.6712) translate(-2870.62,401.278)" width="15" x="1389.464536768915" xlink:href="#Disconnector:刀闸_0" y="819.1564097455977" zvalue="9390"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454433505283" ObjectName="10kV景罕镇线0336隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454433505283"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1396.57,829.224) scale(-0.947693,0.6712) translate(-2870.62,401.278)" width="15" x="1389.464536768915" y="819.1564097455977"/></g>
  <g id="815">
   <use class="kv110" height="30" transform="rotate(0,518.889,176.921) scale(-0.947693,0.6712) translate(-1066.81,81.736)" width="15" x="511.7812394740956" xlink:href="#Disconnector:刀闸_0" y="166.8531260541915" zvalue="9723"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454434291715" ObjectName="110kV瑞景线1736隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454434291715"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,518.889,176.921) scale(-0.947693,0.6712) translate(-1066.81,81.736)" width="15" x="511.7812394740956" y="166.8531260541915"/></g>
  <g id="811">
   <use class="kv110" height="30" transform="rotate(180,519.094,308.19) scale(0.947693,-0.6712) translate(28.2587,-772.284)" width="15" x="511.9867244064055" xlink:href="#Disconnector:刀闸_0" y="298.1218638471603" zvalue="9728"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454434095107" ObjectName="110kV瑞景线1732隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454434095107"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,519.094,308.19) scale(0.947693,-0.6712) translate(28.2587,-772.284)" width="15" x="511.9867244064055" y="298.1218638471603"/></g>
  <g id="995">
   <use class="kv10" height="30" transform="rotate(180,906.287,829.224) scale(-0.947693,0.6712) translate(-1862.99,401.278)" width="15" x="899.1788224832005" xlink:href="#Disconnector:刀闸_0" y="819.1564097455976" zvalue="9862"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454478462979" ObjectName="10kV2号电容器0526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454478462979"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,906.287,829.224) scale(-0.947693,0.6712) translate(-1862.99,401.278)" width="15" x="899.1788224832005" y="819.1564097455976"/></g>
  <g id="980">
   <use class="kv10" height="30" transform="rotate(180,754.287,829.224) scale(-0.947693,0.6712) translate(-1550.6,401.278)" width="15" x="747.1788224832006" xlink:href="#Disconnector:刀闸_0" y="819.1564097455976" zvalue="9882"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454437306371" ObjectName="10kV景罕糖厂Ⅰ回线0546隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454437306371"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,754.287,829.224) scale(-0.947693,0.6712) translate(-1550.6,401.278)" width="15" x="747.1788224832006" y="819.1564097455976"/></g>
  <g id="934">
   <use class="kv10" height="30" transform="rotate(180,678.287,829.224) scale(-0.947693,0.6712) translate(-1394.4,401.278)" width="15" x="671.1788224832006" xlink:href="#Disconnector:刀闸_0" y="819.1564025878906" zvalue="9923"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450170978309" ObjectName="10kV弄贯线0556隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450170978309"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,678.287,829.224) scale(-0.947693,0.6712) translate(-1394.4,401.278)" width="15" x="671.1788224832006" y="819.1564025878906"/></g>
  <g id="37">
   <use class="kv35" height="30" transform="rotate(270,1808.58,164.806) scale(-0.947693,0.6712) translate(-3717.38,75.801)" width="15" x="1801.476341319625" xlink:href="#Disconnector:刀闸_0" y="154.7375390514904" zvalue="10005"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454437830659" ObjectName="35kV景勐线3726隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454437830659"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1808.58,164.806) scale(-0.947693,0.6712) translate(-3717.38,75.801)" width="15" x="1801.476341319625" y="154.7375390514904"/></g>
  <g id="177">
   <use class="kv35" height="30" transform="rotate(270,1808.58,288.806) scale(-0.947693,0.6712) translate(-3717.38,136.545)" width="15" x="1801.476341319625" xlink:href="#Disconnector:刀闸_0" y="278.7375390514902" zvalue="10027"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454438158339" ObjectName="35kV景陇线3716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454438158339"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1808.58,288.806) scale(-0.947693,0.6712) translate(-3717.38,136.545)" width="15" x="1801.476341319625" y="278.7375390514902"/></g>
  <g id="258">
   <use class="kv35" height="26" transform="rotate(90,1638.2,349.833) scale(-1.06265,1.4877) translate(-3179.39,-108.343)" width="14" x="1630.765386606438" xlink:href="#Disconnector:联体手车刀闸_0" y="330.4931748410218" zvalue="10077"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454438617091" ObjectName="35kV分段3121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454438617091"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1638.2,349.833) scale(-1.06265,1.4877) translate(-3179.39,-108.343)" width="14" x="1630.765386606438" y="330.4931748410218"/></g>
  <g id="326">
   <use class="kv35" height="30" transform="rotate(270,1809.7,441.917) scale(-0.947693,0.6712) translate(-3719.67,211.549)" width="15" x="1802.587452430736" xlink:href="#Disconnector:刀闸_0" y="431.8486501626015" zvalue="10093"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454438813699" ObjectName="35kV景安线3816隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454438813699"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1809.7,441.917) scale(-0.947693,0.6712) translate(-3719.67,211.549)" width="15" x="1802.587452430736" y="431.8486501626015"/></g>
  <g id="425">
   <use class="kv35" height="30" transform="rotate(270,1809.7,565.917) scale(-0.947693,0.6712) translate(-3719.67,272.292)" width="15" x="1802.587452430736" xlink:href="#Disconnector:刀闸_0" y="555.8486501626015" zvalue="10116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454439272451" ObjectName="35kV景城线3826隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454439272451"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1809.7,565.917) scale(-0.947693,0.6712) translate(-3719.67,272.292)" width="15" x="1802.587452430736" y="555.8486501626015"/></g>
  <g id="269">
   <use class="kv110" height="30" transform="rotate(0,854.44,298.051) scale(-0.947693,-0.6712) translate(-1756.43,-747.039)" width="15" x="847.3325872987576" xlink:href="#Disconnector:刀闸_0" y="287.9827411887661" zvalue="10291"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454440386563" ObjectName="110kV分段1122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454440386563"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,854.44,298.051) scale(-0.947693,-0.6712) translate(-1756.43,-747.039)" width="15" x="847.3325872987576" y="287.9827411887661"/></g>
  <g id="265">
   <use class="kv110" height="30" transform="rotate(0,976.003,299.162) scale(-0.947693,-0.6712) translate(-2006.27,-749.806)" width="15" x="968.8956328004665" xlink:href="#Disconnector:刀闸_0" y="289.0938522998771" zvalue="10293"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454440321027" ObjectName="110kV分段1121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454440321027"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,976.003,299.162) scale(-0.947693,-0.6712) translate(-2006.27,-749.806)" width="15" x="968.8956328004665" y="289.0938522998771"/></g>
  <g id="295">
   <use class="kv10" height="26" transform="rotate(0,1165.28,742.991) scale(1.06265,-1.4877) translate(-68.2579,-1236.07)" width="14" x="1157.8372407202" xlink:href="#Disconnector:联体手车刀闸_0" y="723.6504329004329" zvalue="10313"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454440910851" ObjectName="10kV分段0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454440910851"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1165.28,742.991) scale(1.06265,-1.4877) translate(-68.2579,-1236.07)" width="14" x="1157.8372407202" y="723.6504329004329"/></g>
  <g id="318">
   <use class="kv10" height="30" transform="rotate(0,1315.27,843.177) scale(-0.947693,0.6712) translate(-2703.54,408.113)" width="15" x="1308.166917721296" xlink:href="#Disconnector:刀闸_0" y="833.1087775106337" zvalue="10328"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454440648707" ObjectName="10kV1号电容器0326隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454440648707"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1315.27,843.177) scale(-0.947693,0.6712) translate(-2703.54,408.113)" width="15" x="1308.166917721296" y="833.1087775106337"/></g>
  <g id="350">
   <use class="kv10" height="26" transform="rotate(0,1432.2,642.161) scale(1.06265,-1.4877) translate(-83.9938,-1067.47)" width="14" x="1424.761708600214" xlink:href="#Disconnector:联体手车刀闸_0" y="622.8210678210678" zvalue="10358"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454440976387" ObjectName="10kVⅠ母电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454440976387"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1432.2,642.161) scale(1.06265,-1.4877) translate(-83.9938,-1067.47)" width="14" x="1424.761708600214" y="622.8210678210678"/></g>
  <g id="377">
   <use class="kv10" height="26" transform="rotate(0,782.561,641.812) scale(1.06265,-1.4877) translate(-45.6957,-1066.88)" width="14" x="775.1229550059145" xlink:href="#Disconnector:联体手车刀闸_0" y="622.4718614718613" zvalue="10364"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454441172995" ObjectName="10kVⅡ母电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454441172995"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,782.561,641.812) scale(1.06265,-1.4877) translate(-45.6957,-1066.88)" width="14" x="775.1229550059145" y="622.4718614718613"/></g>
  <g id="417">
   <use class="kv35" height="26" transform="rotate(270,1638.2,233.526) scale(1.06265,-1.4877) translate(-96.1384,-384.157)" width="14" x="1630.76538619695" xlink:href="#Disconnector:联体手车刀闸_0" y="214.1861471861471" zvalue="10398"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454441697283" ObjectName="35kVⅠ母电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454441697283"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1638.2,233.526) scale(1.06265,-1.4877) translate(-96.1384,-384.157)" width="14" x="1630.76538619695" y="214.1861471861471"/></g>
  <g id="426">
   <use class="kv35" height="26" transform="rotate(270,1638.2,529.241) scale(1.06265,-1.4877) translate(-96.1384,-878.644)" width="14" x="1630.765385992591" xlink:href="#Disconnector:联体手车刀闸_0" y="509.9004329004327" zvalue="10404"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454441762819" ObjectName="35kVⅡ母电压互感器3902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454441762819"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1638.2,529.241) scale(1.06265,-1.4877) translate(-96.1384,-878.644)" width="14" x="1630.765385992591" y="509.9004329004327"/></g>
  <g id="429">
   <use class="kv35" height="30" transform="rotate(270,877.409,495.631) scale(-0.947693,0.6712) translate(-1803.64,237.862)" width="15" x="870.3017381450213" xlink:href="#Disconnector:刀闸_0" y="485.5629358768871" zvalue="10410"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454441893891" ObjectName="#2主变35kV侧3020隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454441893891"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,877.409,495.631) scale(-0.947693,0.6712) translate(-1803.64,237.862)" width="15" x="870.3017381450213" y="485.5629358768871"/></g>
  <g id="432">
   <use class="kv35" height="30" transform="rotate(270,963.409,495.631) scale(-0.947693,0.6712) translate(-1980.39,237.862)" width="15" x="956.3017381450213" xlink:href="#Disconnector:刀闸_0" y="485.5629358768871" zvalue="10414"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454441959427" ObjectName="#1主变35kV侧3010隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454441959427"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,963.409,495.631) scale(-0.947693,0.6712) translate(-1980.39,237.862)" width="15" x="956.3017381450213" y="485.5629358768871"/></g>
  <g id="446">
   <use class="kv10" height="30" transform="rotate(180,421.472,892.78) scale(-0.947693,0.6712) translate(-866.6,432.412)" width="15" x="414.3646729807989" xlink:href="#Disconnector:刀闸_0" y="882.711975097656" zvalue="10423"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454478725123" ObjectName="10kV2号接地变0020接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454478725123"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,421.472,892.78) scale(-0.947693,0.6712) translate(-866.6,432.412)" width="15" x="414.3646729807989" y="882.711975097656"/></g>
  <g id="457">
   <use class="kv10" height="30" transform="rotate(180,1664.28,892.78) scale(-0.947693,0.6712) translate(-3420.81,432.412)" width="15" x="1657.174196790323" xlink:href="#Disconnector:刀闸_0" y="882.7119750976562" zvalue="10429"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454478790659" ObjectName="10kV1号接地变0010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454478790659"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1664.28,892.78) scale(-0.947693,0.6712) translate(-3420.81,432.412)" width="15" x="1657.174196790323" y="882.7119750976562"/></g>
  <g id="473">
   <use class="kv10" height="30" transform="rotate(180,828.287,829.224) scale(-0.947693,0.6712) translate(-1702.68,401.278)" width="15" x="821.1788224832006" xlink:href="#Disconnector:刀闸_0" y="819.1564097455976" zvalue="10520"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454476627971" ObjectName="10kV陇川机场Ⅰ回线0536隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454476627971"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,828.287,829.224) scale(-0.947693,0.6712) translate(-1702.68,401.278)" width="15" x="821.1788224832006" y="819.1564097455976"/></g>
  <g id="614">
   <use class="kv10" height="30" transform="rotate(180,600.203,829.224) scale(-0.947693,0.6712) translate(-1233.93,401.278)" width="15" x="593.0956367937329" xlink:href="#Disconnector:刀闸_0" y="819.1564025930402" zvalue="10935"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450171830277" ObjectName="10kV广母线0566隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450171830277"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,600.203,829.224) scale(-0.947693,0.6712) translate(-1233.93,401.278)" width="15" x="593.0956367937329" y="819.1564025930402"/></g>
  <g id="680">
   <use class="kv110" height="30" transform="rotate(0,1175.4,177.776) scale(-0.947693,0.6712) translate(-2416.07,82.1546)" width="15" x="1168.294147727273" xlink:href="#Disconnector:刀闸_0" y="167.7075869774109" zvalue="10977"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450346745861" ObjectName="110kV景琪线1716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450346745861"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1175.4,177.776) scale(-0.947693,0.6712) translate(-2416.07,82.1546)" width="15" x="1168.294147727273" y="167.7075869774109"/></g>
  <g id="677">
   <use class="kv110" height="30" transform="rotate(180,1175.39,309.044) scale(0.947693,-0.6712) translate(64.4823,-774.412)" width="15" x="1168.282551072511" xlink:href="#Disconnector:刀闸_0" y="298.9763247703797" zvalue="10982"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450346549253" ObjectName="110kV景琪线1711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450346549253"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1175.39,309.044) scale(0.947693,-0.6712) translate(64.4823,-774.412)" width="15" x="1168.282551072511" y="298.9763247703797"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="1427">
   <use class="kv110" height="20" transform="rotate(90,1092.63,142.648) scale(1.24619,-1.0068) translate(-214.621,-284.264)" width="10" x="1086.401175516957" xlink:href="#GroundDisconnector:地刀_0" y="132.579841142082" zvalue="7520"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450334883845" ObjectName="110kV凤景线17267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450334883845"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1092.63,142.648) scale(1.24619,-1.0068) translate(-214.621,-284.264)" width="10" x="1086.401175516957" y="132.579841142082"/></g>
  <g id="40">
   <use class="kv110" height="20" transform="rotate(90,1092.63,205.894) scale(1.24619,-1.0068) translate(-214.621,-410.329)" width="10" x="1086.401175516957" xlink:href="#GroundDisconnector:地刀_0" y="195.825629716301" zvalue="7568"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450335145989" ObjectName="110kV凤景线17260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450335145989"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1092.63,205.894) scale(1.24619,-1.0068) translate(-214.621,-410.329)" width="10" x="1086.401175516957" y="195.825629716301"/></g>
  <g id="43">
   <use class="kv110" height="20" transform="rotate(90,1092.63,277.934) scale(1.24619,-1.0068) translate(-214.621,-553.922)" width="10" x="1086.401175516957" xlink:href="#GroundDisconnector:地刀_0" y="267.8655619672775" zvalue="7572"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450335277061" ObjectName="110kV凤景线17217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450335277061"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1092.63,277.934) scale(1.24619,-1.0068) translate(-214.621,-553.922)" width="10" x="1086.401175516957" y="267.8655619672775"/></g>
  <g id="14">
   <use class="kv110" height="40" transform="rotate(0,1180.06,585.7) scale(0.727779,-1.1) translate(435.951,-1116.15)" width="40" x="1165.507895784728" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="563.7001842006663" zvalue="7586"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454431277059" ObjectName="#1主变中性点1010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454431277059"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1180.06,585.7) scale(0.727779,-1.1) translate(435.951,-1116.15)" width="40" x="1165.507895784728" y="563.7001842006663"/></g>
  <g id="268">
   <use class="kv110" height="20" transform="rotate(270,1260.64,431.004) scale(-1.24619,1.0068) translate(-2271.01,-2.84329)" width="10" x="1254.408270047699" xlink:href="#GroundDisconnector:地刀_0" y="420.9355716269417" zvalue="7818"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454476890115" ObjectName="#1主变110kV侧10160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454476890115"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1260.64,431.004) scale(-1.24619,1.0068) translate(-2271.01,-2.84329)" width="10" x="1254.408270047699" y="420.9355716269417"/></g>
  <g id="267">
   <use class="kv110" height="20" transform="rotate(270,1260.64,381.248) scale(-1.24619,1.0068) translate(-2271.01,-2.50721)" width="10" x="1254.408284240342" xlink:href="#GroundDisconnector:地刀_0" y="371.179687897937" zvalue="7820"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454476759043" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454476759043"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1260.64,381.248) scale(-1.24619,1.0068) translate(-2271.01,-2.50721)" width="10" x="1254.408284240342" y="371.179687897937"/></g>
  <g id="412">
   <use class="kv110" height="40" transform="rotate(0,542.778,585.7) scale(0.727779,-1.1) translate(197.578,-1116.15)" width="40" x="528.2221814990135" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="563.7001838684082" zvalue="7955"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454431801347" ObjectName="#2主变中性点1020接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454431801347"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,542.778,585.7) scale(0.727779,-1.1) translate(197.578,-1116.15)" width="40" x="528.2221814990135" y="563.7001838684082"/></g>
  <g id="150">
   <use class="kv110" height="20" transform="rotate(90,1356.74,309.203) scale(1.24619,-1.0068) translate(-266.796,-616.25)" width="10" x="1350.51229001891" xlink:href="#GroundDisconnector:地刀_0" y="299.1354034574077" zvalue="8032"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454477152259" ObjectName="110kVⅠ母电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454477152259"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1356.74,309.203) scale(1.24619,-1.0068) translate(-266.796,-616.25)" width="10" x="1350.51229001891" y="299.1354034574077"/></g>
  <g id="170">
   <use class="kv110" height="20" transform="rotate(90,1356.74,257.203) scale(1.24619,-1.0068) translate(-266.796,-512.601)" width="10" x="1350.51229001891" xlink:href="#GroundDisconnector:地刀_0" y="247.1354034574077" zvalue="8037"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454477021187" ObjectName="110kVⅠ母电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454477021187"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1356.74,257.203) scale(1.24619,-1.0068) translate(-266.796,-512.601)" width="10" x="1350.51229001891" y="247.1354034574077"/></g>
  <g id="116">
   <use class="kv110" height="20" transform="rotate(90,710.458,309.203) scale(1.24619,-1.0068) translate(-139.121,-616.25)" width="10" x="704.2265757331957" xlink:href="#GroundDisconnector:地刀_0" y="299.1354034574077" zvalue="8861"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454477479939" ObjectName="110kVⅡ母电压互感器19020接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454477479939"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,710.458,309.203) scale(1.24619,-1.0068) translate(-139.121,-616.25)" width="10" x="704.2265757331957" y="299.1354034574077"/></g>
  <g id="112">
   <use class="kv110" height="20" transform="rotate(90,710.458,258.453) scale(1.24619,-1.0068) translate(-139.121,-515.093)" width="10" x="704.2265757331957" xlink:href="#GroundDisconnector:地刀_0" y="248.3854034574077" zvalue="8865"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454477348867" ObjectName="110kVⅡ母电压互感器19027接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454477348867"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,710.458,258.453) scale(1.24619,-1.0068) translate(-139.121,-515.093)" width="10" x="704.2265757331957" y="248.3854034574077"/></g>
  <g id="266">
   <use class="kv35" height="20" transform="rotate(180,1392.31,117.207) scale(1.24619,1.0068) translate(-273.824,-0.723691)" width="10" x="1386.083333333333" xlink:href="#GroundDisconnector:地刀_0" y="107.138888888889" zvalue="8910"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454432653315" ObjectName="#1主变35kV侧30167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454432653315"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1392.31,117.207) scale(1.24619,1.0068) translate(-273.824,-0.723691)" width="10" x="1386.083333333333" y="107.138888888889"/></g>
  <g id="508">
   <use class="kv110" height="20" transform="rotate(270,618.353,429.248) scale(-1.24619,1.0068) translate(-1113.32,-2.83143)" width="10" x="612.122555761985" xlink:href="#GroundDisconnector:地刀_0" y="419.179687897937" zvalue="9175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454432980995" ObjectName="#2主变110kV侧10260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454432980995"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,618.353,429.248) scale(-1.24619,1.0068) translate(-1113.32,-2.83143)" width="10" x="612.122555761985" y="419.179687897937"/></g>
  <g id="507">
   <use class="kv110" height="20" transform="rotate(270,618.354,381.248) scale(-1.24619,1.0068) translate(-1113.32,-2.50721)" width="10" x="612.1225699546278" xlink:href="#GroundDisconnector:地刀_0" y="371.179687897937" zvalue="9177"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454432849923" ObjectName="#2主变110kV侧10227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454432849923"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,618.354,381.248) scale(-1.24619,1.0068) translate(-1113.32,-2.50721)" width="10" x="612.1225699546278" y="371.179687897937"/></g>
  <g id="548">
   <use class="kv10" height="20" transform="rotate(0,1371.86,802.498) scale(-1.24619,1.0068) translate(-2471.47,-5.35262)" width="10" x="1365.626395152603" xlink:href="#GroundDisconnector:地刀_0" y="792.4296945736572" zvalue="9392"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454433439747" ObjectName="10kV景罕镇线03317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454433439747"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1371.86,802.498) scale(-1.24619,1.0068) translate(-2471.47,-5.35262)" width="10" x="1365.626395152603" y="792.4296945736572"/></g>
  <g id="813">
   <use class="kv110" height="20" transform="rotate(90,546.346,142.648) scale(1.24619,-1.0068) translate(-106.701,-284.264)" width="10" x="540.1154612312424" xlink:href="#GroundDisconnector:地刀_0" y="132.5798411420819" zvalue="9725"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454434226179" ObjectName="110kV瑞景线17367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454434226179"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,546.346,142.648) scale(1.24619,-1.0068) translate(-106.701,-284.264)" width="10" x="540.1154612312424" y="132.5798411420819"/></g>
  <g id="644">
   <use class="kv110" height="20" transform="rotate(90,546.346,205.894) scale(1.24619,-1.0068) translate(-106.701,-410.329)" width="10" x="540.1154612312424" xlink:href="#GroundDisconnector:地刀_0" y="195.825629716301" zvalue="9731"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454434029571" ObjectName="110kV瑞景线17360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454434029571"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,546.346,205.894) scale(1.24619,-1.0068) translate(-106.701,-410.329)" width="10" x="540.1154612312424" y="195.825629716301"/></g>
  <g id="636">
   <use class="kv110" height="20" transform="rotate(90,546.346,277.934) scale(1.24619,-1.0068) translate(-106.701,-553.922)" width="10" x="540.1154612312424" xlink:href="#GroundDisconnector:地刀_0" y="267.8655619672775" zvalue="9734"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454477676547" ObjectName="110kV瑞景线17327接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454477676547"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,546.346,277.934) scale(1.24619,-1.0068) translate(-106.701,-553.922)" width="10" x="540.1154612312424" y="267.8655619672775"/></g>
  <g id="822">
   <use class="kv110" height="20" transform="rotate(270,1260.64,465.248) scale(-1.24619,1.0068) translate(-2271.01,-3.0746)" width="10" x="1254.408270047699" xlink:href="#GroundDisconnector:地刀_0" y="455.179687897937" zvalue="9745"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454477807619" ObjectName="#1主变110kV侧10167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454477807619"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1260.64,465.248) scale(-1.24619,1.0068) translate(-2271.01,-3.0746)" width="10" x="1254.408270047699" y="455.179687897937"/></g>
  <g id="824">
   <use class="kv110" height="20" transform="rotate(270,619.603,463.998) scale(-1.24619,1.0068) translate(-1115.57,-3.06616)" width="10" x="613.3725557619852" xlink:href="#GroundDisconnector:地刀_0" y="453.929687897937" zvalue="9748"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454434619395" ObjectName="#2主变110kV侧10267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454434619395"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,619.603,463.998) scale(-1.24619,1.0068) translate(-1115.57,-3.06616)" width="10" x="613.3725557619852" y="453.929687897937"/></g>
  <g id="34">
   <use class="kv10" height="20" transform="rotate(0,1217.04,802.498) scale(-1.24619,1.0068) translate(-2192.42,-5.35262)" width="10" x="1210.808213334422" xlink:href="#GroundDisconnector:地刀_0" y="792.4296946577073" zvalue="9762"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454434881539" ObjectName="10kV1号站用变03117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454434881539"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1217.04,802.498) scale(-1.24619,1.0068) translate(-2192.42,-5.35262)" width="10" x="1210.808213334422" y="792.4296946577073"/></g>
  <g id="93">
   <use class="kv10" height="20" transform="rotate(0,1455.86,802.498) scale(-1.24619,1.0068) translate(-2622.88,-5.35262)" width="10" x="1449.626395152603" xlink:href="#GroundDisconnector:地刀_0" y="792.4296945736572" zvalue="9806"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450170519557" ObjectName="10kV弄冒线03517接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450170519557"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1455.86,802.498) scale(-1.24619,1.0068) translate(-2622.88,-5.35262)" width="10" x="1449.626395152603" y="792.4296945736572"/></g>
  <g id="833">
   <use class="kv10" height="20" transform="rotate(0,1535.86,802.498) scale(-1.24619,1.0068) translate(-2767.07,-5.35262)" width="10" x="1529.626395152603" xlink:href="#GroundDisconnector:地刀_0" y="792.4296945736572" zvalue="9827"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454435799043" ObjectName="10kV芒面线03617接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454435799043"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1535.86,802.498) scale(-1.24619,1.0068) translate(-2767.07,-5.35262)" width="10" x="1529.626395152603" y="792.4296945736572"/></g>
  <g id="855">
   <use class="kv10" height="20" transform="rotate(0,1610.86,802.498) scale(-1.24619,1.0068) translate(-2902.26,-5.35262)" width="10" x="1604.626395152603" xlink:href="#GroundDisconnector:地刀_0" y="792.4296946577073" zvalue="9846"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454436061187" ObjectName="10kV1号接地变03817接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454436061187"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1610.86,802.498) scale(-1.24619,1.0068) translate(-2902.26,-5.35262)" width="10" x="1604.626395152603" y="792.4296946577073"/></g>
  <g id="994">
   <use class="kv10" height="20" transform="rotate(0,881.572,802.498) scale(-1.24619,1.0068) translate(-1587.76,-5.35262)" width="10" x="875.3406808668892" xlink:href="#GroundDisconnector:地刀_0" y="792.4296946577072" zvalue="9864"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454478397443" ObjectName="10kV2号电容器05227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454478397443"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,881.572,802.498) scale(-1.24619,1.0068) translate(-1587.76,-5.35262)" width="10" x="875.3406808668892" y="792.4296946577072"/></g>
  <g id="979">
   <use class="kv10" height="20" transform="rotate(0,729.572,802.498) scale(-1.24619,1.0068) translate(-1313.78,-5.35262)" width="10" x="723.3406808668892" xlink:href="#GroundDisconnector:地刀_0" y="792.4296945736571" zvalue="9884"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454437240835" ObjectName="10kV景罕糖厂Ⅰ回线05427接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454437240835"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,729.572,802.498) scale(-1.24619,1.0068) translate(-1313.78,-5.35262)" width="10" x="723.3406808668892" y="792.4296945736571"/></g>
  <g id="963">
   <use class="kv10" height="20" transform="rotate(0,956.572,806.498) scale(-1.24619,1.0068) translate(-1722.94,-5.37964)" width="10" x="950.3406808668893" xlink:href="#GroundDisconnector:地刀_0" y="796.4296946577072" zvalue="9900"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454436978691" ObjectName="10kV2号站用变05127接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454436978691"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,956.572,806.498) scale(-1.24619,1.0068) translate(-1722.94,-5.37964)" width="10" x="950.3406808668893" y="796.4296946577072"/></g>
  <g id="933">
   <use class="kv10" height="20" transform="rotate(0,657.572,802.498) scale(-1.24619,1.0068) translate(-1184.01,-5.35262)" width="10" x="651.3406808668892" xlink:href="#GroundDisconnector:地刀_0" y="792.4296945736571" zvalue="9925"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450170912773" ObjectName="10kV弄贯线05527接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450170912773"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,657.572,802.498) scale(-1.24619,1.0068) translate(-1184.01,-5.35262)" width="10" x="651.3406808668892" y="792.4296945736571"/></g>
  <g id="899">
   <use class="kv10" height="20" transform="rotate(0,424.572,802.498) scale(-1.24619,1.0068) translate(-764.037,-5.35262)" width="10" x="418.3406808668892" xlink:href="#GroundDisconnector:地刀_0" y="792.4296946577072" zvalue="9963"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454478987267" ObjectName="10kV2号接地变05827接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454478987267"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,424.572,802.498) scale(-1.24619,1.0068) translate(-764.037,-5.35262)" width="10" x="418.3406808668892" y="792.4296946577072"/></g>
  <g id="35">
   <use class="kv35" height="20" transform="rotate(180,1764.09,196.203) scale(1.24619,-1.0068) translate(-347.268,-391.014)" width="10" x="1757.85809738489" xlink:href="#GroundDisconnector:地刀_0" y="186.1354034574077" zvalue="10003"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454437765123" ObjectName="35kV景勐线37217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454437765123"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1764.09,196.203) scale(1.24619,-1.0068) translate(-347.268,-391.014)" width="10" x="1757.85809738489" y="186.1354034574077"/></g>
  <g id="181">
   <use class="kv35" height="20" transform="rotate(180,1764.09,320.203) scale(1.24619,-1.0068) translate(-347.268,-638.176)" width="10" x="1757.85809738489" xlink:href="#GroundDisconnector:地刀_0" y="310.1354034574076" zvalue="10025"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454438289411" ObjectName="35kV景陇线37117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454438289411"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1764.09,320.203) scale(1.24619,-1.0068) translate(-347.268,-638.176)" width="10" x="1757.85809738489" y="310.1354034574076"/></g>
  <g id="228">
   <use class="kv35" height="20" transform="rotate(0,1555.56,640.957) scale(1.24619,-1.0068) translate(-306.074,-1277.52)" width="10" x="1549.333333333333" xlink:href="#GroundDisconnector:地刀_0" y="630.8888888888889" zvalue="10057"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454438551555" ObjectName="#2主变35kV侧30267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454438551555"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1555.56,640.957) scale(1.24619,-1.0068) translate(-306.074,-1277.52)" width="10" x="1549.333333333333" y="630.8888888888889"/></g>
  <g id="330">
   <use class="kv35" height="20" transform="rotate(180,1764.09,472.203) scale(1.24619,-1.0068) translate(-347.268,-941.149)" width="10" x="1757.85809738489" xlink:href="#GroundDisconnector:地刀_0" y="462.1354034574077" zvalue="10091"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454477938691" ObjectName="35kV景安线38127接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454477938691"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1764.09,472.203) scale(1.24619,-1.0068) translate(-347.268,-941.149)" width="10" x="1757.85809738489" y="462.1354034574077"/></g>
  <g id="443">
   <use class="kv35" height="20" transform="rotate(180,1764.09,596.203) scale(1.24619,-1.0068) translate(-347.268,-1188.31)" width="10" x="1757.85809738489" xlink:href="#GroundDisconnector:地刀_0" y="586.1354034574076" zvalue="10114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454478069763" ObjectName="35kV景城线38227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454478069763"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1764.09,596.203) scale(1.24619,-1.0068) translate(-347.268,-1188.31)" width="10" x="1757.85809738489" y="586.1354034574076"/></g>
  <g id="523">
   <use class="kv35" height="20" transform="rotate(180,1764.09,740.203) scale(1.24619,-1.0068) translate(-347.268,-1475.34)" width="10" x="1757.85809738489" xlink:href="#GroundDisconnector:地刀_0" y="730.1354034574076" zvalue="10137"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454478200835" ObjectName="35kV章景线38327接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454478200835"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1764.09,740.203) scale(1.24619,-1.0068) translate(-347.268,-1475.34)" width="10" x="1757.85809738489" y="730.1354034574076"/></g>
  <g id="264">
   <use class="kv110" height="20" transform="rotate(180,976.008,253.743) scale(1.24619,1.0068) translate(-191.581,-1.64595)" width="10" x="969.776740304922" xlink:href="#GroundDisconnector:地刀_0" y="243.6749179117078" zvalue="10295"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454440255491" ObjectName="110kV分段11217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454440255491"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,976.008,253.743) scale(1.24619,1.0068) translate(-191.581,-1.64595)" width="10" x="969.776740304922" y="243.6749179117078"/></g>
  <g id="260">
   <use class="kv110" height="20" transform="rotate(0,854.445,253.743) scale(-1.24619,-1.0068) translate(-1538.86,-505.704)" width="10" x="848.213694803213" xlink:href="#GroundDisconnector:地刀_0" y="243.6749174702983" zvalue="10297"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454440124419" ObjectName="110kV分段11227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454440124419"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,854.445,253.743) scale(-1.24619,-1.0068) translate(-1538.86,-505.704)" width="10" x="848.213694803213" y="243.6749174702983"/></g>
  <g id="323">
   <use class="kv10" height="20" transform="rotate(0,1293.95,805.38) scale(1.625,1.38576) translate(-494.547,-220.338)" width="10" x="1285.821428571429" xlink:href="#GroundDisconnector:地刀_0" y="791.5226086584692" zvalue="10321"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454440779779" ObjectName="10kV1号电容器03217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454440779779"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1293.95,805.38) scale(1.625,1.38576) translate(-494.547,-220.338)" width="10" x="1285.821428571429" y="791.5226086584692"/></g>
  <g id="312">
   <use class="kv10" height="25" transform="rotate(180,1299.61,881.426) scale(1.25,1.25) translate(-257.421,-173.16)" width="20" x="1287.105756298607" xlink:href="#GroundDisconnector:接地刀_0" y="865.8009296785925" zvalue="10337"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454440517635" ObjectName="10kV1号电容器03210接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454440517635"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(180,1299.61,881.426) scale(1.25,1.25) translate(-257.421,-173.16)" width="20" x="1287.105756298607" y="865.8009296785925"/></g>
  <g id="398">
   <use class="kv10" height="25" transform="rotate(180,890.606,883.786) scale(1.25,1.25) translate(-175.621,-173.632)" width="20" x="878.1057562986072" xlink:href="#GroundDisconnector:接地刀_0" y="868.1607142857142" zvalue="10383"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454441500675" ObjectName="10kV2号电容器05210接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454441500675"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(180,890.606,883.786) scale(1.25,1.25) translate(-175.621,-173.632)" width="20" x="878.1057562986072" y="868.1607142857142"/></g>
  <g id="472">
   <use class="kv10" height="20" transform="rotate(0,803.572,802.498) scale(-1.24619,1.0068) translate(-1447.17,-5.35262)" width="10" x="797.3406808668892" xlink:href="#GroundDisconnector:地刀_0" y="792.4296945736571" zvalue="10522"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454476562435" ObjectName="10kV陇川机场Ⅰ回线05327接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454476562435"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,803.572,802.498) scale(-1.24619,1.0068) translate(-1447.17,-5.35262)" width="10" x="797.3406808668892" y="792.4296945736571"/></g>
  <g id="625">
   <use class="kv10" height="20" transform="rotate(0,498.572,802.498) scale(-1.24619,1.0068) translate(-897.418,-5.35262)" width="10" x="492.3406808668892" xlink:href="#GroundDisconnector:地刀_0" y="792.4296945736571" zvalue="10881"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450171305989" ObjectName="10kV芒洪线05727接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450171305989"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,498.572,802.498) scale(-1.24619,1.0068) translate(-897.418,-5.35262)" width="10" x="492.3406808668892" y="792.4296945736571"/></g>
  <g id="648">
   <use class="kv10" height="20" transform="rotate(0,579.572,802.498) scale(-1.24619,1.0068) translate(-1043.42,-5.35262)" width="10" x="573.3406808668892" xlink:href="#GroundDisconnector:地刀_0" y="792.4296945736571" zvalue="10898"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450171699205" ObjectName="10kV广母线05627接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450171699205"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,579.572,802.498) scale(-1.24619,1.0068) translate(-1043.42,-5.35262)" width="10" x="573.3406808668892" y="792.4296945736571"/></g>
  <g id="679">
   <use class="kv110" height="20" transform="rotate(90,1202.86,143.502) scale(1.24619,-1.0068) translate(-236.396,-285.967)" width="10" x="1196.62836948442" xlink:href="#GroundDisconnector:地刀_0" y="133.4343020653013" zvalue="10979"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450346680325" ObjectName="110kV景琪线17167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450346680325"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1202.86,143.502) scale(1.24619,-1.0068) translate(-236.396,-285.967)" width="10" x="1196.62836948442" y="133.4343020653013"/></g>
  <g id="676">
   <use class="kv110" height="20" transform="rotate(90,1202.86,206.748) scale(1.24619,-1.0068) translate(-236.396,-412.032)" width="10" x="1196.62836948442" xlink:href="#GroundDisconnector:地刀_0" y="196.6800906395204" zvalue="10984"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450346483717" ObjectName="110kV景琪线17160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450346483717"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1202.86,206.748) scale(1.24619,-1.0068) translate(-236.396,-412.032)" width="10" x="1196.62836948442" y="196.6800906395204"/></g>
  <g id="674">
   <use class="kv110" height="20" transform="rotate(90,1202.86,278.788) scale(1.24619,-1.0068) translate(-236.396,-555.625)" width="10" x="1196.62836948442" xlink:href="#GroundDisconnector:地刀_0" y="268.7200228904969" zvalue="10987"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450346352645" ObjectName="110kV景琪线17117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450346352645"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1202.86,278.788) scale(1.24619,-1.0068) translate(-236.396,-555.625)" width="10" x="1196.62836948442" y="268.7200228904969"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="18">
   <path class="kv110" d="M 1065.12 227.4 L 1065.12 186.82" stroke-width="1" zvalue="7523"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1429@0" LinkObjectIDznd="1428@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1065.12 227.4 L 1065.12 186.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv110" d="M 1082.82 205.96 L 1065.12 205.96" stroke-width="1" zvalue="7570"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="18" MaxPinNum="2"/>
   </metadata>
  <path d="M 1082.82 205.96 L 1065.12 205.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="kv110" d="M 1065.11 318.09 L 1065.11 336.42" stroke-width="1" zvalue="7582"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1397@1" LinkObjectIDznd="48@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1065.11 318.09 L 1065.11 336.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="273">
   <path class="kv110" d="M 1234.26 419.5 L 1234.26 438.08" stroke-width="1" zvalue="7811"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="275@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.26 419.5 L 1234.26 438.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="263">
   <path class="kv110" d="M 1234.26 350.81 L 1234.26 336.42" stroke-width="1" zvalue="7825"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@1" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.26 350.81 L 1234.26 336.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="262">
   <path class="kv110" d="M 1250.82 381.31 L 1234.1 381.31" stroke-width="1" zvalue="7826"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@0" LinkObjectIDznd="130" MaxPinNum="2"/>
   </metadata>
  <path d="M 1250.82 381.31 L 1234.1 381.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="261">
   <path class="kv110" d="M 1250.82 431.07 L 1234.26 431.07" stroke-width="1" zvalue="7827"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="273" MaxPinNum="2"/>
   </metadata>
  <path d="M 1250.82 431.07 L 1234.26 431.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv110" d="M 1329.32 293.28 L 1329.32 336.42" stroke-width="1" zvalue="8034"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@1" LinkObjectIDznd="48@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1329.32 293.28 L 1329.32 336.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv110" d="M 1346.93 309.27 L 1329.32 309.27" stroke-width="1" zvalue="8035"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="151" MaxPinNum="2"/>
   </metadata>
  <path d="M 1346.93 309.27 L 1329.32 309.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv110" d="M 1329.3 273.65 L 1329.3 227.4" stroke-width="1" zvalue="8041"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@0" LinkObjectIDznd="379@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1329.3 273.65 L 1329.3 227.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv110" d="M 1346.93 257.27 L 1329.3 257.27" stroke-width="1" zvalue="8042"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 1346.93 257.27 L 1329.3 257.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv110" d="M 1065.27 253.29 L 1065.27 298.45" stroke-width="1" zvalue="8776"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1429@1" LinkObjectIDznd="1397@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1065.27 253.29 L 1065.27 298.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv110" d="M 1082.82 278 L 1065.27 278" stroke-width="1" zvalue="8777"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="26" MaxPinNum="2"/>
   </metadata>
  <path d="M 1082.82 278 L 1065.27 278" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv110" d="M 1082.82 142.71 L 1065.11 142.71" stroke-width="1" zvalue="8783"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1427@0" LinkObjectIDznd="575" MaxPinNum="2"/>
   </metadata>
  <path d="M 1082.82 142.71 L 1065.11 142.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="kv110" d="M 1234.1 393.61 L 1234.1 370.44" stroke-width="1" zvalue="8875"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@1" LinkObjectIDznd="272@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.1 393.61 L 1234.1 370.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv110" d="M 1234.28 457.71 L 1234.28 510.86" stroke-width="1" zvalue="8876"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="275@0" LinkObjectIDznd="190@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.28 457.71 L 1234.28 510.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="510">
   <path class="kv110" d="M 587.97 419.5 L 587.97 438.08" stroke-width="1" zvalue="9172"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="512@0" LinkObjectIDznd="511@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 587.97 419.5 L 587.97 438.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="504">
   <path class="kv110" d="M 608.54 381.31 L 587.82 381.31" stroke-width="1" zvalue="9180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="507@0" LinkObjectIDznd="500" MaxPinNum="2"/>
   </metadata>
  <path d="M 608.54 381.31 L 587.82 381.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="501">
   <path class="kv110" d="M 608.54 429.31 L 587.97 429.31" stroke-width="1" zvalue="9181"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="508@0" LinkObjectIDznd="510" MaxPinNum="2"/>
   </metadata>
  <path d="M 608.54 429.31 L 587.97 429.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="500">
   <path class="kv110" d="M 587.82 393.61 L 587.82 370.44" stroke-width="1" zvalue="9182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="512@1" LinkObjectIDznd="509@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 587.82 393.61 L 587.82 370.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="513">
   <path class="kv110" d="M 588 457.71 L 588 510.86" stroke-width="1" zvalue="9183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="511@0" LinkObjectIDznd="483@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 588 457.71 L 588 510.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="555">
   <path class="kv10" d="M 1396.66 838.96 L 1396.57 883.21" stroke-width="1" zvalue="9425"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="549@0" LinkObjectIDznd="552@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1396.66 838.96 L 1396.57 883.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="557">
   <path class="kv10" d="M 1419.65 867.08 L 1419.65 856.33 L 1396.62 856.33" stroke-width="1" zvalue="9427"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="520@0" LinkObjectIDznd="555" MaxPinNum="2"/>
   </metadata>
  <path d="M 1419.65 867.08 L 1419.65 856.33 L 1396.62 856.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="812">
   <path class="kv110" d="M 518.83 229.4 L 518.83 186.82" stroke-width="1" zvalue="9727"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="816@0" LinkObjectIDznd="815@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 518.83 229.4 L 518.83 186.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="643">
   <path class="kv110" d="M 536.53 205.96 L 518.83 205.96" stroke-width="1" zvalue="9733"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="644@0" LinkObjectIDznd="812" MaxPinNum="2"/>
   </metadata>
  <path d="M 536.53 205.96 L 518.83 205.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="633">
   <path class="kv110" d="M 519.04 318.09 L 519.04 335.17" stroke-width="1" zvalue="9736"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="811@1" LinkObjectIDznd="24@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 519.04 318.09 L 519.04 335.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="632">
   <path class="kv110" d="M 518.98 255.29 L 519.01 298.45" stroke-width="1" zvalue="9737"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="816@1" LinkObjectIDznd="811@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 518.98 255.29 L 519.01 298.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="631">
   <path class="kv110" d="M 536.53 278 L 519 278" stroke-width="1" zvalue="9738"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="636@0" LinkObjectIDznd="632" MaxPinNum="2"/>
   </metadata>
  <path d="M 536.53 278 L 519 278" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="628">
   <path class="kv110" d="M 536.53 142.71 L 518.45 142.71" stroke-width="1" zvalue="9740"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="813@0" LinkObjectIDznd="572" MaxPinNum="2"/>
   </metadata>
  <path d="M 536.53 142.71 L 518.45 142.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="827">
   <path class="kv110" d="M 1250.82 465.31 L 1234.28 465.31" stroke-width="1" zvalue="9750"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="822@0" LinkObjectIDznd="131" MaxPinNum="2"/>
   </metadata>
  <path d="M 1250.82 465.31 L 1234.28 465.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="828">
   <path class="kv110" d="M 609.79 464.06 L 588 464.06" stroke-width="1" zvalue="9751"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="824@0" LinkObjectIDznd="513" MaxPinNum="2"/>
   </metadata>
  <path d="M 609.79 464.06 L 588 464.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="831">
   <path class="kv10" d="M 1253.06 599.53 L 1233.28 599.53" stroke-width="1" zvalue="9756"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="829@0" LinkObjectIDznd="8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1253.06 599.53 L 1233.28 599.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv110" d="M 587.79 350.81 L 587.79 335.17" stroke-width="1" zvalue="9758"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="509@1" LinkObjectIDznd="24@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 587.79 350.81 L 587.79 335.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv10" d="M 1241.62 783.33 L 1255.65 783.33 L 1255.65 791.03" stroke-width="1" zvalue="9782"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1241.62 783.33 L 1255.65 783.33 L 1255.65 791.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv10" d="M 1241.62 783.33 L 1216.98 783.33 L 1216.98 792.68" stroke-width="1" zvalue="9783"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46" LinkObjectIDznd="34@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1241.62 783.33 L 1216.98 783.33 L 1216.98 792.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv10" d="M 1396.62 784.67 L 1411.65 784.67 L 1411.65 791.03" stroke-width="1" zvalue="9801"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117" LinkObjectIDznd="64@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1396.62 784.67 L 1411.65 784.67 L 1411.65 791.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv10" d="M 1396.62 785.5 L 1371.77 785.5 L 1371.8 792.68" stroke-width="1" zvalue="9802"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117" LinkObjectIDznd="548@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1396.62 785.5 L 1371.77 785.5 L 1371.8 792.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv10" d="M 1480.61 784.67 L 1495.65 784.67 L 1495.65 791.03" stroke-width="1" zvalue="9821"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="626" LinkObjectIDznd="78@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1480.61 784.67 L 1495.65 784.67 L 1495.65 791.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv10" d="M 1480.61 785.5 L 1455.77 785.5 L 1455.8 792.68" stroke-width="1" zvalue="9822"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="626" LinkObjectIDznd="93@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1480.61 785.5 L 1455.77 785.5 L 1455.8 792.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv10" d="M 1560.61 784.67 L 1575.65 784.67 L 1575.65 791.03" stroke-width="1" zvalue="9842"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="440" LinkObjectIDznd="102@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1560.61 784.67 L 1575.65 784.67 L 1575.65 791.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv10" d="M 1560.61 785.5 L 1535.77 785.5 L 1535.8 792.68" stroke-width="1" zvalue="9843"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="440" LinkObjectIDznd="833@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1560.61 785.5 L 1535.77 785.5 L 1535.8 792.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="844">
   <path class="kv10" d="M 1636.62 783.33 L 1651.65 783.33 L 1651.65 791.03" stroke-width="1" zvalue="9856"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="143" LinkObjectIDznd="846@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1636.62 783.33 L 1651.65 783.33 L 1651.65 791.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="843">
   <path class="kv10" d="M 1636.63 783.33 L 1610.77 783.33 L 1610.8 792.68" stroke-width="1" zvalue="9857"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="844" LinkObjectIDznd="855@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1636.63 783.33 L 1610.77 783.33 L 1610.8 792.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="954">
   <path class="kv10" d="M 983.33 783.33 L 997.36 783.33 L 997.36 791.03" stroke-width="1" zvalue="9911"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145" LinkObjectIDznd="956@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 983.33 783.33 L 997.36 783.33 L 997.36 791.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="953">
   <path class="kv10" d="M 983.33 783.33 L 956.51 783.33 L 956.51 796.68" stroke-width="1" zvalue="9912"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="954" LinkObjectIDznd="963@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 983.33 783.33 L 956.51 783.33 L 956.51 796.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="947">
   <path class="kv10" d="M 907.33 784.67 L 921.54 784.67" stroke-width="1" zvalue="9916"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152" LinkObjectIDznd="986@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 907.33 784.67 L 921.54 784.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="946">
   <path class="kv10" d="M 907.33 784.67 L 881.48 784.67 L 881.51 792.68" stroke-width="1" zvalue="9917"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="947" LinkObjectIDznd="994@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 907.33 784.67 L 881.48 784.67 L 881.51 792.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="938">
   <path class="kv10" d="M 755.33 784.67 L 769.36 784.67" stroke-width="1" zvalue="9921"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157" LinkObjectIDznd="940@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.33 784.67 L 769.36 784.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="935">
   <path class="kv10" d="M 755.33 785.5 L 729.51 785.5 L 729.51 796.68" stroke-width="1" zvalue="9922"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157" LinkObjectIDznd="979@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.33 785.5 L 729.51 785.5 L 729.51 796.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="924">
   <path class="kv10" d="M 678.37 838.96 L 678.29 883.21" stroke-width="1" zvalue="9937"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="934@0" LinkObjectIDznd="925@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 678.37 838.96 L 678.29 883.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="923">
   <path class="kv10" d="M 701.36 867.08 L 701.36 856.33 L 678.34 856.33" stroke-width="1" zvalue="9938"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="926@0" LinkObjectIDznd="924" MaxPinNum="2"/>
   </metadata>
  <path d="M 701.36 867.08 L 701.36 856.33 L 678.34 856.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="921">
   <path class="kv10" d="M 679.33 784.67 L 693.36 784.67" stroke-width="1" zvalue="9940"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159" LinkObjectIDznd="922@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 679.33 784.67 L 693.36 784.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="920">
   <path class="kv10" d="M 679.33 785.5 L 657.51 785.5 L 657.51 792.68" stroke-width="1" zvalue="9941"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159" LinkObjectIDznd="933@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 679.33 785.5 L 657.51 785.5 L 657.51 792.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="888">
   <path class="kv10" d="M 447.32 783.32 L 461.36 783.32 L 461.36 791.03" stroke-width="1" zvalue="9973"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162" LinkObjectIDznd="890@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 447.32 783.32 L 461.36 783.32 L 461.36 791.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="887">
   <path class="kv10" d="M 447.32 783.33 L 424.51 783.33 L 424.51 792.68" stroke-width="1" zvalue="9974"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162" LinkObjectIDznd="899@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 447.32 783.33 L 424.51 783.33 L 424.51 792.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv35" d="M 1818.48 164.86 L 1838.26 164.86" stroke-width="1" zvalue="10011"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@1" LinkObjectIDznd="38@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1818.48 164.86 L 1838.26 164.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv35" d="M 1764.03 186.39 L 1764.03 166.69" stroke-width="1" zvalue="10013"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="209" MaxPinNum="2"/>
   </metadata>
  <path d="M 1764.03 186.39 L 1764.03 166.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv35" d="M 1764.03 166.69 L 1764.03 132.69" stroke-width="1" zvalue="10014"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58" LinkObjectIDznd="462@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1764.03 166.69 L 1764.03 132.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv35" d="M 1771.25 150.79 L 1764.03 150.79" stroke-width="1" zvalue="10015"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="23@0" LinkObjectIDznd="73" MaxPinNum="2"/>
   </metadata>
  <path d="M 1771.25 150.79 L 1764.03 150.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv35" d="M 1818.48 288.86 L 1838.26 288.86" stroke-width="1" zvalue="10034"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@1" LinkObjectIDznd="176@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1818.48 288.86 L 1838.26 288.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="kv35" d="M 1764.03 310.39 L 1764.03 290.69" stroke-width="1" zvalue="10036"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="205" MaxPinNum="2"/>
   </metadata>
  <path d="M 1764.03 310.39 L 1764.03 290.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv35" d="M 1764.03 290.69 L 1764.03 258.38" stroke-width="1" zvalue="10037"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138" LinkObjectIDznd="195@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1764.03 290.69 L 1764.03 258.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv35" d="M 1771.25 278.79 L 1764.03 278.79" stroke-width="1" zvalue="10038"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="137" MaxPinNum="2"/>
   </metadata>
  <path d="M 1771.25 278.79 L 1764.03 278.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="277">
   <path class="kv35" d="M 1655.01 349.85 L 1685.97 349.85" stroke-width="1" zvalue="10078"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="258@0" LinkObjectIDznd="489@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1655.01 349.85 L 1685.97 349.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="kv35" d="M 1655.79 422.21 L 1685.97 422.21" stroke-width="1" zvalue="10079"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="346@1" LinkObjectIDznd="490@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1655.79 422.21 L 1685.97 422.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="307">
   <path class="kv35" d="M 1819.59 441.97 L 1838.34 441.97" stroke-width="1" zvalue="10100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="326@1" LinkObjectIDznd="325@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1819.59 441.97 L 1838.34 441.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="294">
   <path class="kv35" d="M 1764.03 462.39 L 1764.03 442.69" stroke-width="1" zvalue="10102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="330@0" LinkObjectIDznd="201" MaxPinNum="2"/>
   </metadata>
  <path d="M 1764.03 462.39 L 1764.03 442.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="293">
   <path class="kv35" d="M 1764.03 442.69 L 1764.03 406.38" stroke-width="1" zvalue="10103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="294" LinkObjectIDznd="351@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1764.03 442.69 L 1764.03 406.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="292">
   <path class="kv35" d="M 1771.25 426.79 L 1764.03 426.79" stroke-width="1" zvalue="10104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="334@0" LinkObjectIDznd="293" MaxPinNum="2"/>
   </metadata>
  <path d="M 1771.25 426.79 L 1764.03 426.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="394">
   <path class="kv35" d="M 1819.59 565.97 L 1839.37 565.97" stroke-width="1" zvalue="10123"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="425@1" LinkObjectIDznd="410@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1819.59 565.97 L 1839.37 565.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="374">
   <path class="kv35" d="M 1764.03 586.39 L 1764.03 566.69" stroke-width="1" zvalue="10125"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="443@0" LinkObjectIDznd="200" MaxPinNum="2"/>
   </metadata>
  <path d="M 1764.03 586.39 L 1764.03 566.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="373">
   <path class="kv35" d="M 1764.03 566.69 L 1764.03 530.38" stroke-width="1" zvalue="10126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="374" LinkObjectIDznd="453@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1764.03 566.69 L 1764.03 530.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="372">
   <path class="kv35" d="M 1771.25 550.79 L 1764.03 550.79" stroke-width="1" zvalue="10127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="447@0" LinkObjectIDznd="373" MaxPinNum="2"/>
   </metadata>
  <path d="M 1771.25 550.79 L 1764.03 550.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="497">
   <path class="kv35" d="M 1764.03 730.39 L 1764.03 710.69" stroke-width="1" zvalue="10148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="523@0" LinkObjectIDznd="163" MaxPinNum="2"/>
   </metadata>
  <path d="M 1764.03 730.39 L 1764.03 710.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="496">
   <path class="kv35" d="M 1764.03 710.69 L 1764.03 674.38" stroke-width="1" zvalue="10149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="497" LinkObjectIDznd="563@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1764.03 710.69 L 1764.03 674.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="493">
   <path class="kv35" d="M 1771.25 694.79 L 1764.03 694.79" stroke-width="1" zvalue="10150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="524@0" LinkObjectIDznd="496" MaxPinNum="2"/>
   </metadata>
  <path d="M 1771.25 694.79 L 1764.03 694.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="573">
   <path class="kv110" d="M 683.32 273.65 L 683.32 228.83" stroke-width="1" zvalue="10158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@0" LinkObjectIDznd="382@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 683.32 273.65 L 683.32 228.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="574">
   <path class="kv110" d="M 700.64 258.52 L 683.32 258.52" stroke-width="1" zvalue="10159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="573" MaxPinNum="2"/>
   </metadata>
  <path d="M 700.64 258.52 L 683.32 258.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="578">
   <path class="kv110" d="M 683.35 293.28 L 683.35 335.17" stroke-width="1" zvalue="10160"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@1" LinkObjectIDznd="24@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 683.35 293.28 L 683.35 335.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="579">
   <path class="kv110" d="M 700.64 309.27 L 683.35 309.27" stroke-width="1" zvalue="10161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="578" MaxPinNum="2"/>
   </metadata>
  <path d="M 700.64 309.27 L 683.35 309.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv10" d="M 1233.28 616.08 L 1233.28 586.51" stroke-width="1" zvalue="10162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="461@1" LinkObjectIDznd="190@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1233.28 616.08 L 1233.28 586.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv10" d="M 1233.28 648.02 L 1233.28 692.14" stroke-width="1" zvalue="10163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="461@0" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1233.28 648.02 L 1233.28 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv10" d="M 1241.62 715.95 L 1241.62 692.14" stroke-width="1" zvalue="10164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@1" LinkObjectIDznd="166@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1241.62 715.95 L 1241.62 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 1241.62 747.89 L 1241.62 855.25" stroke-width="1" zvalue="10165"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1241.62 747.89 L 1241.62 855.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv10" d="M 1396.62 715.95 L 1396.62 692.14" stroke-width="1" zvalue="10168"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="546@1" LinkObjectIDznd="166@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1396.62 715.95 L 1396.62 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv10" d="M 1396.62 747.89 L 1396.63 819.33" stroke-width="1" zvalue="10169"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="546@0" LinkObjectIDznd="549@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1396.62 747.89 L 1396.63 819.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv10" d="M 1480.62 715.95 L 1480.62 692.14" stroke-width="1" zvalue="10171"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@1" LinkObjectIDznd="166@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1480.62 715.95 L 1480.62 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv10" d="M 1636.62 715.95 L 1636.62 692.14" stroke-width="1" zvalue="10172"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="854@1" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1636.62 715.95 L 1636.62 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv10" d="M 1560.62 715.95 L 1560.62 692.14" stroke-width="1" zvalue="10174"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="795@1" LinkObjectIDznd="166@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1560.62 715.95 L 1560.62 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv10" d="M 1636.62 747.89 L 1636.62 859.3" stroke-width="1" zvalue="10175"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="854@0" LinkObjectIDznd="603" MaxPinNum="2"/>
   </metadata>
  <path d="M 1636.62 747.89 L 1636.62 859.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv10" d="M 983.33 715.95 L 983.33 692.14" stroke-width="1" zvalue="10176"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="962@1" LinkObjectIDznd="737@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 983.33 715.95 L 983.33 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv10" d="M 983.33 747.89 L 983.33 855.25" stroke-width="1" zvalue="10177"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="962@0" LinkObjectIDznd="949@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 983.33 747.89 L 983.33 855.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv10" d="M 907.33 715.95 L 907.33 692.14" stroke-width="1" zvalue="10178"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="992@1" LinkObjectIDznd="737@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 907.33 715.95 L 907.33 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv10" d="M 907.33 747.89 L 907.33 819.33" stroke-width="1" zvalue="10179"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="992@0" LinkObjectIDznd="995@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 907.33 747.89 L 907.33 819.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv10" d="M 755.33 715.95 L 755.33 692.14" stroke-width="1" zvalue="10180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="978@1" LinkObjectIDznd="737@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.33 715.95 L 755.33 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv10" d="M 755.33 747.89 L 755.33 819.33" stroke-width="1" zvalue="10181"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="978@0" LinkObjectIDznd="980@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.33 747.89 L 755.33 819.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv10" d="M 679.33 715.95 L 679.33 692.14" stroke-width="1" zvalue="10182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="932@1" LinkObjectIDznd="737@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 679.33 715.95 L 679.33 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="kv10" d="M 679.33 747.89 L 679.33 819.33" stroke-width="1" zvalue="10183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="932@0" LinkObjectIDznd="934@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 679.33 747.89 L 679.33 819.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv10" d="M 447.33 715.95 L 447.33 692.14" stroke-width="1" zvalue="10184"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="898@1" LinkObjectIDznd="737@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 447.33 715.95 L 447.33 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv10" d="M 447.33 747.89 L 447.33 859.4" stroke-width="1" zvalue="10185"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="898@0" LinkObjectIDznd="602" MaxPinNum="2"/>
   </metadata>
  <path d="M 447.33 747.89 L 447.33 859.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="kv35" d="M 1712.13 710.69 L 1685.97 710.69" stroke-width="1" zvalue="10187"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="562@1" LinkObjectIDznd="490@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1712.13 710.69 L 1685.97 710.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv35" d="M 1712.13 566.69 L 1685.97 566.69" stroke-width="1" zvalue="10188"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="452@1" LinkObjectIDznd="490@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1712.13 566.69 L 1685.97 566.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv35" d="M 1656.23 668.04 L 1685.97 668.04" stroke-width="1" zvalue="10190"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@0" LinkObjectIDznd="490@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1656.23 668.04 L 1685.97 668.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="kv35" d="M 1744.06 566.69 L 1799.96 566.69" stroke-width="1" zvalue="10192"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="452@0" LinkObjectIDznd="425@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1744.06 566.69 L 1799.96 566.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv35" d="M 1744.06 442.69 L 1799.96 442.69" stroke-width="1" zvalue="10193"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="349@0" LinkObjectIDznd="326@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1744.06 442.69 L 1799.96 442.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv35" d="M 1712.13 442.69 L 1685.97 442.69" stroke-width="1" zvalue="10194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="349@1" LinkObjectIDznd="490@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1712.13 442.69 L 1685.97 442.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="203">
   <path class="kv35" d="M 1712.13 290.69 L 1685.97 290.69" stroke-width="1" zvalue="10195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@1" LinkObjectIDznd="489@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1712.13 290.69 L 1685.97 290.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="205">
   <path class="kv35" d="M 1744.06 290.69 L 1798.85 290.69" stroke-width="1" zvalue="10196"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@0" LinkObjectIDznd="177@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1744.06 290.69 L 1798.85 290.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv35" d="M 1712.13 166.69 L 1685.97 166.69" stroke-width="1" zvalue="10197"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@1" LinkObjectIDznd="489@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1712.13 166.69 L 1685.97 166.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv35" d="M 1744.06 166.69 L 1798.85 166.69" stroke-width="1" zvalue="10198"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@0" LinkObjectIDznd="37@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1744.06 166.69 L 1798.85 166.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv10" d="M 587.92 615.13 L 588 586.51" stroke-width="1" zvalue="10286"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="721@1" LinkObjectIDznd="483@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 587.92 615.13 L 588 586.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="kv10" d="M 587.92 647.06 L 587.92 692.14" stroke-width="1" zvalue="10287"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="721@0" LinkObjectIDznd="737@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 587.92 647.06 L 587.92 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="259">
   <path class="kv110" d="M 854.38 263.56 L 854.38 288.15" stroke-width="1" zvalue="10299"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="260@0" LinkObjectIDznd="269@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.38 263.56 L 854.38 288.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="249">
   <path class="kv110" d="M 854.36 307.79 L 854.36 335.17" stroke-width="1" zvalue="10300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="269@0" LinkObjectIDznd="24@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.36 307.79 L 854.36 335.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv110" d="M 975.95 263.56 L 975.95 289.27" stroke-width="1" zvalue="10301"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="264@0" LinkObjectIDznd="265@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 975.95 263.56 L 975.95 289.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="kv110" d="M 925.7 279.37 L 975.95 279.37" stroke-width="1" zvalue="10302"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="270@1" LinkObjectIDznd="240" MaxPinNum="2"/>
   </metadata>
  <path d="M 925.7 279.37 L 975.95 279.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="227">
   <path class="kv110" d="M 899 279.3 L 854.38 279.3" stroke-width="1" zvalue="10303"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="270@0" LinkObjectIDznd="259" MaxPinNum="2"/>
   </metadata>
  <path d="M 899 279.3 L 854.38 279.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv110" d="M 975.92 308.9 L 975.92 336.42" stroke-width="1" zvalue="10304"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265@0" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 975.92 308.9 L 975.92 336.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="320">
   <path class="kv10" d="M 1315.19 714.2 L 1315.19 692.14" stroke-width="1" zvalue="10326"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="322@0" LinkObjectIDznd="166@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1315.19 714.2 L 1315.19 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="316">
   <path class="kv10" d="M 1315.19 833.44 L 1315.19 747.38" stroke-width="1" zvalue="10332"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="318@0" LinkObjectIDznd="322@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1315.19 833.44 L 1315.19 747.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="313">
   <path class="kv10" d="M 1315.22 897.54 L 1315.22 853.07" stroke-width="1" zvalue="10335"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="314@0" LinkObjectIDznd="318@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1315.22 897.54 L 1315.22 853.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="353">
   <path class="kv10" d="M 1432.22 609.93 L 1432.22 625.32" stroke-width="1" zvalue="10359"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="364@0" LinkObjectIDznd="350@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1432.22 609.93 L 1432.22 625.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="371">
   <path class="kv10" d="M 782.58 612.16 L 782.58 624.97" stroke-width="1" zvalue="10366"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="369@0" LinkObjectIDznd="377@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 782.58 612.16 L 782.58 624.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="370">
   <path class="kv10" d="M 782.55 658.62 L 782.55 692.14" stroke-width="1" zvalue="10367"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="377@0" LinkObjectIDznd="737@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 782.55 658.62 L 782.55 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="386">
   <path class="kv35" d="M 1656.23 145.02 L 1685.97 145.02" stroke-width="1" zvalue="10375"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="495@0" LinkObjectIDznd="489@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1656.23 145.02 L 1685.97 145.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="400">
   <path class="kv10" d="M 906.37 838.96 L 906.37 897.54" stroke-width="1" zvalue="10386"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="995@0" LinkObjectIDznd="399@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 906.37 838.96 L 906.37 897.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="401">
   <path class="kv35" d="M 641.18 548.62 L 675 548.62 L 675 668.04 L 1624.29 668.04" stroke-width="1" zvalue="10387"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="483@3" LinkObjectIDznd="235@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 641.18 548.62 L 675 548.62 L 675 668.04 L 1624.29 668.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="402">
   <path class="kv35" d="M 1555.63 650.77 L 1555.63 668.04" stroke-width="1" zvalue="10388"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="228@0" LinkObjectIDznd="401" MaxPinNum="2"/>
   </metadata>
  <path d="M 1555.63 650.77 L 1555.63 668.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="404">
   <path class="kv35" d="M 1287.47 548.62 L 1392.51 548.62 L 1392.51 145.02 L 1624.29 145.02" stroke-width="1" zvalue="10389"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@3" LinkObjectIDznd="495@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1287.47 548.62 L 1392.51 548.62 L 1392.51 145.02 L 1624.29 145.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="406">
   <path class="kv35" d="M 1320.43 559.57 L 1320.43 548.62" stroke-width="1" zvalue="10391"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="515@0" LinkObjectIDznd="404" MaxPinNum="2"/>
   </metadata>
  <path d="M 1320.43 559.57 L 1320.43 548.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="408">
   <path class="kv35" d="M 656.03 560.06 L 656.03 548.62" stroke-width="1" zvalue="10392"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="480@0" LinkObjectIDznd="401" MaxPinNum="2"/>
   </metadata>
  <path d="M 656.03 560.06 L 656.03 548.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="411">
   <path class="kv10" d="M 610.78 599.53 L 587.96 599.53" stroke-width="1" zvalue="10393"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="830@0" LinkObjectIDznd="216" MaxPinNum="2"/>
   </metadata>
  <path d="M 610.78 599.53 L 587.96 599.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="413">
   <path class="kv35" d="M 1392.25 127.02 L 1392.25 145.02" stroke-width="1" zvalue="10394"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266@0" LinkObjectIDznd="404" MaxPinNum="2"/>
   </metadata>
  <path d="M 1392.25 127.02 L 1392.25 145.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="418">
   <path class="kv35" d="M 1599.2 233.5 L 1621.37 233.5" stroke-width="1" zvalue="10399"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@0" LinkObjectIDznd="417@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1599.2 233.5 L 1621.37 233.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="419">
   <path class="kv35" d="M 1655.01 233.54 L 1685.97 233.54" stroke-width="1" zvalue="10400"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="417@0" LinkObjectIDznd="489@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1655.01 233.54 L 1685.97 233.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="424">
   <path class="kv35" d="M 1599.2 528.66 L 1621.37 528.66" stroke-width="1" zvalue="10406"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="427@0" LinkObjectIDznd="426@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1599.2 528.66 L 1621.37 528.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="423">
   <path class="kv35" d="M 1655.01 529.25 L 1685.97 529.25" stroke-width="1" zvalue="10407"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="426@0" LinkObjectIDznd="490@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1655.01 529.25 L 1685.97 529.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="430">
   <path class="kv35" d="M 619.56 548.62 L 619.56 495.71 L 867.67 495.71" stroke-width="1" zvalue="10411"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="483@2" LinkObjectIDznd="429@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 619.56 548.62 L 619.56 495.71 L 867.67 495.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="434">
   <path class="kv35" d="M 973.31 495.69 L 1265.85 495.69 L 1265.85 548.62" stroke-width="1" zvalue="10416"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="432@1" LinkObjectIDznd="190@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 973.31 495.69 L 1265.85 495.69 L 1265.85 548.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="449">
   <path class="kv10" d="M 421.56 902.52 L 421.56 912.75" stroke-width="1" zvalue="10425"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="446@0" LinkObjectIDznd="448@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 421.56 902.52 L 421.56 912.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="455">
   <path class="kv10" d="M 1664.37 902.52 L 1664.37 912.75" stroke-width="1" zvalue="10432"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="457@0" LinkObjectIDznd="456@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1664.37 902.52 L 1664.37 912.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="237">
   <path class="kv35" d="M 1621.37 349.81 L 1601 349.81 L 1601 422.11 L 1624.21 422.11" stroke-width="1" zvalue="10463"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="258@1" LinkObjectIDznd="346@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1621.37 349.81 L 1601 349.81 L 1601 422.11 L 1624.21 422.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="238">
   <path class="kv10" d="M 1055.57 727.93 L 1055.57 692.14" stroke-width="1" zvalue="10464"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="338@0" LinkObjectIDznd="737@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1055.57 727.93 L 1055.57 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="241">
   <path class="kv10" d="M 1055.46 763.23 L 1055.46 789.25 L 1165.26 789.25 L 1165.26 759.8" stroke-width="1" zvalue="10465"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="338@1" LinkObjectIDznd="295@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1055.46 763.23 L 1055.46 789.25 L 1165.26 789.25 L 1165.26 759.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="kv10" d="M 1165.3 726.15 L 1165.3 692.14" stroke-width="1" zvalue="10466"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="295@1" LinkObjectIDznd="166@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1165.3 726.15 L 1165.3 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="468">
   <path class="kv10" d="M 828.37 838.96 L 828.29 883.21" stroke-width="1" zvalue="10529"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="473@0" LinkObjectIDznd="469@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 828.37 838.96 L 828.29 883.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="467">
   <path class="kv10" d="M 851.36 867.08 L 851.36 856.33 L 828.34 856.33" stroke-width="1" zvalue="10530"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="470@0" LinkObjectIDznd="468" MaxPinNum="2"/>
   </metadata>
  <path d="M 851.36 867.08 L 851.36 856.33 L 828.34 856.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="465">
   <path class="kv10" d="M 829.33 784.67 L 843.36 784.67" stroke-width="1" zvalue="10532"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="431" LinkObjectIDznd="466@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.33 784.67 L 843.36 784.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="445">
   <path class="kv10" d="M 829.33 785.5 L 803.51 785.5 L 803.51 796.68" stroke-width="1" zvalue="10533"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="431" LinkObjectIDznd="472@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.33 785.5 L 803.51 785.5 L 803.51 796.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="436">
   <path class="kv10" d="M 829.33 715.95 L 829.33 692.14" stroke-width="1" zvalue="10534"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="471@1" LinkObjectIDznd="737@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.33 715.95 L 829.33 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="431">
   <path class="kv10" d="M 829.33 747.89 L 829.33 819.33" stroke-width="1" zvalue="10535"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="471@0" LinkObjectIDznd="473@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.33 747.89 L 829.33 819.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="572">
   <path class="kv110" d="M 518.45 125.01 L 518.45 146.1 L 518.81 146.1 L 518.81 167.19" stroke-width="1" zvalue="10585"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="819@0" LinkObjectIDznd="815@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 518.45 125.01 L 518.45 146.1 L 518.81 146.1 L 518.81 167.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="575">
   <path class="kv110" d="M 1065.12 125.01 L 1065.09 167.19" stroke-width="1" zvalue="10586"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1430@0" LinkObjectIDznd="1428@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1065.12 125.01 L 1065.09 167.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="576">
   <path class="kv10" d="M 895.61 892.54 L 895.61 897.54 L 906.37 897.54" stroke-width="1" zvalue="10587"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="398@0" LinkObjectIDznd="400" MaxPinNum="2"/>
   </metadata>
  <path d="M 895.61 892.54 L 895.61 897.54 L 906.37 897.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="kv10" d="M 1315.19 783.82 L 1328.31 783.82 L 1328.31 795.62" stroke-width="1" zvalue="10589"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="316" LinkObjectIDznd="327@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1315.19 783.82 L 1328.31 783.82 L 1328.31 795.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv10" d="M 1315.19 783 L 1294.03 783 L 1294.03 791.87" stroke-width="1" zvalue="10590"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="316" LinkObjectIDznd="323@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1315.19 783 L 1294.03 783 L 1294.03 791.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv35" d="M 1744.06 710.69 L 1839.37 710.69" stroke-width="1" zvalue="10595"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="562@0" LinkObjectIDznd="518@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1744.06 710.69 L 1839.37 710.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv35" d="M 887.31 495.69 L 909 495.69" stroke-width="1" zvalue="10596"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="429@1" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 887.31 495.69 L 909 495.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv35" d="M 953.67 495.71 L 930 495.71" stroke-width="1" zvalue="10597"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="432@0" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 953.67 495.71 L 930 495.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="302">
   <path class="kv110" d="M 588.07 529.12 L 544 529 L 544.67 572.28" stroke-width="1" zvalue="10604"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="483@1" LinkObjectIDznd="412@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 588.07 529.12 L 544 529 L 544.67 572.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="324">
   <path class="kv110" d="M 1234.36 529.12 L 1182 529 L 1181.96 572.28" stroke-width="1" zvalue="10605"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@1" LinkObjectIDznd="14@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.36 529.12 L 1182 529 L 1181.96 572.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="602">
   <path class="kv10" d="M 446.97 859.4 L 446.97 869.07 L 421.53 869.07 L 421.53 882.88" stroke-width="1" zvalue="10686"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="306@0" LinkObjectIDznd="446@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 446.97 859.4 L 446.97 869.07 L 421.53 869.07 L 421.53 882.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="603">
   <path class="kv10" d="M 1638.27 859.3 L 1638.27 869.07 L 1664.34 869.07 L 1664.34 882.88" stroke-width="1" zvalue="10687"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="279@0" LinkObjectIDznd="457@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1638.27 859.3 L 1638.27 869.07 L 1664.34 869.07 L 1664.34 882.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="440">
   <path class="kv10" d="M 1560.62 747.89 L 1560.57 883.21" stroke-width="1" zvalue="10691"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="795@0" LinkObjectIDznd="219@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1560.62 747.89 L 1560.57 883.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv10" d="M 1304.61 890.18 L 1315.22 890.18" stroke-width="1" zvalue="10763"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="312@0" LinkObjectIDznd="313" MaxPinNum="2"/>
   </metadata>
  <path d="M 1304.61 890.18 L 1315.22 890.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="255">
   <path class="kv10" d="M 754.37 838.96 L 754.37 884.15" stroke-width="1" zvalue="10777"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="980@0" LinkObjectIDznd="298@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 754.37 838.96 L 754.37 884.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="287">
   <path class="kv10" d="M 777.36 867.08 L 777.36 858 L 754.37 858" stroke-width="1" zvalue="10778"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="971@0" LinkObjectIDznd="255" MaxPinNum="2"/>
   </metadata>
  <path d="M 777.36 867.08 L 777.36 858 L 754.37 858" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="393">
   <path class="kv10" d="M 1432.19 658.97 L 1432.19 692.14" stroke-width="1" zvalue="10865"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="350@0" LinkObjectIDznd="166@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1432.19 658.97 L 1432.19 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="618">
   <path class="kv10" d="M 520.33 784.67 L 534.36 784.67" stroke-width="1" zvalue="10891"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="651" LinkObjectIDznd="619@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 520.33 784.67 L 534.36 784.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="617">
   <path class="kv10" d="M 520.33 785.5 L 498.51 785.5 L 498.51 792.68" stroke-width="1" zvalue="10892"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="651" LinkObjectIDznd="625@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 520.33 785.5 L 498.51 785.5 L 498.51 792.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="616">
   <path class="kv10" d="M 520.33 715.95 L 520.33 692.14" stroke-width="1" zvalue="10893"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="624@1" LinkObjectIDznd="737@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 520.33 715.95 L 520.33 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="641">
   <path class="kv10" d="M 623.36 867.08 L 623.36 856.33 L 600.29 856.33" stroke-width="1" zvalue="10906"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="646@0" LinkObjectIDznd="621" MaxPinNum="2"/>
   </metadata>
  <path d="M 623.36 867.08 L 623.36 856.33 L 600.29 856.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="639">
   <path class="kv10" d="M 601.33 784.67 L 615.36 784.67" stroke-width="1" zvalue="10908"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="615" LinkObjectIDznd="640@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 601.33 784.67 L 615.36 784.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="638">
   <path class="kv10" d="M 601.33 785.5 L 579.51 785.5 L 579.51 792.68" stroke-width="1" zvalue="10909"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="615" LinkObjectIDznd="648@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 601.33 785.5 L 579.51 785.5 L 579.51 792.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="637">
   <path class="kv10" d="M 601.33 715.95 L 601.33 692.14" stroke-width="1" zvalue="10910"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="647@1" LinkObjectIDznd="737@10" MaxPinNum="2"/>
   </metadata>
  <path d="M 601.33 715.95 L 601.33 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="651">
   <path class="kv10" d="M 520.33 747.89 L 520.33 883.21" stroke-width="1" zvalue="10933"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="624@0" LinkObjectIDznd="622@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 520.33 747.89 L 520.33 883.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="615">
   <path class="kv10" d="M 601.33 747.89 L 601.33 819.33" stroke-width="1" zvalue="10936"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="647@0" LinkObjectIDznd="614@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 601.33 747.89 L 601.33 819.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="621">
   <path class="kv10" d="M 600.29 838.96 L 600.29 883.21" stroke-width="1" zvalue="10937"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="614@0" LinkObjectIDznd="645@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 600.29 838.96 L 600.29 883.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="626">
   <path class="kv10" d="M 1480.62 747.89 L 1480.57 883.21" stroke-width="1" zvalue="10938"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="81@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1480.62 747.89 L 1480.57 883.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="678">
   <path class="kv110" d="M 1175.34 228.25 L 1175.34 187.67" stroke-width="1" zvalue="10981"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="681@0" LinkObjectIDznd="680@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1175.34 228.25 L 1175.34 187.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="675">
   <path class="kv110" d="M 1193.04 206.81 L 1175.34 206.81" stroke-width="1" zvalue="10986"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="676@0" LinkObjectIDznd="678" MaxPinNum="2"/>
   </metadata>
  <path d="M 1193.04 206.81 L 1175.34 206.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="673">
   <path class="kv110" d="M 1175.33 318.94 L 1175.33 336.42" stroke-width="1" zvalue="10989"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="677@1" LinkObjectIDznd="48@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1175.33 318.94 L 1175.33 336.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="672">
   <path class="kv110" d="M 1175.5 254.14 L 1175.5 299.31" stroke-width="1" zvalue="10990"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="681@1" LinkObjectIDznd="677@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1175.5 254.14 L 1175.5 299.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="671">
   <path class="kv110" d="M 1193.04 278.85 L 1175.5 278.85" stroke-width="1" zvalue="10991"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="674@0" LinkObjectIDznd="672" MaxPinNum="2"/>
   </metadata>
  <path d="M 1193.04 278.85 L 1175.5 278.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="670">
   <path class="kv110" d="M 1193.04 143.56 L 1175.32 143.56" stroke-width="1" zvalue="10992"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="679@0" LinkObjectIDznd="669" MaxPinNum="2"/>
   </metadata>
  <path d="M 1193.04 143.56 L 1175.32 143.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="669">
   <path class="kv110" d="M 1175.32 128 L 1175.32 168.04" stroke-width="1" zvalue="10993"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="689@0" LinkObjectIDznd="680@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1175.32 128 L 1175.32 168.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="48">
   <path class="kv110" d="M 956.62 336.42 L 1371.25 336.42" stroke-width="4" zvalue="7579"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674412920835" ObjectName="110kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674412920835"/></metadata>
  <path d="M 956.62 336.42 L 1371.25 336.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv10" d="M 1130 692.14 L 1659.37 692.14" stroke-width="4" zvalue="7718"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674412986371" ObjectName="10kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674412986371"/></metadata>
  <path d="M 1130 692.14 L 1659.37 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="489">
   <path class="kv35" d="M 1685.97 109 L 1685.97 370" stroke-width="4" zvalue="8106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674413051907" ObjectName="35kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674413051907"/></metadata>
  <path d="M 1685.97 109 L 1685.97 370" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="490">
   <path class="kv35" d="M 1685.97 403 L 1685.97 742.29" stroke-width="4" zvalue="8108"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674413117443" ObjectName="35kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674413117443"/></metadata>
  <path d="M 1685.97 403 L 1685.97 742.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv110" d="M 439.33 335.17 L 872.86 335.17" stroke-width="4" zvalue="8775"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674413182979" ObjectName="110kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674413182979"/></metadata>
  <path d="M 439.33 335.17 L 872.86 335.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="737">
   <path class="kv10" d="M 432.57 692.14 L 1088.89 692.14" stroke-width="4" zvalue="9203"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674413248515" ObjectName="10kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674413248515"/></metadata>
  <path d="M 432.57 692.14 L 1088.89 692.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer3Class">
  <g id="190">
   <g id="1900">
    <use class="kv110" height="50" transform="rotate(0,1249.43,548.619) scale(1.52766,1.51857) translate(-418.364,-174.382)" width="50" x="1211.24" xlink:href="#PowerTransformer3:可调三卷变YDY11_0" y="510.65" zvalue="8900"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874581999618" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1901">
    <use class="kv35" height="50" transform="rotate(0,1249.43,548.619) scale(1.52766,1.51857) translate(-418.364,-174.382)" width="50" x="1211.24" xlink:href="#PowerTransformer3:可调三卷变YDY11_1" y="510.65" zvalue="8900"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874582065154" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1902">
    <use class="kv10" height="50" transform="rotate(0,1249.43,548.619) scale(1.52766,1.51857) translate(-418.364,-174.382)" width="50" x="1211.24" xlink:href="#PowerTransformer3:可调三卷变YDY11_2" y="510.65" zvalue="8900"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874582130690" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399529201666" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399529201666"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1249.43,548.619) scale(1.52766,1.51857) translate(-418.364,-174.382)" width="50" x="1211.24" y="510.65"/></g>
  <g id="483">
   <g id="4830">
    <use class="kv110" height="50" transform="rotate(0,603.142,548.619) scale(1.52766,1.51857) translate(-195.135,-174.382)" width="50" x="564.95" xlink:href="#PowerTransformer3:可调三卷变YDY11_0" y="510.65" zvalue="9166"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874582196226" ObjectName="110"/>
    </metadata>
   </g>
   <g id="4831">
    <use class="kv35" height="50" transform="rotate(0,603.142,548.619) scale(1.52766,1.51857) translate(-195.135,-174.382)" width="50" x="564.95" xlink:href="#PowerTransformer3:可调三卷变YDY11_1" y="510.65" zvalue="9166"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874582261762" ObjectName="35"/>
    </metadata>
   </g>
   <g id="4832">
    <use class="kv10" height="50" transform="rotate(0,603.142,548.619) scale(1.52766,1.51857) translate(-195.135,-174.382)" width="50" x="564.95" xlink:href="#PowerTransformer3:可调三卷变YDY11_2" y="510.65" zvalue="9166"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874582327298" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399529267202" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399529267202"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,603.142,548.619) scale(1.52766,1.51857) translate(-195.135,-174.382)" width="50" x="564.95" y="510.65"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="480">
   <use class="kv35" height="26" transform="rotate(0,656.056,571.528) scale(-0.838049,0.927421) translate(-1439.87,43.7837)" width="12" x="651.0281809263649" xlink:href="#Accessory:避雷器1_0" y="559.4718190736351" zvalue="9162"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454432718851" ObjectName="#2主变35kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,656.056,571.528) scale(-0.838049,0.927421) translate(-1439.87,43.7837)" width="12" x="651.0281809263649" y="559.4718190736351"/></g>
  <g id="515">
   <use class="kv35" height="26" transform="rotate(0,1320.46,571.04) scale(-0.838049,0.927421) translate(-2897.07,43.7455)" width="12" x="1315.432942831127" xlink:href="#Accessory:避雷器1_0" y="558.9837238355399" zvalue="9187"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454433177603" ObjectName="#1主变35kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1320.46,571.04) scale(-0.838049,0.927421) translate(-2897.07,43.7455)" width="12" x="1315.432942831127" y="558.9837238355399"/></g>
  <g id="462">
   <use class="kv35" height="18" transform="rotate(270,1772,132.694) scale(-1.38489,1.23553) translate(-3048.83,-23.1762)" width="14" x="1762.304190540183" xlink:href="#Accessory:PT7_0" y="121.5743829858316" zvalue="9279"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454433243139" ObjectName="35kV景勐线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(270,1772,132.694) scale(-1.38489,1.23553) translate(-3048.83,-23.1762)" width="14" x="1762.304190540183" y="121.5743829858316"/></g>
  <g id="520">
   <use class="kv10" height="26" transform="rotate(0,1419.68,878.552) scale(-0.838049,0.927421) translate(-3114.67,67.8111)" width="12" x="1414.647243611278" xlink:href="#Accessory:避雷器1_0" y="866.495966701401" zvalue="9417"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454433308675" ObjectName="10kV景罕镇线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1419.68,878.552) scale(-0.838049,0.927421) translate(-3114.67,67.8111)" width="12" x="1414.647243611278" y="866.495966701401"/></g>
  <g id="829">
   <use class="kv10" height="26" transform="rotate(270,1264.53,599.504) scale(-0.838049,0.927421) translate(-2774.41,45.9731)" width="12" x="1259.504371402556" xlink:href="#Accessory:避雷器1_0" y="587.4480095498255" zvalue="9753"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454434684931" ObjectName="#1主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1264.53,599.504) scale(-0.838049,0.927421) translate(-2774.41,45.9731)" width="12" x="1259.504371402556" y="587.4480095498255"/></g>
  <g id="830">
   <use class="kv10" height="26" transform="rotate(270,622.247,599.504) scale(-0.838049,0.927421) translate(-1365.71,45.9731)" width="12" x="617.2186571168411" xlink:href="#Accessory:避雷器1_0" y="587.4480095498255" zvalue="9755"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454434750467" ObjectName="#2主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,622.247,599.504) scale(-0.838049,0.927421) translate(-1365.71,45.9731)" width="12" x="617.2186571168411" y="587.4480095498255"/></g>
  <g id="36">
   <use class="kv10" height="26" transform="rotate(0,1255.68,802.498) scale(-0.838049,0.927421) translate(-2754.98,61.8591)" width="12" x="1250.6472130937" xlink:href="#Accessory:避雷器1_0" y="790.4412277029879" zvalue="9778"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454434947075" ObjectName="10kV1号站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1255.68,802.498) scale(-0.838049,0.927421) translate(-2754.98,61.8591)" width="12" x="1250.6472130937" y="790.4412277029879"/></g>
  <g id="64">
   <use class="kv10" height="26" transform="rotate(0,1411.68,802.498) scale(-0.838049,0.927421) translate(-3097.13,61.8591)" width="12" x="1406.647243611278" xlink:href="#Accessory:避雷器1_0" y="790.4412277029879" zvalue="9800"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454435078147" ObjectName="10kV景罕镇线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1411.68,802.498) scale(-0.838049,0.927421) translate(-3097.13,61.8591)" width="12" x="1406.647243611278" y="790.4412277029879"/></g>
  <g id="78">
   <use class="kv10" height="26" transform="rotate(0,1495.68,802.498) scale(-0.838049,0.927421) translate(-3281.36,61.8591)" width="12" x="1490.647243611278" xlink:href="#Accessory:避雷器1_0" y="790.4412277029879" zvalue="9820"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450170257413" ObjectName="10kV弄冒线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1495.68,802.498) scale(-0.838049,0.927421) translate(-3281.36,61.8591)" width="12" x="1490.647243611278" y="790.4412277029879"/></g>
  <g id="102">
   <use class="kv10" height="26" transform="rotate(0,1575.68,802.498) scale(-0.838049,0.927421) translate(-3456.82,61.8591)" width="12" x="1570.647243611278" xlink:href="#Accessory:避雷器1_0" y="790.4412277029879" zvalue="9841"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454435536899" ObjectName="10kV芒面线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1575.68,802.498) scale(-0.838049,0.927421) translate(-3456.82,61.8591)" width="12" x="1570.647243611278" y="790.4412277029879"/></g>
  <g id="846">
   <use class="kv10" height="26" transform="rotate(0,1651.68,802.498) scale(-0.838049,0.927421) translate(-3623.51,61.8591)" width="12" x="1646.6472130937" xlink:href="#Accessory:避雷器1_0" y="790.4412277029879" zvalue="9854"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454435930115" ObjectName="10kV1号接地变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1651.68,802.498) scale(-0.838049,0.927421) translate(-3623.51,61.8591)" width="12" x="1646.6472130937" y="790.4412277029879"/></g>
  <g id="986">
   <use class="kv10" height="26" transform="rotate(0,921.572,796.136) scale(-0.838049,0.927421) translate(-2022.21,61.3612)" width="12" x="916.5433225384542" xlink:href="#Accessory:避雷器1_0" y="784.0794592077049" zvalue="9875"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454437371907" ObjectName="10kV#2电容器避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,921.572,796.136) scale(-0.838049,0.927421) translate(-2022.21,61.3612)" width="12" x="916.5433225384542" y="784.0794592077049"/></g>
  <g id="971">
   <use class="kv10" height="26" transform="rotate(0,777.39,878.552) scale(-0.838049,0.927421) translate(-1705.98,67.8111)" width="12" x="772.3615293255634" xlink:href="#Accessory:避雷器1_0" y="866.495966701401" zvalue="9894"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454437109763" ObjectName="10kV景罕糖厂Ⅰ回线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,777.39,878.552) scale(-0.838049,0.927421) translate(-1705.98,67.8111)" width="12" x="772.3615293255634" y="866.495966701401"/></g>
  <g id="956">
   <use class="kv10" height="26" transform="rotate(0,997.39,802.498) scale(-0.838049,0.927421) translate(-2188.49,61.8591)" width="12" x="992.3614988079853" xlink:href="#Accessory:避雷器1_0" y="790.4412277029878" zvalue="9909"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454436847619" ObjectName="10kV2号站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,997.39,802.498) scale(-0.838049,0.927421) translate(-2188.49,61.8591)" width="12" x="992.3614988079853" y="790.4412277029878"/></g>
  <g id="940">
   <use class="kv10" height="26" transform="rotate(0,769.39,796.136) scale(-0.838049,0.927421) translate(-1688.43,61.3612)" width="12" x="764.3615293255634" xlink:href="#Accessory:避雷器1_0" y="784.0793009004516" zvalue="9920"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454436716547" ObjectName="10kV景罕糖厂Ⅰ回线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,769.39,796.136) scale(-0.838049,0.927421) translate(-1688.43,61.3612)" width="12" x="764.3615293255634" y="784.0793009004516"/></g>
  <g id="926">
   <use class="kv10" height="26" transform="rotate(0,701.39,878.552) scale(-0.838049,0.927421) translate(-1539.29,67.8111)" width="12" x="696.3615293255634" xlink:href="#Accessory:避雷器1_0" y="866.495966701401" zvalue="9934"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450170781701" ObjectName="10kV弄贯线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,701.39,878.552) scale(-0.838049,0.927421) translate(-1539.29,67.8111)" width="12" x="696.3615293255634" y="866.495966701401"/></g>
  <g id="922">
   <use class="kv10" height="26" transform="rotate(0,693.39,796.136) scale(-0.838049,0.927421) translate(-1521.75,61.3612)" width="12" x="688.3615293255634" xlink:href="#Accessory:避雷器1_0" y="784.0793009004516" zvalue="9939"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450170650629" ObjectName="10kV弄贯线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,693.39,796.136) scale(-0.838049,0.927421) translate(-1521.75,61.3612)" width="12" x="688.3615293255634" y="784.0793009004516"/></g>
  <g id="890">
   <use class="kv10" height="26" transform="rotate(0,461.39,802.498) scale(-0.838049,0.927421) translate(-1012.91,61.8591)" width="12" x="456.3614988079854" xlink:href="#Accessory:避雷器1_0" y="790.4412277029878" zvalue="9971"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454478856195" ObjectName="10kV2号接地变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,461.39,802.498) scale(-0.838049,0.927421) translate(-1012.91,61.8591)" width="12" x="456.3614988079854" y="790.4412277029878"/></g>
  <g id="23">
   <use class="kv35" height="26" transform="rotate(270,1782.72,150.821) scale(0.838049,0.927421) translate(343.536,10.8596)" width="12" x="1777.694847593032" xlink:href="#Accessory:避雷器1_0" y="138.7649483190753" zvalue="10001"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454437634051" ObjectName="35kV景勐线372避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1782.72,150.821) scale(0.838049,0.927421) translate(343.536,10.8596)" width="12" x="1777.694847593032" y="138.7649483190753"/></g>
  <g id="195">
   <use class="kv35" height="18" transform="rotate(270,1773.4,258.381) scale(-1.38489,1.23553) translate(-3051.24,-47.1364)" width="14" x="1763.701997798714" xlink:href="#Accessory:PT7_0" y="247.2612526748508" zvalue="10017"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454438420483" ObjectName="35kV景陇线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(270,1773.4,258.381) scale(-1.38489,1.23553) translate(-3051.24,-47.1364)" width="14" x="1763.701997798714" y="247.2612526748508"/></g>
  <g id="182">
   <use class="kv35" height="26" transform="rotate(270,1782.72,278.821) scale(0.838049,0.927421) translate(343.536,20.8767)" width="12" x="1777.694847593032" xlink:href="#Accessory:避雷器1_0" y="266.7649483190753" zvalue="10024"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454438354947" ObjectName="35kV景陇线371避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1782.72,278.821) scale(0.838049,0.927421) translate(343.536,20.8767)" width="12" x="1777.694847593032" y="266.7649483190753"/></g>
  <g id="351">
   <use class="kv35" height="18" transform="rotate(270,1773.4,406.381) scale(-1.38489,1.23553) translate(-3051.24,-75.3502)" width="14" x="1763.701997798714" xlink:href="#Accessory:PT7_0" y="395.2612526748508" zvalue="10083"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454439075843" ObjectName="35kV景安线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(270,1773.4,406.381) scale(-1.38489,1.23553) translate(-3051.24,-75.3502)" width="14" x="1763.701997798714" y="395.2612526748508"/></g>
  <g id="334">
   <use class="kv35" height="26" transform="rotate(270,1782.72,426.821) scale(0.838049,0.927421) translate(343.536,32.4591)" width="12" x="1777.694847593032" xlink:href="#Accessory:避雷器1_0" y="414.7649483190752" zvalue="10090"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454439010307" ObjectName="35kV景安线381避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1782.72,426.821) scale(0.838049,0.927421) translate(343.536,32.4591)" width="12" x="1777.694847593032" y="414.7649483190752"/></g>
  <g id="453">
   <use class="kv35" height="18" transform="rotate(270,1773.4,530.381) scale(-1.38489,1.23553) translate(-3051.24,-98.9888)" width="14" x="1763.701997798714" xlink:href="#Accessory:PT7_0" y="519.2612526748507" zvalue="10106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454439534595" ObjectName="35kV景城线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(270,1773.4,530.381) scale(-1.38489,1.23553) translate(-3051.24,-98.9888)" width="14" x="1763.701997798714" y="519.2612526748507"/></g>
  <g id="447">
   <use class="kv35" height="26" transform="rotate(270,1782.72,550.821) scale(0.838049,0.927421) translate(343.536,42.1632)" width="12" x="1777.694847593032" xlink:href="#Accessory:避雷器1_0" y="538.7649483190752" zvalue="10113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454439469059" ObjectName="35kV景城线382避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1782.72,550.821) scale(0.838049,0.927421) translate(343.536,42.1632)" width="12" x="1777.694847593032" y="538.7649483190752"/></g>
  <g id="563">
   <use class="kv35" height="18" transform="rotate(270,1773.4,674.381) scale(-1.38489,1.23553) translate(-3051.24,-126.44)" width="14" x="1763.701997798714" xlink:href="#Accessory:PT7_0" y="663.2612526748508" zvalue="10129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454476169219" ObjectName="35kV章景线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(270,1773.4,674.381) scale(-1.38489,1.23553) translate(-3051.24,-126.44)" width="14" x="1763.701997798714" y="663.2612526748508"/></g>
  <g id="524">
   <use class="kv35" height="26" transform="rotate(270,1782.72,694.821) scale(0.838049,0.927421) translate(343.536,53.4325)" width="12" x="1777.694847593032" xlink:href="#Accessory:避雷器1_0" y="682.7649483190753" zvalue="10136"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454476103683" ObjectName="35kV章景线383避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1782.72,694.821) scale(0.838049,0.927421) translate(343.536,53.4325)" width="12" x="1777.694847593032" y="682.7649483190753"/></g>
  <g id="327">
   <use class="kv10" height="26" transform="rotate(0,1328.34,807.091) scale(-0.838049,0.927421) translate(-2914.35,62.2186)" width="12" x="1323.309195554327" xlink:href="#Accessory:避雷器1_0" y="795.034639691999" zvalue="10320"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454440845315" ObjectName="10kV1号电容器避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1328.34,807.091) scale(-0.838049,0.927421) translate(-2914.35,62.2186)" width="12" x="1323.309195554327" y="795.034639691999"/></g>
  <g id="364">
   <use class="kv10" height="42" transform="rotate(0,1444.28,586.713) scale(1.26667,1.13976) translate(-300.06,-69.0113)" width="30" x="1425.283198672493" xlink:href="#Accessory:5卷PT带容断器_0" y="562.7777802982027" zvalue="10362"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454441041923" ObjectName="10kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1444.28,586.713) scale(1.26667,1.13976) translate(-300.06,-69.0113)" width="30" x="1425.283198672493" y="562.7777802982027"/></g>
  <g id="369">
   <use class="kv10" height="42" transform="rotate(0,794.644,588.935) scale(1.26667,1.13976) translate(-163.294,-69.2838)" width="30" x="775.6444450781936" xlink:href="#Accessory:5卷PT带容断器_0" y="565.0000025761592" zvalue="10368"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454441107459" ObjectName="10kVⅡ母电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,794.644,588.935) scale(1.26667,1.13976) translate(-163.294,-69.2838)" width="30" x="775.6444450781936" y="565.0000025761592"/></g>
  <g id="379">
   <use class="kv110" height="35" transform="rotate(0,1330.57,208.571) scale(1.42857,1.42857) translate(-392.743,-55.0714)" width="30" x="1309.142857142857" xlink:href="#Accessory:4绕组PT带接地_0" y="183.5714285714286" zvalue="10370"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454441238531" ObjectName="110kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1330.57,208.571) scale(1.42857,1.42857) translate(-392.743,-55.0714)" width="30" x="1309.142857142857" y="183.5714285714286"/></g>
  <g id="382">
   <use class="kv110" height="35" transform="rotate(0,684.286,210) scale(1.42857,1.42857) translate(-198.857,-55.5)" width="30" x="662.8571428571431" xlink:href="#Accessory:4绕组PT带接地_0" y="185" zvalue="10372"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454441304067" ObjectName="110kVⅡ母电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,684.286,210) scale(1.42857,1.42857) translate(-198.857,-55.5)" width="30" x="662.8571428571431" y="185"/></g>
  <g id="414">
   <use class="kv35" height="42" transform="rotate(270,1574.4,222.486) scale(1.15714,1.21716) translate(-211.45,-35.1348)" width="30" x="1557.042846362622" xlink:href="#Accessory:4卷PT带容断器_0" y="196.9257109718634" zvalue="10395"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454441631747" ObjectName="35kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(270,1574.4,222.486) scale(1.15714,1.21716) translate(-211.45,-35.1348)" width="30" x="1557.042846362622" y="196.9257109718634"/></g>
  <g id="427">
   <use class="kv35" height="42" transform="rotate(270,1574.4,517.643) scale(1.15714,1.21716) translate(-211.45,-87.7955)" width="30" x="1557.042846362622" xlink:href="#Accessory:4卷PT带容断器_0" y="492.0824675324677" zvalue="10402"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454441828355" ObjectName="35kVⅡ母电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(270,1574.4,517.643) scale(1.15714,1.21716) translate(-211.45,-87.7955)" width="30" x="1557.042846362622" y="492.0824675324677"/></g>
  <g id="470">
   <use class="kv10" height="26" transform="rotate(0,851.39,878.552) scale(-0.838049,0.927421) translate(-1868.28,67.8111)" width="12" x="846.3615293255634" xlink:href="#Accessory:避雷器1_0" y="866.495966701401" zvalue="10526"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454476431363" ObjectName="10kV陇川机场Ⅰ回线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,851.39,878.552) scale(-0.838049,0.927421) translate(-1868.28,67.8111)" width="12" x="846.3615293255634" y="866.495966701401"/></g>
  <g id="466">
   <use class="kv10" height="26" transform="rotate(0,843.39,796.136) scale(-0.838049,0.927421) translate(-1850.73,61.3612)" width="12" x="838.3615293255634" xlink:href="#Accessory:避雷器1_0" y="784.0793009004516" zvalue="10531"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454476300291" ObjectName="10kV陇川机场Ⅰ回线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,843.39,796.136) scale(-0.838049,0.927421) translate(-1850.73,61.3612)" width="12" x="838.3615293255634" y="784.0793009004516"/></g>
  <g id="448">
   <use class="kv10" height="26" transform="rotate(0,421,925) scale(1,1) translate(0,0)" width="12" x="415" xlink:href="#Accessory:接地变接地设备_0" y="912" zvalue="10688"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449514962949" ObjectName="2号接地变接地"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,421,925) scale(1,1) translate(0,0)" width="12" x="415" y="912"/></g>
  <g id="456">
   <use class="kv10" height="26" transform="rotate(0,1665,925) scale(1,1) translate(0,0)" width="12" x="1659" xlink:href="#Accessory:接地变接地设备_0" y="912" zvalue="10690"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449515028485" ObjectName="1号接地变接地"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1665,925) scale(1,1) translate(0,0)" width="12" x="1659" y="912"/></g>
  <g id="619">
   <use class="kv10" height="26" transform="rotate(0,534.39,796.136) scale(-0.838049,0.927421) translate(-1173.02,61.3612)" width="12" x="529.3615293255634" xlink:href="#Accessory:避雷器1_0" y="784.0793009004516" zvalue="10890"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450169470981" ObjectName="10kV057避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,534.39,796.136) scale(-0.838049,0.927421) translate(-1173.02,61.3612)" width="12" x="529.3615293255634" y="784.0793009004516"/></g>
  <g id="646">
   <use class="kv10" height="26" transform="rotate(0,623.39,878.552) scale(-0.838049,0.927421) translate(-1368.22,67.8111)" width="12" x="618.3615293255634" xlink:href="#Accessory:避雷器1_0" y="866.495966701401" zvalue="10902"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450169995269" ObjectName="10kV056避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,623.39,878.552) scale(-0.838049,0.927421) translate(-1368.22,67.8111)" width="12" x="618.3615293255634" y="866.495966701401"/></g>
  <g id="640">
   <use class="kv10" height="26" transform="rotate(0,615.39,796.136) scale(-0.838049,0.927421) translate(-1350.67,61.3612)" width="12" x="610.3615293255634" xlink:href="#Accessory:避雷器1_0" y="784.0793009004516" zvalue="10907"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450169864197" ObjectName="10kV056避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,615.39,796.136) scale(-0.838049,0.927421) translate(-1350.67,61.3612)" width="12" x="610.3615293255634" y="784.0793009004516"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="552">
   <use class="kv10" height="30" transform="rotate(0,1396.57,900.083) scale(1.25,-1.25) translate(-277.814,-1616.4)" width="12" x="1389.072233654204" xlink:href="#EnergyConsumer:负荷_0" y="881.3333333333335" zvalue="9424"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454433570819" ObjectName="10kV景罕镇线"/>
   <cge:TPSR_Ref TObjectID="6192454433570819"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1396.57,900.083) scale(1.25,-1.25) translate(-277.814,-1616.4)" width="12" x="1389.072233654204" y="881.3333333333335"/></g>
  <g id="49">
   <use class="kv10" height="30" transform="rotate(0,1240.12,877.369) scale(1.53571,1.53571) translate(-425.1,-298.023)" width="28" x="1218.619047619048" xlink:href="#EnergyConsumer:站用变DY接地_0" y="854.3333333333333" zvalue="9785"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454435012611" ObjectName="10kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1240.12,877.369) scale(1.53571,1.53571) translate(-425.1,-298.023)" width="28" x="1218.619047619048" y="854.3333333333333"/></g>
  <g id="81">
   <use class="kv10" height="30" transform="rotate(0,1480.57,900.083) scale(1.25,-1.25) translate(-294.614,-1616.4)" width="12" x="1473.072233654204" xlink:href="#EnergyConsumer:负荷_0" y="881.3333333333335" zvalue="9816"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450170322949" ObjectName="10kV弄冒线"/>
   <cge:TPSR_Ref TObjectID="6192450170322949"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1480.57,900.083) scale(1.25,-1.25) translate(-294.614,-1616.4)" width="12" x="1473.072233654204" y="881.3333333333335"/></g>
  <g id="219">
   <use class="kv10" height="30" transform="rotate(0,1560.57,900.083) scale(1.25,-1.25) translate(-310.614,-1616.4)" width="12" x="1553.072233654204" xlink:href="#EnergyConsumer:负荷_0" y="881.3333333333335" zvalue="9837"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454435602435" ObjectName="10kV芒面线"/>
   <cge:TPSR_Ref TObjectID="6192454435602435"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1560.57,900.083) scale(1.25,-1.25) translate(-310.614,-1616.4)" width="12" x="1553.072233654204" y="881.3333333333335"/></g>
  <g id="949">
   <use class="kv10" height="30" transform="rotate(0,981.833,877.369) scale(1.53571,1.53571) translate(-335,-298.023)" width="28" x="960.3333333333334" xlink:href="#EnergyConsumer:站用变DY接地_0" y="854.3333333333333" zvalue="9913"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454436782083" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,981.833,877.369) scale(1.53571,1.53571) translate(-335,-298.023)" width="28" x="960.3333333333334" y="854.3333333333333"/></g>
  <g id="925">
   <use class="kv10" height="30" transform="rotate(0,678.287,900.083) scale(1.25,-1.25) translate(-134.157,-1616.4)" width="12" x="670.7865193684896" xlink:href="#EnergyConsumer:负荷_0" y="881.3333333333333" zvalue="9935"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450170716165" ObjectName="10kV弄贯线"/>
   <cge:TPSR_Ref TObjectID="6192450170716165"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,678.287,900.083) scale(1.25,-1.25) translate(-134.157,-1616.4)" width="12" x="670.7865193684896" y="881.3333333333333"/></g>
  <g id="279">
   <use class="kv10" height="35" transform="rotate(0,1638.27,880.5) scale(1.3294,1.51429) translate(-400.666,-290.038)" width="32" x="1617" xlink:href="#EnergyConsumer:站用变13_0" y="854" zvalue="10486"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454442418179" ObjectName="10kV1号接地变"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1638.27,880.5) scale(1.3294,1.51429) translate(-400.666,-290.038)" width="32" x="1617" y="854"/></g>
  <g id="306">
   <use class="kv10" height="35" transform="rotate(0,446.975,881) scale(1.50159,1.54286) translate(-141.282,-300.481)" width="32" x="422.9490499614199" xlink:href="#EnergyConsumer:站用变13_0" y="854.0000000000002" zvalue="10488"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454441369603" ObjectName="10kV2号接地变"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,446.975,881) scale(1.50159,1.54286) translate(-141.282,-300.481)" width="32" x="422.9490499614199" y="854.0000000000002"/></g>
  <g id="469">
   <use class="kv10" height="30" transform="rotate(0,828.287,900.083) scale(1.25,-1.25) translate(-164.157,-1616.4)" width="12" x="820.7865193684896" xlink:href="#EnergyConsumer:负荷_0" y="881.3333333333333" zvalue="10527"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454476365827" ObjectName="10kV陇川机场Ⅰ回线"/>
   <cge:TPSR_Ref TObjectID="6192454476365827"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,828.287,900.083) scale(1.25,-1.25) translate(-164.157,-1616.4)" width="12" x="820.7865193684896" y="881.3333333333333"/></g>
  <g id="622">
   <use class="kv10" height="30" transform="rotate(0,519.287,900.083) scale(1.25,-1.25) translate(-102.357,-1616.4)" width="12" x="511.7865193684896" xlink:href="#EnergyConsumer:负荷_0" y="881.3333333333333" zvalue="10886"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450171109381" ObjectName="10kV芒洪线"/>
   <cge:TPSR_Ref TObjectID="6192450171109381"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,519.287,900.083) scale(1.25,-1.25) translate(-102.357,-1616.4)" width="12" x="511.7865193684896" y="881.3333333333333"/></g>
  <g id="645">
   <use class="kv10" height="30" transform="rotate(0,600.287,900.083) scale(1.25,-1.25) translate(-118.557,-1616.4)" width="12" x="592.7865193684896" xlink:href="#EnergyConsumer:负荷_0" y="881.3333333333333" zvalue="10903"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450171502597" ObjectName="10kV广母线"/>
   <cge:TPSR_Ref TObjectID="6192450171502597"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,600.287,900.083) scale(1.25,-1.25) translate(-118.557,-1616.4)" width="12" x="592.7865193684896" y="881.3333333333333"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="341">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="341" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,88.5,475.583) scale(1,1) translate(0,-1.02825e-13)" writing-mode="lr" x="88.27" xml:space="preserve" y="480.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135219539970" ObjectName=""/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,87.5,503.833) scale(1,1) translate(0,-1.09098e-13)" writing-mode="lr" x="87.27" xml:space="preserve" y="508.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135219277826" ObjectName=""/>
   </metadata>
  </g>
  <g id="1032">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1032" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,87.5,531.083) scale(1,1) translate(0,0)" writing-mode="lr" x="87.27" xml:space="preserve" y="535.78" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135219343362" ObjectName=""/>
   </metadata>
  </g>
  <g id="1033">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="1033" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,87.5,558.333) scale(1,1) translate(0,6.05997e-14)" writing-mode="lr" x="87.27" xml:space="preserve" y="563.03" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135219408898" ObjectName=""/>
   </metadata>
  </g>
  <g id="1034">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="1034" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,87.5,584.5) scale(1,1) translate(0,0)" writing-mode="lr" x="87.27" xml:space="preserve" y="589.2" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135219736578" ObjectName=""/>
   </metadata>
  </g>
  <g id="1040">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="1040" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,193.5,476.583) scale(1,1) translate(0,0)" writing-mode="lr" x="193.27" xml:space="preserve" y="481.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135221112834" ObjectName=""/>
   </metadata>
  </g>
  <g id="1041">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1041" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,193.5,503.833) scale(1,1) translate(0,-1.09098e-13)" writing-mode="lr" x="193.27" xml:space="preserve" y="508.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135220850690" ObjectName=""/>
   </metadata>
  </g>
  <g id="1042">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1042" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,193.5,531.083) scale(1,1) translate(0,0)" writing-mode="lr" x="193.27" xml:space="preserve" y="535.78" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135220916226" ObjectName=""/>
   </metadata>
  </g>
  <g id="1043">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="1043" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,193.5,558.333) scale(1,1) translate(0,6.05997e-14)" writing-mode="lr" x="193.27" xml:space="preserve" y="563.03" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135220981762" ObjectName=""/>
   </metadata>
  </g>
  <g id="1044">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="1044" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,193.5,585.583) scale(1,1) translate(0,1.90875e-13)" writing-mode="lr" x="193.27" xml:space="preserve" y="590.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135221309442" ObjectName=""/>
   </metadata>
  </g>
  <g id="1050">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="1050" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,295,476.583) scale(1,1) translate(0,0)" writing-mode="lr" x="294.77" xml:space="preserve" y="481.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135220064258" ObjectName=""/>
   </metadata>
  </g>
  <g id="1051">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1051" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,295,503.833) scale(1,1) translate(0,-1.09098e-13)" writing-mode="lr" x="294.77" xml:space="preserve" y="508.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135219802114" ObjectName=""/>
   </metadata>
  </g>
  <g id="1052">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1052" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,295,531.083) scale(1,1) translate(0,0)" writing-mode="lr" x="294.77" xml:space="preserve" y="535.78" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135219867650" ObjectName=""/>
   </metadata>
  </g>
  <g id="1053">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="1053" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,295,558.333) scale(1,1) translate(0,6.05997e-14)" writing-mode="lr" x="294.77" xml:space="preserve" y="563.03" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135219933186" ObjectName=""/>
   </metadata>
  </g>
  <g id="1054">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="1054" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,295,585.583) scale(1,1) translate(0,-1.2725e-13)" writing-mode="lr" x="294.77" xml:space="preserve" y="590.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135220260866" ObjectName=""/>
   </metadata>
  </g>
  <g id="1060">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="1060" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,139.5,476.583) scale(1,1) translate(0,0)" writing-mode="lr" x="139.27" xml:space="preserve" y="481.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135222685698" ObjectName=""/>
   </metadata>
  </g>
  <g id="1061">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1061" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,139.5,503.833) scale(1,1) translate(0,-1.09098e-13)" writing-mode="lr" x="139.27" xml:space="preserve" y="508.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135222423554" ObjectName=""/>
   </metadata>
  </g>
  <g id="1062">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1062" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,139.5,531.083) scale(1,1) translate(0,0)" writing-mode="lr" x="139.27" xml:space="preserve" y="535.78" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135222489090" ObjectName=""/>
   </metadata>
  </g>
  <g id="1063">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="1063" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,139.5,557.333) scale(1,1) translate(0,6.04887e-14)" writing-mode="lr" x="139.27" xml:space="preserve" y="562.03" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135222554626" ObjectName=""/>
   </metadata>
  </g>
  <g id="1064">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="1064" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,139.5,585.583) scale(1,1) translate(0,1.90875e-13)" writing-mode="lr" x="139.27" xml:space="preserve" y="590.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135222882306" ObjectName=""/>
   </metadata>
  </g>
  <g id="1065">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="1065" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,243.5,476.583) scale(1,1) translate(0,0)" writing-mode="lr" x="243.27" xml:space="preserve" y="481.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135221637122" ObjectName=""/>
   </metadata>
  </g>
  <g id="1066">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1066" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,243.5,503.833) scale(1,1) translate(0,-1.09098e-13)" writing-mode="lr" x="243.27" xml:space="preserve" y="508.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135221374978" ObjectName=""/>
   </metadata>
  </g>
  <g id="1067">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1067" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,243.5,531.083) scale(1,1) translate(0,0)" writing-mode="lr" x="243.27" xml:space="preserve" y="535.78" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135221440514" ObjectName=""/>
   </metadata>
  </g>
  <g id="1068">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="1068" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,243.5,558.333) scale(1,1) translate(0,6.05997e-14)" writing-mode="lr" x="243.27" xml:space="preserve" y="563.03" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135221506050" ObjectName=""/>
   </metadata>
  </g>
  <g id="1069">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="1069" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,243.5,585.583) scale(1,1) translate(0,-1.2725e-13)" writing-mode="lr" x="243.27" xml:space="preserve" y="590.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135221833730" ObjectName=""/>
   </metadata>
  </g>
  <g id="1070">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="1070" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,348,476.583) scale(1,1) translate(0,0)" writing-mode="lr" x="347.77" xml:space="preserve" y="481.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135229501442" ObjectName=""/>
   </metadata>
  </g>
  <g id="1071">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1071" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,348,503.833) scale(1,1) translate(0,-1.09098e-13)" writing-mode="lr" x="347.77" xml:space="preserve" y="508.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135229239298" ObjectName=""/>
   </metadata>
  </g>
  <g id="1072">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1072" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,348,531.083) scale(1,1) translate(0,0)" writing-mode="lr" x="347.77" xml:space="preserve" y="535.78" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135229304834" ObjectName=""/>
   </metadata>
  </g>
  <g id="1073">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="1073" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,348,558.333) scale(1,1) translate(0,6.05997e-14)" writing-mode="lr" x="347.77" xml:space="preserve" y="563.03" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135229370370" ObjectName=""/>
   </metadata>
  </g>
  <g id="1074">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="1074" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,348,585.583) scale(1,1) translate(0,-1.2725e-13)" writing-mode="lr" x="347.77" xml:space="preserve" y="590.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135229698050" ObjectName=""/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="17" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,155,176) scale(1,1) translate(0,0)" writing-mode="lr" x="154.64" xml:space="preserve" y="182.21" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135256502274" ObjectName=""/>
   </metadata>
  </g>
  <g id="1035">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="1035" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,334,175.333) scale(1,1) translate(0,7.20905e-14)" writing-mode="lr" x="333.64" xml:space="preserve" y="181.54" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135256567810" ObjectName=""/>
   </metadata>
  </g>
  <g id="1036">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="16" id="1036" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,155,199.333) scale(1,1) translate(0,8.27486e-14)" writing-mode="lr" x="154.64" xml:space="preserve" y="205.54" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135219671042" ObjectName=""/>
   </metadata>
  </g>
  <g id="1037">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="16" id="1037" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,335,200.333) scale(1,1) translate(0,8.31927e-14)" writing-mode="lr" x="334.64" xml:space="preserve" y="206.54" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135222816770" ObjectName=""/>
   </metadata>
  </g>
  <g id="1047">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="1047" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,156,296.333) scale(1,1) translate(0,1.25825e-13)" writing-mode="lr" x="155.64" xml:space="preserve" y="302.54" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135223668738" ObjectName=""/>
   </metadata>
  </g>
  <g id="1048">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="1048" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,335,296.333) scale(1,1) translate(0,1.25825e-13)" writing-mode="lr" x="334.64" xml:space="preserve" y="302.54" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135226290178" ObjectName=""/>
   </metadata>
  </g>
  <g id="1038">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="16" id="1038" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,156,225.333) scale(1,1) translate(0,9.42949e-14)" writing-mode="lr" x="155.64" xml:space="preserve" y="231.54" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135221243906" ObjectName=""/>
   </metadata>
  </g>
  <g id="1039">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="16" id="1039" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,335,225.333) scale(1,1) translate(0,9.42949e-14)" writing-mode="lr" x="334.64" xml:space="preserve" y="231.54" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135221243906" ObjectName=""/>
   </metadata>
  </g>
  <g id="1045">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="16" id="1045" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,157.25,271.833) scale(1,1) translate(0,1.14945e-13)" writing-mode="lr" x="156.89" xml:space="preserve" y="278.04" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135223930882" ObjectName=""/>
   </metadata>
  </g>
  <g id="1046">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="16" id="1046" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,336.25,272.833) scale(1,1) translate(0,2.88473e-13)" writing-mode="lr" x="335.89" xml:space="preserve" y="279.04" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135226552322" ObjectName=""/>
   </metadata>
  </g>
  <g id="1049">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="16" id="1049" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,156,247.333) scale(1,1) translate(0,1.04065e-13)" writing-mode="lr" x="155.64" xml:space="preserve" y="253.54" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135220195330" ObjectName=""/>
   </metadata>
  </g>
  <g id="1055">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="16" id="1055" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,335,247.333) scale(1,1) translate(0,1.04065e-13)" writing-mode="lr" x="334.64" xml:space="preserve" y="253.54" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135229632514" ObjectName=""/>
   </metadata>
  </g>
  <g id="60">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="60" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1065.17,16.2289) scale(1,1) translate(1.13262e-13,-1.52648e-14)" writing-mode="lr" x="1064.71" xml:space="preserve" y="20.9" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128141062148" ObjectName="P"/>
   </metadata>
  </g>
  <g id="61">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="61" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,532.889,14.6455) scale(1,1) translate(0,0)" writing-mode="lr" x="532.42" xml:space="preserve" y="19.31" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135232253954" ObjectName="P"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="62" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1861.96,177.513) scale(1,1) translate(0,0)" writing-mode="lr" x="1861.5" xml:space="preserve" y="182.18" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135243395074" ObjectName="P"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="63" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1862.87,306.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1862.4" xml:space="preserve" y="310.81" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135244443650" ObjectName="P"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="103" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1863.84,463.187) scale(1,1) translate(0,0)" writing-mode="lr" x="1863.37" xml:space="preserve" y="467.85" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135246540802" ObjectName="P"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="104" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1864.95,583.389) scale(1,1) translate(0,-2.54414e-13)" writing-mode="lr" x="1864.49" xml:space="preserve" y="588.0599999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135248113666" ObjectName="P"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="105" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1859.54,729.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1859.07" xml:space="preserve" y="734.58" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135376629762" ObjectName="P"/>
   </metadata>
  </g>
  <g id="109">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="109" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1065.17,35.2289) scale(1,1) translate(1.13262e-13,0)" writing-mode="lr" x="1064.71" xml:space="preserve" y="39.9" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128141127684" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="111" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,531.639,32.3955) scale(1,1) translate(0,0)" writing-mode="lr" x="531.17" xml:space="preserve" y="37.06" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135232319490" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="113">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="113" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1861.96,192.513) scale(1,1) translate(0,0)" writing-mode="lr" x="1861.5" xml:space="preserve" y="197.18" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135243460610" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="120">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="120" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1862.87,321.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1862.4" xml:space="preserve" y="325.81" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135244509186" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="121">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="121" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1863.84,478.187) scale(1,1) translate(0,0)" writing-mode="lr" x="1863.37" xml:space="preserve" y="482.85" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135246606338" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="123">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="123" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1866.2,597.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1865.74" xml:space="preserve" y="601.8099999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135248179202" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="127">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="127" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1859.54,744.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1859.07" xml:space="preserve" y="749.58" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135376695298" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="128">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="128" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1064.17,53.2289) scale(1,1) translate(1.13151e-13,0)" writing-mode="lr" x="1063.71" xml:space="preserve" y="57.9" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128141193220" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="184">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="184" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,532.889,52.6455) scale(1,1) translate(0,0)" writing-mode="lr" x="532.42" xml:space="preserve" y="57.31" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135232385026" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="191">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="191" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1861.96,207.513) scale(1,1) translate(0,0)" writing-mode="lr" x="1861.5" xml:space="preserve" y="212.18" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135243526146" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="197">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="197" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1862.87,336.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1862.4" xml:space="preserve" y="340.81" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135244574722" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="198">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="198" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1862.59,494.437) scale(1,1) translate(0,0)" writing-mode="lr" x="1862.12" xml:space="preserve" y="499.1" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135246671874" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="199">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="199" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1866.2,612.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1865.74" xml:space="preserve" y="616.8099999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135248244738" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="204">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="204" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1859.54,759.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1859.07" xml:space="preserve" y="764.58" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135376760834" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="206">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="206" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,912.401,183.177) scale(1,1) translate(0,0)" writing-mode="lr" x="911.9299999999999" xml:space="preserve" y="187.84" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135251259394" ObjectName="P"/>
   </metadata>
  </g>
  <g id="207">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="207" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1113.71,812.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1113.25" xml:space="preserve" y="817.24" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135253422082" ObjectName="P"/>
   </metadata>
  </g>
  <g id="210">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="210" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1551.79,363) scale(1,1) translate(0,0)" writing-mode="lr" x="1551.33" xml:space="preserve" y="367.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135378202626" ObjectName="P"/>
   </metadata>
  </g>
  <g id="229">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="229" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,912.401,202.177) scale(1,1) translate(0,0)" writing-mode="lr" x="911.9299999999999" xml:space="preserve" y="206.84" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135251324930" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="230">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="230" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1113.71,831.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1113.25" xml:space="preserve" y="836.24" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135253487618" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="231">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="231" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1551.79,386) scale(1,1) translate(0,0)" writing-mode="lr" x="1551.33" xml:space="preserve" y="390.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135378268162" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="232">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="232" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,912.401,224.177) scale(1,1) translate(0,0)" writing-mode="lr" x="911.9299999999999" xml:space="preserve" y="228.84" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135251390466" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="233">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="233" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1113.71,848.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1113.25" xml:space="preserve" y="853.24" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135253553154" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1550.79,408) scale(1,1) translate(0,2.64788e-13)" writing-mode="lr" x="1550.33" xml:space="preserve" y="412.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135378333698" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="244" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1395.32,991.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1394.85" xml:space="preserve" y="996.3099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135230812162" ObjectName="P"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="246" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1480.57,991.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1480.1" xml:space="preserve" y="996.3099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127474167812" ObjectName="P"/>
   </metadata>
  </g>
  <g id="251">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="251" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1560.57,991.643) scale(1,1) translate(-1.00957e-12,0)" writing-mode="lr" x="1560.1" xml:space="preserve" y="996.3099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135235858434" ObjectName="P"/>
   </metadata>
  </g>
  <g id="254">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="254" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,677.037,991.643) scale(1,1) translate(0,0)" writing-mode="lr" x="676.5700000000001" xml:space="preserve" y="996.3099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127475609604" ObjectName="P"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="256" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1396.57,1009.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1396.1" xml:space="preserve" y="1014.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135230877698" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1480.57,1009.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1480.1" xml:space="preserve" y="1014.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127474233348" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="280">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="280" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1560.57,1009.5) scale(1,1) translate(-1.00957e-12,0)" writing-mode="lr" x="1560.1" xml:space="preserve" y="1014.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135235923970" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="282">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="282" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,677.037,1009.5) scale(1,1) translate(0,0)" writing-mode="lr" x="676.5700000000001" xml:space="preserve" y="1014.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127475675140" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="288">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="288" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1396.57,1028.21) scale(1,1) translate(0,0)" writing-mode="lr" x="1396.1" xml:space="preserve" y="1032.88" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135230943234" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="289">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="289" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1480.57,1028.21) scale(1,1) translate(0,0)" writing-mode="lr" x="1480.1" xml:space="preserve" y="1032.88" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127474298884" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="296">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="296" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1560.57,1028.21) scale(1,1) translate(-1.00957e-12,0)" writing-mode="lr" x="1560.1" xml:space="preserve" y="1032.88" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135235989506" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="297">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="297" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,677.037,1027.21) scale(1,1) translate(0,0)" writing-mode="lr" x="676.5700000000001" xml:space="preserve" y="1031.88" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127475740676" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="299">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="299" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1310.82,1010.86) scale(1,1) translate(0,0)" writing-mode="lr" x="1310.35" xml:space="preserve" y="1015.53" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135252176898" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="300">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="300" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,906.447,1010.9) scale(1,1) translate(0,0)" writing-mode="lr" x="905.98" xml:space="preserve" y="1015.57" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135255257090" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="301">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="301" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1310.82,1029.57) scale(1,1) translate(0,-2.71536e-12)" writing-mode="lr" x="1310.35" xml:space="preserve" y="1034.24" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135252242434" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="303">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="303" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,906.447,1029.62) scale(1,1) translate(0,0)" writing-mode="lr" x="905.98" xml:space="preserve" y="1034.29" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135255322626" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="317">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="317" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,428.686,991.643) scale(1,1) translate(0,0)" writing-mode="lr" x="428.22" xml:space="preserve" y="996.3099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135257681922" ObjectName="P"/>
   </metadata>
  </g>
  <g id="319">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="319" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1640.3,993.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1639.84" xml:space="preserve" y="997.8099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135256764418" ObjectName="P"/>
   </metadata>
  </g>
  <g id="321">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="321" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,427.257,1009.5) scale(1,1) translate(0,0)" writing-mode="lr" x="426.79" xml:space="preserve" y="1014.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135257747458" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="328">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="328" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1640.3,1011) scale(1,1) translate(0,0)" writing-mode="lr" x="1639.84" xml:space="preserve" y="1015.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135256829954" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="331">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="331" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,424.4,1028.21) scale(1,1) translate(0,0)" writing-mode="lr" x="423.93" xml:space="preserve" y="1032.88" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135257812994" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="332">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="332" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1640.3,1029.71) scale(1,1) translate(0,2.71573e-12)" writing-mode="lr" x="1639.84" xml:space="preserve" y="1034.38" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135256895490" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="335">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="335" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,498.856,403.012) scale(1,1) translate(0,0)" writing-mode="lr" x="498.39" xml:space="preserve" y="407.68" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135225569282" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="336">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="336" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,498.856,426.012) scale(1,1) translate(0,0)" writing-mode="lr" x="498.39" xml:space="preserve" y="430.68" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135225634818" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="337">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="337" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,498.856,449.012) scale(1,1) translate(0,0)" writing-mode="lr" x="498.39" xml:space="preserve" y="453.68" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135225700354" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="340">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="340" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,718.048,605.19) scale(1,1) translate(-1.49447e-13,0)" writing-mode="lr" x="717.58" xml:space="preserve" y="609.86" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135226028034" ObjectName="MP"/>
   </metadata>
  </g>
  <g id="342">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="342" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,718.048,628.19) scale(1,1) translate(-1.49447e-13,0)" writing-mode="lr" x="717.58" xml:space="preserve" y="632.86" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135226093570" ObjectName="MQ"/>
   </metadata>
  </g>
  <g id="343">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="343" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,718.048,651.19) scale(1,1) translate(-1.49447e-13,0)" writing-mode="lr" x="717.58" xml:space="preserve" y="655.86" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135226355714" ObjectName="MIa"/>
   </metadata>
  </g>
  <g id="348">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="348" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,508.856,642.226) scale(1,1) translate(0,0)" writing-mode="lr" x="508.39" xml:space="preserve" y="646.89" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135225831426" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="354">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="354" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,508.856,612.226) scale(1,1) translate(0,0)" writing-mode="lr" x="508.39" xml:space="preserve" y="616.89" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135226159106" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="355">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="355" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,508.856,627.226) scale(1,1) translate(0,0)" writing-mode="lr" x="508.39" xml:space="preserve" y="631.89" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135226224642" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="357">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="357" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1136.57,394.44) scale(1,1) translate(0,0)" writing-mode="lr" x="1136.1" xml:space="preserve" y="399.11" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135222947842" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="358">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="358" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1136.57,417.44) scale(1,1) translate(0,0)" writing-mode="lr" x="1136.1" xml:space="preserve" y="422.11" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135223013378" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="360">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="360" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1136.57,440.44) scale(1,1) translate(0,0)" writing-mode="lr" x="1136.1" xml:space="preserve" y="445.11" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135223078914" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="363">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="363" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1345.87,437.976) scale(1,1) translate(0,0)" writing-mode="lr" x="1345.4" xml:space="preserve" y="442.64" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135223406594" ObjectName="MP"/>
   </metadata>
  </g>
  <g id="376">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="376" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1344.62,458.476) scale(1,1) translate(0,0)" writing-mode="lr" x="1344.15" xml:space="preserve" y="463.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135223472130" ObjectName="MQ"/>
   </metadata>
  </g>
  <g id="378">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="378" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1344.62,481.476) scale(1,1) translate(0,0)" writing-mode="lr" x="1344.15" xml:space="preserve" y="486.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135223734274" ObjectName="MIa"/>
   </metadata>
  </g>
  <g id="380">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="380" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1323.14,649.369) scale(1,1) translate(0,4.25572e-13)" writing-mode="lr" x="1322.67" xml:space="preserve" y="654.04" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135223209986" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="384">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="384" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1323.14,619.369) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.67" xml:space="preserve" y="624.04" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135223537666" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="385">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="385" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1323.14,634.369) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.67" xml:space="preserve" y="639.04" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135223603202" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="474">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="474" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,828.287,991.643) scale(1,1) translate(0,0)" writing-mode="lr" x="827.8200000000001" xml:space="preserve" y="996.3099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135379120130" ObjectName="P"/>
   </metadata>
  </g>
  <g id="475">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="475" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,828.287,1009.5) scale(1,1) translate(0,0)" writing-mode="lr" x="827.8200000000001" xml:space="preserve" y="1014.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135379185666" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="476">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="476" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,828.287,1028.21) scale(1,1) translate(0,0)" writing-mode="lr" x="827.8200000000001" xml:space="preserve" y="1032.88" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135379251202" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="477">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="477" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1243.37,935.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1242.9" xml:space="preserve" y="940.3099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135233826818" ObjectName="P"/>
   </metadata>
  </g>
  <g id="478">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="478" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1244.62,953.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1244.15" xml:space="preserve" y="958.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135233892354" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="479">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="479" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1243.37,972.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1242.9" xml:space="preserve" y="976.88" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135233957890" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="481">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="481" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,985.083,935.643) scale(1,1) translate(0,0)" writing-mode="lr" x="984.61" xml:space="preserve" y="940.3099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135239790594" ObjectName="P"/>
   </metadata>
  </g>
  <g id="482">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="482" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,985.083,953.5) scale(1,1) translate(0,0)" writing-mode="lr" x="984.61" xml:space="preserve" y="958.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135239856130" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="502">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="502" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,985.083,972.214) scale(1,1) translate(0,0)" writing-mode="lr" x="984.61" xml:space="preserve" y="976.88" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135239921666" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="582">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="582" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,754,991.643) scale(1,1) translate(0,0)" writing-mode="lr" x="753.53" xml:space="preserve" y="996.3099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135240904706" ObjectName="P"/>
   </metadata>
  </g>
  <g id="591">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="591" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,754,1009.5) scale(1,1) translate(0,0)" writing-mode="lr" x="753.53" xml:space="preserve" y="1014.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135240970242" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="608">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="608" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,754,1027.21) scale(1,1) translate(0,0)" writing-mode="lr" x="753.53" xml:space="preserve" y="1031.88" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135241035778" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="362">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="362" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1280.17,154) scale(1,1) translate(0,0)" writing-mode="lr" x="1279.93" xml:space="preserve" y="158.7" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135219539970" ObjectName=""/>
   </metadata>
  </g>
  <g id="601">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="601" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,687.944,154) scale(1,1) translate(0,0)" writing-mode="lr" x="687.71" xml:space="preserve" y="158.7" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135222685698" ObjectName=""/>
   </metadata>
  </g>
  <g id="606">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="606" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1578.06,292.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1577.82" xml:space="preserve" y="297.48" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135221112834" ObjectName=""/>
   </metadata>
  </g>
  <g id="607">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="607" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1566.94,452.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1566.71" xml:space="preserve" y="457.48" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135221637122" ObjectName=""/>
   </metadata>
  </g>
  <g id="609">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="609" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1438.06,502.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1437.82" xml:space="preserve" y="507.48" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135220064258" ObjectName=""/>
   </metadata>
  </g>
  <g id="610">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="610" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,790.167,509.556) scale(1,1) translate(0,0)" writing-mode="lr" x="789.9299999999999" xml:space="preserve" y="514.25" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135229501442" ObjectName=""/>
   </metadata>
  </g>
  <g id="126">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="126" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,519.287,991.643) scale(1,1) translate(0,0)" writing-mode="lr" x="518.8200000000001" xml:space="preserve" y="996.3099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127477051396" ObjectName="P"/>
   </metadata>
  </g>
  <g id="605">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="605" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,519.287,1009.5) scale(1,1) translate(0,0)" writing-mode="lr" x="518.8200000000001" xml:space="preserve" y="1014.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127477116932" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="661">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="661" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,519.287,1028.21) scale(1,1) translate(0,0)" writing-mode="lr" x="518.8200000000001" xml:space="preserve" y="1032.88" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127477182468" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="662">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="662" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,600.287,991.643) scale(1,1) translate(0,0)" writing-mode="lr" x="599.8200000000001" xml:space="preserve" y="996.3099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127478493188" ObjectName="P"/>
   </metadata>
  </g>
  <g id="663">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="663" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,600.287,1009.5) scale(1,1) translate(0,0)" writing-mode="lr" x="599.8200000000001" xml:space="preserve" y="1014.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127478558724" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="664">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="664" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,600.287,1028.21) scale(1,1) translate(0,0)" writing-mode="lr" x="599.8200000000001" xml:space="preserve" y="1032.88" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127478624262" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="691">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="691" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1179.32,16.2289) scale(1,1) translate(-2.51869e-13,0)" writing-mode="lr" x="1136.19" xml:space="preserve" y="20.65" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128182480900" ObjectName="P"/>
   </metadata>
  </g>
  <g id="692">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="692" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1179.32,35.2289) scale(1,1) translate(-2.51869e-13,0)" writing-mode="lr" x="1136.19" xml:space="preserve" y="39.65" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128182546438" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="693">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="693" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1178.32,52.2289) scale(1,1) translate(-2.51647e-13,0)" writing-mode="lr" x="1135.19" xml:space="preserve" y="56.65" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128182611974" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="1057">
   <use height="30" transform="rotate(0,326.625,338.675) scale(0.708333,0.665547) translate(130.118,165.175)" width="30" x="316" xlink:href="#State:红绿圆(方形)_0" y="328.69" zvalue="10284"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374926151681" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,326.625,338.675) scale(0.708333,0.665547) translate(130.118,165.175)" width="30" x="316" y="328.69"/></g>
  <g id="1056">
   <use height="30" transform="rotate(0,231,338.675) scale(0.708333,0.665547) translate(90.7426,165.175)" width="30" x="220.38" xlink:href="#State:红绿圆(方形)_0" y="328.69" zvalue="10285"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562960949575682" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,231,338.675) scale(0.708333,0.665547) translate(90.7426,165.175)" width="30" x="220.38" y="328.69"/></g>
  <g id="192">
   <use height="30" transform="rotate(0,322.812,130.464) scale(1.22222,1.03092) translate(-48.6932,-3.44935)" width="90" x="267.81" xlink:href="#State:全站检修_0" y="115" zvalue="10780"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549682241537" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,322.812,130.464) scale(1.22222,1.03092) translate(-48.6932,-3.44935)" width="90" x="267.81" y="115"/></g>
  <g id="1084">
   <use height="30" transform="rotate(0,266.235,405) scale(0.910937,0.8) translate(22.4673,98.25)" width="80" x="229.8" xlink:href="#State:间隔模板_0" y="393" zvalue="10922"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629499677278212" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,266.235,405) scale(0.910937,0.8) translate(22.4673,98.25)" width="80" x="229.8" y="393"/></g>
  <g id="1082">
   <use height="30" transform="rotate(0,62.2857,331.539) scale(0.910937,0.8) translate(2.52718,79.8847)" width="80" x="25.85" xlink:href="#State:间隔模板_0" y="319.54" zvalue="10923"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629500454928386" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,62.2857,331.539) scale(0.910937,0.8) translate(2.52718,79.8847)" width="80" x="25.85" y="319.54"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="314">
   <use class="kv10" height="50" transform="rotate(0,1310.82,934.257) scale(1.64858,1.49376) translate(-507.59,-296.472)" width="25" x="1290.213254769236" xlink:href="#Compensator:电容20200722_0" y="896.9133295158435" zvalue="10334"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454440583171" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192454440583171"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1310.82,934.257) scale(1.64858,1.49376) translate(-507.59,-296.472)" width="25" x="1290.213254769236" y="896.9133295158435"/></g>
  <g id="399">
   <use class="kv10" height="50" transform="rotate(0,901.974,934.257) scale(1.64858,1.49376) translate(-346.743,-296.472)" width="25" x="881.366310312169" xlink:href="#Compensator:电容20200722_0" y="896.9133300781251" zvalue="10381"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454441566211" ObjectName="10kV2号电容器"/>
   <cge:TPSR_Ref TObjectID="6192454441566211"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,901.974,934.257) scale(1.64858,1.49376) translate(-346.743,-296.472)" width="25" x="881.366310312169" y="896.9133300781251"/></g>
 </g>
</svg>