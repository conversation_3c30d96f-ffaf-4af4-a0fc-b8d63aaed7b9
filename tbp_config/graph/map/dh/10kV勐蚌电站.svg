<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549592719362" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_0" viewBox="0,0,30,50">
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01666666666667" x2="15.01666666666667" y1="37.50000000000001" y2="32.91666666666667"/>
   <use terminal-index="1" type="1" x="15.00325153685922" xlink:href="#terminal" y="49.86055225321343"/>
   <use terminal-index="2" type="2" x="15" xlink:href="#terminal" y="37.75085860895189"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.25" x2="15" y1="43.08333333333334" y2="37.75000000000001"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15" x2="20" y1="37.74761215261902" y2="42.91666666666667"/>
   <ellipse cx="15" cy="35.25" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_1" viewBox="0,0,30,50">
   <path d="M 10 16 L 20.1667 16 L 15 7.83333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="0" type="1" x="15.00731595793324" xlink:href="#terminal" y="0.3902606310013752"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:站外刀闸_0" viewBox="0,0,30,14">
   <use terminal-index="0" type="0" x="0.4166666666666661" xlink:href="#terminal" y="10.66666666666667"/>
   <use terminal-index="1" type="0" x="29.91666666666666" xlink:href="#terminal" y="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.75" x2="19.75" y1="7" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="8.75" y1="2" y2="7"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="21.75" y1="2" y2="7"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="18.75" y1="4.75" y2="0.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.25" x2="29.58333333333333" y1="10.58333333333333" y2="10.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.583333333333336" x2="23.41666666666666" y1="3.583333333333333" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.833333333333339" x2="0.6666666666666714" y1="10.57515993898413" y2="10.57515993898413"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.92232884821164" x2="5.92232884821164" y1="13.33333333333333" y2="8.083333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.583333333333332" x2="19.58333333333333" y1="6.833333333333333" y2="11.83333333333333"/>
  </symbol>
  <symbol id="Disconnector:站外刀闸_1" viewBox="0,0,30,14">
   <use terminal-index="0" type="0" x="0.4166666666666661" xlink:href="#terminal" y="10.66666666666667"/>
   <use terminal-index="1" type="0" x="29.91666666666666" xlink:href="#terminal" y="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="17" y1="8" y2="4"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,17,10.5) scale(1,1) translate(0,0)" width="14" x="10" y="8"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.33333333333333" x2="6.000000000000004" y1="10.55" y2="10.55"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.25" x2="29.58333333333333" y1="10.58333333333333" y2="10.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.833333333333339" x2="0.6666666666666714" y1="10.57515993898413" y2="10.57515993898413"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.92232884821164" x2="5.92232884821164" y1="13.33333333333333" y2="8.083333333333332"/>
  </symbol>
  <symbol id="Disconnector:站外刀闸_2" viewBox="0,0,30,14">
   <use terminal-index="0" type="0" x="0.4166666666666661" xlink:href="#terminal" y="10.66666666666667"/>
   <use terminal-index="1" type="0" x="29.91666666666666" xlink:href="#terminal" y="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="21" y1="13" y2="7"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="21" y1="7" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="14" y1="6" y2="3"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.25" x2="29.58333333333333" y1="10.58333333333333" y2="10.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.833333333333339" x2="0.6666666666666714" y1="10.57515993898413" y2="10.57515993898413"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.92232884821164" x2="5.92232884821164" y1="13.33333333333333" y2="8.083333333333332"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV勐蚌电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="42.43" xlink:href="logo.png" y="41.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,177.054,71.4286) scale(1,1) translate(0,0)" writing-mode="lr" x="177.05" xml:space="preserve" y="74.93000000000001" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,178.762,71.1189) scale(1,1) translate(6.70892e-15,0)" writing-mode="lr" x="178.76" xml:space="preserve" y="80.12" zvalue="3">10kV勐蚌电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="27" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,82.9286,312.429) scale(1,1) translate(0,0)" width="97" x="34.43" y="300.43" zvalue="9"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.9286,312.429) scale(1,1) translate(0,0)" writing-mode="lr" x="82.93000000000001" xml:space="preserve" y="316.93" zvalue="9">全站公用</text>
  <line fill="none" id="32" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="376.4285714285714" x2="376.4285714285714" y1="9.428571428571445" y2="1039.428571428572" zvalue="4"/>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.428571428571672" x2="369.4285714285712" y1="145.2990640426539" y2="145.2990640426539" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571445" x2="184.4285714285714" y1="157.4285714285716" y2="157.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571445" x2="184.4285714285714" y1="183.4285714285716" y2="183.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571445" x2="3.428571428571445" y1="157.4285714285716" y2="183.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="184.4285714285714" y1="157.4285714285716" y2="183.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="365.4285714285714" y1="157.4285714285716" y2="157.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="365.4285714285714" y1="183.4285714285716" y2="183.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="184.4285714285714" y1="157.4285714285716" y2="183.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285714" x2="365.4285714285714" y1="157.4285714285716" y2="183.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571445" x2="184.4285714285714" y1="183.4285714285716" y2="183.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571445" x2="184.4285714285714" y1="207.6785714285716" y2="207.6785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571445" x2="3.428571428571445" y1="183.4285714285716" y2="207.6785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="184.4285714285714" y1="183.4285714285716" y2="207.6785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="365.4285714285714" y1="183.4285714285716" y2="183.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="365.4285714285714" y1="207.6785714285716" y2="207.6785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="184.4285714285714" y1="183.4285714285716" y2="207.6785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285714" x2="365.4285714285714" y1="183.4285714285716" y2="207.6785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571445" x2="184.4285714285714" y1="207.6785714285716" y2="207.6785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571445" x2="184.4285714285714" y1="230.4285714285716" y2="230.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571445" x2="3.428571428571445" y1="207.6785714285716" y2="230.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="184.4285714285714" y1="207.6785714285716" y2="230.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="365.4285714285714" y1="207.6785714285716" y2="207.6785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="365.4285714285714" y1="230.4285714285716" y2="230.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="184.4285714285714" y1="207.6785714285716" y2="230.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285714" x2="365.4285714285714" y1="207.6785714285716" y2="230.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571445" x2="184.4285714285714" y1="230.4285714285716" y2="230.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571445" x2="184.4285714285714" y1="253.1785714285716" y2="253.1785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571445" x2="3.428571428571445" y1="230.4285714285716" y2="253.1785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="184.4285714285714" y1="230.4285714285716" y2="253.1785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="365.4285714285714" y1="230.4285714285716" y2="230.4285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="365.4285714285714" y1="253.1785714285716" y2="253.1785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="184.4285714285714" y1="230.4285714285716" y2="253.1785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285714" x2="365.4285714285714" y1="230.4285714285716" y2="253.1785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571445" x2="184.4285714285714" y1="253.1785714285716" y2="253.1785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571445" x2="184.4285714285714" y1="275.9285714285716" y2="275.9285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571445" x2="3.428571428571445" y1="253.1785714285716" y2="275.9285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="184.4285714285714" y1="253.1785714285716" y2="275.9285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="365.4285714285714" y1="253.1785714285716" y2="253.1785714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="365.4285714285714" y1="275.9285714285716" y2="275.9285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285714" x2="184.4285714285714" y1="253.1785714285716" y2="275.9285714285716"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285714" x2="365.4285714285714" y1="253.1785714285716" y2="275.9285714285716"/>
  <line fill="none" id="28" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.428571428571672" x2="369.4285714285712" y1="615.299064042654" y2="615.299064042654" zvalue="8"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591833" x2="103.9956331659183" y1="438.0952149344308" y2="438.0952149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591833" x2="103.9956331659183" y1="475.5852149344308" y2="475.5852149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591833" x2="55.65093316591833" y1="438.0952149344308" y2="475.5852149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9956331659183" x2="103.9956331659183" y1="438.0952149344308" y2="475.5852149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659182" x2="166.1042331659182" y1="438.0952149344308" y2="438.0952149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659182" x2="166.1042331659182" y1="475.5852149344308" y2="475.5852149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659182" x2="103.9959331659182" y1="438.0952149344308" y2="475.5852149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1042331659182" x2="166.1042331659182" y1="438.0952149344308" y2="475.5852149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659182" x2="229.4285331659183" y1="438.0952149344308" y2="438.0952149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659182" x2="229.4285331659183" y1="475.5852149344308" y2="475.5852149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659182" x2="166.1037331659182" y1="438.0952149344308" y2="475.5852149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4285331659183" x2="229.4285331659183" y1="438.0952149344308" y2="475.5852149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659182" x2="291.5367331659183" y1="438.0952149344308" y2="438.0952149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659182" x2="291.5367331659183" y1="475.5852149344308" y2="475.5852149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659182" x2="229.4284331659182" y1="438.0952149344308" y2="475.5852149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="291.5367331659183" y1="438.0952149344308" y2="475.5852149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="353.6450331659182" y1="438.0952149344308" y2="438.0952149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="353.6450331659182" y1="475.5852149344308" y2="475.5852149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="291.5367331659183" y1="438.0952149344308" y2="475.5852149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="353.6450331659182" x2="353.6450331659182" y1="438.0952149344308" y2="475.5852149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591833" x2="103.9956331659183" y1="475.5853149344309" y2="475.5853149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591833" x2="103.9956331659183" y1="499.7539149344308" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591833" x2="55.65093316591833" y1="475.5853149344309" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9956331659183" x2="103.9956331659183" y1="475.5853149344309" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659182" x2="166.1042331659182" y1="475.5853149344309" y2="475.5853149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659182" x2="166.1042331659182" y1="499.7539149344308" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659182" x2="103.9959331659182" y1="475.5853149344309" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1042331659182" x2="166.1042331659182" y1="475.5853149344309" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659182" x2="229.4285331659183" y1="475.5853149344309" y2="475.5853149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659182" x2="229.4285331659183" y1="499.7539149344308" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659182" x2="166.1037331659182" y1="475.5853149344309" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4285331659183" x2="229.4285331659183" y1="475.5853149344309" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659182" x2="291.5367331659183" y1="475.5853149344309" y2="475.5853149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659182" x2="291.5367331659183" y1="499.7539149344308" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659182" x2="229.4284331659182" y1="475.5853149344309" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="291.5367331659183" y1="475.5853149344309" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="353.6450331659182" y1="475.5853149344309" y2="475.5853149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="353.6450331659182" y1="499.7539149344308" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="291.5367331659183" y1="475.5853149344309" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="353.6450331659182" x2="353.6450331659182" y1="475.5853149344309" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591833" x2="103.9956331659183" y1="499.7539149344308" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591833" x2="103.9956331659183" y1="523.9225149344309" y2="523.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591833" x2="55.65093316591833" y1="499.7539149344308" y2="523.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9956331659183" x2="103.9956331659183" y1="499.7539149344308" y2="523.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659182" x2="166.1042331659182" y1="499.7539149344308" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659182" x2="166.1042331659182" y1="523.9225149344309" y2="523.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659182" x2="103.9959331659182" y1="499.7539149344308" y2="523.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1042331659182" x2="166.1042331659182" y1="499.7539149344308" y2="523.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659182" x2="229.4285331659183" y1="499.7539149344308" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659182" x2="229.4285331659183" y1="523.9225149344309" y2="523.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659182" x2="166.1037331659182" y1="499.7539149344308" y2="523.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4285331659183" x2="229.4285331659183" y1="499.7539149344308" y2="523.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659182" x2="291.5367331659183" y1="499.7539149344308" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659182" x2="291.5367331659183" y1="523.9225149344309" y2="523.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659182" x2="229.4284331659182" y1="499.7539149344308" y2="523.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="291.5367331659183" y1="499.7539149344308" y2="523.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="353.6450331659182" y1="499.7539149344308" y2="499.7539149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="353.6450331659182" y1="523.9225149344309" y2="523.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="291.5367331659183" y1="499.7539149344308" y2="523.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="353.6450331659182" x2="353.6450331659182" y1="499.7539149344308" y2="523.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591833" x2="103.9956331659183" y1="523.9225549344309" y2="523.9225549344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591833" x2="103.9956331659183" y1="548.0911549344308" y2="548.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591833" x2="55.65093316591833" y1="523.9225549344309" y2="548.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9956331659183" x2="103.9956331659183" y1="523.9225549344309" y2="548.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659182" x2="166.1042331659182" y1="523.9225549344309" y2="523.9225549344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659182" x2="166.1042331659182" y1="548.0911549344308" y2="548.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659182" x2="103.9959331659182" y1="523.9225549344309" y2="548.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1042331659182" x2="166.1042331659182" y1="523.9225549344309" y2="548.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659182" x2="229.4285331659183" y1="523.9225549344309" y2="523.9225549344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659182" x2="229.4285331659183" y1="548.0911549344308" y2="548.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659182" x2="166.1037331659182" y1="523.9225549344309" y2="548.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4285331659183" x2="229.4285331659183" y1="523.9225549344309" y2="548.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659182" x2="291.5367331659183" y1="523.9225549344309" y2="523.9225549344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659182" x2="291.5367331659183" y1="548.0911549344308" y2="548.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659182" x2="229.4284331659182" y1="523.9225549344309" y2="548.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="291.5367331659183" y1="523.9225549344309" y2="548.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="353.6450331659182" y1="523.9225549344309" y2="523.9225549344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="353.6450331659182" y1="548.0911549344308" y2="548.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="291.5367331659183" y1="523.9225549344309" y2="548.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="353.6450331659182" x2="353.6450331659182" y1="523.9225549344309" y2="548.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591833" x2="103.9956331659183" y1="548.0913149344309" y2="548.0913149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591833" x2="103.9956331659183" y1="572.2599149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591833" x2="55.65093316591833" y1="548.0913149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9956331659183" x2="103.9956331659183" y1="548.0913149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659182" x2="166.1042331659182" y1="548.0913149344309" y2="548.0913149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659182" x2="166.1042331659182" y1="572.2599149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659182" x2="103.9959331659182" y1="548.0913149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1042331659182" x2="166.1042331659182" y1="548.0913149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659182" x2="229.4285331659183" y1="548.0913149344309" y2="548.0913149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659182" x2="229.4285331659183" y1="572.2599149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659182" x2="166.1037331659182" y1="548.0913149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4285331659183" x2="229.4285331659183" y1="548.0913149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659182" x2="291.5367331659183" y1="548.0913149344309" y2="548.0913149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659182" x2="291.5367331659183" y1="572.2599149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659182" x2="229.4284331659182" y1="548.0913149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="291.5367331659183" y1="548.0913149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="353.6450331659182" y1="548.0913149344309" y2="548.0913149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="353.6450331659182" y1="572.2599149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="291.5367331659183" y1="548.0913149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="353.6450331659182" x2="353.6450331659182" y1="548.0913149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591833" x2="103.9956331659183" y1="572.2599149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591833" x2="103.9956331659183" y1="596.4285149344308" y2="596.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591833" x2="55.65093316591833" y1="572.2599149344309" y2="596.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9956331659183" x2="103.9956331659183" y1="572.2599149344309" y2="596.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659182" x2="166.1042331659182" y1="572.2599149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659182" x2="166.1042331659182" y1="596.4285149344308" y2="596.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659182" x2="103.9959331659182" y1="572.2599149344309" y2="596.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1042331659182" x2="166.1042331659182" y1="572.2599149344309" y2="596.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659182" x2="229.4285331659183" y1="572.2599149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659182" x2="229.4285331659183" y1="596.4285149344308" y2="596.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659182" x2="166.1037331659182" y1="572.2599149344309" y2="596.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4285331659183" x2="229.4285331659183" y1="572.2599149344309" y2="596.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659182" x2="291.5367331659183" y1="572.2599149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659182" x2="291.5367331659183" y1="596.4285149344308" y2="596.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659182" x2="229.4284331659182" y1="572.2599149344309" y2="596.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="291.5367331659183" y1="572.2599149344309" y2="596.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="353.6450331659182" y1="572.2599149344309" y2="572.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="353.6450331659182" y1="596.4285149344308" y2="596.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659183" x2="291.5367331659183" y1="572.2599149344309" y2="596.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="353.6450331659182" x2="353.6450331659182" y1="572.2599149344309" y2="596.4285149344308"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571445" x2="92.42857142857144" y1="930.4285714285717" y2="930.4285714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571445" x2="92.42857142857144" y1="969.5918714285717" y2="969.5918714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571445" x2="2.428571428571445" y1="930.4285714285717" y2="969.5918714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857144" x2="92.42857142857144" y1="930.4285714285717" y2="969.5918714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857144" x2="362.4285714285714" y1="930.4285714285717" y2="930.4285714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857144" x2="362.4285714285714" y1="969.5918714285717" y2="969.5918714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857144" x2="92.42857142857144" y1="930.4285714285717" y2="969.5918714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362.4285714285714" x2="362.4285714285714" y1="930.4285714285717" y2="969.5918714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571445" x2="92.42857142857144" y1="969.5918414285717" y2="969.5918414285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571445" x2="92.42857142857144" y1="997.5102414285716" y2="997.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571445" x2="2.428571428571445" y1="969.5918414285717" y2="997.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857144" x2="92.42857142857144" y1="969.5918414285717" y2="997.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857144" x2="182.4285714285714" y1="969.5918414285717" y2="969.5918414285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857144" x2="182.4285714285714" y1="997.5102414285716" y2="997.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857144" x2="92.42857142857144" y1="969.5918414285717" y2="997.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285714" x2="182.4285714285714" y1="969.5918414285717" y2="997.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285716" x2="272.4285714285716" y1="969.5918414285717" y2="969.5918414285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285716" x2="272.4285714285716" y1="997.5102414285716" y2="997.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285716" x2="182.4285714285716" y1="969.5918414285717" y2="997.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285716" x2="272.4285714285716" y1="969.5918414285717" y2="997.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285714" x2="362.4285714285714" y1="969.5918414285717" y2="969.5918414285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285714" x2="362.4285714285714" y1="997.5102414285716" y2="997.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285714" x2="272.4285714285714" y1="969.5918414285717" y2="997.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362.4285714285714" x2="362.4285714285714" y1="969.5918414285717" y2="997.5102414285716"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571445" x2="92.42857142857144" y1="997.5101714285717" y2="997.5101714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571445" x2="92.42857142857144" y1="1025.428571428572" y2="1025.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571445" x2="2.428571428571445" y1="997.5101714285717" y2="1025.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857144" x2="92.42857142857144" y1="997.5101714285717" y2="1025.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857144" x2="182.4285714285714" y1="997.5101714285717" y2="997.5101714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857144" x2="182.4285714285714" y1="1025.428571428572" y2="1025.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857144" x2="92.42857142857144" y1="997.5101714285717" y2="1025.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285714" x2="182.4285714285714" y1="997.5101714285717" y2="1025.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285716" x2="272.4285714285716" y1="997.5101714285717" y2="997.5101714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285716" x2="272.4285714285716" y1="1025.428571428572" y2="1025.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285716" x2="182.4285714285716" y1="997.5101714285717" y2="1025.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285716" x2="272.4285714285716" y1="997.5101714285717" y2="1025.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285714" x2="362.4285714285714" y1="997.5101714285717" y2="997.5101714285717"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285714" x2="362.4285714285714" y1="1025.428571428572" y2="1025.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285714" x2="272.4285714285714" y1="997.5101714285717" y2="1025.428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362.4285714285714" x2="362.4285714285714" y1="997.5101714285717" y2="1025.428571428572"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.4286,950.429) scale(1,1) translate(0,0)" writing-mode="lr" x="47.43" xml:space="preserve" y="956.4299999999999" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.4286,984.429) scale(1,1) translate(0,0)" writing-mode="lr" x="44.43" xml:space="preserve" y="990.4299999999999" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.429,984.429) scale(1,1) translate(0,0)" writing-mode="lr" x="226.43" xml:space="preserve" y="990.4299999999999" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43.4286,1012.43) scale(1,1) translate(0,0)" writing-mode="lr" x="43.43" xml:space="preserve" y="1018.43" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225.429,1012.43) scale(1,1) translate(0,0)" writing-mode="lr" x="225.43" xml:space="preserve" y="1018.43" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.9286,644.929) scale(1,1) translate(0,0)" writing-mode="lr" x="67.92857142857144" xml:space="preserve" y="649.4285714285716" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,200.827,312.27) scale(1,1) translate(0,0)" writing-mode="lr" x="200.83" xml:space="preserve" y="316.77" zvalue="19">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,305.827,312.27) scale(1,1) translate(0,0)" writing-mode="lr" x="305.83" xml:space="preserve" y="316.77" zvalue="20">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.4286,487.929) scale(1,1) translate(0,0)" writing-mode="lr" x="79.42857142857144" xml:space="preserve" y="492.4285714285714" zvalue="21">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.4286,513.429) scale(1,1) translate(0,0)" writing-mode="lr" x="79.42857142857144" xml:space="preserve" y="517.9285714285716" zvalue="22">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.4286,538.929) scale(1,1) translate(0,0)" writing-mode="lr" x="79.42857142857144" xml:space="preserve" y="543.4285714285714" zvalue="23">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.4286,563.429) scale(1,1) translate(0,0)" writing-mode="lr" x="78.42857142857144" xml:space="preserve" y="567.9285714285716" zvalue="24">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.4286,589.929) scale(1,1) translate(0,0)" writing-mode="lr" x="79.42857142857144" xml:space="preserve" y="594.4285714285716" zvalue="25">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.483,952.429) scale(1,1) translate(0,0)" writing-mode="lr" x="227.48" xml:space="preserve" y="958.4299999999999" zvalue="26">MGS4-01-2008</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,137.483,984.429) scale(1,1) translate(0,0)" writing-mode="lr" x="137.48" xml:space="preserve" y="990.4299999999999" zvalue="27">何成飞</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,317.483,984.429) scale(1,1) translate(0,0)" writing-mode="lr" x="317.48" xml:space="preserve" y="990.4299999999999" zvalue="28">20210510</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,41.4286,171.429) scale(1,1) translate(0,0)" writing-mode="lr" x="41.43" xml:space="preserve" y="176.93" zvalue="29">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,221.429,171.429) scale(1,1) translate(0,0)" writing-mode="lr" x="221.43" xml:space="preserve" y="176.93" zvalue="30">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.6161,243.429) scale(1,1) translate(0,0)" writing-mode="lr" x="48.62" xml:space="preserve" y="247.93" zvalue="31">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.512,198.123) scale(1,1) translate(0,0)" writing-mode="lr" x="233.51" xml:space="preserve" y="202.62" zvalue="32">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" x="254.796875" xml:space="preserve" y="456.1875" zvalue="33">0.4kV  母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="254.796875" xml:space="preserve" y="472.1875" zvalue="33">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.9286,196.262) scale(1,1) translate(0,0)" writing-mode="lr" x="49.93" xml:space="preserve" y="200.76" zvalue="34">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" x="135.4375" xml:space="preserve" y="453.765625" zvalue="35">10kV  母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="135.4375" xml:space="preserve" y="469.765625" zvalue="35">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1073.2,982.071) scale(1,1) translate(2.32413e-13,0)" writing-mode="lr" x="1073.2" xml:space="preserve" y="986.5700000000001" zvalue="38">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1131.04,507.259) scale(1,1) translate(0,0)" writing-mode="lr" x="1131.04" xml:space="preserve" y="511.76" zvalue="40">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1070.66,96.0714) scale(1,1) translate(0,0)" writing-mode="lr" x="1070.66" xml:space="preserve" y="100.57" zvalue="41">10kV平蚌线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1093.29,734.071) scale(1,1) translate(0,0)" writing-mode="lr" x="1093.29" xml:space="preserve" y="738.5700000000001" zvalue="51">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1034,346) scale(1,1) translate(0,0)" writing-mode="lr" x="1034" xml:space="preserve" y="350.5" zvalue="55">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1130.5,532) scale(1,1) translate(0,0)" writing-mode="lr" x="1130.5" xml:space="preserve" y="536.5" zvalue="60">320kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1074,1006.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1074" xml:space="preserve" y="1011" zvalue="61">250kW</text>
 </g>
 <g id="ButtonClass">
  <g href="500kV德宏变_全站公用.svg"><rect fill-opacity="0" height="24" width="97" x="34.43" y="300.43" zvalue="9"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="GeneratorClass">
  <g id="50">
   <use class="v400" height="30" transform="rotate(0,1069.45,917.884) scale(2.39583,2.39583) translate(-602.131,-513.83)" width="30" x="1033.50924049026" xlink:href="#Generator:发电机_0" y="881.9464285714287" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450021228549" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192450021228549"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1069.45,917.884) scale(2.39583,2.39583) translate(-602.131,-513.83)" width="30" x="1033.50924049026" y="881.9464285714287"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="49">
   <g id="490">
    <use class="v400" height="50" transform="rotate(0,1069.47,520.259) scale(1.7625,1.7625) translate(-451.243,-206.014)" width="30" x="1043.04" xlink:href="#PowerTransformer2:D-Y_0" y="476.2" zvalue="39"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874454990851" ObjectName="0.4"/>
    </metadata>
   </g>
   <g id="491">
    <use class="kv10" height="50" transform="rotate(0,1069.47,520.259) scale(1.7625,1.7625) translate(-451.243,-206.014)" width="30" x="1043.04" xlink:href="#PowerTransformer2:D-Y_1" y="476.2" zvalue="39"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874454925315" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399459340291" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399459340291"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1069.47,520.259) scale(1.7625,1.7625) translate(-451.243,-206.014)" width="30" x="1043.04" y="476.2"/></g>
 </g>
 <g id="BreakerClass">
  <g id="45">
   <use class="v400" height="20" transform="rotate(0,1069.41,735.071) scale(1.875,1.6875) translate(-494.683,-292.599)" width="10" x="1060.035714285714" xlink:href="#Breaker:开关_0" y="718.1964285714287" zvalue="49"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924557864965" ObjectName="#1主变401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924557864965"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1069.41,735.071) scale(1.875,1.6875) translate(-494.683,-292.599)" width="10" x="1060.035714285714" y="718.1964285714287"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="41">
   <path class="v400" d="M 1069.45 882.55 L 1069.54 751.19" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="45@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1069.45 882.55 L 1069.54 751.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="v400" d="M 1069.35 718.93 L 1069.35 564.08" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="49@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1069.35 718.93 L 1069.35 564.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv10" d="M 1070.66 165.47 L 1070.67 333.08" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@0" LinkObjectIDznd="52@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1070.66 165.47 L 1070.67 333.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv10" d="M 1070.67 362.58 L 1070.67 476.88" stroke-width="1" zvalue="57"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1070.67 362.58 L 1070.67 476.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="52">
   <use class="kv10" height="14" transform="rotate(270,1067,348) scale(1,1) translate(0,0)" width="30" x="1052" xlink:href="#Disconnector:站外刀闸_0" y="341" zvalue="54"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450021294085" ObjectName="#1主变0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450021294085"/></metadata>
  <rect fill="white" height="14" opacity="0" stroke="white" transform="rotate(270,1067,348) scale(1,1) translate(0,0)" width="30" x="1052" y="341"/></g>
 </g>
</svg>