<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549679947777" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="1"/>
   <ellipse cx="14.99" cy="7.93" fill-opacity="0" rx="7.43" ry="6.85" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.93" cy="17.56" fill-opacity="0" rx="7.43" ry="6.85" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.9834232682896" x2="14.9834232682896" y1="14.95787681993586" y2="18.2114918126664"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.98342326828961" x2="11.56276000329148" y1="18.23128772350278" y2="20.61950731913853"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00726151837816" x2="18.51522958638884" y1="18.19641261556525" y2="20.42154821077479"/>
   <path d="M 11.6895 9.29402 L 18.7806 9.29402 L 15.2653 3.08877 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Compensator:电容20200722_0" viewBox="0,0,25,50">
   <use terminal-index="0" type="0" x="15.16666666666667" xlink:href="#terminal" y="0.4166666666666643"/>
   <path d="M 15.5 23 L 20.5 23 L 20.5 28" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 19.5 27 L 20.5 28 L 21.5 27" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.5" x2="22.5" y1="46" y2="45"/>
   <rect fill-opacity="0" height="4" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.33,32) scale(1,1) translate(0,0)" width="2" x="14.33" y="30"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="46" y2="45"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20.58,28.25) scale(1,1) translate(0,0)" width="3" x="19.08" y="25.75"/>
   <path d="M 15.5 46 L 15.5 48 L 2.5 48 L 2.5 1" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 20 32.5833 L 21 32.5833" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="21" y1="33.16666666666667" y2="33.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.5" x2="21.5" y1="32" y2="32"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5" x2="20.5" y1="30.75" y2="32"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.35833333333333" x2="15.35833333333333" y1="43.69166666666667" y2="46.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333332" x2="15.35833333333334" y1="28.35833333333333" y2="28.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.5" x2="7.5" y1="46.10833333333335" y2="46.10833333333335"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.358333333333334" x2="9.358333333333334" y1="30.60833333333334" y2="28.40462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.35833333333333" x2="15.35833333333333" y1="21.5" y2="40.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.35833333333333" x2="15.35833333333333" y1="41.58333333333333" y2="43.62685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.321296296296296" x2="9.321296296296296" y1="41.38611111111112" y2="43.62685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25" x2="9.333333333333332" y1="43.60833333333333" y2="43.60833333333333"/>
   <path d="M 9.35833 34.2072 A 2.96392 1.81747 -180 0 1 9.35833 30.5723" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 9.35833 37.8421 A 2.96392 1.81747 -180 0 1 9.35833 34.2072" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 9.35833 41.3771 A 2.96392 1.81747 -180 0 1 9.35833 37.7421" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.94166666666667" x2="13.94166666666667" y1="41.525" y2="41.525"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.025" x2="14.025" y1="40.19166666666667" y2="40.19166666666667"/>
   <path d="M 22.5 14.7417 A 6.84167 7.10597 -270 1 0 15.394 21.5833" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 22.4547 14.749 L 15.3041 14.749 L 15.3041 0.416667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_0" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="3" y1="19" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="4.75" y2="8.75"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="18.75" y2="21.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_1" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="2" y2="24"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_2" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.08333333333333" x2="3.995614035087721" y1="9.083333333333332" y2="17.19627192982456"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.717927631578948" x2="10.16666666666667" y1="9.172423245614038" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="2" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="21.25" y2="24.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.998992968246015" x2="4.998992968246015" y1="14.41666666666666" y2="17.66666666666666"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="1.08333333333333" y2="5.749999999999999"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="6" y2="14"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="14" y2="6"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:母线PT带保险_0" viewBox="0,0,44,51">
   <use terminal-index="0" type="0" x="23.21996232536395" xlink:href="#terminal" y="50.54273758135757"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.81333347460429" x2="37.91333348509471" y1="15.35514348552543" y2="20.85514353797752"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.89666669429144" x2="24.89666669429144" y1="17.55514350650627" y2="13.1551434645446"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.81333347460429" x2="35.71333346411387" y1="15.35514348552543" y2="20.85514353797752"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.89666669429144" x2="27.09666671527227" y1="13.1551434645446" y2="14.25514347503501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.89666669429144" x2="27.09666671527227" y1="17.55514350650627" y2="16.45514349601585"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.97499997115135" x2="18.97499997115135" y1="23.84999998426438" y2="43.92500017571449"/>
   <rect fill-opacity="0" height="9.9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,18.97,32.93) scale(1,1) translate(0,0)" width="5.5" x="16.22" y="27.98"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.88333330361049" x2="36.85000014162064" y1="43.92500017571449" y2="43.92500017571449"/>
   <path d="M 11.55 15.875 L 6.05 15.875 L 6.05 27.975" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.85000014162064" x2="36.85000014162064" y1="43.92500017571449" y2="15.04999990034103"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.10000001049042" x2="23.10000001049042" y1="43.92500017571449" y2="50.525000238657"/>
   <rect fill-opacity="0" height="17.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,36.85,16) scale(1,-1) translate(0,-706.43)" width="7.7" x="33" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.099999800682067" x2="10.99999989509582" y1="37.60000011539459" y2="28.80000003147125"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.77237710644068" x2="36.77237710644068" y1="7.471810077010772" y2="4.008772090519184"/>
   <ellipse cx="19.04" cy="19.44" fill-opacity="0" rx="4.59" ry="4.59" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="39.00513934691844" x2="34.30064187290063" y1="3.934072700740849" y2="3.934072700740849"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="38.08847267150976" x2="35.2173085483093" y1="2.559072687627836" y2="2.559072687627836"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.44680599872369" x2="35.85897522109538" y1="1.184072674514812" y2="1.184072674514812"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.01064172708383" x2="21.65064175226082" y1="19.67729253017713" y2="21.03858652594676"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.01064172708382" x2="19.01064172708382" y1="16.95470453863786" y2="19.67729253017711"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.01064172708382" x2="16.37064170190682" y1="19.67729253017713" y2="21.03858652594676"/>
   <ellipse cx="18.67" cy="11.65" fill-opacity="0" rx="4.59" ry="4.59" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.8" cy="15.59" fill-opacity="0" rx="4.59" ry="4.59" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.76897499135525" x2="14.40897501653224" y1="15.82729249346067" y2="17.1885864892303"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.76897499135524" x2="9.128974966178244" y1="15.82729249346067" y2="17.1885864892303"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.64397505692036" x2="21.28397508209735" y1="11.88562578920334" y2="13.24691978497297"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.64397505692035" x2="16.00397503174336" y1="11.88562578920334" y2="13.24691978497297"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.64397505692035" x2="18.64397505692035" y1="9.163037797664071" y2="11.88562578920332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.76897499135524" x2="11.76897499135524" y1="13.1047045019214" y2="15.82729249346065"/>
   <ellipse cx="25.46" cy="15.41" fill-opacity="0" rx="4.59" ry="4.59" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <rect fill-opacity="0" height="9.77" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6.19,32.74) scale(1,1) translate(0,0)" width="5.78" x="3.3" y="27.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.129282572815271" x2="6.129282572815271" y1="37.59454254183299" y2="39.80000013637542"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.387769126221219" x2="4.691566274612683" y1="39.65334678993617" y2="39.65334678993617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.862416669798145" x2="5.216918731035754" y1="40.44137547457077" y2="40.44137547457077"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.494669950301995" x2="5.584665450531904" y1="41.22940415920537" y2="41.22940415920537"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:支那变双联地刀_0" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="20.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="13" y1="28.83333333333334" y2="28.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.927164662603633" x2="5.927164662603633" y1="35.73783949080951" y2="33.38156647584609"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.940012628342391" x2="9.914316696864876" y1="35.81657844392075" y2="35.81657844392075"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.461425904573654" x2="8.392903420633612" y1="37.61600124906283" y2="37.61600124906283"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.48434686602373" x2="7.369982459183538" y1="39.33429284309519" y2="39.33429284309519"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.6833333333333336" x2="5.927164662603635" y1="23.72291938246423" y2="33.38156647584608"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.83108493932905" x2="8.028087080656674" y1="23.60113107469012" y2="23.60113107469012"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.97259166003632" x2="5.97259166003632" y1="23.55945108167042" y2="20.52711096774605"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.57716466260364" x2="15.57716466260364" y1="35.62739518973012" y2="33.27112217476669"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.47836350249219" x2="19.45266757101467" y1="35.70613414284136" y2="35.70613414284136"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="12.99977677872345" x2="17.93125429478341" y1="37.50555694798344" y2="37.50555694798344"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.18103107350686" x2="16.75" y1="39.2238485420158" y2="39.2238485420158"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.33333333333333" x2="15.57716466260364" y1="23.61247508138485" y2="33.2711221747667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.62259166003632" x2="15.62259166003632" y1="23.44900678059104" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.48108493932905" x2="17.67808708065667" y1="23.49068677361073" y2="23.49068677361073"/>
  </symbol>
  <symbol id="GroundDisconnector:支那变双联地刀_1" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="20.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.916666666666667" x2="15.75" y1="28.83333333333333" y2="28.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.927164662603633" x2="5.927164662603633" y1="35.73783949080951" y2="23.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.070030169158852" x2="10.04433423768134" y1="35.73324511058742" y2="35.73324511058742"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.591443445390117" x2="8.522920961450074" y1="37.61600124906283" y2="37.61600124906283"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.781031073506858" x2="7.333333333333333" y1="39.33429284309519" y2="39.33429284309519"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.83108493932905" x2="8.028087080656674" y1="23.60113107469012" y2="23.60113107469012"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.97259166003632" x2="5.97259166003632" y1="23.55945108167042" y2="20.52711096774605"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.66049799593697" x2="15.66049799593697" y1="35.66666666666666" y2="23.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.64503016915885" x2="19.61933423768134" y1="35.70613414284136" y2="35.70613414284136"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.16644344539012" x2="18.09792096145007" y1="37.50555694798344" y2="37.50555694798344"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.43103107350686" x2="16.83333333333333" y1="39.2238485420158" y2="39.2238485420158"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.62259166003632" x2="15.62259166003632" y1="23.44900678059104" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.48108493932905" x2="17.67808708065667" y1="23.49068677361073" y2="23.49068677361073"/>
  </symbol>
  <symbol id="GroundDisconnector:支那变双联地刀_2" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="20.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.177164662603633" x2="15.66666666666667" y1="33.98783949080951" y2="24.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.070030169158852" x2="10.04433423768134" y1="35.73324511058742" y2="35.73324511058742"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.591443445390117" x2="8.522920961450074" y1="37.61600124906283" y2="37.61600124906283"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.781031073506858" x2="7.333333333333333" y1="39.33429284309519" y2="39.33429284309519"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.97259166003632" x2="5.97259166003632" y1="23.55945108167042" y2="20.52711096774605"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.83108493932905" x2="8.028087080656674" y1="23.60113107469012" y2="23.60113107469012"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.41049799593697" x2="6.333333333333334" y1="34.00000000000001" y2="25.00000000000001"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.64503016915885" x2="19.61933423768134" y1="35.70613414284136" y2="35.70613414284136"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.16644344539012" x2="18.09792096145007" y1="37.50555694798344" y2="37.50555694798344"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.43103107350686" x2="16.83333333333333" y1="39.2238485420158" y2="39.2238485420158"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.62259166003632" x2="15.62259166003632" y1="23.44900678059104" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.48108493932905" x2="17.67808708065667" y1="23.49068677361073" y2="23.49068677361073"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id="State:间隔模板_0" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(85,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="State:间隔模板_1" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(185,185,185)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV苏典变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,169.51,51.3772) scale(1,1) translate(-1.29705e-14,0)" writing-mode="lr" x="169.51" xml:space="preserve" y="55.88" zvalue="328"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="1" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,187.482,48.3894) scale(1,1) translate(0,2.92884e-15)" writing-mode="lr" x="187.48" xml:space="preserve" y="55.89" zvalue="329"> 35kV苏典变</text>
  <image height="82.56999999999999" id="2" preserveAspectRatio="xMidYMid slice" width="249.69" x="71.31" xlink:href="logo.png" y="14"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.153,55.2864) scale(1,1) translate(-1.58333e-14,0)" writing-mode="lr" x="196.15" xml:space="preserve" y="58.79" zvalue="419"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,225,55.2631) scale(1,1) translate(0,0)" writing-mode="lr" x="225" xml:space="preserve" y="64.26000000000001" zvalue="420">35kV苏典变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="262" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,196.531,404) scale(1,1) translate(0,0)" width="72.88" x="160.09" y="392" zvalue="468"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.531,404) scale(1,1) translate(0,0)" writing-mode="lr" x="196.53" xml:space="preserve" y="408.5" zvalue="468">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="258" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,94.625,404) scale(1,1) translate(0,0)" width="72.88" x="58.19" y="392" zvalue="469"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94.625,404) scale(1,1) translate(0,0)" writing-mode="lr" x="94.63" xml:space="preserve" y="408.5" zvalue="469">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="222" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,94.625,360.75) scale(1,1) translate(0,0)" width="72.88" x="58.19" y="348.75" zvalue="470"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94.625,360.75) scale(1,1) translate(0,0)" writing-mode="lr" x="94.63" xml:space="preserve" y="365.25" zvalue="470">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,598.786,384.607) scale(1,1) translate(0,0)" writing-mode="lr" x="598.79" xml:space="preserve" y="389.11" zvalue="37">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1782,685.607) scale(1,1) translate(0,0)" writing-mode="lr" x="1782" xml:space="preserve" y="690.11" zvalue="39">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1465.71,580.286) scale(1,1) translate(-1.92208e-12,0)" writing-mode="lr" x="1465.71" xml:space="preserve" y="584.79" zvalue="40">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1437.21,506.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1437.21" xml:space="preserve" y="511" zvalue="43">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1430.09,450) scale(1,1) translate(0,0)" writing-mode="lr" x="1430.09" xml:space="preserve" y="454.5" zvalue="46">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1446.38,653.639) scale(1,1) translate(0,4.2875e-13)" writing-mode="lr" x="1446.38" xml:space="preserve" y="658.14" zvalue="64">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1373.71,495.714) scale(1,1) translate(0,-1.0785e-13)" writing-mode="lr" x="1373.71" xml:space="preserve" y="500.21" zvalue="72">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,917.5,309.5) scale(1,1) translate(0,0)" writing-mode="lr" x="917.5" xml:space="preserve" y="314" zvalue="75">391</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,906.75,237) scale(1,1) translate(0,0)" writing-mode="lr" x="906.75" xml:space="preserve" y="241.5" zvalue="78">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,962.625,342.634) scale(1,1) translate(0,0)" writing-mode="lr" x="962.62" xml:space="preserve" y="347.13" zvalue="81">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,911.25,369) scale(1,1) translate(0,0)" writing-mode="lr" x="911.25" xml:space="preserve" y="373.5" zvalue="85">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,962.625,194.634) scale(1,1) translate(0,0)" writing-mode="lr" x="962.62" xml:space="preserve" y="199.13" zvalue="95">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,962.625,274.634) scale(1,1) translate(0,0)" writing-mode="lr" x="962.62" xml:space="preserve" y="279.13" zvalue="99">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,921.75,165.5) scale(1,1) translate(0,0)" writing-mode="lr" x="921.75" xml:space="preserve" y="170" zvalue="102">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1138,337) scale(1,1) translate(0,0)" writing-mode="lr" x="1138" xml:space="preserve" y="341.5" zvalue="110">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1181.87,294.634) scale(1,1) translate(0,0)" writing-mode="lr" x="1181.87" xml:space="preserve" y="299.13" zvalue="114">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1179.27,370.531) scale(1,1) translate(0,0)" writing-mode="lr" x="1179.27" xml:space="preserve" y="375.03" zvalue="116">10</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1123.18,194.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1123.18" xml:space="preserve" y="199.25" zvalue="118">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1023.82,545.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1023.82" xml:space="preserve" y="549.71" zvalue="119">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1276.87,587.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1276.87" xml:space="preserve" y="592.13" zvalue="124">10kVⅠ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1241.04,677.171) scale(1,1) translate(0,0)" writing-mode="lr" x="1241.04" xml:space="preserve" y="681.67" zvalue="125">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1788.75,756.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1788.75" xml:space="preserve" y="761" zvalue="131">081</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1831.62,826.134) scale(1,1) translate(0,0)" writing-mode="lr" x="1831.62" xml:space="preserve" y="830.63" zvalue="142">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1724.02,878.781) scale(1,1) translate(0,0)" writing-mode="lr" x="1724.02" xml:space="preserve" y="883.28" zvalue="145">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1711.25,801.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1711.25" xml:space="preserve" y="806" zvalue="152">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1763.12,996.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1763.12" xml:space="preserve" y="1001" zvalue="155">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1625.75,756.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1625.75" xml:space="preserve" y="761" zvalue="172">082</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="171" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1587.16,851.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1587.16" xml:space="preserve" y="856" zvalue="177">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1552,804.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1552" xml:space="preserve" y="809" zvalue="188">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1596.97,938.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1596.97" xml:space="preserve" y="943" zvalue="190">10kV黄草坝线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="245" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1663.2,996.607) scale(1,1) translate(0,0)" writing-mode="lr" x="1663.196428571428" xml:space="preserve" y="1001.10714163099" zvalue="252">10kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1471.57,597.214) scale(1,1) translate(0,-1.3034e-13)" writing-mode="lr" x="1471.57" xml:space="preserve" y="601.71" zvalue="265">2500kVA</text>
  <line fill="none" id="149" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="383.857142857143" x2="383.857142857143" y1="5.999999999999886" y2="996" zvalue="330"/>
  <line fill="none" id="145" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.76263883569413" x2="324.3738988893133" y1="159.7708030256258" y2="159.7708030256258" zvalue="332"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.85757142857153" x2="115.8289714285715" y1="918.7056480819297" y2="918.7056480819297"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.85757142857153" x2="115.8289714285715" y1="970.0451480819296" y2="970.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.85757142857153" x2="47.85757142857153" y1="918.7056480819297" y2="970.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="115.8289714285715" x2="115.8289714285715" y1="918.7056480819297" y2="970.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="115.8289714285715" x2="338.9999714285716" y1="918.7056480819297" y2="918.7056480819297"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="115.8289714285715" x2="338.9999714285716" y1="970.0451480819296" y2="970.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="115.8289714285715" x2="115.8289714285715" y1="918.7056480819297" y2="970.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="338.9999714285716" x2="338.9999714285716" y1="918.7056480819297" y2="970.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.85757142857153" x2="115.8289714285715" y1="970.0451280819295" y2="970.0451280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.85757142857153" x2="115.8289714285715" y1="997.5226280819296" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.85757142857153" x2="47.85757142857153" y1="970.0451280819295" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="115.8289714285715" x2="115.8289714285715" y1="970.0451280819295" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="115.8289714285715" x2="183.4152714285716" y1="970.0451280819295" y2="970.0451280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="115.8289714285715" x2="183.4152714285716" y1="997.5226280819296" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="115.8289714285715" x2="115.8289714285715" y1="970.0451280819295" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.4152714285716" x2="183.4152714285716" y1="970.0451280819295" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.4153714285716" x2="261.2076714285715" y1="970.0451280819295" y2="970.0451280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.4153714285716" x2="261.2076714285715" y1="997.5226280819296" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.4153714285716" x2="183.4153714285716" y1="970.0451280819295" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="261.2076714285715" x2="261.2076714285715" y1="970.0451280819295" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="261.2075714285716" x2="338.9998714285716" y1="970.0451280819295" y2="970.0451280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="261.2075714285716" x2="338.9998714285716" y1="997.5226280819296" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="261.2075714285716" x2="261.2075714285716" y1="970.0451280819295" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="338.9998714285716" x2="338.9998714285716" y1="970.0451280819295" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.85757142857153" x2="115.8289714285715" y1="997.5225480819296" y2="997.5225480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.85757142857153" x2="115.8289714285715" y1="1025.00004808193" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.85757142857153" x2="47.85757142857153" y1="997.5225480819296" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="115.8289714285715" x2="115.8289714285715" y1="997.5225480819296" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="115.8289714285715" x2="183.4152714285716" y1="997.5225480819296" y2="997.5225480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="115.8289714285715" x2="183.4152714285716" y1="1025.00004808193" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="115.8289714285715" x2="115.8289714285715" y1="997.5225480819296" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.4152714285716" x2="183.4152714285716" y1="997.5225480819296" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.4153714285716" x2="261.2076714285715" y1="997.5225480819296" y2="997.5225480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.4153714285716" x2="261.2076714285715" y1="1025.00004808193" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.4153714285716" x2="183.4153714285716" y1="997.5225480819296" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="261.2076714285715" x2="261.2076714285715" y1="997.5225480819296" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="261.2075714285716" x2="338.9998714285716" y1="997.5225480819296" y2="997.5225480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="261.2075714285716" x2="338.9998714285716" y1="1025.00004808193" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="261.2075714285716" x2="261.2075714285716" y1="997.5225480819296" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="338.9998714285716" x2="338.9998714285716" y1="997.5225480819296" y2="1025.00004808193"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="139" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,194.91,948.279) scale(1,1) translate(-1.18155e-14,1.04078e-13)" writing-mode="lr" x="53.21" xml:space="preserve" y="953.78" zvalue="334">参考图号            SuDian-02-2021</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="133" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,125.435,984.648) scale(1,1) translate(5.59016e-14,-1.51362e-12)" writing-mode="lr" x="62.94" xml:space="preserve" y="990.15" zvalue="335">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="132" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,265.738,984.648) scale(1,1) translate(1.31252e-13,-1.51362e-12)" writing-mode="lr" x="197.03" xml:space="preserve" y="990.15" zvalue="336">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,115.278,1013.21) scale(1,1) translate(0,1.11286e-13)" writing-mode="lr" x="115.28" xml:space="preserve" y="1018.71" zvalue="337">更新           段勇</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="129" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,274.032,1013.21) scale(1,1) translate(0,1.11286e-13)" writing-mode="lr" x="197.21" xml:space="preserve" y="1018.71" zvalue="338">更新日期    20210819</text>
  <line fill="none" id="128" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.14182784373793" x2="323.7530878973571" y1="610.5373878122266" y2="610.5373878122266" zvalue="339"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90.2064,631.579) scale(1,1) translate(4.34224e-15,-1.36087e-13)" writing-mode="lr" x="90.20642234775949" xml:space="preserve" y="636.0792383117243" zvalue="341">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="162.9999999999999" y2="162.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="188.9999999999999" y2="188.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="8.857142857143003" y1="162.9999999999999" y2="188.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="162.9999999999999" y2="188.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="162.9999999999999" y2="162.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="188.9999999999999" y2="188.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="162.9999999999999" y2="188.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370.857142857143" x2="370.857142857143" y1="162.9999999999999" y2="188.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="188.9999999999999" y2="188.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="213.2499999999999" y2="213.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="8.857142857143003" y1="188.9999999999999" y2="213.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="188.9999999999999" y2="213.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="188.9999999999999" y2="188.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="213.2499999999999" y2="213.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="188.9999999999999" y2="213.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370.857142857143" x2="370.857142857143" y1="188.9999999999999" y2="213.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="213.2499999999999" y2="213.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="235.9999999999999" y2="235.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="8.857142857143003" y1="213.2499999999999" y2="235.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="213.2499999999999" y2="235.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="213.2499999999999" y2="213.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="235.9999999999999" y2="235.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="213.2499999999999" y2="235.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370.857142857143" x2="370.857142857143" y1="213.2499999999999" y2="235.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="235.9999999999999" y2="235.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="258.7499999999999" y2="258.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="8.857142857143003" y1="235.9999999999999" y2="258.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="235.9999999999999" y2="258.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="235.9999999999999" y2="235.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="258.7499999999999" y2="258.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="235.9999999999999" y2="258.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370.857142857143" x2="370.857142857143" y1="235.9999999999999" y2="258.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="258.7499999999999" y2="258.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="281.4999999999999" y2="281.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="8.857142857143003" y1="258.7499999999999" y2="281.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="258.7499999999999" y2="281.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="258.7499999999999" y2="258.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="281.4999999999999" y2="281.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="258.7499999999999" y2="281.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370.857142857143" x2="370.857142857143" y1="258.7499999999999" y2="281.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="442.9999999999999" y2="442.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="59.857142857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="442.9999999999999" y2="442.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="442.9999999999999" y2="442.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244442857143" x2="223.244442857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="442.9999999999999" y2="442.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="223.244342857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="442.9999999999999" y2="442.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="340.857142857143" x2="340.857142857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="59.857142857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244442857143" x2="223.244442857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="223.244342857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="340.857142857143" x2="340.857142857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="59.857142857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244442857143" x2="223.244442857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="223.244342857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="340.857142857143" x2="340.857142857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="555.3204999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="59.857142857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="555.3204999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="555.3204999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244442857143" x2="223.244442857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="555.3204999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="223.244342857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="555.3204999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="340.857142857143" x2="340.857142857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="555.3205999999999" y2="555.3205999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="59.857142857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="555.3205999999999" y2="555.3205999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="555.3205999999999" y2="555.3205999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244442857143" x2="223.244442857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="555.3205999999999" y2="555.3205999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="223.244342857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="555.3205999999999" y2="555.3205999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="340.857142857143" x2="340.857142857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="604.6794" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="59.857142857143" y1="579.9999999999999" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="579.9999999999999" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="604.6794" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="579.9999999999999" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="579.9999999999999" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="604.6794" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="579.9999999999999" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244442857143" x2="223.244442857143" y1="579.9999999999999" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="604.6794" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="223.244342857143" y1="579.9999999999999" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="579.9999999999999" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="604.6794" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="579.9999999999999" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="340.857142857143" x2="340.857142857143" y1="579.9999999999999" y2="604.6794"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,136.857,460.5) scale(1,1) translate(0,0)" writing-mode="lr" x="136.857142857143" xml:space="preserve" y="464.9999999999999" zvalue="345">35kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,206.256,317.841) scale(1,1) translate(0,0)" writing-mode="lr" x="206.26" xml:space="preserve" y="322.34" zvalue="346">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,311.256,317.841) scale(1,1) translate(0,0)" writing-mode="lr" x="311.26" xml:space="preserve" y="322.34" zvalue="347">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,254.19,460.5) scale(1,1) translate(0,0)" writing-mode="lr" x="254.1904761904765" xml:space="preserve" y="464.9999999999999" zvalue="348">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84.8571,493.5) scale(1,1) translate(0,0)" writing-mode="lr" x="84.857142857143" xml:space="preserve" y="497.9999999999999" zvalue="349">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84.8571,519) scale(1,1) translate(0,0)" writing-mode="lr" x="84.857142857143" xml:space="preserve" y="523.5" zvalue="350">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84.8571,544.5) scale(1,1) translate(0,5.88418e-14)" writing-mode="lr" x="84.857142857143" xml:space="preserve" y="548.9999999999999" zvalue="351">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84.8571,570) scale(1,1) translate(0,0)" writing-mode="lr" x="84.857142857143" xml:space="preserve" y="574.5" zvalue="352">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84.8571,595.5) scale(1,1) translate(0,0)" writing-mode="lr" x="84.857142857143" xml:space="preserve" y="600" zvalue="353">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50.8571,177) scale(1,1) translate(0,0)" writing-mode="lr" x="50.86" xml:space="preserve" y="181.5" zvalue="354">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.857,177) scale(1,1) translate(0,0)" writing-mode="lr" x="226.86" xml:space="preserve" y="181.5" zvalue="355">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.3571,201.25) scale(1,1) translate(0,0)" writing-mode="lr" x="57.36" xml:space="preserve" y="205.75" zvalue="356">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.3571,249) scale(1,1) translate(0,0)" writing-mode="lr" x="51.36" xml:space="preserve" y="253.5" zvalue="357">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.3571,272) scale(1,1) translate(0,0)" writing-mode="lr" x="51.36" xml:space="preserve" y="276.5" zvalue="358">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.8571,225.25) scale(1,1) translate(0,0)" writing-mode="lr" x="56.86" xml:space="preserve" y="229.75" zvalue="359">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="821" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,498.5,687.607) scale(1,1) translate(0,0)" writing-mode="lr" x="498.5" xml:space="preserve" y="692.11" zvalue="1015">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="820" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,715.071,575.286) scale(1,1) translate(-4.61012e-13,0)" writing-mode="lr" x="715.0700000000001" xml:space="preserve" y="579.79" zvalue="1016">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="819" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,686.5,504.657) scale(1,1) translate(0,0)" writing-mode="lr" x="686.5" xml:space="preserve" y="509.16" zvalue="1018">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="818" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,679.5,450.5) scale(1,1) translate(0,0)" writing-mode="lr" x="679.5" xml:space="preserve" y="455" zvalue="1022">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="817" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,695.128,655.389) scale(1,1) translate(0,0)" writing-mode="lr" x="695.13" xml:space="preserve" y="659.89" zvalue="1024">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="816" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,621.214,496.214) scale(1,1) translate(0,-1.07961e-13)" writing-mode="lr" x="621.21" xml:space="preserve" y="500.71" zvalue="1027">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1090" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,895.25,587.625) scale(1,1) translate(0,0)" writing-mode="lr" x="895.25" xml:space="preserve" y="592.13" zvalue="1031">10kVⅡ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="815" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,856.036,677.171) scale(1,1) translate(0,0)" writing-mode="lr" x="856.04" xml:space="preserve" y="681.67" zvalue="1032">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="814" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,548.251,756.5) scale(1,1) translate(0,0)" writing-mode="lr" x="548.25" xml:space="preserve" y="761" zvalue="1035">091</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="812" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,484.768,862.031) scale(1,1) translate(0,0)" writing-mode="lr" x="484.77" xml:space="preserve" y="866.53" zvalue="1040">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="811" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,469.5,802.5) scale(1,1) translate(0,0)" writing-mode="lr" x="469.5" xml:space="preserve" y="807" zvalue="1044">27</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="810" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,524.298,992) scale(1,1) translate(0,0)" writing-mode="lr" x="524.3" xml:space="preserve" y="996.5" zvalue="1046">10kV2号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="840" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,719.071,597.714) scale(1,1) translate(3.03154e-13,-1.30451e-13)" writing-mode="lr" x="719.0700000000001" xml:space="preserve" y="602.21" zvalue="1101">8000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="792" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,556.9,831.3) scale(1,1) translate(0,0)" writing-mode="lr" x="556.9" xml:space="preserve" y="835.8" zvalue="1115">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="910" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1475.81,999) scale(1,1) translate(0,0)" writing-mode="lr" x="1475.81" xml:space="preserve" y="1003.5" zvalue="1133">备用1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="909" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1368.75,756.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1368.75" xml:space="preserve" y="761" zvalue="1136">084</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="908" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1370.66,860.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1370.66" xml:space="preserve" y="865.25" zvalue="1139">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="907" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1296.25,804.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1296.25" xml:space="preserve" y="809" zvalue="1143">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="906" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1345.72,939.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1345.72" xml:space="preserve" y="943.75" zvalue="1145">10kV劈石线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="905" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1239.68,756.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1239.68" xml:space="preserve" y="761" zvalue="1148">085</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="904" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1241.66,862.861) scale(1,1) translate(0,0)" writing-mode="lr" x="1241.66" xml:space="preserve" y="867.36" zvalue="1151">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="903" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1161.64,804.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1161.64" xml:space="preserve" y="809" zvalue="1155">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="902" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1216.72,941.361) scale(1,1) translate(0,0)" writing-mode="lr" x="1216.72" xml:space="preserve" y="945.86" zvalue="1157">10kV勐嘎线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="955" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1780.47,853.053) scale(1,1) translate(0,0)" writing-mode="lr" x="1780.47" xml:space="preserve" y="857.55" zvalue="1171">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1101.03,754) scale(1,1) translate(0,0)" writing-mode="lr" x="1101.03" xml:space="preserve" y="758.5" zvalue="1191">086</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="965" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1091.42,865.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1091.42" xml:space="preserve" y="870.25" zvalue="1193">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="964" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1024.17,804.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1024.17" xml:space="preserve" y="809" zvalue="1197">27</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="975" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1079.42,941.75) scale(1,1) translate(2.31352e-13,0)" writing-mode="lr" x="1079.42" xml:space="preserve" y="946.25" zvalue="1203">10kV苏典线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="990" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,952.917,999) scale(1,1) translate(0,0)" writing-mode="lr" x="952.92" xml:space="preserve" y="1003.5" zvalue="1233">备用2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1030" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,828.917,997.75) scale(1,1) translate(-3.59897e-13,0)" writing-mode="lr" x="828.92" xml:space="preserve" y="1002.25" zvalue="1275">备用3</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1050" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,695.417,1000.25) scale(1,1) translate(0,0)" writing-mode="lr" x="695.42" xml:space="preserve" y="1004.75" zvalue="1296">备用4</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1080" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1127.23,613.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1127.232142857143" xml:space="preserve" y="617.714284488133" zvalue="1320">10kV分段012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1079" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1059.39,677.935) scale(1,1) translate(0,0)" writing-mode="lr" x="1059.39" xml:space="preserve" y="682.4299999999999" zvalue="1323">2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,314.19,460.5) scale(1,1) translate(0,0)" writing-mode="lr" x="314.1904761904766" xml:space="preserve" y="464.9999999999999" zvalue="1340">10kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.357,247) scale(1,1) translate(0,0)" writing-mode="lr" x="232.36" xml:space="preserve" y="251.5" zvalue="1347">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.357,270) scale(1,1) translate(0,0)" writing-mode="lr" x="232.36" xml:space="preserve" y="274.5" zvalue="1348">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.857,223.25) scale(1,1) translate(0,0)" writing-mode="lr" x="237.86" xml:space="preserve" y="227.75" zvalue="1349">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,882,76.5) scale(1,1) translate(0,0)" writing-mode="lr" x="882" xml:space="preserve" y="81" zvalue="1440">35kV勐苏线</text>
  <ellipse cx="641.66" cy="504.66" fill="rgb(255,0,0)" fill-opacity="1" id="289" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1478"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="294" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,292.235,404) scale(1,1) translate(0,-1.74083e-13)" writing-mode="lr" x="292.2350158691406" xml:space="preserve" y="408.5" zvalue="1482">小电流接地</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="293" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94.625,318.539) scale(1,1) translate(0,3.41327e-13)" writing-mode="lr" x="94.625" xml:space="preserve" y="323.0388641357421" zvalue="1483">全站公用</text>
 </g>
 <g id="ButtonClass">
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="160.09" y="392" zvalue="468"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="58.19" y="392" zvalue="469"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="58.19" y="348.75" zvalue="470"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="35">
   <path class="kv35" d="M 555 402.86 L 1517 402.86" stroke-width="4" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674402304003" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674402304003"/></metadata>
  <path d="M 555 402.86 L 1517 402.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv10" d="M 1154.75 709.61 L 1814.89 709.61" stroke-width="4" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674402369539" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674402369539"/></metadata>
  <path d="M 1154.75 709.61 L 1814.89 709.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="900">
   <path class="kv10" d="M 461 711.36 L 1103.5 711.36" stroke-width="4" zvalue="1013"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674238398469" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674238398469"/></metadata>
  <path d="M 461 711.36 L 1103.5 711.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="39">
   <g id="390">
    <use class="kv35" height="30" transform="rotate(0,1409.28,568) scale(2.5,2.46667) translate(-827.57,-315.73)" width="24" x="1379.28" xlink:href="#PowerTransformer2:可调不带中性点_0" y="531" zvalue="39"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874571644930" ObjectName="35"/>
    </metadata>
   </g>
   <g id="391">
    <use class="kv10" height="30" transform="rotate(0,1409.28,568) scale(2.5,2.46667) translate(-827.57,-315.73)" width="24" x="1379.28" xlink:href="#PowerTransformer2:可调不带中性点_1" y="531" zvalue="39"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874571710466" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399525531650" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399525531650"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1409.28,568) scale(2.5,2.46667) translate(-827.57,-315.73)" width="24" x="1379.28" y="531"/></g>
  <g id="899">
   <g id="8990">
    <use class="kv35" height="30" transform="rotate(0,658.643,568) scale(2.5,2.46667) translate(-377.186,-315.73)" width="24" x="628.64" xlink:href="#PowerTransformer2:可调两卷变_0" y="531" zvalue="1014"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874427531268" ObjectName="35"/>
    </metadata>
   </g>
   <g id="8991">
    <use class="kv10" height="30" transform="rotate(0,658.643,568) scale(2.5,2.46667) translate(-377.186,-315.73)" width="24" x="628.64" xlink:href="#PowerTransformer2:可调两卷变_1" y="531" zvalue="1014"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874427596804" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399446036484" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399446036484"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,658.643,568) scale(2.5,2.46667) translate(-377.186,-315.73)" width="24" x="628.64" y="531"/></g>
 </g>
 <g id="BreakerClass">
  <g id="43">
   <use class="kv35" height="20" transform="rotate(0,1409.21,505) scale(1.5542,1.35421) translate(-499.727,-128.547)" width="10" x="1401.436759042017" xlink:href="#Breaker:开关_0" y="491.4578952882238" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925108957187" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925108957187"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1409.21,505) scale(1.5542,1.35421) translate(-499.727,-128.547)" width="10" x="1401.436759042017" y="491.4578952882238"/></g>
  <g id="63">
   <use class="kv10" height="20" transform="rotate(0,1409.28,654.639) scale(2.22222,2.22222) translate(-768.995,-347.829)" width="10" x="1398.17282317591" xlink:href="#Breaker:小车断路器_0" y="632.4166666666667" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925109022723" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925109022723"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1409.28,654.639) scale(2.22222,2.22222) translate(-768.995,-347.829)" width="10" x="1398.17282317591" y="632.4166666666667"/></g>
  <g id="78">
   <use class="kv35" height="20" transform="rotate(0,881.75,308) scale(1.5542,1.35421) translate(-311.645,-77.0191)" width="10" x="873.9790032033688" xlink:href="#Breaker:开关_0" y="294.4578952882238" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925109153795" ObjectName="35kV勐苏线391断路器"/>
   <cge:TPSR_Ref TObjectID="6473925109153795"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,881.75,308) scale(1.5542,1.35421) translate(-311.645,-77.0191)" width="10" x="873.9790032033688" y="294.4578952882238"/></g>
  <g id="130">
   <use class="kv10" height="20" transform="rotate(0,1765.75,755) scale(2.22222,2.22222) translate(-965.052,-403.028)" width="10" x="1754.639640280912" xlink:href="#Breaker:小车断路器_0" y="732.7777691947091" zvalue="130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925109088259" ObjectName="10kV1号电容器081断路器"/>
   <cge:TPSR_Ref TObjectID="6473925109088259"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1765.75,755) scale(2.22222,2.22222) translate(-965.052,-403.028)" width="10" x="1754.639640280912" y="732.7777691947091"/></g>
  <g id="188">
   <use class="kv10" height="20" transform="rotate(0,1601,755) scale(2.22222,2.22222) translate(-874.439,-403.028)" width="10" x="1589.888888888889" xlink:href="#Breaker:小车断路器_0" y="732.7777693006727" zvalue="170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925109219331" ObjectName="10kV黄草坝线082断路器"/>
   <cge:TPSR_Ref TObjectID="6473925109219331"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1601,755) scale(2.22222,2.22222) translate(-874.439,-403.028)" width="10" x="1589.888888888889" y="732.7777693006727"/></g>
  <g id="898">
   <use class="kv35" height="20" transform="rotate(0,658.5,505.5) scale(1.5542,1.35421) translate(-232.038,-128.678)" width="10" x="650.7290032033688" xlink:href="#Breaker:开关_0" y="491.9578952882238" zvalue="1017"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924490887173" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473924490887173"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,658.5,505.5) scale(1.5542,1.35421) translate(-232.038,-128.678)" width="10" x="650.7290032033688" y="491.9578952882238"/></g>
  <g id="893">
   <use class="kv10" height="20" transform="rotate(0,658.034,656.389) scale(2.22222,2.22222) translate(-355.808,-348.792)" width="10" x="646.9228231759096" xlink:href="#Breaker:小车断路器_0" y="634.1666666666667" zvalue="1023"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924490821637" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473924490821637"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,658.034,656.389) scale(2.22222,2.22222) translate(-355.808,-348.792)" width="10" x="646.9228231759096" y="634.1666666666667"/></g>
  <g id="885">
   <use class="kv10" height="20" transform="rotate(0,525.251,755) scale(2.22222,2.22222) translate(-282.777,-403.028)" width="10" x="514.1396402809124" xlink:href="#Breaker:小车断路器_0" y="732.7777693006727" zvalue="1034"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924490756101" ObjectName="10kV2号电容器091断路器"/>
   <cge:TPSR_Ref TObjectID="6473924490756101"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,525.251,755) scale(2.22222,2.22222) translate(-282.777,-403.028)" width="10" x="514.1396402809124" y="732.7777693006727"/></g>
  <g id="943">
   <use class="kv10" height="20" transform="rotate(0,1472.75,755) scale(2.22222,2.22222) translate(-803.901,-403.028)" width="10" x="1461.638888888889" xlink:href="#Breaker:小车断路器_0" y="732.7777693006727" zvalue="1123"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925109284867" ObjectName="10kV备用1线092断路器"/>
   <cge:TPSR_Ref TObjectID="6473925109284867"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1472.75,755) scale(2.22222,2.22222) translate(-803.901,-403.028)" width="10" x="1461.638888888889" y="732.7777693006727"/></g>
  <g id="935">
   <use class="kv10" height="20" transform="rotate(0,1345.75,755) scale(2.22222,2.22222) translate(-734.051,-403.028)" width="10" x="1334.638888888889" xlink:href="#Breaker:小车断路器_0" y="732.7777693006727" zvalue="1135"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924493115397" ObjectName="10kV劈石线084断路器"/>
   <cge:TPSR_Ref TObjectID="6473924493115397"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1345.75,755) scale(2.22222,2.22222) translate(-734.051,-403.028)" width="10" x="1334.638888888889" y="732.7777693006727"/></g>
  <g id="927">
   <use class="kv10" height="20" transform="rotate(0,1216.68,755) scale(2.22222,2.22222) translate(-663.06,-403.028)" width="10" x="1205.564444444445" xlink:href="#Breaker:小车断路器_0" y="732.7777693006726" zvalue="1147"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924493049861" ObjectName="10kV勐嘎线085断路器"/>
   <cge:TPSR_Ref TObjectID="6473924493049861"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1216.68,755) scale(2.22222,2.22222) translate(-663.06,-403.028)" width="10" x="1205.564444444445" y="732.7777693006726"/></g>
  <g id="974">
   <use class="kv10" height="20" transform="rotate(0,1076.42,755) scale(2.22222,2.22222) translate(-585.918,-403.028)" width="10" x="1065.305555555556" xlink:href="#Breaker:小车断路器_0" y="732.7777693006727" zvalue="1190"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924491083781" ObjectName="10kV苏典线086断路器"/>
   <cge:TPSR_Ref TObjectID="6473924491083781"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1076.42,755) scale(2.22222,2.22222) translate(-585.918,-403.028)" width="10" x="1065.305555555556" y="732.7777693006727"/></g>
  <g id="1009">
   <use class="kv10" height="20" transform="rotate(0,949.917,755) scale(2.22222,2.22222) translate(-516.343,-403.028)" width="10" x="938.8055555555558" xlink:href="#Breaker:小车断路器_0" y="732.7777693006726" zvalue="1222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924491149317" ObjectName="备用2断路器"/>
   <cge:TPSR_Ref TObjectID="6473924491149317"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,949.917,755) scale(2.22222,2.22222) translate(-516.343,-403.028)" width="10" x="938.8055555555558" y="732.7777693006726"/></g>
  <g id="1049">
   <use class="kv10" height="20" transform="rotate(0,825.917,755) scale(2.22222,2.22222) translate(-448.143,-403.028)" width="10" x="814.8055555555559" xlink:href="#Breaker:小车断路器_0" y="732.7777693006726" zvalue="1264"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924491214853" ObjectName="备用3断路器"/>
   <cge:TPSR_Ref TObjectID="6473924491214853"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,825.917,755) scale(2.22222,2.22222) translate(-448.143,-403.028)" width="10" x="814.8055555555559" y="732.7777693006726"/></g>
  <g id="1069">
   <use class="kv10" height="20" transform="rotate(0,692.504,755) scale(2.22222,2.22222) translate(-374.766,-403.028)" width="10" x="681.3933529514693" xlink:href="#Breaker:小车断路器_0" y="732.7777693006726" zvalue="1285"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924491280389" ObjectName="备用4断路器"/>
   <cge:TPSR_Ref TObjectID="6473924491280389"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,692.504,755) scale(2.22222,2.22222) translate(-374.766,-403.028)" width="10" x="681.3933529514693" y="732.7777693006726"/></g>
  <g id="1085">
   <use class="kv10" height="20" transform="rotate(90,1130.57,643.321) scale(2.22222,2.22222) translate(-615.703,-341.605)" width="10" x="1119.460317460317" xlink:href="#Breaker:母联小车开关_0" y="621.0992063492062" zvalue="1319"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924491345925" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473924491345925"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1130.57,643.321) scale(2.22222,2.22222) translate(-615.703,-341.605)" width="10" x="1119.460317460317" y="621.0992063492062"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="45">
   <path class="kv35" d="M 1409.31 533.66 L 1409.31 517.93" stroke-width="1" zvalue="43"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@0" LinkObjectIDznd="43@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.31 533.66 L 1409.31 517.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv35" d="M 1409.16 492.04 L 1409.16 461.81" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="47@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.16 492.04 L 1409.16 461.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv35" d="M 1409.18 440.36 L 1409.18 402.86" stroke-width="1" zvalue="47"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.18 440.36 L 1409.18 402.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv35" d="M 1387.64 479.21 L 1409.16 479.21" stroke-width="1" zvalue="72"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="49" MaxPinNum="2"/>
   </metadata>
  <path d="M 1387.64 479.21 L 1409.16 479.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv35" d="M 950.25 154 L 932.56 154.06" stroke-width="1" zvalue="104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="99@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 950.25 154 L 932.56 154.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv35" d="M 1112.96 402.86 L 1113.06 348.81" stroke-width="1" zvalue="109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@4" LinkObjectIDznd="108@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1112.96 402.86 L 1113.06 348.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv35" d="M 1113.09 327.36 L 1113.09 260.8" stroke-width="1" zvalue="111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="115@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1113.09 327.36 L 1113.09 260.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv35" d="M 1136.75 293.6 L 1113.09 293.6" stroke-width="1" zvalue="113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="113" MaxPinNum="2"/>
   </metadata>
  <path d="M 1136.75 293.6 L 1113.09 293.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv35" d="M 1136.75 373.6 L 1113.02 373.6" stroke-width="1" zvalue="116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="114" MaxPinNum="2"/>
   </metadata>
  <path d="M 1136.75 373.6 L 1113.02 373.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv35" d="M 1023.55 424.96 L 1023.55 402.86" stroke-width="1" zvalue="120"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="35@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.55 424.96 L 1023.55 402.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv35" d="M 1023.31 461.39 L 1023.31 498.5" stroke-width="1" zvalue="121"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@1" LinkObjectIDznd="117@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.31 461.39 L 1023.31 498.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv10" d="M 1276.77 690.61 L 1276.77 709.61" stroke-width="1" zvalue="126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@1" LinkObjectIDznd="37@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1276.77 690.61 L 1276.77 709.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv35" d="M 882 119.85 L 882 227.36" stroke-width="1" zvalue="156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@0" LinkObjectIDznd="81@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 882 119.85 L 882 227.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv35" d="M 881.81 248.81 L 881.7 295.04" stroke-width="1" zvalue="157"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@1" LinkObjectIDznd="78@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 881.81 248.81 L 881.7 295.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv35" d="M 881.85 320.93 L 881.84 359.36" stroke-width="1" zvalue="158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@1" LinkObjectIDznd="83@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 881.85 320.93 L 881.84 359.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv35" d="M 881.81 380.81 L 881.81 402.86" stroke-width="1" zvalue="160"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@1" LinkObjectIDznd="35@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 881.81 380.81 L 881.81 402.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="kv35" d="M 905.5 341.6 L 881.85 341.6" stroke-width="1" zvalue="161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="158" MaxPinNum="2"/>
   </metadata>
  <path d="M 905.5 341.6 L 881.85 341.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv35" d="M 905.5 273.6 L 881.75 273.6" stroke-width="1" zvalue="162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="157" MaxPinNum="2"/>
   </metadata>
  <path d="M 905.5 273.6 L 881.75 273.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv35" d="M 905.5 193.6 L 882 193.6" stroke-width="1" zvalue="163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="156" MaxPinNum="2"/>
   </metadata>
  <path d="M 905.5 193.6 L 882 193.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv35" d="M 911.11 154.09 L 882 154.09" stroke-width="1" zvalue="164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="156" MaxPinNum="2"/>
   </metadata>
  <path d="M 911.11 154.09 L 882 154.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="kv35" d="M 860.78 137.63 L 882 137.63 L 882 161.42" stroke-width="1" zvalue="165"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="156" MaxPinNum="2"/>
   </metadata>
  <path d="M 860.78 137.63 L 882 137.63 L 882 161.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv10" d="M 1600.97 899.5 L 1600.97 855.81" stroke-width="1" zvalue="176"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="184@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1600.97 899.5 L 1600.97 855.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="246">
   <path class="kv10" d="M 1664.57 927.5 L 1664.57 938" stroke-width="1" zvalue="254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@1" LinkObjectIDznd="249@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1664.57 927.5 L 1664.57 938" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="255">
   <path class="kv10" d="M 1664.81 891.07 L 1664.81 881.66 L 1600.97 881.66" stroke-width="1" zvalue="260"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@0" LinkObjectIDznd="183" MaxPinNum="2"/>
   </metadata>
  <path d="M 1664.81 891.07 L 1664.81 881.66 L 1600.97 881.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="254">
   <path class="kv10" d="M 1601 834.36 L 1601 775" stroke-width="1" zvalue="319"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="188@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1601 834.36 L 1601 775" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="256">
   <path class="kv10" d="M 1601 734.44 L 1601 709.61" stroke-width="1" zvalue="320"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@0" LinkObjectIDznd="37@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1601 734.44 L 1601 709.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="257">
   <path class="kv10" d="M 1765.75 734.44 L 1765.75 709.61" stroke-width="1" zvalue="321"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="37@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1765.75 734.44 L 1765.75 709.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="259">
   <path class="kv10" d="M 1409.28 634.08 L 1409.28 602.65" stroke-width="1" zvalue="323"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="39@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.28 634.08 L 1409.28 602.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="260">
   <path class="kv10" d="M 1409.28 674.64 L 1409.28 709.61" stroke-width="1" zvalue="324"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@1" LinkObjectIDznd="37@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.28 674.64 L 1409.28 709.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="261">
   <path class="kv10" d="M 1276.73 658.3 L 1276.74 665.75" stroke-width="1" zvalue="325"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@0" LinkObjectIDznd="123@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1276.73 658.3 L 1276.74 665.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="896">
   <path class="kv35" d="M 658.67 533.66 L 658.6 518.43" stroke-width="1" zvalue="1019"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="899@0" LinkObjectIDznd="898@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 658.67 533.66 L 658.6 518.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="894">
   <path class="kv35" d="M 658.45 492.54 L 658.56 462.31" stroke-width="1" zvalue="1021"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="898@0" LinkObjectIDznd="895@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 658.45 492.54 L 658.56 462.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="891">
   <path class="kv35" d="M 635.14 479.71 L 658.5 479.71" stroke-width="1" zvalue="1026"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="892@0" LinkObjectIDznd="894" MaxPinNum="2"/>
   </metadata>
  <path d="M 635.14 479.71 L 658.5 479.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="886">
   <path class="kv10" d="M 891.77 690.61 L 891.77 711.36" stroke-width="1" zvalue="1033"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="887@1" LinkObjectIDznd="900@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 891.77 690.61 L 891.77 711.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="831">
   <path class="kv10" d="M 525.25 734.44 L 525.25 711.36" stroke-width="1" zvalue="1110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="885@0" LinkObjectIDznd="900@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 525.25 734.44 L 525.25 711.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="830">
   <path class="kv10" d="M 658.03 635.83 L 658.03 602.65" stroke-width="1" zvalue="1111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="893@0" LinkObjectIDznd="899@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 658.03 635.83 L 658.03 602.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="829">
   <path class="kv10" d="M 658.03 676.39 L 658.03 711.36" stroke-width="1" zvalue="1112"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="893@1" LinkObjectIDznd="900@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 658.03 676.39 L 658.03 711.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="828">
   <path class="kv10" d="M 891.36 658.3 L 891.36 665.75" stroke-width="1" zvalue="1113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="888@0" LinkObjectIDznd="887@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 891.36 658.3 L 891.36 665.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="826">
   <path class="kv10" d="M 525.25 824.66 L 525.25 775" stroke-width="1" zvalue="1116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="827@0" LinkObjectIDznd="885@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 525.25 824.66 L 525.25 775" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="901">
   <path class="kv35" d="M 658.59 402.86 L 658.59 440.86" stroke-width="1" zvalue="1121"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@1" LinkObjectIDznd="895@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 658.59 402.86 L 658.59 440.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="941">
   <path class="kv10" d="M 1472.81 960 L 1472.81 909.59" stroke-width="1" zvalue="1126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="937@0" LinkObjectIDznd="942@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472.81 960 L 1472.81 909.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="933">
   <path class="kv10" d="M 1345.72 900.25 L 1345.72 872.56" stroke-width="1" zvalue="1138"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="929@0" LinkObjectIDznd="934@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1345.72 900.25 L 1345.72 872.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="925">
   <path class="kv10" d="M 1216.72 902.36 L 1216.72 874.67" stroke-width="1" zvalue="1150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="921@0" LinkObjectIDznd="926@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1216.72 902.36 L 1216.72 874.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="919">
   <path class="kv10" d="M 1216.68 734.44 L 1216.68 709.61" stroke-width="1" zvalue="1159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="927@0" LinkObjectIDznd="37@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1216.68 734.44 L 1216.68 709.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="918">
   <path class="kv10" d="M 1216.75 853.22 L 1216.68 775" stroke-width="1" zvalue="1160"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="926@0" LinkObjectIDznd="927@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1216.75 853.22 L 1216.68 775" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="917">
   <path class="kv10" d="M 1345.75 851.11 L 1345.75 775" stroke-width="1" zvalue="1161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="934@0" LinkObjectIDznd="935@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1345.75 851.11 L 1345.75 775" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="915">
   <path class="kv10" d="M 1472.75 734.44 L 1472.75 709.61" stroke-width="1" zvalue="1163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="943@0" LinkObjectIDznd="37@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472.75 734.44 L 1472.75 709.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="914">
   <path class="kv10" d="M 1472.84 888.14 L 1472.75 775" stroke-width="1" zvalue="1164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="942@0" LinkObjectIDznd="943@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472.84 888.14 L 1472.75 775" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="948">
   <path class="kv10" d="M 1765.75 775 L 1765.75 838.42" stroke-width="1" zvalue="1171"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@1" LinkObjectIDznd="947@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1765.75 775 L 1765.75 838.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="952">
   <path class="kv10" d="M 1786.5 825.1 L 1765.75 825.1" stroke-width="1" zvalue="1175"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="143@0" LinkObjectIDznd="948" MaxPinNum="2"/>
   </metadata>
  <path d="M 1786.5 825.1 L 1765.75 825.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="958">
   <path class="kv10" d="M 1194.44 890.59 L 1194.44 883.75 L 1216.72 883.75" stroke-width="1" zvalue="1182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="956@0" LinkObjectIDznd="925" MaxPinNum="2"/>
   </metadata>
  <path d="M 1194.44 890.59 L 1194.44 883.75 L 1216.72 883.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="959">
   <path class="kv10" d="M 1324.3 893.3 L 1324.3 881.5 L 1345.72 881.5" stroke-width="1" zvalue="1183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="957@0" LinkObjectIDznd="933" MaxPinNum="2"/>
   </metadata>
  <path d="M 1324.3 893.3 L 1324.3 881.5 L 1345.72 881.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="960">
   <path class="kv10" d="M 1345.75 734.44 L 1345.75 709.61" stroke-width="1" zvalue="1184"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="935@0" LinkObjectIDznd="37@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1345.75 734.44 L 1345.75 709.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="963">
   <path class="kv10" d="M 1453.8 955.21 L 1453.8 946.78 L 1472.81 946.78" stroke-width="1" zvalue="1188"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="962@0" LinkObjectIDznd="941" MaxPinNum="2"/>
   </metadata>
  <path d="M 1453.8 955.21 L 1453.8 946.78 L 1472.81 946.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="968">
   <path class="kv10" d="M 1076.42 734.44 L 1076.42 711.36" stroke-width="1" zvalue="1199"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="974@0" LinkObjectIDznd="900@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1076.42 734.44 L 1076.42 711.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="967">
   <path class="kv10" d="M 1076.5 853.61 L 1076.42 775" stroke-width="1" zvalue="1200"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="973@0" LinkObjectIDznd="974@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1076.5 853.61 L 1076.42 775" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="984">
   <path class="kv10" d="M 1076.48 875.06 L 1076.42 901.4" stroke-width="1" zvalue="1214"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="973@1" LinkObjectIDznd="978@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1076.48 875.06 L 1076.42 901.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1003">
   <path class="kv10" d="M 949.92 734.44 L 949.92 711.36" stroke-width="1" zvalue="1230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1009@0" LinkObjectIDznd="900@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 949.92 734.44 L 949.92 711.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1002">
   <path class="kv10" d="M 950 888.14 L 949.92 775" stroke-width="1" zvalue="1231"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1008@0" LinkObjectIDznd="1009@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 950 888.14 L 949.92 775" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="997">
   <path class="kv10" d="M 949.98 909.59 L 949.92 960" stroke-width="1" zvalue="1237"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1008@1" LinkObjectIDznd="1001@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 949.98 909.59 L 949.92 960" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="994">
   <path class="kv10" d="M 949.93 946 L 930.8 946 L 930.8 955.21" stroke-width="1" zvalue="1240"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="997" LinkObjectIDznd="1000@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 949.93 946 L 930.8 946 L 930.8 955.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1043">
   <path class="kv10" d="M 825.92 734.44 L 825.92 711.36" stroke-width="1" zvalue="1272"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1049@0" LinkObjectIDznd="900@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.92 734.44 L 825.92 711.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1042">
   <path class="kv10" d="M 826 886.89 L 825.92 775" stroke-width="1" zvalue="1273"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1048@0" LinkObjectIDznd="1049@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 826 886.89 L 825.92 775" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1037">
   <path class="kv10" d="M 825.98 908.34 L 825.92 958.75" stroke-width="1" zvalue="1279"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1048@1" LinkObjectIDznd="1041@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.98 908.34 L 825.92 958.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1034">
   <path class="kv10" d="M 825.93 944.75 L 806.8 944.75 L 806.8 953.96" stroke-width="1" zvalue="1282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1037" LinkObjectIDznd="1040@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.93 944.75 L 806.8 944.75 L 806.8 953.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1063">
   <path class="kv10" d="M 692.5 734.44 L 692.5 711.36" stroke-width="1" zvalue="1293"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1069@0" LinkObjectIDznd="900@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 692.5 734.44 L 692.5 711.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1062">
   <path class="kv10" d="M 692.5 889.39 L 692.5 775" stroke-width="1" zvalue="1294"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1068@0" LinkObjectIDznd="1069@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 692.5 889.39 L 692.5 775" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1057">
   <path class="kv10" d="M 692.48 910.84 L 692.42 961.25" stroke-width="1" zvalue="1300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1068@1" LinkObjectIDznd="1061@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 692.48 910.84 L 692.42 961.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1054">
   <path class="kv10" d="M 692.43 947.25 L 673.3 947.25 L 673.3 956.46" stroke-width="1" zvalue="1303"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1057" LinkObjectIDznd="1060@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 692.43 947.25 L 673.3 947.25 L 673.3 956.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1071">
   <path class="kv10" d="M 621.55 644.34 L 621.55 619.75 L 658.03 619.75" stroke-width="1" zvalue="1307"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1070@0" LinkObjectIDznd="830" MaxPinNum="2"/>
   </metadata>
  <path d="M 621.55 644.34 L 621.55 619.75 L 658.03 619.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1073">
   <path class="kv10" d="M 1372.8 641.84 L 1372.8 618.37 L 1409.28 618.37" stroke-width="1" zvalue="1310"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1072@0" LinkObjectIDznd="259" MaxPinNum="2"/>
   </metadata>
  <path d="M 1372.8 641.84 L 1372.8 618.37 L 1409.28 618.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1078">
   <path class="kv10" d="M 1678.58 894.05 L 1678.58 881.66 L 1664.27 881.66" stroke-width="1" zvalue="1317"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1075@0" LinkObjectIDznd="255" MaxPinNum="2"/>
   </metadata>
  <path d="M 1678.58 894.05 L 1678.58 881.66 L 1664.27 881.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1086">
   <path class="kv10" d="M 1085.35 690.61 L 1085.35 711.36" stroke-width="1" zvalue="1326"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1084@1" LinkObjectIDznd="900@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1085.35 690.61 L 1085.35 711.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1088">
   <path class="kv10" d="M 1085.31 665.75 L 1085.31 643.32 L 1110.57 643.32" stroke-width="1" zvalue="1328"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1084@0" LinkObjectIDznd="1085@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1085.31 665.75 L 1085.31 643.32 L 1110.57 643.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1089">
   <path class="kv10" d="M 1150.68 643.43 L 1174.75 643.43 L 1174.75 709.61" stroke-width="1" zvalue="1329"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1085@0" LinkObjectIDznd="37@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1150.68 643.43 L 1174.75 643.43 L 1174.75 709.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv10" d="M 1053.3 890.09 L 1053.3 882 L 1076.46 882" stroke-width="1" zvalue="1336"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="977@0" LinkObjectIDznd="984" MaxPinNum="2"/>
   </metadata>
  <path d="M 1053.3 890.09 L 1053.3 882 L 1076.46 882" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv10" d="M 1609.75 869.25 L 1600.97 869.25" stroke-width="1" zvalue="1338"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1074@0" LinkObjectIDznd="183" MaxPinNum="2"/>
   </metadata>
  <path d="M 1609.75 869.25 L 1600.97 869.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="kv10" d="M 513.43 804.29 L 525.25 804.29" stroke-width="1" zvalue="1371"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="879@0" LinkObjectIDznd="826" MaxPinNum="2"/>
   </metadata>
  <path d="M 513.43 804.29 L 525.25 804.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv10" d="M 534.22 804.29 L 525.25 804.29" stroke-width="1" zvalue="1372"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="881@0" LinkObjectIDznd="826" MaxPinNum="2"/>
   </metadata>
  <path d="M 534.22 804.29 L 525.25 804.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="kv10" d="M 681.1 804.29 L 692.5 804.29" stroke-width="1" zvalue="1373"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1065@0" LinkObjectIDznd="1062" MaxPinNum="2"/>
   </metadata>
  <path d="M 681.1 804.29 L 692.5 804.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv10" d="M 701.5 804.29 L 692.5 804.29" stroke-width="1" zvalue="1374"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1067@0" LinkObjectIDznd="1062" MaxPinNum="2"/>
   </metadata>
  <path d="M 701.5 804.29 L 692.5 804.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv10" d="M 814.6 803.29 L 825.94 803.29" stroke-width="1" zvalue="1375"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1045@0" LinkObjectIDznd="1042" MaxPinNum="2"/>
   </metadata>
  <path d="M 814.6 803.29 L 825.94 803.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv10" d="M 835 803.17 L 825.94 803.17" stroke-width="1" zvalue="1376"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1047@0" LinkObjectIDznd="1042" MaxPinNum="2"/>
   </metadata>
  <path d="M 835 803.17 L 825.94 803.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv10" d="M 938.6 804.29 L 949.94 804.29" stroke-width="1" zvalue="1377"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1005@0" LinkObjectIDznd="1002" MaxPinNum="2"/>
   </metadata>
  <path d="M 938.6 804.29 L 949.94 804.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv10" d="M 960 804.17 L 949.94 804.17" stroke-width="1" zvalue="1378"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1007@0" LinkObjectIDznd="1002" MaxPinNum="2"/>
   </metadata>
  <path d="M 960 804.17 L 949.94 804.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="220">
   <path class="kv10" d="M 1065.1 804.29 L 1076.45 804.29" stroke-width="1" zvalue="1380"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="970@0" LinkObjectIDznd="967" MaxPinNum="2"/>
   </metadata>
  <path d="M 1065.1 804.29 L 1076.45 804.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv10" d="M 1203.57 804.29 L 1216.7 804.29" stroke-width="1" zvalue="1384"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="922@0" LinkObjectIDznd="918" MaxPinNum="2"/>
   </metadata>
  <path d="M 1203.57 804.29 L 1216.7 804.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="227">
   <path class="kv10" d="M 1223.61 804.17 L 1216.7 804.17" stroke-width="1" zvalue="1385"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="924@0" LinkObjectIDznd="918" MaxPinNum="2"/>
   </metadata>
  <path d="M 1223.61 804.17 L 1216.7 804.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="kv10" d="M 1335.18 804.29 L 1345.75 804.29" stroke-width="1" zvalue="1386"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="930@0" LinkObjectIDznd="917" MaxPinNum="2"/>
   </metadata>
  <path d="M 1335.18 804.29 L 1345.75 804.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="229">
   <path class="kv10" d="M 1351.47 804.29 L 1345.75 804.29" stroke-width="1" zvalue="1387"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="932@0" LinkObjectIDznd="917" MaxPinNum="2"/>
   </metadata>
  <path d="M 1351.47 804.29 L 1345.75 804.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="230">
   <path class="kv10" d="M 1462.68 803.29 L 1472.77 803.29" stroke-width="1" zvalue="1388"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="938@0" LinkObjectIDznd="914" MaxPinNum="2"/>
   </metadata>
  <path d="M 1462.68 803.29 L 1472.77 803.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="kv10" d="M 1480.58 803.17 L 1472.77 803.17" stroke-width="1" zvalue="1389"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="940@0" LinkObjectIDznd="914" MaxPinNum="2"/>
   </metadata>
  <path d="M 1480.58 803.17 L 1472.77 803.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="kv10" d="M 1591.93 804.29 L 1591.93 804.29 L 1601 804.29 L 1601 804.29" stroke-width="1" zvalue="1390"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@0" LinkObjectIDznd="254" MaxPinNum="2"/>
   </metadata>
  <path d="M 1591.93 804.29 L 1591.93 804.29 L 1601 804.29 L 1601 804.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="kv10" d="M 1610.72 804.29 L 1601 804.29" stroke-width="1" zvalue="1391"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@0" LinkObjectIDznd="254" MaxPinNum="2"/>
   </metadata>
  <path d="M 1610.72 804.29 L 1601 804.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="234">
   <path class="kv10" d="M 1755.18 804.29 L 1765.75 804.29" stroke-width="1" zvalue="1392"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="148@0" LinkObjectIDznd="948" MaxPinNum="2"/>
   </metadata>
  <path d="M 1755.18 804.29 L 1765.75 804.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="235">
   <path class="kv10" d="M 1774.72 804.29 L 1765.75 804.29" stroke-width="1" zvalue="1393"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="144@0" LinkObjectIDznd="948" MaxPinNum="2"/>
   </metadata>
  <path d="M 1774.72 804.29 L 1765.75 804.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="kv10" d="M 681.1 866.71 L 692.5 866.71" stroke-width="1" zvalue="1394"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1059@0" LinkObjectIDznd="1062" MaxPinNum="2"/>
   </metadata>
  <path d="M 681.1 866.71 L 692.5 866.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="237">
   <path class="kv10" d="M 814.6 866.71 L 825.99 866.72" stroke-width="1" zvalue="1395"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1039@0" LinkObjectIDznd="1042" MaxPinNum="2"/>
   </metadata>
  <path d="M 814.6 866.71 L 825.99 866.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="238">
   <path class="kv10" d="M 938.6 866.71 L 949.99 866.71" stroke-width="1" zvalue="1396"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="999@0" LinkObjectIDznd="1002" MaxPinNum="2"/>
   </metadata>
  <path d="M 938.6 866.71 L 949.99 866.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="kv10" d="M 708.54 922.21 L 692.46 922.21" stroke-width="1" zvalue="1397"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1058@0" LinkObjectIDznd="1057" MaxPinNum="2"/>
   </metadata>
  <path d="M 708.54 922.21 L 692.46 922.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv10" d="M 842.04 922.21 L 825.96 922.21" stroke-width="1" zvalue="1398"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1038@0" LinkObjectIDznd="1037" MaxPinNum="2"/>
   </metadata>
  <path d="M 842.04 922.21 L 825.96 922.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="241">
   <path class="kv10" d="M 966.04 922.21 L 949.96 922.21" stroke-width="1" zvalue="1399"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="998@0" LinkObjectIDznd="997" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.04 922.21 L 949.96 922.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="247">
   <path class="kv10" d="M 1085.5 804.29 L 1065.07 804.29" stroke-width="1" zvalue="1403"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="972@0" LinkObjectIDznd="220" MaxPinNum="2"/>
   </metadata>
  <path d="M 1085.5 804.29 L 1065.07 804.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="223">
   <path class="kv10" d="M 525.22 846.11 L 525.22 892.2" stroke-width="1" zvalue="1413"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="827@1" LinkObjectIDznd="878@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 525.22 846.11 L 525.22 892.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="kv10" d="M 514.62 871.48 L 514.62 881 L 525.22 881" stroke-width="1" zvalue="1414"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="882@0" LinkObjectIDznd="223" MaxPinNum="2"/>
   </metadata>
  <path d="M 514.62 871.48 L 514.62 881 L 525.22 881" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv10" d="M 552.47 892.13 L 552.47 880 L 525.22 880" stroke-width="1" zvalue="1415"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="823@0" LinkObjectIDznd="223" MaxPinNum="2"/>
   </metadata>
  <path d="M 552.47 892.13 L 552.47 880 L 525.22 880" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv10" d="M 1766.05 859.86 L 1766.05 903.45" stroke-width="1" zvalue="1416"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="947@1" LinkObjectIDznd="151@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1766.05 859.86 L 1766.05 903.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv10" d="M 1752.87 884.23 L 1752.87 891 L 1766.05 891" stroke-width="1" zvalue="1417"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="244" MaxPinNum="2"/>
   </metadata>
  <path d="M 1752.87 884.23 L 1752.87 891 L 1766.05 891" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv10" d="M 1791.22 913.63 L 1791.22 891 L 1766.05 891" stroke-width="1" zvalue="1418"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="250" MaxPinNum="2"/>
   </metadata>
  <path d="M 1791.22 913.63 L 1791.22 891 L 1766.05 891" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv35" d="M 658.66 550.51 L 617.5 550.51 L 617.5 558.45" stroke-width="1" zvalue="1420"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="899@2" LinkObjectIDznd="10@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 658.66 550.51 L 617.5 550.51 L 617.5 558.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv10" d="M 1490.04 922.21 L 1472.81 922.21" stroke-width="1" zvalue="1432"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="270@0" LinkObjectIDznd="941" MaxPinNum="2"/>
   </metadata>
  <path d="M 1490.04 922.21 L 1472.81 922.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="272">
   <path class="kv10" d="M 1459.6 866.71 L 1472.82 866.71" stroke-width="1" zvalue="1435"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@0" LinkObjectIDznd="914" MaxPinNum="2"/>
   </metadata>
  <path d="M 1459.6 866.71 L 1472.82 866.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="47">
   <use class="kv35" height="30" transform="rotate(0,1409.09,451) scale(1,0.733333) translate(0,160)" width="15" x="1401.594784769232" xlink:href="#Disconnector:刀闸_0" y="440" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453907120131" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453907120131"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1409.09,451) scale(1,0.733333) translate(0,160)" width="15" x="1401.594784769232" y="440"/></g>
  <g id="81">
   <use class="kv35" height="30" transform="rotate(0,881.75,238) scale(1,0.733333) translate(0,82.5455)" width="15" x="874.25" xlink:href="#Disconnector:刀闸_0" y="227" zvalue="76"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453907316739" ObjectName="35kV勐苏线3016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453907316739"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,881.75,238) scale(1,0.733333) translate(0,82.5455)" width="15" x="874.25" y="227"/></g>
  <g id="83">
   <use class="kv35" height="30" transform="rotate(0,881.75,370) scale(1,0.733333) translate(0,130.545)" width="15" x="874.25" xlink:href="#Disconnector:刀闸_0" y="359" zvalue="84"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453907382275" ObjectName="35kV勐苏线3911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453907382275"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,881.75,370) scale(1,0.733333) translate(0,130.545)" width="15" x="874.25" y="359"/></g>
  <g id="99">
   <use class="kv35" height="30" transform="rotate(270,921.75,154) scale(-1,0.733333) translate(-1843.5,52)" width="15" x="914.25" xlink:href="#Disconnector:刀闸_0" y="143" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453907906563" ObjectName="35kV勐苏线3919隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453907906563"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,921.75,154) scale(-1,0.733333) translate(-1843.5,52)" width="15" x="914.25" y="143"/></g>
  <g id="108">
   <use class="kv35" height="30" transform="rotate(0,1113,338) scale(1,0.733333) translate(0,118.909)" width="15" x="1105.5" xlink:href="#Disconnector:刀闸_0" y="327" zvalue="108"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453908103171" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453908103171"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1113,338) scale(1,0.733333) translate(0,118.909)" width="15" x="1105.5" y="327"/></g>
  <g id="119">
   <use class="kv35" height="30" transform="rotate(0,1023.43,443.889) scale(1.42857,1.42857) translate(-303.814,-126.738)" width="15" x="1012.714285714286" xlink:href="#Disconnector:令克_0" y="422.4603174603175" zvalue="119"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453908561923" ObjectName="35kV1号站用变跌落刀闸"/>
   <cge:TPSR_Ref TObjectID="6192453908561923"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1023.43,443.889) scale(1.42857,1.42857) translate(-303.814,-126.738)" width="15" x="1012.714285714286" y="422.4603174603175"/></g>
  <g id="123">
   <use class="kv10" height="26" transform="rotate(0,1276.75,678.171) scale(1.02041,1.0989) translate(-25.3921,-59.7496)" width="14" x="1269.607142857143" xlink:href="#Disconnector:联体手车刀闸_0" y="663.8848398072378" zvalue="124"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453908692995" ObjectName="10kVⅠ段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453908692995"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1276.75,678.171) scale(1.02041,1.0989) translate(-25.3921,-59.7496)" width="14" x="1269.607142857143" y="663.8848398072378"/></g>
  <g id="184">
   <use class="kv10" height="30" transform="rotate(0,1600.91,845) scale(1,0.733333) translate(0,303.273)" width="15" x="1593.412222949139" xlink:href="#Disconnector:刀闸_0" y="834.0000000000001" zvalue="175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453909610499" ObjectName="10kV黄草坝线0826隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453909610499"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1600.91,845) scale(1,0.733333) translate(0,303.273)" width="15" x="1593.412222949139" y="834.0000000000001"/></g>
  <g id="248">
   <use class="kv10" height="30" transform="rotate(0,1664.69,910) scale(1.42857,1.42857) translate(-496.193,-266.571)" width="15" x="1653.97619047619" xlink:href="#Disconnector:令克_0" y="888.5714285714287" zvalue="251"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453910659075" ObjectName="10kV2号站用变跌落刀闸"/>
   <cge:TPSR_Ref TObjectID="6192453910659075"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1664.69,910) scale(1.42857,1.42857) translate(-496.193,-266.571)" width="15" x="1653.97619047619" y="888.5714285714287"/></g>
  <g id="895">
   <use class="kv35" height="30" transform="rotate(0,658.5,451.5) scale(1,0.733333) translate(0,160.182)" width="15" x="651" xlink:href="#Disconnector:刀闸_0" y="440.5" zvalue="1020"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449684635654" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449684635654"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,658.5,451.5) scale(1,0.733333) translate(0,160.182)" width="15" x="651" y="440.5"/></g>
  <g id="887">
   <use class="kv10" height="26" transform="rotate(0,891.75,678.171) scale(1.02041,1.0989) translate(-17.6921,-59.7496)" width="14" x="884.6071428571429" xlink:href="#Disconnector:联体手车刀闸_0" y="663.8848393304007" zvalue="1031"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449684373510" ObjectName="10kVⅡ段母线电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449684373510"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,891.75,678.171) scale(1.02041,1.0989) translate(-17.6921,-59.7496)" width="14" x="884.6071428571429" y="663.8848393304007"/></g>
  <g id="827">
   <use class="kv10" height="30" transform="rotate(0,525.163,835.3) scale(1,0.733333) translate(0,299.745)" width="15" x="517.6629743411627" xlink:href="#Disconnector:刀闸_0" y="824.3" zvalue="1114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449683783686" ObjectName="10kV2号电容器0916隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449683783686"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,525.163,835.3) scale(1,0.733333) translate(0,299.745)" width="15" x="517.6629743411627" y="824.3"/></g>
  <g id="942">
   <use class="kv10" height="30" transform="rotate(0,1472.75,898.778) scale(1,0.733333) translate(0,322.828)" width="15" x="1465.25" xlink:href="#Disconnector:刀闸_0" y="887.777770996094" zvalue="1125"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453909938179" ObjectName="10kV备用1线0926隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453909938179"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1472.75,898.778) scale(1,0.733333) translate(0,322.828)" width="15" x="1465.25" y="887.777770996094"/></g>
  <g id="934">
   <use class="kv10" height="30" transform="rotate(0,1345.66,861.75) scale(1,0.733333) translate(0,309.364)" width="15" x="1338.162222949139" xlink:href="#Disconnector:刀闸_0" y="850.7500000000002" zvalue="1137"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449685291014" ObjectName="10kV劈石线0846隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449685291014"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1345.66,861.75) scale(1,0.733333) translate(0,309.364)" width="15" x="1338.162222949139" y="850.7500000000002"/></g>
  <g id="926">
   <use class="kv10" height="30" transform="rotate(0,1216.66,863.861) scale(1,0.733333) translate(0,310.131)" width="15" x="1209.161111838028" xlink:href="#Disconnector:刀闸_0" y="852.8611111111113" zvalue="1149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449684963334" ObjectName="10kV勐嘎线0856隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449684963334"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1216.66,863.861) scale(1,0.733333) translate(0,310.131)" width="15" x="1209.161111838028" y="852.8611111111113"/></g>
  <g id="947">
   <use class="kv10" height="30" transform="rotate(0,1765.99,849.053) scale(1,0.733333) translate(0,304.746)" width="15" x="1758.487709830457" xlink:href="#Disconnector:刀闸_0" y="838.0526789531314" zvalue="1170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449685356550" ObjectName="10kV1号电容器0816隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449685356550"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1765.99,849.053) scale(1,0.733333) translate(0,304.746)" width="15" x="1758.487709830457" y="838.0526789531314"/></g>
  <g id="973">
   <use class="kv10" height="30" transform="rotate(0,1076.42,864.25) scale(1,0.733333) translate(0,310.273)" width="15" x="1068.916687011719" xlink:href="#Disconnector:刀闸_0" y="853.2499991522898" zvalue="1192"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449685815301" ObjectName="10kV苏典线0866隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449685815301"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1076.42,864.25) scale(1,0.733333) translate(0,310.273)" width="15" x="1068.916687011719" y="853.2499991522898"/></g>
  <g id="1008">
   <use class="kv10" height="30" transform="rotate(0,949.917,898.778) scale(1,0.733333) translate(0,322.828)" width="15" x="942.4166870117195" xlink:href="#Disconnector:刀闸_0" y="887.7777709960935" zvalue="1223"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449686863878" ObjectName="备用26隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449686863878"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,949.917,898.778) scale(1,0.733333) translate(0,322.828)" width="15" x="942.4166870117195" y="887.7777709960935"/></g>
  <g id="1048">
   <use class="kv10" height="30" transform="rotate(0,825.917,897.528) scale(1,0.733333) translate(0,322.374)" width="15" x="818.4166870117194" xlink:href="#Disconnector:刀闸_0" y="886.5277777777781" zvalue="1265"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449687519238" ObjectName="备用36隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449687519238"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,825.917,897.528) scale(1,0.733333) translate(0,322.374)" width="15" x="818.4166870117194" y="886.5277777777781"/></g>
  <g id="1068">
   <use class="kv10" height="30" transform="rotate(0,692.417,900.028) scale(1,0.733333) translate(0,323.283)" width="15" x="684.9166870117194" xlink:href="#Disconnector:刀闸_0" y="889.0277777777781" zvalue="1286"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449688174598" ObjectName="备用46隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449688174598"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,692.417,900.028) scale(1,0.733333) translate(0,323.283)" width="15" x="684.9166870117194" y="889.0277777777781"/></g>
  <g id="1084">
   <use class="kv10" height="26" transform="rotate(0,1085.33,678.171) scale(1.02041,1.0989) translate(-21.5637,-59.7496)" width="14" x="1078.183648016855" xlink:href="#Disconnector:联体手车刀闸_0" y="663.8848397391183" zvalue="1321"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449688502278" ObjectName="10kV分段0122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449688502278"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1085.33,678.171) scale(1.02041,1.0989) translate(-21.5637,-59.7496)" width="14" x="1078.183648016855" y="663.8848397391183"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="71">
   <use class="kv35" height="20" transform="rotate(90,1373.71,479.143) scale(1.42857,1.42857) translate(-409.971,-139.457)" width="10" x="1366.571428571429" xlink:href="#GroundDisconnector:地刀_0" y="464.8571428571429" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453907251203" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453907251203"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1373.71,479.143) scale(1.42857,1.42857) translate(-409.971,-139.457)" width="10" x="1366.571428571429" y="464.8571428571429"/></g>
  <g id="79">
   <use class="kv35" height="20" transform="rotate(270,919.428,341.531) scale(-1.42857,1.42857) translate(-1560.89,-98.1737)" width="10" x="912.2855996625974" xlink:href="#GroundDisconnector:地刀_0" y="327.2455360959887" zvalue="79"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453907578883" ObjectName="35kV勐苏线39117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453907578883"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,919.428,341.531) scale(-1.42857,1.42857) translate(-1560.89,-98.1737)" width="10" x="912.2855996625974" y="327.2455360959887"/></g>
  <g id="94">
   <use class="kv35" height="20" transform="rotate(270,919.428,193.531) scale(-1.42857,1.42857) translate(-1560.89,-53.7737)" width="10" x="912.2855996625974" xlink:href="#GroundDisconnector:地刀_0" y="179.2455360959887" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453907709955" ObjectName="35kV勐苏线39167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453907709955"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,919.428,193.531) scale(-1.42857,1.42857) translate(-1560.89,-53.7737)" width="10" x="912.2855996625974" y="179.2455360959887"/></g>
  <g id="97">
   <use class="kv35" height="20" transform="rotate(270,919.428,273.531) scale(-1.42857,1.42857) translate(-1560.89,-77.7737)" width="10" x="912.2855996625974" xlink:href="#GroundDisconnector:地刀_0" y="259.2455360959887" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453907841027" ObjectName="35kV勐苏线39160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453907841027"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,919.428,273.531) scale(-1.42857,1.42857) translate(-1560.89,-77.7737)" width="10" x="912.2855996625974" y="259.2455360959887"/></g>
  <g id="111">
   <use class="kv35" height="20" transform="rotate(270,1150.68,293.531) scale(-1.42857,1.42857) translate(-1954.01,-83.7737)" width="10" x="1143.535599662597" xlink:href="#GroundDisconnector:地刀_0" y="279.2455360959887" zvalue="112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453908365315" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453908365315"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1150.68,293.531) scale(-1.42857,1.42857) translate(-1954.01,-83.7737)" width="10" x="1143.535599662597" y="279.2455360959887"/></g>
  <g id="110">
   <use class="kv35" height="20" transform="rotate(270,1150.68,373.531) scale(-1.42857,1.42857) translate(-1954.01,-107.774)" width="10" x="1143.535599662597" xlink:href="#GroundDisconnector:地刀_0" y="359.2455360959887" zvalue="115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453908234243" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453908234243"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1150.68,373.531) scale(-1.42857,1.42857) translate(-1954.01,-107.774)" width="10" x="1143.535599662597" y="359.2455360959887"/></g>
  <g id="143">
   <use class="kv10" height="20" transform="rotate(270,1800.43,825.031) scale(-1.42857,1.42857) translate(-3058.59,-243.224)" width="10" x="1793.285618373326" xlink:href="#GroundDisconnector:地刀_0" y="810.7455360959887" zvalue="140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453909020675" ObjectName="10kV1号电容器08160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453909020675"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1800.43,825.031) scale(-1.42857,1.42857) translate(-3058.59,-243.224)" width="10" x="1793.285618373326" y="810.7455360959887"/></g>
  <g id="140">
   <use class="kv10" height="40" transform="rotate(180,1748.43,884.781) scale(1.11111,1.11111) translate(-173.732,-86.2559)" width="20" x="1737.317364290993" xlink:href="#GroundDisconnector:支那变双联地刀_0" y="862.5590281594807" zvalue="143"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453908889603" ObjectName="10kV1号电容器08167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453908889603"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,1748.43,884.781) scale(1.11111,1.11111) translate(-173.732,-86.2559)" width="20" x="1737.317364290993" y="862.5590281594807"/></g>
  <g id="148">
   <use class="kv10" height="20" transform="rotate(90,1741.25,804.214) scale(1.42857,1.42857) translate(-520.232,-236.979)" width="10" x="1734.107142857143" xlink:href="#GroundDisconnector:地刀_0" y="789.9285580771309" zvalue="151"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453909217283" ObjectName="10kV1号电容器08117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453909217283"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1741.25,804.214) scale(1.42857,1.42857) translate(-520.232,-236.979)" width="10" x="1734.107142857143" y="789.9285580771309"/></g>
  <g id="175">
   <use class="kv10" height="20" transform="rotate(90,1578,804.214) scale(1.42857,1.42857) translate(-471.257,-236.979)" width="10" x="1570.857142857143" xlink:href="#GroundDisconnector:地刀_0" y="789.9285580771309" zvalue="187"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453909479427" ObjectName="10kV黄草坝线08267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453909479427"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1578,804.214) scale(1.42857,1.42857) translate(-471.257,-236.979)" width="10" x="1570.857142857143" y="789.9285580771309"/></g>
  <g id="892">
   <use class="kv35" height="20" transform="rotate(90,621.214,479.643) scale(1.42857,1.42857) translate(-184.221,-139.607)" width="10" x="614.0714285714287" xlink:href="#GroundDisconnector:地刀_0" y="465.3571428571429" zvalue="1025"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449684570118" ObjectName="#2主变35kV侧30217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449684570118"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,621.214,479.643) scale(1.42857,1.42857) translate(-184.221,-139.607)" width="10" x="614.0714285714287" y="465.3571428571429"/></g>
  <g id="882">
   <use class="kv10" height="40" transform="rotate(180,510.178,872.031) scale(1.11111,1.11111) translate(-49.9067,-84.9809)" width="20" x="499.0673456943434" xlink:href="#GroundDisconnector:支那变双联地刀_0" y="849.8090281594807" zvalue="1039"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449684176902" ObjectName="10kV2号电容器09167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449684176902"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,510.178,872.031) scale(1.11111,1.11111) translate(-49.9067,-84.9809)" width="20" x="499.0673456943434" y="849.8090281594807"/></g>
  <g id="879">
   <use class="kv10" height="20" transform="rotate(90,499.5,804.214) scale(1.42857,1.42857) translate(-147.707,-236.979)" width="10" x="492.3571428571429" xlink:href="#GroundDisconnector:地刀_0" y="789.9285583496093" zvalue="1043"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449683980294" ObjectName="10kV2号电容器09127接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449683980294"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,499.5,804.214) scale(1.42857,1.42857) translate(-147.707,-236.979)" width="10" x="492.3571428571429" y="789.9285583496093"/></g>
  <g id="938">
   <use class="kv10" height="20" transform="rotate(90,1448.75,803.214) scale(1.42857,1.42857) translate(-432.482,-236.679)" width="10" x="1441.607142857143" xlink:href="#GroundDisconnector:地刀_0" y="788.9285580771312" zvalue="1130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453909807107" ObjectName="10kV备用1线09267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453909807107"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1448.75,803.214) scale(1.42857,1.42857) translate(-432.482,-236.679)" width="10" x="1441.607142857143" y="788.9285580771312"/></g>
  <g id="930">
   <use class="kv10" height="20" transform="rotate(90,1321.25,804.214) scale(1.42857,1.42857) translate(-394.232,-236.979)" width="10" x="1314.107142857143" xlink:href="#GroundDisconnector:地刀_0" y="789.9285580771312" zvalue="1142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449685159942" ObjectName="10kV劈石线08467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449685159942"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1321.25,804.214) scale(1.42857,1.42857) translate(-394.232,-236.979)" width="10" x="1314.107142857143" y="789.9285580771312"/></g>
  <g id="922">
   <use class="kv10" height="20" transform="rotate(90,1189.64,804.214) scale(1.42857,1.42857) translate(-354.749,-236.979)" width="10" x="1182.496031746032" xlink:href="#GroundDisconnector:地刀_0" y="789.9285585214072" zvalue="1154"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449684832262" ObjectName="10kV勐嘎线08567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449684832262"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1189.64,804.214) scale(1.42857,1.42857) translate(-354.749,-236.979)" width="10" x="1182.496031746032" y="789.9285585214072"/></g>
  <g id="970">
   <use class="kv10" height="20" transform="rotate(90,1051.17,804.214) scale(1.42857,1.42857) translate(-313.207,-236.979)" width="10" x="1044.02380952381" xlink:href="#GroundDisconnector:地刀_0" y="789.9285584372951" zvalue="1196"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449685684230" ObjectName="10kV苏典线08627接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449685684230"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1051.17,804.214) scale(1.42857,1.42857) translate(-313.207,-236.979)" width="10" x="1044.02380952381" y="789.9285584372951"/></g>
  <g id="1005">
   <use class="kv10" height="20" transform="rotate(90,924.667,804.214) scale(1.42857,1.42857) translate(-275.257,-236.979)" width="10" x="917.5238080705916" xlink:href="#GroundDisconnector:地刀_0" y="789.9285584215137" zvalue="1227"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449686732806" ObjectName="备用267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449686732806"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,924.667,804.214) scale(1.42857,1.42857) translate(-275.257,-236.979)" width="10" x="917.5238080705916" y="789.9285584215137"/></g>
  <g id="999">
   <use class="kv10" height="20" transform="rotate(90,924.667,866.643) scale(1.42857,1.42857) translate(-275.257,-255.707)" width="10" x="917.5238080365318" xlink:href="#GroundDisconnector:地刀_0" y="852.3571341378348" zvalue="1235"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449686470662" ObjectName="备用269接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449686470662"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,924.667,866.643) scale(1.42857,1.42857) translate(-275.257,-255.707)" width="10" x="917.5238080365318" y="852.3571341378348"/></g>
  <g id="998">
   <use class="kv10" height="20" transform="rotate(90,979.964,922.143) scale(1.42857,-1.42857) translate(-291.846,-1563.36)" width="10" x="972.8214285714287" xlink:href="#GroundDisconnector:地刀_0" y="907.8571341378348" zvalue="1236"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449686339589" ObjectName="备用268接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449686339589"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,979.964,922.143) scale(1.42857,-1.42857) translate(-291.846,-1563.36)" width="10" x="972.8214285714287" y="907.8571341378348"/></g>
  <g id="1045">
   <use class="kv10" height="20" transform="rotate(90,800.667,803.214) scale(1.42857,1.42857) translate(-238.057,-236.679)" width="10" x="793.5238080365318" xlink:href="#GroundDisconnector:地刀_0" y="788.9285583398027" zvalue="1269"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449687388166" ObjectName="备用367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449687388166"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,800.667,803.214) scale(1.42857,1.42857) translate(-238.057,-236.679)" width="10" x="793.5238080365318" y="788.9285583398027"/></g>
  <g id="1039">
   <use class="kv10" height="20" transform="rotate(90,800.667,866.643) scale(1.42857,1.42857) translate(-238.057,-255.707)" width="10" x="793.5238080705915" xlink:href="#GroundDisconnector:地刀_0" y="852.3571335588183" zvalue="1277"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449687126022" ObjectName="备用369接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449687126022"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,800.667,866.643) scale(1.42857,1.42857) translate(-238.057,-255.707)" width="10" x="793.5238080705915" y="852.3571335588183"/></g>
  <g id="1038">
   <use class="kv10" height="20" transform="rotate(90,855.964,922.143) scale(1.42857,-1.42857) translate(-254.646,-1563.36)" width="10" x="848.8214285714287" xlink:href="#GroundDisconnector:地刀_0" y="907.857134274074" zvalue="1278"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449686994950" ObjectName="备用368接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449686994950"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,855.964,922.143) scale(1.42857,-1.42857) translate(-254.646,-1563.36)" width="10" x="848.8214285714287" y="907.857134274074"/></g>
  <g id="1065">
   <use class="kv10" height="20" transform="rotate(90,667.167,804.214) scale(1.42857,1.42857) translate(-198.007,-236.979)" width="10" x="660.0238211495532" xlink:href="#GroundDisconnector:地刀_0" y="789.9285584215137" zvalue="1290"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449688043526" ObjectName="备用467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449688043526"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,667.167,804.214) scale(1.42857,1.42857) translate(-198.007,-236.979)" width="10" x="660.0238211495532" y="789.9285584215137"/></g>
  <g id="1059">
   <use class="kv10" height="20" transform="rotate(90,667.167,866.643) scale(1.42857,1.42857) translate(-198.007,-255.707)" width="10" x="660.0238211495537" xlink:href="#GroundDisconnector:地刀_0" y="852.3571341378348" zvalue="1298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449687781382" ObjectName="备用469接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449687781382"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,667.167,866.643) scale(1.42857,1.42857) translate(-198.007,-255.707)" width="10" x="660.0238211495537" y="852.3571341378348"/></g>
  <g id="1058">
   <use class="kv10" height="20" transform="rotate(90,722.464,922.143) scale(1.42857,-1.42857) translate(-214.596,-1563.36)" width="10" x="715.3214285714287" xlink:href="#GroundDisconnector:地刀_0" y="907.8571341378354" zvalue="1299"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449687650310" ObjectName="备用468接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449687650310"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,722.464,922.143) scale(1.42857,-1.42857) translate(-214.596,-1563.36)" width="10" x="715.3214285714287" y="907.8571341378354"/></g>
  <g id="270">
   <use class="kv10" height="20" transform="rotate(90,1503.96,922.143) scale(1.42857,-1.42857) translate(-449.046,-1563.36)" width="10" x="1496.821428571429" xlink:href="#GroundDisconnector:地刀_0" y="907.857134274074" zvalue="1431"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449724547078" ObjectName="备用168接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449724547078"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1503.96,922.143) scale(1.42857,-1.42857) translate(-449.046,-1563.36)" width="10" x="1496.821428571429" y="907.857134274074"/></g>
  <g id="271">
   <use class="kv10" height="20" transform="rotate(90,1445.67,866.643) scale(1.42857,1.42857) translate(-431.557,-255.707)" width="10" x="1438.523808036532" xlink:href="#GroundDisconnector:地刀_0" y="852.3571340356555" zvalue="1434"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449724678150" ObjectName="备用169接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449724678150"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1445.67,866.643) scale(1.42857,1.42857) translate(-431.557,-255.707)" width="10" x="1438.523808036532" y="852.3571340356555"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="101">
   <use class="kv35" height="40" transform="rotate(90,968.75,154) scale(1,1) translate(0,0)" width="30" x="953.75" xlink:href="#Accessory:带熔断器的线路PT1_0" y="134" zvalue="103"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453907972099" ObjectName="35kV勐苏线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,968.75,154) scale(1,1) translate(0,0)" width="30" x="953.75" y="134"/></g>
  <g id="104">
   <use class="kv35" height="26" transform="rotate(0,860.75,150) scale(1,1) translate(0,0)" width="12" x="854.75" xlink:href="#Accessory:避雷器1_0" y="137" zvalue="105"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453908037635" ObjectName="35kV勐苏线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,860.75,150) scale(1,1) translate(0,0)" width="12" x="854.75" y="137"/></g>
  <g id="115">
   <use class="kv35" height="51" transform="rotate(180,1114.47,236.25) scale(1.13636,-0.980392) translate(-130.737,-477.725)" width="44" x="1089.474097875138" xlink:href="#Accessory:母线PT带保险_0" y="211.25" zvalue="117"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453908430851" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="51" opacity="0" stroke="white" transform="rotate(180,1114.47,236.25) scale(1.13636,-0.980392) translate(-130.737,-477.725)" width="44" x="1089.474097875138" y="211.25"/></g>
  <g id="122">
   <use class="kv10" height="51" transform="rotate(180,1278.12,633.75) scale(1.13636,-0.980392) translate(-150.374,-1280.67)" width="44" x="1253.119669435001" xlink:href="#Accessory:母线PT带保险_0" y="608.75" zvalue="123"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453908627459" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="51" opacity="0" stroke="white" transform="rotate(180,1278.12,633.75) scale(1.13636,-0.980392) translate(-150.374,-1280.67)" width="44" x="1253.119669435001" y="608.75"/></g>
  <g id="144">
   <use class="kv10" height="26" transform="rotate(270,1787.09,804.252) scale(-1,1) translate(-3574.18,0)" width="12" x="1781.08777705086" xlink:href="#Accessory:避雷器1_0" y="791.2523676009405" zvalue="147"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453909086211" ObjectName="10kV1号电容器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1787.09,804.252) scale(-1,1) translate(-3574.18,0)" width="12" x="1781.08777705086" y="791.2523676009405"/></g>
  <g id="177">
   <use class="kv10" height="26" transform="rotate(270,1623.09,804.252) scale(-1,1) translate(-3246.18,0)" width="12" x="1617.087777050861" xlink:href="#Accessory:避雷器1_0" y="791.2523676009405" zvalue="185"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453909544963" ObjectName="10kV黄草坝线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1623.09,804.252) scale(-1,1) translate(-3246.18,0)" width="12" x="1617.087777050861" y="791.2523676009405"/></g>
  <g id="9">
   <use class="kv10" height="26" transform="rotate(0,1791.25,926) scale(-1,1) translate(-3582.5,0)" width="12" x="1785.25" xlink:href="#Accessory:避雷器1_0" y="913" zvalue="433"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453911248899" ObjectName="10kV1号电容器避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1791.25,926) scale(-1,1) translate(-3582.5,0)" width="12" x="1785.25" y="913"/></g>
  <g id="888">
   <use class="kv10" height="51" transform="rotate(180,892.75,633.75) scale(1.13636,-0.980392) translate(-104.13,-1280.67)" width="44" x="867.75" xlink:href="#Accessory:母线PT带保险_0" y="608.75" zvalue="1030"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449684439046" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="51" opacity="0" stroke="white" transform="rotate(180,892.75,633.75) scale(1.13636,-0.980392) translate(-104.13,-1280.67)" width="44" x="867.75" y="608.75"/></g>
  <g id="881">
   <use class="kv10" height="26" transform="rotate(270,546.588,804.252) scale(-1,1) translate(-1093.18,0)" width="12" x="540.5877770508607" xlink:href="#Accessory:避雷器1_0" y="791.2523678734187" zvalue="1041"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449684045830" ObjectName="10kV2号电容器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,546.588,804.252) scale(-1,1) translate(-1093.18,0)" width="12" x="540.5877770508607" y="791.2523678734187"/></g>
  <g id="823">
   <use class="kv10" height="26" transform="rotate(0,552.5,904.5) scale(-1,1) translate(-1105,0)" width="12" x="546.5" xlink:href="#Accessory:避雷器1_0" y="891.5" zvalue="1119"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449683718150" ObjectName="10kV2号电容器避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,552.5,904.5) scale(-1,1) translate(-1105,0)" width="12" x="546.5" y="891.5"/></g>
  <g id="940">
   <use class="kv10" height="26" transform="rotate(270,1492.95,803.138) scale(-1,1) translate(-2985.9,0)" width="12" x="1486.948888161972" xlink:href="#Accessory:避雷器1_0" y="790.1382143425453" zvalue="1128"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453909872643" ObjectName="10kV备用1线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1492.95,803.138) scale(-1,1) translate(-2985.9,0)" width="12" x="1486.948888161972" y="790.1382143425453"/></g>
  <g id="932">
   <use class="kv10" height="26" transform="rotate(270,1363.84,804.252) scale(-1,1) translate(-2727.68,0)" width="12" x="1357.837777050861" xlink:href="#Accessory:避雷器1_0" y="791.2523676009407" zvalue="1140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453910200323" ObjectName="10kV劈石线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1363.84,804.252) scale(-1,1) translate(-2727.68,0)" width="12" x="1357.837777050861" y="791.2523676009407"/></g>
  <g id="924">
   <use class="kv10" height="26" transform="rotate(270,1235.98,804.138) scale(-1,1) translate(-2471.95,0)" width="12" x="1229.97666593975" xlink:href="#Accessory:避雷器1_0" y="791.1382140991923" zvalue="1152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453910528003" ObjectName="10kV勐嘎线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1235.98,804.138) scale(-1,1) translate(-2471.95,0)" width="12" x="1229.97666593975" y="791.1382140991923"/></g>
  <g id="956">
   <use class="kv10" height="26" transform="rotate(0,1194.48,902.954) scale(-1,1) translate(-2388.95,0)" width="12" x="1188.47666593975" xlink:href="#Accessory:避雷器1_0" y="889.9535097785896" zvalue="1179"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449685422086" ObjectName="10kV勐嘎线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1194.48,902.954) scale(-1,1) translate(-2388.95,0)" width="12" x="1188.47666593975" y="889.9535097785896"/></g>
  <g id="957">
   <use class="kv10" height="26" transform="rotate(0,1324.34,905.662) scale(-1,1) translate(-2648.68,0)" width="12" x="1318.337777050861" xlink:href="#Accessory:避雷器1_0" y="892.6621212121213" zvalue="1181"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449685487621" ObjectName="10kV劈石线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1324.34,905.662) scale(-1,1) translate(-2648.68,0)" width="12" x="1318.337777050861" y="892.6621212121213"/></g>
  <g id="962">
   <use class="kv10" height="26" transform="rotate(0,1453.84,967.579) scale(-1,1) translate(-2907.68,0)" width="12" x="1447.837777050861" xlink:href="#Accessory:避雷器1_0" y="954.5787955081826" zvalue="1187"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449685553157" ObjectName="10kV备用1线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1453.84,967.579) scale(-1,1) translate(-2907.68,0)" width="12" x="1447.837777050861" y="954.5787955081826"/></g>
  <g id="972">
   <use class="kv10" height="26" transform="rotate(270,1097.87,804.252) scale(-1,1) translate(-2195.73,0)" width="12" x="1091.865554828639" xlink:href="#Accessory:避雷器1_0" y="791.2523690660073" zvalue="1194"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449685749766" ObjectName="苏典线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1097.87,804.252) scale(-1,1) translate(-2195.73,0)" width="12" x="1091.865554828639" y="791.2523690660073"/></g>
  <g id="977">
   <use class="kv10" height="26" transform="rotate(0,1053.34,902.457) scale(-1,1) translate(-2106.68,0)" width="12" x="1047.337777050861" xlink:href="#Accessory:避雷器1_0" y="889.4569841268819" zvalue="1204"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449685880837" ObjectName="苏典线避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1053.34,902.457) scale(-1,1) translate(-2106.68,0)" width="12" x="1047.337777050861" y="889.4569841268819"/></g>
  <g id="1007">
   <use class="kv10" height="26" transform="rotate(270,972.366,804.138) scale(-1,1) translate(-1944.73,0)" width="12" x="966.3655548286388" xlink:href="#Accessory:避雷器1_0" y="791.1382141933906" zvalue="1225"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449686798342" ObjectName="备用2避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,972.366,804.138) scale(-1,1) translate(-1944.73,0)" width="12" x="966.3655548286388" y="791.1382141933906"/></g>
  <g id="1000">
   <use class="kv10" height="26" transform="rotate(0,930.838,967.579) scale(-1,1) translate(-1861.68,0)" width="12" x="924.8377770508612" xlink:href="#Accessory:避雷器1_0" y="954.578796386719" zvalue="1234"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449686536198" ObjectName="备用2避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,930.838,967.579) scale(-1,1) translate(-1861.68,0)" width="12" x="924.8377770508612" y="954.578796386719"/></g>
  <g id="1047">
   <use class="kv10" height="26" transform="rotate(270,847.366,803.138) scale(-1,1) translate(-1694.73,0)" width="12" x="841.3655548286388" xlink:href="#Accessory:避雷器1_0" y="790.138214104437" zvalue="1267"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449687453702" ObjectName="备用3避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,847.366,803.138) scale(-1,1) translate(-1694.73,0)" width="12" x="841.3655548286388" y="790.138214104437"/></g>
  <g id="1040">
   <use class="kv10" height="26" transform="rotate(0,806.838,966.329) scale(-1,1) translate(-1613.68,0)" width="12" x="800.8377770508612" xlink:href="#Accessory:避雷器1_0" y="953.328787878788" zvalue="1276"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449687191558" ObjectName="备用3避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,806.838,966.329) scale(-1,1) translate(-1613.68,0)" width="12" x="800.8377770508612" y="953.328787878788"/></g>
  <g id="1067">
   <use class="kv10" height="26" transform="rotate(270,713.866,804.252) scale(-1,1) translate(-1427.73,0)" width="12" x="707.8655548286388" xlink:href="#Accessory:避雷器1_0" y="791.2523679453232" zvalue="1288"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449688109062" ObjectName="备用4避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,713.866,804.252) scale(-1,1) translate(-1427.73,0)" width="12" x="707.8655548286388" y="791.2523679453232"/></g>
  <g id="1060">
   <use class="kv10" height="26" transform="rotate(0,673.338,968.829) scale(-1,1) translate(-1346.68,0)" width="12" x="667.3377770508612" xlink:href="#Accessory:避雷器1_0" y="955.828787878788" zvalue="1297"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449687846918" ObjectName="备用4避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,673.338,968.829) scale(-1,1) translate(-1346.68,0)" width="12" x="667.3377770508612" y="955.828787878788"/></g>
  <g id="1070">
   <use class="kv10" height="26" transform="rotate(0,621.588,656.704) scale(-1,1) translate(-1243.18,0)" width="12" x="615.5877770508607" xlink:href="#Accessory:避雷器1_0" y="643.703521644708" zvalue="1306"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449688240134" ObjectName="#2主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,621.588,656.704) scale(-1,1) translate(-1243.18,0)" width="12" x="615.5877770508607" y="643.703521644708"/></g>
  <g id="1072">
   <use class="kv10" height="26" transform="rotate(0,1372.84,654.204) scale(-1,1) translate(-2745.68,0)" width="12" x="1366.837777050861" xlink:href="#Accessory:避雷器1_0" y="641.203521644708" zvalue="1309"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449688305670" ObjectName="#1主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1372.84,654.204) scale(-1,1) translate(-2745.68,0)" width="12" x="1366.837777050861" y="641.203521644708"/></g>
  <g id="1074">
   <use class="kv10" height="26" transform="rotate(270,1622.12,869.217) scale(-1,1) translate(-3244.23,0)" width="12" x="1616.115554828639" xlink:href="#Accessory:避雷器1_0" y="856.2166666666667" zvalue="1312"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449688371206" ObjectName="10kV黄草坝线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1622.12,869.217) scale(-1,1) translate(-3244.23,0)" width="12" x="1616.115554828639" y="856.2166666666667"/></g>
  <g id="1075">
   <use class="kv10" height="26" transform="rotate(0,1678.62,906.412) scale(-1,1) translate(-3357.23,0)" width="12" x="1672.615554828639" xlink:href="#Accessory:避雷器1_0" y="893.4121212121213" zvalue="1314"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449688436742" ObjectName="10kV黄草坝线避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1678.62,906.412) scale(-1,1) translate(-3357.23,0)" width="12" x="1672.615554828639" y="893.4121212121213"/></g>
  <g id="10">
   <use class="kv35" height="20" transform="rotate(0,617.5,573.5) scale(1.55,2.15) translate(-213.613,-295.256)" width="20" x="602" xlink:href="#Accessory:线路PT3_0" y="552" zvalue="1419"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449708097542" ObjectName="#2变压器高压侧中性点避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,617.5,573.5) scale(1.55,2.15) translate(-213.613,-295.256)" width="20" x="602" y="552"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="117">
   <use class="kv35" height="30" transform="rotate(0,1023.82,518.5) scale(1.42857,1.42857) translate(-300.718,-149.121)" width="30" x="1002.392857142857" xlink:href="#EnergyConsumer:站用变DY_0" y="497.0714285714286" zvalue="118"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453908496387" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1023.82,518.5) scale(1.42857,1.42857) translate(-300.718,-149.121)" width="30" x="1002.392857142857" y="497.0714285714286"/></g>
  <g id="174">
   <use class="kv10" height="30" transform="rotate(180,1600.97,913) scale(1.33333,1) translate(-398.243,0)" width="12" x="1592.973387373245" xlink:href="#EnergyConsumer:负荷_0" y="898.0000000000001" zvalue="189"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453909348355" ObjectName="10kV黄草坝线"/>
   <cge:TPSR_Ref TObjectID="6192453909348355"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1600.97,913) scale(1.33333,1) translate(-398.243,0)" width="12" x="1592.973387373245" y="898.0000000000001"/></g>
  <g id="249">
   <use class="kv10" height="30" transform="rotate(0,1664.57,958) scale(1.42857,1.42857) translate(-492.943,-280.971)" width="30" x="1643.142857142857" xlink:href="#EnergyConsumer:站用变DY_0" y="936.5714285714287" zvalue="250"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453910724611" ObjectName="35kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1664.57,958) scale(1.42857,1.42857) translate(-492.943,-280.971)" width="30" x="1643.142857142857" y="936.5714285714287"/></g>
  <g id="937">
   <use class="kv10" height="30" transform="rotate(180,1472.81,973.5) scale(1.33333,1) translate(-366.203,0)" width="12" x="1464.811164424106" xlink:href="#EnergyConsumer:负荷_0" y="958.5000000000002" zvalue="1132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453909676035" ObjectName="10kV备用1线"/>
   <cge:TPSR_Ref TObjectID="6192453909676035"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1472.81,973.5) scale(1.33333,1) translate(-366.203,0)" width="12" x="1464.811164424106" y="958.5000000000002"/></g>
  <g id="929">
   <use class="kv10" height="30" transform="rotate(180,1345.72,913.75) scale(1.33333,1) translate(-334.431,0)" width="12" x="1337.723387373245" xlink:href="#EnergyConsumer:负荷_0" y="898.7500000000002" zvalue="1144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453910003715" ObjectName="10kV劈石线"/>
   <cge:TPSR_Ref TObjectID="6192453910003715"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1345.72,913.75) scale(1.33333,1) translate(-334.431,0)" width="12" x="1337.723387373245" y="898.7500000000002"/></g>
  <g id="921">
   <use class="kv10" height="30" transform="rotate(180,1216.72,915.861) scale(1.33333,1) translate(-302.181,0)" width="12" x="1208.722276262134" xlink:href="#EnergyConsumer:负荷_0" y="900.8611111111113" zvalue="1156"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453910331395" ObjectName="10kV勐嘎线"/>
   <cge:TPSR_Ref TObjectID="6192453910331395"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1216.72,915.861) scale(1.33333,1) translate(-302.181,0)" width="12" x="1208.722276262134" y="900.8611111111113"/></g>
  <g id="1001">
   <use class="kv10" height="30" transform="rotate(180,949.917,973.5) scale(1.33333,1) translate(-235.479,0)" width="12" x="941.9166870567049" xlink:href="#EnergyConsumer:负荷_0" y="958.5000000000002" zvalue="1232"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449686601734" ObjectName="备用2"/>
   <cge:TPSR_Ref TObjectID="6192449686601734"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,949.917,973.5) scale(1.33333,1) translate(-235.479,0)" width="12" x="941.9166870567049" y="958.5000000000002"/></g>
  <g id="1041">
   <use class="kv10" height="30" transform="rotate(180,825.917,972.25) scale(1.33333,1) translate(-204.479,0)" width="12" x="817.9166870567049" xlink:href="#EnergyConsumer:负荷_0" y="957.2500000000002" zvalue="1274"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449687257094" ObjectName="备用3"/>
   <cge:TPSR_Ref TObjectID="6192449687257094"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,825.917,972.25) scale(1.33333,1) translate(-204.479,0)" width="12" x="817.9166870567049" y="957.2500000000002"/></g>
  <g id="1061">
   <use class="kv10" height="30" transform="rotate(180,692.417,974.75) scale(1.33333,1) translate(-171.104,0)" width="12" x="684.4166870567049" xlink:href="#EnergyConsumer:负荷_0" y="959.7500000000002" zvalue="1295"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449687912454" ObjectName="备用4"/>
   <cge:TPSR_Ref TObjectID="6192449687912454"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,692.417,974.75) scale(1.33333,1) translate(-171.104,0)" width="12" x="684.4166870567049" y="959.7500000000002"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="151">
   <use class="kv10" height="50" transform="rotate(0,1761.42,945) scale(1.73629,1.69) translate(-737.742,-368.578)" width="25" x="1739.715167862318" xlink:href="#Compensator:电容20200722_0" y="902.75" zvalue="154"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453909282819" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453909282819"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1761.42,945) scale(1.73629,1.69) translate(-737.742,-368.578)" width="25" x="1739.715167862318" y="902.75"/></g>
  <g id="878">
   <use class="kv10" height="50" transform="rotate(0,520.594,933.75) scale(1.73629,1.69) translate(-211.559,-363.985)" width="25" x="498.8904323730238" xlink:href="#Compensator:电容20200722_0" y="891.5" zvalue="1045"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449683849222" ObjectName="10kV2号电容器"/>
   <cge:TPSR_Ref TObjectID="6192449683849222"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,520.594,933.75) scale(1.73629,1.69) translate(-211.559,-363.985)" width="25" x="498.8904323730238" y="891.5"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="205">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="205" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,134.357,520.111) scale(1,1) translate(-2.37271e-14,0)" writing-mode="lr" x="134.48" xml:space="preserve" y="525.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133579501571" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="204">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="204" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,134.357,543.111) scale(1,1) translate(-2.37271e-14,0)" writing-mode="lr" x="134.48" xml:space="preserve" y="548.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133579567107" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="203">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,134.357,566.111) scale(1,1) translate(-2.37271e-14,0)" writing-mode="lr" x="134.48" xml:space="preserve" y="571.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133579632643" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,134.5,494.611) scale(1,1) translate(0,0)" writing-mode="lr" x="134.62" xml:space="preserve" y="499.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133579763715" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.857,201.111) scale(1,1) translate(0,0)" writing-mode="lr" x="138.05" xml:space="preserve" y="207.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133579894787" ObjectName="F"/>
   </metadata>
  </g>
  <g id="200">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,252.19,520) scale(1,1) translate(-4.98913e-14,0)" writing-mode="lr" x="252.31" xml:space="preserve" y="524.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133580025859" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="62" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,252.19,543) scale(1,1) translate(-4.98913e-14,0)" writing-mode="lr" x="252.31" xml:space="preserve" y="547.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133580091395" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="61">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,252.19,566) scale(1,1) translate(-4.98913e-14,0)" writing-mode="lr" x="252.31" xml:space="preserve" y="570.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133580156931" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="60">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,252.19,493.5) scale(1,1) translate(-4.98913e-14,0)" writing-mode="lr" x="252.31" xml:space="preserve" y="498.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133580288003" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="59">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="59" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.857,226) scale(1,1) translate(0,0)" writing-mode="lr" x="138.05" xml:space="preserve" y="232.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133580419075" ObjectName="F"/>
   </metadata>
  </g>
  <g id="58">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,137.857,248.194) scale(1,1) translate(-1.89601e-14,0)" writing-mode="lr" x="138.08" xml:space="preserve" y="254.68" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133581008900" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,137.857,271.194) scale(1,1) translate(0,0)" writing-mode="lr" x="138.05" xml:space="preserve" y="277.68" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133581074436" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,134.607,594.111) scale(1,1) translate(-2.37826e-14,0)" writing-mode="lr" x="134.73" xml:space="preserve" y="599.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133579960323" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,252.899,593) scale(1,1) translate(-5.00486e-14,0)" writing-mode="lr" x="253.02" xml:space="preserve" y="597.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133580484611" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="53" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,137.857,178) scale(1,1) translate(0,0)" writing-mode="lr" x="138.01" xml:space="preserve" y="184.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133595492355" ObjectName=""/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="52" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,314.857,177) scale(1,1) translate(0,0)" writing-mode="lr" x="315.01" xml:space="preserve" y="183.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133595557891" ObjectName=""/>
   </metadata>
  </g>
  <g id="182">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="182" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1521.78,461.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1522.02" xml:space="preserve" y="466.49" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133580550147" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="185">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="185" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1521.78,488.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1522.02" xml:space="preserve" y="493.49" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133580615683" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="186">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="186" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1521.78,515.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1522.02" xml:space="preserve" y="520.49" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133580812291" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="187">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="187" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1525.78,645.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1526.02" xml:space="preserve" y="650.49" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133580681219" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="189">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="189" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1525.78,668.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1526.02" xml:space="preserve" y="673.49" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133580746755" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="190">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="190" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1525.78,693.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1526.02" xml:space="preserve" y="698.49" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133581139972" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="208">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="208" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1588.97,965.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1589.16" xml:space="preserve" y="970.9299999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133587038211" ObjectName="P"/>
   </metadata>
  </g>
  <g id="218">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="218" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1588.97,982.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1589.16" xml:space="preserve" y="987.9299999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133587103747" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="221">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="221" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1588.97,999.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1589.16" xml:space="preserve" y="1004.93" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133587169283" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="7" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1774.62,1016.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1774.82" xml:space="preserve" y="1021.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133585793027" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="8" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1774.62,1035.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1774.82" xml:space="preserve" y="1040.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133585858563" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,312.19,520) scale(1,1) translate(-6.3214e-14,0)" writing-mode="lr" x="312.31" xml:space="preserve" y="524.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124659068933" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="29">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="29" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,312.19,543) scale(1,1) translate(-6.3214e-14,0)" writing-mode="lr" x="312.31" xml:space="preserve" y="547.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124659134469" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="28">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,312.19,566) scale(1,1) translate(-6.3214e-14,0)" writing-mode="lr" x="312.31" xml:space="preserve" y="570.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124659200005" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,312.19,493.5) scale(1,1) translate(-6.3214e-14,0)" writing-mode="lr" x="312.31" xml:space="preserve" y="498.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124659331077" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,312.899,593) scale(1,1) translate(-6.33713e-14,0)" writing-mode="lr" x="313.02" xml:space="preserve" y="597.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124659527685" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="42">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="42" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,318.857,224) scale(1,1) translate(0,0)" writing-mode="lr" x="319.05" xml:space="preserve" y="230.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124659462149" ObjectName="F"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,318.857,246.194) scale(1,1) translate(-5.91502e-14,0)" writing-mode="lr" x="319.08" xml:space="preserve" y="252.68" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124657496069" ObjectName="YW1"/>
   </metadata>
  </g>
  <g id="146">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,318.857,269.194) scale(1,1) translate(0,0)" writing-mode="lr" x="319.05" xml:space="preserve" y="275.68" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124657561605" ObjectName="Tap"/>
   </metadata>
  </g>
  <g id="181">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="181" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,764.143,461.5) scale(1,1) translate(0,0)" writing-mode="lr" x="763.5599999999999" xml:space="preserve" y="466.2" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124657037317" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="191">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="191" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,764.143,488.5) scale(1,1) translate(0,0)" writing-mode="lr" x="763.5599999999999" xml:space="preserve" y="493.2" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124657102853" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="192">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="192" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,764.143,645.5) scale(1,1) translate(0,0)" writing-mode="lr" x="763.5599999999999" xml:space="preserve" y="650.2" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124657168389" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="193">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="193" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,764.143,668.5) scale(1,1) translate(0,0)" writing-mode="lr" x="763.5599999999999" xml:space="preserve" y="673.2" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124657233925" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="194">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="194" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,764.143,515.5) scale(1,1) translate(0,0)" writing-mode="lr" x="763.5599999999999" xml:space="preserve" y="520.2" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124657299461" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="195">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="195" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,764.143,693.5) scale(1,1) translate(0,0)" writing-mode="lr" x="763.5599999999999" xml:space="preserve" y="698.2" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124657627141" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="196">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="196" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,520.594,1012) scale(1,1) translate(0,0)" writing-mode="lr" x="520.14" xml:space="preserve" y="1016.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124654743557" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="197">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="197" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,520.594,1029) scale(1,1) translate(0,0)" writing-mode="lr" x="520.14" xml:space="preserve" y="1033.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124654809093" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="198">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="198" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1077.42,964.75) scale(1,1) translate(1.14732e-13,0)" writing-mode="lr" x="1076.96" xml:space="preserve" y="969.53" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124663001093" ObjectName="P"/>
   </metadata>
  </g>
  <g id="199">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="199" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1077.42,981.75) scale(1,1) translate(1.14732e-13,0)" writing-mode="lr" x="1076.96" xml:space="preserve" y="986.53" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124663066629" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="201">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="201" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1078.42,999.75) scale(1,1) translate(1.14843e-13,0)" writing-mode="lr" x="1077.96" xml:space="preserve" y="1004.53" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124663132165" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="4" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1217.72,964.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1217.26" xml:space="preserve" y="969.53" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133591363587" ObjectName="P"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="11" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1346.72,964.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1346.27" xml:space="preserve" y="969.53" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133589921795" ObjectName="P"/>
   </metadata>
  </g>
  <g id="173">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="173" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1218.72,981.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1218.26" xml:space="preserve" y="986.53" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133591429123" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="176">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="176" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1347.72,981.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1347.27" xml:space="preserve" y="986.53" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133589987331" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="206">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="206" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1218.72,999.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1218.26" xml:space="preserve" y="1004.53" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133591494659" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="209">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="209" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1347.72,999.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1347.27" xml:space="preserve" y="1004.53" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133590052867" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="166">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="166" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1130.57,536.71) scale(1,1) translate(0,0)" writing-mode="lr" x="1130.1" xml:space="preserve" y="541.49" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124668243973" ObjectName="P"/>
   </metadata>
  </g>
  <g id="178">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="178" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1130.57,553.71) scale(1,1) translate(0,0)" writing-mode="lr" x="1130.1" xml:space="preserve" y="558.49" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124668309509" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="219">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="219" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1130.57,572.71) scale(1,1) translate(0,0)" writing-mode="lr" x="1130.1" xml:space="preserve" y="577.49" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124668375045" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="207">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="207" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,880,20.5001) scale(1,1) translate(0,2.22045e-15)" writing-mode="lr" x="879.53" xml:space="preserve" y="25.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133583630339" ObjectName="P"/>
   </metadata>
  </g>
  <g id="273">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="273" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,880,39.5) scale(1,1) translate(0,6.43928e-15)" writing-mode="lr" x="879.53" xml:space="preserve" y="44.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133583695875" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="274">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="274" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,880,56.5) scale(1,1) translate(0,1.0214e-14)" writing-mode="lr" x="879.53" xml:space="preserve" y="61.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133583761411" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="286">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="286" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1114.5,172.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1114.62" xml:space="preserve" y="177.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133579763715" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="287">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="287" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1283.5,565.5) scale(1,1) translate(-2.78888e-13,0)" writing-mode="lr" x="1283.62" xml:space="preserve" y="570.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133580288003" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="288">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="288" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,895.5,564.5) scale(1,1) translate(-1.92735e-13,0)" writing-mode="lr" x="895.62" xml:space="preserve" y="569.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124659331077" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,344.482,318.5) scale(0.708333,0.665547) translate(137.471,155.037)" width="30" x="333.86" xlink:href="#State:红绿圆(方形)_0" y="308.52" zvalue="372"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374922940417" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,344.482,318.5) scale(0.708333,0.665547) translate(137.471,155.037)" width="30" x="333.86" y="308.52"/></g>
  <g id="56">
   <use height="30" transform="rotate(0,248.857,318.5) scale(0.708333,0.665547) translate(98.0956,155.037)" width="30" x="238.23" xlink:href="#State:红绿圆(方形)_0" y="308.52" zvalue="373"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562958011596805" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,248.857,318.5) scale(0.708333,0.665547) translate(98.0956,155.037)" width="30" x="238.23" y="308.52"/></g>
  <g id="993">
   <use height="30" transform="rotate(0,315.812,138.464) scale(1.27778,1.03333) translate(-56.1549,-3.96657)" width="90" x="258.31" xlink:href="#State:全站检修_0" y="122.96" zvalue="1476"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549679947777" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,315.812,138.464) scale(1.27778,1.03333) translate(-56.1549,-3.96657)" width="90" x="258.31" y="122.96"/></g>
  <g id="295">
   <use height="30" transform="rotate(0,292.235,404) scale(0.910937,0.8) translate(25.0094,98)" width="80" x="255.8" xlink:href="#State:间隔模板_0" y="392" zvalue="1480"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629500525641730" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,292.235,404) scale(0.910937,0.8) translate(25.0094,98)" width="80" x="255.8" y="392"/></g>
  <g id="1082">
   <use height="30" transform="rotate(0,94.625,318.539) scale(0.910937,0.8) translate(5.689,76.6347)" width="80" x="58.19" xlink:href="#State:间隔模板_0" y="306.54" zvalue="1481"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629500363440130" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,94.625,318.539) scale(0.910937,0.8) translate(5.689,76.6347)" width="80" x="58.19" y="306.54"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="ACLineSegmentClass">
  <g id="179">
   <use class="kv35" height="30" transform="rotate(0,882,105) scale(1,1) translate(0,0)" width="7" x="878.5" xlink:href="#ACLineSegment:线路_0" y="90" zvalue="1439"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249313902598" ObjectName="35kV勐苏线"/>
   <cge:TPSR_Ref TObjectID="8444249313902598_5066549679947777"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,882,105) scale(1,1) translate(0,0)" width="7" x="878.5" y="90"/></g>
 </g>
</svg>