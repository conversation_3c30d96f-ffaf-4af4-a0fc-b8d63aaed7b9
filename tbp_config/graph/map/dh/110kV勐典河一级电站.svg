<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549684338689" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:三卷PT带容断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,6.5) scale(1,1) translate(0,0)" width="4" x="13" y="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.750000000000004" y2="4.166666666666668"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="32"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="1" y1="27" y2="11"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="27" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.45181617405996" x2="1.714850492606708" y1="5.276370517142858" y2="14.05696281619048"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.583333333333333" x2="8.583333333333334" y1="5.166666666666667" y2="14.16666666666667"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Disconnector:三相刀闸_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="30"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="23.66666666666666" y2="29.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.25" x2="15.08333333333333" y1="5.999999999999998" y2="23.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.46666666666667" x2="16.43333333333333" y1="5.755662181544974" y2="5.755662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.750000000000002" y2="0.3554618309314215"/>
  </symbol>
  <symbol id="Accessory:RT1122_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="22.91666666666667" y2="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="18.16666666666666" y2="20.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="18.16666666666666" y2="20.16666666666666"/>
   <path d="M 13 11 L 17 11 L 15 8 L 13 11 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="15.41666666666666" y2="18.16666666666666"/>
   <ellipse cx="14.95" cy="18.02" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.95" cy="9.92" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变11_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="9.25" y2="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="26.83333333333333" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0928507596068" x2="10.0928507596068" y1="10.97013412501683" y2="12.64189293057399"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.77292225201073" x2="10.0928507596068" y1="14.31365173613114" y2="12.64189293057398"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
  <symbol id="PowerTransformer3:主变高压侧有中性点_0" viewBox="0,0,50,50">
   <use terminal-index="0" type="1" x="14.75" xlink:href="#terminal" y="0.2499999999999929"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.33333333333333" x2="15.15740740740741" y1="10.58333333333333" y2="14.1754686785551"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.08333333333333" x2="15.17203932327389" y1="10.66666666666667" y2="14.1754686785551"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.16666666666666" x2="15.16666666666666" y1="14.16666666666666" y2="18.91666666666666"/>
   <ellipse cx="14.93" cy="15" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="2" x="15.08333333333334" xlink:href="#terminal" y="14.16666666666666"/>
  </symbol>
  <symbol id="PowerTransformer3:主变高压侧有中性点_1" viewBox="0,0,50,50">
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.66666666666666" x2="34.66666666666666" y1="29.41666666666667" y2="25.11796982167353"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.66598079561043" x2="29.75" y1="25.11796982167353" y2="20.66666666666667"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.67329675354367" x2="39.25" y1="25.11065386374029" y2="20.58333333333334"/>
   <ellipse cx="34.78" cy="25.08" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="1" x="49.50000000000001" xlink:href="#terminal" y="25.08333333333333"/>
  </symbol>
  <symbol id="PowerTransformer3:主变高压侧有中性点_2" viewBox="0,0,50,50">
   <path d="M 15.1667 32 L 10.0833 40 L 20.1667 40 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.12" cy="35.17" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="3" type="1" x="15.08333333333334" xlink:href="#terminal" y="49.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.583333333333332" y2="1.050000000000001"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="26.41666666666667" y2="34.83333333333334"/>
   <rect fill-opacity="0" height="17" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,18) scale(1,1) translate(0,0)" width="8" x="3" y="9.5"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="35" y2="1.166666666666664"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <rect fill-opacity="0" height="17" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,18) scale(1,1) translate(0,0)" width="8" x="3" y="9.5"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="9.999999999999995" y2="1.299999999999994"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV勐典河一级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="273" x="45.75" xlink:href="logo.png" y="35"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,182.25,65) scale(1,1) translate(0,0)" writing-mode="lr" x="182.25" xml:space="preserve" y="68.5" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,181.625,64.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="181.63" xml:space="preserve" y="73.69" zvalue="3">110kV勐典河一级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="371" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="444"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="444">信号一览</text>
  <line fill="none" id="36" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="379.75" x2="379.75" y1="3" y2="1033" zvalue="4"/>
  <line fill="none" id="34" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.750000000000455" x2="372.75" y1="138.8704926140824" y2="138.8704926140824" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,607,303) scale(1,1) translate(0,0)" writing-mode="lr" x="607" xml:space="preserve" y="307.5" zvalue="41">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,736.083,244.361) scale(1,1) translate(0,0)" writing-mode="lr" x="736.08" xml:space="preserve" y="248.86" zvalue="44">151</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,727.306,291.917) scale(1,1) translate(0,0)" writing-mode="lr" x="727.3099999999999" xml:space="preserve" y="296.42" zvalue="45">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,728.972,183.917) scale(1,1) translate(0,0)" writing-mode="lr" x="728.97" xml:space="preserve" y="188.42" zvalue="48">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,702.682,93.6944) scale(1,1) translate(0,0)" writing-mode="lr" x="702.6799999999999" xml:space="preserve" y="98.19" zvalue="52">110kV勐平线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,667.806,283.028) scale(1,1) translate(0,0)" writing-mode="lr" x="667.8099999999999" xml:space="preserve" y="287.53" zvalue="55">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,667.806,230.583) scale(1,1) translate(0,0)" writing-mode="lr" x="667.8099999999999" xml:space="preserve" y="235.08" zvalue="57">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,667.806,176.139) scale(1,1) translate(0,0)" writing-mode="lr" x="667.8099999999999" xml:space="preserve" y="180.64" zvalue="59">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,852.333,243.111) scale(1,1) translate(0,0)" writing-mode="lr" x="852.33" xml:space="preserve" y="247.61" zvalue="66">152</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,843.556,290.667) scale(1,1) translate(0,0)" writing-mode="lr" x="843.5599999999999" xml:space="preserve" y="295.17" zvalue="67">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,845.222,182.667) scale(1,1) translate(0,0)" writing-mode="lr" x="845.22" xml:space="preserve" y="187.17" zvalue="70">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,830.932,92.4444) scale(1,1) translate(0,0)" writing-mode="lr" x="830.9299999999999" xml:space="preserve" y="96.94" zvalue="74">110kV勐二一线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,784.056,281.778) scale(1,1) translate(0,0)" writing-mode="lr" x="784.0599999999999" xml:space="preserve" y="286.28" zvalue="77">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,784.056,229.333) scale(1,1) translate(0,0)" writing-mode="lr" x="784.0599999999999" xml:space="preserve" y="233.83" zvalue="79">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,784.056,174.889) scale(1,1) translate(0,0)" writing-mode="lr" x="784.0599999999999" xml:space="preserve" y="179.39" zvalue="81">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,966.833,244.361) scale(1,1) translate(0,0)" writing-mode="lr" x="966.83" xml:space="preserve" y="248.86" zvalue="88">153</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,958.056,291.917) scale(1,1) translate(0,0)" writing-mode="lr" x="958.0599999999999" xml:space="preserve" y="296.42" zvalue="89">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,959.722,183.917) scale(1,1) translate(0,0)" writing-mode="lr" x="959.72" xml:space="preserve" y="188.42" zvalue="92">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,945.432,93.6944) scale(1,1) translate(0,0)" writing-mode="lr" x="945.4299999999999" xml:space="preserve" y="98.19" zvalue="96">110kV勐五一线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,898.556,283.028) scale(1,1) translate(0,0)" writing-mode="lr" x="898.5599999999999" xml:space="preserve" y="287.53" zvalue="99">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,898.556,230.583) scale(1,1) translate(3.95486e-13,0)" writing-mode="lr" x="898.5599999999999" xml:space="preserve" y="235.08" zvalue="101">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,898.556,176.139) scale(1,1) translate(0,0)" writing-mode="lr" x="898.5599999999999" xml:space="preserve" y="180.64" zvalue="103">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1102.56,277.667) scale(1,1) translate(0,-4.78136e-13)" writing-mode="lr" x="1102.56" xml:space="preserve" y="282.17" zvalue="109">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1035.33,268.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1035.33" xml:space="preserve" y="273.44" zvalue="111">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1072.24,171) scale(1,1) translate(0,0)" writing-mode="lr" x="1072.24" xml:space="preserve" y="175.5" zvalue="117">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1035.33,310.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1035.33" xml:space="preserve" y="314.61" zvalue="119">10</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1351.38,303) scale(1,1) translate(0,0)" writing-mode="lr" x="1351.38" xml:space="preserve" y="307.5" zvalue="125">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1481.33,240.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1481.33" xml:space="preserve" y="245.17" zvalue="128">351</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1472.56,288.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1472.56" xml:space="preserve" y="292.72" zvalue="129">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1474.22,188.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1474.22" xml:space="preserve" y="192.72" zvalue="132">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1458.68,77.4722) scale(1,1) translate(6.20029e-13,0)" writing-mode="lr" x="1458.68" xml:space="preserve" y="81.97" zvalue="136">35kV光明硅厂Ⅲ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1593.83,240.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1593.83" xml:space="preserve" y="245.17" zvalue="141">352</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1585.06,288.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1585.06" xml:space="preserve" y="292.72" zvalue="142">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1586.72,188.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1586.72" xml:space="preserve" y="192.72" zvalue="145">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1573.34,92.8813) scale(1,1) translate(0,0)" writing-mode="lr" x="1573.34" xml:space="preserve" y="97.38" zvalue="149">35kV勐支线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1405.86,179.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1405.86" xml:space="preserve" y="184.39" zvalue="153">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1522.11,182.639) scale(1,1) translate(0,0)" writing-mode="lr" x="1522.11" xml:space="preserve" y="187.14" zvalue="156">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="150" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1727.14,270.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1727.14" xml:space="preserve" y="275.17" zvalue="160">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1696.83,164) scale(1,1) translate(0,0)" writing-mode="lr" x="1696.83" xml:space="preserve" y="168.5" zvalue="168">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,494.5,660.5) scale(1,1) translate(0,0)" writing-mode="lr" x="494.5" xml:space="preserve" y="665" zvalue="176">6.3kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,812.528,366.222) scale(1,1) translate(0,0)" writing-mode="lr" x="812.53" xml:space="preserve" y="370.72" zvalue="178">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,821.861,406.222) scale(1,1) translate(0,0)" writing-mode="lr" x="821.86" xml:space="preserve" y="410.72" zvalue="180">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,970.556,543.667) scale(1,1) translate(0,0)" writing-mode="lr" x="970.5599999999999" xml:space="preserve" y="548.17" zvalue="184">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,752.194,395.222) scale(1,1) translate(0,0)" writing-mode="lr" x="752.1900000000001" xml:space="preserve" y="399.72" zvalue="189">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,826.875,641.222) scale(1,1) translate(0,0)" writing-mode="lr" x="826.88" xml:space="preserve" y="645.72" zvalue="198">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,666.598,636.222) scale(1,1) translate(0,0)" writing-mode="lr" x="666.6" xml:space="preserve" y="640.72" zvalue="202">656</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="196" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,597.723,581.49) scale(1,1) translate(0,0)" writing-mode="lr" x="597.72" xml:space="preserve" y="585.99" zvalue="204">近区变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,668,505.75) scale(1,1) translate(0,0)" writing-mode="lr" x="668" xml:space="preserve" y="510.25" zvalue="206">0516</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,585.25,527.75) scale(1,1) translate(0,0)" writing-mode="lr" x="585.25" xml:space="preserve" y="532.25" zvalue="208">0519</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,640.458,366.25) scale(1,1) translate(0,0)" writing-mode="lr" x="640.46" xml:space="preserve" y="370.75" zvalue="212">10kV近区</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,599.223,601.49) scale(1,1) translate(0,0)" writing-mode="lr" x="599.22" xml:space="preserve" y="605.99" zvalue="220">0.63MWA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="205" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,648.03,734.154) scale(1,1) translate(0,0)" writing-mode="lr" x="648.03" xml:space="preserve" y="738.65" zvalue="223">651</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,624.738,897.852) scale(1,1) translate(-1.29446e-13,0)" writing-mode="lr" x="624.7379869487851" xml:space="preserve" y="902.3517292402917" zvalue="227">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,573.605,801.383) scale(1,1) translate(0,0)" writing-mode="lr" x="573.6" xml:space="preserve" y="805.88" zvalue="230">6911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,740.034,807.977) scale(1,1) translate(0,0)" writing-mode="lr" x="740.03" xml:space="preserve" y="812.48" zvalue="233">6913</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,660.867,801.531) scale(1,1) translate(0,0)" writing-mode="lr" x="660.87" xml:space="preserve" y="806.03" zvalue="242">6912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,624.738,917.852) scale(1,1) translate(-1.29446e-13,0)" writing-mode="lr" x="624.7379869487851" xml:space="preserve" y="922.3517292402917" zvalue="248">6.3MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,893.03,734.154) scale(1,1) translate(0,0)" writing-mode="lr" x="893.03" xml:space="preserve" y="738.65" zvalue="251">652</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,869.738,897.852) scale(1,1) translate(-1.83847e-13,0)" writing-mode="lr" x="869.737986948785" xml:space="preserve" y="902.3517292402917" zvalue="255">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,818.605,801.383) scale(1,1) translate(0,0)" writing-mode="lr" x="818.6" xml:space="preserve" y="805.88" zvalue="257">6921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,995.034,805.977) scale(1,1) translate(0,0)" writing-mode="lr" x="995.03" xml:space="preserve" y="810.48" zvalue="260">6923</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,905.867,801.531) scale(1,1) translate(0,0)" writing-mode="lr" x="905.87" xml:space="preserve" y="806.03" zvalue="265">6922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="206" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,869.738,917.852) scale(1,1) translate(-1.83847e-13,0)" writing-mode="lr" x="869.737986948785" xml:space="preserve" y="922.3517292402917" zvalue="270">6.3MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1065,741.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1065" xml:space="preserve" y="745.83" zvalue="275">654</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="235" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1038,850) scale(1,1) translate(0,0)" writing-mode="lr" x="1038" xml:space="preserve" y="854.5" zvalue="279">#1厂用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="241" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1163.6,753.383) scale(1,1) translate(0,0)" writing-mode="lr" x="1163.6" xml:space="preserve" y="757.88" zvalue="282">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1289,664) scale(1,1) translate(0,0)" writing-mode="lr" x="1289" xml:space="preserve" y="668.5" zvalue="287">6.3kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="254" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1381.03,734.154) scale(1,1) translate(0,0)" writing-mode="lr" x="1381.03" xml:space="preserve" y="738.65" zvalue="290">653</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1357.74,897.852) scale(1,1) translate(-2.92205e-13,0)" writing-mode="lr" x="1357.737986948785" xml:space="preserve" y="902.3517292402917" zvalue="294">#3发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1306.6,801.383) scale(1,1) translate(0,0)" writing-mode="lr" x="1306.6" xml:space="preserve" y="805.88" zvalue="296">6931</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1473.03,807.977) scale(1,1) translate(0,0)" writing-mode="lr" x="1473.03" xml:space="preserve" y="812.48" zvalue="299">6933</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="248" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1393.87,801.531) scale(1,1) translate(0,0)" writing-mode="lr" x="1393.87" xml:space="preserve" y="806.03" zvalue="305">6932</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1357.74,917.852) scale(1,1) translate(-2.92205e-13,0)" writing-mode="lr" x="1357.737986948785" xml:space="preserve" y="922.3517292402917" zvalue="310">6.3MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="274" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1591.03,754.562) scale(1,1) translate(-7.00207e-13,-6.61351e-13)" writing-mode="lr" x="1591.03" xml:space="preserve" y="759.0599999999999" zvalue="313">655</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="273" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1557.45,889.687) scale(1,1) translate(-2.36955e-12,-5.84781e-13)" writing-mode="lr" x="1557.45" xml:space="preserve" y="894.1900000000001" zvalue="317">#2厂用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="279" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1716.6,753.383) scale(1,1) translate(0,0)" writing-mode="lr" x="1716.6" xml:space="preserve" y="757.88" zvalue="320">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="294" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1437.89,407.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1437.89" xml:space="preserve" y="411.72" zvalue="329">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="293" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1447.23,463.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1447.23" xml:space="preserve" y="467.72" zvalue="330">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1469.56,440.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1469.56" xml:space="preserve" y="444.72" zvalue="333">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="290" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1340.12,561) scale(1,1) translate(0,0)" writing-mode="lr" x="1340.12" xml:space="preserve" y="565.5" zvalue="340">1020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="310" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1517.37,529.75) scale(1,1) translate(-1.3155e-12,0)" writing-mode="lr" x="1517.367063355077" xml:space="preserve" y="534.25" zvalue="341">40MVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1519,513.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1519" xml:space="preserve" y="518" zvalue="346">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="286" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1715.67,399.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1715.67" xml:space="preserve" y="403.72" zvalue="352">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1725.11,455.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1725.11" xml:space="preserve" y="459.72" zvalue="353">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1718.48,505.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1718.48" xml:space="preserve" y="509.72" zvalue="357">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="323" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1774.56,523.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1774.56" xml:space="preserve" y="528.1900000000001" zvalue="370">67</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="393" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="422"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="391" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="424">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="390" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="425">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="389" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="426">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="388" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="427">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="387" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="428">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="385" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="430">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="384" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="431">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="383" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="432">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="382" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="433">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="381" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="434">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="380" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="435">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="379" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241.625,334) scale(1,1) translate(0,0)" writing-mode="lr" x="241.63" xml:space="preserve" y="338.5" zvalue="436">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="378" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,357.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="361.75" zvalue="437">6.3kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="370" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="445">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="369" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="446">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="366" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="449">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="364" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="451">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="363" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="452">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="362" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="453">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="361" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="454">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="456">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="395" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,239.688,357.25) scale(1,1) translate(0,0)" writing-mode="lr" x="239.69" xml:space="preserve" y="361.75" zvalue="460">6.3kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,207.5,955) scale(1,1) translate(0,0)" writing-mode="lr" x="207.5" xml:space="preserve" y="961" zvalue="463">MengDian-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1448,642) scale(1,1) translate(0,0)" writing-mode="lr" x="1448" xml:space="preserve" y="646.5" zvalue="485">602</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1040.77,871.239) scale(1,1) translate(-2.21823e-13,0)" writing-mode="lr" x="1040.765473600949" xml:space="preserve" y="875.7386486920639" zvalue="488">0.315MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1556.77,906.239) scale(1,1) translate(-3.36398e-13,0)" writing-mode="lr" x="1556.765473600949" xml:space="preserve" y="910.7386486920639" zvalue="490">0.315MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,864.25,489.75) scale(1,1) translate(0,0)" writing-mode="lr" x="864.2499999999999" xml:space="preserve" y="494.25" zvalue="492">16MVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,859.25,466.75) scale(1,1) translate(-1.82743e-13,0)" writing-mode="lr" x="859.2499999999999" xml:space="preserve" y="471.25" zvalue="495">#1主变</text>
 </g>
 <g id="ButtonClass">
  <g href="户撒四级电站_110kV母线.svg"><rect fill-opacity="0" height="40" width="12" x="1071" y="252" zvalue="115"/></g>
  <g href="户撒四级电站_110kV母线.svg"><rect fill-opacity="0" height="40" width="12" x="1695.59" y="245" zvalue="166"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="444"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="116">
   <use class="kv110" height="30" transform="rotate(0,1077.22,278.667) scale(-1.11111,-0.814815) translate(-2045.89,-623.444)" width="15" x="1068.888888888889" xlink:href="#Disconnector:刀闸_0" y="266.4444581137763" zvalue="108"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454783795202" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454783795202"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1077.22,278.667) scale(-1.11111,-0.814815) translate(-2045.89,-623.444)" width="15" x="1068.888888888889" y="266.4444581137763"/></g>
  <g id="848">
   <use class="kv35" height="30" transform="rotate(0,1701.81,271.667) scale(-1.11111,-0.814815) translate(-3232.61,-607.854)" width="15" x="1693.476400781394" xlink:href="#Disconnector:刀闸_0" y="259.4444581137762" zvalue="159"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454784647170" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454784647170"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1701.81,271.667) scale(-1.11111,-0.814815) translate(-3232.61,-607.854)" width="15" x="1693.476400781394" y="259.4444581137762"/></g>
  <g id="60">
   <use class="kv110" height="30" transform="rotate(0,714.75,292.917) scale(-1.11111,-0.814815) translate(-1357.19,-655.183)" width="15" x="706.4166666666666" xlink:href="#Disconnector:刀闸_0" y="280.6944580078125" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454782222338" ObjectName="110kV勐平线1511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454782222338"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,714.75,292.917) scale(-1.11111,-0.814815) translate(-1357.19,-655.183)" width="15" x="706.4166666666666" y="280.6944580078125"/></g>
  <g id="59">
   <use class="kv110" height="30" transform="rotate(0,714.75,184.917) scale(-1.11111,-0.814815) translate(-1357.19,-414.638)" width="15" x="706.4166666931577" xlink:href="#Disconnector:刀闸_0" y="172.6944444444445" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454782156802" ObjectName="110kV勐平线1516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454782156802"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,714.75,184.917) scale(-1.11111,-0.814815) translate(-1357.19,-414.638)" width="15" x="706.4166666931577" y="172.6944444444445"/></g>
  <g id="81">
   <use class="kv110" height="30" transform="rotate(0,831,291.667) scale(-1.11111,-0.814815) translate(-1578.07,-652.399)" width="15" x="822.6666666666666" xlink:href="#Disconnector:刀闸_0" y="279.4444580078125" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454782812162" ObjectName="110kV勐二一线1521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454782812162"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,831,291.667) scale(-1.11111,-0.814815) translate(-1578.07,-652.399)" width="15" x="822.6666666666666" y="279.4444580078125"/></g>
  <g id="80">
   <use class="kv110" height="30" transform="rotate(0,831,183.667) scale(-1.11111,-0.814815) translate(-1578.07,-411.854)" width="15" x="822.6666666931577" xlink:href="#Disconnector:刀闸_0" y="171.4444444444445" zvalue="68"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454782746626" ObjectName="110kV勐二一线1526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454782746626"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,831,183.667) scale(-1.11111,-0.814815) translate(-1578.07,-411.854)" width="15" x="822.6666666931577" y="171.4444444444445"/></g>
  <g id="102">
   <use class="kv110" height="30" transform="rotate(0,945.5,292.917) scale(-1.11111,-0.814815) translate(-1795.62,-655.183)" width="15" x="937.1666666666666" xlink:href="#Disconnector:刀闸_0" y="280.6944580078125" zvalue="87"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454783401986" ObjectName="110kV勐五一线1531隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454783401986"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,945.5,292.917) scale(-1.11111,-0.814815) translate(-1795.62,-655.183)" width="15" x="937.1666666666666" y="280.6944580078125"/></g>
  <g id="101">
   <use class="kv110" height="30" transform="rotate(0,945.5,184.917) scale(-1.11111,-0.814815) translate(-1795.62,-414.638)" width="15" x="937.1666666931577" xlink:href="#Disconnector:刀闸_0" y="172.6944444444445" zvalue="90"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454783336450" ObjectName="110kV勐五一线1536隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454783336450"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,945.5,184.917) scale(-1.11111,-0.814815) translate(-1795.62,-414.638)" width="15" x="937.1666666931577" y="172.6944444444445"/></g>
  <g id="824">
   <use class="kv35" height="30" transform="rotate(0,1460,289.222) scale(-1.11111,-0.814815) translate(-2773.17,-646.955)" width="15" x="1451.666666666667" xlink:href="#Disconnector:刀闸_0" y="276.9999862247042" zvalue="127"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454784057346" ObjectName="35kV光明硅厂Ⅲ回线3511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454784057346"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1460,289.222) scale(-1.11111,-0.814815) translate(-2773.17,-646.955)" width="15" x="1451.666666666667" y="276.9999862247042"/></g>
  <g id="823">
   <use class="kv35" height="30" transform="rotate(0,1460,189.222) scale(-1.11111,-0.814815) translate(-2773.17,-424.227)" width="15" x="1451.666666693158" xlink:href="#Disconnector:刀闸_0" y="176.9999862247043" zvalue="130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454783991810" ObjectName="35kV光明硅厂Ⅲ回线3516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454783991810"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1460,189.222) scale(-1.11111,-0.814815) translate(-2773.17,-424.227)" width="15" x="1451.666666693158" y="176.9999862247043"/></g>
  <g id="139">
   <use class="kv35" height="30" transform="rotate(0,1572.5,289.222) scale(-1.11111,-0.814815) translate(-2986.92,-646.955)" width="15" x="1564.166666666667" xlink:href="#Disconnector:刀闸_0" y="276.9999862247042" zvalue="140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454784253954" ObjectName="35kV勐支线3521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454784253954"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1572.5,289.222) scale(-1.11111,-0.814815) translate(-2986.92,-646.955)" width="15" x="1564.166666666667" y="276.9999862247042"/></g>
  <g id="138">
   <use class="kv35" height="30" transform="rotate(0,1572.5,189.222) scale(-1.11111,-0.814815) translate(-2986.92,-424.227)" width="15" x="1564.166666693157" xlink:href="#Disconnector:刀闸_0" y="176.9999862247043" zvalue="143"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454784188418" ObjectName="35kV勐支线3526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454784188418"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1572.5,189.222) scale(-1.11111,-0.814815) translate(-2986.92,-424.227)" width="15" x="1564.166666693157" y="176.9999862247043"/></g>
  <g id="186">
   <use class="kv110" height="30" transform="rotate(0,801.083,367.222) scale(1.11111,0.814815) translate(-79.275,80.6818)" width="15" x="792.7500101725263" xlink:href="#Disconnector:刀闸_0" y="355" zvalue="177"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454785040386" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454785040386"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,801.083,367.222) scale(1.11111,0.814815) translate(-79.275,80.6818)" width="15" x="792.7500101725263" y="355"/></g>
  <g id="250">
   <use class="kv10" height="36" transform="rotate(0,640.25,506.75) scale(1,-1) translate(0,-1013.5)" width="14" x="633.25" xlink:href="#Disconnector:手车刀闸_0" y="488.75" zvalue="205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454785105922" ObjectName="近区变10kV侧0516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454785105922"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,640.25,506.75) scale(1,-1) translate(0,-1013.5)" width="14" x="633.25" y="488.75"/></g>
  <g id="180">
   <use class="kv10" height="36" transform="rotate(90,586.5,543) scale(-1,-1) translate(-1173,-1086)" width="14" x="579.5" xlink:href="#Disconnector:手车刀闸_0" y="525" zvalue="207"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454785171458" ObjectName="近区变10kV侧0519隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454785171458"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(90,586.5,543) scale(-1,-1) translate(-1173,-1086)" width="14" x="579.5" y="525"/></g>
  <g id="182">
   <use class="kv10" height="30" transform="rotate(0,640.354,445.25) scale(1.25,1.25) translate(-126.196,-85.3)" width="15" x="630.9791666666667" xlink:href="#Disconnector:令克_0" y="426.5" zvalue="208"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454785236994" ObjectName="10kV近区隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454785236994"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,640.354,445.25) scale(1.25,1.25) translate(-126.196,-85.3)" width="15" x="630.9791666666667" y="426.5"/></g>
  <g id="215">
   <use class="v6300" height="36" transform="rotate(0,546.58,802.531) scale(1.05291,0.829527) translate(-27.0965,161.857)" width="14" x="539.2094126952742" xlink:href="#Disconnector:手车刀闸_0" y="787.5996699684119" zvalue="228"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454785695746" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454785695746"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,546.58,802.531) scale(1.05291,0.829527) translate(-27.0965,161.857)" width="14" x="539.2094126952742" y="787.5996699684119"/></g>
  <g id="212">
   <use class="v6300" height="30" transform="rotate(0,716.868,802.531) scale(0.491359,0.995433) translate(734.453,3.61381)" width="30" x="709.4973304330639" xlink:href="#Disconnector:三相刀闸_0" y="787.5996698904675" zvalue="232"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454785630210" ObjectName="#1发电机6913隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454785630210"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,716.868,802.531) scale(0.491359,0.995433) translate(734.453,3.61381)" width="30" x="709.4973304330639" y="787.5996698904675"/></g>
  <g id="218">
   <use class="v6300" height="36" transform="rotate(0,684.738,802.531) scale(1.05291,0.829527) translate(-34.0392,161.857)" width="14" x="677.3673074321163" xlink:href="#Disconnector:手车刀闸_0" y="787.599669910141" zvalue="241"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454785826818" ObjectName="#1发电机6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454785826818"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,684.738,802.531) scale(1.05291,0.829527) translate(-34.0392,161.857)" width="14" x="677.3673074321163" y="787.599669910141"/></g>
  <g id="229">
   <use class="v6300" height="36" transform="rotate(0,791.58,802.531) scale(1.05291,0.829527) translate(-39.4082,161.857)" width="14" x="784.2094126952742" xlink:href="#Disconnector:联体小车刀闸2_0" y="787.5996699684119" zvalue="256"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454786154498" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454786154498"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,791.58,802.531) scale(1.05291,0.829527) translate(-39.4082,161.857)" width="14" x="784.2094126952742" y="787.5996699684119"/></g>
  <g id="227">
   <use class="v6300" height="30" transform="rotate(0,961.868,802.531) scale(0.491359,0.995433) translate(988.071,3.61381)" width="30" x="954.4973304330638" xlink:href="#Disconnector:三相刀闸_0" y="787.5996698904675" zvalue="259"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454786088962" ObjectName="#2发电机6923隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454786088962"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,961.868,802.531) scale(0.491359,0.995433) translate(988.071,3.61381)" width="30" x="954.4973304330638" y="787.5996698904675"/></g>
  <g id="223">
   <use class="v6300" height="36" transform="rotate(0,929.738,802.531) scale(1.05291,0.829527) translate(-46.351,161.857)" width="14" x="922.3673074321163" xlink:href="#Disconnector:手车刀闸_0" y="787.599669910141" zvalue="264"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454785892354" ObjectName="#2发电机6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454785892354"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,929.738,802.531) scale(1.05291,0.829527) translate(-46.351,161.857)" width="14" x="922.3673074321163" y="787.599669910141"/></g>
  <g id="244">
   <use class="v6300" height="36" transform="rotate(0,1136.58,754.531) scale(1.05291,0.829527) translate(-56.7453,151.992)" width="14" x="1129.209412695274" xlink:href="#Disconnector:手车刀闸_0" y="739.5996699684119" zvalue="281"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449605074950" ObjectName="6.3kVⅠ母电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449605074950"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1136.58,754.531) scale(1.05291,0.829527) translate(-56.7453,151.992)" width="14" x="1129.209412695274" y="739.5996699684119"/></g>
  <g id="267">
   <use class="v6300" height="36" transform="rotate(0,1279.58,802.531) scale(1.05291,0.829527) translate(-63.9313,161.857)" width="14" x="1272.209412695274" xlink:href="#Disconnector:手车刀闸_0" y="787.5996699684119" zvalue="295"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454786875394" ObjectName="#3发电机6931隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454786875394"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1279.58,802.531) scale(1.05291,0.829527) translate(-63.9313,161.857)" width="14" x="1272.209412695274" y="787.5996699684119"/></g>
  <g id="265">
   <use class="v6300" height="30" transform="rotate(0,1449.87,802.531) scale(0.491359,0.995433) translate(1493.24,3.61381)" width="30" x="1442.497330433064" xlink:href="#Disconnector:三相刀闸_0" y="787.5996698904675" zvalue="298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454786809858" ObjectName="#3发电机6933隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454786809858"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1449.87,802.531) scale(0.491359,0.995433) translate(1493.24,3.61381)" width="30" x="1442.497330433064" y="787.5996698904675"/></g>
  <g id="260">
   <use class="v6300" height="36" transform="rotate(0,1417.74,802.531) scale(1.05291,0.829527) translate(-70.8741,161.857)" width="14" x="1410.367307432116" xlink:href="#Disconnector:手车刀闸_0" y="787.599669910141" zvalue="304"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454786547714" ObjectName="#3发电机6932隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454786547714"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1417.74,802.531) scale(1.05291,0.829527) translate(-70.8741,161.857)" width="14" x="1410.367307432116" y="787.599669910141"/></g>
  <g id="283">
   <use class="v6300" height="36" transform="rotate(0,1689.58,754.531) scale(1.05291,0.829527) translate(-84.5347,151.992)" width="14" x="1682.209412695274" xlink:href="#Disconnector:手车刀闸_0" y="739.5996699684119" zvalue="319"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449605730310" ObjectName="6.3kVⅡ母电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449605730310"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1689.58,754.531) scale(1.05291,0.829527) translate(-84.5347,151.992)" width="14" x="1682.209412695274" y="739.5996699684119"/></g>
  <g id="342">
   <use class="kv110" height="30" transform="rotate(0,1426.45,408.222) scale(1.11111,0.814815) translate(-141.812,90)" width="15" x="1418.117073527604" xlink:href="#Disconnector:刀闸_0" y="396" zvalue="327"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454787661826" ObjectName="#2主变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454787661826"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1426.45,408.222) scale(1.11111,0.814815) translate(-141.812,90)" width="15" x="1418.117073527604" y="396"/></g>
  <g id="306">
   <use class="kv35" height="30" transform="rotate(0,1704.22,400.222) scale(1.11111,0.814815) translate(-169.589,88.1818)" width="15" x="1695.891298801276" xlink:href="#Disconnector:刀闸_0" y="388" zvalue="350"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454787268610" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454787268610"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1704.22,400.222) scale(1.11111,0.814815) translate(-169.589,88.1818)" width="15" x="1695.891298801276" y="388"/></g>
  <g id="303">
   <use class="kv35" height="30" transform="rotate(0,1704.32,506.222) scale(1.11111,0.814815) translate(-169.598,112.273)" width="15" x="1695.983951438215" xlink:href="#Disconnector:刀闸_0" y="494" zvalue="355"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454787203074" ObjectName="#2主变35kV侧3026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454787203074"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1704.32,506.222) scale(1.11111,0.814815) translate(-169.598,112.273)" width="15" x="1695.983951438215" y="494"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="39">
   <path class="kv110" d="M 625 325.25 L 1160 325.25" stroke-width="6" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674421112835" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674421112835"/></metadata>
  <path d="M 625 325.25 L 1160 325.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv35" d="M 1395 325.25 L 1781 325.25" stroke-width="6" zvalue="124"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674421178371" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674421178371"/></metadata>
  <path d="M 1395 325.25 L 1781 325.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="v6300" d="M 500 687.75 L 1201.25 687.75" stroke-width="6" zvalue="175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674421243907" ObjectName="6.3kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674421243907"/></metadata>
  <path d="M 500 687.75 L 1201.25 687.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="246">
   <path class="v6300" d="M 1322 687 L 1754 687" stroke-width="6" zvalue="286"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674421309443" ObjectName="6.3kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674421309443"/></metadata>
  <path d="M 1322 687 L 1754 687" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="61">
   <use class="kv110" height="20" transform="rotate(0,714.75,245.361) scale(1.22222,1.11111) translate(-128.843,-23.425)" width="10" x="708.6388888888889" xlink:href="#Breaker:开关_0" y="234.25" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925215191043" ObjectName="110kV勐平线151断路器"/>
   <cge:TPSR_Ref TObjectID="6473925215191043"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,714.75,245.361) scale(1.22222,1.11111) translate(-128.843,-23.425)" width="10" x="708.6388888888889" y="234.25"/></g>
  <g id="82">
   <use class="kv110" height="20" transform="rotate(0,831,244.111) scale(1.22222,1.11111) translate(-149.98,-23.3)" width="10" x="824.8888888888889" xlink:href="#Breaker:开关_0" y="233" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925215256579" ObjectName="110kV勐二一线152断路器"/>
   <cge:TPSR_Ref TObjectID="6473925215256579"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,831,244.111) scale(1.22222,1.11111) translate(-149.98,-23.3)" width="10" x="824.8888888888889" y="233"/></g>
  <g id="103">
   <use class="kv110" height="20" transform="rotate(0,945.5,245.361) scale(1.22222,1.11111) translate(-170.798,-23.425)" width="10" x="939.3888888888889" xlink:href="#Breaker:开关_0" y="234.25" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925215322115" ObjectName="110kV勐五一线153断路器"/>
   <cge:TPSR_Ref TObjectID="6473925215322115"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,945.5,245.361) scale(1.22222,1.11111) translate(-170.798,-23.425)" width="10" x="939.3888888888889" y="234.25"/></g>
  <g id="825">
   <use class="kv35" height="20" transform="rotate(0,1460,241.667) scale(1.22222,1.11111) translate(-264.343,-23.0556)" width="10" x="1453.888888888889" xlink:href="#Breaker:开关_0" y="230.55554178026" zvalue="126"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925215387651" ObjectName="35kV光明硅厂Ⅲ回线351断路器"/>
   <cge:TPSR_Ref TObjectID="6473925215387651"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1460,241.667) scale(1.22222,1.11111) translate(-264.343,-23.0556)" width="10" x="1453.888888888889" y="230.55554178026"/></g>
  <g id="140">
   <use class="kv35" height="20" transform="rotate(0,1572.5,241.667) scale(1.22222,1.11111) translate(-284.798,-23.0556)" width="10" x="1566.388888888889" xlink:href="#Breaker:开关_0" y="230.55554178026" zvalue="139"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925215453187" ObjectName="35kV勐支线352断路器"/>
   <cge:TPSR_Ref TObjectID="6473925215453187"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1572.5,241.667) scale(1.22222,1.11111) translate(-284.798,-23.0556)" width="10" x="1566.388888888889" y="230.55554178026"/></g>
  <g id="185">
   <use class="kv110" height="20" transform="rotate(0,801.083,407.222) scale(1.22222,1.11111) translate(-144.54,-39.6111)" width="10" x="794.9722324079938" xlink:href="#Breaker:开关_0" y="396.1111111111111" zvalue="179"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925215518723" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925215518723"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,801.083,407.222) scale(1.22222,1.11111) translate(-144.54,-39.6111)" width="10" x="794.9722324079938" y="396.1111111111111"/></g>
  <g id="249">
   <use class="v6300" height="20" transform="rotate(0,801.75,637.222) scale(2,2) translate(-395.875,-308.611)" width="10" x="791.7500000000001" xlink:href="#Breaker:手车开关_0" y="617.2222449845738" zvalue="197"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925215584259" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473925215584259"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,801.75,637.222) scale(2,2) translate(-395.875,-308.611)" width="10" x="791.7500000000001" y="617.2222449845738"/></g>
  <g id="178">
   <use class="v6300" height="20" transform="rotate(0,640.223,637.222) scale(2,2) translate(-315.112,-308.611)" width="10" x="630.2233882030179" xlink:href="#Breaker:手车开关_0" y="617.2222442626954" zvalue="201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925215649795" ObjectName="近区变6.3kV侧656断路器"/>
   <cge:TPSR_Ref TObjectID="6473925215649795"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,640.223,637.222) scale(2,2) translate(-315.112,-308.611)" width="10" x="630.2233882030179" y="617.2222442626954"/></g>
  <g id="341">
   <use class="v6300" height="20" transform="rotate(0,623.03,735.089) scale(2,1.87004) translate(-306.515,-333.301)" width="10" x="613.0295695852833" xlink:href="#Breaker:手车开关_0" y="716.3883672042609" zvalue="222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925215715331" ObjectName="#1发电机651断路器"/>
   <cge:TPSR_Ref TObjectID="6473925215715331"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,623.03,735.089) scale(2,1.87004) translate(-306.515,-333.301)" width="10" x="613.0295695852833" y="716.3883672042609"/></g>
  <g id="233">
   <use class="v6300" height="20" transform="rotate(0,868.03,735.089) scale(2,1.87004) translate(-429.015,-333.301)" width="10" x="858.0295695852833" xlink:href="#Breaker:手车开关_0" y="716.3883672042609" zvalue="250"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925215780867" ObjectName="#2发电机652断路器"/>
   <cge:TPSR_Ref TObjectID="6473925215780867"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,868.03,735.089) scale(2,1.87004) translate(-429.015,-333.301)" width="10" x="858.0295695852833" y="716.3883672042609"/></g>
  <g id="240">
   <use class="v6300" height="20" transform="rotate(0,1038.5,742.333) scale(2,2) translate(-514.25,-361.167)" width="10" x="1028.5" xlink:href="#Breaker:手车开关_0" y="722.3333384195963" zvalue="274"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925215846403" ObjectName="#1厂用变654断路器"/>
   <cge:TPSR_Ref TObjectID="6473925215846403"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1038.5,742.333) scale(2,2) translate(-514.25,-361.167)" width="10" x="1028.5" y="722.3333384195963"/></g>
  <g id="271">
   <use class="v6300" height="20" transform="rotate(0,1356.03,735.089) scale(2,1.87004) translate(-673.015,-333.301)" width="10" x="1346.029569585283" xlink:href="#Breaker:手车开关_0" y="716.3883672042609" zvalue="289"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925215911939" ObjectName="#3发电机653断路器"/>
   <cge:TPSR_Ref TObjectID="6473925215911939"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1356.03,735.089) scale(2,1.87004) translate(-673.015,-333.301)" width="10" x="1346.029569585283" y="716.3883672042609"/></g>
  <g id="278">
   <use class="v6300" height="20" transform="rotate(0,1558.07,755.806) scale(2.48696,2.48696) translate(-924.141,-437.028)" width="10" x="1545.639130434783" xlink:href="#Breaker:手车开关_0" y="730.9362382087154" zvalue="312"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925215977475" ObjectName="#2厂用变655断路器"/>
   <cge:TPSR_Ref TObjectID="6473925215977475"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1558.07,755.806) scale(2.48696,2.48696) translate(-924.141,-437.028)" width="10" x="1545.639130434783" y="730.9362382087154"/></g>
  <g id="340">
   <use class="kv110" height="20" transform="rotate(0,1426.45,464.222) scale(1.22222,1.11111) translate(-258.244,-45.3111)" width="10" x="1420.339295763071" xlink:href="#Breaker:开关_0" y="453.1111111111111" zvalue="328"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925216108547" ObjectName="#2主变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473925216108547"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1426.45,464.222) scale(1.22222,1.11111) translate(-258.244,-45.3111)" width="10" x="1420.339295763071" y="453.1111111111111"/></g>
  <g id="305">
   <use class="kv35" height="20" transform="rotate(0,1704.33,456.222) scale(1.22222,1.11111) translate(-308.768,-44.5111)" width="10" x="1698.222222235468" xlink:href="#Breaker:开关_0" y="445.1111111111111" zvalue="351"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925216043011" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925216043011"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1704.33,456.222) scale(1.22222,1.11111) translate(-308.768,-44.5111)" width="10" x="1698.222222235468" y="445.1111111111111"/></g>
  <g id="23">
   <use class="v6300" height="20" transform="rotate(0,1426,643) scale(2,2) translate(-708,-311.5)" width="10" x="1416" xlink:href="#Breaker:手车开关_0" y="623" zvalue="484"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924570906628" ObjectName="#2主变6.3kV侧602断路器"/>
   <cge:TPSR_Ref TObjectID="6473924570906628"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1426,643) scale(2,2) translate(-708,-311.5)" width="10" x="1416" y="623"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="58">
   <path class="kv110" d="M 714.65 304.73 L 714.65 325.25" stroke-width="1" zvalue="47"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="39@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 714.65 304.73 L 714.65 325.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv110" d="M 714.68 280.9 L 714.68 255.97" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@1" LinkObjectIDznd="61@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 714.68 280.9 L 714.68 255.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv110" d="M 714.71 234.73 L 714.65 196.73" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="59@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 714.71 234.73 L 714.65 196.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv110" d="M 714.68 172.9 L 714.68 152.47" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@1" LinkObjectIDznd="55@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 714.68 172.9 L 714.68 152.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv110" d="M 677.19 268.53 L 714.68 268.53" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="57" MaxPinNum="2"/>
   </metadata>
  <path d="M 677.19 268.53 L 714.68 268.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv110" d="M 677.19 216.08 L 714.68 216.08" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="56" MaxPinNum="2"/>
   </metadata>
  <path d="M 677.19 216.08 L 714.68 216.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv110" d="M 677.19 161.64 L 714.68 161.64" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="54" MaxPinNum="2"/>
   </metadata>
  <path d="M 677.19 161.64 L 714.68 161.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv110" d="M 830.9 303.48 L 830.9 325.25" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@0" LinkObjectIDznd="39@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 830.9 303.48 L 830.9 325.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv110" d="M 830.93 279.65 L 830.93 254.72" stroke-width="1" zvalue="71"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@1" LinkObjectIDznd="82@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 830.93 279.65 L 830.93 254.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv110" d="M 830.96 233.48 L 830.9 195.48" stroke-width="1" zvalue="72"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="80@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 830.96 233.48 L 830.9 195.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv110" d="M 830.93 171.65 L 830.93 151.22" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@1" LinkObjectIDznd="76@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 830.93 171.65 L 830.93 151.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv110" d="M 793.44 267.28 L 830.93 267.28" stroke-width="1" zvalue="82"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="78" MaxPinNum="2"/>
   </metadata>
  <path d="M 793.44 267.28 L 830.93 267.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv110" d="M 793.44 214.83 L 830.93 214.83" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@0" LinkObjectIDznd="77" MaxPinNum="2"/>
   </metadata>
  <path d="M 793.44 214.83 L 830.93 214.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv110" d="M 793.44 160.39 L 830.93 160.39" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@0" LinkObjectIDznd="75" MaxPinNum="2"/>
   </metadata>
  <path d="M 793.44 160.39 L 830.93 160.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv110" d="M 945.4 304.73 L 945.4 325.25" stroke-width="1" zvalue="91"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="39@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 945.4 304.73 L 945.4 325.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv110" d="M 945.43 280.9 L 945.43 255.97" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@1" LinkObjectIDznd="103@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 945.43 280.9 L 945.43 255.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv110" d="M 945.46 234.73 L 945.4 196.73" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="101@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 945.46 234.73 L 945.4 196.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv110" d="M 945.43 172.9 L 945.43 152.47" stroke-width="1" zvalue="97"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@1" LinkObjectIDznd="97@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 945.43 172.9 L 945.43 152.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv110" d="M 907.94 268.53 L 945.43 268.53" stroke-width="1" zvalue="104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@0" LinkObjectIDznd="99" MaxPinNum="2"/>
   </metadata>
  <path d="M 907.94 268.53 L 945.43 268.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv110" d="M 907.94 216.08 L 945.43 216.08" stroke-width="1" zvalue="105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="98" MaxPinNum="2"/>
   </metadata>
  <path d="M 907.94 216.08 L 945.43 216.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv110" d="M 907.94 161.64 L 945.43 161.64" stroke-width="1" zvalue="106"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 907.94 161.64 L 945.43 161.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv110" d="M 1077.12 290.48 L 1077.12 325.25" stroke-width="1" zvalue="112"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="39@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1077.12 290.48 L 1077.12 325.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv110" d="M 1046.17 296.61 L 1077.12 296.61" stroke-width="1" zvalue="120"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="114" MaxPinNum="2"/>
   </metadata>
  <path d="M 1046.17 296.61 L 1077.12 296.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv110" d="M 1114.47 219.37 L 1114.47 239.47 L 1077.15 239.47" stroke-width="1" zvalue="123"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="110@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1114.47 219.37 L 1114.47 239.47 L 1077.15 239.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv35" d="M 1459.9 301.04 L 1459.9 325.25" stroke-width="1" zvalue="131"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="824@0" LinkObjectIDznd="119@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1459.9 301.04 L 1459.9 325.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv35" d="M 1459.93 277.21 L 1459.93 252.28" stroke-width="1" zvalue="133"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="824@1" LinkObjectIDznd="825@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1459.93 277.21 L 1459.93 252.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv35" d="M 1459.96 231.04 L 1459.9 201.04" stroke-width="1" zvalue="134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="825@0" LinkObjectIDznd="823@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1459.96 231.04 L 1459.9 201.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv35" d="M 1459.93 177.21 L 1459.93 137.78" stroke-width="1" zvalue="137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="823@1" LinkObjectIDznd="819@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1459.93 177.21 L 1459.93 137.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv35" d="M 1572.4 301.04 L 1572.4 325.25" stroke-width="1" zvalue="144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@0" LinkObjectIDznd="119@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1572.4 301.04 L 1572.4 325.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv35" d="M 1572.43 277.21 L 1572.43 252.28" stroke-width="1" zvalue="146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@1" LinkObjectIDznd="140@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1572.43 277.21 L 1572.43 252.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv35" d="M 1572.46 231.04 L 1572.4 201.04" stroke-width="1" zvalue="147"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="138@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1572.46 231.04 L 1572.4 201.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv35" d="M 1572.43 177.21 L 1572.43 141.19" stroke-width="1" zvalue="150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@1" LinkObjectIDznd="134@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1572.43 177.21 L 1572.43 141.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv35" d="M 1417.94 155.39 L 1459.93 155.39" stroke-width="1" zvalue="153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@0" LinkObjectIDznd="125" MaxPinNum="2"/>
   </metadata>
  <path d="M 1417.94 155.39 L 1459.93 155.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv35" d="M 1532.94 159.14 L 1572.43 159.14" stroke-width="1" zvalue="156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="143@0" LinkObjectIDznd="133" MaxPinNum="2"/>
   </metadata>
  <path d="M 1532.94 159.14 L 1572.43 159.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv35" d="M 1701.71 283.48 L 1701.71 325.25" stroke-width="1" zvalue="163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="848@0" LinkObjectIDznd="119@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1701.71 283.48 L 1701.71 325.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv35" d="M 1701.74 259.65 L 1701.74 232.47" stroke-width="1" zvalue="164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="848@1" LinkObjectIDznd="842@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1701.74 259.65 L 1701.74 232.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv35" d="M 1739.47 221.87 L 1739.47 236.5 L 1701.74 236.5" stroke-width="1" zvalue="174"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="154" MaxPinNum="2"/>
   </metadata>
  <path d="M 1739.47 221.87 L 1739.47 236.5 L 1701.74 236.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv110" d="M 801.15 379.24 L 801.15 396.59" stroke-width="1" zvalue="185"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@1" LinkObjectIDznd="185@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 801.15 379.24 L 801.15 396.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv110" d="M 801.78 499.73 L 801.78 510.42 L 911.89 510.42 L 911.89 531.11" stroke-width="1" zvalue="187"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@2" LinkObjectIDznd="181@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 801.78 499.73 L 801.78 510.42 L 911.89 510.42 L 911.89 531.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv110" d="M 763.03 383.06 L 801.15 383.06" stroke-width="1" zvalue="190"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@0" LinkObjectIDznd="173" MaxPinNum="2"/>
   </metadata>
  <path d="M 763.03 383.06 L 801.15 383.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv110" d="M 801.18 355.4 L 801.18 325.25" stroke-width="1" zvalue="193"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="39@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 801.18 355.4 L 801.18 325.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="kv110" d="M 801.16 417.83 L 801.16 471.89" stroke-width="1" zvalue="195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@1" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 801.16 417.83 L 801.16 471.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="v6300" d="M 801.75 568.47 L 801.75 618.72" stroke-width="1" zvalue="198"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@1" LinkObjectIDznd="249@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 801.75 568.47 L 801.75 618.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="v6300" d="M 801.75 655.22 L 801.75 687.75" stroke-width="1" zvalue="199"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@1" LinkObjectIDznd="159@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 801.75 655.22 L 801.75 687.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="v6300" d="M 640.22 655.22 L 640.22 687.75" stroke-width="1" zvalue="212"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@1" LinkObjectIDznd="159@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 640.22 655.22 L 640.22 687.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="v6300" d="M 640.22 600.4 L 640.22 618.72" stroke-width="1" zvalue="213"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@1" LinkObjectIDznd="178@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 640.22 600.4 L 640.22 618.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="kv10" d="M 640.25 523.75 L 640.25 564.65" stroke-width="1" zvalue="214"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@0" LinkObjectIDznd="179@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 640.25 523.75 L 640.25 564.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="kv10" d="M 640.25 460.56 L 640.25 489.75" stroke-width="1" zvalue="215"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@1" LinkObjectIDznd="250@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 640.25 460.56 L 640.25 489.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv10" d="M 640.46 399.94 L 640.46 428.69" stroke-width="1" zvalue="216"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@0" LinkObjectIDznd="182@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 640.46 399.94 L 640.46 428.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 552.46 542.34 L 569.5 542.34" stroke-width="1" zvalue="217"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@0" LinkObjectIDznd="180@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 552.46 542.34 L 569.5 542.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv10" d="M 603.5 543 L 640.25 543" stroke-width="1" zvalue="218"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@1" LinkObjectIDznd="191" MaxPinNum="2"/>
   </metadata>
  <path d="M 603.5 543 L 640.25 543" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="v6300" d="M 623.03 717.79 L 623.03 687.75" stroke-width="1" zvalue="224"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@0" LinkObjectIDznd="159@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 623.03 717.79 L 623.03 687.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="v6300" d="M 623.03 819.32 L 623.03 751.92" stroke-width="1" zvalue="225"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="341@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 623.03 819.32 L 623.03 751.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="v6300" d="M 546.58 816.63 L 546.58 831.35" stroke-width="1" zvalue="231"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@1" LinkObjectIDznd="209@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 546.58 816.63 L 546.58 831.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="v6300" d="M 716.87 817.46 L 716.87 838.22" stroke-width="1" zvalue="235"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@1" LinkObjectIDznd="208@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 716.87 817.46 L 716.87 838.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="219">
   <path class="v6300" d="M 684.93 842.15 L 684.93 816.63" stroke-width="1" zvalue="242"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="218@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 684.93 842.15 L 684.93 816.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="v6300" d="M 546.58 788.43 L 546.58 770 L 623.03 770" stroke-width="1" zvalue="244"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@0" LinkObjectIDznd="216" MaxPinNum="2"/>
   </metadata>
  <path d="M 546.58 788.43 L 546.58 770 L 623.03 770" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="v6300" d="M 716.87 787.6 L 716.87 768 L 623.03 768" stroke-width="1" zvalue="245"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="216" MaxPinNum="2"/>
   </metadata>
  <path d="M 716.87 787.6 L 716.87 768 L 623.03 768" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="v6300" d="M 684.74 788.43 L 684.74 768" stroke-width="1" zvalue="246"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@0" LinkObjectIDznd="149" MaxPinNum="2"/>
   </metadata>
  <path d="M 684.74 788.43 L 684.74 768" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="v6300" d="M 868.03 717.79 L 868.03 687.75" stroke-width="1" zvalue="252"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@0" LinkObjectIDznd="159@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 868.03 717.79 L 868.03 687.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="v6300" d="M 868.03 819.32 L 868.03 751.92" stroke-width="1" zvalue="253"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="233@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 868.03 819.32 L 868.03 751.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="v6300" d="M 791.58 816.63 L 791.58 831.35" stroke-width="1" zvalue="258"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@1" LinkObjectIDznd="234@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 791.58 816.63 L 791.58 831.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="v6300" d="M 961.87 817.46 L 961.87 838.22" stroke-width="1" zvalue="261"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="227@1" LinkObjectIDznd="225@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 961.87 817.46 L 961.87 838.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="v6300" d="M 929.93 842.15 L 929.93 816.63" stroke-width="1" zvalue="266"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="224@0" LinkObjectIDznd="223@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 929.93 842.15 L 929.93 816.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="v6300" d="M 791.58 788.43 L 791.58 770 L 868.03 770" stroke-width="1" zvalue="267"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@0" LinkObjectIDznd="231" MaxPinNum="2"/>
   </metadata>
  <path d="M 791.58 788.43 L 791.58 770 L 868.03 770" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="v6300" d="M 961.87 787.6 L 961.87 768 L 868.03 768" stroke-width="1" zvalue="268"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="227@0" LinkObjectIDznd="231" MaxPinNum="2"/>
   </metadata>
  <path d="M 961.87 787.6 L 961.87 768 L 868.03 768" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="v6300" d="M 929.74 788.43 L 929.74 768" stroke-width="1" zvalue="269"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@0" LinkObjectIDznd="214" MaxPinNum="2"/>
   </metadata>
  <path d="M 929.74 788.43 L 929.74 768" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="v6300" d="M 1038.5 723.83 L 1038.5 687.75" stroke-width="1" zvalue="276"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="240@0" LinkObjectIDznd="159@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1038.5 723.83 L 1038.5 687.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="238">
   <path class="v6300" d="M 1038.5 779.79 L 1038.5 760.33" stroke-width="1" zvalue="277"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@0" LinkObjectIDznd="240@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1038.5 779.79 L 1038.5 760.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="243">
   <path class="v6300" d="M 1136.58 768.63 L 1136.58 783.35" stroke-width="1" zvalue="283"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="244@1" LinkObjectIDznd="242@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1136.58 768.63 L 1136.58 783.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="245">
   <path class="v6300" d="M 1136.58 740.43 L 1136.58 687.75" stroke-width="1" zvalue="285"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="244@0" LinkObjectIDznd="159@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1136.58 740.43 L 1136.58 687.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="v6300" d="M 1356.03 717.79 L 1356.03 687" stroke-width="1" zvalue="291"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@0" LinkObjectIDznd="246@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1356.03 717.79 L 1356.03 687" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="v6300" d="M 1356.03 819.32 L 1356.03 751.92" stroke-width="1" zvalue="292"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="271@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1356.03 819.32 L 1356.03 751.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="v6300" d="M 1279.58 816.63 L 1279.58 831.35" stroke-width="1" zvalue="297"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@1" LinkObjectIDznd="263@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1279.58 816.63 L 1279.58 831.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="264">
   <path class="v6300" d="M 1449.87 817.46 L 1449.87 838.22" stroke-width="1" zvalue="300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265@1" LinkObjectIDznd="262@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1449.87 817.46 L 1449.87 838.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="259">
   <path class="v6300" d="M 1417.93 842.15 L 1417.93 816.63" stroke-width="1" zvalue="306"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@0" LinkObjectIDznd="260@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1417.93 842.15 L 1417.93 816.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="258">
   <path class="v6300" d="M 1279.58 788.43 L 1279.58 770 L 1356.03 770" stroke-width="1" zvalue="307"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@0" LinkObjectIDznd="269" MaxPinNum="2"/>
   </metadata>
  <path d="M 1279.58 788.43 L 1279.58 770 L 1356.03 770" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="257">
   <path class="v6300" d="M 1449.87 787.6 L 1449.87 768 L 1356.03 768" stroke-width="1" zvalue="308"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265@0" LinkObjectIDznd="269" MaxPinNum="2"/>
   </metadata>
  <path d="M 1449.87 787.6 L 1449.87 768 L 1356.03 768" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="256">
   <path class="v6300" d="M 1417.74 788.43 L 1417.74 768" stroke-width="1" zvalue="309"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="260@0" LinkObjectIDznd="257" MaxPinNum="2"/>
   </metadata>
  <path d="M 1417.74 788.43 L 1417.74 768" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="277">
   <path class="v6300" d="M 1558.07 732.8 L 1558.07 687" stroke-width="1" zvalue="314"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="278@0" LinkObjectIDznd="246@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1558.07 732.8 L 1558.07 687" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="276">
   <path class="v6300" d="M 1558.07 802.38 L 1558.07 778.19" stroke-width="1" zvalue="315"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="275@0" LinkObjectIDznd="278@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1558.07 802.38 L 1558.07 778.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="282">
   <path class="v6300" d="M 1689.58 768.63 L 1689.58 783.35" stroke-width="1" zvalue="321"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="283@1" LinkObjectIDznd="281@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1689.58 768.63 L 1689.58 783.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="280">
   <path class="v6300" d="M 1689.58 740.43 L 1689.58 687" stroke-width="1" zvalue="323"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="283@0" LinkObjectIDznd="246@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1689.58 740.43 L 1689.58 687" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="316">
   <path class="kv110" d="M 1426.52 420.24 L 1426.52 453.59" stroke-width="1" zvalue="331"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="342@1" LinkObjectIDznd="340@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426.52 420.24 L 1426.52 453.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="314">
   <path class="kv110" d="M 1426.53 474.83 L 1426.53 505.45" stroke-width="1" zvalue="336"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="340@1" LinkObjectIDznd="347@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426.53 474.83 L 1426.53 505.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="313">
   <path class="kv110" d="M 1458.73 428.06 L 1426.52 428.06" stroke-width="1" zvalue="337"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="338@0" LinkObjectIDznd="316" MaxPinNum="2"/>
   </metadata>
  <path d="M 1458.73 428.06 L 1426.52 428.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv110" d="M 1426.15 530.5 L 1307.94 530.5 L 1307.94 548.33" stroke-width="1" zvalue="339"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="347@1" LinkObjectIDznd="312@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426.15 530.5 L 1307.94 530.5 L 1307.94 548.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="304">
   <path class="kv35" d="M 1704.29 412.24 L 1704.29 445.59" stroke-width="1" zvalue="354"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="306@1" LinkObjectIDznd="305@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1704.29 412.24 L 1704.29 445.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="301">
   <path class="kv35" d="M 1704.41 466.83 L 1704.41 494.4" stroke-width="1" zvalue="358"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="305@1" LinkObjectIDznd="303@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1704.41 466.83 L 1704.41 494.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="317">
   <path class="kv35" d="M 1704.32 388.4 L 1704.32 325.25" stroke-width="1" zvalue="364"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="306@0" LinkObjectIDznd="119@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1704.32 388.4 L 1704.32 325.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="318">
   <path class="kv35" d="M 1704.39 518.24 L 1704.39 550.15 L 1488.1 550.15" stroke-width="1" zvalue="365"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="303@1" LinkObjectIDznd="347@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1704.39 518.24 L 1704.39 550.15 L 1488.1 550.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="319">
   <path class="kv110" d="M 1426.55 396.4 L 1426.55 366.5 L 1078.75 366.5 L 1078.75 325.25" stroke-width="1" zvalue="366"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="342@0" LinkObjectIDznd="39@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426.55 396.4 L 1426.55 366.5 L 1078.75 366.5 L 1078.75 325.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="325">
   <path class="kv35" d="M 1763.73 549.31 L 1704.39 549.31" stroke-width="1" zvalue="373"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="321@0" LinkObjectIDznd="318" MaxPinNum="2"/>
   </metadata>
  <path d="M 1763.73 549.31 L 1704.39 549.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="327">
   <path class="kv110" d="M 1077.5 239.47 L 1077.5 266.65" stroke-width="1" zvalue="375"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118" LinkObjectIDznd="116@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1077.5 239.47 L 1077.5 266.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="328">
   <path class="kv110" d="M 1046.17 252.78 L 1077.5 252.78" stroke-width="1" zvalue="376"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="327" MaxPinNum="2"/>
   </metadata>
  <path d="M 1046.17 252.78 L 1077.5 252.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="v6300" d="M 1426 687 L 1426 661" stroke-width="1" zvalue="485"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@3" LinkObjectIDznd="23@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426 687 L 1426 661" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="v6300" d="M 1426 624.5 L 1426 594.85" stroke-width="1" zvalue="486"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="23@0" LinkObjectIDznd="347@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426 624.5 L 1426 594.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="55">
   <use class="kv110" height="30" transform="rotate(0,714.682,130.472) scale(6.34921,1.48148) translate(-583.397,-35.1812)" width="7" x="692.4598173330401" xlink:href="#ACLineSegment:线路_0" y="108.2499999999999" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249331662850" ObjectName="110kV勐平线"/>
   <cge:TPSR_Ref TObjectID="8444249331662850_5066549684338689"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,714.682,130.472) scale(6.34921,1.48148) translate(-583.397,-35.1812)" width="7" x="692.4598173330401" y="108.2499999999999"/></g>
  <g id="76">
   <use class="kv110" height="30" transform="rotate(0,830.932,129.222) scale(6.34921,1.48148) translate(-681.338,-34.775)" width="7" x="808.7098173330401" xlink:href="#ACLineSegment:线路_0" y="106.9999999999999" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249302499333" ObjectName="110kV勐二一线"/>
   <cge:TPSR_Ref TObjectID="8444249302499333_5066549684338689"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,830.932,129.222) scale(6.34921,1.48148) translate(-681.338,-34.775)" width="7" x="808.7098173330401" y="106.9999999999999"/></g>
  <g id="97">
   <use class="kv110" height="30" transform="rotate(0,945.432,130.472) scale(-6.34921,1.48148) translate(-1075.62,-35.1812)" width="7" x="923.2098173330401" xlink:href="#ACLineSegment:线路_0" y="108.2499999999999" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249307938822" ObjectName="110kV勐五一线勐典河一级侧"/>
   <cge:TPSR_Ref TObjectID="8444249307938822_5066549684338689"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,945.432,130.472) scale(-6.34921,1.48148) translate(-1075.62,-35.1812)" width="7" x="923.2098173330401" y="108.2499999999999"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="53">
   <use class="kv110" height="20" transform="rotate(90,666.361,268.472) scale(1.11111,1.11111) translate(-66.0806,-25.7361)" width="10" x="660.8055556615193" xlink:href="#GroundDisconnector:地刀_0" y="257.3611111111111" zvalue="54"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454782025730" ObjectName="110kV勐平线15117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454782025730"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,666.361,268.472) scale(1.11111,1.11111) translate(-66.0806,-25.7361)" width="10" x="660.8055556615193" y="257.3611111111111"/></g>
  <g id="52">
   <use class="kv110" height="20" transform="rotate(90,666.361,216.028) scale(1.11111,1.11111) translate(-66.0806,-20.4917)" width="10" x="660.8055556615193" xlink:href="#GroundDisconnector:地刀_0" y="204.9166666666667" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454781894658" ObjectName="110kV勐平线15160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454781894658"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,666.361,216.028) scale(1.11111,1.11111) translate(-66.0806,-20.4917)" width="10" x="660.8055556615193" y="204.9166666666667"/></g>
  <g id="51">
   <use class="kv110" height="20" transform="rotate(90,666.361,161.583) scale(1.11111,1.11111) translate(-66.0806,-15.0472)" width="10" x="660.8055555555555" xlink:href="#GroundDisconnector:地刀_0" y="150.4722222222222" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454781763586" ObjectName="110kV勐平线15167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454781763586"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,666.361,161.583) scale(1.11111,1.11111) translate(-66.0806,-15.0472)" width="10" x="660.8055555555555" y="150.4722222222222"/></g>
  <g id="74">
   <use class="kv110" height="20" transform="rotate(90,782.611,267.222) scale(1.11111,1.11111) translate(-77.7056,-25.6111)" width="10" x="777.0555556615193" xlink:href="#GroundDisconnector:地刀_0" y="256.1111111111111" zvalue="76"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454782615554" ObjectName="110kV勐二一线15217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454782615554"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,782.611,267.222) scale(1.11111,1.11111) translate(-77.7056,-25.6111)" width="10" x="777.0555556615193" y="256.1111111111111"/></g>
  <g id="73">
   <use class="kv110" height="20" transform="rotate(90,782.611,214.778) scale(1.11111,1.11111) translate(-77.7056,-20.3667)" width="10" x="777.0555556615193" xlink:href="#GroundDisconnector:地刀_0" y="203.6666666666667" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454782484482" ObjectName="110kV勐二一线15260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454782484482"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,782.611,214.778) scale(1.11111,1.11111) translate(-77.7056,-20.3667)" width="10" x="777.0555556615193" y="203.6666666666667"/></g>
  <g id="72">
   <use class="kv110" height="20" transform="rotate(90,782.611,160.333) scale(1.11111,1.11111) translate(-77.7056,-14.9222)" width="10" x="777.0555555555555" xlink:href="#GroundDisconnector:地刀_0" y="149.2222222222222" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454782353410" ObjectName="110kV勐二一线15267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454782353410"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,782.611,160.333) scale(1.11111,1.11111) translate(-77.7056,-14.9222)" width="10" x="777.0555555555555" y="149.2222222222222"/></g>
  <g id="95">
   <use class="kv110" height="20" transform="rotate(90,897.111,268.472) scale(1.11111,1.11111) translate(-89.1556,-25.7361)" width="10" x="891.5555556615193" xlink:href="#GroundDisconnector:地刀_0" y="257.3611111111111" zvalue="98"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454783205378" ObjectName="110kV勐五一线15317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454783205378"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,897.111,268.472) scale(1.11111,1.11111) translate(-89.1556,-25.7361)" width="10" x="891.5555556615193" y="257.3611111111111"/></g>
  <g id="94">
   <use class="kv110" height="20" transform="rotate(90,897.111,216.028) scale(1.11111,1.11111) translate(-89.1556,-20.4917)" width="10" x="891.5555556615193" xlink:href="#GroundDisconnector:地刀_0" y="204.9166666666667" zvalue="100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454783074306" ObjectName="110kV勐五一线15360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454783074306"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,897.111,216.028) scale(1.11111,1.11111) translate(-89.1556,-20.4917)" width="10" x="891.5555556615193" y="204.9166666666667"/></g>
  <g id="93">
   <use class="kv110" height="20" transform="rotate(90,897.111,161.583) scale(1.11111,1.11111) translate(-89.1556,-15.0472)" width="10" x="891.5555555555555" xlink:href="#GroundDisconnector:地刀_0" y="150.4722222222222" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454782943234" ObjectName="110kV勐五一线15367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454782943234"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,897.111,161.583) scale(1.11111,1.11111) translate(-89.1556,-15.0472)" width="10" x="891.5555555555555" y="150.4722222222222"/></g>
  <g id="115">
   <use class="kv110" height="20" transform="rotate(90,1035.33,252.722) scale(1.11111,1.11111) translate(-102.978,-24.1611)" width="10" x="1029.777784559462" xlink:href="#GroundDisconnector:地刀_0" y="241.6111008326212" zvalue="110"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454783729666" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454783729666"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1035.33,252.722) scale(1.11111,1.11111) translate(-102.978,-24.1611)" width="10" x="1029.777784559462" y="241.6111008326212"/></g>
  <g id="109">
   <use class="kv110" height="20" transform="rotate(90,1035.33,296.556) scale(1.11111,1.11111) translate(-102.978,-28.5444)" width="10" x="1029.777784559462" xlink:href="#GroundDisconnector:地刀_0" y="285.4444444444444" zvalue="118"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454783533058" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454783533058"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1035.33,296.556) scale(1.11111,1.11111) translate(-102.978,-28.5444)" width="10" x="1029.777784559462" y="285.4444444444444"/></g>
  <g id="141">
   <use class="kv35" height="20" transform="rotate(90,1407.11,155.333) scale(1.11111,1.11111) translate(-140.156,-14.4222)" width="10" x="1401.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="144.2222222222222" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454784385026" ObjectName="35kV光明硅厂Ⅲ回线35167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454784385026"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1407.11,155.333) scale(1.11111,1.11111) translate(-140.156,-14.4222)" width="10" x="1401.555555555556" y="144.2222222222222"/></g>
  <g id="143">
   <use class="kv35" height="20" transform="rotate(90,1522.11,159.083) scale(1.11111,1.11111) translate(-151.656,-14.7972)" width="10" x="1516.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="147.9722222222222" zvalue="155"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454784516098" ObjectName="35kV勐支线35267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454784516098"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1522.11,159.083) scale(1.11111,1.11111) translate(-151.656,-14.7972)" width="10" x="1516.555555555556" y="147.9722222222222"/></g>
  <g id="181">
   <use class="kv110" height="40" transform="rotate(0,926.778,544.667) scale(1.11111,-1.11111) translate(-90.4556,-1032.64)" width="40" x="904.5555555555555" xlink:href="#GroundDisconnector:中性点地刀12_0" y="522.4444444444445" zvalue="183"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454784974850" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454784974850"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,926.778,544.667) scale(1.11111,-1.11111) translate(-90.4556,-1032.64)" width="40" x="904.5555555555555" y="522.4444444444445"/></g>
  <g id="177">
   <use class="kv110" height="20" transform="rotate(90,752.194,383) scale(1.11111,1.11111) translate(-74.6639,-37.1889)" width="10" x="746.6388990614149" xlink:href="#GroundDisconnector:地刀_0" y="371.8888888888889" zvalue="188"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454784843778" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454784843778"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,752.194,383) scale(1.11111,1.11111) translate(-74.6639,-37.1889)" width="10" x="746.6388990614149" y="371.8888888888889"/></g>
  <g id="338">
   <use class="kv110" height="20" transform="rotate(270,1469.56,428) scale(-1.11111,1.11111) translate(-2791.61,-41.6889)" width="10" x="1464.005962416492" xlink:href="#GroundDisconnector:地刀_0" y="416.8888888888889" zvalue="332"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454787596290" ObjectName="#2主变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454787596290"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1469.56,428) scale(-1.11111,1.11111) translate(-2791.61,-41.6889)" width="10" x="1464.005962416492" y="416.8888888888889"/></g>
  <g id="312">
   <use class="kv110" height="40" transform="rotate(0,1306.12,559) scale(0.7,-0.875) translate(553.764,-1200.36)" width="40" x="1292.117063355077" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="541.5" zvalue="338"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454787465218" ObjectName="#2主变110kV侧1020中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454787465218"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1306.12,559) scale(0.7,-0.875) translate(553.764,-1200.36)" width="40" x="1292.117063355077" y="541.5"/></g>
  <g id="321">
   <use class="kv35" height="20" transform="rotate(270,1774.56,549.25) scale(-1.11111,1.11111) translate(-3371.11,-53.8139)" width="10" x="1769.005962416492" xlink:href="#GroundDisconnector:地刀_0" y="538.1388888888889" zvalue="369"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454787792898" ObjectName="#2主变35kV侧30267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454787792898"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1774.56,549.25) scale(-1.11111,1.11111) translate(-3371.11,-53.8139)" width="10" x="1769.005962416492" y="538.1388888888889"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="110">
   <use class="kv110" height="30" transform="rotate(0,1077.24,217.25) scale(1.73333,-1.51667) translate(-444.756,-352.742)" width="30" x="1051.24092841766" xlink:href="#Accessory:PT789_0" y="194.5" zvalue="116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454783598594" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1077.24,217.25) scale(1.73333,-1.51667) translate(-444.756,-352.742)" width="30" x="1051.24092841766" y="194.5"/></g>
  <g id="117">
   <use class="kv110" height="26" transform="rotate(180,1114.5,207) scale(1,1) translate(0,0)" width="12" x="1108.5" xlink:href="#Accessory:避雷器1_0" y="194" zvalue="122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454783860738" ObjectName="110kV母线电压互感器避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1114.5,207) scale(1,1) translate(0,0)" width="12" x="1108.5" y="194"/></g>
  <g id="842">
   <use class="kv35" height="30" transform="rotate(0,1701.83,210.25) scale(1.73333,-1.51667) translate(-709.004,-341.126)" width="30" x="1675.828440310165" xlink:href="#Accessory:三卷PT带容断器_0" y="187.5" zvalue="167"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454784581634" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1701.83,210.25) scale(1.73333,-1.51667) translate(-709.004,-341.126)" width="30" x="1675.828440310165" y="187.5"/></g>
  <g id="157">
   <use class="kv35" height="26" transform="rotate(180,1739.5,209.5) scale(1,1) translate(0,0)" width="12" x="1733.5" xlink:href="#Accessory:避雷器1_0" y="196.5" zvalue="173"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454784712706" ObjectName="35kV母线电压互感器避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1739.5,209.5) scale(1,1) translate(0,0)" width="12" x="1733.5" y="196.5"/></g>
  <g id="183">
   <use class="kv10" height="30" transform="rotate(270,530.241,542.25) scale(1.73333,-1.51667) translate(-213.333,-892.027)" width="30" x="504.2409284176601" xlink:href="#Accessory:PT789_0" y="519.5" zvalue="210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454785302530" ObjectName="近区变电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,530.241,542.25) scale(1.73333,-1.51667) translate(-213.333,-892.027)" width="30" x="504.2409284176601" y="519.5"/></g>
  <g id="209">
   <use class="v6300" height="30" transform="rotate(0,546.641,848.176) scale(1.2284,1.14858) translate(-98.2113,-107.489)" width="30" x="528.2152659679655" xlink:href="#Accessory:PT789_0" y="830.9471748335609" zvalue="236"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454785564674" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,546.641,848.176) scale(1.2284,1.14858) translate(-98.2113,-107.489)" width="30" x="528.2152659679655" y="830.9471748335609"/></g>
  <g id="208">
   <use class="v6300" height="30" transform="rotate(0,716.868,855.067) scale(1.2284,-1.53143) translate(-129.862,-1405.44)" width="30" x="698.4417638916361" xlink:href="#Accessory:RT1122_0" y="832.0957508748792" zvalue="237"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454785499138" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,716.868,855.067) scale(1.2284,-1.53143) translate(-129.862,-1405.44)" width="30" x="698.4417638916361" y="832.0957508748792"/></g>
  <g id="207">
   <use class="v6300" height="29" transform="rotate(0,684.929,858.513) scale(1.2284,-1.14858) translate(-123.923,-1603.82)" width="30" x="666.5034605497326" xlink:href="#Accessory:PT12321_0" y="841.8586472260756" zvalue="238"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454785433602" ObjectName="#1发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,684.929,858.513) scale(1.2284,-1.14858) translate(-123.923,-1603.82)" width="30" x="666.5034605497326" y="841.8586472260756"/></g>
  <g id="225">
   <use class="v6300" height="30" transform="rotate(0,961.868,855.067) scale(1.2284,-1.53143) translate(-175.415,-1405.44)" width="30" x="943.4417638916361" xlink:href="#Accessory:RT1122_0" y="832.0957508748792" zvalue="262"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454786023426" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,961.868,855.067) scale(1.2284,-1.53143) translate(-175.415,-1405.44)" width="30" x="943.4417638916361" y="832.0957508748792"/></g>
  <g id="224">
   <use class="v6300" height="29" transform="rotate(0,929.929,858.513) scale(1.2284,-1.14858) translate(-169.476,-1603.82)" width="30" x="911.5034605497326" xlink:href="#Accessory:PT12321_0" y="841.8586472260756" zvalue="263"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454785957890" ObjectName="#2发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,929.929,858.513) scale(1.2284,-1.14858) translate(-169.476,-1603.82)" width="30" x="911.5034605497326" y="841.8586472260756"/></g>
  <g id="234">
   <use class="v6300" height="30" transform="rotate(0,791.641,848.176) scale(1.2284,1.14858) translate(-143.764,-107.489)" width="30" x="773.2152659679656" xlink:href="#Accessory:PT789_0" y="830.9471748335609" zvalue="272"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454786285570" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,791.641,848.176) scale(1.2284,1.14858) translate(-143.764,-107.489)" width="30" x="773.2152659679656" y="830.9471748335609"/></g>
  <g id="242">
   <use class="v6300" height="30" transform="rotate(0,1136.64,800.176) scale(1.2284,1.14858) translate(-207.91,-101.279)" width="30" x="1118.215265967965" xlink:href="#Accessory:PT789_0" y="782.9471748335609" zvalue="284"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454786416642" ObjectName="6.3kVⅠ母电压互感器互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1136.64,800.176) scale(1.2284,1.14858) translate(-207.91,-101.279)" width="30" x="1118.215265967965" y="782.9471748335609"/></g>
  <g id="263">
   <use class="v6300" height="30" transform="rotate(0,1279.64,848.176) scale(1.2284,1.14858) translate(-234.498,-107.489)" width="30" x="1261.215265967965" xlink:href="#Accessory:PT789_0" y="830.9471748335609" zvalue="301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454786744322" ObjectName="#3发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1279.64,848.176) scale(1.2284,1.14858) translate(-234.498,-107.489)" width="30" x="1261.215265967965" y="830.9471748335609"/></g>
  <g id="262">
   <use class="v6300" height="30" transform="rotate(0,1449.87,855.067) scale(1.2284,-1.53143) translate(-266.149,-1405.44)" width="30" x="1431.441763891636" xlink:href="#Accessory:RT1122_0" y="832.0957508748792" zvalue="302"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454786678786" ObjectName="#3发电机PT2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1449.87,855.067) scale(1.2284,-1.53143) translate(-266.149,-1405.44)" width="30" x="1431.441763891636" y="832.0957508748792"/></g>
  <g id="261">
   <use class="v6300" height="29" transform="rotate(0,1417.93,858.513) scale(1.2284,-1.14858) translate(-260.21,-1603.82)" width="30" x="1399.503460549733" xlink:href="#Accessory:PT12321_0" y="841.8586472260756" zvalue="303"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454786613250" ObjectName="#3发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1417.93,858.513) scale(1.2284,-1.14858) translate(-260.21,-1603.82)" width="30" x="1399.503460549733" y="841.8586472260756"/></g>
  <g id="281">
   <use class="v6300" height="30" transform="rotate(0,1689.64,800.176) scale(1.2284,1.14858) translate(-310.73,-101.279)" width="30" x="1671.215265967965" xlink:href="#Accessory:PT789_0" y="782.9471748335609" zvalue="322"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454787072002" ObjectName="6.3kVⅡ母电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1689.64,800.176) scale(1.2284,1.14858) translate(-310.73,-101.279)" width="30" x="1671.215265967965" y="782.9471748335609"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="166">
   <g id="1660">
    <use class="kv110" height="50" transform="rotate(0,801.75,520.081) scale(1.99167,1.96323) translate(-384.323,-231.089)" width="30" x="771.88" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="471" zvalue="194"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874597990402" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1661">
    <use class="v6300" height="50" transform="rotate(0,801.75,520.081) scale(1.99167,1.96323) translate(-384.323,-231.089)" width="30" x="771.88" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="471" zvalue="194"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874423468036" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399533264898" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399533264898"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,801.75,520.081) scale(1.99167,1.96323) translate(-384.323,-231.089)" width="30" x="771.88" y="471"/></g>
  <g id="179">
   <g id="1790">
    <use class="kv10" height="50" transform="rotate(0,640.223,582.49) scale(0.808333,0.726843) translate(148.931,212.078)" width="30" x="628.1" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="564.3200000000001" zvalue="203"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874423533572" ObjectName="10"/>
    </metadata>
   </g>
   <g id="1791">
    <use class="v6300" height="50" transform="rotate(0,640.223,582.49) scale(0.808333,0.726843) translate(148.931,212.078)" width="30" x="628.1" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="564.3200000000001" zvalue="203"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874423599108" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399533330434" ObjectName="近区变"/>
   <cge:TPSR_Ref TObjectID="6755399533330434"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,640.223,582.49) scale(0.808333,0.726843) translate(148.931,212.078)" width="30" x="628.1" y="564.3200000000001"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="187">
   <use class="kv10" height="30" transform="rotate(0,640.458,391.5) scale(1.35417,0.625) translate(-165.379,229.275)" width="12" x="632.3333333333335" xlink:href="#EnergyConsumer:负荷_0" y="382.125" zvalue="211"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454785368066" ObjectName="10kV近区"/>
   <cge:TPSR_Ref TObjectID="6192454785368066"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,640.458,391.5) scale(1.35417,0.625) translate(-165.379,229.275)" width="12" x="632.3333333333335" y="382.125"/></g>
  <g id="237">
   <use class="v6300" height="30" transform="rotate(0,1038.5,809.5) scale(2.1,2.1) translate(-532.976,-407.524)" width="20" x="1017.5" xlink:href="#EnergyConsumer:站用变11_0" y="778" zvalue="278"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454786351106" ObjectName="#1厂用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1038.5,809.5) scale(2.1,2.1) translate(-532.976,-407.524)" width="20" x="1017.5" y="778"/></g>
  <g id="275">
   <use class="v6300" height="30" transform="rotate(0,1558.07,839.326) scale(2.6113,2.6113) translate(-945.296,-493.736)" width="20" x="1531.960869565218" xlink:href="#EnergyConsumer:站用变11_0" y="800.1565217391308" zvalue="316"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454787006466" ObjectName="#2厂用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1558.07,839.326) scale(2.6113,2.6113) translate(-945.296,-493.736)" width="20" x="1531.960869565218" y="800.1565217391308"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v6300" height="30" transform="rotate(0,623.03,844.73) scale(1.84259,1.72286) translate(-272.264,-343.581)" width="30" x="595.3906532317139" xlink:href="#Generator:发电机_0" y="818.8871263997291" zvalue="226"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454785761282" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192454785761282"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,623.03,844.73) scale(1.84259,1.72286) translate(-272.264,-343.581)" width="30" x="595.3906532317139" y="818.8871263997291"/></g>
  <g id="230">
   <use class="v6300" height="30" transform="rotate(0,868.03,844.73) scale(1.84259,1.72286) translate(-384.3,-343.581)" width="30" x="840.3906532317141" xlink:href="#Generator:发电机_0" y="818.8871263997291" zvalue="254"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454786220034" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192454786220034"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,868.03,844.73) scale(1.84259,1.72286) translate(-384.3,-343.581)" width="30" x="840.3906532317141" y="818.8871263997291"/></g>
  <g id="268">
   <use class="v6300" height="30" transform="rotate(0,1356.03,844.73) scale(1.84259,1.72286) translate(-607.456,-343.581)" width="30" x="1328.390653231714" xlink:href="#Generator:发电机_0" y="818.8871263997291" zvalue="293"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454786940930" ObjectName="#3发电机"/>
   <cge:TPSR_Ref TObjectID="6192454786940930"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1356.03,844.73) scale(1.84259,1.72286) translate(-607.456,-343.581)" width="30" x="1328.390653231714" y="818.8871263997291"/></g>
 </g>
 <g id="PowerTransformer3Class">
  <g id="347">
   <g id="3470">
    <use class="kv110" height="50" transform="rotate(0,1444,550) scale(1.8,1.8) translate(-621.778,-224.444)" width="50" x="1399" xlink:href="#PowerTransformer3:主变高压侧有中性点_0" y="505" zvalue="344"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874592223234" ObjectName="110"/>
    </metadata>
   </g>
   <g id="3471">
    <use class="kv35" height="50" transform="rotate(0,1444,550) scale(1.8,1.8) translate(-621.778,-224.444)" width="50" x="1399" xlink:href="#PowerTransformer3:主变高压侧有中性点_1" y="505" zvalue="344"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874592288770" ObjectName="35"/>
    </metadata>
   </g>
   <g id="3472">
    <use class="v6300" height="50" transform="rotate(0,1444,550) scale(1.8,1.8) translate(-621.778,-224.444)" width="50" x="1399" xlink:href="#PowerTransformer3:主变高压侧有中性点_2" y="505" zvalue="344"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874423664644" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399533395970" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399533395970"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1444,550) scale(1.8,1.8) translate(-621.778,-224.444)" width="50" x="1399" y="505"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="112">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="112" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,701.682,39.125) scale(1,1) translate(0,0)" writing-mode="lr" x="701.21" xml:space="preserve" y="43.77" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136366288898" ObjectName="P"/>
   </metadata>
  </g>
  <g id="113">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="113" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,701.682,55.125) scale(1,1) translate(0,0)" writing-mode="lr" x="701.21" xml:space="preserve" y="59.77" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136366354434" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="220">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="220" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,701.682,71.125) scale(1,1) translate(0,0)" writing-mode="lr" x="701.21" xml:space="preserve" y="75.77" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136366419970" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="272">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="272" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,818.932,41) scale(1,1) translate(0,0)" writing-mode="lr" x="818.46" xml:space="preserve" y="45.66" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136367861762" ObjectName="P"/>
   </metadata>
  </g>
  <g id="289">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="289" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,818.932,57.5) scale(1,1) translate(0,0)" writing-mode="lr" x="818.46" xml:space="preserve" y="62.15" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136367927298" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="291">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="291" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,818.932,74) scale(1,1) translate(0,0)" writing-mode="lr" x="818.46" xml:space="preserve" y="78.64" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136367992834" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="295">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="295" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,930.432,47.3472) scale(1,1) translate(0,-8.36985e-15)" writing-mode="lr" x="929.96" xml:space="preserve" y="52" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136369434626" ObjectName="P"/>
   </metadata>
  </g>
  <g id="296">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="296" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,930.432,63.3472) scale(1,1) translate(0,-1.15895e-14)" writing-mode="lr" x="929.96" xml:space="preserve" y="68.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136369500162" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="298">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="298" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,930.432,78.3472) scale(1,1) translate(0,-1.48091e-14)" writing-mode="lr" x="929.96" xml:space="preserve" y="83.03" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136369565698" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="299">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="299" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1455.93,33.5) scale(1,1) translate(0,5.10703e-15)" writing-mode="lr" x="1455.46" xml:space="preserve" y="38.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136371531778" ObjectName="P"/>
   </metadata>
  </g>
  <g id="300">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="300" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1455.93,49) scale(1,1) translate(0,-8.32667e-15)" writing-mode="lr" x="1455.46" xml:space="preserve" y="53.68" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136371597314" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="302">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="302" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1455.93,64) scale(1,1) translate(0,5.77316e-15)" writing-mode="lr" x="1455.46" xml:space="preserve" y="68.69" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136371662850" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="307">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="307" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1559.34,36.6591) scale(1,1) translate(0,2.82098e-15)" writing-mode="lr" x="1558.87" xml:space="preserve" y="41.34" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136373104642" ObjectName="P"/>
   </metadata>
  </g>
  <g id="308">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="308" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1559.34,52.1591) scale(1,1) translate(0,9.30569e-15)" writing-mode="lr" x="1558.87" xml:space="preserve" y="56.82" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136373170178" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="309">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="309" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1559.34,68.6591) scale(1,1) translate(0,1.29694e-14)" writing-mode="lr" x="1558.87" xml:space="preserve" y="73.31999999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136373235714" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="330">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="330" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,889.625,385.5) scale(1,1) translate(0,0)" writing-mode="lr" x="889.0700000000001" xml:space="preserve" y="390.18" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136812130305" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="331">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="331" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,889.625,403) scale(1,1) translate(0,0)" writing-mode="lr" x="889.0700000000001" xml:space="preserve" y="407.66" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136812195841" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="332">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="332" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,891.625,622.081) scale(1,1) translate(0,6.78525e-14)" writing-mode="lr" x="891.0700000000001" xml:space="preserve" y="626.75" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124356096004" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="333">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="333" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,891.625,639.581) scale(1,1) translate(0,6.97954e-14)" writing-mode="lr" x="891.0700000000001" xml:space="preserve" y="644.25" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124356161540" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="334">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="334" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,889.625,425.5) scale(1,1) translate(0,0)" writing-mode="lr" x="889.0700000000001" xml:space="preserve" y="430.21" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136812261377" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="335">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="335" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,891.625,659.081) scale(1,1) translate(0,7.17383e-14)" writing-mode="lr" x="891.0700000000001" xml:space="preserve" y="663.79" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124356227076" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="336">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="336" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1370,440) scale(1,1) translate(0,0)" writing-mode="lr" x="1369.53" xml:space="preserve" y="444.72" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136388702210" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="337">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="337" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1370,457.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1369.53" xml:space="preserve" y="462.21" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136388767746" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="339">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="339" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1370,475.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1369.53" xml:space="preserve" y="480.2" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136388833282" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="343">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="343" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1364,648) scale(1,1) translate(0,0)" writing-mode="lr" x="1363.53" xml:space="preserve" y="652.6900000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124366319620" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="344">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="344" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1638,441.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1637.53" xml:space="preserve" y="445.94" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136389160962" ObjectName="MP"/>
   </metadata>
  </g>
  <g id="345">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="345" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1638,461.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1637.53" xml:space="preserve" y="465.94" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136389226498" ObjectName="MQ"/>
   </metadata>
  </g>
  <g id="346">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="346" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1364,610.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1363.53" xml:space="preserve" y="615.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124366450692" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="348">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="348" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1364,630) scale(1,1) translate(0,0)" writing-mode="lr" x="1363.53" xml:space="preserve" y="634.6900000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124366516228" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="349">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="349" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1638,479.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1637.53" xml:space="preserve" y="484.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136389488642" ObjectName="MIa"/>
   </metadata>
  </g>
  <g id="351">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="351" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,613.03,949.037) scale(1,1) translate(0,1.03925e-13)" writing-mode="lr" x="612.48" xml:space="preserve" y="953.74" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136381886466" ObjectName="P"/>
   </metadata>
  </g>
  <g id="352">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="352" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,858.03,952.323) scale(1,1) translate(0,1.0429e-13)" writing-mode="lr" x="857.48" xml:space="preserve" y="957.03" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136383066114" ObjectName="P"/>
   </metadata>
  </g>
  <g id="353">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="353" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1354.03,953.073) scale(1,1) translate(0,1.04373e-13)" writing-mode="lr" x="1353.48" xml:space="preserve" y="957.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136385884162" ObjectName="P"/>
   </metadata>
  </g>
  <g id="354">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="354" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,613.03,969.323) scale(1,1) translate(0,1.06177e-13)" writing-mode="lr" x="612.48" xml:space="preserve" y="974.03" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136381952002" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="355">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="355" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,858.03,968.073) scale(1,1) translate(0,1.06038e-13)" writing-mode="lr" x="857.48" xml:space="preserve" y="972.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136383131650" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="356">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="356" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1354.03,972.823) scale(1,1) translate(0,1.06566e-13)" writing-mode="lr" x="1353.48" xml:space="preserve" y="977.53" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136385949698" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="357">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="357" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,613.03,989.073) scale(1,1) translate(0,1.0837e-13)" writing-mode="lr" x="612.48" xml:space="preserve" y="993.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136382017538" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="358">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="358" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,858.03,987.823) scale(1,1) translate(0,1.08231e-13)" writing-mode="lr" x="857.48" xml:space="preserve" y="992.53" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136383197186" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="359">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="359" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1354.03,992.573) scale(1,1) translate(0,1.08759e-13)" writing-mode="lr" x="1353.48" xml:space="preserve" y="997.28" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136386015234" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="377">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="377" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136366157826" ObjectName="F"/>
   </metadata>
  </g>
  <g id="376">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="376" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127236730884" ObjectName="勐典河一级电站上网有功"/>
   </metadata>
  </g>
  <g id="375">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="375" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136397549570" ObjectName="F"/>
   </metadata>
  </g>
  <g id="374">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="374" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,336.111,334.056) scale(1,1) translate(0,0)" writing-mode="lr" x="336.27" xml:space="preserve" y="338.97" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136371400706" ObjectName="F"/>
   </metadata>
  </g>
  <g id="368">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="368" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136397352962" ObjectName="F"/>
   </metadata>
  </g>
  <g id="367">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="367" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136397418498" ObjectName="F"/>
   </metadata>
  </g>
  <g id="360">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="360" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127180500997" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127180435461" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.972,353.917) scale(1,1) translate(0,0)" writing-mode="lr" x="160.13" xml:space="preserve" y="358.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136375070722" ObjectName="F"/>
   </metadata>
  </g>
  <g id="396">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="396" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,339.972,353.917) scale(1,1) translate(0,0)" writing-mode="lr" x="340.13" xml:space="preserve" y="358.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136385753090" ObjectName="F"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1086,81.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1085.53" xml:space="preserve" y="86.53" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136365764610" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1710,85.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1709.53" xml:space="preserve" y="90.53" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136371007490" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="5" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1147,854.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1146.53" xml:space="preserve" y="859.03" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136374677506" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="6" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1684,843.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1683.53" xml:space="preserve" y="848.28" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136385359874" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="7" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1086,110.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1085.53" xml:space="preserve" y="115.53" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136365830146" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="11" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1710,110.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1709.53" xml:space="preserve" y="115.53" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136371073026" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="12" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1147,883.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1146.53" xml:space="preserve" y="888.03" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136374743042" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="13" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1684,868.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1683.53" xml:space="preserve" y="873.28" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136385425410" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="14" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1086,139.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1085.53" xml:space="preserve" y="144.53" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136365895682" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="15" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1710,135.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1709.53" xml:space="preserve" y="140.53" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136371138562" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="16" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1147,908.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1146.53" xml:space="preserve" y="913.03" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136374808578" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="17" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1684,889.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1683.53" xml:space="preserve" y="894.28" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136385490946" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="18" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,566,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="565.53" xml:space="preserve" y="340.03" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136366026754" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1394,341.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1393.53" xml:space="preserve" y="346.03" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136371269634" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="20" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,485,709.75) scale(1,1) translate(0,0)" writing-mode="lr" x="484.53" xml:space="preserve" y="714.53" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136374939650" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="21" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1286,705) scale(1,1) translate(0,0)" writing-mode="lr" x="1285.53" xml:space="preserve" y="709.78" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136385622018" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="322">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="322" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,572.958,392.375) scale(1,1) translate(0,-8.49814e-14)" writing-mode="lr" x="529.83" xml:space="preserve" y="396.8" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136380837890" ObjectName="P"/>
   </metadata>
  </g>
  <g id="350">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="350" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,571.744,417.375) scale(1,1) translate(0,-9.05325e-14)" writing-mode="lr" x="528.62" xml:space="preserve" y="421.8" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136380903426" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="365">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="365" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,571.744,438.375) scale(1,1) translate(0,-9.51955e-14)" writing-mode="lr" x="528.62" xml:space="preserve" y="442.8" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136380968962" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="373">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="442"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374928510977" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="2">
   <use height="30" transform="rotate(0,237.625,187.983) scale(0.708333,0.665547) translate(93.4706,89.4493)" width="30" x="227" xlink:href="#State:红绿圆(方形)_0" y="178" zvalue="465"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,237.625,187.983) scale(0.708333,0.665547) translate(93.4706,89.4493)" width="30" x="227" y="178"/></g>
 </g>
</svg>