<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549595078658" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变0716_0" viewBox="0,0,20,25">
   <use terminal-index="0" type="0" x="8" xlink:href="#terminal" y="1.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="8" y1="2.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.25" x2="15.75" y1="20.5" y2="15.15"/>
   <ellipse cx="8.130000000000001" cy="6.97" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.19" cy="12.96" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.259517426273471" x2="8.259517426273471" y1="11.6368007916835" y2="13.30855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.939588918677396" x2="8.259517426273467" y1="14.98031840279781" y2="13.30855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.579445933869528" x2="8.259517426273458" y1="14.98031840279781" y2="13.30855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85" x2="13.64982126899018" y1="18.56619780557639" y2="18.56619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.00996425379804" x2="14.48985701519214" y1="19.40207720835499" y2="19.40207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.16992850759607" x2="15.32989276139411" y1="20.23795661113357" y2="20.23795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.259517426273472" x2="15.75" y1="13.30855959724066" y2="13.30855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.74991063449509" x2="15.74991063449509" y1="13.33333333333333" y2="18.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.353115032679748" x2="8.353115032679748" y1="22.5" y2="17.14357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.259517426273471" x2="8.259517426273471" y1="4.3868007916835" y2="6.058559597240661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.939588918677396" x2="8.259517426273467" y1="7.730318402797811" y2="6.05855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.579445933869528" x2="8.259517426273458" y1="7.730318402797811" y2="6.05855959724065"/>
  </symbol>
  <symbol id="Accessory:PT带保险_0" viewBox="0,0,11,29">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="0.1666666666666661"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.5,7.5) scale(1,1) translate(0,0)" width="4" x="3.5" y="4.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="13.16666666666667" y2="0.5"/>
   <ellipse cx="5.4" cy="18.1" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.4" cy="23.78" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="6.3kV马鹿塘电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="43" xlink:href="logo.png" y="46.67"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,192,76.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="192" xml:space="preserve" y="80.17" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,188.5,76.357) scale(1,1) translate(0,0)" writing-mode="lr" x="188.5" xml:space="preserve" y="85.36" zvalue="3">6.3kV马鹿塘电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="216" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="642"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="642">信号一览</text>
  <line fill="none" id="32" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="385" x2="385" y1="14.66666666666663" y2="1044.666666666667" zvalue="4"/>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00000000000023" x2="377.9999999999998" y1="150.5371592807491" y2="150.5371592807491" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="352" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="616"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="342" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="618">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="340" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="619">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="319" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="620">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="312" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="621">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="306" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="622">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="298" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="624">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="625">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="626">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.054,956) scale(1,1) translate(0,0)" writing-mode="lr" x="237.05" xml:space="preserve" y="962" zvalue="627">MaLuTang-01-2023</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="266" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="630">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="261" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="631">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="632">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="235" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,359.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="363.75" zvalue="634">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="215" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="643">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="214" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="644">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="212" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="647">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="649">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="191" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="651">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="653">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="655">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="657">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,867.071,135.857) scale(1,1) translate(1.8398e-13,0)" writing-mode="lr" x="867.0700000000001" xml:space="preserve" y="140.36" zvalue="663">6.3kV邦马线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,892.714,417) scale(1,1) translate(0,0)" writing-mode="lr" x="892.71" xml:space="preserve" y="421.5" zvalue="669">631</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,835.143,488.429) scale(1,1) translate(0,0)" writing-mode="lr" x="835.14" xml:space="preserve" y="492.93" zvalue="676">6311</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,801.429,817.143) scale(1,1) translate(0,0)" writing-mode="lr" x="801.4299999999999" xml:space="preserve" y="821.64" zvalue="679">站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,833.429,604.143) scale(1,1) translate(0,0)" writing-mode="lr" x="833.4299999999999" xml:space="preserve" y="608.64" zvalue="682">6031</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" x="1061.421875" xml:space="preserve" y="865.3191976547241" zvalue="685">1号发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1061.421875" xml:space="preserve" y="881.3191976547241" zvalue="685">800kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="218" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1088.43,669.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1088.43" xml:space="preserve" y="674.36" zvalue="688">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1032.13,604.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1032.13" xml:space="preserve" y="608.64" zvalue="689">6011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="486" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1093.57,749.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1093.57" xml:space="preserve" y="754.36" zvalue="700">6911</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="523" stroke="rgb(255,255,255)" text-anchor="middle" x="1321.421875" xml:space="preserve" y="865.3191976547241" zvalue="704">2号发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="523" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1321.421875" xml:space="preserve" y="881.3191976547241" zvalue="704">800kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="522" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1348.43,669.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1348.43" xml:space="preserve" y="674.36" zvalue="706">602</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="521" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1292.13,604.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1292.13" xml:space="preserve" y="608.64" zvalue="709">6021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="520" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1353.57,749.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1353.57" xml:space="preserve" y="754.36" zvalue="716">6921</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,146.554,1016.86) scale(1,1) translate(0,0)" writing-mode="lr" x="146.55" xml:space="preserve" y="1022.86" zvalue="725">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,326.554,1016.86) scale(1,1) translate(0,0)" writing-mode="lr" x="326.55" xml:space="preserve" y="1022.86" zvalue="726">20230626</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="642"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="233">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="233" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="230">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="230" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127111753732" ObjectName="F"/>
   </metadata>
  </g>
  <g id="229">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="229" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127111819268" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127111622660" ObjectName="F"/>
   </metadata>
  </g>
  <g id="213">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="213" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127111688196" ObjectName="F"/>
   </metadata>
  </g>
  <g id="211">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="211" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="384.3" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="192">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="192" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="384.3" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="187">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="187" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,402.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="407.3" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127111622660" ObjectName="F"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127111622660" ObjectName="F"/>
   </metadata>
  </g>
  <g id="393">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="393" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,358.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="363.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127106117636" ObjectName="F"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="4" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,790.071,80.9286) scale(1,1) translate(0,0)" writing-mode="lr" x="789.6" xml:space="preserve" y="85.70999999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127106248710" ObjectName="P"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="6" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,788.071,112.929) scale(1,1) translate(0,0)" writing-mode="lr" x="787.6" xml:space="preserve" y="117.71" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127106314247" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="7" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,792.071,137.929) scale(1,1) translate(0,0)" writing-mode="lr" x="791.6" xml:space="preserve" y="142.71" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127106379780" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="217">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="640"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="641"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="1">
   <path class="v6300" d="M 755.71 544.57 L 1464.29 544.57" stroke-width="6" zvalue="661"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674258714628" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674258714628"/></metadata>
  <path d="M 755.71 544.57 L 1464.29 544.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="9">
   <use class="v6300" height="20" transform="rotate(0,867.143,418) scale(2.14286,1.92857) translate(-456.762,-191.974)" width="10" x="856.4285714285713" xlink:href="#Breaker:开关_0" y="398.7142857142857" zvalue="668"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924569858052" ObjectName="631"/>
   <cge:TPSR_Ref TObjectID="6473924569858052"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,867.143,418) scale(2.14286,1.92857) translate(-456.762,-191.974)" width="10" x="856.4285714285713" y="398.7142857142857"/></g>
  <g id="50">
   <use class="v6300" height="20" transform="rotate(0,1062.86,670.857) scale(2.14286,1.92857) translate(-561.143,-313.72)" width="10" x="1052.142857142857" xlink:href="#Breaker:开关_0" y="651.5714285714287" zvalue="687"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924569923588" ObjectName="601"/>
   <cge:TPSR_Ref TObjectID="6473924569923588"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1062.86,670.857) scale(2.14286,1.92857) translate(-561.143,-313.72)" width="10" x="1052.142857142857" y="651.5714285714287"/></g>
  <g id="534">
   <use class="v6300" height="20" transform="rotate(0,1322.86,670.857) scale(2.14286,1.92857) translate(-699.81,-313.72)" width="10" x="1312.142857142857" xlink:href="#Breaker:开关_0" y="651.5714285714287" zvalue="705"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924569989124" ObjectName="602"/>
   <cge:TPSR_Ref TObjectID="6473924569989124"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1322.86,670.857) scale(2.14286,1.92857) translate(-699.81,-313.72)" width="10" x="1312.142857142857" y="651.5714285714287"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="10">
   <path class="v6300" d="M 867.07 196.07 L 867.07 399.55" stroke-width="1" zvalue="669"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@0" LinkObjectIDznd="9@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 867.07 196.07 L 867.07 399.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="v6300" d="M 867.29 436.42 L 867.29 474.23" stroke-width="1" zvalue="676"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@1" LinkObjectIDznd="17@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 867.29 436.42 L 867.29 474.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="v6300" d="M 865.8 504.87 L 865.8 544.57" stroke-width="1" zvalue="677"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="17@1" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 865.8 504.87 L 865.8 544.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="v6300" d="M 801.71 746.54 L 801.71 620.34" stroke-width="1" zvalue="682"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@0" LinkObjectIDznd="24@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 801.71 746.54 L 801.71 620.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="v6300" d="M 802.94 589.7 L 802.94 544.57" stroke-width="1" zvalue="683"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@1" LinkObjectIDznd="1@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 802.94 589.7 L 802.94 544.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="v6300" d="M 1061.79 789.11 L 1061.79 689.28" stroke-width="1" zvalue="689"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="50@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1061.79 789.11 L 1061.79 689.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="394">
   <path class="v6300" d="M 1062.79 652.41 L 1062.79 620.59" stroke-width="1" zvalue="691"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="62@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.79 652.41 L 1062.79 620.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="395">
   <path class="v6300" d="M 1062.82 589.95 L 1062.82 544.57" stroke-width="1" zvalue="692"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="1@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.82 589.95 L 1062.82 544.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="462">
   <path class="v6300" d="M 1147 770.1 L 1147 752.29 L 1121.43 752.29" stroke-width="1" zvalue="697"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="436@0" LinkObjectIDznd="501" MaxPinNum="2"/>
   </metadata>
  <path d="M 1147 770.1 L 1147 752.29 L 1121.43 752.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="498">
   <path class="v6300" d="M 1061.79 720.73 L 1076.95 720.73" stroke-width="1" zvalue="700"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="228" LinkObjectIDznd="473@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1061.79 720.73 L 1076.95 720.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="501">
   <path class="v6300" d="M 1107.59 720.77 L 1121.43 720.77 L 1121.43 770.1" stroke-width="1" zvalue="701"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="473@1" LinkObjectIDznd="428@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1107.59 720.77 L 1121.43 720.77 L 1121.43 770.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="532">
   <path class="v6300" d="M 1321.79 789.11 L 1321.79 689.28" stroke-width="1" zvalue="708"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="535@0" LinkObjectIDznd="534@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1321.79 789.11 L 1321.79 689.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="531">
   <path class="v6300" d="M 1322.79 652.41 L 1322.79 620.59" stroke-width="1" zvalue="710"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="534@0" LinkObjectIDznd="533@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.79 652.41 L 1322.79 620.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="530">
   <path class="v6300" d="M 1322.82 589.95 L 1322.82 544.57" stroke-width="1" zvalue="711"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="533@0" LinkObjectIDznd="1@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.82 589.95 L 1322.82 544.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="527">
   <path class="v6300" d="M 1407 770.1 L 1407 752.29 L 1381.43 752.29" stroke-width="1" zvalue="714"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="528@0" LinkObjectIDznd="524" MaxPinNum="2"/>
   </metadata>
  <path d="M 1407 770.1 L 1407 752.29 L 1381.43 752.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="525">
   <path class="v6300" d="M 1321.79 720.73 L 1336.95 720.73" stroke-width="1" zvalue="717"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="532" LinkObjectIDznd="526@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1321.79 720.73 L 1336.95 720.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="524">
   <path class="v6300" d="M 1367.59 720.77 L 1381.43 720.77 L 1381.43 770.1" stroke-width="1" zvalue="718"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="526@1" LinkObjectIDznd="529@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1367.59 720.77 L 1381.43 720.77 L 1381.43 770.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="17">
   <use class="v6300" height="30" transform="rotate(0,865.714,489.429) scale(1.42857,1.04762) translate(-256.5,-21.5325)" width="15" x="855" xlink:href="#Disconnector:刀闸_0" y="473.7142857142857" zvalue="675"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450110685190" ObjectName="6311"/>
   <cge:TPSR_Ref TObjectID="6192450110685190"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,865.714,489.429) scale(1.42857,1.04762) translate(-256.5,-21.5325)" width="15" x="855" y="473.7142857142857"/></g>
  <g id="24">
   <use class="v6300" height="30" transform="rotate(0,802.857,605.143) scale(1.42857,-1.04762) translate(-237.643,-1182.06)" width="15" x="792.1428571428571" xlink:href="#Disconnector:刀闸_0" y="589.4285714285714" zvalue="681"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450110816261" ObjectName="6031"/>
   <cge:TPSR_Ref TObjectID="6192450110816261"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,802.857,605.143) scale(1.42857,-1.04762) translate(-237.643,-1182.06)" width="15" x="792.1428571428571" y="589.4285714285714"/></g>
  <g id="62">
   <use class="v6300" height="30" transform="rotate(0,1062.7,605.143) scale(1.42857,1.04762) translate(-315.595,-26.7922)" width="15" x="1051.984050822706" xlink:href="#Disconnector:刀闸_0" y="589.4285714285714" zvalue="688"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450110947333" ObjectName="6011"/>
   <cge:TPSR_Ref TObjectID="6192450110947333"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1062.7,605.143) scale(1.42857,1.04762) translate(-315.595,-26.7922)" width="15" x="1051.984050822706" y="589.4285714285714"/></g>
  <g id="473">
   <use class="v6300" height="30" transform="rotate(270,1092.14,720.857) scale(1.42857,1.04762) translate(-324.429,-32.0519)" width="15" x="1081.428571428571" xlink:href="#Disconnector:刀闸_0" y="705.1428571428571" zvalue="699"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450111143942" ObjectName="6911"/>
   <cge:TPSR_Ref TObjectID="6192450111143942"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1092.14,720.857) scale(1.42857,1.04762) translate(-324.429,-32.0519)" width="15" x="1081.428571428571" y="705.1428571428571"/></g>
  <g id="533">
   <use class="v6300" height="30" transform="rotate(0,1322.7,605.143) scale(1.42857,1.04762) translate(-393.595,-26.7922)" width="15" x="1311.984050822706" xlink:href="#Disconnector:刀闸_0" y="589.4285714285714" zvalue="707"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450111406085" ObjectName="6021"/>
   <cge:TPSR_Ref TObjectID="6192450111406085"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1322.7,605.143) scale(1.42857,1.04762) translate(-393.595,-26.7922)" width="15" x="1311.984050822706" y="589.4285714285714"/></g>
  <g id="526">
   <use class="v6300" height="30" transform="rotate(270,1352.14,720.857) scale(1.42857,1.04762) translate(-402.429,-32.0519)" width="15" x="1341.428571428572" xlink:href="#Disconnector:刀闸_0" y="705.1428571428571" zvalue="715"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450111209478" ObjectName="6921"/>
   <cge:TPSR_Ref TObjectID="6192450111209478"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1352.14,720.857) scale(1.42857,1.04762) translate(-402.429,-32.0519)" width="15" x="1341.428571428572" y="705.1428571428571"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="21">
   <use class="v6300" height="25" transform="rotate(0,806.429,772.464) scale(2.35714,2.35714) translate(-450.736,-427.788)" width="20" x="782.857142857143" xlink:href="#EnergyConsumer:站用变0716_0" y="743" zvalue="678"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450110750726" ObjectName="站用变"/>
   <cge:TPSR_Ref TObjectID="6192450110750726"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,806.429,772.464) scale(2.35714,2.35714) translate(-450.736,-427.788)" width="20" x="782.857142857143" y="743"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="28">
   <use class="v6300" height="30" transform="rotate(0,1061.79,812.643) scale(1.59524,1.59524) translate(-387.26,-294.296)" width="30" x="1037.857142857143" xlink:href="#Generator:发电机_0" y="788.7142857142857" zvalue="684"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450110881797" ObjectName="1号发电机"/>
   <cge:TPSR_Ref TObjectID="6192450110881797"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1061.79,812.643) scale(1.59524,1.59524) translate(-387.26,-294.296)" width="30" x="1037.857142857143" y="788.7142857142857"/></g>
  <g id="535">
   <use class="v6300" height="30" transform="rotate(0,1321.79,812.643) scale(1.59524,1.59524) translate(-484.275,-294.296)" width="30" x="1297.857142857143" xlink:href="#Generator:发电机_0" y="788.7142857142857" zvalue="703"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450111471621" ObjectName="2号发电机"/>
   <cge:TPSR_Ref TObjectID="6192450111471621"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1321.79,812.643) scale(1.59524,1.59524) translate(-484.275,-294.296)" width="30" x="1297.857142857143" y="788.7142857142857"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="428">
   <use class="v6300" height="29" transform="rotate(0,1121.43,790.571) scale(1.42857,1.42857) translate(-334.071,-230.957)" width="11" x="1113.571428571428" xlink:href="#Accessory:PT带保险_0" y="769.8571341378351" zvalue="693"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450111012869" ObjectName="1号发电机PT1"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1121.43,790.571) scale(1.42857,1.42857) translate(-334.071,-230.957)" width="11" x="1113.571428571428" y="769.8571341378351"/></g>
  <g id="436">
   <use class="v6300" height="29" transform="rotate(0,1147,790.571) scale(1.42857,1.42857) translate(-341.743,-230.957)" width="11" x="1139.142857142857" xlink:href="#Accessory:PT带保险_0" y="769.8571341122899" zvalue="695"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450111078405" ObjectName="1号发电机PT2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1147,790.571) scale(1.42857,1.42857) translate(-341.743,-230.957)" width="11" x="1139.142857142857" y="769.8571341122899"/></g>
  <g id="529">
   <use class="v6300" height="29" transform="rotate(0,1381.43,790.571) scale(1.42857,1.42857) translate(-412.071,-230.957)" width="11" x="1373.571428571428" xlink:href="#Accessory:PT带保险_0" y="769.8571341378351" zvalue="712"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450111340549" ObjectName="2号发电机PT1"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1381.43,790.571) scale(1.42857,1.42857) translate(-412.071,-230.957)" width="11" x="1373.571428571428" y="769.8571341378351"/></g>
  <g id="528">
   <use class="v6300" height="29" transform="rotate(0,1407,790.571) scale(1.42857,1.42857) translate(-419.743,-230.957)" width="11" x="1399.142857142857" xlink:href="#Accessory:PT带保险_0" y="769.8571341122899" zvalue="713"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450111275013" ObjectName="2号发电机PT2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1407,790.571) scale(1.42857,1.42857) translate(-419.743,-230.957)" width="11" x="1399.142857142857" y="769.8571341122899"/></g>
 </g>
</svg>