<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549593899010" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV那邦勐乃河电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="50.21" xlink:href="logo.png" y="47.57"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.214,79.5714) scale(1,1) translate(0,0)" writing-mode="lr" x="199.21" xml:space="preserve" y="83.06999999999999" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,201.714,77.2618) scale(1,1) translate(0,0)" writing-mode="lr" x="201.71" xml:space="preserve" y="86.26000000000001" zvalue="5">10kV那邦勐乃河电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="66" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,81.625,223) scale(1,1) translate(0,0)" width="72.88" x="45.19" y="211" zvalue="93"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,81.625,223) scale(1,1) translate(0,0)" writing-mode="lr" x="81.63" xml:space="preserve" y="227.5" zvalue="93">信号一览</text>
  <line fill="none" id="37" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384.2142857142856" x2="384.2142857142856" y1="15.57142857142867" y2="1045.571428571429" zvalue="6"/>
  <line fill="none" id="35" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="151.441921185511" y2="151.441921185511" zvalue="8"/>
  <line fill="none" id="34" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="621.441921185511" y2="621.441921185511" zvalue="9"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="936.5714285714287" y2="936.5714285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="975.7347285714286" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="370.2142857142856" y1="936.5714285714287" y2="936.5714285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="370.2142857142856" y1="975.7347285714286" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142856" x2="190.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="190.2142857142857" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142857" x2="280.2142857142857" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="280.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142856" x2="190.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="190.2142857142857" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142857" x2="280.2142857142857" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="280.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.2143,956.571) scale(1,1) translate(0,0)" writing-mode="lr" x="55.21" xml:space="preserve" y="962.5700000000001" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.2143,990.571) scale(1,1) translate(0,0)" writing-mode="lr" x="52.21" xml:space="preserve" y="996.5700000000001" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.214,990.571) scale(1,1) translate(0,0)" writing-mode="lr" x="234.21" xml:space="preserve" y="996.5700000000001" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.2143,1018.57) scale(1,1) translate(0,0)" writing-mode="lr" x="51.21" xml:space="preserve" y="1024.57" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.214,1018.57) scale(1,1) translate(0,0)" writing-mode="lr" x="233.21" xml:space="preserve" y="1024.57" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,75.7143,651.071) scale(1,1) translate(0,2.10522e-13)" writing-mode="lr" x="75.71428571428555" xml:space="preserve" y="655.5714285714286" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,238.634,958.571) scale(1,1) translate(0,0)" writing-mode="lr" x="238.63" xml:space="preserve" y="964.5700000000001" zvalue="27">NaBangMengNaiHe-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,628.537,559.352) scale(1,1) translate(0,-1.2187e-13)" writing-mode="lr" x="628.54" xml:space="preserve" y="563.85" zvalue="33">#1主变630kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,712.061,891.99) scale(1,1) translate(2.23129e-13,2.90178e-13)" writing-mode="lr" x="712.0606713030318" xml:space="preserve" y="896.49045413687" zvalue="36">#1发电机500kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,726.551,736.758) scale(1,1) translate(0,0)" writing-mode="lr" x="726.55" xml:space="preserve" y="741.26" zvalue="45">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,733.205,666.369) scale(1,1) translate(0,0)" writing-mode="lr" x="733.2" xml:space="preserve" y="670.87" zvalue="47">4011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1174.22,393.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1174.22" xml:space="preserve" y="398.17" zvalue="52">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,684.667,438.111) scale(1,1) translate(0,0)" writing-mode="lr" x="684.67" xml:space="preserve" y="442.61" zvalue="53">0011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,936.537,559.352) scale(1,1) translate(0,-1.2187e-13)" writing-mode="lr" x="936.54" xml:space="preserve" y="563.85" zvalue="57">#2主变630kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1020.06,891.99) scale(1,1) translate(0,2.90178e-13)" writing-mode="lr" x="1020.060671303032" xml:space="preserve" y="896.49045413687" zvalue="59">#2发电机500kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1038.55,736.758) scale(1,1) translate(0,0)" writing-mode="lr" x="1038.55" xml:space="preserve" y="741.26" zvalue="62">402</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1041.2,666.369) scale(1,1) translate(0,0)" writing-mode="lr" x="1041.2" xml:space="preserve" y="670.87" zvalue="64">4021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,981.25,438.111) scale(1,1) translate(-2.14347e-13,0)" writing-mode="lr" x="981.25" xml:space="preserve" y="442.61" zvalue="69">0021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,898.22,222.313) scale(1,1) translate(0,0)" writing-mode="lr" x="898.22" xml:space="preserve" y="226.81" zvalue="74">031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,900.094,295.258) scale(1,1) translate(0,0)" writing-mode="lr" x="900.09" xml:space="preserve" y="299.76" zvalue="75">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,866.26,112.444) scale(1,1) translate(0,0)" writing-mode="lr" x="866.26" xml:space="preserve" y="116.94" zvalue="78">10kV刀弄线</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="284.75" y2="284.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="310.75" y2="310.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="284.75" y2="310.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="284.75" y2="310.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="284.75" y2="284.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="310.75" y2="310.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="284.75" y2="310.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="284.75" y2="310.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="310.75" y2="310.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="335" y2="335"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="310.75" y2="335"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="310.75" y2="335"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="310.75" y2="310.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="335" y2="335"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="310.75" y2="335"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="310.75" y2="335"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="335" y2="335"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="357.75" y2="357.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="335" y2="357.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="335" y2="357.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="335" y2="335"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="357.75" y2="357.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="335" y2="357.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="335" y2="357.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="357.75" y2="357.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="380.5" y2="380.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="357.75" y2="380.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="357.75" y2="380.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="357.75" y2="357.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="380.5" y2="380.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="357.75" y2="380.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="357.75" y2="380.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="380.5" y2="380.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="403.25" y2="403.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="380.5" y2="403.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="380.5" y2="403.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="380.5" y2="380.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="403.25" y2="403.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="380.5" y2="403.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="380.5" y2="403.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="403.25" y2="403.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="426" y2="426"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="403.25" y2="426"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="403.25" y2="426"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="403.25" y2="403.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="426" y2="426"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="403.25" y2="426"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="403.25" y2="426"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="426" y2="426"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="448.75" y2="448.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="426" y2="448.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="426" y2="448.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="426" y2="426"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="448.75" y2="448.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="426" y2="448.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="426" y2="448.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,197.399,223.591) scale(1,1) translate(0,0)" writing-mode="lr" x="197.4" xml:space="preserve" y="228.09" zvalue="84">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,302.399,223.591) scale(1,1) translate(0,0)" writing-mode="lr" x="302.4" xml:space="preserve" y="228.09" zvalue="85">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.5,297.75) scale(1,1) translate(0,0)" writing-mode="lr" x="12" xml:space="preserve" y="302.25" zvalue="86">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235,297.75) scale(1,1) translate(0,0)" writing-mode="lr" x="192.5" xml:space="preserve" y="302.25" zvalue="87">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.6875,371) scale(1,1) translate(0,0)" writing-mode="lr" x="57.69" xml:space="preserve" y="375.5" zvalue="88">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,53.5,323.75) scale(1,1) translate(0,0)" writing-mode="lr" x="11" xml:space="preserve" y="328.25" zvalue="94">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,234,323.75) scale(1,1) translate(0,0)" writing-mode="lr" x="191.5" xml:space="preserve" y="328.25" zvalue="95">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.6875,417) scale(1,1) translate(0,0)" writing-mode="lr" x="54.69" xml:space="preserve" y="421.5" zvalue="98">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.688,416) scale(1,1) translate(0,0)" writing-mode="lr" x="222.69" xml:space="preserve" y="420.5" zvalue="99">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.6875,440) scale(1,1) translate(0,0)" writing-mode="lr" x="54.69" xml:space="preserve" y="444.5" zvalue="100">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.688,439) scale(1,1) translate(0,0)" writing-mode="lr" x="222.69" xml:space="preserve" y="443.5" zvalue="101">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,53.5,346.75) scale(1,1) translate(0,0)" writing-mode="lr" x="11" xml:space="preserve" y="351.25" zvalue="102">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,233.5,345.75) scale(1,1) translate(0,0)" writing-mode="lr" x="191" xml:space="preserve" y="350.25" zvalue="104">厂用电率</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="45.19" y="211" zvalue="93"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="PowerTransformer2Class">
  <g id="414">
   <g id="4140">
    <use class="kv10" height="60" transform="rotate(0,708.03,556.077) scale(1.6125,1.54462) translate(-256.692,-179.73)" width="40" x="675.78" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="509.74" zvalue="32"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874459119619" ObjectName="10"/>
    </metadata>
   </g>
   <g id="4141">
    <use class="v400" height="60" transform="rotate(0,708.03,556.077) scale(1.6125,1.54462) translate(-256.692,-179.73)" width="40" x="675.78" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="509.74" zvalue="32"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874459185155" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399461437443" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399461437443"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,708.03,556.077) scale(1.6125,1.54462) translate(-256.692,-179.73)" width="40" x="675.78" y="509.74"/></g>
  <g id="63">
   <g id="630">
    <use class="kv10" height="60" transform="rotate(0,1016.03,556.077) scale(1.6125,1.54462) translate(-373.684,-179.73)" width="40" x="983.78" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="509.74" zvalue="56"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874459250691" ObjectName="10"/>
    </metadata>
   </g>
   <g id="631">
    <use class="v400" height="60" transform="rotate(0,1016.03,556.077) scale(1.6125,1.54462) translate(-373.684,-179.73)" width="40" x="983.78" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="509.74" zvalue="56"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874459316227" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399461502979" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399461502979"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1016.03,556.077) scale(1.6125,1.54462) translate(-373.684,-179.73)" width="40" x="983.78" y="509.74"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="212">
   <use class="v400" height="30" transform="rotate(0,707.271,834.672) scale(1.85899,1.85899) translate(-313.926,-372.795)" width="30" x="679.3863709131574" xlink:href="#Generator:发电机_0" y="806.7867361713461" zvalue="35"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450065530885" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192450065530885"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,707.271,834.672) scale(1.85899,1.85899) translate(-313.926,-372.795)" width="30" x="679.3863709131574" y="806.7867361713461"/></g>
  <g id="62">
   <use class="v400" height="30" transform="rotate(0,1015.27,834.672) scale(1.85899,1.85899) translate(-456.245,-372.795)" width="30" x="987.3863709131574" xlink:href="#Generator:发电机_0" y="806.7867361713461" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450065793029" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192450065793029"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1015.27,834.672) scale(1.85899,1.85899) translate(-456.245,-372.795)" width="30" x="987.3863709131574" y="806.7867361713461"/></g>
 </g>
 <g id="BreakerClass">
  <g id="108">
   <use class="v400" height="20" transform="rotate(0,707.19,737.758) scale(1.22222,1.11111) translate(-127.469,-72.6647)" width="10" x="701.078631472918" xlink:href="#Breaker:开关_0" y="726.6468253968253" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924563959812" ObjectName="#1主变0.4kV侧401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924563959812"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,707.19,737.758) scale(1.22222,1.11111) translate(-127.469,-72.6647)" width="10" x="701.078631472918" y="726.6468253968253"/></g>
  <g id="61">
   <use class="v400" height="20" transform="rotate(0,1015.19,737.758) scale(1.22222,1.11111) translate(-183.469,-72.6647)" width="10" x="1009.078631472918" xlink:href="#Breaker:开关_0" y="726.6468253968253" zvalue="60"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924564025348" ObjectName="#2主变0.4kV侧402断路器"/>
   <cge:TPSR_Ref TObjectID="6473924564025348"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1015.19,737.758) scale(1.22222,1.11111) translate(-183.469,-72.6647)" width="10" x="1009.078631472918" y="726.6468253968253"/></g>
  <g id="68">
   <use class="kv10" height="20" transform="rotate(0,866.301,222.202) scale(1.22222,1.11111) translate(-156.398,-21.1091)" width="10" x="860.1897425840291" xlink:href="#Breaker:开关_0" y="211.0912698412696" zvalue="72"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924564090884" ObjectName="10kV刀弄线031断路器"/>
   <cge:TPSR_Ref TObjectID="6473924564090884"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,866.301,222.202) scale(1.22222,1.11111) translate(-156.398,-21.1091)" width="10" x="860.1897425840291" y="211.0912698412696"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="109">
   <use class="v400" height="30" transform="rotate(0,707.247,667.369) scale(-1.11111,-0.814815) translate(-1342.94,-1489.19)" width="15" x="698.913198566467" xlink:href="#Disconnector:刀闸_0" y="655.1468390661572" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450065465349" ObjectName="#1主变0.4kV侧4011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450065465349"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,707.247,667.369) scale(-1.11111,-0.814815) translate(-1342.94,-1489.19)" width="15" x="698.913198566467" y="655.1468390661572"/></g>
  <g id="45">
   <use class="kv10" height="30" transform="rotate(0,708.889,439.111) scale(1.11111,1.11111) translate(-70.0556,-42.2444)" width="15" x="700.5555555555555" xlink:href="#Disconnector:令克_0" y="422.4444444444445" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450065596421" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450065596421"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,708.889,439.111) scale(1.11111,1.11111) translate(-70.0556,-42.2444)" width="15" x="700.5555555555555" y="422.4444444444445"/></g>
  <g id="60">
   <use class="v400" height="30" transform="rotate(0,1015.25,667.369) scale(-1.11111,-0.814815) translate(-1928.14,-1489.19)" width="15" x="1006.913198566467" xlink:href="#Disconnector:刀闸_0" y="655.1468390661572" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450065727493" ObjectName="#2主变0.4kV侧4021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450065727493"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1015.25,667.369) scale(-1.11111,-0.814815) translate(-1928.14,-1489.19)" width="15" x="1006.913198566467" y="655.1468390661572"/></g>
  <g id="56">
   <use class="kv10" height="30" transform="rotate(0,1016.89,439.111) scale(1.11111,1.11111) translate(-100.856,-42.2444)" width="15" x="1008.555555555556" xlink:href="#Disconnector:令克_0" y="422.4444444444445" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450065661957" ObjectName="#2主变10kV侧0021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450065661957"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1016.89,439.111) scale(1.11111,1.11111) translate(-100.856,-42.2444)" width="15" x="1008.555555555556" y="422.4444444444445"/></g>
  <g id="67">
   <use class="kv10" height="30" transform="rotate(0,866.358,291.814) scale(-1.11111,-0.814815) translate(-1645.25,-652.726)" width="15" x="858.0243096775781" xlink:href="#Disconnector:刀闸_0" y="279.5912835106016" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450065858565" ObjectName="10kV刀弄线0311隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450065858565"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,866.358,291.814) scale(-1.11111,-0.814815) translate(-1645.25,-652.726)" width="15" x="858.0243096775781" y="279.5912835106016"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="10">
   <path class="v400" d="M 707.18 655.36 L 707.18 601.76" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@1" LinkObjectIDznd="414@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 707.18 655.36 L 707.18 601.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="v400" d="M 707.15 679.19 L 707.15 727.13" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="108@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 707.15 679.19 L 707.15 727.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="v400" d="M 707.27 748.37 L 707.27 807.25" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@1" LinkObjectIDznd="212@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 707.27 748.37 L 707.27 807.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv10" d="M 708.98 424.39 L 708.98 363.56" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="43@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 708.98 424.39 L 708.98 363.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="v400" d="M 1015.18 655.36 L 1015.18 601.76" stroke-width="1" zvalue="63"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@1" LinkObjectIDznd="63@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1015.18 655.36 L 1015.18 601.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="v400" d="M 1015.15 679.19 L 1015.15 727.13" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="61@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1015.15 679.19 L 1015.15 727.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="v400" d="M 1015.27 748.37 L 1015.27 807.25" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@1" LinkObjectIDznd="62@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1015.27 748.37 L 1015.27 807.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv10" d="M 1016.09 510.58 L 1016.09 452.72" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="56@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1016.09 510.58 L 1016.09 452.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv10" d="M 1016.98 424.39 L 1016.98 363.56" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@0" LinkObjectIDznd="43@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1016.98 424.39 L 1016.98 363.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv10" d="M 866.26 303.63 L 866.26 363.56" stroke-width="1" zvalue="78"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="43@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.26 303.63 L 866.26 363.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv10" d="M 866.29 279.8 L 866.38 232.81" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@1" LinkObjectIDznd="68@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.29 279.8 L 866.38 232.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv10" d="M 866.26 158.51 L 866.26 211.57" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="68@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.26 158.51 L 866.26 211.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 708.09 510.58 L 708.09 452.72" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@0" LinkObjectIDznd="45@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 708.09 510.58 L 708.09 452.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="43">
   <path class="kv10" d="M 555.56 363.56 L 1201.11 363.56" stroke-width="6" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674256748548" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674256748548"/></metadata>
  <path d="M 555.56 363.56 L 1201.11 363.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="MeasurementClass">
  <g id="274">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="274" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,154.611,369.917) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="374.83" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="273">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="273" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,154.611,297.917) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="302.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126940704772" ObjectName="F"/>
   </metadata>
  </g>
  <g id="272">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="272" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,332.222,298.917) scale(1,1) translate(0,0)" writing-mode="lr" x="332.38" xml:space="preserve" y="303.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126940770308" ObjectName="F"/>
   </metadata>
  </g>
  <g id="260">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="260" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,154.611,322.917) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="327.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126940573700" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,332.222,323.917) scale(1,1) translate(0,0)" writing-mode="lr" x="332.38" xml:space="preserve" y="328.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126940639236" ObjectName="F"/>
   </metadata>
  </g>
  <g id="209">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="209" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,154.611,346.917) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="351.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126940573700" ObjectName="F"/>
   </metadata>
  </g>
  <g id="198">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,334.611,345.917) scale(1,1) translate(0,0)" writing-mode="lr" x="334.77" xml:space="preserve" y="350.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126940573700" ObjectName="F"/>
   </metadata>
  </g>
  <g id="287">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="287" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,154.611,413.917) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="418.83" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="288">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="288" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,330.611,413.917) scale(1,1) translate(0,0)" writing-mode="lr" x="330.77" xml:space="preserve" y="418.83" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="雨量采集"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="271">
   <use height="30" transform="rotate(0,329.673,224.107) scale(0.708333,0.665547) translate(131.373,107.602)" width="30" x="319.05" xlink:href="#State:红绿圆(方形)_0" y="214.12" zvalue="92"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374892466179" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.673,224.107) scale(0.708333,0.665547) translate(131.373,107.602)" width="30" x="319.05" y="214.12"/></g>
  <g id="286">
   <use height="30" transform="rotate(0,234.048,224.107) scale(0.708333,0.665547) translate(91.9975,107.602)" width="30" x="223.42" xlink:href="#State:红绿圆(方形)_0" y="214.12" zvalue="106"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,234.048,224.107) scale(0.708333,0.665547) translate(91.9975,107.602)" width="30" x="223.42" y="214.12"/></g>
 </g>
</svg>