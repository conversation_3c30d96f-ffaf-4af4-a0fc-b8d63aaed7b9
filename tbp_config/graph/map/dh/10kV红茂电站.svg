<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549592784898" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_0" viewBox="0,0,30,50">
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01666666666667" x2="15.01666666666667" y1="37.50000000000001" y2="32.91666666666667"/>
   <use terminal-index="1" type="1" x="15.00325153685922" xlink:href="#terminal" y="49.86055225321343"/>
   <use terminal-index="2" type="2" x="15" xlink:href="#terminal" y="37.75085860895189"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.25" x2="15" y1="43.08333333333334" y2="37.75000000000001"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15" x2="20" y1="37.74761215261902" y2="42.91666666666667"/>
   <ellipse cx="15" cy="35.25" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_1" viewBox="0,0,30,50">
   <path d="M 10 16 L 20.1667 16 L 15 7.83333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="0" type="1" x="15.00731595793324" xlink:href="#terminal" y="0.3902606310013752"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV红茂电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="42.43" xlink:href="logo.png" y="34.29"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,177.054,64.2857) scale(1,1) translate(0,0)" writing-mode="lr" x="177.05" xml:space="preserve" y="67.79000000000001" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,178.762,63.976) scale(1,1) translate(6.70892e-15,0)" writing-mode="lr" x="178.76" xml:space="preserve" y="72.98" zvalue="3">10kV红茂电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="58" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,82.9286,305.286) scale(1,1) translate(0,0)" width="97" x="34.43" y="293.29" zvalue="9"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.9286,305.286) scale(1,1) translate(0,0)" writing-mode="lr" x="82.93000000000001" xml:space="preserve" y="309.79" zvalue="9">全站公用</text>
  <line fill="none" id="63" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="376.4285714285716" x2="376.4285714285716" y1="2.285714285714448" y2="1032.285714285714" zvalue="4"/>
  <line fill="none" id="61" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.4285714285719" x2="369.4285714285714" y1="138.1562068997968" y2="138.1562068997968" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="150.2857142857144" y2="150.2857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="176.2857142857144" y2="176.2857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="3.428571428571672" y1="150.2857142857144" y2="176.2857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="150.2857142857144" y2="176.2857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="150.2857142857144" y2="150.2857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="176.2857142857144" y2="176.2857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="150.2857142857144" y2="176.2857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285717" x2="365.4285714285717" y1="150.2857142857144" y2="176.2857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="176.2857142857144" y2="176.2857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="200.5357142857144" y2="200.5357142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="3.428571428571672" y1="176.2857142857144" y2="200.5357142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="176.2857142857144" y2="200.5357142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="176.2857142857144" y2="176.2857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="200.5357142857144" y2="200.5357142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="176.2857142857144" y2="200.5357142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285717" x2="365.4285714285717" y1="176.2857142857144" y2="200.5357142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="200.5357142857144" y2="200.5357142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="223.2857142857144" y2="223.2857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="3.428571428571672" y1="200.5357142857144" y2="223.2857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="200.5357142857144" y2="223.2857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="200.5357142857144" y2="200.5357142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="223.2857142857144" y2="223.2857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="200.5357142857144" y2="223.2857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285717" x2="365.4285714285717" y1="200.5357142857144" y2="223.2857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="223.2857142857144" y2="223.2857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="246.0357142857144" y2="246.0357142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="3.428571428571672" y1="223.2857142857144" y2="246.0357142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="223.2857142857144" y2="246.0357142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="223.2857142857144" y2="223.2857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="246.0357142857144" y2="246.0357142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="223.2857142857144" y2="246.0357142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285717" x2="365.4285714285717" y1="223.2857142857144" y2="246.0357142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="246.0357142857144" y2="246.0357142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="268.7857142857144" y2="268.7857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="3.428571428571672" y1="246.0357142857144" y2="268.7857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="246.0357142857144" y2="268.7857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="246.0357142857144" y2="246.0357142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="268.7857142857144" y2="268.7857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="246.0357142857144" y2="268.7857142857144"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285717" x2="365.4285714285717" y1="246.0357142857144" y2="268.7857142857144"/>
  <line fill="none" id="59" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.4285714285719" x2="369.4285714285714" y1="608.1562068997969" y2="608.1562068997969" zvalue="8"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591856" x2="103.9956331659185" y1="430.9523577915738" y2="430.9523577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591856" x2="103.9956331659185" y1="468.4423577915738" y2="468.4423577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591856" x2="55.65093316591856" y1="430.9523577915738" y2="468.4423577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9956331659185" x2="103.9956331659185" y1="430.9523577915738" y2="468.4423577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659185" x2="166.1042331659185" y1="430.9523577915738" y2="430.9523577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659185" x2="166.1042331659185" y1="468.4423577915738" y2="468.4423577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659185" x2="103.9959331659185" y1="430.9523577915738" y2="468.4423577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1042331659185" x2="166.1042331659185" y1="430.9523577915738" y2="468.4423577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659185" x2="229.4285331659186" y1="430.9523577915738" y2="430.9523577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659185" x2="229.4285331659186" y1="468.4423577915738" y2="468.4423577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659185" x2="166.1037331659185" y1="430.9523577915738" y2="468.4423577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4285331659186" x2="229.4285331659186" y1="430.9523577915738" y2="468.4423577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659185" x2="291.5367331659186" y1="430.9523577915738" y2="430.9523577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659185" x2="291.5367331659186" y1="468.4423577915738" y2="468.4423577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659185" x2="229.4284331659185" y1="430.9523577915738" y2="468.4423577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="291.5367331659186" y1="430.9523577915738" y2="468.4423577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="353.6450331659184" y1="430.9523577915738" y2="430.9523577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="353.6450331659184" y1="468.4423577915738" y2="468.4423577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="291.5367331659186" y1="430.9523577915738" y2="468.4423577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="353.6450331659184" x2="353.6450331659184" y1="430.9523577915738" y2="468.4423577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591856" x2="103.9956331659185" y1="468.4424577915738" y2="468.4424577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591856" x2="103.9956331659185" y1="492.6110577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591856" x2="55.65093316591856" y1="468.4424577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9956331659185" x2="103.9956331659185" y1="468.4424577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659185" x2="166.1042331659185" y1="468.4424577915738" y2="468.4424577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659185" x2="166.1042331659185" y1="492.6110577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659185" x2="103.9959331659185" y1="468.4424577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1042331659185" x2="166.1042331659185" y1="468.4424577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659185" x2="229.4285331659186" y1="468.4424577915738" y2="468.4424577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659185" x2="229.4285331659186" y1="492.6110577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659185" x2="166.1037331659185" y1="468.4424577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4285331659186" x2="229.4285331659186" y1="468.4424577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659185" x2="291.5367331659186" y1="468.4424577915738" y2="468.4424577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659185" x2="291.5367331659186" y1="492.6110577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659185" x2="229.4284331659185" y1="468.4424577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="291.5367331659186" y1="468.4424577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="353.6450331659184" y1="468.4424577915738" y2="468.4424577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="353.6450331659184" y1="492.6110577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="291.5367331659186" y1="468.4424577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="353.6450331659184" x2="353.6450331659184" y1="468.4424577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591856" x2="103.9956331659185" y1="492.6110577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591856" x2="103.9956331659185" y1="516.7796577915738" y2="516.7796577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591856" x2="55.65093316591856" y1="492.6110577915738" y2="516.7796577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9956331659185" x2="103.9956331659185" y1="492.6110577915738" y2="516.7796577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659185" x2="166.1042331659185" y1="492.6110577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659185" x2="166.1042331659185" y1="516.7796577915738" y2="516.7796577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659185" x2="103.9959331659185" y1="492.6110577915738" y2="516.7796577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1042331659185" x2="166.1042331659185" y1="492.6110577915738" y2="516.7796577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659185" x2="229.4285331659186" y1="492.6110577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659185" x2="229.4285331659186" y1="516.7796577915738" y2="516.7796577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659185" x2="166.1037331659185" y1="492.6110577915738" y2="516.7796577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4285331659186" x2="229.4285331659186" y1="492.6110577915738" y2="516.7796577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659185" x2="291.5367331659186" y1="492.6110577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659185" x2="291.5367331659186" y1="516.7796577915738" y2="516.7796577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659185" x2="229.4284331659185" y1="492.6110577915738" y2="516.7796577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="291.5367331659186" y1="492.6110577915738" y2="516.7796577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="353.6450331659184" y1="492.6110577915738" y2="492.6110577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="353.6450331659184" y1="516.7796577915738" y2="516.7796577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="291.5367331659186" y1="492.6110577915738" y2="516.7796577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="353.6450331659184" x2="353.6450331659184" y1="492.6110577915738" y2="516.7796577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591856" x2="103.9956331659185" y1="516.7796977915738" y2="516.7796977915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591856" x2="103.9956331659185" y1="540.9482977915737" y2="540.9482977915737"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591856" x2="55.65093316591856" y1="516.7796977915738" y2="540.9482977915737"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9956331659185" x2="103.9956331659185" y1="516.7796977915738" y2="540.9482977915737"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659185" x2="166.1042331659185" y1="516.7796977915738" y2="516.7796977915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659185" x2="166.1042331659185" y1="540.9482977915737" y2="540.9482977915737"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659185" x2="103.9959331659185" y1="516.7796977915738" y2="540.9482977915737"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1042331659185" x2="166.1042331659185" y1="516.7796977915738" y2="540.9482977915737"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659185" x2="229.4285331659186" y1="516.7796977915738" y2="516.7796977915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659185" x2="229.4285331659186" y1="540.9482977915737" y2="540.9482977915737"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659185" x2="166.1037331659185" y1="516.7796977915738" y2="540.9482977915737"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4285331659186" x2="229.4285331659186" y1="516.7796977915738" y2="540.9482977915737"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659185" x2="291.5367331659186" y1="516.7796977915738" y2="516.7796977915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659185" x2="291.5367331659186" y1="540.9482977915737" y2="540.9482977915737"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659185" x2="229.4284331659185" y1="516.7796977915738" y2="540.9482977915737"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="291.5367331659186" y1="516.7796977915738" y2="540.9482977915737"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="353.6450331659184" y1="516.7796977915738" y2="516.7796977915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="353.6450331659184" y1="540.9482977915737" y2="540.9482977915737"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="291.5367331659186" y1="516.7796977915738" y2="540.9482977915737"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="353.6450331659184" x2="353.6450331659184" y1="516.7796977915738" y2="540.9482977915737"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591856" x2="103.9956331659185" y1="540.9484577915738" y2="540.9484577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591856" x2="103.9956331659185" y1="565.1170577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591856" x2="55.65093316591856" y1="540.9484577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9956331659185" x2="103.9956331659185" y1="540.9484577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659185" x2="166.1042331659185" y1="540.9484577915738" y2="540.9484577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659185" x2="166.1042331659185" y1="565.1170577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659185" x2="103.9959331659185" y1="540.9484577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1042331659185" x2="166.1042331659185" y1="540.9484577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659185" x2="229.4285331659186" y1="540.9484577915738" y2="540.9484577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659185" x2="229.4285331659186" y1="565.1170577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659185" x2="166.1037331659185" y1="540.9484577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4285331659186" x2="229.4285331659186" y1="540.9484577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659185" x2="291.5367331659186" y1="540.9484577915738" y2="540.9484577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659185" x2="291.5367331659186" y1="565.1170577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659185" x2="229.4284331659185" y1="540.9484577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="291.5367331659186" y1="540.9484577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="353.6450331659184" y1="540.9484577915738" y2="540.9484577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="353.6450331659184" y1="565.1170577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="291.5367331659186" y1="540.9484577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="353.6450331659184" x2="353.6450331659184" y1="540.9484577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591856" x2="103.9956331659185" y1="565.1170577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591856" x2="103.9956331659185" y1="589.2856577915738" y2="589.2856577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.65093316591856" x2="55.65093316591856" y1="565.1170577915738" y2="589.2856577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9956331659185" x2="103.9956331659185" y1="565.1170577915738" y2="589.2856577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659185" x2="166.1042331659185" y1="565.1170577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659185" x2="166.1042331659185" y1="589.2856577915738" y2="589.2856577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.9959331659185" x2="103.9959331659185" y1="565.1170577915738" y2="589.2856577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1042331659185" x2="166.1042331659185" y1="565.1170577915738" y2="589.2856577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659185" x2="229.4285331659186" y1="565.1170577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659185" x2="229.4285331659186" y1="589.2856577915738" y2="589.2856577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="166.1037331659185" x2="166.1037331659185" y1="565.1170577915738" y2="589.2856577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4285331659186" x2="229.4285331659186" y1="565.1170577915738" y2="589.2856577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659185" x2="291.5367331659186" y1="565.1170577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659185" x2="291.5367331659186" y1="589.2856577915738" y2="589.2856577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.4284331659185" x2="229.4284331659185" y1="565.1170577915738" y2="589.2856577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="291.5367331659186" y1="565.1170577915738" y2="589.2856577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="353.6450331659184" y1="565.1170577915738" y2="565.1170577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="353.6450331659184" y1="589.2856577915738" y2="589.2856577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.5367331659186" x2="291.5367331659186" y1="565.1170577915738" y2="589.2856577915738"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="353.6450331659184" x2="353.6450331659184" y1="565.1170577915738" y2="589.2856577915738"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="92.42857142857167" y1="923.2857142857147" y2="923.2857142857147"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="92.42857142857167" y1="962.4490142857146" y2="962.4490142857146"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="2.428571428571672" y1="923.2857142857147" y2="962.4490142857146"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="92.42857142857167" y1="923.2857142857147" y2="962.4490142857146"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="362.4285714285717" y1="923.2857142857147" y2="923.2857142857147"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="362.4285714285717" y1="962.4490142857146" y2="962.4490142857146"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="92.42857142857167" y1="923.2857142857147" y2="962.4490142857146"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362.4285714285717" x2="362.4285714285717" y1="923.2857142857147" y2="962.4490142857146"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="92.42857142857167" y1="962.4489842857147" y2="962.4489842857147"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="92.42857142857167" y1="990.3673842857146" y2="990.3673842857146"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="2.428571428571672" y1="962.4489842857147" y2="990.3673842857146"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="92.42857142857167" y1="962.4489842857147" y2="990.3673842857146"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="182.4285714285717" y1="962.4489842857147" y2="962.4489842857147"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="182.4285714285717" y1="990.3673842857146" y2="990.3673842857146"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="92.42857142857167" y1="962.4489842857147" y2="990.3673842857146"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285717" x2="182.4285714285717" y1="962.4489842857147" y2="990.3673842857146"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="272.4285714285718" y1="962.4489842857147" y2="962.4489842857147"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="272.4285714285718" y1="990.3673842857146" y2="990.3673842857146"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="182.4285714285718" y1="962.4489842857147" y2="990.3673842857146"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285718" x2="272.4285714285718" y1="962.4489842857147" y2="990.3673842857146"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285717" x2="362.4285714285717" y1="962.4489842857147" y2="962.4489842857147"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285717" x2="362.4285714285717" y1="990.3673842857146" y2="990.3673842857146"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285717" x2="272.4285714285717" y1="962.4489842857147" y2="990.3673842857146"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362.4285714285717" x2="362.4285714285717" y1="962.4489842857147" y2="990.3673842857146"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="92.42857142857167" y1="990.3673142857147" y2="990.3673142857147"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="92.42857142857167" y1="1018.285714285715" y2="1018.285714285715"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="2.428571428571672" y1="990.3673142857147" y2="1018.285714285715"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="92.42857142857167" y1="990.3673142857147" y2="1018.285714285715"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="182.4285714285717" y1="990.3673142857147" y2="990.3673142857147"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="182.4285714285717" y1="1018.285714285715" y2="1018.285714285715"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="92.42857142857167" y1="990.3673142857147" y2="1018.285714285715"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285717" x2="182.4285714285717" y1="990.3673142857147" y2="1018.285714285715"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="272.4285714285718" y1="990.3673142857147" y2="990.3673142857147"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="272.4285714285718" y1="1018.285714285715" y2="1018.285714285715"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="182.4285714285718" y1="990.3673142857147" y2="1018.285714285715"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285718" x2="272.4285714285718" y1="990.3673142857147" y2="1018.285714285715"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285717" x2="362.4285714285717" y1="990.3673142857147" y2="990.3673142857147"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285717" x2="362.4285714285717" y1="1018.285714285715" y2="1018.285714285715"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285717" x2="272.4285714285717" y1="990.3673142857147" y2="1018.285714285715"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362.4285714285717" x2="362.4285714285717" y1="990.3673142857147" y2="1018.285714285715"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.4286,943.286) scale(1,1) translate(0,0)" writing-mode="lr" x="47.43" xml:space="preserve" y="949.29" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.4286,977.286) scale(1,1) translate(0,0)" writing-mode="lr" x="44.43" xml:space="preserve" y="983.29" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.429,977.286) scale(1,1) translate(0,0)" writing-mode="lr" x="226.43" xml:space="preserve" y="983.29" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43.4286,1005.29) scale(1,1) translate(0,0)" writing-mode="lr" x="43.43" xml:space="preserve" y="1011.29" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225.429,1005.29) scale(1,1) translate(0,0)" writing-mode="lr" x="225.43" xml:space="preserve" y="1011.29" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.9286,637.786) scale(1,1) translate(0,-2.74796e-13)" writing-mode="lr" x="67.92857142857167" xml:space="preserve" y="642.2857142857144" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,200.827,305.127) scale(1,1) translate(0,0)" writing-mode="lr" x="200.83" xml:space="preserve" y="309.63" zvalue="19">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,305.827,305.127) scale(1,1) translate(0,0)" writing-mode="lr" x="305.83" xml:space="preserve" y="309.63" zvalue="20">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.4286,480.786) scale(1,1) translate(0,0)" writing-mode="lr" x="79.42857142857167" xml:space="preserve" y="485.2857142857144" zvalue="21">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.4286,506.286) scale(1,1) translate(0,5.45992e-14)" writing-mode="lr" x="79.42857142857167" xml:space="preserve" y="510.7857142857144" zvalue="22">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.4286,531.786) scale(1,1) translate(0,0)" writing-mode="lr" x="79.42857142857167" xml:space="preserve" y="536.2857142857144" zvalue="23">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.4286,556.286) scale(1,1) translate(0,6.01503e-14)" writing-mode="lr" x="78.42857142857167" xml:space="preserve" y="560.7857142857144" zvalue="24">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.4286,582.786) scale(1,1) translate(0,0)" writing-mode="lr" x="79.42857142857167" xml:space="preserve" y="587.2857142857144" zvalue="25">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.483,945.286) scale(1,1) translate(0,0)" writing-mode="lr" x="227.48" xml:space="preserve" y="951.29" zvalue="26">DHDDBH-HongMao-01-2024</text>
  
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,317.483,977.286) scale(1,1) translate(0,0)" writing-mode="lr" x="317.48" xml:space="preserve" y="983.29" zvalue="28">20240614</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,41.4286,164.286) scale(1,1) translate(0,0)" writing-mode="lr" x="41.43" xml:space="preserve" y="169.79" zvalue="29">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,221.429,164.286) scale(1,1) translate(0,0)" writing-mode="lr" x="221.43" xml:space="preserve" y="169.79" zvalue="30">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.6161,236.286) scale(1,1) translate(0,0)" writing-mode="lr" x="48.62" xml:space="preserve" y="240.79" zvalue="31">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.512,190.98) scale(1,1) translate(0,0)" writing-mode="lr" x="233.51" xml:space="preserve" y="195.48" zvalue="32">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" x="254.796875" xml:space="preserve" y="449.046875" zvalue="33">0.4kV  母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="254.796875" xml:space="preserve" y="465.046875" zvalue="33">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.0714,191.786) scale(1,1) translate(0,0)" writing-mode="lr" x="54.07" xml:space="preserve" y="196.29" zvalue="34">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233,237.5) scale(1,1) translate(0,0)" writing-mode="lr" x="233" xml:space="preserve" y="242" zvalue="35">10kV#2变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" x="132.71875" xml:space="preserve" y="448.578125" zvalue="36">10kV  母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="132.71875" xml:space="preserve" y="464.578125" zvalue="36">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1802.8,435.036) scale(1,1) translate(0,0)" writing-mode="lr" x="1802.8" xml:space="preserve" y="439.54" zvalue="38">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,818.568,1006.04) scale(1,1) translate(0,0)" writing-mode="lr" x="818.5700000000001" xml:space="preserve" y="1010.54" zvalue="41">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,760.679,694.098) scale(1,1) translate(0,0)" writing-mode="lr" x="760.6799999999999" xml:space="preserve" y="698.6" zvalue="42">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,781.304,565.036) scale(1,1) translate(0,0)" writing-mode="lr" x="781.3" xml:space="preserve" y="569.54" zvalue="45">0021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,796.304,822.536) scale(1,1) translate(0,0)" writing-mode="lr" x="796.3" xml:space="preserve" y="827.04" zvalue="48">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,836.693,876.286) scale(1,1) translate(0,0)" writing-mode="lr" x="836.6900000000001" xml:space="preserve" y="880.79" zvalue="51">402</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,697.679,23.0357) scale(1,1) translate(0,-3.90719e-14)" writing-mode="lr" x="697.6799999999999" xml:space="preserve" y="27.54" zvalue="56">10kV河茂线T红茂支线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1522.57,1006.04) scale(1,1) translate(0,0)" writing-mode="lr" x="1522.57" xml:space="preserve" y="1010.54" zvalue="59">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1464.68,694.098) scale(1,1) translate(0,0)" writing-mode="lr" x="1464.68" xml:space="preserve" y="698.6" zvalue="60">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1485.3,565.036) scale(1,1) translate(0,0)" writing-mode="lr" x="1485.3" xml:space="preserve" y="569.54" zvalue="63">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1500.3,822.536) scale(1,1) translate(0,0)" writing-mode="lr" x="1500.3" xml:space="preserve" y="827.04" zvalue="66">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1540.69,876.286) scale(1,1) translate(0,0)" writing-mode="lr" x="1540.69" xml:space="preserve" y="880.79" zvalue="69">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,757.5,716.5) scale(1,1) translate(0,0)" writing-mode="lr" x="757.5" xml:space="preserve" y="721" zvalue="74">315kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1468.5,716.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1468.5" xml:space="preserve" y="721" zvalue="76">200kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,817.5,1026.5) scale(1,1) translate(0,0)" writing-mode="lr" x="817.5" xml:space="preserve" y="1031" zvalue="77">250kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1521.5,1026) scale(1,1) translate(0,0)" writing-mode="lr" x="1521.5" xml:space="preserve" y="1030.5" zvalue="78">160kW</text>
 </g>
 <g id="ButtonClass">
  <g href="500kV德宏变_全站公用.svg"><rect fill-opacity="0" height="24" width="97" x="34.43" y="293.29" zvalue="9"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="36">
   <path class="kv10" d="M 513.93 436.04 L 1765.18 436.04" stroke-width="6" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674254127108" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674254127108"/></metadata>
  <path d="M 513.93 436.04 L 1765.18 436.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="37">
   <use class="v400" height="30" transform="rotate(0,812.318,962.786) scale(1.875,1.875) translate(-365.957,-436.175)" width="30" x="784.1925269587039" xlink:href="#Generator:发电机_0" y="934.6607142857144" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450021752838" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192450021752838"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,812.318,962.786) scale(1.875,1.875) translate(-365.957,-436.175)" width="30" x="784.1925269587039" y="934.6607142857144"/></g>
  <g id="72">
   <use class="v400" height="30" transform="rotate(0,1516.32,962.786) scale(1.875,1.875) translate(-694.49,-436.175)" width="30" x="1488.192526958704" xlink:href="#Generator:发电机_0" y="934.6607142857144" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450021490693" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192450021490693"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1516.32,962.786) scale(1.875,1.875) translate(-694.49,-436.175)" width="30" x="1488.192526958704" y="934.6607142857144"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="38">
   <g id="380">
    <use class="v400" height="50" transform="rotate(0,811.616,695.098) scale(1.6375,1.6375) translate(-306.41,-254.673)" width="30" x="787.05" xlink:href="#PowerTransformer2:D-Y_0" y="654.16" zvalue="40"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874455252995" ObjectName="0.4"/>
    </metadata>
   </g>
   <g id="381">
    <use class="kv10" height="50" transform="rotate(0,811.616,695.098) scale(1.6375,1.6375) translate(-306.41,-254.673)" width="30" x="787.05" xlink:href="#PowerTransformer2:D-Y_1" y="654.16" zvalue="40"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874455187459" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399459471363" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399459471363"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,811.616,695.098) scale(1.6375,1.6375) translate(-306.41,-254.673)" width="30" x="787.05" y="654.16"/></g>
  <g id="71">
   <g id="710">
    <use class="v400" height="50" transform="rotate(0,1515.62,695.098) scale(1.6375,1.6375) translate(-580.487,-254.673)" width="30" x="1491.05" xlink:href="#PowerTransformer2:D-Y_0" y="654.16" zvalue="58"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874455121923" ObjectName="0.4"/>
    </metadata>
   </g>
   <g id="711">
    <use class="kv10" height="50" transform="rotate(0,1515.62,695.098) scale(1.6375,1.6375) translate(-580.487,-254.673)" width="30" x="1491.05" xlink:href="#PowerTransformer2:D-Y_1" y="654.16" zvalue="58"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874455056387" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399459405827" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399459405827"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1515.62,695.098) scale(1.6375,1.6375) translate(-580.487,-254.673)" width="30" x="1491.05" y="654.16"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="41">
   <use class="kv10" height="30" transform="rotate(0,810.179,566.036) scale(1.25,0.916667) translate(-160.161,50.2078)" width="15" x="800.8035714285716" xlink:href="#Disconnector:刀闸_0" y="552.2857142857144" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450021687301" ObjectName="#2主变0021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450021687301"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,810.179,566.036) scale(1.25,0.916667) translate(-160.161,50.2078)" width="15" x="800.8035714285716" y="552.2857142857144"/></g>
  <g id="44">
   <use class="v400" height="30" transform="rotate(0,812.054,823.536) scale(1.25,0.916667) translate(-160.536,73.6169)" width="15" x="802.6785714285716" xlink:href="#Disconnector:刀闸_0" y="809.7857142857144" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450021621765" ObjectName="#2主变4026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450021621765"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,812.054,823.536) scale(1.25,0.916667) translate(-160.536,73.6169)" width="15" x="802.6785714285716" y="809.7857142857144"/></g>
  <g id="70">
   <use class="kv10" height="30" transform="rotate(0,1514.18,566.036) scale(1.25,0.916667) translate(-300.961,50.2078)" width="15" x="1504.803571428572" xlink:href="#Disconnector:刀闸_0" y="552.2857142857144" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450021425157" ObjectName="#1主变0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450021425157"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1514.18,566.036) scale(1.25,0.916667) translate(-300.961,50.2078)" width="15" x="1504.803571428572" y="552.2857142857144"/></g>
  <g id="67">
   <use class="v400" height="30" transform="rotate(0,1516.05,823.536) scale(1.25,0.916667) translate(-301.336,73.6169)" width="15" x="1506.678571428572" xlink:href="#Disconnector:刀闸_0" y="809.7857142857144" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450021359621" ObjectName="#1主变4016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450021359621"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1516.05,823.536) scale(1.25,0.916667) translate(-301.336,73.6169)" width="15" x="1506.678571428572" y="809.7857142857144"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="23">
   <path class="kv10" d="M 811.63 654.8 L 811.63 579.55" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="41@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 811.63 654.8 L 811.63 579.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv10" d="M 810.29 552.74 L 810.29 436.04" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.29 552.74 L 810.29 436.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="v400" d="M 812.16 810.24 L 812.16 735.81" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="38@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 812.16 810.24 L 812.16 735.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="v400" d="M 812.32 935.13 L 812.32 893.4" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="47@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 812.32 935.13 L 812.32 893.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="v400" d="M 812.13 861.14 L 812.13 837.05" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="44@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 812.13 861.14 L 812.13 837.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv10" d="M 691.43 86.43 L 691.43 436.04" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="36@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 691.43 86.43 L 691.43 436.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv10" d="M 1515.63 654.8 L 1515.63 579.55" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="70@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1515.63 654.8 L 1515.63 579.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv10" d="M 1514.29 552.74 L 1514.29 436.04" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1514.29 552.74 L 1514.29 436.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="v400" d="M 1516.16 810.24 L 1516.16 735.81" stroke-width="1" zvalue="67"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="71@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1516.16 810.24 L 1516.16 735.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="v400" d="M 1516.32 935.13 L 1516.32 893.4" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@0" LinkObjectIDznd="65@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1516.32 935.13 L 1516.32 893.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="v400" d="M 1516.13 861.14 L 1516.13 837.05" stroke-width="1" zvalue="71"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="67@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1516.13 861.14 L 1516.13 837.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="47">
   <use class="v400" height="20" transform="rotate(0,812.193,877.286) scale(1.875,1.6875) translate(-374.648,-350.538)" width="10" x="802.8175269587039" xlink:href="#Breaker:开关_0" y="860.4107142857142" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924557996037" ObjectName="#2主变402断路器"/>
   <cge:TPSR_Ref TObjectID="6473924557996037"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,812.193,877.286) scale(1.875,1.6875) translate(-374.648,-350.538)" width="10" x="802.8175269587039" y="860.4107142857142"/></g>
  <g id="65">
   <use class="v400" height="20" transform="rotate(0,1516.19,877.286) scale(1.875,1.6875) translate(-703.182,-350.538)" width="10" x="1506.817526958704" xlink:href="#Breaker:开关_0" y="860.4107142857142" zvalue="68"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924557930501" ObjectName="#1主变401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924557930501"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1516.19,877.286) scale(1.875,1.6875) translate(-703.182,-350.538)" width="10" x="1506.817526958704" y="860.4107142857142"/></g>
 </g>
</svg>