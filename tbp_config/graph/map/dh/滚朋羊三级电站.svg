<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="688" id="thSvg" source="NR-PCS9000" viewBox="0 0 1000 688" width="1000">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="12.0194189818494"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="5.416666666666668" y2="12.02321291373541"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.92126160277786" x2="6.666666666666666" y1="12.10207526123773" y2="18.50000000000001"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.18646053143751" x2="24.5" y1="12.18904818695178" y2="19.00000000000001"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 7.33333 43.5833 L 23 43.5833 L 15 31.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:四卷PT_0" viewBox="0,0,18,18">
   <use terminal-index="0" type="0" x="9" xlink:href="#terminal" y="0.2666666666666639"/>
   <ellipse cx="9.25" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.25" cy="9.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.08" cy="12.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.82" cy="9.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="11.75" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.25" x2="9.25" y1="7.500000000000001" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="15.25" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="12.31481481481481" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.94444444444444" x2="13.62962962962963" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14.94444444444444" y1="9.966124661246614" y2="9.966124661246614"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="滚朋羊三级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="688" width="1000" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="BusbarSectionClass">
  <g id="9">
   <path class="kv10" d="M 136.15 387.85 L 880.77 387.85" stroke-width="6" zvalue="7"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV母线"/>
   </metadata>
  <path d="M 136.15 387.85 L 880.77 387.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="11">
   <g id="110">
    <use class="kv110" height="50" transform="rotate(0,253.077,278.615) scale(0.769231,0.769231) translate(72.4615,77.8154)" width="30" x="241.54" xlink:href="#PowerTransformer2:Y-D_0" y="259.38" zvalue="8"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="110"/>
    </metadata>
   </g>
   <g id="111">
    <use class="kv10" height="50" transform="rotate(0,253.077,278.615) scale(0.769231,0.769231) translate(72.4615,77.8154)" width="30" x="241.54" xlink:href="#PowerTransformer2:Y-D_1" y="259.38" zvalue="8"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号主变"/>
   </metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,253.077,278.615) scale(0.769231,0.769231) translate(72.4615,77.8154)" width="30" x="241.54" y="259.38"/></g>
 </g>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,160.462,369.154) scale(1,1) translate(0,-7.98592e-14)" writing-mode="lr" x="160.46" xml:space="preserve" y="373.65" zvalue="8">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,221.846,277.615) scale(1,1) translate(0,0)" writing-mode="lr" x="221.85" xml:space="preserve" y="282.12" zvalue="9">1号主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,273.923,323.769) scale(1,1) translate(0,0)" writing-mode="lr" x="273.92" xml:space="preserve" y="328.27" zvalue="10">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.615,357.615) scale(1,1) translate(0,0)" writing-mode="lr" x="232.62" xml:space="preserve" y="362.12" zvalue="11">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,268.154,220.692) scale(1,1) translate(0,1.88909e-13)" writing-mode="lr" x="268.15" xml:space="preserve" y="225.19" zvalue="15">161</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230.762,165.937) scale(1,1) translate(0,0)" writing-mode="lr" x="230.76" xml:space="preserve" y="170.44" zvalue="16">1616</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,253.077,35.2308) scale(1,1) translate(0,0)" writing-mode="lr" x="253.08" xml:space="preserve" y="39.73" zvalue="17">滚朋羊二三级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,319.308,148.385) scale(1,1) translate(0,0)" writing-mode="lr" x="319.31" xml:space="preserve" y="152.88" zvalue="18">16167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,318.538,190.692) scale(1,1) translate(0,0)" writing-mode="lr" x="318.54" xml:space="preserve" y="195.19" zvalue="19">16160</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,278.328,114.368) scale(1,1) translate(0,0)" writing-mode="lr" x="278.33" xml:space="preserve" y="118.87" zvalue="25">1619</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,295.146,61.3254) scale(1,1) translate(0,0)" writing-mode="lr" x="295.15" xml:space="preserve" y="65.83" zvalue="37">16197</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,291.128,286.083) scale(1,1) translate(0,-1.23494e-13)" writing-mode="lr" x="291.13" xml:space="preserve" y="290.58" zvalue="46">1010</text>
 </g>
 <g id="BreakerClass">
  <g id="13">
   <use class="kv10" height="20" transform="rotate(0,251.538,323.231) scale(1.15385,1.03846) translate(-32.7692,-11.5869)" width="10" x="245.7692307692309" xlink:href="#Breaker:开关_0" y="312.8461538461538" zvalue="9"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="001"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,251.538,323.231) scale(1.15385,1.03846) translate(-32.7692,-11.5869)" width="10" x="245.7692307692309" y="312.8461538461538"/></g>
  <g id="20">
   <use class="kv110" height="20" transform="rotate(0,253.846,221.692) scale(1.15385,1.03846) translate(-33.0769,-7.82621)" width="10" x="248.0769230769232" xlink:href="#Breaker:开关_0" y="211.3076923076923" zvalue="14"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="161"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,253.846,221.692) scale(1.15385,1.03846) translate(-33.0769,-7.82621)" width="10" x="248.0769230769232" y="211.3076923076923"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="15">
   <use class="kv10" height="30" transform="rotate(0,253.077,359.385) scale(0.769231,0.564103) translate(74.1923,271.168)" width="15" x="247.3076923076925" xlink:href="#Disconnector:刀闸_0" y="350.9230769230769" zvalue="10"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,253.077,359.385) scale(0.769231,0.564103) translate(74.1923,271.168)" width="15" x="247.3076923076925" y="350.9230769230769"/></g>
  <g id="22">
   <use class="kv110" height="30" transform="rotate(0,253.077,167.846) scale(0.769231,0.564103) translate(74.1923,123.161)" width="15" x="247.3076923076924" xlink:href="#Disconnector:刀闸_0" y="159.3846153846154" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1616"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,253.077,167.846) scale(0.769231,0.564103) translate(74.1923,123.161)" width="15" x="247.3076923076924" y="159.3846153846154"/></g>
  <g id="35">
   <use class="kv110" height="30" transform="rotate(270,277.74,99.5288) scale(0.769231,0.564103) translate(81.5913,70.3702)" width="15" x="271.9711538461539" xlink:href="#Disconnector:刀闸_0" y="91.06730769230774" zvalue="24"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1619"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,277.74,99.5288) scale(0.769231,0.564103) translate(81.5913,70.3702)" width="15" x="271.9711538461539" y="91.06730769230774"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="17">
   <path class="kv10" d="M 253.12 367.7 L 253.12 387.85" stroke-width="1" zvalue="11"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@1" LinkObjectIDznd="9@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 253.12 367.7 L 253.12 387.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv10" d="M 253.14 351.2 L 253.14 333.15" stroke-width="1" zvalue="12"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@0" LinkObjectIDznd="13@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 253.14 351.2 L 253.14 333.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv10" d="M 251.5 313.3 L 251.5 297.57" stroke-width="1" zvalue="13"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="13@0" LinkObjectIDznd="11@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 251.5 313.3 L 251.5 297.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv110" d="M 253.1 259.73 L 253.1 231.61" stroke-width="1" zvalue="19"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="11@0" LinkObjectIDznd="20@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 253.1 259.73 L 253.1 231.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv110" d="M 253.81 211.76 L 253.81 176.16" stroke-width="1" zvalue="20"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@0" LinkObjectIDznd="22@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 253.81 211.76 L 253.81 176.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="kv110" d="M 253.14 159.66 L 253.08 78.35" stroke-width="1" zvalue="21"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@0" LinkObjectIDznd="24@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 253.14 159.66 L 253.08 78.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv110" d="M 282.5 189.35 L 253.81 189.35" stroke-width="1" zvalue="22"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="31" MaxPinNum="2"/>
   </metadata>
  <path d="M 282.5 189.35 L 253.81 189.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv110" d="M 281.73 147.04 L 253.13 147.04" stroke-width="1" zvalue="23"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@0" LinkObjectIDznd="32" MaxPinNum="2"/>
   </metadata>
  <path d="M 281.73 147.04 L 253.13 147.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv110" d="M 320.76 99.48 L 320.76 90.66" stroke-width="1" zvalue="40"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="53@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 320.76 99.48 L 320.76 90.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv110" d="M 253.09 268.63 L 318.12 268.63 L 318.12 276.93" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="11@2" LinkObjectIDznd="61@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 253.09 268.63 L 318.12 268.63 L 318.12 276.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv110" d="M 312.34 268.63 L 340.45 268.63 L 340.45 274.45" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67" LinkObjectIDznd="59@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 312.34 268.63 L 340.45 268.63 L 340.45 274.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv110" d="M 269.56 99.46 L 253.09 99.46" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="32" MaxPinNum="2"/>
   </metadata>
  <path d="M 269.56 99.46 L 253.09 99.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv110" d="M 301.47 99.48 L 301.47 83.99" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 301.47 99.48 L 301.47 83.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv110" d="M 340.81 89.24 L 340.81 99.48" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 340.81 89.24 L 340.81 99.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv110" d="M 340.81 99.48 L 286.06 99.48" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78" LinkObjectIDznd="35@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 340.81 99.48 L 286.06 99.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="24">
   <use class="kv110" height="30" transform="rotate(0,253.077,65.5385) scale(0.961538,0.948718) translate(9.89231,2.77339)" width="12" x="247.3076923076924" xlink:href="#EnergyConsumer:负荷_0" y="51.30769230769226" zvalue="16"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="滚朋羊二三级线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,253.077,65.5385) scale(0.961538,0.948718) translate(9.89231,2.77339)" width="12" x="247.3076923076924" y="51.30769230769226"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="26">
   <use class="kv110" height="20" transform="rotate(270,289.231,147.077) scale(0.769231,0.769231) translate(85.6154,41.8154)" width="10" x="285.3846153846155" xlink:href="#GroundDisconnector:地刀_0" y="139.3846153846154" zvalue="17"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="16167"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,289.231,147.077) scale(0.769231,0.769231) translate(85.6154,41.8154)" width="10" x="285.3846153846155" y="139.3846153846154"/></g>
  <g id="28">
   <use class="kv110" height="20" transform="rotate(270,290,189.385) scale(0.769231,0.769231) translate(85.8462,54.5077)" width="10" x="286.1538461538462" xlink:href="#GroundDisconnector:地刀_0" y="181.6923076923077" zvalue="18"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="16160"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,290,189.385) scale(0.769231,0.769231) translate(85.8462,54.5077)" width="10" x="286.1538461538462" y="181.6923076923077"/></g>
  <g id="49">
   <use class="kv110" height="20" transform="rotate(0,301.442,78.0067) scale(0.613681,-0.613681) translate(187.83,-208.983)" width="10" x="298.3733284720128" xlink:href="#GroundDisconnector:地刀_0" y="71.86992734361149" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="16197"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,301.442,78.0067) scale(0.613681,-0.613681) translate(187.83,-208.983)" width="10" x="298.3733284720128" y="71.86992734361149"/></g>
  <g id="61">
   <use class="kv110" height="20" transform="rotate(0,318.083,285.014) scale(0.829615,0.829615) translate(64.4757,56.8321)" width="10" x="313.9350912778905" xlink:href="#GroundDisconnector:地刀_0" y="276.7180527383367" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1010"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,318.083,285.014) scale(0.829615,0.829615) translate(64.4757,56.8321)" width="10" x="313.9350912778905" y="276.7180527383367"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="53">
   <use class="kv110" height="20" transform="rotate(0,320.765,82.2358) scale(0.962963,-0.962963) translate(11.9667,-168.005)" width="20" x="311.1350358979671" xlink:href="#Accessory:线路PT3_0" y="72.60612767940376" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,320.765,82.2358) scale(0.962963,-0.962963) translate(11.9667,-168.005)" width="20" x="311.1350358979671" y="72.60612767940376"/></g>
  <g id="55">
   <use class="kv110" height="18" transform="rotate(0,340.814,80.5242) scale(0.997588,-0.997588) translate(0.802448,-161.265)" width="18" x="331.8356718787754" xlink:href="#Accessory:四卷PT_0" y="71.54590797694254" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,340.814,80.5242) scale(0.997588,-0.997588) translate(0.802448,-161.265)" width="18" x="331.8356718787754" y="71.54590797694254"/></g>
  <g id="59">
   <use class="kv110" height="20" transform="rotate(0,340.446,284.379) scale(1.13448,1.13448) translate(-39.0116,-32.3655)" width="20" x="329.101272142801" xlink:href="#Accessory:线路PT3_0" y="273.0345143905516" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,340.446,284.379) scale(1.13448,1.13448) translate(-39.0116,-32.3655)" width="20" x="329.101272142801" y="273.0345143905516"/></g>
 </g>
</svg>