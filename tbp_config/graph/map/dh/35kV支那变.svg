<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549679161345" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷带壁雷器_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="28.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11.75" y1="17.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="14.75" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="12" y1="11.75" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="9" y1="12.75" y2="12.75"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.04,18.25) scale(1,1) translate(0,0)" width="3.25" x="9.42" y="14.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.5" y1="10.83333333333333" y2="10.83333333333333"/>
   <path d="M 5.025 2.775 L 4.16667 9.5 L 5.025 7.60833 L 5.91667 9.5 L 5.025 2.775" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 5 23.75 L 11 23.75 L 11 17.75 L 10.25 20.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="7.627914951989029" y2="28.23902606310013"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.998992968246015" x2="4.998992968246015" y1="14.41666666666666" y2="17.66666666666666"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="1.08333333333333" y2="5.749999999999999"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="6" y2="14"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="14" y2="6"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.750000000000004" y2="4.166666666666668"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="32"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="1" y1="27" y2="11"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="27" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2019.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="GroundDisconnector:支那变双联地刀_0" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="20.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="13" y1="28.83333333333334" y2="28.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.927164662603633" x2="5.927164662603633" y1="35.73783949080951" y2="33.38156647584609"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.940012628342391" x2="9.914316696864876" y1="35.81657844392075" y2="35.81657844392075"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.461425904573654" x2="8.392903420633612" y1="37.61600124906283" y2="37.61600124906283"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.48434686602373" x2="7.369982459183538" y1="39.33429284309519" y2="39.33429284309519"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.6833333333333336" x2="5.927164662603635" y1="23.72291938246423" y2="33.38156647584608"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.83108493932905" x2="8.028087080656674" y1="23.60113107469012" y2="23.60113107469012"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.97259166003632" x2="5.97259166003632" y1="23.55945108167042" y2="20.52711096774605"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.57716466260364" x2="15.57716466260364" y1="35.62739518973012" y2="33.27112217476669"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.47836350249219" x2="19.45266757101467" y1="35.70613414284136" y2="35.70613414284136"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="12.99977677872345" x2="17.93125429478341" y1="37.50555694798344" y2="37.50555694798344"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.18103107350686" x2="16.75" y1="39.2238485420158" y2="39.2238485420158"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.33333333333333" x2="15.57716466260364" y1="23.61247508138485" y2="33.2711221747667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.62259166003632" x2="15.62259166003632" y1="23.44900678059104" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.48108493932905" x2="17.67808708065667" y1="23.49068677361073" y2="23.49068677361073"/>
  </symbol>
  <symbol id="GroundDisconnector:支那变双联地刀_1" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="20.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.916666666666667" x2="15.75" y1="28.83333333333333" y2="28.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.927164662603633" x2="5.927164662603633" y1="35.73783949080951" y2="23.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.070030169158852" x2="10.04433423768134" y1="35.73324511058742" y2="35.73324511058742"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.591443445390117" x2="8.522920961450074" y1="37.61600124906283" y2="37.61600124906283"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.781031073506858" x2="7.333333333333333" y1="39.33429284309519" y2="39.33429284309519"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.83108493932905" x2="8.028087080656674" y1="23.60113107469012" y2="23.60113107469012"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.97259166003632" x2="5.97259166003632" y1="23.55945108167042" y2="20.52711096774605"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.66049799593697" x2="15.66049799593697" y1="35.66666666666666" y2="23.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.64503016915885" x2="19.61933423768134" y1="35.70613414284136" y2="35.70613414284136"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.16644344539012" x2="18.09792096145007" y1="37.50555694798344" y2="37.50555694798344"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.43103107350686" x2="16.83333333333333" y1="39.2238485420158" y2="39.2238485420158"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.62259166003632" x2="15.62259166003632" y1="23.44900678059104" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.48108493932905" x2="17.67808708065667" y1="23.49068677361073" y2="23.49068677361073"/>
  </symbol>
  <symbol id="GroundDisconnector:支那变双联地刀_2" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="20.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.177164662603633" x2="15.66666666666667" y1="33.98783949080951" y2="24.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.070030169158852" x2="10.04433423768134" y1="35.73324511058742" y2="35.73324511058742"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.591443445390117" x2="8.522920961450074" y1="37.61600124906283" y2="37.61600124906283"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.781031073506858" x2="7.333333333333333" y1="39.33429284309519" y2="39.33429284309519"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.97259166003632" x2="5.97259166003632" y1="23.55945108167042" y2="20.52711096774605"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.83108493932905" x2="8.028087080656674" y1="23.60113107469012" y2="23.60113107469012"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.41049799593697" x2="6.333333333333334" y1="34.00000000000001" y2="25.00000000000001"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.64503016915885" x2="19.61933423768134" y1="35.70613414284136" y2="35.70613414284136"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.16644344539012" x2="18.09792096145007" y1="37.50555694798344" y2="37.50555694798344"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.43103107350686" x2="16.83333333333333" y1="39.2238485420158" y2="39.2238485420158"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.62259166003632" x2="15.62259166003632" y1="23.44900678059104" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.48108493932905" x2="17.67808708065667" y1="23.49068677361073" y2="23.49068677361073"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Compensator:10kV电容器_0" viewBox="0,0,24,40">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="4.699999999999999"/>
   <path d="M 12 20.1 L 17.85 20.1 L 17.85 24.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="4.749074074074073" y1="35.35833333333333" y2="35.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.72129629629629" x2="4.72129629629629" y1="33.13611111111111" y2="35.37685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="24.85833333333333" y2="30.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.258333333333324" x2="0.258333333333324" y1="22.10833333333333" y2="33.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.75833333333333" x2="4.75833333333333" y1="22.35833333333333" y2="20.15462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="31.85833333333333" y2="35.37685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="20.04351851851851" y2="24.775"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.024239280774554" x2="4.024239280774554" y1="39.35833333333333" y2="37.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.786111111111114" x2="12.00833333333334" y1="20.10833333333333" y2="20.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="35.35833333333333" y2="39.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.84166666666666" x2="4.024239280774546" y1="39.35833333333333" y2="39.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.84166666666667" x2="19.84166666666667" y1="37.35833333333333" y2="39.35833333333333"/>
   <path d="M 4.75833 25.9572 A 2.96392 1.81747 180 0 1 4.75833 22.3223" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 4.75833 29.5921 A 2.96392 1.81747 180 0 1 4.75833 25.9572" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 4.75833 33.1271 A 2.96392 1.81747 180 0 1 4.75833 29.4921" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="30.85833333333333" y2="30.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="31.85833333333333" y2="31.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="12" y1="4.5" y2="7.166666666666668"/>
   <path d="M 7.26667 12.1 A 4.91667 4.75 -450 1 0 12.0167 7.18333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 7.33333 12.0833 L 12 12.1667 L 12 20.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510778" x2="17.14262023217246" y1="27.75922956383932" y2="26.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="29.36667381975841" y2="30.33114037330986"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.50700202690252" x2="18.32283029297954" y1="31.0857461490052" y2="31.0857461490052"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.28450340888154" x2="18.47116270499357" y1="30.7156109584975" y2="30.7156109584975"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.98783858485353" x2="18.76782752902158" y1="30.34547576798984" y2="30.34547576798984"/>
   <rect fill-opacity="0" height="4.29" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,17.83,27.22) scale(1,1) translate(0,0)" width="2.33" x="16.67" y="25.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="24.56666666666668" y2="25.08015580397416"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510781" x2="18.53565505804314" y1="27.75922956383932" y2="26.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="25.08015580397418" y2="27.73243882624067"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变无融断_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <path d="M 10 9 L 10 0" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="26.83333333333333" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="10.8868007916835" y2="12.55855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="14.23031840279781" y2="12.55855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV支那变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="63.81" xlink:href="logo.png" y="23.68"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,188.653,64.9636) scale(1,1) translate(-1.41679e-14,0)" writing-mode="lr" x="188.65" xml:space="preserve" y="68.45999999999999" zvalue="1"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="334" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,190.153,63.7136) scale(1,1) translate(-1.4501e-14,0)" writing-mode="lr" x="190.15" xml:space="preserve" y="68.20999999999999" zvalue="3"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,218,64.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="218" xml:space="preserve" y="73.69" zvalue="4">35kV支那变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="205" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,288.438,387.25) scale(1,1) translate(0,0)" width="72.88" x="252" y="375.25" zvalue="815"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,288.438,387.25) scale(1,1) translate(0,0)" writing-mode="lr" x="288.44" xml:space="preserve" y="391.75" zvalue="815">小电流接地</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="22" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.625,314.25) scale(1,1) translate(0,0)" width="72.88" x="49.19" y="302.25" zvalue="816"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.625,314.25) scale(1,1) translate(0,0)" writing-mode="lr" x="85.63" xml:space="preserve" y="318.75" zvalue="816">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="157" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,187.531,387.25) scale(1,1) translate(0,0)" width="72.88" x="151.09" y="375.25" zvalue="817"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,187.531,387.25) scale(1,1) translate(0,0)" writing-mode="lr" x="187.53" xml:space="preserve" y="391.75" zvalue="817">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="57" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.625,387.25) scale(1,1) translate(0,0)" width="72.88" x="49.19" y="375.25" zvalue="818"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.625,387.25) scale(1,1) translate(0,0)" writing-mode="lr" x="85.63" xml:space="preserve" y="391.75" zvalue="818">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="53" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.625,350.75) scale(1,1) translate(0,0)" width="72.88" x="49.19" y="338.75" zvalue="819"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.625,350.75) scale(1,1) translate(0,0)" writing-mode="lr" x="85.63" xml:space="preserve" y="355.25" zvalue="819">信号一览</text>
  <line fill="none" id="331" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.000000000000455" x2="373" y1="152.8704926140824" y2="152.8704926140824" zvalue="6"/>
  <line fill="none" id="330" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="374" x2="374" y1="11" y2="1041" zvalue="7"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="260.75" y2="283.5"/>
  <line fill="none" id="328" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.000000000000455" x2="373" y1="622.8704926140824" y2="622.8704926140824" zvalue="9"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="58" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3873" x2="221.3873" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="221.3872" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339" x2="339" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="58" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3873" x2="221.3873" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="221.3872" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339" x2="339" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="58" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3873" x2="221.3873" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="221.3872" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339" x2="339" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="58" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3873" x2="221.3873" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="221.3872" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339" x2="339" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="58" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3873" x2="221.3873" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="221.3872" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339" x2="339" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="58" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3873" x2="221.3873" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="221.3872" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339" x2="339" y1="582" y2="606.6794"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="6" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="366" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="366" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366" x2="366" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="6" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="186" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="186" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186" x2="186" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="276.0000000000001" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="276.0000000000001" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="186.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.0000000000001" x2="276.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="366" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="366" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="276" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366" x2="366" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="6" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="186" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="186" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186" x2="186" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="276.0000000000001" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="276.0000000000001" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="186.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.0000000000001" x2="276.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="366" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="366" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="276" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366" x2="366" y1="1005.0816" y2="1033"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="324" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52,958) scale(1,1) translate(0,0)" writing-mode="lr" x="52" xml:space="preserve" y="964" zvalue="13">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="323" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49,992) scale(1,1) translate(0,0)" writing-mode="lr" x="49" xml:space="preserve" y="998" zvalue="14">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="322" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231,992) scale(1,1) translate(0,0)" writing-mode="lr" x="231" xml:space="preserve" y="998" zvalue="15">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="321" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="47" xml:space="preserve" y="1026" zvalue="16">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="320" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="229" xml:space="preserve" y="1026" zvalue="17">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="319" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,130.528,465.389) scale(1,1) translate(0,9.9772e-14)" writing-mode="lr" x="130.527804904514" xml:space="preserve" y="469.8888914320204" zvalue="18">35kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,613.75,341.361) scale(1,1) translate(0,0)" writing-mode="lr" x="613.75" xml:space="preserve" y="345.86" zvalue="20">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,781.125,290.722) scale(1,1) translate(0,0)" writing-mode="lr" x="781.13" xml:space="preserve" y="295.22" zvalue="35">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1075.64,669.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1075.64" xml:space="preserve" y="674.42" zvalue="40">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1356.04,671.083) scale(1,1) translate(0,0)" writing-mode="lr" x="1356.04" xml:space="preserve" y="675.58" zvalue="41">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="311" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1238.32,579.681) scale(1,1) translate(0,-1.24314e-13)" writing-mode="lr" x="1238.31746031746" xml:space="preserve" y="584.1805555555555" zvalue="44">10kV分段 012</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="303" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,71.5,652.5) scale(1,1) translate(0,0)" writing-mode="lr" x="71.5" xml:space="preserve" y="657" zvalue="47">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,204.399,319.841) scale(1,1) translate(0,0)" writing-mode="lr" x="204.4" xml:space="preserve" y="324.34" zvalue="48">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,309.399,319.841) scale(1,1) translate(0,0)" writing-mode="lr" x="309.4" xml:space="preserve" y="324.34" zvalue="49">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="296" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,249.931,463.833) scale(1,1) translate(0,9.94266e-14)" writing-mode="lr" x="249.9305758608714" xml:space="preserve" y="468.3333358830876" zvalue="51">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="295" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,309,463.833) scale(1,1) translate(0,0)" writing-mode="lr" x="309" xml:space="preserve" y="468.3333358764648" zvalue="52">10kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="294" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,495.75) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="500.25" zvalue="53">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="293" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,521.25) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="525.75" zvalue="54">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,544.25) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="548.75" zvalue="55">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,567.25) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="571.75" zvalue="56">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="290" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,594.25) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="598.75" zvalue="57">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224.054,960) scale(1,1) translate(0,0)" writing-mode="lr" x="224.05" xml:space="preserve" y="966" zvalue="58">ZhiNa-01-2018</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,142.054,992) scale(1,1) translate(0,0)" writing-mode="lr" x="142.05" xml:space="preserve" y="998" zvalue="59">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,321.054,992) scale(1,1) translate(0,0)" writing-mode="lr" x="321.05" xml:space="preserve" y="998" zvalue="60">20200901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="286" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45,179) scale(1,1) translate(0,0)" writing-mode="lr" x="45" xml:space="preserve" y="183.5" zvalue="61">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225,179) scale(1,1) translate(0,0)" writing-mode="lr" x="225" xml:space="preserve" y="183.5" zvalue="62">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.6875,203.25) scale(1,1) translate(0,0)" writing-mode="lr" x="52.69" xml:space="preserve" y="207.75" zvalue="63">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46.6875,251) scale(1,1) translate(0,0)" writing-mode="lr" x="46.69" xml:space="preserve" y="255.5" zvalue="65">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="281" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.75,250.5) scale(1,1) translate(0,0)" writing-mode="lr" x="233.75" xml:space="preserve" y="255" zvalue="66">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="280" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46.6875,274) scale(1,1) translate(0,0)" writing-mode="lr" x="46.69" xml:space="preserve" y="278.5" zvalue="67">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="279" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.75,273.5) scale(1,1) translate(0,0)" writing-mode="lr" x="233.75" xml:space="preserve" y="278" zvalue="68">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="276" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.1875,227.25) scale(1,1) translate(0,0)" writing-mode="lr" x="52.19" xml:space="preserve" y="231.75" zvalue="73">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="275" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.75,227) scale(1,1) translate(0,0)" writing-mode="lr" x="232.75" xml:space="preserve" y="231.5" zvalue="74">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" x="747.3671875" xml:space="preserve" y="498.6961811913385" zvalue="82">#1主变   </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="747.3671875" xml:space="preserve" y="515.6961811913385" zvalue="82">2500KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1147.6,655.933) scale(1,1) translate(0,0)" writing-mode="lr" x="1147.6" xml:space="preserve" y="660.4299999999999" zvalue="100">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,781.859,447.667) scale(1,1) translate(0,0)" writing-mode="lr" x="781.86" xml:space="preserve" y="452.17" zvalue="132">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,792.391,389.694) scale(1,1) translate(0,0)" writing-mode="lr" x="792.39" xml:space="preserve" y="394.19" zvalue="135">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" x="1371.765625" xml:space="preserve" y="498.9635425143772" zvalue="148">#2主变     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1371.765625" xml:space="preserve" y="515.9635425143772" zvalue="148">5000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1404.35,447.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1404.35" xml:space="preserve" y="452.17" zvalue="151">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1415.72,387.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1415.72" xml:space="preserve" y="392.19" zvalue="154">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,794.183,825.015) scale(1,1) translate(0,0)" writing-mode="lr" x="794.1799999999999" xml:space="preserve" y="829.52" zvalue="285">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,779.321,952.25) scale(1,1) translate(0,0)" writing-mode="lr" x="779.3200000000001" xml:space="preserve" y="956.75" zvalue="295">10kV芒朵线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="371" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,627.832,735.126) scale(1,1) translate(0,0)" writing-mode="lr" x="627.83" xml:space="preserve" y="739.63" zvalue="422">052</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="368" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,685.472,789.111) scale(1,1) translate(0,0)" writing-mode="lr" x="685.47" xml:space="preserve" y="793.61" zvalue="424">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="367" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,629.5,799.528) scale(1,1) translate(0,0)" writing-mode="lr" x="629.5" xml:space="preserve" y="804.03" zvalue="428">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="366" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,671.183,812.015) scale(1,1) translate(0,0)" writing-mode="lr" x="671.1799999999999" xml:space="preserve" y="816.52" zvalue="431">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="365" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,660.148,972.375) scale(1,1) translate(0,0)" writing-mode="lr" x="660.15" xml:space="preserve" y="976.88" zvalue="437">10kV2号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="364" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,673.955,944.889) scale(1,1) translate(0,0)" writing-mode="lr" x="673.95" xml:space="preserve" y="949.39" zvalue="440">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="392" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,753.767,736.62) scale(1,1) translate(0,0)" writing-mode="lr" x="753.77" xml:space="preserve" y="741.12" zvalue="443">053</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="395" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,802.972,789.111) scale(1,1) translate(0,0)" writing-mode="lr" x="802.97" xml:space="preserve" y="793.61" zvalue="450">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="407" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,836.392,642.62) scale(1,1) translate(0,0)" writing-mode="lr" x="836.39" xml:space="preserve" y="647.12" zvalue="457">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="412" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1460.77,646.12) scale(1,1) translate(3.37775e-12,4.94753e-13)" writing-mode="lr" x="1460.77" xml:space="preserve" y="650.62" zvalue="461">002</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="414" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,812.683,177.625) scale(1,1) translate(0,0)" writing-mode="lr" x="812.6799999999999" xml:space="preserve" y="182.13" zvalue="464">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="420" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,757.75,317.778) scale(1,1) translate(0,0)" writing-mode="lr" x="757.75" xml:space="preserve" y="322.28" zvalue="468">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="421" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,757.75,262.778) scale(1,1) translate(0,0)" writing-mode="lr" x="757.75" xml:space="preserve" y="267.28" zvalue="472">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="427" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,750.25,420.278) scale(1,1) translate(0,0)" writing-mode="lr" x="750.25" xml:space="preserve" y="424.78" zvalue="476">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="428" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1372.75,414.419) scale(1,1) translate(0,0)" writing-mode="lr" x="1372.75" xml:space="preserve" y="418.92" zvalue="479">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="435" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1434.23,189.25) scale(1,1) translate(9.24746e-13,0)" writing-mode="lr" x="1434.23" xml:space="preserve" y="193.75" zvalue="484">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1406.88,297) scale(1,1) translate(0,0)" writing-mode="lr" x="1406.88" xml:space="preserve" y="301.5" zvalue="486">3811</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="443" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1119.88,97.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1119.88" xml:space="preserve" y="102.13" zvalue="490">35kV西那线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="447" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1099.22,261.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1099.22" xml:space="preserve" y="265.67" zvalue="493">351</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="452" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1103,310.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1103" xml:space="preserve" y="314.94" zvalue="497">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="456" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1104.99,209.194) scale(1,1) translate(0,0)" writing-mode="lr" x="1104.99" xml:space="preserve" y="213.69" zvalue="501">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="459" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1174.36,286.528) scale(1,1) translate(0,0)" writing-mode="lr" x="1174.36" xml:space="preserve" y="291.03" zvalue="505">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="463" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1174.36,237.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1174.36" xml:space="preserve" y="242.42" zvalue="509">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="467" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1172.86,185.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1172.86" xml:space="preserve" y="190.17" zvalue="513">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="474" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1104.5,168.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1104.5" xml:space="preserve" y="173.42" zvalue="517">9</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="476" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,594.515,536.375) scale(1,1) translate(0,0)" writing-mode="lr" x="594.51" xml:space="preserve" y="540.88" zvalue="522">10kVⅠ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="482" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,562,643.679) scale(1,1) translate(0,0)" writing-mode="lr" x="562" xml:space="preserve" y="648.1799999999999" zvalue="526">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="484" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1664.51,536.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1664.51" xml:space="preserve" y="540.88" zvalue="530">10kVⅡ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="483" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1635.78,642.679) scale(1,1) translate(0,0)" writing-mode="lr" x="1635.78" xml:space="preserve" y="647.1799999999999" zvalue="533">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="494" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,922.183,825.015) scale(1,1) translate(0,0)" writing-mode="lr" x="922.1799999999999" xml:space="preserve" y="829.52" zvalue="537">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="493" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,906.321,952.25) scale(1,1) translate(0,0)" writing-mode="lr" x="906.3200000000001" xml:space="preserve" y="956.75" zvalue="540">10kV支那街道线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="491" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,881.767,736.62) scale(1,1) translate(0,0)" writing-mode="lr" x="881.77" xml:space="preserve" y="741.12" zvalue="542">054</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="490" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,926.972,789.111) scale(1,1) translate(2.04275e-13,0)" writing-mode="lr" x="926.97" xml:space="preserve" y="793.61" zvalue="546">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="509" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1123.27,736.62) scale(1,1) translate(0,0)" writing-mode="lr" x="1123.27" xml:space="preserve" y="741.12" zvalue="552">056</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="507" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1168.47,789.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1168.47" xml:space="preserve" y="793.61" zvalue="555">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1166.12,827.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1166.13" xml:space="preserve" y="832.25" zvalue="563">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="521" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1143.6,952.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1143.6" xml:space="preserve" y="956.75" zvalue="565">10kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="527" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1402.43,825.015) scale(1,1) translate(0,0)" writing-mode="lr" x="1402.43" xml:space="preserve" y="829.52" zvalue="571">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="526" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1386.2,952.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1386.195767195767" xml:space="preserve" y="956.75" zvalue="574">10kV支东线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="525" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1362.02,736.62) scale(1,1) translate(0,0)" writing-mode="lr" x="1362.02" xml:space="preserve" y="741.12" zvalue="576">057</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="524" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1407.22,789.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1407.22" xml:space="preserve" y="793.61" zvalue="580">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="551" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1418,895.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1418" xml:space="preserve" y="900.25" zvalue="598">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1501.33,736.126) scale(1,1) translate(0,0)" writing-mode="lr" x="1501.33" xml:space="preserve" y="740.63" zvalue="601">058</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1554.97,790.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1554.97" xml:space="preserve" y="794.61" zvalue="603">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1544.68,824.015) scale(1,1) translate(0,0)" writing-mode="lr" x="1544.68" xml:space="preserve" y="828.52" zvalue="610">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1532.69,952.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1532.69" xml:space="preserve" y="956.75" zvalue="618">10kV3号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1502.75,830.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1502.75" xml:space="preserve" y="835.13" zvalue="620">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1661.33,736.126) scale(1,1) translate(0,0)" writing-mode="lr" x="1661.33" xml:space="preserve" y="740.63" zvalue="623">059</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1718.97,789.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1718.97" xml:space="preserve" y="793.61" zvalue="625">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1704.68,824.015) scale(1,1) translate(0,0)" writing-mode="lr" x="1704.68" xml:space="preserve" y="828.52" zvalue="629">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1688.19,952.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1688.19" xml:space="preserve" y="956.75" zvalue="635">10kV4号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1662.5,836) scale(1,1) translate(0,0)" writing-mode="lr" x="1662.5" xml:space="preserve" y="840.5" zvalue="638">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,481.541,734.626) scale(1,1) translate(0,0)" writing-mode="lr" x="481.54" xml:space="preserve" y="739.13" zvalue="647">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,536.222,788.611) scale(1,1) translate(0,0)" writing-mode="lr" x="536.22" xml:space="preserve" y="793.11" zvalue="649">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,485.25,800.028) scale(1,1) translate(0,0)" writing-mode="lr" x="485.25" xml:space="preserve" y="804.53" zvalue="653">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,525.933,811.515) scale(1,1) translate(0,0)" writing-mode="lr" x="525.9299999999999" xml:space="preserve" y="816.02" zvalue="656">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,515,972.875) scale(1,1) translate(0,0)" writing-mode="lr" x="515" xml:space="preserve" y="977.38" zvalue="659">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,529.371,946.722) scale(1,1) translate(0,0)" writing-mode="lr" x="529.37" xml:space="preserve" y="951.22" zvalue="661">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,629.111,859.111) scale(1,1) translate(0,0)" writing-mode="lr" x="629.11" xml:space="preserve" y="863.61" zvalue="668">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,486.111,859.111) scale(1,1) translate(0,0)" writing-mode="lr" x="486.11" xml:space="preserve" y="863.61" zvalue="671">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1039.18,825.015) scale(1,1) translate(0,0)" writing-mode="lr" x="1039.18" xml:space="preserve" y="829.52" zvalue="674">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1023.32,952.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1023.32" xml:space="preserve" y="956.75" zvalue="677">10kV备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,998.767,736.62) scale(1,1) translate(0,0)" writing-mode="lr" x="998.77" xml:space="preserve" y="741.12" zvalue="679">055</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1043.97,789.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1043.97" xml:space="preserve" y="793.61" zvalue="683">17</text>
 </g>
 <g id="ButtonClass">
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="252" y="375.25" zvalue="815"/></g>
  <g href="全站公用20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="49.19" y="302.25" zvalue="816"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="151.09" y="375.25" zvalue="817"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="49.19" y="375.25" zvalue="818"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="49.19" y="338.75" zvalue="819"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="508">
   <path class="kv35" d="M 655.5 344.61 L 1586 344.61" stroke-width="6" zvalue="19"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674399944707" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674399944707"/></metadata>
  <path d="M 655.5 344.61 L 1586 344.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="429">
   <path class="kv10" d="M 462.33 690.5 L 1209.75 690.5" stroke-width="6" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674399879171" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674399879171"/></metadata>
  <path d="M 462.33 690.5 L 1209.75 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="426">
   <path class="kv10" d="M 1271 690.75 L 1829 690.75" stroke-width="6" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674400010243" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674400010243"/></metadata>
  <path d="M 1271 690.75 L 1829 690.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="449">
   <use class="kv35" height="30" transform="rotate(0,809.75,289.944) scale(0.833333,0.833333) translate(160.7,55.4889)" width="15" x="803.5" xlink:href="#Disconnector:刀闸_0" y="277.444442987442" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453797609475" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453797609475"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,809.75,289.944) scale(0.833333,0.833333) translate(160.7,55.4889)" width="15" x="803.5" y="277.444442987442"/></g>
  <g id="574">
   <use class="kv10" height="36" transform="rotate(0,1168.98,643.679) scale(1.30867,1.22026) translate(-273.564,-112.22)" width="14" x="1159.821428571429" xlink:href="#Disconnector:手车刀闸_0" y="621.7142857142857" zvalue="99"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453797543939" ObjectName="10kV分段0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453797543939"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1168.98,643.679) scale(1.30867,1.22026) translate(-273.564,-112.22)" width="14" x="1159.821428571429" y="621.7142857142857"/></g>
  <g id="633">
   <use class="kv35" height="30" transform="rotate(0,810.863,388.694) scale(0.833333,0.833333) translate(160.923,75.2389)" width="15" x="804.6131037206526" xlink:href="#Disconnector:刀闸_0" y="376.1944428814782" zvalue="133"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453797478403" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453797478403"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,810.863,388.694) scale(0.833333,0.833333) translate(160.923,75.2389)" width="15" x="804.6131037206526" y="376.1944428814782"/></g>
  <g id="664">
   <use class="kv35" height="30" transform="rotate(0,1434.19,388.694) scale(0.833333,0.833333) translate(285.589,75.2389)" width="15" x="1427.944444444445" xlink:href="#Disconnector:刀闸_0" y="376.1944427490234" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453797412867" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453797412867"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1434.19,388.694) scale(0.833333,0.833333) translate(285.589,75.2389)" width="15" x="1427.944444444445" y="376.1944427490234"/></g>
  <g id="162">
   <use class="kv10" height="30" transform="rotate(0,778.808,825.015) scale(0.833333,-0.833333) translate(154.512,-1817.53)" width="15" x="772.5582010582013" xlink:href="#Disconnector:刀闸_0" y="812.5153504566865" zvalue="284"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453797347331" ObjectName="10kV芒朵线0536隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453797347331"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,778.808,825.015) scale(0.833333,-0.833333) translate(154.512,-1817.53)" width="15" x="772.5582010582013" y="812.5153504566865"/></g>
  <g id="382">
   <use class="kv10" height="30" transform="rotate(0,658.558,813.015) scale(0.833333,-0.833333) translate(130.462,-1791.13)" width="15" x="652.3082010582013" xlink:href="#Disconnector:刀闸_0" y="800.5153503417969" zvalue="430"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453798395907" ObjectName="10kV2号电容器0526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453798395907"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,658.558,813.015) scale(0.833333,-0.833333) translate(130.462,-1791.13)" width="15" x="652.3082010582013" y="800.5153503417969"/></g>
  <g id="438">
   <use class="kv35" height="30" transform="rotate(0,1435.75,299) scale(1.25,1.25) translate(-285.275,-56.05)" width="15" x="1426.375" xlink:href="#Disconnector:令克_0" y="280.25" zvalue="485"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453799641091" ObjectName="35kV1号站用变3811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453799641091"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1435.75,299) scale(1.25,1.25) translate(-285.275,-56.05)" width="15" x="1426.375" y="280.25"/></g>
  <g id="448">
   <use class="kv35" height="30" transform="rotate(0,1121.75,311.444) scale(0.833333,0.833333) translate(223.1,59.7889)" width="15" x="1115.5" xlink:href="#Disconnector:刀闸_0" y="298.9444428814782" zvalue="496"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453799772163" ObjectName="35kV西那线3511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453799772163"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1121.75,311.444) scale(0.833333,0.833333) translate(223.1,59.7889)" width="15" x="1115.5" y="298.9444428814782"/></g>
  <g id="453">
   <use class="kv35" height="30" transform="rotate(0,1121.61,210.194) scale(0.833333,0.833333) translate(223.073,39.5389)" width="15" x="1115.363103720653" xlink:href="#Disconnector:刀闸_0" y="197.6944428814782" zvalue="500"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453799837699" ObjectName="35kV西那线3516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453799837699"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1121.61,210.194) scale(0.833333,0.833333) translate(223.073,39.5389)" width="15" x="1115.363103720653" y="197.6944428814782"/></g>
  <g id="470">
   <use class="kv35" height="30" transform="rotate(90,1103.5,184.667) scale(0.833333,-0.833333) translate(219.45,-408.767)" width="15" x="1097.25" xlink:href="#Disconnector:刀闸_0" y="172.1666666666666" zvalue="516"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453800361987" ObjectName="35kV西那线3519隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453800361987"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1103.5,184.667) scale(0.833333,-0.833333) translate(219.45,-408.767)" width="15" x="1097.25" y="172.1666666666666"/></g>
  <g id="479">
   <use class="kv10" height="36" transform="rotate(0,595.063,643.679) scale(1.30867,1.22026) translate(-138.195,-112.22)" width="14" x="585.9025971615879" xlink:href="#Disconnector:手车刀闸_0" y="621.7142857142857" zvalue="525"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453800493059" ObjectName="10kVⅠ段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453800493059"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,595.063,643.679) scale(1.30867,1.22026) translate(-138.195,-112.22)" width="14" x="585.9025971615879" y="621.7142857142857"/></g>
  <g id="487">
   <use class="kv10" height="36" transform="rotate(0,1665.06,643.679) scale(1.30867,1.22026) translate(-390.574,-112.22)" width="14" x="1655.902597161588" xlink:href="#Disconnector:手车刀闸_0" y="621.7142857142857" zvalue="531"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453800558595" ObjectName="10kVⅡ段母线电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453800558595"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1665.06,643.679) scale(1.30867,1.22026) translate(-390.574,-112.22)" width="14" x="1655.902597161588" y="621.7142857142857"/></g>
  <g id="505">
   <use class="kv10" height="30" transform="rotate(0,906.808,825.015) scale(0.833333,-0.833333) translate(180.112,-1817.53)" width="15" x="900.5582010582013" xlink:href="#Disconnector:刀闸_0" y="812.5153503417969" zvalue="536"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453800951811" ObjectName="10kV支那街道线0546隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453800951811"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,906.808,825.015) scale(0.833333,-0.833333) translate(180.112,-1817.53)" width="15" x="900.5582010582013" y="812.5153503417969"/></g>
  <g id="518">
   <use class="kv10" height="30" transform="rotate(0,1147.25,832.75) scale(1.25,-1.25) translate(-227.575,-1495.2)" width="15" x="1137.875" xlink:href="#Disconnector:令克_0" y="814" zvalue="562"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453801213955" ObjectName="10kV2号站用变0566隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453801213955"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1147.25,832.75) scale(1.25,-1.25) translate(-227.575,-1495.2)" width="15" x="1137.875" y="814"/></g>
  <g id="537">
   <use class="kv10" height="30" transform="rotate(0,1387.06,825.015) scale(0.833333,-0.833333) translate(276.162,-1817.53)" width="15" x="1380.808201058201" xlink:href="#Disconnector:刀闸_0" y="812.5153503417969" zvalue="570"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453801672707" ObjectName="10kV支东线0576隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453801672707"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1387.06,825.015) scale(0.833333,-0.833333) translate(276.162,-1817.53)" width="15" x="1380.808201058201" y="812.5153503417969"/></g>
  <g id="9">
   <use class="kv10" height="30" transform="rotate(0,1532.06,825.015) scale(0.833333,-0.833333) translate(305.162,-1817.53)" width="15" x="1525.808201058201" xlink:href="#Disconnector:刀闸_0" y="812.5153504566865" zvalue="609"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453802131459" ObjectName="10kV3号电容器0586隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453802131459"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1532.06,825.015) scale(0.833333,-0.833333) translate(305.162,-1817.53)" width="15" x="1525.808201058201" y="812.5153504566865"/></g>
  <g id="40">
   <use class="kv10" height="30" transform="rotate(0,1692.06,825.015) scale(0.833333,-0.833333) translate(337.162,-1817.53)" width="15" x="1685.808201058201" xlink:href="#Disconnector:刀闸_0" y="812.5153504566865" zvalue="628"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453802786819" ObjectName="10kV4号电容器0596隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453802786819"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1692.06,825.015) scale(0.833333,-0.833333) translate(337.162,-1817.53)" width="15" x="1685.808201058201" y="812.5153504566865"/></g>
  <g id="58">
   <use class="kv10" height="30" transform="rotate(0,513.308,812.515) scale(0.833333,-0.833333) translate(101.412,-1790.03)" width="15" x="507.0582010582013" xlink:href="#Disconnector:刀闸_0" y="800.015342827292" zvalue="654"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453797806083" ObjectName="10kV1号电容器0516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453797806083"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,513.308,812.515) scale(0.833333,-0.833333) translate(101.412,-1790.03)" width="15" x="507.0582010582013" y="800.015342827292"/></g>
  <g id="42">
   <use class="kv10" height="30" transform="rotate(0,1023.81,825.015) scale(0.833333,-0.833333) translate(203.512,-1817.53)" width="15" x="1017.558201058201" xlink:href="#Disconnector:刀闸_0" y="812.5153503417969" zvalue="673"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453903384579" ObjectName="10kV备用线0556隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453903384579"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1023.81,825.015) scale(0.833333,-0.833333) translate(203.512,-1817.53)" width="15" x="1017.558201058201" y="812.5153503417969"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="309">
   <path class="kv10" d="M 1168.98 664.42 L 1168.98 690.5" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="574@1" LinkObjectIDznd="429@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1168.98 664.42 L 1168.98 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="264">
   <path class="kv10" d="M 1168.98 622.93 L 1168.98 605.09 L 1213.64 605.09" stroke-width="1" zvalue="102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="574@0" LinkObjectIDznd="576@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1168.98 622.93 L 1168.98 605.09 L 1213.64 605.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="252">
   <path class="kv35" d="M 810.09 493.99 L 810.09 467" stroke-width="1" zvalue="131"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="549@0" LinkObjectIDznd="630@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.09 493.99 L 810.09 467" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv35" d="M 810.91 429.82 L 810.91 400.98" stroke-width="1" zvalue="134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="630@0" LinkObjectIDznd="633@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.91 429.82 L 810.91 400.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv35" d="M 810.94 376.61 L 810.94 344.61" stroke-width="1" zvalue="136"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="633@0" LinkObjectIDznd="508@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.94 376.61 L 810.94 344.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="245">
   <path class="kv35" d="M 1433.36 493.99 L 1433.36 467" stroke-width="1" zvalue="150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="667@0" LinkObjectIDznd="666@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.36 493.99 L 1433.36 467" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv35" d="M 1434.25 429.82 L 1434.25 400.98" stroke-width="1" zvalue="153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="666@0" LinkObjectIDznd="664@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1434.25 429.82 L 1434.25 400.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="243">
   <path class="kv35" d="M 1434.27 376.61 L 1434.27 344.61" stroke-width="1" zvalue="155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="664@0" LinkObjectIDznd="508@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1434.27 376.61 L 1434.27 344.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv10" d="M 778.81 891.01 L 778.88 837.1" stroke-width="1" zvalue="288"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@0" LinkObjectIDznd="162@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 778.81 891.01 L 778.88 837.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="283">
   <path class="kv10" d="M 1259.35 605.22 L 1310.44 605.22 L 1310.44 690.75" stroke-width="1" zvalue="401"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="576@1" LinkObjectIDznd="426@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1259.35 605.22 L 1310.44 605.22 L 1310.44 690.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="386">
   <path class="kv10" d="M 675.42 771.17 L 658.61 771.17" stroke-width="1" zvalue="425"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="387@0" LinkObjectIDznd="380" MaxPinNum="2"/>
   </metadata>
  <path d="M 675.42 771.17 L 658.61 771.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="385">
   <path class="kv10" d="M 659.06 719.29 L 659.06 690.5" stroke-width="1" zvalue="426"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="388@1" LinkObjectIDznd="429@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 659.06 719.29 L 659.06 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="383">
   <path class="kv10" d="M 641.33 784.92 L 658.61 784.92" stroke-width="1" zvalue="429"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="384@0" LinkObjectIDznd="380" MaxPinNum="2"/>
   </metadata>
  <path d="M 641.33 784.92 L 658.61 784.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="380">
   <path class="kv10" d="M 658.61 800.73 L 658.61 756.46" stroke-width="1" zvalue="433"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="382@1" LinkObjectIDznd="388@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 658.61 800.73 L 658.61 756.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="390">
   <path class="kv10" d="M 778.86 812.73 L 778.86 756.46" stroke-width="1" zvalue="443"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162@1" LinkObjectIDznd="389@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 778.86 812.73 L 778.86 756.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="391">
   <path class="kv10" d="M 778.06 719.29 L 778.06 690.5" stroke-width="1" zvalue="444"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="389@1" LinkObjectIDznd="429@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 778.06 719.29 L 778.06 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="397">
   <path class="kv10" d="M 792.92 771.17 L 778.86 771.17" stroke-width="1" zvalue="451"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="396@0" LinkObjectIDznd="390" MaxPinNum="2"/>
   </metadata>
  <path d="M 792.92 771.17 L 778.86 771.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="405">
   <path class="kv10" d="M 810.06 566.71 L 810.06 625.29" stroke-width="1" zvalue="457"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="549@1" LinkObjectIDznd="403@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.06 566.71 L 810.06 625.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="406">
   <path class="kv10" d="M 810.06 662.46 L 810.06 690.5" stroke-width="1" zvalue="458"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="403@0" LinkObjectIDznd="429@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.06 662.46 L 810.06 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="410">
   <path class="kv10" d="M 1433.33 566.71 L 1433.33 628.79" stroke-width="1" zvalue="461"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="667@1" LinkObjectIDznd="409@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.33 566.71 L 1433.33 628.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="411">
   <path class="kv10" d="M 1433.81 665.96 L 1433.81 690.75" stroke-width="1" zvalue="462"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="409@0" LinkObjectIDznd="426@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.81 665.96 L 1433.81 690.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="415">
   <path class="kv35" d="M 809.8 302.23 L 809.8 344.61" stroke-width="1" zvalue="464"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="449@1" LinkObjectIDznd="508@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.8 302.23 L 809.8 344.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="416">
   <path class="kv35" d="M 809.82 277.86 L 809.75 255.3" stroke-width="1" zvalue="465"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="449@0" LinkObjectIDznd="413@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.82 277.86 L 809.75 255.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="419">
   <path class="kv35" d="M 794.83 316.92 L 809.8 316.92" stroke-width="1" zvalue="469"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="417@0" LinkObjectIDznd="415" MaxPinNum="2"/>
   </metadata>
  <path d="M 794.83 316.92 L 809.8 316.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="423">
   <path class="kv35" d="M 794.83 261.92 L 809.77 261.92" stroke-width="1" zvalue="473"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="422@0" LinkObjectIDznd="416" MaxPinNum="2"/>
   </metadata>
  <path d="M 794.83 261.92 L 809.77 261.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="425">
   <path class="kv35" d="M 792.33 419.42 L 810.91 419.42" stroke-width="1" zvalue="476"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="424@0" LinkObjectIDznd="251" MaxPinNum="2"/>
   </metadata>
  <path d="M 792.33 419.42 L 810.91 419.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="431">
   <path class="kv35" d="M 1414.83 413.56 L 1434.25 413.56" stroke-width="1" zvalue="480"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="430@0" LinkObjectIDznd="244" MaxPinNum="2"/>
   </metadata>
  <path d="M 1414.83 413.56 L 1434.25 413.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="433">
   <path class="kv10" d="M 1414.21 605.16 L 1433.33 605.16" stroke-width="1" zvalue="482"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="432@0" LinkObjectIDznd="410" MaxPinNum="2"/>
   </metadata>
  <path d="M 1414.21 605.16 L 1433.33 605.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="439">
   <path class="kv35" d="M 1434.85 268.65 L 1434.85 282.44" stroke-width="1" zvalue="486"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="434@0" LinkObjectIDznd="438@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1434.85 268.65 L 1434.85 282.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="440">
   <path class="kv35" d="M 1435.65 314.31 L 1435.65 344.61" stroke-width="1" zvalue="487"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="438@1" LinkObjectIDznd="243" MaxPinNum="2"/>
   </metadata>
  <path d="M 1435.65 314.31 L 1435.65 344.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="450">
   <path class="kv35" d="M 1121.66 280.5 L 1121.66 299.36" stroke-width="1" zvalue="497"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="444@1" LinkObjectIDznd="448@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1121.66 280.5 L 1121.66 299.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="451">
   <path class="kv35" d="M 1121.8 323.73 L 1121.8 344.61" stroke-width="1" zvalue="498"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="448@1" LinkObjectIDznd="508@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1121.8 323.73 L 1121.8 344.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="454">
   <path class="kv35" d="M 1121.69 146.07 L 1121.69 198.11" stroke-width="1" zvalue="501"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="441@0" LinkObjectIDznd="453@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1121.69 146.07 L 1121.69 198.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="455">
   <path class="kv35" d="M 1121.66 222.48 L 1121.66 243.32" stroke-width="1" zvalue="502"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="453@1" LinkObjectIDznd="444@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1121.66 222.48 L 1121.66 243.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="461">
   <path class="kv35" d="M 1137.28 285.67 L 1121.66 285.67" stroke-width="1" zvalue="506"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="460@0" LinkObjectIDznd="450" MaxPinNum="2"/>
   </metadata>
  <path d="M 1137.28 285.67 L 1121.66 285.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="465">
   <path class="kv35" d="M 1137.28 235.67 L 1121.66 235.67" stroke-width="1" zvalue="510"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="462@0" LinkObjectIDznd="455" MaxPinNum="2"/>
   </metadata>
  <path d="M 1137.28 235.67 L 1121.66 235.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="468">
   <path class="kv35" d="M 1138.53 184.67 L 1121.69 184.67" stroke-width="1" zvalue="513"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="466@0" LinkObjectIDznd="454" MaxPinNum="2"/>
   </metadata>
  <path d="M 1138.53 184.67 L 1121.69 184.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="472">
   <path class="kv35" d="M 1082.5 174.13 L 1082.5 184.67 L 1091.41 184.74" stroke-width="1" zvalue="518"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="469@0" LinkObjectIDznd="470@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1082.5 174.13 L 1082.5 184.67 L 1091.41 184.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="473">
   <path class="kv35" d="M 1115.79 184.72 L 1121.69 184.72" stroke-width="1" zvalue="519"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="470@1" LinkObjectIDznd="454" MaxPinNum="2"/>
   </metadata>
  <path d="M 1115.79 184.72 L 1121.69 184.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="480">
   <path class="kv10" d="M 595.06 609.05 L 595.06 622.93" stroke-width="1" zvalue="526"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="475@0" LinkObjectIDznd="479@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 595.06 609.05 L 595.06 622.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="481">
   <path class="kv10" d="M 595.06 664.42 L 595.06 690.5" stroke-width="1" zvalue="527"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="479@1" LinkObjectIDznd="429@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 595.06 664.42 L 595.06 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="486">
   <path class="kv10" d="M 1665.06 609.05 L 1665.06 622.93" stroke-width="1" zvalue="532"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="488@0" LinkObjectIDznd="487@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1665.06 609.05 L 1665.06 622.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="485">
   <path class="kv10" d="M 1665.06 664.42 L 1665.06 690.75" stroke-width="1" zvalue="534"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="487@1" LinkObjectIDznd="426@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1665.06 664.42 L 1665.06 690.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="504">
   <path class="kv10" d="M 906.81 891.01 L 906.88 837.1" stroke-width="1" zvalue="538"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="503@0" LinkObjectIDznd="505@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 906.81 891.01 L 906.88 837.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="500">
   <path class="kv10" d="M 906.86 812.73 L 906.86 756.46" stroke-width="1" zvalue="543"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="505@1" LinkObjectIDznd="501@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 906.86 812.73 L 906.86 756.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="499">
   <path class="kv10" d="M 906.06 719.29 L 906.06 690.5" stroke-width="1" zvalue="544"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="501@1" LinkObjectIDznd="429@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 906.06 719.29 L 906.06 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="497">
   <path class="kv10" d="M 916.92 771.17 L 906.86 771.17" stroke-width="1" zvalue="547"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="498@0" LinkObjectIDznd="500" MaxPinNum="2"/>
   </metadata>
  <path d="M 916.92 771.17 L 906.86 771.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="514">
   <path class="kv10" d="M 1147.56 719.29 L 1147.56 690.5" stroke-width="1" zvalue="553"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="515@1" LinkObjectIDznd="429@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1147.56 719.29 L 1147.56 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="512">
   <path class="kv10" d="M 1158.42 771.17 L 1147.56 771.17" stroke-width="1" zvalue="556"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="513@0" LinkObjectIDznd="516" MaxPinNum="2"/>
   </metadata>
  <path d="M 1158.42 771.17 L 1147.56 771.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="516">
   <path class="kv10" d="M 1147.56 756.46 L 1147.56 817.44" stroke-width="1" zvalue="559"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="515@0" LinkObjectIDznd="518@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1147.56 756.46 L 1147.56 817.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="520">
   <path class="kv10" d="M 1147.35 881.16 L 1147.35 849.31" stroke-width="1" zvalue="565"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="519@0" LinkObjectIDznd="518@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1147.35 881.16 L 1147.35 849.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="523">
   <path class="kv10" d="M 1121.22 874.92 L 1121.22 868 L 1147.35 868" stroke-width="1" zvalue="568"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="522@0" LinkObjectIDznd="520" MaxPinNum="2"/>
   </metadata>
  <path d="M 1121.22 874.92 L 1121.22 868 L 1147.35 868" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="536">
   <path class="kv10" d="M 1387.06 895.01 L 1387.13 837.1" stroke-width="1" zvalue="572"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="535@0" LinkObjectIDznd="537@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1387.06 895.01 L 1387.13 837.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="533">
   <path class="kv10" d="M 1387.11 812.73 L 1387.11 756.46" stroke-width="1" zvalue="577"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="537@1" LinkObjectIDznd="534@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1387.11 812.73 L 1387.11 756.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="532">
   <path class="kv10" d="M 1386.31 719.29 L 1386.31 690.75" stroke-width="1" zvalue="578"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="534@1" LinkObjectIDznd="426@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1386.31 719.29 L 1386.31 690.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="530">
   <path class="kv10" d="M 1397.17 771.17 L 1387.11 771.17" stroke-width="1" zvalue="581"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="531@0" LinkObjectIDznd="533" MaxPinNum="2"/>
   </metadata>
  <path d="M 1397.17 771.17 L 1387.11 771.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="544">
   <path class="kv10" d="M 1389.75 771.17 L 1338.75 771.17 L 1338.75 782.88" stroke-width="1" zvalue="591"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="530" LinkObjectIDznd="538@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1389.75 771.17 L 1338.75 771.17 L 1338.75 782.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="545">
   <path class="kv10" d="M 1362.47 781.17 L 1362.47 771.17" stroke-width="1" zvalue="592"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="540@0" LinkObjectIDznd="544" MaxPinNum="2"/>
   </metadata>
  <path d="M 1362.47 781.17 L 1362.47 771.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="547">
   <path class="kv10" d="M 1366.22 886.17 L 1366.22 877.25 L 1387.08 877.25" stroke-width="1" zvalue="595"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="546@0" LinkObjectIDznd="536" MaxPinNum="2"/>
   </metadata>
  <path d="M 1366.22 886.17 L 1366.22 877.25 L 1387.08 877.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="550">
   <path class="kv10" d="M 1400.92 877.25 L 1387.08 877.25" stroke-width="1" zvalue="598"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="548@0" LinkObjectIDznd="536" MaxPinNum="2"/>
   </metadata>
  <path d="M 1400.92 877.25 L 1387.08 877.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv10" d="M 1532.56 720.29 L 1532.56 690.75" stroke-width="1" zvalue="605"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@1" LinkObjectIDznd="426@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1532.56 720.29 L 1532.56 690.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv10" d="M 1532.11 812.73 L 1532.11 757.46" stroke-width="1" zvalue="611"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@1" LinkObjectIDznd="15@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1532.11 812.73 L 1532.11 757.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv10" d="M 1692.56 720.29 L 1692.56 690.75" stroke-width="1" zvalue="627"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@1" LinkObjectIDznd="426@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1692.56 720.29 L 1692.56 690.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv10" d="M 1692.06 863.89 L 1692.13 837.1" stroke-width="1" zvalue="636"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="40@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1692.06 863.89 L 1692.13 837.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv10" d="M 1670.5 864 L 1670.5 938 L 1692.06 938 L 1692.06 863.89" stroke-width="1" zvalue="639"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="29@0" LinkObjectIDznd="30" MaxPinNum="2"/>
   </metadata>
  <path d="M 1670.5 864 L 1670.5 938 L 1692.06 938 L 1692.06 863.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv10" d="M 1544.92 772.17 L 1532.11 772.17" stroke-width="1" zvalue="642"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="14@0" LinkObjectIDznd="8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1544.92 772.17 L 1532.11 772.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv10" d="M 1692.56 757.46 L 1692.56 812.73" stroke-width="1" zvalue="643"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="40@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1692.56 757.46 L 1692.56 812.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv10" d="M 1708.92 771.17 L 1692.56 771.17" stroke-width="1" zvalue="644"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="11" MaxPinNum="2"/>
   </metadata>
  <path d="M 1708.92 771.17 L 1692.56 771.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv10" d="M 526.17 770.67 L 513.36 770.67" stroke-width="1" zvalue="650"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="56" MaxPinNum="2"/>
   </metadata>
  <path d="M 526.17 770.67 L 513.36 770.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv10" d="M 513.81 718.79 L 513.81 690.5" stroke-width="1" zvalue="651"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@1" LinkObjectIDznd="429@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 513.81 718.79 L 513.81 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv10" d="M 513.36 800.23 L 513.36 755.96" stroke-width="1" zvalue="657"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@1" LinkObjectIDznd="63@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 513.36 800.23 L 513.36 755.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv10" d="M 496.08 784.42 L 513.36 784.42" stroke-width="1" zvalue="663"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="56" MaxPinNum="2"/>
   </metadata>
  <path d="M 496.08 784.42 L 513.36 784.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv10" d="M 641.94 840.5 L 658.63 840.5" stroke-width="1" zvalue="668"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="155" MaxPinNum="2"/>
   </metadata>
  <path d="M 641.94 840.5 L 658.63 840.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv10" d="M 496.94 838.5 L 513.38 838.5" stroke-width="1" zvalue="671"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="152" MaxPinNum="2"/>
   </metadata>
  <path d="M 496.94 838.5 L 513.38 838.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv10" d="M 1023.81 891.01 L 1023.88 837.1" stroke-width="1" zvalue="675"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="42@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.81 891.01 L 1023.88 837.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv10" d="M 1023.86 812.73 L 1023.86 756.46" stroke-width="1" zvalue="680"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@1" LinkObjectIDznd="34@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.86 812.73 L 1023.86 756.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv10" d="M 1023.06 719.29 L 1023.06 690.5" stroke-width="1" zvalue="681"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@1" LinkObjectIDznd="429@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.06 719.29 L 1023.06 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv10" d="M 1033.92 771.17 L 1023.86 771.17" stroke-width="1" zvalue="684"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="16@0" LinkObjectIDznd="33" MaxPinNum="2"/>
   </metadata>
  <path d="M 1033.92 771.17 L 1023.86 771.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv10" d="M 513.38 824.6 L 513.38 880.75 L 513.64 880.75 L 513.64 936.89" stroke-width="1" zvalue="764"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="54@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 513.38 824.6 L 513.38 880.75 L 513.64 880.75 L 513.64 936.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv10" d="M 513.44 870.39 L 513.38 870.39" stroke-width="1" zvalue="765"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="152" MaxPinNum="2"/>
   </metadata>
  <path d="M 513.44 870.39 L 513.38 870.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv10" d="M 658.63 825.1 L 658.63 935.06" stroke-width="1" zvalue="766"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="382@0" LinkObjectIDznd="374@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 658.63 825.1 L 658.63 935.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv10" d="M 658.59 869.89 L 658.63 869.89" stroke-width="1" zvalue="767"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="375@0" LinkObjectIDznd="155" MaxPinNum="2"/>
   </metadata>
  <path d="M 658.59 869.89 L 658.63 869.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="kv10" d="M 1510.75 858.63 L 1510.75 937.25 L 1532.13 937.25 L 1532.13 837.1" stroke-width="1" zvalue="769"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@0" LinkObjectIDznd="9@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1510.75 858.63 L 1510.75 937.25 L 1532.13 937.25 L 1532.13 837.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv10" d="M 1531.69 864.89 L 1532.13 864.89" stroke-width="1" zvalue="770"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="17@0" LinkObjectIDznd="159" MaxPinNum="2"/>
   </metadata>
  <path d="M 1531.69 864.89 L 1532.13 864.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="549">
   <g id="5490">
    <use class="kv35" height="30" transform="rotate(0,810.063,530.194) scale(2.58333,2.6) translate(-477.49,-302.274)" width="24" x="779.0599999999999" xlink:href="#PowerTransformer2:可调不带中性点_0" y="491.19" zvalue="81"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874565681154" ObjectName="35"/>
    </metadata>
   </g>
   <g id="5491">
    <use class="kv10" height="30" transform="rotate(0,810.063,530.194) scale(2.58333,2.6) translate(-477.49,-302.274)" width="24" x="779.0599999999999" xlink:href="#PowerTransformer2:可调不带中性点_1" y="491.19" zvalue="81"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874565746690" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399524024322" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399524024322"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,810.063,530.194) scale(2.58333,2.6) translate(-477.49,-302.274)" width="24" x="779.0599999999999" y="491.19"/></g>
  <g id="667">
   <g id="6670">
    <use class="kv35" height="30" transform="rotate(0,1433.33,530.194) scale(2.58333,2.6) translate(-859.495,-302.274)" width="24" x="1402.33" xlink:href="#PowerTransformer2:可调不带中性点_0" y="491.19" zvalue="147"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874565550082" ObjectName="35"/>
    </metadata>
   </g>
   <g id="6671">
    <use class="kv10" height="30" transform="rotate(0,1433.33,530.194) scale(2.58333,2.6) translate(-859.495,-302.274)" width="24" x="1402.33" xlink:href="#PowerTransformer2:可调不带中性点_1" y="491.19" zvalue="147"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874565615618" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399523958786" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399523958786"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1433.33,530.194) scale(2.58333,2.6) translate(-859.495,-302.274)" width="24" x="1402.33" y="491.19"/></g>
 </g>
 <g id="BreakerClass">
  <g id="576">
   <use class="kv10" height="20" transform="rotate(270,1236.56,605.222) scale(2.72698,2.5322) translate(-774.472,-350.89)" width="10" x="1222.925170068027" xlink:href="#Breaker:母联小车开关_0" y="579.9002267573696" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925094932483" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473925094932483"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1236.56,605.222) scale(2.72698,2.5322) translate(-774.472,-350.89)" width="10" x="1222.925170068027" y="579.9002267573696"/></g>
  <g id="630">
   <use class="kv35" height="20" transform="rotate(0,810.914,448.667) scale(2.24069,2.03699) translate(-442.808,-218.038)" width="10" x="799.7106038966135" xlink:href="#Breaker:小车断路器_0" y="428.2967224121094" zvalue="130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925094866948" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925094866948"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,810.914,448.667) scale(2.24069,2.03699) translate(-442.808,-218.038)" width="10" x="799.7106038966135" y="428.2967224121094"/></g>
  <g id="666">
   <use class="kv35" height="20" transform="rotate(0,1434.25,448.667) scale(2.24069,2.03699) translate(-787.953,-218.038)" width="10" x="1423.043937229947" xlink:href="#Breaker:小车断路器_0" y="428.2967223862045" zvalue="149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925094801412" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925094801412"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1434.25,448.667) scale(2.24069,2.03699) translate(-787.953,-218.038)" width="10" x="1423.043937229947" y="428.2967223862045"/></g>
  <g id="388">
   <use class="kv10" height="20" transform="rotate(0,659.063,737.62) scale(2.24069,-2.03699) translate(-358.726,-1089.36)" width="10" x="647.8598412698416" xlink:href="#Breaker:小车断路器_0" y="717.25" zvalue="421"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925094998019" ObjectName="10kV2号电容器052断路器"/>
   <cge:TPSR_Ref TObjectID="6473925094998019"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,659.063,737.62) scale(2.24069,-2.03699) translate(-358.726,-1089.36)" width="10" x="647.8598412698416" y="717.25"/></g>
  <g id="389">
   <use class="kv10" height="20" transform="rotate(0,778.063,737.62) scale(2.24069,-2.03699) translate(-424.618,-1089.36)" width="10" x="766.8598412698416" xlink:href="#Breaker:小车断路器_0" y="717.25" zvalue="442"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925095063556" ObjectName="10kV芒朵线053断路器"/>
   <cge:TPSR_Ref TObjectID="6473925095063556"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,778.063,737.62) scale(2.24069,-2.03699) translate(-424.618,-1089.36)" width="10" x="766.8598412698416" y="717.25"/></g>
  <g id="403">
   <use class="kv10" height="20" transform="rotate(0,810.063,643.62) scale(2.24069,-2.03699) translate(-442.336,-949.215)" width="10" x="798.8598412698416" xlink:href="#Breaker:小车断路器_0" y="623.25" zvalue="456"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925095129092" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925095129092"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,810.063,643.62) scale(2.24069,-2.03699) translate(-442.336,-949.215)" width="10" x="798.8598412698416" y="623.25"/></g>
  <g id="409">
   <use class="kv10" height="20" transform="rotate(0,1433.81,647.12) scale(2.24069,-2.03699) translate(-787.713,-954.434)" width="10" x="1422.609841269842" xlink:href="#Breaker:小车断路器_0" y="626.75" zvalue="460"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925095194628" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473925095194628"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1433.81,647.12) scale(2.24069,-2.03699) translate(-787.713,-954.434)" width="10" x="1422.609841269842" y="626.75"/></g>
  <g id="444">
   <use class="kv35" height="20" transform="rotate(0,1121.66,262.167) scale(2.24069,2.03699) translate(-614.873,-123.094)" width="10" x="1110.460603896614" xlink:href="#Breaker:小车断路器_0" y="241.7967208894656" zvalue="492"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925095260164" ObjectName="35kV西那线351断路器"/>
   <cge:TPSR_Ref TObjectID="6473925095260164"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1121.66,262.167) scale(2.24069,2.03699) translate(-614.873,-123.094)" width="10" x="1110.460603896614" y="241.7967208894656"/></g>
  <g id="501">
   <use class="kv10" height="20" transform="rotate(0,906.063,737.62) scale(2.24069,-2.03699) translate(-495.493,-1089.36)" width="10" x="894.8598412698416" xlink:href="#Breaker:小车断路器_0" y="717.25" zvalue="541"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925095325700" ObjectName="10kV支那街道线054断路器"/>
   <cge:TPSR_Ref TObjectID="6473925095325700"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,906.063,737.62) scale(2.24069,-2.03699) translate(-495.493,-1089.36)" width="10" x="894.8598412698416" y="717.25"/></g>
  <g id="515">
   <use class="kv10" height="20" transform="rotate(0,1147.56,737.62) scale(2.24069,-2.03699) translate(-629.213,-1089.36)" width="10" x="1136.359841269842" xlink:href="#Breaker:小车断路器_0" y="717.25" zvalue="551"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925095391236" ObjectName="10kV2号站用变056断路器"/>
   <cge:TPSR_Ref TObjectID="6473925095391236"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1147.56,737.62) scale(2.24069,-2.03699) translate(-629.213,-1089.36)" width="10" x="1136.359841269842" y="717.25"/></g>
  <g id="534">
   <use class="kv10" height="20" transform="rotate(0,1386.31,737.62) scale(2.24069,-2.03699) translate(-761.412,-1089.36)" width="10" x="1375.109841269842" xlink:href="#Breaker:小车断路器_0" y="717.25" zvalue="575"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925095456772" ObjectName="10kV支东线057断路器"/>
   <cge:TPSR_Ref TObjectID="6473925095456772"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1386.31,737.62) scale(2.24069,-2.03699) translate(-761.412,-1089.36)" width="10" x="1375.109841269842" y="717.25"/></g>
  <g id="15">
   <use class="kv10" height="20" transform="rotate(0,1532.56,738.62) scale(2.24069,-2.03699) translate(-842.392,-1090.85)" width="10" x="1521.359841269842" xlink:href="#Breaker:小车断路器_0" y="718.25" zvalue="600"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925095522308" ObjectName="10kV3号电容器058断路器"/>
   <cge:TPSR_Ref TObjectID="6473925095522308"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1532.56,738.62) scale(2.24069,-2.03699) translate(-842.392,-1090.85)" width="10" x="1521.359841269842" y="718.25"/></g>
  <g id="44">
   <use class="kv10" height="20" transform="rotate(0,1692.56,738.62) scale(2.24069,-2.03699) translate(-930.985,-1090.85)" width="10" x="1681.359841269842" xlink:href="#Breaker:小车断路器_0" y="718.25" zvalue="622"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925095587844" ObjectName="10kV4号电容器059断路器"/>
   <cge:TPSR_Ref TObjectID="6473925095587844"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1692.56,738.62) scale(2.24069,-2.03699) translate(-930.985,-1090.85)" width="10" x="1681.359841269842" y="718.25"/></g>
  <g id="63">
   <use class="kv10" height="20" transform="rotate(0,513.813,737.12) scale(2.24069,-2.03699) translate(-278.3,-1088.62)" width="10" x="502.6098412698415" xlink:href="#Breaker:小车断路器_0" y="716.75" zvalue="646"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925094735876" ObjectName="10kV1号电容器051断路器"/>
   <cge:TPSR_Ref TObjectID="6473925094735876"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,513.813,737.12) scale(2.24069,-2.03699) translate(-278.3,-1088.62)" width="10" x="502.6098412698415" y="716.75"/></g>
  <g id="34">
   <use class="kv10" height="20" transform="rotate(0,1023.06,737.62) scale(2.24069,-2.03699) translate(-560.277,-1089.36)" width="10" x="1011.859841269842" xlink:href="#Breaker:小车断路器_0" y="717.25" zvalue="678"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925108367363" ObjectName="10kV备用线055断路器"/>
   <cge:TPSR_Ref TObjectID="6473925108367363"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1023.06,737.62) scale(2.24069,-2.03699) translate(-560.277,-1089.36)" width="10" x="1011.859841269842" y="717.25"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="153">
   <use class="kv10" height="30" transform="rotate(0,782.392,910) scale(1.43333,-1.43333) translate(-233.287,-1538.38)" width="15" x="771.6415343915346" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="888.5" zvalue="294"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453797281795" ObjectName="10kV芒朵线"/>
   <cge:TPSR_Ref TObjectID="6192453797281795"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,782.392,910) scale(1.43333,-1.43333) translate(-233.287,-1538.38)" width="15" x="771.6415343915346" y="888.5"/></g>
  <g id="434">
   <use class="kv35" height="30" transform="rotate(0,1434.85,239.906) scale(2.4375,-2.03125) translate(-831.821,-342.545)" width="20" x="1410.479166666667" xlink:href="#EnergyConsumer:站用变无融断_0" y="209.4375" zvalue="483"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453799575555" ObjectName="35kV1号站用变"/>
   <cge:TPSR_Ref TObjectID="6192453799575555"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1434.85,239.906) scale(2.4375,-2.03125) translate(-831.821,-342.545)" width="20" x="1410.479166666667" y="209.4375"/></g>
  <g id="503">
   <use class="kv10" height="30" transform="rotate(0,910.392,910) scale(1.43333,-1.43333) translate(-271.985,-1538.38)" width="15" x="899.6415343915346" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="888.5" zvalue="539"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453800886275" ObjectName="10kV支那街道线"/>
   <cge:TPSR_Ref TObjectID="6192453800886275"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,910.392,910) scale(1.43333,-1.43333) translate(-271.985,-1538.38)" width="15" x="899.6415343915346" y="888.5"/></g>
  <g id="519">
   <use class="kv10" height="30" transform="rotate(0,1147.35,909.906) scale(2.4375,2.03125) translate(-662.27,-446.484)" width="20" x="1122.979166666667" xlink:href="#EnergyConsumer:站用变无融断_0" y="879.4375" zvalue="564"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453801279491" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1147.35,909.906) scale(2.4375,2.03125) translate(-662.27,-446.484)" width="20" x="1122.979166666667" y="879.4375"/></g>
  <g id="535">
   <use class="kv10" height="30" transform="rotate(0,1390.64,914) scale(1.43333,-1.43333) translate(-417.177,-1545.17)" width="15" x="1379.891534391535" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="892.5" zvalue="573"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453801607171" ObjectName="10kV支东线"/>
   <cge:TPSR_Ref TObjectID="6192453801607171"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1390.64,914) scale(1.43333,-1.43333) translate(-417.177,-1545.17)" width="15" x="1379.891534391535" y="892.5"/></g>
  <g id="37">
   <use class="kv10" height="30" transform="rotate(0,1027.39,910) scale(1.43333,-1.43333) translate(-307.357,-1538.38)" width="15" x="1016.641534391535" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="888.5" zvalue="676"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453903319043" ObjectName="10kV备用线"/>
   <cge:TPSR_Ref TObjectID="6192453903319043"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1027.39,910) scale(1.43333,-1.43333) translate(-307.357,-1538.38)" width="15" x="1016.641534391535" y="888.5"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="387">
   <use class="kv10" height="20" transform="rotate(270,686.25,771.222) scale(1.11111,1.11111) translate(-68.0694,-76.0111)" width="10" x="680.6944444444445" xlink:href="#GroundDisconnector:地刀_0" y="760.1111111111111" zvalue="423"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453798658051" ObjectName="10kV2号电容器05217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453798658051"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,686.25,771.222) scale(1.11111,1.11111) translate(-68.0694,-76.0111)" width="10" x="680.6944444444445" y="760.1111111111111"/></g>
  <g id="384">
   <use class="kv10" height="20" transform="rotate(90,630.5,784.972) scale(-1.11111,1.11111) translate(-1197.39,-77.3861)" width="10" x="624.9444444444445" xlink:href="#GroundDisconnector:地刀_0" y="773.8611111111111" zvalue="427"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453798526979" ObjectName="10kV2号电容器05260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453798526979"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,630.5,784.972) scale(-1.11111,1.11111) translate(-1197.39,-77.3861)" width="10" x="624.9444444444445" y="773.8611111111111"/></g>
  <g id="374">
   <use class="kv10" height="20" transform="rotate(0,658.274,945.889) scale(-1.11111,1.11111) translate(-1250.17,-93.4778)" width="10" x="652.7185416666666" xlink:href="#GroundDisconnector:地刀_0" y="934.7777777777777" zvalue="438"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453798199299" ObjectName="10kV2号电容器05210接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453798199299"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,658.274,945.889) scale(-1.11111,1.11111) translate(-1250.17,-93.4778)" width="10" x="652.7185416666666" y="934.7777777777777"/></g>
  <g id="396">
   <use class="kv10" height="20" transform="rotate(270,803.75,771.222) scale(1.11111,1.11111) translate(-79.8194,-76.0111)" width="10" x="798.1944444444445" xlink:href="#GroundDisconnector:地刀_0" y="760.1111111111111" zvalue="449"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453798789123" ObjectName="10kV芒朵线05317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453798789123"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,803.75,771.222) scale(1.11111,1.11111) translate(-79.8194,-76.0111)" width="10" x="798.1944444444445" y="760.1111111111111"/></g>
  <g id="417">
   <use class="kv35" height="20" transform="rotate(90,784,316.972) scale(-1.11111,1.11111) translate(-1489.04,-30.5861)" width="10" x="778.4444444444445" xlink:href="#GroundDisconnector:地刀_0" y="305.8611111111111" zvalue="467"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453799051267" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453799051267"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,784,316.972) scale(-1.11111,1.11111) translate(-1489.04,-30.5861)" width="10" x="778.4444444444445" y="305.8611111111111"/></g>
  <g id="422">
   <use class="kv35" height="20" transform="rotate(90,784,261.972) scale(-1.11111,1.11111) translate(-1489.04,-25.0861)" width="10" x="778.4444444444445" xlink:href="#GroundDisconnector:地刀_0" y="250.8611111111111" zvalue="471"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453799182339" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453799182339"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,784,261.972) scale(-1.11111,1.11111) translate(-1489.04,-25.0861)" width="10" x="778.4444444444445" y="250.8611111111111"/></g>
  <g id="424">
   <use class="kv35" height="20" transform="rotate(90,781.5,419.472) scale(-1.11111,1.11111) translate(-1484.29,-40.8361)" width="10" x="775.9444444444445" xlink:href="#GroundDisconnector:地刀_0" y="408.3611111111111" zvalue="475"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453799313411" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453799313411"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,781.5,419.472) scale(-1.11111,1.11111) translate(-1484.29,-40.8361)" width="10" x="775.9444444444445" y="408.3611111111111"/></g>
  <g id="430">
   <use class="kv35" height="20" transform="rotate(90,1404,413.613) scale(-1.11111,1.11111) translate(-2667.04,-40.2502)" width="10" x="1398.444444444444" xlink:href="#GroundDisconnector:地刀_0" y="402.5018867479586" zvalue="478"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453799444483" ObjectName="#2主变35kV侧30217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453799444483"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1404,413.613) scale(-1.11111,1.11111) translate(-2667.04,-40.2502)" width="10" x="1398.444444444444" y="402.5018867479586"/></g>
  <g id="460">
   <use class="kv35" height="20" transform="rotate(270,1148.11,285.722) scale(1.11111,1.11111) translate(-114.256,-27.4611)" width="10" x="1142.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="274.6111111111111" zvalue="504"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453799968771" ObjectName="35kV西那线35117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453799968771"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1148.11,285.722) scale(1.11111,1.11111) translate(-114.256,-27.4611)" width="10" x="1142.555555555556" y="274.6111111111111"/></g>
  <g id="462">
   <use class="kv35" height="20" transform="rotate(270,1148.11,235.722) scale(1.11111,1.11111) translate(-114.256,-22.4611)" width="10" x="1142.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="224.6111111111111" zvalue="508"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453800099843" ObjectName="35kV西那线35160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453800099843"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1148.11,235.722) scale(1.11111,1.11111) translate(-114.256,-22.4611)" width="10" x="1142.555555555556" y="224.6111111111111"/></g>
  <g id="466">
   <use class="kv35" height="20" transform="rotate(270,1149.36,184.722) scale(1.11111,1.11111) translate(-114.381,-17.3611)" width="10" x="1143.805555555556" xlink:href="#GroundDisconnector:地刀_0" y="173.6111111111111" zvalue="512"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453800230915" ObjectName="35kV西那线35167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453800230915"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1149.36,184.722) scale(1.11111,1.11111) translate(-114.381,-17.3611)" width="10" x="1143.805555555556" y="173.6111111111111"/></g>
  <g id="498">
   <use class="kv10" height="20" transform="rotate(270,927.75,771.222) scale(1.11111,1.11111) translate(-92.2194,-76.0111)" width="10" x="922.1944444444445" xlink:href="#GroundDisconnector:地刀_0" y="760.1111111111111" zvalue="545"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453800820739" ObjectName="10kV支那街道线05417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453800820739"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,927.75,771.222) scale(1.11111,1.11111) translate(-92.2194,-76.0111)" width="10" x="922.1944444444445" y="760.1111111111111"/></g>
  <g id="513">
   <use class="kv10" height="20" transform="rotate(270,1169.25,771.222) scale(1.11111,1.11111) translate(-116.369,-76.0111)" width="10" x="1163.694444444444" xlink:href="#GroundDisconnector:地刀_0" y="760.1111111111111" zvalue="554"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453801148419" ObjectName="10kV2号站用变05617接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453801148419"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1169.25,771.222) scale(1.11111,1.11111) translate(-116.369,-76.0111)" width="10" x="1163.694444444444" y="760.1111111111111"/></g>
  <g id="531">
   <use class="kv10" height="20" transform="rotate(270,1408,771.222) scale(1.11111,1.11111) translate(-140.244,-76.0111)" width="10" x="1402.444444444444" xlink:href="#GroundDisconnector:地刀_0" y="760.1111111111111" zvalue="579"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453801541635" ObjectName="10kV支东线05717接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453801541635"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1408,771.222) scale(1.11111,1.11111) translate(-140.244,-76.0111)" width="10" x="1402.444444444444" y="760.1111111111111"/></g>
  <g id="548">
   <use class="kv10" height="20" transform="rotate(270,1411.75,877.306) scale(1.11111,1.11111) translate(-140.619,-86.6194)" width="10" x="1406.194444444444" xlink:href="#GroundDisconnector:地刀_0" y="866.1944444444443" zvalue="597"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453802000387" ObjectName="10kV支东线05767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453802000387"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1411.75,877.306) scale(1.11111,1.11111) translate(-140.619,-86.6194)" width="10" x="1406.194444444444" y="866.1944444444443"/></g>
  <g id="14">
   <use class="kv10" height="20" transform="rotate(270,1555.75,772.222) scale(1.11111,1.11111) translate(-155.019,-76.1111)" width="10" x="1550.194444444444" xlink:href="#GroundDisconnector:地刀_0" y="761.1111111111111" zvalue="602"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453802262531" ObjectName="10kV3号电容器05817接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453802262531"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1555.75,772.222) scale(1.11111,1.11111) translate(-155.019,-76.1111)" width="10" x="1550.194444444444" y="761.1111111111111"/></g>
  <g id="20">
   <use class="kv10" height="40" transform="rotate(90,1511.25,854.625) scale(-1,1) translate(-3022.5,0)" width="20" x="1501.25" xlink:href="#GroundDisconnector:支那变双联地刀_0" y="834.625" zvalue="619"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453802459139" ObjectName="10kV3号电容器05810接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453802459139"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1511.25,854.625) scale(-1,1) translate(-3022.5,0)" width="20" x="1501.25" y="834.625"/></g>
  <g id="43">
   <use class="kv10" height="20" transform="rotate(270,1719.75,771.222) scale(1.11111,1.11111) translate(-171.419,-76.0111)" width="10" x="1714.194444444444" xlink:href="#GroundDisconnector:地刀_0" y="760.1111111111111" zvalue="624"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453802917891" ObjectName="10kV4号电容器05917接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453802917891"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1719.75,771.222) scale(1.11111,1.11111) translate(-171.419,-76.0111)" width="10" x="1714.194444444444" y="760.1111111111111"/></g>
  <g id="29">
   <use class="kv10" height="40" transform="rotate(90,1671,860) scale(-1,1) translate(-3342,0)" width="20" x="1661" xlink:href="#GroundDisconnector:支那变双联地刀_0" y="840" zvalue="637"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453802590211" ObjectName="10kV4号电容器05910接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453802590211"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1671,860) scale(-1,1) translate(-3342,0)" width="20" x="1661" y="840"/></g>
  <g id="62">
   <use class="kv10" height="20" transform="rotate(270,537,770.722) scale(1.11111,1.11111) translate(-53.1444,-75.9611)" width="10" x="531.4444444444445" xlink:href="#GroundDisconnector:地刀_0" y="759.6111111111111" zvalue="648"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453797216259" ObjectName="10kV1号电容器05117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453797216259"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,537,770.722) scale(1.11111,1.11111) translate(-53.1444,-75.9611)" width="10" x="531.4444444444445" y="759.6111111111111"/></g>
  <g id="59">
   <use class="kv10" height="20" transform="rotate(90,485.25,784.472) scale(-1.11111,1.11111) translate(-921.419,-77.3361)" width="10" x="479.6944444444445" xlink:href="#GroundDisconnector:地刀_0" y="773.3611111111111" zvalue="652"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453797740547" ObjectName="10kV1号电容器05160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453797740547"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,485.25,784.472) scale(-1.11111,1.11111) translate(-921.419,-77.3361)" width="10" x="479.6944444444445" y="773.3611111111111"/></g>
  <g id="54">
   <use class="kv10" height="20" transform="rotate(0,513.691,947.722) scale(-1.11111,1.11111) translate(-975.457,-93.6611)" width="10" x="508.1352083333333" xlink:href="#GroundDisconnector:地刀_0" y="936.6111111111111" zvalue="660"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453798068227" ObjectName="10kV1号电容器05110接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453798068227"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,513.691,947.722) scale(-1.11111,1.11111) translate(-975.457,-93.6611)" width="10" x="508.1352083333333" y="936.6111111111111"/></g>
  <g id="66">
   <use class="kv10" height="20" transform="rotate(90,631.111,840.556) scale(-1.11111,1.11111) translate(-1198.56,-82.9444)" width="10" x="625.5555555555555" xlink:href="#GroundDisconnector:地刀_0" y="829.4444444444443" zvalue="667"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453902991363" ObjectName="10kV2号电容器05267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453902991363"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,631.111,840.556) scale(-1.11111,1.11111) translate(-1198.56,-82.9444)" width="10" x="625.5555555555555" y="829.4444444444443"/></g>
  <g id="69">
   <use class="kv10" height="20" transform="rotate(90,486.111,838.556) scale(-1.11111,1.11111) translate(-923.056,-82.7444)" width="10" x="480.5555555555555" xlink:href="#GroundDisconnector:地刀_0" y="827.4444444444443" zvalue="670"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453903122435" ObjectName="10kV1号电容器05167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453903122435"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,486.111,838.556) scale(-1.11111,1.11111) translate(-923.056,-82.7444)" width="10" x="480.5555555555555" y="827.4444444444443"/></g>
  <g id="16">
   <use class="kv10" height="20" transform="rotate(270,1044.75,771.222) scale(1.11111,1.11111) translate(-103.919,-76.0111)" width="10" x="1039.194444444444" xlink:href="#GroundDisconnector:地刀_0" y="760.1111111111111" zvalue="682"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453903253507" ObjectName="10kV备用线05517接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453903253507"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1044.75,771.222) scale(1.11111,1.11111) translate(-103.919,-76.0111)" width="10" x="1039.194444444444" y="760.1111111111111"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="375">
   <use class="kv10" height="40" transform="rotate(0,658.586,894.75) scale(1.90104,1.625) translate(-301.339,-331.635)" width="24" x="635.7732236005853" xlink:href="#Compensator:10kV电容器_0" y="862.25" zvalue="436"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453798264835" ObjectName="10kV2号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453798264835"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,658.586,894.75) scale(1.90104,1.625) translate(-301.339,-331.635)" width="24" x="635.7732236005853" y="862.25"/></g>
  <g id="17">
   <use class="kv10" height="40" transform="rotate(0,1531.69,889.75) scale(1.49479,1.625) translate(-501.067,-329.712)" width="24" x="1513.75" xlink:href="#Compensator:10kV电容器_0" y="857.25" zvalue="617"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453802328067" ObjectName="10kV3号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453802328067"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1531.69,889.75) scale(1.49479,1.625) translate(-501.067,-329.712)" width="24" x="1513.75" y="857.25"/></g>
  <g id="32">
   <use class="kv10" height="40" transform="rotate(0,1692.06,888.75) scale(1.49479,1.625) translate(-554.153,-329.327)" width="24" x="1674.125" xlink:href="#Compensator:10kV电容器_0" y="856.25" zvalue="634"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453802655747" ObjectName="10kV4号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453802655747"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1692.06,888.75) scale(1.49479,1.625) translate(-554.153,-329.327)" width="24" x="1674.125" y="856.25"/></g>
  <g id="55">
   <use class="kv10" height="40" transform="rotate(0,513.438,895.25) scale(1.90104,1.625) translate(-232.543,-331.827)" width="24" x="490.625" xlink:href="#Compensator:10kV电容器_0" y="862.75" zvalue="658"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453797937155" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453797937155"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,513.438,895.25) scale(1.90104,1.625) translate(-232.543,-331.827)" width="24" x="490.625" y="862.75"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="413">
   <use class="kv35" height="42" transform="rotate(0,824.201,224.375) scale(1.51786,1.51786) translate(-273.43,-65.6765)" width="30" x="801.433470691534" xlink:href="#Accessory:4卷PT带容断器_0" y="192.5" zvalue="463"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453798920195" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,824.201,224.375) scale(1.51786,1.51786) translate(-273.43,-65.6765)" width="30" x="801.433470691534" y="192.5"/></g>
  <g id="432">
   <use class="kv10" height="26" transform="rotate(90,1398.75,605.125) scale(0.9375,1.25) translate(92.875,-117.775)" width="12" x="1393.125" xlink:href="#Accessory:避雷器1_0" y="588.875" zvalue="481"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453799510019" ObjectName="2号主变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1398.75,605.125) scale(0.9375,1.25) translate(92.875,-117.775)" width="12" x="1393.125" y="588.875"/></g>
  <g id="469">
   <use class="kv35" height="40" transform="rotate(0,1082.5,151) scale(1.25,1.25) translate(-212.75,-25.2)" width="30" x="1063.75" xlink:href="#Accessory:带熔断器的线路PT1_0" y="126" zvalue="514"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453800296451" ObjectName="35kV西那线线路PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1082.5,151) scale(1.25,1.25) translate(-212.75,-25.2)" width="30" x="1063.75" y="126"/></g>
  <g id="475">
   <use class="kv10" height="42" transform="rotate(0,609.515,578.125) scale(1.51786,1.51786) translate(-200.184,-186.368)" width="30" x="586.7467821388361" xlink:href="#Accessory:4卷PT带容断器_0" y="546.25" zvalue="521"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453800427523" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,609.515,578.125) scale(1.51786,1.51786) translate(-200.184,-186.368)" width="30" x="586.7467821388361" y="546.25"/></g>
  <g id="488">
   <use class="kv10" height="42" transform="rotate(0,1679.51,578.125) scale(1.51786,1.51786) translate(-565.243,-186.368)" width="30" x="1656.746782138836" xlink:href="#Accessory:4卷PT带容断器_0" y="546.25" zvalue="529"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453800624131" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1679.51,578.125) scale(1.51786,1.51786) translate(-565.243,-186.368)" width="30" x="1656.746782138836" y="546.25"/></g>
  <g id="522">
   <use class="kv10" height="26" transform="rotate(0,1121.25,890.375) scale(-0.9375,1.25) translate(-2317.62,-174.825)" width="12" x="1115.625" xlink:href="#Accessory:避雷器1_0" y="874.125" zvalue="567"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453801345027" ObjectName="2号站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1121.25,890.375) scale(-0.9375,1.25) translate(-2317.62,-174.825)" width="12" x="1115.625" y="874.125"/></g>
  <g id="538">
   <use class="kv10" height="40" transform="rotate(0,1338.75,806) scale(1.25,-1.25) translate(-264,-1445.8)" width="30" x="1320" xlink:href="#Accessory:带熔断器的线路PT1_0" y="781" zvalue="585"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453801738243" ObjectName="支东线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1338.75,806) scale(1.25,-1.25) translate(-264,-1445.8)" width="30" x="1320" y="781"/></g>
  <g id="540">
   <use class="kv10" height="26" transform="rotate(0,1362.5,796.625) scale(-0.9375,1.25) translate(-2816.21,-156.075)" width="12" x="1356.875" xlink:href="#Accessory:避雷器1_0" y="780.375" zvalue="587"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453801803779" ObjectName="支东线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1362.5,796.625) scale(-0.9375,1.25) translate(-2816.21,-156.075)" width="12" x="1356.875" y="780.375"/></g>
  <g id="546">
   <use class="kv10" height="26" transform="rotate(0,1366.25,901.625) scale(-0.9375,1.25) translate(-2823.96,-177.075)" width="12" x="1360.625" xlink:href="#Accessory:避雷器1_0" y="885.375" zvalue="594"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453801869315" ObjectName="支东线避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1366.25,901.625) scale(-0.9375,1.25) translate(-2823.96,-177.075)" width="12" x="1360.625" y="885.375"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="441">
   <use class="kv35" height="30" transform="rotate(0,1121.69,128.625) scale(4.08692,1.175) translate(-836.425,-16.5319)" width="7" x="1107.382018458804" xlink:href="#ACLineSegment:线路_0" y="111" zvalue="489"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249327009796" ObjectName="35kV西那线"/>
   <cge:TPSR_Ref TObjectID="8444249327009796_5066549679161345"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1121.69,128.625) scale(4.08692,1.175) translate(-836.425,-16.5319)" width="7" x="1107.382018458804" y="111"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="72">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="72" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1121.69,18.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1121.88" xml:space="preserve" y="23.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133075136515" ObjectName="P"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="73" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1121.69,41.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1121.88" xml:space="preserve" y="46.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133075202051" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="74">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="74" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1121.69,64.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1121.88" xml:space="preserve" y="69.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133075267587" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="75">
   <text Format="f4.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="75" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,661.086,999.25) scale(1,1) translate(0,0)" writing-mode="lr" x="661.28" xml:space="preserve" y="1004.3" zvalue="1">Q:dd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133071728644" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="76">
   <text Format="f4.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="76" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1534.19,994.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1534.38" xml:space="preserve" y="999.3" zvalue="1">Q:dd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133081427971" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="77">
   <text Format="f4.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="77" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1688.69,994.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1688.88" xml:space="preserve" y="999.3" zvalue="1">Q:dd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133081821187" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f4.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="78" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,514.938,999.75) scale(1,1) translate(0,0)" writing-mode="lr" x="515.13" xml:space="preserve" y="1004.8" zvalue="1">Q:dd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133071335427" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="79">
   <text Format="f4.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="79" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,659.086,1024.25) scale(1,1) translate(0,0)" writing-mode="lr" x="659.28" xml:space="preserve" y="1029.3" zvalue="1">Ia:dd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133071794180" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f4.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="80" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1532.19,1019.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1532.38" xml:space="preserve" y="1024.3" zvalue="1">Ia:dd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133081493507" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="84">
   <text Format="f4.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="84" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1687.69,1018.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1687.88" xml:space="preserve" y="1023.3" zvalue="1">Ia:dd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133081886723" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f4.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="85" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,513.938,1024.75) scale(1,1) translate(0,0)" writing-mode="lr" x="514.13" xml:space="preserve" y="1029.8" zvalue="1">Ia:dd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133071400963" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="86" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,782.392,978) scale(1,1) translate(0,0)" writing-mode="lr" x="782.59" xml:space="preserve" y="982.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133062881283" ObjectName="P"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="89" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1434.85,116.938) scale(1,1) translate(9.25829e-13,0)" writing-mode="lr" x="1435.05" xml:space="preserve" y="121.85" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133074219013" ObjectName="P"/>
   </metadata>
  </g>
  <g id="90">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="90" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,910.392,978) scale(1,1) translate(0,0)" writing-mode="lr" x="910.59" xml:space="preserve" y="982.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133077102595" ObjectName="P"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="91" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1147.35,976.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1147.55" xml:space="preserve" y="981.79" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133078544388" ObjectName="P"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="92" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1390.64,982) scale(1,1) translate(0,0)" writing-mode="lr" x="1390.84" xml:space="preserve" y="986.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133079986179" ObjectName="P"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="93" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1027.39,978) scale(1,1) translate(0,0)" writing-mode="lr" x="1027.59" xml:space="preserve" y="982.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133556170755" ObjectName="P"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="94" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,782.392,1001) scale(1,1) translate(0,0)" writing-mode="lr" x="782.59" xml:space="preserve" y="1005.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133062946819" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="95" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1434.85,139.938) scale(1,1) translate(9.25829e-13,0)" writing-mode="lr" x="1435.05" xml:space="preserve" y="144.85" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133074284547" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="96" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,910.392,1001) scale(1,1) translate(0,0)" writing-mode="lr" x="910.59" xml:space="preserve" y="1005.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133077168131" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="98" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1147.35,999.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1147.55" xml:space="preserve" y="1004.79" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133078609924" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="99" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1390.64,1005) scale(1,1) translate(0,0)" writing-mode="lr" x="1390.84" xml:space="preserve" y="1009.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133080051715" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="100" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1027.39,1001) scale(1,1) translate(0,0)" writing-mode="lr" x="1027.59" xml:space="preserve" y="1005.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133556236291" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="101" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,782.392,1024) scale(1,1) translate(0,0)" writing-mode="lr" x="782.59" xml:space="preserve" y="1028.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133063012355" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="102" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1434.85,162.938) scale(1,1) translate(9.25829e-13,0)" writing-mode="lr" x="1435.05" xml:space="preserve" y="167.85" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133074350083" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="104" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,910.392,1024) scale(1,1) translate(0,0)" writing-mode="lr" x="910.59" xml:space="preserve" y="1028.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133077233668" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="105" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1147.35,1022.88) scale(1,1) translate(0,0)" writing-mode="lr" x="1147.55" xml:space="preserve" y="1027.79" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133078675460" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="106">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="106" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1390.64,1028) scale(1,1) translate(0,0)" writing-mode="lr" x="1390.84" xml:space="preserve" y="1032.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133080117251" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="110">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="110" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1027.39,1024) scale(1,1) translate(0,0)" writing-mode="lr" x="1027.59" xml:space="preserve" y="1028.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133556301827" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="111" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,131.639,521.25) scale(1,1) translate(1.17992e-14,0)" writing-mode="lr" x="131.75" xml:space="preserve" y="526.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133070286852" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="112">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="112" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,131.639,544.25) scale(1,1) translate(1.17992e-14,5.96467e-14)" writing-mode="lr" x="131.75" xml:space="preserve" y="549.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133070352388" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="113">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="113" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,131.639,567.25) scale(1,1) translate(1.17992e-14,-1.244e-13)" writing-mode="lr" x="131.75" xml:space="preserve" y="572.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133070417924" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,131.639,495.75) scale(1,1) translate(1.17992e-14,-1.08524e-13)" writing-mode="lr" x="131.75" xml:space="preserve" y="500.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133070548996" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="118">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="118" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,136,203.111) scale(1,1) translate(0,0)" writing-mode="lr" x="136.15" xml:space="preserve" y="209.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133070680068" ObjectName="F"/>
   </metadata>
  </g>
  <g id="119">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,251.042,521.25) scale(1,1) translate(2.50556e-14,0)" writing-mode="lr" x="251.15" xml:space="preserve" y="526.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133069762563" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="120">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="120" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,251.042,544.25) scale(1,1) translate(2.50556e-14,5.96467e-14)" writing-mode="lr" x="251.15" xml:space="preserve" y="549.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133069828099" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="121">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,251.042,567.25) scale(1,1) translate(2.50556e-14,-1.244e-13)" writing-mode="lr" x="251.15" xml:space="preserve" y="572.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133069893636" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="123">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,251.042,495.75) scale(1,1) translate(2.50556e-14,-1.08524e-13)" writing-mode="lr" x="251.15" xml:space="preserve" y="500.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133070024708" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="125">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="125" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,136,228) scale(1,1) translate(0,0)" writing-mode="lr" x="136.15" xml:space="preserve" y="234.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133070155779" ObjectName="F"/>
   </metadata>
  </g>
  <g id="126">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="126" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,309,521.25) scale(1,1) translate(3.14902e-14,0)" writing-mode="lr" x="309.11" xml:space="preserve" y="526.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133070811140" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="127">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="127" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,309,544.25) scale(1,1) translate(3.14902e-14,-1.19293e-13)" writing-mode="lr" x="309.11" xml:space="preserve" y="549.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133070876676" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="128">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="128" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,309,567.25) scale(1,1) translate(3.14902e-14,-1.244e-13)" writing-mode="lr" x="309.11" xml:space="preserve" y="572.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133070942212" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="129">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="129" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,313,225.625) scale(1,1) translate(0,0)" writing-mode="lr" x="313.15" xml:space="preserve" y="231.96" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133071204356" ObjectName="F"/>
   </metadata>
  </g>
  <g id="131">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="131" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,309,495.75) scale(1,1) translate(3.14902e-14,-1.08524e-13)" writing-mode="lr" x="309.11" xml:space="preserve" y="500.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133071073284" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="133">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="133" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,888.563,378.694) scale(1,1) translate(0,0)" writing-mode="lr" x="888.8" xml:space="preserve" y="383.68" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133067730948" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="134" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,888.563,405.694) scale(1,1) translate(0,-2.6192e-13)" writing-mode="lr" x="888.8" xml:space="preserve" y="410.68" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133067796484" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="135">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="135" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,888.563,432.694) scale(1,1) translate(0,0)" writing-mode="lr" x="888.8" xml:space="preserve" y="437.68" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133067993091" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="136" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,903.563,602.694) scale(1,1) translate(0,0)" writing-mode="lr" x="903.8" xml:space="preserve" y="607.6799999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133067862020" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="137" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,903.563,629.694) scale(1,1) translate(0,0)" writing-mode="lr" x="903.8" xml:space="preserve" y="634.6799999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133067927556" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="138" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,903.563,656.694) scale(1,1) translate(0,0)" writing-mode="lr" x="903.8" xml:space="preserve" y="661.6799999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133068320771" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="139">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,136,250.194) scale(1,1) translate(0,0)" writing-mode="lr" x="136.15" xml:space="preserve" y="256.47" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133068189700" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,136,273.194) scale(1,1) translate(0,-1.77321e-13)" writing-mode="lr" x="136.15" xml:space="preserve" y="279.47" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133068255235" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="141" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1509.83,388.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.07" xml:space="preserve" y="393.68" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133064323075" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="142" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1509.83,415.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.07" xml:space="preserve" y="420.68" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133064388612" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="143" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1509.83,442.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.07" xml:space="preserve" y="447.68" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133064585219" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="144" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1526.83,599.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1527.07" xml:space="preserve" y="604.6799999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133064454148" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="145" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1526.83,626.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1527.07" xml:space="preserve" y="631.6799999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133064519683" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="146">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="146" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1526.83,653.694) scale(1,1) translate(0,-4.27121e-13)" writing-mode="lr" x="1527.07" xml:space="preserve" y="658.6799999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133064912899" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="147">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,313,249.194) scale(1,1) translate(0,0)" writing-mode="lr" x="313.15" xml:space="preserve" y="255.47" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133064781827" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,313,272.194) scale(1,1) translate(0,-3.5331e-13)" writing-mode="lr" x="313.15" xml:space="preserve" y="278.47" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133064847363" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="149">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,131.639,594.25) scale(1,1) translate(1.17992e-14,1.30396e-13)" writing-mode="lr" x="131.75" xml:space="preserve" y="599.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133070745604" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="150" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,251.042,594.25) scale(1,1) translate(2.50556e-14,1.30396e-13)" writing-mode="lr" x="251.15" xml:space="preserve" y="599.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133070221315" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="151">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="151" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,309,594.25) scale(1,1) translate(3.14902e-14,1.30396e-13)" writing-mode="lr" x="309.11" xml:space="preserve" y="599.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133071269892" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="117" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,136,178.5) scale(1,1) translate(0,0)" writing-mode="lr" x="136.15" xml:space="preserve" y="184.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133082738691" ObjectName=""/>
   </metadata>
  </g>
  <g id="124">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="124" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,313,178.5) scale(1,1) translate(0,0)" writing-mode="lr" x="313.15" xml:space="preserve" y="184.85" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133082804227" ObjectName=""/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="35" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1236.56,516.087) scale(1,1) translate(0,0)" writing-mode="lr" x="1236.09" xml:space="preserve" y="520.87" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133066878980" ObjectName="P"/>
   </metadata>
  </g>
  <g id="132">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="132" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1236.56,533.087) scale(1,1) translate(0,0)" writing-mode="lr" x="1236.09" xml:space="preserve" y="537.87" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133066944516" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="206">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="206" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1236.56,548.087) scale(1,1) translate(0,0)" writing-mode="lr" x="1236.09" xml:space="preserve" y="552.87" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133067010052" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="163">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="163" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,814.361,153) scale(1,1) translate(8.75966e-14,1.13465e-13)" writing-mode="lr" x="814.47" xml:space="preserve" y="157.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133070548996" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="165">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="165" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,599.361,511) scale(1,1) translate(6.37268e-14,-1.1191e-13)" writing-mode="lr" x="599.47" xml:space="preserve" y="515.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133070024708" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="169">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="169" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1655.36,513) scale(1,1) translate(1.80966e-13,5.61773e-14)" writing-mode="lr" x="1655.47" xml:space="preserve" y="517.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133071073284" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,342.625,320.5) scale(0.708333,0.665547) translate(136.706,156.042)" width="30" x="332" xlink:href="#State:红绿圆(方形)_0" y="310.52" zvalue="752"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374922678273" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,342.625,320.5) scale(0.708333,0.665547) translate(136.706,156.042)" width="30" x="332" y="310.52"/></g>
  <g id="1035">
   <use height="30" transform="rotate(0,247,320.5) scale(0.708333,0.665547) translate(97.3309,156.042)" width="30" x="236.38" xlink:href="#State:红绿圆(方形)_0" y="310.52" zvalue="753"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562957846708229" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,247,320.5) scale(0.708333,0.665547) translate(97.3309,156.042)" width="30" x="236.38" y="310.52"/></g>
 </g>
</svg>