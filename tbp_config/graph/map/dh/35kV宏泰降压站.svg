<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549588262914" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:10kV母线PT带消谐装置_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="17.56245852479325" xlink:href="#terminal" y="28.12373692455963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.61245852479333" x2="34.61245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.86245852479331" x2="30.86245852479331" y1="22.68299618381883" y2="19.84040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.6124585247933" x2="26.6124585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.61245852479332" x2="34.61245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666667" x2="31" y1="22.69225544307809" y2="22.69225544307809"/>
   <rect fill-opacity="0" height="3.55" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,30.81,15.38) scale(1,1) translate(0,0)" width="8.92" x="26.34" y="13.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.647721108666" x2="30.647721108666" y1="10.83674821859629" y2="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.71438777533266" x2="28.71438777533266" y1="7.51612768565195" y2="7.51612768565195"/>
   <ellipse cx="14.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.05048569403981" x2="22.05048569403981" y1="34.53084700683308" y2="34.53084700683308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.7608382776216" x2="28.48402243293775" y1="7.754208141873304" y2="7.754208141873304"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.01083827762157" x2="29.40068909960439" y1="6.50420814187332" y2="6.50420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.51083827762159" x2="30.06735576627107" y1="5.004208141873296" y2="5.004208141873296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.89410730945214" x2="23.0877735452272" y1="24.15299989806297" y2="21.53208583485582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58713830216066" x2="22.81883560169719" y1="21.54040359122622" y2="21.54040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51457106407813" x2="19.32090482830307" y1="24.05654513693459" y2="21.43563107372743"/>
   <ellipse cx="14.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.3644003886642" x2="22.3644003886642" y1="30.19213090500035" y2="30.19213090500035"/>
   <ellipse cx="21.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.19220382559271" x2="17.19220382559271" y1="23.07076893362116" y2="23.07076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.4422038255927" x2="16.4422038255927" y1="16.82076893362115" y2="16.82076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.92264294445647" x2="20.87303257583197" y1="16.99992879670395" y2="15.47611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.873032575832" x2="20.37063985073817" y1="15.47611741162252" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.87303257583199" x2="23.32581493230132" y1="15.47611741162254" y2="16.60543752773795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.94220382559271" x2="16.94220382559271" y1="16.32076893362115" y2="16.32076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="16.07581493230131" y1="22.72611741162255" y2="23.85543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="13.12063985073816" y1="22.72611741162255" y2="20.07298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583197" y1="24.24992879670398" y2="22.72611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="16.07581493230131" y1="15.47611741162254" y2="16.60543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="13.12063985073816" y1="15.47611741162253" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583196" y1="16.99992879670396" y2="15.47611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.59103363843511" x2="17.59103363843511" y1="25.15822158129307" y2="28.16666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Disconnector:20210316_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="6.66666666666667" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="6.727157046327149" y2="14.02109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="6.734473004260392" y2="14.02109851866368"/>
  </symbol>
  <symbol id="Disconnector:20210316_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="0.25" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="0.7271570463271502" y2="8.021098518663681"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="0.7344730042603906" y2="8.021098518663679"/>
  </symbol>
  <symbol id="Disconnector:20210316_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="EnergyConsumer:Y-d厂用变_0" viewBox="0,0,20,35">
   <use terminal-index="0" type="0" x="10.16666666666667" xlink:href="#terminal" y="1.166666666666671"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="13" y1="15.25" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="7" y1="20.25" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="34.5" y2="31.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="24.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="7" y1="15.25" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="9" y1="34.5" y2="31.5"/>
   <ellipse cx="9.92" cy="7.6" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10" cy="18.17" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.11171811872235" x2="10.11171811872235" y1="4.753560677018264" y2="7.352548777035198"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.7052656081938" x2="10.11171811872235" y1="9.951536877052112" y2="7.352548777035178"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.518170629250881" x2="10.11171811872233" y1="9.951536877052112" y2="7.352548777035178"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:炉变D-y型_0" viewBox="0,0,17,30">
   <use terminal-index="0" type="0" x="8.666666666666666" xlink:href="#terminal" y="0.1666666666666714"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="8.5" y1="25.25" y2="30.08333333333334"/>
   <path d="M 8.5 5 L 5.5 11 L 11.5 11 L 8.5 5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="9.5" y1="30" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="7.5" y1="30" y2="28"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.597025948103791" x2="8.597025948103791" y1="1.79293756914176" y2="0.2382945839350779"/>
   <ellipse cx="8.42" cy="8.35" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.5" cy="18.92" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.611718118722354" x2="8.611718118722354" y1="16.35874008086165" y2="18.95772818087859"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.2052656081938" x2="8.611718118722351" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.018170629250881" x2="8.611718118722335" y1="21.55671628089551" y2="18.95772818087858"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV宏泰降压站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="48" xlink:href="logo.png" y="43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,176,73) scale(1,1) translate(0,0)" writing-mode="lr" x="176" xml:space="preserve" y="76.5" zvalue="54"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,176.5,72.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="176.5" xml:space="preserve" y="81.69" zvalue="55">35kV宏泰降压站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="54" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,69.625,305.75) scale(1,1) translate(0,0)" width="72.88" x="33.19" y="293.75" zvalue="1886"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.625,305.75) scale(1,1) translate(0,0)" writing-mode="lr" x="69.63" xml:space="preserve" y="310.25" zvalue="1886">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,756.841,491.032) scale(1,1) translate(0,0)" writing-mode="lr" x="756.84" xml:space="preserve" y="495.53" zvalue="7">35kV母线</text>
  <line fill="none" id="67" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382" x2="382" y1="11" y2="1041" zvalue="58"/>
  <line fill="none" id="666" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="146.8704926140824" y2="146.8704926140824" zvalue="1056"/>
  <line fill="none" id="613" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="616.8704926140824" y2="616.8704926140824" zvalue="1058"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="999.0816" y2="1027"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="609" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,952) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="958" zvalue="1062">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="608" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50,986) scale(1,1) translate(0,0)" writing-mode="lr" x="50" xml:space="preserve" y="992" zvalue="1063">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="607" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232,986) scale(1,1) translate(0,0)" writing-mode="lr" x="232" xml:space="preserve" y="992" zvalue="1064">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="606" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="49" xml:space="preserve" y="1020" zvalue="1065">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="605" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="231" xml:space="preserve" y="1020" zvalue="1066">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="602" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.5,646.5) scale(1,1) translate(0,0)" writing-mode="lr" x="73.5" xml:space="preserve" y="651" zvalue="1069">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.054,954) scale(1,1) translate(0,0)" writing-mode="lr" x="233.05" xml:space="preserve" y="960" zvalue="1080">LiXin-01-2020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,143.054,986) scale(1,1) translate(0,0)" writing-mode="lr" x="143.05" xml:space="preserve" y="992" zvalue="1081">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,323.054,986) scale(1,1) translate(0,0)" writing-mode="lr" x="323.05" xml:space="preserve" y="992" zvalue="1082">20201106</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,814.397,366.508) scale(1,1) translate(0,0)" writing-mode="lr" x="814.4" xml:space="preserve" y="371.01" zvalue="1715">391</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,845.683,179.365) scale(1,1) translate(1.79564e-13,0)" writing-mode="lr" x="845.6799999999999" xml:space="preserve" y="183.87" zvalue="1716">35kV丽宏线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1431.15,568.079) scale(1,1) translate(0,0)" writing-mode="lr" x="1431.15" xml:space="preserve" y="572.58" zvalue="1773">396</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1302.1,568.079) scale(1,1) translate(0,0)" writing-mode="lr" x="1302.1" xml:space="preserve" y="572.58" zvalue="1773">395</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1042.59,568.079) scale(1,1) translate(0,0)" writing-mode="lr" x="1042.59" xml:space="preserve" y="572.58" zvalue="1773">393</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1173.06,568.079) scale(1,1) translate(0,0)" writing-mode="lr" x="1173.06" xml:space="preserve" y="572.58" zvalue="1773">394</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1560.19,568.079) scale(1,1) translate(0,0)" writing-mode="lr" x="1560.19" xml:space="preserve" y="572.58" zvalue="1773">397</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,974.333,643.508) scale(1,1) translate(0,0)" writing-mode="lr" x="974.33" xml:space="preserve" y="648.01" zvalue="1778">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1101.59,643.508) scale(1,1) translate(0,0)" writing-mode="lr" x="1101.59" xml:space="preserve" y="648.01" zvalue="1778">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1359.67,643.508) scale(1,1) translate(0,0)" writing-mode="lr" x="1359.67" xml:space="preserve" y="648.01" zvalue="1778">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1488.71,643.508) scale(1,1) translate(0,0)" writing-mode="lr" x="1488.71" xml:space="preserve" y="648.01" zvalue="1778">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="150" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1230.63,643.508) scale(1,1) translate(0,0)" writing-mode="lr" x="1230.63" xml:space="preserve" y="648.01" zvalue="1778">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1270.66,856.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1270.66" xml:space="preserve" y="861.22" zvalue="1781">35kV#2加热器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1402.43,855.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1402.43" xml:space="preserve" y="860.22" zvalue="1781">35kV谐波及补偿装置</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1531.47,855.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1531.47" xml:space="preserve" y="860.22" zvalue="1781">35kV#2厂用变（备用）</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1144.35,855.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1144.35" xml:space="preserve" y="860.22" zvalue="1781">35kV#1加热器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="151" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1007.17,864.079) scale(1,1) translate(0,0)" writing-mode="lr" x="1007.17" xml:space="preserve" y="868.58" zvalue="1782">35kV高压电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,925.815,566.206) scale(1,1) translate(0,0)" writing-mode="lr" x="925.8200000000001" xml:space="preserve" y="570.71" zvalue="1786">392</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,853.556,641.635) scale(1,1) translate(0,0)" writing-mode="lr" x="853.5599999999999" xml:space="preserve" y="646.13" zvalue="1790">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1120.67,209.31) scale(1,1) translate(0,0)" writing-mode="lr" x="1120.67" xml:space="preserve" y="213.81" zvalue="1820">35kV母线电压互感器</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="150" y2="150"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="176" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="1.678571428571331" y1="150" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="150" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="150" y2="150"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="176" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="150" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.6785714285713" x2="363.6785714285713" y1="150" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="176" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="199.5" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="1.678571428571331" y1="176" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="176" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="176" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="199.5" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="176" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.6785714285713" x2="363.6785714285713" y1="176" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="199.5" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="222.25" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="1.678571428571331" y1="199.5" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="199.5" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="199.5" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="222.25" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="199.5" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.6785714285713" x2="363.6785714285713" y1="199.5" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="222.25" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="245" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="1.678571428571331" y1="222.25" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="222.25" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="222.25" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="245" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="222.25" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.6785714285713" x2="363.6785714285713" y1="222.25" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="245" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="267.75" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="1.678571428571331" y1="245" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="245" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="245" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="267.75" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="245" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.6785714285713" x2="363.6785714285713" y1="245" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="430.9166435058594" y2="430.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="468.4066435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="430.9166435058594" y2="430.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="468.4066435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="430.9166435058594" y2="430.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="468.4066435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="430.9166435058594" y2="430.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="468.4066435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="430.9166435058594" y2="430.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="468.4066435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="468.4067435058594" y2="468.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="468.4067435058594" y2="468.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="468.4067435058594" y2="468.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="468.4067435058594" y2="468.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="468.4067435058594" y2="468.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="516.7439435058594" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="516.7439435058594" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="516.7439435058594" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="516.7439435058594" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="516.7439435058594" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="516.7439835058594" y2="516.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="540.9125835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="516.7439835058594" y2="516.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="540.9125835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="516.7439835058594" y2="516.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="540.9125835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="516.7439835058594" y2="516.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="540.9125835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="516.7439835058594" y2="516.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="540.9125835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="540.9127435058595" y2="540.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="540.9127435058595" y2="540.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="540.9127435058595" y2="540.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="540.9127435058595" y2="540.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="540.9127435058595" y2="540.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="589.2499435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="589.2499435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="589.2499435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="589.2499435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="589.2499435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="565.0813435058594" y2="589.2499435058594"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,157.827,303.841) scale(1,1) translate(0,0)" writing-mode="lr" x="157.83" xml:space="preserve" y="308.34" zvalue="1873">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,262.827,303.841) scale(1,1) translate(0,0)" writing-mode="lr" x="262.83" xml:space="preserve" y="308.34" zvalue="1874">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,174.595,451.75) scale(1,1) translate(0,0)" writing-mode="lr" x="174.5952380952378" xml:space="preserve" y="456.2500000000001" zvalue="1875">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.4286,479.5) scale(1,1) translate(0,0)" writing-mode="lr" x="56.42857142857133" xml:space="preserve" y="484.0000000000001" zvalue="1877">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.4286,505) scale(1,1) translate(0,0)" writing-mode="lr" x="56.42857142857133" xml:space="preserve" y="509.5" zvalue="1878">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.4286,530.5) scale(1,1) translate(0,0)" writing-mode="lr" x="56.42857142857133" xml:space="preserve" y="535" zvalue="1879">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.4286,555) scale(1,1) translate(0,0)" writing-mode="lr" x="55.42857142857133" xml:space="preserve" y="559.5" zvalue="1880">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.4286,581.5) scale(1,1) translate(0,0)" writing-mode="lr" x="56.42857142857133" xml:space="preserve" y="586" zvalue="1881">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,38.4286,163) scale(1,1) translate(0,0)" writing-mode="lr" x="38.43" xml:space="preserve" y="168.5" zvalue="1882">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,218.429,163) scale(1,1) translate(0,0)" writing-mode="lr" x="218.43" xml:space="preserve" y="168.5" zvalue="1883">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.5536,188.5) scale(1,1) translate(0,0)" writing-mode="lr" x="55.55" xml:space="preserve" y="193" zvalue="1884">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,773.73,564.397) scale(1,1) translate(0,0)" writing-mode="lr" x="773.73" xml:space="preserve" y="568.9" zvalue="1907">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1134.5,399) scale(1,1) translate(0,3.46834e-13)" writing-mode="lr" x="1134.5" xml:space="preserve" y="403.5" zvalue="1918">3903</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="189" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,897,772) scale(1,1) translate(0,0)" writing-mode="lr" x="897" xml:space="preserve" y="776.5" zvalue="1922">35kV#1厂用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,742.5,736.5) scale(1,1) translate(0,0)" writing-mode="lr" x="742.5" xml:space="preserve" y="741" zvalue="1924">35kV站用变</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="33.19" y="293.75" zvalue="1886"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="6">
   <path class="kv35" d="M 673 510.81 L 1593.62 510.81" stroke-width="4" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674246197252" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674246197252"/></metadata>
  <path d="M 673 510.81 L 1593.62 510.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="236">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,181.222,511.167) scale(1,1) translate(-3.42689e-14,0)" writing-mode="lr" x="181.34" xml:space="preserve" y="516.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125544853508" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="237">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="237" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,181.222,533.667) scale(1,1) translate(0,0)" writing-mode="lr" x="181.34" xml:space="preserve" y="538.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125544919044" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="238">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="238" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,181.222,558.167) scale(1,1) translate(-3.42689e-14,0)" writing-mode="lr" x="181.34" xml:space="preserve" y="563.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125544984580" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="235">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="235" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,181.222,485.667) scale(1,1) translate(-3.42689e-14,0)" writing-mode="lr" x="181.34" xml:space="preserve" y="490.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125545115652" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,679,529.81) scale(1,1) translate(0,0)" writing-mode="lr" x="678.53" xml:space="preserve" y="534.59" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125545050116" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="8" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,844.683,103.025) scale(1,1) translate(8.87826e-14,-1.42898e-13)" writing-mode="lr" x="844.21" xml:space="preserve" y="107.7" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125545902084" ObjectName="P"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="17" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,844.683,126.468) scale(1,1) translate(8.87826e-14,-1.79337e-13)" writing-mode="lr" x="844.21" xml:space="preserve" y="131.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125545967620" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,844.683,149.912) scale(1,1) translate(8.87826e-14,-2.15775e-13)" writing-mode="lr" x="844.21" xml:space="preserve" y="154.59" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125546033156" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="53" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,109,162) scale(1,1) translate(0,0)" writing-mode="lr" x="109.15" xml:space="preserve" y="168.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125557043204" ObjectName=""/>
   </metadata>
  </g>
  <g id="1035">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="1035" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,292,161) scale(1,1) translate(0,0)" writing-mode="lr" x="292.15" xml:space="preserve" y="167.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125557108740" ObjectName=""/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,140,187.71) scale(1,1) translate(0,0)" writing-mode="lr" x="139.54" xml:space="preserve" y="192.49" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125545246724" ObjectName="F"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="5" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,922.007,833.5) scale(1,1) translate(0,0)" writing-mode="lr" x="921.54" xml:space="preserve" y="838.28" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125553242116" ObjectName="P"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="11" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,922.007,870.5) scale(1,1) translate(0,0)" writing-mode="lr" x="921.54" xml:space="preserve" y="875.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125553307652" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="13" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,922.007,907.5) scale(1,1) translate(0,0)" writing-mode="lr" x="921.54" xml:space="preserve" y="912.28" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125553373188" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="16" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1273.12,879.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1272.65" xml:space="preserve" y="884.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125550358532" ObjectName="P"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="20" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1273.12,916.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1272.65" xml:space="preserve" y="921.5" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125550424068" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="21" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1273.12,953.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1272.65" xml:space="preserve" y="958.5" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125550489604" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="BreakerClass">
  <g id="12">
   <use class="kv35" height="20" transform="rotate(0,845.683,367.508) scale(3.14286,3.14286) translate(-565.887,-229.145)" width="10" x="829.9682539682539" xlink:href="#Breaker:小车断路器_0" y="336.0793650793651" zvalue="1714"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924521295877" ObjectName="35kV丽宏线391断路器"/>
   <cge:TPSR_Ref TObjectID="6473924521295877"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,845.683,367.508) scale(3.14286,3.14286) translate(-565.887,-229.145)" width="10" x="829.9682539682539" y="336.0793650793651"/></g>
  <g id="109">
   <use class="kv35" height="20" transform="rotate(0,1402.43,569.079) scale(3.14286,3.14286) translate(-945.489,-366.58)" width="10" x="1386.717855482998" xlink:href="#Breaker:小车断路器_0" y="537.6507885645306" zvalue="1772"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924562190341" ObjectName="35kV谐波及补偿装置396断路器"/>
   <cge:TPSR_Ref TObjectID="6473924562190341"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1402.43,569.079) scale(3.14286,3.14286) translate(-945.489,-366.58)" width="10" x="1386.717855482998" y="537.6507885645306"/></g>
  <g id="164">
   <use class="kv35" height="20" transform="rotate(0,1015.31,569.079) scale(3.14286,3.14286) translate(-681.541,-366.58)" width="10" x="999.5928555851779" xlink:href="#Breaker:小车断路器_0" y="537.6507886326503" zvalue="1772"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924521689093" ObjectName="35kV高压电机393断路器"/>
   <cge:TPSR_Ref TObjectID="6473924521689093"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1015.31,569.079) scale(3.14286,3.14286) translate(-681.541,-366.58)" width="10" x="999.5928555851779" y="537.6507886326503"/></g>
  <g id="133">
   <use class="kv35" height="20" transform="rotate(0,1144.35,569.079) scale(3.14286,3.14286) translate(-769.524,-366.58)" width="10" x="1128.634522217785" xlink:href="#Breaker:小车断路器_0" y="537.6507885645306" zvalue="1772"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924521623557" ObjectName="35kV#1加热器394断路器"/>
   <cge:TPSR_Ref TObjectID="6473924521623557"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1144.35,569.079) scale(3.14286,3.14286) translate(-769.524,-366.58)" width="10" x="1128.634522217785" y="537.6507885645306"/></g>
  <g id="122">
   <use class="kv35" height="20" transform="rotate(0,1273.39,569.079) scale(3.14286,3.14286) translate(-857.506,-366.58)" width="10" x="1257.676188850392" xlink:href="#Breaker:小车断路器_0" y="537.6507885645306" zvalue="1772"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924521558021" ObjectName="35kV#2加热器（备用）395断路器"/>
   <cge:TPSR_Ref TObjectID="6473924521558021"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1273.39,569.079) scale(3.14286,3.14286) translate(-857.506,-366.58)" width="10" x="1257.676188850392" y="537.6507885645306"/></g>
  <g id="98">
   <use class="kv35" height="20" transform="rotate(0,1531.47,569.079) scale(3.14286,3.14286) translate(-1033.47,-366.58)" width="10" x="1515.759522115605" xlink:href="#Breaker:小车断路器_0" y="537.6507885645306" zvalue="1772"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924521426949" ObjectName="35kV#2厂用变（备用）397断路器"/>
   <cge:TPSR_Ref TObjectID="6473924521426949"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1531.47,569.079) scale(3.14286,3.14286) translate(-1033.47,-366.58)" width="10" x="1515.759522115605" y="537.6507885645306"/></g>
  <g id="183">
   <use class="kv35" height="20" transform="rotate(0,898.529,567.206) scale(3.14286,3.14286) translate(-601.919,-365.303)" width="10" x="882.8150778074" xlink:href="#Breaker:小车断路器_0" y="535.7777777777778" zvalue="1785"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924521361413" ObjectName="35kV#1厂用变392断路器"/>
   <cge:TPSR_Ref TObjectID="6473924521361413"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,898.529,567.206) scale(3.14286,3.14286) translate(-601.919,-365.303)" width="10" x="882.8150778074" y="535.7777777777778"/></g>
  <g id="179">
   <use class="kv35" height="20" transform="rotate(0,742.016,565.397) scale(3.14286,3.14286) translate(-495.206,-364.069)" width="10" x="726.3015873015873" xlink:href="#Breaker:小车断路器_0" y="533.9682488819909" zvalue="1906"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924562255876" ObjectName="35kV站用变3901"/>
   <cge:TPSR_Ref TObjectID="6473924562255876"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,742.016,565.397) scale(3.14286,3.14286) translate(-495.206,-364.069)" width="10" x="726.3015873015873" y="533.9682488819909"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="39">
   <path class="kv35" d="M 845.68 395.79 L 845.68 510.81" stroke-width="1" zvalue="1717"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="12@1" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 845.68 395.79 L 845.68 510.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv35" d="M 845.68 218.26 L 845.68 338.44" stroke-width="1" zvalue="1722"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@0" LinkObjectIDznd="12@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 845.68 218.26 L 845.68 338.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv35" d="M 1126.98 347.05 L 1105.33 347.05" stroke-width="1" zvalue="1740"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="186" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.98 347.05 L 1105.33 347.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv35" d="M 1144.35 540.01 L 1144.35 510.81" stroke-width="1" zvalue="1774"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@0" LinkObjectIDznd="6@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1144.35 540.01 L 1144.35 510.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv35" d="M 1015.31 540.01 L 1015.31 510.81" stroke-width="1" zvalue="1774"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@0" LinkObjectIDznd="6@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1015.31 540.01 L 1015.31 510.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv35" d="M 1531.47 540.01 L 1531.47 510.81" stroke-width="1" zvalue="1774"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="6@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1531.47 540.01 L 1531.47 510.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="kv35" d="M 1402.43 540.01 L 1402.43 510.81" stroke-width="1" zvalue="1774"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="6@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1402.43 540.01 L 1402.43 510.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv35" d="M 1273.39 540.01 L 1273.39 510.81" stroke-width="1" zvalue="1774"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@0" LinkObjectIDznd="6@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1273.39 540.01 L 1273.39 510.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv35" d="M 1273.39 802.79 L 1273.39 597.37" stroke-width="1" zvalue="1781"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="122@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1273.39 802.79 L 1273.39 597.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv35" d="M 1144.35 803.51 L 1144.35 597.37" stroke-width="1" zvalue="1781"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="133@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1144.35 803.51 L 1144.35 597.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv35" d="M 1402.43 803.51 L 1402.43 597.37" stroke-width="1" zvalue="1781"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="109@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1402.43 803.51 L 1402.43 597.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv35" d="M 1015.31 801.61 L 1015.31 597.37" stroke-width="1" zvalue="1781"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="156@0" LinkObjectIDznd="164@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1015.31 801.61 L 1015.31 597.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv35" d="M 1531.47 802.51 L 1531.47 597.37" stroke-width="1" zvalue="1781"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="98@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1531.47 802.51 L 1531.47 597.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv35" d="M 898.53 538.13 L 898.53 510.81" stroke-width="1" zvalue="1787"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@0" LinkObjectIDznd="6@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.53 538.13 L 898.53 510.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv35" d="M 1121.02 633.44 L 1121.02 618.19 L 1144.35 618.19" stroke-width="1" zvalue="1849"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="123" MaxPinNum="2"/>
   </metadata>
  <path d="M 1121.02 633.44 L 1121.02 618.19 L 1144.35 618.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv35" d="M 991.98 633.44 L 991.98 618.19 L 1015.31 618.19" stroke-width="1" zvalue="1849"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="155" MaxPinNum="2"/>
   </metadata>
  <path d="M 991.98 633.44 L 991.98 618.19 L 1015.31 618.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv35" d="M 1379.1 633.44 L 1379.1 618.19 L 1402.43 618.19" stroke-width="1" zvalue="1849"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="102" MaxPinNum="2"/>
   </metadata>
  <path d="M 1379.1 633.44 L 1379.1 618.19 L 1402.43 618.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv35" d="M 1508.14 633.44 L 1508.14 618.19 L 1531.47 618.19" stroke-width="1" zvalue="1849"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@0" LinkObjectIDznd="90" MaxPinNum="2"/>
   </metadata>
  <path d="M 1508.14 633.44 L 1508.14 618.19 L 1531.47 618.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv35" d="M 1250.06 633.44 L 1250.06 618.19 L 1273.39 618.19" stroke-width="1" zvalue="1849"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@0" LinkObjectIDznd="113" MaxPinNum="2"/>
   </metadata>
  <path d="M 1250.06 633.44 L 1250.06 618.19 L 1273.39 618.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv35" d="M 1531.47 618.19 L 1555.96 618.19 L 1555.96 631.41" stroke-width="1" zvalue="1850"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90" LinkObjectIDznd="95@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1531.47 618.19 L 1555.96 618.19 L 1555.96 631.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv35" d="M 1015.31 618.19 L 1038.8 618.19 L 1038.8 630.41" stroke-width="1" zvalue="1850"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44" LinkObjectIDznd="160@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1015.31 618.19 L 1038.8 618.19 L 1038.8 630.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv35" d="M 1144.35 618.19 L 1168.84 618.19 L 1168.84 630.41" stroke-width="1" zvalue="1850"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126" LinkObjectIDznd="130@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1144.35 618.19 L 1168.84 618.19 L 1168.84 630.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv35" d="M 1402.43 618.19 L 1426.92 618.19 L 1426.92 631.41" stroke-width="1" zvalue="1850"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100" LinkObjectIDznd="106@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1402.43 618.19 L 1426.92 618.19 L 1426.92 631.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv35" d="M 1273.39 618.19 L 1298.88 618.19 L 1298.88 630.41" stroke-width="1" zvalue="1850"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111" LinkObjectIDznd="119@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1273.39 618.19 L 1298.88 618.19 L 1298.88 630.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv35" d="M 875.2 631.56 L 875.2 614 L 923.02 614 L 923.02 629.54" stroke-width="1" zvalue="1851"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="172@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 875.2 631.56 L 875.2 614 L 923.02 614 L 923.02 629.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv35" d="M 898.53 595.49 L 898.53 614" stroke-width="1" zvalue="1852"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@1" LinkObjectIDznd="46" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.53 595.49 L 898.53 614" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv35" d="M 897.83 614 L 897.83 688.68" stroke-width="1" zvalue="1853"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46" LinkObjectIDznd="188@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 897.83 614 L 897.83 688.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv35" d="M 845.68 289 L 869.92 289 L 869.92 271.1" stroke-width="1" zvalue="1855"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85" LinkObjectIDznd="3@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 845.68 289 L 869.92 289 L 869.92 271.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv35" d="M 742.02 536.33 L 742.02 510.81" stroke-width="1" zvalue="1908"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@0" LinkObjectIDznd="6@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 742.02 536.33 L 742.02 510.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv35" d="M 742.02 593.68 L 742.02 682.17" stroke-width="1" zvalue="1913"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@1" LinkObjectIDznd="192@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 742.02 593.68 L 742.02 682.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv35" d="M 714.04 626.73 L 714.04 610 L 742.02 610" stroke-width="1" zvalue="1915"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@0" LinkObjectIDznd="173" MaxPinNum="2"/>
   </metadata>
  <path d="M 714.04 626.73 L 714.04 610 L 742.02 610" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv35" d="M 1105.33 303.42 L 1105.33 374.07" stroke-width="1" zvalue="1919"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="184@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1105.33 303.42 L 1105.33 374.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv35" d="M 1106 425.85 L 1106 510.81" stroke-width="1" zvalue="1920"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@1" LinkObjectIDznd="6@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1106 425.85 L 1106 510.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="114">
   <use class="kv35" height="20" transform="rotate(180,1126.98,317.365) scale(2.36905,3.39286) translate(-637.577,-199.897)" width="20" x="1103.285714285714" xlink:href="#Accessory:线路PT3_0" y="283.4365178971065" zvalue="1739"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449842446342" ObjectName="电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1126.98,317.365) scale(2.36905,3.39286) translate(-637.577,-199.897)" width="20" x="1103.285714285714" y="283.4365178971065"/></g>
  <g id="119">
   <use class="kv35" height="26" transform="rotate(0,1298.85,648.079) scale(1.07143,1.42857) translate(-86.1611,-188.852)" width="12" x="1292.416666598547" xlink:href="#Accessory:避雷器1_0" y="629.5079314216734" zvalue="1776"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449841659910" ObjectName="35kV#2加热器（备用）避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1298.85,648.079) scale(1.07143,1.42857) translate(-86.1611,-188.852)" width="12" x="1292.416666598547" y="629.5079314216734"/></g>
  <g id="95">
   <use class="kv35" height="26" transform="rotate(0,1555.93,649.079) scale(1.07143,1.42857) translate(-103.3,-189.152)" width="12" x="1549.499999863761" xlink:href="#Accessory:避雷器1_0" y="630.5079314216734" zvalue="1776"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449841135622" ObjectName="35kV#2厂用变（备用）避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1555.93,649.079) scale(1.07143,1.42857) translate(-103.3,-189.152)" width="12" x="1549.499999863761" y="630.5079314216734"/></g>
  <g id="130">
   <use class="kv35" height="26" transform="rotate(0,1168.8,648.079) scale(1.07143,1.42857) translate(-77.4917,-188.852)" width="12" x="1162.37499996594" xlink:href="#Accessory:避雷器1_0" y="629.5079314216734" zvalue="1776"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449841922054" ObjectName="35kV#1加热器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1168.8,648.079) scale(1.07143,1.42857) translate(-77.4917,-188.852)" width="12" x="1162.37499996594" y="629.5079314216734"/></g>
  <g id="160">
   <use class="kv35" height="26" transform="rotate(0,1038.76,648.079) scale(1.07143,1.42857) translate(-68.8222,-188.852)" width="12" x="1032.333333333333" xlink:href="#Accessory:避雷器1_0" y="629.5079314897931" zvalue="1776"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449842184198" ObjectName="35kV高压电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1038.76,648.079) scale(1.07143,1.42857) translate(-68.8222,-188.852)" width="12" x="1032.333333333333" y="629.5079314897931"/></g>
  <g id="106">
   <use class="kv35" height="26" transform="rotate(0,1426.89,649.079) scale(1.07143,1.42857) translate(-94.6972,-189.152)" width="12" x="1420.458333231154" xlink:href="#Accessory:避雷器1_0" y="630.5079314216734" zvalue="1776"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449841397766" ObjectName="谐波及补偿装置避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1426.89,649.079) scale(1.07143,1.42857) translate(-94.6972,-189.152)" width="12" x="1420.458333231154" y="630.5079314216734"/></g>
  <g id="172">
   <use class="kv35" height="26" transform="rotate(0,922.984,647.206) scale(1.07143,1.42857) translate(-61.1037,-188.59)" width="12" x="916.5555555555557" xlink:href="#Accessory:避雷器1_0" y="628.6349206349206" zvalue="1788"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449840807942" ObjectName="35kV#1厂用变392避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,922.984,647.206) scale(1.07143,1.42857) translate(-61.1037,-188.59)" width="12" x="916.5555555555557" y="628.6349206349206"/></g>
  <g id="3">
   <use class="kv35" height="26" transform="rotate(0,869.889,253.429) scale(1.07143,-1.42857) translate(-57.564,-425.257)" width="12" x="863.4603174603174" xlink:href="#Accessory:避雷器1_0" y="234.8571428571428" zvalue="1815"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449840873478" ObjectName="35kV丽宏线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,869.889,253.429) scale(1.07143,-1.42857) translate(-57.564,-425.257)" width="12" x="863.4603174603174" y="234.8571428571428"/></g>
  <g id="9">
   <use class="kv35" height="30" transform="rotate(180,1105.43,268.421) scale(1.63265,-2.66667) translate(-417.282,-344.078)" width="35" x="1076.857142857143" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="228.4206448812334" zvalue="1819"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449842380806" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1105.43,268.421) scale(1.63265,-2.66667) translate(-417.282,-344.078)" width="35" x="1076.857142857143" y="228.4206448812334"/></g>
  <g id="177">
   <use class="kv35" height="26" transform="rotate(0,714,644.397) scale(1.07143,1.42857) translate(-47.1714,-187.748)" width="12" x="707.5714285714287" xlink:href="#Accessory:避雷器1_0" y="625.8253917391338" zvalue="1909"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449842249734" ObjectName="备用避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,714,644.397) scale(1.07143,1.42857) translate(-47.1714,-187.748)" width="12" x="707.5714285714287" y="625.8253917391338"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="105">
   <use class="kv35" height="20" transform="rotate(0,1379.03,647.365) scale(1.42857,1.42857) translate(-411.566,-189.924)" width="10" x="1371.886904659725" xlink:href="#GroundDisconnector:地刀_0" y="633.0793599931019" zvalue="1777"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449841332230" ObjectName="谐波及补偿装置39667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449841332230"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1379.03,647.365) scale(1.42857,1.42857) translate(-411.566,-189.924)" width="10" x="1371.886904659725" y="633.0793599931019"/></g>
  <g id="158">
   <use class="kv35" height="20" transform="rotate(0,991.905,647.365) scale(1.42857,1.42857) translate(-295.429,-189.924)" width="10" x="984.7619047619046" xlink:href="#GroundDisconnector:地刀_0" y="633.0793600612216" zvalue="1777"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449842118662" ObjectName="35kV高压电机39367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449842118662"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,991.905,647.365) scale(1.42857,1.42857) translate(-295.429,-189.924)" width="10" x="984.7619047619046" y="633.0793600612216"/></g>
  <g id="118">
   <use class="kv35" height="20" transform="rotate(0,1249.99,647.365) scale(1.42857,1.42857) translate(-372.854,-189.924)" width="10" x="1242.845238027118" xlink:href="#GroundDisconnector:地刀_0" y="633.0793599931019" zvalue="1777"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450056486917" ObjectName="35kV#2加热器（备用）39567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450056486917"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1249.99,647.365) scale(1.42857,1.42857) translate(-372.854,-189.924)" width="10" x="1242.845238027118" y="633.0793599931019"/></g>
  <g id="93">
   <use class="kv35" height="20" transform="rotate(0,1508.07,647.365) scale(1.42857,1.42857) translate(-450.279,-189.924)" width="10" x="1500.928571292332" xlink:href="#GroundDisconnector:地刀_0" y="633.0793599931019" zvalue="1777"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450056028165" ObjectName="35kV#2厂用变（备用）39767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450056028165"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1508.07,647.365) scale(1.42857,1.42857) translate(-450.279,-189.924)" width="10" x="1500.928571292332" y="633.0793599931019"/></g>
  <g id="129">
   <use class="kv35" height="20" transform="rotate(0,1120.95,647.365) scale(1.42857,1.42857) translate(-334.141,-189.924)" width="10" x="1113.803571394511" xlink:href="#GroundDisconnector:地刀_0" y="633.0793599931019" zvalue="1777"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449841856518" ObjectName="35kV#1加热器39467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449841856518"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1120.95,647.365) scale(1.42857,1.42857) translate(-334.141,-189.924)" width="10" x="1113.803571394511" y="633.0793599931019"/></g>
  <g id="171">
   <use class="kv35" height="20" transform="rotate(0,875.127,645.492) scale(1.42857,1.42857) translate(-260.395,-189.362)" width="10" x="867.9841269841269" xlink:href="#GroundDisconnector:地刀_0" y="631.2063492063492" zvalue="1789"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449840742406" ObjectName="35kV#1厂用变39267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449840742406"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,875.127,645.492) scale(1.42857,1.42857) translate(-260.395,-189.362)" width="10" x="867.9841269841269" y="631.2063492063492"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="91">
   <use class="kv35" height="30" transform="rotate(0,1531.47,821.794) scale(1.42857,-1.42857) translate(-456.871,-1390.62)" width="12" x="1522.902379258462" xlink:href="#EnergyConsumer:负荷_0" y="800.3650742788163" zvalue="1780"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449840939014" ObjectName="35kV#2厂用变（备用）"/>
   <cge:TPSR_Ref TObjectID="6192449840939014"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1531.47,821.794) scale(1.42857,-1.42857) translate(-456.871,-1390.62)" width="12" x="1522.902379258462" y="800.3650742788163"/></g>
  <g id="127">
   <use class="kv35" height="30" transform="rotate(0,1144.35,822.794) scale(1.42857,-1.42857) translate(-340.733,-1392.32)" width="12" x="1135.777379360642" xlink:href="#EnergyConsumer:负荷_0" y="801.3650742788163" zvalue="1780"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449841725446" ObjectName="35kV#1加热器"/>
   <cge:TPSR_Ref TObjectID="6192449841725446"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1144.35,822.794) scale(1.42857,-1.42857) translate(-340.733,-1392.32)" width="12" x="1135.777379360642" y="801.3650742788163"/></g>
  <g id="103">
   <use class="kv35" height="30" transform="rotate(0,1402.43,822.794) scale(1.42857,-1.42857) translate(-418.158,-1392.32)" width="12" x="1393.860712625856" xlink:href="#EnergyConsumer:负荷_0" y="801.3650742788163" zvalue="1780"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450049802245" ObjectName="35kV谐波及补偿装置"/>
   <cge:TPSR_Ref TObjectID="6192450049802245"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1402.43,822.794) scale(1.42857,-1.42857) translate(-418.158,-1392.32)" width="12" x="1393.860712625856" y="801.3650742788163"/></g>
  <g id="156">
   <use class="kv35" height="30" transform="rotate(0,1015.01,823.183) scale(1.79855,1.4545) translate(-443.873,-250.408)" width="17" x="999.7196822769406" xlink:href="#EnergyConsumer:炉变D-y型_0" y="801.3650743469359" zvalue="1780"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449841987590" ObjectName="35kV高压电机"/>
   <cge:TPSR_Ref TObjectID="6192449841987590"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1015.01,823.183) scale(1.79855,1.4545) translate(-443.873,-250.408)" width="17" x="999.7196822769406" y="801.3650743469359"/></g>
  <g id="116">
   <use class="kv35" height="35" transform="rotate(0,1273.12,822.794) scale(1.60905,1.22449) translate(-475.805,-146.917)" width="20" x="1257.031822944579" xlink:href="#EnergyConsumer:Y-d厂用变_0" y="801.3650742788163" zvalue="1780"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449841463302" ObjectName="35kV#2加热器"/>
   <cge:TPSR_Ref TObjectID="6192449841463302"/></metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1273.12,822.794) scale(1.60905,1.22449) translate(-475.805,-146.917)" width="20" x="1257.031822944579" y="801.3650742788163"/></g>
  <g id="188">
   <use class="kv35" height="35" transform="rotate(0,897.5,719.25) scale(1.95,1.87143) translate(-427.744,-319.668)" width="20" x="878" xlink:href="#EnergyConsumer:Y-d厂用变_0" y="686.5" zvalue="1921"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450050719749" ObjectName="35kV#1厂用变"/>
   <cge:TPSR_Ref TObjectID="6192450050719749"/></metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,897.5,719.25) scale(1.95,1.87143) translate(-427.744,-319.668)" width="20" x="878" y="686.5"/></g>
  <g id="192">
   <use class="kv35" height="35" transform="rotate(0,741.5,698.5) scale(1,1) translate(0,0)" width="20" x="731.5" xlink:href="#EnergyConsumer:Y-d厂用变_0" y="681" zvalue="1923"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450050785285" ObjectName="35kV站用变"/>
   <cge:TPSR_Ref TObjectID="6192450050785285"/></metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,741.5,698.5) scale(1,1) translate(0,0)" width="20" x="731.5" y="681"/></g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,294.339,302.643) scale(0.708333,0.665547) translate(116.824,147.069)" width="30" x="283.71" xlink:href="#State:红绿圆(方形)_0" y="292.66" zvalue="1891"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374890500099" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,294.339,302.643) scale(0.708333,0.665547) translate(116.824,147.069)" width="30" x="283.71" y="292.66"/></g>
  <g id="56">
   <use height="30" transform="rotate(0,198.714,302.643) scale(0.708333,0.665547) translate(77.4485,147.069)" width="30" x="188.09" xlink:href="#State:红绿圆(方形)_0" y="292.66" zvalue="1892"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562951435649028" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,198.714,302.643) scale(0.708333,0.665547) translate(77.4485,147.069)" width="30" x="188.09" y="292.66"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="184">
   <use class="kv35" height="26" transform="rotate(180,1106,400) scale(2,2) translate(-547,-187)" width="12" x="1094" xlink:href="#Disconnector:20210316_0" y="373.9999999999999" zvalue="1917"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450050654213" ObjectName="35kV母线电压互感器3903隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450050654213"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1106,400) scale(2,2) translate(-547,-187)" width="12" x="1094" y="373.9999999999999"/></g>
 </g>
</svg>