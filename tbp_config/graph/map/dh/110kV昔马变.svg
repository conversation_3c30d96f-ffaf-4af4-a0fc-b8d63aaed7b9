<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549685780481" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:线路PT三绕组_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="20" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="24" y2="24"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,11) scale(1,1) translate(0,0)" width="6" x="17" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="32" y1="17" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.98040845230975" x2="31.98040845230975" y1="16.76788570496156" y2="23.53635804601289"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.98821300514558" x2="19.98821300514558" y1="1.029523490692871" y2="18.75"/>
   <path d="M 30.0147 23.4481 L 34.2865 23.4481 L 32.115 28.6155 L 30.0147 23.4481" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <rect fill-opacity="0" height="6.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,32.04,24.56) scale(1,1) translate(0,0)" width="15.34" x="24.37" y="21.53"/>
   <ellipse cx="19.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.22179295459144" x2="32.22179295459144" y1="32.22550978083666" y2="36.2625308385742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.01788570496155" x2="35.42570020422134" y1="36.23023467011234" y2="36.23023467011234"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.26384963537316" x2="34.53572596821302" y1="37.52208140858838" y2="37.52208140858838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.15382387138148" x2="33.11176719059974" y1="38.49096646244535" y2="38.49096646244535"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="32.66666666666667" y2="32.66666666666667"/>
   <ellipse cx="12.01" cy="28.48" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.166666666666664" x2="15.16666666666666" y1="28.91666666666667" y2="28.91666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Accessory:线路PT99_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="35" xlink:href="#terminal" y="6.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.5" x2="1.5" y1="4" y2="9"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.27377700368697" x2="20.43246971500333" y1="6.765818666751574" y2="6.765818666751574"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.6817302644139" x2="34.98333333333333" y1="6.765818666751574" y2="6.765818666751574"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.55947355115261" x2="24.55947355115261" y1="6.895129129519741" y2="14.09675167759026"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.46102695297582" x2="5.583333333333334" y1="6.846289710456816" y2="6.846289710456816"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="20.47373975336484" x2="20.47373975336484" y1="6.846289710456816" y2="6.846289710456816"/>
   <ellipse cx="24.54" cy="18.35" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="20.47373975336483" x2="20.47373975336483" y1="3.466505874834564" y2="10.30654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.61665011096792" x2="31.61665011096792" y1="6.846289710456816" y2="6.846289710456816"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="17.37848687625285" x2="17.37848687625285" y1="3.466505874834558" y2="10.30654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="31.6166501109679" x2="31.6166501109679" y1="3.466505874834564" y2="10.30654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="28.52139723385593" x2="28.52139723385593" y1="3.466505874834564" y2="10.30654458978453"/>
   <ellipse cx="27.04" cy="22.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="22.04" cy="22.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Accessory:线路PT带避雷器0904_0" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="12.91666666666667" y1="23.75" y2="23.75"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,11) scale(1,1) translate(0,0)" width="6" x="7" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.988213005145578" x2="9.988213005145578" y1="1.029523490692871" y2="18.75"/>
   <ellipse cx="9.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="12.91666666666667" y1="32.91666666666667" y2="32.91666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="ACLineSegment:线路带壁雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="14.875" xlink:href="#terminal" y="39.83880854456296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.25" x2="6.25" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="5.75" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.25" x2="7.25" y1="32.25" y2="32.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="8.25" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="6.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="4.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.91022336769755" x2="14.91022336769755" y1="39.75" y2="0.8333333333333321"/>
   <path d="M 14.75 9.25 L 5.75 9.25 L 5.75 21.25 L 5.75 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.79,21.17) scale(1,1) translate(0,0)" width="6.08" x="2.75" y="14"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变无融断_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <path d="M 10 9 L 10 0" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="26.83333333333333" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="10.8868007916835" y2="12.55855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="14.23031840279781" y2="12.55855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
  <symbol id="State:间隔模板_0" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(85,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="State:间隔模板_1" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(185,185,185)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV昔马变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="62.31" xlink:href="logo.png" y="26.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="177" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,187.153,67.7136) scale(1,1) translate(-1.38349e-14,0)" writing-mode="lr" x="187.15" xml:space="preserve" y="71.20999999999999" zvalue="10073"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,216,67.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="216" xml:space="preserve" y="76.69" zvalue="10074">110kV昔马变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="109" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,189.531,410.25) scale(1,1) translate(0,0)" width="72.88" x="153.09" y="398.25" zvalue="10501"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,189.531,410.25) scale(1,1) translate(0,0)" writing-mode="lr" x="189.53" xml:space="preserve" y="414.75" zvalue="10501">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="108" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,87.625,410.25) scale(1,1) translate(0,0)" width="72.88" x="51.19" y="398.25" zvalue="10502"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,87.625,410.25) scale(1,1) translate(0,0)" writing-mode="lr" x="87.63" xml:space="preserve" y="414.75" zvalue="10502">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="71" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,87.625,369.75) scale(1,1) translate(0,0)" width="72.88" x="51.19" y="357.75" zvalue="10503"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,87.625,369.75) scale(1,1) translate(0,0)" writing-mode="lr" x="87.63" xml:space="preserve" y="374.25" zvalue="10503">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,781.076,365.091) scale(1,1) translate(0,0)" writing-mode="lr" x="781.08" xml:space="preserve" y="369.59" zvalue="7577">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,610.076,686.535) scale(1,1) translate(0,0)" writing-mode="lr" x="610.08" xml:space="preserve" y="691.04" zvalue="7716">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1433.98,331.52) scale(1,1) translate(0,0)" writing-mode="lr" x="1433.98" xml:space="preserve" y="336.02" zvalue="8016">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1482.61,369.394) scale(1,1) translate(0,0)" writing-mode="lr" x="1482.61" xml:space="preserve" y="373.89" zvalue="8030">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1482.61,318.644) scale(1,1) translate(0,0)" writing-mode="lr" x="1482.61" xml:space="preserve" y="323.14" zvalue="8035">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="219" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1454.75,227.091) scale(1,1) translate(0,0)" writing-mode="lr" x="1454.75" xml:space="preserve" y="231.59" zvalue="9703">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="631" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1110.98,440.584) scale(1,1) translate(0,0)" writing-mode="lr" x="1110.98" xml:space="preserve" y="445.08" zvalue="9723">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="629" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1109.55,410.68) scale(1,1) translate(0,0)" writing-mode="lr" x="1109.55" xml:space="preserve" y="415.18" zvalue="9725">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="628" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1161.11,649.542) scale(1,1) translate(0,0)" writing-mode="lr" x="1161.11" xml:space="preserve" y="654.04" zvalue="9729">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="625" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1150.25,677.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1150.25" xml:space="preserve" y="681.63" zvalue="9735">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="854" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,754.083,654.096) scale(1,1) translate(0,0)" writing-mode="lr" x="754.08" xml:space="preserve" y="658.6" zvalue="9762">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="851" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,781.472,561.667) scale(1,1) translate(0,0)" writing-mode="lr" x="781.47" xml:space="preserve" y="566.17" zvalue="9772">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,932.155,300.645) scale(1,1) translate(-2.04537e-13,0)" writing-mode="lr" x="932.15" xml:space="preserve" y="305.14" zvalue="9968">161</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,875.526,169.63) scale(1,1) translate(0,0)" writing-mode="lr" x="875.53" xml:space="preserve" y="174.13" zvalue="9972">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,931.425,355.549) scale(1,1) translate(0,0)" writing-mode="lr" x="931.42" xml:space="preserve" y="360.05" zvalue="9975">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,876.276,257.065) scale(1,1) translate(0,0)" writing-mode="lr" x="876.28" xml:space="preserve" y="261.56" zvalue="10028">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,930.925,207.088) scale(1,1) translate(0,0)" writing-mode="lr" x="930.92" xml:space="preserve" y="211.59" zvalue="10032">6</text>
  <line fill="none" id="174" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="152.8704926140824" y2="152.8704926140824" zvalue="10076"/>
  <line fill="none" id="173" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="380" x2="380" y1="11" y2="1041" zvalue="10077"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="260.75" y2="283.5"/>
  <line fill="none" id="171" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="622.8704926140824" y2="622.8704926140824" zvalue="10079"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="582" y2="606.6794"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1005.0816" y2="1033"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,958) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="964" zvalue="10083">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,992) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="998" zvalue="10084">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,992) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="998" zvalue="10085">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1026" zvalue="10086">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1026" zvalue="10087">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" x="137.765625" xml:space="preserve" y="461.3993055555555" zvalue="10088">110kV</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="137.765625" xml:space="preserve" y="478.3993055555555" zvalue="10088">母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,652.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="657" zvalue="10090">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,210.399,319.841) scale(1,1) translate(0,0)" writing-mode="lr" x="210.4" xml:space="preserve" y="324.34" zvalue="10091">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,315.399,319.841) scale(1,1) translate(0,0)" writing-mode="lr" x="315.4" xml:space="preserve" y="324.34" zvalue="10092">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" x="254.96875" xml:space="preserve" y="460.8368055555555" zvalue="10093">35kV母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="254.96875" xml:space="preserve" y="477.8368055555555" zvalue="10093">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89,495.75) scale(1,1) translate(0,0)" writing-mode="lr" x="89" xml:space="preserve" y="500.25" zvalue="10095">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89,521.25) scale(1,1) translate(0,0)" writing-mode="lr" x="89" xml:space="preserve" y="525.75" zvalue="10096">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89,544.25) scale(1,1) translate(0,0)" writing-mode="lr" x="89" xml:space="preserve" y="548.75" zvalue="10097">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89,567.25) scale(1,1) translate(0,0)" writing-mode="lr" x="89" xml:space="preserve" y="571.75" zvalue="10098">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89,594.25) scale(1,1) translate(0,0)" writing-mode="lr" x="89" xml:space="preserve" y="598.75" zvalue="10099">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230.054,959) scale(1,1) translate(0,0)" writing-mode="lr" x="230.05" xml:space="preserve" y="965" zvalue="10100">XiMa-01-2019</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,147.054,992) scale(1,1) translate(0,0)" writing-mode="lr" x="147.05" xml:space="preserve" y="998" zvalue="10101">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,327.054,992) scale(1,1) translate(0,0)" writing-mode="lr" x="327.05" xml:space="preserve" y="998" zvalue="10102">20210301</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51,179) scale(1,1) translate(0,0)" writing-mode="lr" x="51" xml:space="preserve" y="183.5" zvalue="10103">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231,179) scale(1,1) translate(0,0)" writing-mode="lr" x="231" xml:space="preserve" y="183.5" zvalue="10104">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,203.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="207.75" zvalue="10105">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.1875,251.409) scale(1,1) translate(0,0)" writing-mode="lr" x="57.19" xml:space="preserve" y="255.91" zvalue="10107">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.1875,272.591) scale(1,1) translate(0,-4.08925e-13)" writing-mode="lr" x="57.19" xml:space="preserve" y="277.09" zvalue="10109">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.1875,227.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.19" xml:space="preserve" y="231.75" zvalue="10110">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,908,85.875) scale(1,1) translate(0,0)" writing-mode="lr" x="908" xml:space="preserve" y="90.38" zvalue="10140">110kV南昔线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1599.91,817.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1599.91" xml:space="preserve" y="821.97" zvalue="10176">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1552.84,776.368) scale(1,1) translate(0,0)" writing-mode="lr" x="1552.84" xml:space="preserve" y="780.87" zvalue="10178">366</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="224" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1599.73,733.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1599.73" xml:space="preserve" y="737.97" zvalue="10182">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="244" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1396.75,544.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1396.75" xml:space="preserve" y="548.88" zvalue="10194">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1144.94,474.372) scale(1,1) translate(0,0)" writing-mode="lr" x="1144.94" xml:space="preserve" y="478.87" zvalue="10203">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1144.89,606.818) scale(1,1) translate(0,0)" writing-mode="lr" x="1144.89" xml:space="preserve" y="611.3200000000001" zvalue="10207">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,824.374,630.412) scale(1,1) translate(0,0)" writing-mode="lr" x="824.37" xml:space="preserve" y="634.91" zvalue="10211">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,882.196,349.545) scale(1,1) translate(0,0)" writing-mode="lr" x="882.2" xml:space="preserve" y="354.05" zvalue="10259">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1022,565) scale(1,1) translate(0,0)" writing-mode="lr" x="1022" xml:space="preserve" y="569.5" zvalue="10263">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1250.15,298.645) scale(1,1) translate(0,0)" writing-mode="lr" x="1250.15" xml:space="preserve" y="303.14" zvalue="10267">162</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1193.53,167.63) scale(1,1) translate(0,0)" writing-mode="lr" x="1193.53" xml:space="preserve" y="172.13" zvalue="10271">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1249.42,353.549) scale(1,1) translate(0,0)" writing-mode="lr" x="1249.42" xml:space="preserve" y="358.05" zvalue="10274">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1194.28,255.065) scale(1,1) translate(0,0)" writing-mode="lr" x="1194.28" xml:space="preserve" y="259.56" zvalue="10278">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1248.92,205.088) scale(1,1) translate(0,0)" writing-mode="lr" x="1248.92" xml:space="preserve" y="209.59" zvalue="10280">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1225.34,83.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1225.34" xml:space="preserve" y="88.38" zvalue="10284">110kV勐乃河新二级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1200.2,347.545) scale(1,1) translate(0,0)" writing-mode="lr" x="1200.2" xml:space="preserve" y="352.05" zvalue="10291">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,827.374,676.412) scale(1,1) translate(0,0)" writing-mode="lr" x="827.37" xml:space="preserve" y="680.91" zvalue="10294">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1606.37,855.831) scale(1,1) translate(0,0)" writing-mode="lr" x="1606.37" xml:space="preserve" y="860.33" zvalue="10297">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1540.11,890.062) scale(1,1) translate(0,0)" writing-mode="lr" x="1540.11" xml:space="preserve" y="894.5599999999999" zvalue="10303">97</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1558.99,858.975) scale(1,1) translate(0,0)" writing-mode="lr" x="1558.99" xml:space="preserve" y="863.48" zvalue="10305">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1426.91,818.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1426.91" xml:space="preserve" y="822.97" zvalue="10310">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1379.84,777.368) scale(1,1) translate(0,0)" writing-mode="lr" x="1379.84" xml:space="preserve" y="781.87" zvalue="10312">365</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1426.73,734.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1426.73" xml:space="preserve" y="738.97" zvalue="10316">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1402.41,938.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1402.41" xml:space="preserve" y="943.13" zvalue="10320">35kV昔那线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1433.37,856.831) scale(1,1) translate(0,0)" writing-mode="lr" x="1433.37" xml:space="preserve" y="861.33" zvalue="10323">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="254" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1367.11,891.062) scale(1,1) translate(0,0)" writing-mode="lr" x="1367.11" xml:space="preserve" y="895.5599999999999" zvalue="10327">97</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1385.99,859.975) scale(1,1) translate(0,0)" writing-mode="lr" x="1385.99" xml:space="preserve" y="864.48" zvalue="10329">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="278" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1430.37,767.831) scale(1,1) translate(0,0)" writing-mode="lr" x="1430.37" xml:space="preserve" y="772.33" zvalue="10334">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="286" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1261.91,817.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1261.91" xml:space="preserve" y="821.97" zvalue="10337">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1214.84,776.368) scale(1,1) translate(0,0)" writing-mode="lr" x="1214.84" xml:space="preserve" y="780.87" zvalue="10339">364</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1261.73,733.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1261.73" xml:space="preserve" y="737.97" zvalue="10343">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1268.37,855.831) scale(1,1) translate(0,0)" writing-mode="lr" x="1268.37" xml:space="preserve" y="860.33" zvalue="10350">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="281" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1202.11,890.062) scale(1,1) translate(0,0)" writing-mode="lr" x="1202.11" xml:space="preserve" y="894.5599999999999" zvalue="10354">97</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="280" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1220.99,858.975) scale(1,1) translate(0,0)" writing-mode="lr" x="1220.99" xml:space="preserve" y="863.48" zvalue="10356">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="311" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1096.91,817.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1096.91" xml:space="preserve" y="821.97" zvalue="10364">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="310" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1049.84,776.368) scale(1,1) translate(0,0)" writing-mode="lr" x="1049.84" xml:space="preserve" y="780.87" zvalue="10366">363</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="309" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1096.73,733.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1096.73" xml:space="preserve" y="737.97" zvalue="10370">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="307" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1103.37,855.831) scale(1,1) translate(0,0)" writing-mode="lr" x="1103.37" xml:space="preserve" y="860.33" zvalue="10377">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="305" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1055.99,858.975) scale(1,1) translate(0,0)" writing-mode="lr" x="1055.99" xml:space="preserve" y="863.48" zvalue="10383">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="334" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,924.908,819.474) scale(1,1) translate(4.08965e-13,0)" writing-mode="lr" x="924.91" xml:space="preserve" y="823.97" zvalue="10389">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="333" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,877.837,778.368) scale(1,1) translate(0,0)" writing-mode="lr" x="877.84" xml:space="preserve" y="782.87" zvalue="10391">362</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="332" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,924.727,735.474) scale(1,1) translate(5.11661e-13,0)" writing-mode="lr" x="924.73" xml:space="preserve" y="739.97" zvalue="10395">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="330" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,931.374,857.831) scale(1,1) translate(-2.0503e-13,0)" writing-mode="lr" x="931.37" xml:space="preserve" y="862.33" zvalue="10402">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="329" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,883.994,860.975) scale(1,1) translate(0,0)" writing-mode="lr" x="883.99" xml:space="preserve" y="865.48" zvalue="10405">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="354" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,759.908,819.474) scale(1,1) translate(0,0)" writing-mode="lr" x="759.91" xml:space="preserve" y="823.97" zvalue="10410">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="353" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,712.837,778.368) scale(1,1) translate(0,0)" writing-mode="lr" x="712.84" xml:space="preserve" y="782.87" zvalue="10412">361</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="352" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,759.727,735.474) scale(1,1) translate(0,0)" writing-mode="lr" x="759.73" xml:space="preserve" y="739.97" zvalue="10416">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="350" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,766.374,857.831) scale(1,1) translate(0,0)" writing-mode="lr" x="766.37" xml:space="preserve" y="862.33" zvalue="10423">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="349" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,718.994,860.975) scale(1,1) translate(0,0)" writing-mode="lr" x="718.99" xml:space="preserve" y="865.48" zvalue="10426">9</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="371" stroke="rgb(255,255,255)" text-anchor="middle" x="1221.25" xml:space="preserve" y="538.75" zvalue="10431">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="371" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1221.25" xml:space="preserve" y="555.75" zvalue="10431">31.5MVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,739,939.625) scale(1,1) translate(0,0)" writing-mode="lr" x="739" xml:space="preserve" y="944.13" zvalue="10472">35kV昔铝Ⅰ线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,904.077,939.625) scale(1,1) translate(0,0)" writing-mode="lr" x="904.08" xml:space="preserve" y="944.13" zvalue="10474">35kV昔铝Ⅱ线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1076.5,939.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1076.5" xml:space="preserve" y="944.13" zvalue="10477">35kV昔勐Ⅱ线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1241.5,939.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1241.5" xml:space="preserve" y="944.13" zvalue="10479">35kV昔勐Ⅰ线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1579.5,939.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1579.5" xml:space="preserve" y="944.13" zvalue="10481">35kV昔灰线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,88.2857,328.539) scale(1,1) translate(0,3.52429e-13)" writing-mode="lr" x="88.28570556640625" xml:space="preserve" y="333.0388641357421" zvalue="10507">全站公用</text>
 </g>
 <g id="ButtonClass">
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="153.09" y="398.25" zvalue="10501"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="51.19" y="398.25" zvalue="10502"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="51.19" y="357.75" zvalue="10503"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="48">
   <path class="kv110" d="M 762.5 383.09 L 1542.25 383.09" stroke-width="6" zvalue="7576"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674424979459" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674424979459"/></metadata>
  <path d="M 762.5 383.09 L 1542.25 383.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv35" d="M 619.67 702.09 L 1680 702.09" stroke-width="6" zvalue="7715"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674425044995" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674425044995"/></metadata>
  <path d="M 619.67 702.09 L 1680 702.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="1453">
   <use class="kv110" height="30" transform="rotate(180,1454.01,330.828) scale(0.947693,-0.6712) translate(79.8605,-828.649)" width="15" x="1446.902401042228" xlink:href="#Disconnector:刀闸_0" y="320.7597602301333" zvalue="8015"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454899007490" ObjectName="110kV母线1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454899007490"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1454.01,330.828) scale(0.947693,-0.6712) translate(79.8605,-828.649)" width="15" x="1446.902401042228" y="320.7597602301333"/></g>
  <g id="815">
   <use class="kv110" height="30" transform="rotate(0,1132.59,410.372) scale(0.947693,-0.6712) translate(62.1199,-1026.7)" width="15" x="1125.480010535841" xlink:href="#Disconnector:刀闸_0" y="400.3043387992517" zvalue="9724"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454897958914" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454897958914"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1132.59,410.372) scale(0.947693,-0.6712) translate(62.1199,-1026.7)" width="15" x="1125.480010535841" y="400.3043387992517"/></g>
  <g id="636">
   <use class="kv35" height="30" transform="rotate(0,1132.28,677.818) scale(0.947693,-0.6712) translate(62.1031,-1692.61)" width="15" x="1125.175204945725" xlink:href="#Disconnector:刀闸_0" y="667.75" zvalue="9734"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454897893378" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454897893378"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1132.28,677.818) scale(0.947693,-0.6712) translate(62.1031,-1692.61)" width="15" x="1125.175204945725" y="667.75"/></g>
  <g id="864">
   <use class="kv35" height="30" transform="rotate(180,781.697,653.404) scale(0.947693,-0.6712) translate(42.7528,-1631.82)" width="15" x="774.5894229335038" xlink:href="#Disconnector:刀闸_0" y="643.3355178058908" zvalue="9761"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454898089986" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454898089986"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,781.697,653.404) scale(0.947693,-0.6712) translate(42.7528,-1631.82)" width="15" x="774.5894229335038" y="643.3355178058908"/></g>
  <g id="96">
   <use class="kv110" height="30" transform="rotate(180,907.392,354.857) scale(0.947693,-0.6712) translate(49.6904,-888.478)" width="15" x="900.2841334259956" xlink:href="#Disconnector:刀闸_0" y="344.7885305077181" zvalue="9974"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454898155522" ObjectName="110kV南昔线1611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454898155522"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,907.392,354.857) scale(0.947693,-0.6712) translate(49.6904,-888.478)" width="15" x="900.2841334259956" y="344.7885305077181"/></g>
  <g id="32">
   <use class="kv110" height="30" transform="rotate(0,907.211,213.088) scale(-0.947693,0.6712) translate(-1864.89,99.4529)" width="15" x="900.1034928921569" xlink:href="#Disconnector:刀闸_0" y="203.0197927208583" zvalue="10031"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454898548738" ObjectName="110kV南昔线1616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454898548738"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,907.211,213.088) scale(-0.947693,0.6712) translate(-1864.89,99.4529)" width="15" x="900.1034928921569" y="203.0197927208583"/></g>
  <g id="236">
   <use class="kv35" height="30" transform="rotate(180,1578.99,816.891) scale(-0.947693,0.6712) translate(-3245.53,395.237)" width="15" x="1571.88614466846" xlink:href="#Disconnector:刀闸_0" y="806.8230764122643" zvalue="10175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454899269634" ObjectName="35kV昔灰线3666隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454899269634"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1578.99,816.891) scale(-0.947693,0.6712) translate(-3245.53,395.237)" width="15" x="1571.88614466846" y="806.8230764122643"/></g>
  <g id="232">
   <use class="kv35" height="30" transform="rotate(180,1578.81,732.891) scale(-0.947693,0.6712) translate(-3245.16,354.088)" width="15" x="1571.705504134622" xlink:href="#Disconnector:刀闸_0" y="722.8230764122643" zvalue="10181"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454899204098" ObjectName="35kV昔灰线3661隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454899204098"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1578.81,732.891) scale(-0.947693,0.6712) translate(-3245.16,354.088)" width="15" x="1571.705504134622" y="722.8230764122643"/></g>
  <g id="245">
   <use class="kv35" height="30" transform="rotate(0,1400.25,674.25) scale(0.947693,0.6712) translate(76.8933,325.361)" width="15" x="1393.142303114711" xlink:href="#Disconnector:令克_0" y="664.1819936485927" zvalue="10194"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454899400706" ObjectName="35kV1号站用变跌落保险"/>
   <cge:TPSR_Ref TObjectID="6192454899400706"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1400.25,674.25) scale(0.947693,0.6712) translate(76.8933,325.361)" width="15" x="1393.142303114711" y="664.1819936485927"/></g>
  <g id="2">
   <use class="kv110" height="30" transform="rotate(0,1132.33,475.372) scale(0.947693,-0.6712) translate(62.1059,-1188.55)" width="15" x="1125.226269374257" xlink:href="#Disconnector:刀闸_0" y="465.3043387992516" zvalue="10202"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454899466242" ObjectName="#1主变110kV侧1016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454899466242"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1132.33,475.372) scale(0.947693,-0.6712) translate(62.1059,-1188.55)" width="15" x="1125.226269374257" y="465.3043387992516"/></g>
  <g id="10">
   <use class="kv35" height="30" transform="rotate(0,1132.28,607.818) scale(0.947693,-0.6712) translate(62.1031,-1518.32)" width="15" x="1125.175204945725" xlink:href="#Disconnector:刀闸_0" y="597.75" zvalue="10206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454899531778" ObjectName="#1主变35kV侧3016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454899531778"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1132.28,607.818) scale(0.947693,-0.6712) translate(62.1031,-1518.32)" width="15" x="1125.175204945725" y="597.75"/></g>
  <g id="155">
   <use class="kv110" height="30" transform="rotate(180,1225.39,352.857) scale(0.947693,-0.6712) translate(67.2421,-883.498)" width="15" x="1218.284133425996" xlink:href="#Disconnector:刀闸_0" y="342.7885305077181" zvalue="10273"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454900449283" ObjectName="110kV勐乃河新二级线1621隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454900449283"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1225.39,352.857) scale(0.947693,-0.6712) translate(67.2421,-883.498)" width="15" x="1218.284133425996" y="342.7885305077181"/></g>
  <g id="145">
   <use class="kv110" height="30" transform="rotate(0,1225.21,211.088) scale(-0.947693,0.6712) translate(-2518.44,98.4732)" width="15" x="1218.103492892157" xlink:href="#Disconnector:刀闸_0" y="201.0197927208583" zvalue="10279"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454900252675" ObjectName="110kV勐乃河新二级线1626隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454900252675"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1225.21,211.088) scale(-0.947693,0.6712) translate(-2518.44,98.4732)" width="15" x="1218.103492892157" y="201.0197927208583"/></g>
  <g id="247">
   <use class="kv35" height="30" transform="rotate(90,1556.99,838.083) scale(-0.947693,0.6712) translate(-3200.32,405.618)" width="15" x="1549.88614466846" xlink:href="#Disconnector:刀闸_0" y="828.0151793380599" zvalue="10304"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454901170178" ObjectName="35kV昔灰线3669隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454901170178"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1556.99,838.083) scale(-0.947693,0.6712) translate(-3200.32,405.618)" width="15" x="1549.88614466846" y="828.0151793380599"/></g>
  <g id="275">
   <use class="kv35" height="30" transform="rotate(180,1405.99,817.891) scale(-0.947693,0.6712) translate(-2889.98,395.727)" width="15" x="1398.88614466846" xlink:href="#Disconnector:刀闸_0" y="807.8230764122643" zvalue="10309"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454901760002" ObjectName="35kV昔那线3656隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454901760002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1405.99,817.891) scale(-0.947693,0.6712) translate(-2889.98,395.727)" width="15" x="1398.88614466846" y="807.8230764122643"/></g>
  <g id="271">
   <use class="kv35" height="30" transform="rotate(180,1405.81,733.891) scale(-0.947693,0.6712) translate(-2889.61,354.578)" width="15" x="1398.705504134622" xlink:href="#Disconnector:刀闸_0" y="723.8230764122643" zvalue="10315"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454901694466" ObjectName="35kV昔那线3651隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454901694466"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1405.81,733.891) scale(-0.947693,0.6712) translate(-2889.61,354.578)" width="15" x="1398.705504134622" y="723.8230764122643"/></g>
  <g id="262">
   <use class="kv35" height="30" transform="rotate(90,1383.99,839.083) scale(-0.947693,0.6712) translate(-2844.77,406.108)" width="15" x="1376.88614466846" xlink:href="#Disconnector:刀闸_0" y="829.0151793380599" zvalue="10328"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454901235714" ObjectName="35kV昔那线3659隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454901235714"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1383.99,839.083) scale(-0.947693,0.6712) translate(-2844.77,406.108)" width="15" x="1376.88614466846" y="829.0151793380599"/></g>
  <g id="304">
   <use class="kv35" height="30" transform="rotate(180,1240.99,816.891) scale(-0.947693,0.6712) translate(-2550.88,395.237)" width="15" x="1233.88614466846" xlink:href="#Disconnector:刀闸_0" y="806.8230764122643" zvalue="10336"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454902480898" ObjectName="35kV昔勐Ⅰ线3646隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454902480898"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1240.99,816.891) scale(-0.947693,0.6712) translate(-2550.88,395.237)" width="15" x="1233.88614466846" y="806.8230764122643"/></g>
  <g id="300">
   <use class="kv35" height="30" transform="rotate(180,1240.81,732.891) scale(-0.947693,0.6712) translate(-2550.5,354.088)" width="15" x="1233.705504134622" xlink:href="#Disconnector:刀闸_0" y="722.8230764122643" zvalue="10342"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454902415362" ObjectName="35kV昔勐Ⅰ线3641隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454902415362"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1240.81,732.891) scale(-0.947693,0.6712) translate(-2550.5,354.088)" width="15" x="1233.705504134622" y="722.8230764122643"/></g>
  <g id="291">
   <use class="kv35" height="30" transform="rotate(90,1218.99,838.083) scale(-0.947693,0.6712) translate(-2505.66,405.618)" width="15" x="1211.88614466846" xlink:href="#Disconnector:刀闸_0" y="828.0151793380599" zvalue="10355"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454901956610" ObjectName="35kV昔勐Ⅰ线3649隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454901956610"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1218.99,838.083) scale(-0.947693,0.6712) translate(-2505.66,405.618)" width="15" x="1211.88614466846" y="828.0151793380599"/></g>
  <g id="327">
   <use class="kv35" height="30" transform="rotate(180,1075.99,816.891) scale(-0.947693,0.6712) translate(-2211.77,395.237)" width="15" x="1068.88614466846" xlink:href="#Disconnector:刀闸_0" y="806.8230764122643" zvalue="10363"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454902939650" ObjectName="35kV昔勐Ⅱ线3636隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454902939650"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1075.99,816.891) scale(-0.947693,0.6712) translate(-2211.77,395.237)" width="15" x="1068.88614466846" y="806.8230764122643"/></g>
  <g id="323">
   <use class="kv35" height="30" transform="rotate(180,1075.81,732.891) scale(-0.947693,0.6712) translate(-2211.4,354.088)" width="15" x="1068.705504134622" xlink:href="#Disconnector:刀闸_0" y="722.8230764122643" zvalue="10369"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454902874114" ObjectName="35kV昔勐Ⅱ线3631隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454902874114"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1075.81,732.891) scale(-0.947693,0.6712) translate(-2211.4,354.088)" width="15" x="1068.705504134622" y="722.8230764122643"/></g>
  <g id="314">
   <use class="kv35" height="30" transform="rotate(90,1053.99,838.083) scale(-0.947693,0.6712) translate(-2166.55,405.618)" width="15" x="1046.88614466846" xlink:href="#Disconnector:刀闸_0" y="828.0151793380599" zvalue="10382"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454902546434" ObjectName="35kV昔勐Ⅱ线3639隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454902546434"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1053.99,838.083) scale(-0.947693,0.6712) translate(-2166.55,405.618)" width="15" x="1046.88614466846" y="828.0151793380599"/></g>
  <g id="348">
   <use class="kv35" height="30" transform="rotate(180,903.994,818.891) scale(-0.947693,0.6712) translate(-1858.28,396.217)" width="15" x="896.8861446684601" xlink:href="#Disconnector:刀闸_0" y="808.8230764122643" zvalue="10388"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454903398402" ObjectName="35kV昔铝Ⅱ线3626隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454903398402"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,903.994,818.891) scale(-0.947693,0.6712) translate(-1858.28,396.217)" width="15" x="896.8861446684601" y="808.8230764122643"/></g>
  <g id="344">
   <use class="kv35" height="30" transform="rotate(180,903.813,734.891) scale(-0.947693,0.6712) translate(-1857.9,355.068)" width="15" x="896.7055041346215" xlink:href="#Disconnector:刀闸_0" y="724.8230764122643" zvalue="10394"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454903332866" ObjectName="35kV昔铝Ⅱ线3621隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454903332866"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,903.813,734.891) scale(-0.947693,0.6712) translate(-1857.9,355.068)" width="15" x="896.7055041346215" y="724.8230764122643"/></g>
  <g id="337">
   <use class="kv35" height="30" transform="rotate(90,881.994,840.083) scale(-0.947693,0.6712) translate(-1813.06,406.598)" width="15" x="874.8861446684602" xlink:href="#Disconnector:刀闸_0" y="830.0151793380599" zvalue="10404"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454903005186" ObjectName="35kV昔铝Ⅱ线3629隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454903005186"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,881.994,840.083) scale(-0.947693,0.6712) translate(-1813.06,406.598)" width="15" x="874.8861446684602" y="830.0151793380599"/></g>
  <g id="368">
   <use class="kv35" height="30" transform="rotate(180,738.994,818.891) scale(-0.947693,0.6712) translate(-1519.17,396.217)" width="15" x="731.8861446684602" xlink:href="#Disconnector:刀闸_0" y="808.8230764122643" zvalue="10409"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454903857154" ObjectName="35kV昔铝Ⅰ线3616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454903857154"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,738.994,818.891) scale(-0.947693,0.6712) translate(-1519.17,396.217)" width="15" x="731.8861446684602" y="808.8230764122643"/></g>
  <g id="364">
   <use class="kv35" height="30" transform="rotate(180,738.813,734.891) scale(-0.947693,0.6712) translate(-1518.8,355.068)" width="15" x="731.7055041346215" xlink:href="#Disconnector:刀闸_0" y="724.8230764122643" zvalue="10415"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454903791618" ObjectName="35kV昔铝Ⅰ线3611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454903791618"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,738.813,734.891) scale(-0.947693,0.6712) translate(-1518.8,355.068)" width="15" x="731.7055041346215" y="724.8230764122643"/></g>
  <g id="357">
   <use class="kv35" height="30" transform="rotate(90,716.994,840.083) scale(-0.947693,0.6712) translate(-1473.95,406.598)" width="15" x="709.8861446684602" xlink:href="#Disconnector:刀闸_0" y="830.0151793380599" zvalue="10425"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454903463938" ObjectName="35kV昔铝Ⅰ线3619隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454903463938"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,716.994,840.083) scale(-0.947693,0.6712) translate(-1473.95,406.598)" width="15" x="709.8861446684602" y="830.0151793380599"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="150">
   <use class="kv110" height="20" transform="rotate(90,1481.37,355.393) scale(1.24619,-1.0068) translate(-291.418,-708.317)" width="10" x="1475.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="345.3248086033777" zvalue="8029"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454898941954" ObjectName="110kV母线19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454898941954"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1481.37,355.393) scale(1.24619,-1.0068) translate(-291.418,-708.317)" width="10" x="1475.143242399862" y="345.3248086033777"/></g>
  <g id="170">
   <use class="kv110" height="20" transform="rotate(90,1481.37,304.643) scale(1.24619,-1.0068) translate(-291.418,-607.16)" width="10" x="1475.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="294.5748086033776" zvalue="8034"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454898810882" ObjectName="110kV母线19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454898810882"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1481.37,304.643) scale(1.24619,-1.0068) translate(-291.418,-607.16)" width="10" x="1475.143242399862" y="294.5748086033776"/></g>
  <g id="98">
   <use class="kv110" height="20" transform="rotate(270,877.196,188.315) scale(-1.24619,-1.0068) translate(-1579.87,-375.289)" width="10" x="870.9650768138583" xlink:href="#GroundDisconnector:地刀_0" y="178.246507813326" zvalue="9971"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454898352130" ObjectName="110kV南昔线16167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454898352130"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,877.196,188.315) scale(-1.24619,-1.0068) translate(-1579.87,-375.289)" width="10" x="870.9650768138583" y="178.246507813326"/></g>
  <g id="29">
   <use class="kv110" height="20" transform="rotate(270,877.196,240.315) scale(-1.24619,-1.0068) translate(-1579.87,-478.938)" width="10" x="870.9650768138583" xlink:href="#GroundDisconnector:地刀_0" y="230.246507813326" zvalue="10027"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454898483202" ObjectName="110kV南昔线16160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454898483202"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,877.196,240.315) scale(-1.24619,-1.0068) translate(-1579.87,-478.938)" width="10" x="870.9650768138583" y="230.246507813326"/></g>
  <g id="15">
   <use class="kv35" height="20" transform="rotate(90,804.374,631.643) scale(1.24619,-1.0068) translate(-157.675,-1258.95)" width="10" x="798.1432423998622" xlink:href="#GroundDisconnector:地刀_0" y="621.5748086033776" zvalue="10210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454899662850" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454899662850"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,804.374,631.643) scale(1.24619,-1.0068) translate(-157.675,-1258.95)" width="10" x="798.1432423998622" y="621.5748086033776"/></g>
  <g id="114">
   <use class="kv110" height="20" transform="rotate(270,883.196,332.315) scale(-1.24619,-1.0068) translate(-1590.68,-662.316)" width="10" x="876.9650768138583" xlink:href="#GroundDisconnector:地刀_0" y="322.246507813326" zvalue="10258"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454899793922" ObjectName="110kV南昔线16117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454899793922"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,883.196,332.315) scale(-1.24619,-1.0068) translate(-1590.68,-662.316)" width="10" x="876.9650768138583" y="322.246507813326"/></g>
  <g id="123">
   <use class="kv110" height="40" transform="rotate(0,1063,565) scale(1,-1) translate(0,-1130)" width="40" x="1043" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="545" zvalue="10262"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454899924994" ObjectName="#1主变110kV侧中性点1010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454899924994"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1063,565) scale(1,-1) translate(0,-1130)" width="40" x="1043" y="545"/></g>
  <g id="158">
   <use class="kv110" height="20" transform="rotate(270,1195.2,186.315) scale(-1.24619,-1.0068) translate(-2153.05,-371.303)" width="10" x="1188.965076813859" xlink:href="#GroundDisconnector:地刀_0" y="176.246507813326" zvalue="10270"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454900580354" ObjectName="110kV勐乃河新二级线16267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454900580354"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1195.2,186.315) scale(-1.24619,-1.0068) translate(-2153.05,-371.303)" width="10" x="1188.965076813859" y="176.246507813326"/></g>
  <g id="146">
   <use class="kv110" height="20" transform="rotate(270,1195.2,238.315) scale(-1.24619,-1.0068) translate(-2153.05,-474.951)" width="10" x="1188.965076813859" xlink:href="#GroundDisconnector:地刀_0" y="228.246507813326" zvalue="10277"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454900383747" ObjectName="110kV勐乃河新二级线16260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454900383747"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1195.2,238.315) scale(-1.24619,-1.0068) translate(-2153.05,-474.951)" width="10" x="1188.965076813859" y="228.246507813326"/></g>
  <g id="135">
   <use class="kv110" height="20" transform="rotate(270,1201.2,330.315) scale(-1.24619,-1.0068) translate(-2163.86,-658.33)" width="10" x="1194.965076813859" xlink:href="#GroundDisconnector:地刀_0" y="320.246507813326" zvalue="10289"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454900056066" ObjectName="110kV勐乃河新二级线16217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454900056066"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1201.2,330.315) scale(-1.24619,-1.0068) translate(-2163.86,-658.33)" width="10" x="1194.965076813859" y="320.246507813326"/></g>
  <g id="186">
   <use class="kv35" height="20" transform="rotate(90,803.374,676.643) scale(1.24619,-1.0068) translate(-157.477,-1348.65)" width="10" x="797.1432423998622" xlink:href="#GroundDisconnector:地刀_0" y="666.5748086033776" zvalue="10293"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454900776962" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454900776962"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,803.374,676.643) scale(1.24619,-1.0068) translate(-157.477,-1348.65)" width="10" x="797.1432423998622" y="666.5748086033776"/></g>
  <g id="195">
   <use class="kv35" height="20" transform="rotate(270,1605.37,838.062) scale(1.24619,1.0068) translate(-315.914,-5.59285)" width="10" x="1599.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="827.994302984848" zvalue="10296"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454900908034" ObjectName="35kV昔灰线36667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454900908034"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1605.37,838.062) scale(1.24619,1.0068) translate(-315.914,-5.59285)" width="10" x="1599.143242399862" y="827.994302984848"/></g>
  <g id="242">
   <use class="kv35" height="20" transform="rotate(0,1538.37,866.062) scale(1.24619,1.0068) translate(-302.678,-5.78198)" width="10" x="1532.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="855.994302984848" zvalue="10302"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454901104642" ObjectName="35kV昔灰线36697接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454901104642"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1538.37,866.062) scale(1.24619,1.0068) translate(-302.678,-5.78198)" width="10" x="1532.143242399862" y="855.994302984848"/></g>
  <g id="267">
   <use class="kv35" height="20" transform="rotate(270,1432.37,839.062) scale(1.24619,1.0068) translate(-281.738,-5.59961)" width="10" x="1426.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="828.994302984848" zvalue="10321"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454901563394" ObjectName="35kV昔那线36567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454901563394"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1432.37,839.062) scale(1.24619,1.0068) translate(-281.738,-5.59961)" width="10" x="1426.143242399862" y="828.994302984848"/></g>
  <g id="263">
   <use class="kv35" height="20" transform="rotate(0,1365.37,867.062) scale(1.24619,1.0068) translate(-268.502,-5.78874)" width="10" x="1359.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="856.994302984848" zvalue="10326"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454901366786" ObjectName="35kV昔那线36597接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454901366786"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1365.37,867.062) scale(1.24619,1.0068) translate(-268.502,-5.78874)" width="10" x="1359.143242399862" y="856.994302984848"/></g>
  <g id="276">
   <use class="kv35" height="20" transform="rotate(270,1431.37,753.062) scale(1.24619,1.0068) translate(-281.54,-5.0187)" width="10" x="1425.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="742.994302984848" zvalue="10333"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454901891074" ObjectName="35kV昔那线36517接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454901891074"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1431.37,753.062) scale(1.24619,1.0068) translate(-281.54,-5.0187)" width="10" x="1425.143242399862" y="742.994302984848"/></g>
  <g id="296">
   <use class="kv35" height="20" transform="rotate(270,1267.37,838.062) scale(1.24619,1.0068) translate(-249.141,-5.59285)" width="10" x="1261.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="827.994302984848" zvalue="10348"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454902284290" ObjectName="35kV昔勐Ⅰ线36467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454902284290"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1267.37,838.062) scale(1.24619,1.0068) translate(-249.141,-5.59285)" width="10" x="1261.143242399862" y="827.994302984848"/></g>
  <g id="292">
   <use class="kv35" height="20" transform="rotate(0,1200.37,866.062) scale(1.24619,1.0068) translate(-235.905,-5.78198)" width="10" x="1194.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="855.994302984848" zvalue="10353"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454902087682" ObjectName="35kV昔勐Ⅰ线36497接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454902087682"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1200.37,866.062) scale(1.24619,1.0068) translate(-235.905,-5.78198)" width="10" x="1194.143242399862" y="855.994302984848"/></g>
  <g id="319">
   <use class="kv35" height="20" transform="rotate(270,1102.37,838.062) scale(1.24619,1.0068) translate(-216.545,-5.59285)" width="10" x="1096.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="827.994302984848" zvalue="10375"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454902743042" ObjectName="35kV昔勐Ⅱ线36367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454902743042"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1102.37,838.062) scale(1.24619,1.0068) translate(-216.545,-5.59285)" width="10" x="1096.143242399862" y="827.994302984848"/></g>
  <g id="340">
   <use class="kv35" height="20" transform="rotate(270,930.374,840.062) scale(1.24619,1.0068) translate(-182.566,-5.60636)" width="10" x="924.1432423998623" xlink:href="#GroundDisconnector:地刀_0" y="829.994302984848" zvalue="10400"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454903201794" ObjectName="35kV昔铝Ⅱ线36267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454903201794"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,930.374,840.062) scale(1.24619,1.0068) translate(-182.566,-5.60636)" width="10" x="924.1432423998623" y="829.994302984848"/></g>
  <g id="360">
   <use class="kv35" height="20" transform="rotate(270,765.374,840.062) scale(1.24619,1.0068) translate(-149.97,-5.60636)" width="10" x="759.1432423998622" xlink:href="#GroundDisconnector:地刀_0" y="829.994302984848" zvalue="10421"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454903660546" ObjectName="35kV昔铝Ⅰ线36167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454903660546"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,765.374,840.062) scale(1.24619,1.0068) translate(-149.97,-5.60636)" width="10" x="759.1432423998622" y="829.994302984848"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="151">
   <path class="kv110" d="M 1453.95 340.72 L 1453.95 383.09" stroke-width="1" zvalue="8031"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@1" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1453.95 340.72 L 1453.95 383.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv110" d="M 1471.56 355.46 L 1453.95 355.46" stroke-width="1" zvalue="8032"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="151" MaxPinNum="2"/>
   </metadata>
  <path d="M 1471.56 355.46 L 1453.95 355.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv110" d="M 1453.93 321.09 L 1453.93 278.23" stroke-width="1" zvalue="8038"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@0" LinkObjectIDznd="218@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1453.93 321.09 L 1453.93 278.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv110" d="M 1471.56 304.71 L 1453.93 304.71" stroke-width="1" zvalue="8039"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 1471.56 304.71 L 1453.93 304.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="813">
   <path class="kv110" d="M 1132.65 400.48 L 1132.65 383.09" stroke-width="1" zvalue="9726"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="815@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.65 400.48 L 1132.65 383.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="812">
   <path class="kv110" d="M 1132.48 427.28 L 1132.48 420.11" stroke-width="1" zvalue="9727"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="816@1" LinkObjectIDznd="815@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.48 427.28 L 1132.48 420.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="633">
   <path class="kv35" d="M 1132.82 658.5 L 1132.82 667.92" stroke-width="1" zvalue="9736"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="811@0" LinkObjectIDznd="636@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.82 658.5 L 1132.82 667.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="632">
   <path class="kv35" d="M 1132.37 687.55 L 1132.37 702.09" stroke-width="1" zvalue="9737"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="636@0" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.37 687.55 L 1132.37 702.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="865">
   <path class="kv35" d="M 781.64 663.3 L 781.64 702.09" stroke-width="1" zvalue="9765"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="864@1" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.64 663.3 L 781.64 702.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="857">
   <path class="kv35" d="M 781.61 643.67 L 781.61 612.81" stroke-width="1" zvalue="9769"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="864@0" LinkObjectIDznd="855@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.61 643.67 L 781.61 612.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv110" d="M 907.31 313.95 L 907.31 345.12" stroke-width="1" zvalue="9978"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@1" LinkObjectIDznd="96@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 907.31 313.95 L 907.31 345.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv110" d="M 907.33 364.75 L 907.33 383.09" stroke-width="1" zvalue="9981"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@1" LinkObjectIDznd="48@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 907.33 364.75 L 907.33 383.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv110" d="M 907.84 151.55 L 907.84 203.35" stroke-width="1" zvalue="10040"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="32@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 907.84 151.55 L 907.84 203.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv110" d="M 887.01 240.38 L 907.15 240.38" stroke-width="1" zvalue="10141"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="29@0" LinkObjectIDznd="4" MaxPinNum="2"/>
   </metadata>
  <path d="M 887.01 240.38 L 907.15 240.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="234">
   <path class="kv35" d="M 1579.08 826.63 L 1579.08 887.15" stroke-width="1" zvalue="10179"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="236@0" LinkObjectIDznd="57@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1579.08 826.63 L 1579.08 887.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="kv35" d="M 1579.05 786.57 L 1579.05 807" stroke-width="1" zvalue="10180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@0" LinkObjectIDznd="236@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1579.05 786.57 L 1579.05 807" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="kv35" d="M 1578.9 742.63 L 1578.9 760.69" stroke-width="1" zvalue="10183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="232@0" LinkObjectIDznd="235@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1578.9 742.63 L 1578.9 760.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="230">
   <path class="kv35" d="M 1578.87 723 L 1578.87 702.09" stroke-width="1" zvalue="10184"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="232@1" LinkObjectIDznd="166@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1578.87 723 L 1578.87 702.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv110" d="M 1132.64 453.16 L 1132.64 465.48" stroke-width="1" zvalue="10203"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="816@0" LinkObjectIDznd="2@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.64 453.16 L 1132.64 465.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv35" d="M 1133 587.67 L 1133 597.92" stroke-width="1" zvalue="10207"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="370@1" LinkObjectIDznd="10@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1133 587.67 L 1133 597.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv35" d="M 1132.37 617.55 L 1132.37 632.61" stroke-width="1" zvalue="10208"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@0" LinkObjectIDznd="811@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.37 617.55 L 1132.37 632.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv35" d="M 794.56 631.71 L 781.61 631.71" stroke-width="1" zvalue="10211"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@0" LinkObjectIDznd="857" MaxPinNum="2"/>
   </metadata>
  <path d="M 794.56 631.71 L 781.61 631.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv110" d="M 887.01 188.38 L 907.84 188.38" stroke-width="1" zvalue="10255"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="41" MaxPinNum="2"/>
   </metadata>
  <path d="M 887.01 188.38 L 907.84 188.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv110" d="M 907 188.38 L 927.91 188.38 L 927.91 181" stroke-width="1" zvalue="10256"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49" LinkObjectIDznd="42@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 907 188.38 L 927.91 188.38 L 927.91 181" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv110" d="M 893.01 332.38 L 907.31 332.38" stroke-width="1" zvalue="10259"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="93" MaxPinNum="2"/>
   </metadata>
  <path d="M 893.01 332.38 L 907.31 332.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv35" d="M 1400.33 646.15 L 1400.33 665.36" stroke-width="1" zvalue="10260"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243@0" LinkObjectIDznd="245@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1400.33 646.15 L 1400.33 665.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv35" d="M 1400.17 682.47 L 1400.17 702.09" stroke-width="1" zvalue="10261"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="245@1" LinkObjectIDznd="166@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1400.17 682.47 L 1400.17 702.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv110" d="M 1225.31 311.95 L 1225.31 343.12" stroke-width="1" zvalue="10275"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@1" LinkObjectIDznd="155@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1225.31 311.95 L 1225.31 343.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv110" d="M 1225.49 149.55 L 1225.49 201.35" stroke-width="1" zvalue="10281"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@0" LinkObjectIDznd="145@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1225.49 149.55 L 1225.49 201.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="kv110" d="M 1205.01 238.38 L 1225.15 238.38" stroke-width="1" zvalue="10286"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1205.01 238.38 L 1225.15 238.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv110" d="M 1205.01 186.38 L 1225.49 186.38" stroke-width="1" zvalue="10287"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="144" MaxPinNum="2"/>
   </metadata>
  <path d="M 1205.01 186.38 L 1225.49 186.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv110" d="M 1224.31 186.38 L 1261.91 186.38 L 1261.91 179" stroke-width="1" zvalue="10288"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139" LinkObjectIDznd="143@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1224.31 186.38 L 1261.91 186.38 L 1261.91 179" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv110" d="M 1211.01 330.38 L 1225.31 330.38" stroke-width="1" zvalue="10290"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@0" LinkObjectIDznd="152" MaxPinNum="2"/>
   </metadata>
  <path d="M 1211.01 330.38 L 1225.31 330.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv35" d="M 793.56 676.71 L 781.64 676.71" stroke-width="1" zvalue="10294"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="865" MaxPinNum="2"/>
   </metadata>
  <path d="M 793.56 676.71 L 781.64 676.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="237">
   <path class="kv35" d="M 1595.56 838 L 1579.08 838" stroke-width="1" zvalue="10297"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="234" MaxPinNum="2"/>
   </metadata>
  <path d="M 1595.56 838 L 1579.08 838" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="241">
   <path class="kv35" d="M 1539 829.08 L 1539 856.25" stroke-width="1" zvalue="10300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="239@0" LinkObjectIDznd="242@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1539 829.08 L 1539 856.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="249">
   <path class="kv35" d="M 1566.73 838 L 1579.08 838" stroke-width="1" zvalue="10306"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247@0" LinkObjectIDznd="234" MaxPinNum="2"/>
   </metadata>
  <path d="M 1566.73 838 L 1579.08 838" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv35" d="M 1547.1 838.03 L 1539 838.03" stroke-width="1" zvalue="10307"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247@1" LinkObjectIDznd="241" MaxPinNum="2"/>
   </metadata>
  <path d="M 1547.1 838.03 L 1539 838.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="273">
   <path class="kv35" d="M 1406.08 827.63 L 1406.16 887.44" stroke-width="1" zvalue="10313"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="275@0" LinkObjectIDznd="268@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1406.08 827.63 L 1406.16 887.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="272">
   <path class="kv35" d="M 1406.05 787.57 L 1406.05 808" stroke-width="1" zvalue="10314"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="275@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1406.05 787.57 L 1406.05 808" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv35" d="M 1405.9 743.63 L 1405.9 761.69" stroke-width="1" zvalue="10317"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@0" LinkObjectIDznd="274@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1405.9 743.63 L 1405.9 761.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv35" d="M 1405.87 724 L 1405.87 702.09" stroke-width="1" zvalue="10318"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@1" LinkObjectIDznd="166@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1405.87 724 L 1405.87 702.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="kv35" d="M 1422.56 839 L 1406.09 839" stroke-width="1" zvalue="10322"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@0" LinkObjectIDznd="273" MaxPinNum="2"/>
   </metadata>
  <path d="M 1422.56 839 L 1406.09 839" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="264">
   <path class="kv35" d="M 1366 830.08 L 1366 857.25" stroke-width="1" zvalue="10325"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265@0" LinkObjectIDznd="263@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1366 830.08 L 1366 857.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="261">
   <path class="kv35" d="M 1393.73 839 L 1406.08 839" stroke-width="1" zvalue="10330"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="262@0" LinkObjectIDznd="266" MaxPinNum="2"/>
   </metadata>
  <path d="M 1393.73 839 L 1406.08 839" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="260">
   <path class="kv35" d="M 1374.1 839.03 L 1366 839.03" stroke-width="1" zvalue="10331"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="262@1" LinkObjectIDznd="264" MaxPinNum="2"/>
   </metadata>
  <path d="M 1374.1 839.03 L 1366 839.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="277">
   <path class="kv35" d="M 1421.56 753 L 1405.9 753" stroke-width="1" zvalue="10334"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="270" MaxPinNum="2"/>
   </metadata>
  <path d="M 1421.56 753 L 1405.9 753" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="302">
   <path class="kv35" d="M 1241.08 826.63 L 1241.08 889.15" stroke-width="1" zvalue="10340"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="304@0" LinkObjectIDznd="39@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1241.08 826.63 L 1241.08 889.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="301">
   <path class="kv35" d="M 1241.05 786.57 L 1241.05 807" stroke-width="1" zvalue="10341"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="303@0" LinkObjectIDznd="304@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1241.05 786.57 L 1241.05 807" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="299">
   <path class="kv35" d="M 1240.9 742.63 L 1240.9 760.69" stroke-width="1" zvalue="10344"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="300@0" LinkObjectIDznd="303@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1240.9 742.63 L 1240.9 760.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="298">
   <path class="kv35" d="M 1240.87 723 L 1240.87 702.09" stroke-width="1" zvalue="10345"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="300@1" LinkObjectIDznd="166@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1240.87 723 L 1240.87 702.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="295">
   <path class="kv35" d="M 1257.56 838 L 1241.08 838" stroke-width="1" zvalue="10349"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="296@0" LinkObjectIDznd="302" MaxPinNum="2"/>
   </metadata>
  <path d="M 1257.56 838 L 1241.08 838" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="293">
   <path class="kv35" d="M 1201 829.08 L 1201 856.25" stroke-width="1" zvalue="10352"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="294@0" LinkObjectIDznd="292@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1201 829.08 L 1201 856.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="290">
   <path class="kv35" d="M 1228.73 838 L 1241.08 838" stroke-width="1" zvalue="10357"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="291@0" LinkObjectIDznd="295" MaxPinNum="2"/>
   </metadata>
  <path d="M 1228.73 838 L 1241.08 838" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="289">
   <path class="kv35" d="M 1209.1 838.03 L 1201 838.03" stroke-width="1" zvalue="10358"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="291@1" LinkObjectIDznd="293" MaxPinNum="2"/>
   </metadata>
  <path d="M 1209.1 838.03 L 1201 838.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="325">
   <path class="kv35" d="M 1076.08 826.63 L 1076.08 889.15" stroke-width="1" zvalue="10367"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="327@0" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1076.08 826.63 L 1076.08 889.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="324">
   <path class="kv35" d="M 1076.05 786.57 L 1076.05 807" stroke-width="1" zvalue="10368"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="326@0" LinkObjectIDznd="327@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1076.05 786.57 L 1076.05 807" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="322">
   <path class="kv35" d="M 1075.9 742.63 L 1075.9 760.69" stroke-width="1" zvalue="10371"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="323@0" LinkObjectIDznd="326@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1075.9 742.63 L 1075.9 760.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="321">
   <path class="kv35" d="M 1075.87 723 L 1075.87 702.09" stroke-width="1" zvalue="10372"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="323@1" LinkObjectIDznd="166@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1075.87 723 L 1075.87 702.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="318">
   <path class="kv35" d="M 1092.56 838 L 1076.08 838" stroke-width="1" zvalue="10376"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="319@0" LinkObjectIDznd="325" MaxPinNum="2"/>
   </metadata>
  <path d="M 1092.56 838 L 1076.08 838" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="313">
   <path class="kv35" d="M 1063.73 838 L 1076.08 838" stroke-width="1" zvalue="10384"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="314@0" LinkObjectIDznd="318" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.73 838 L 1076.08 838" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="328">
   <path class="kv35" d="M 1044.1 838.03 L 1036.1 838.03 L 1036.1 854.03" stroke-width="1" zvalue="10386"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="314@1" LinkObjectIDznd="317@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1044.1 838.03 L 1036.1 838.03 L 1036.1 854.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="346">
   <path class="kv35" d="M 904.08 828.63 L 904.08 892.15" stroke-width="1" zvalue="10392"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="348@0" LinkObjectIDznd="105@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 904.08 828.63 L 904.08 892.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="345">
   <path class="kv35" d="M 904.05 788.57 L 904.05 809" stroke-width="1" zvalue="10393"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="347@0" LinkObjectIDznd="348@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 904.05 788.57 L 904.05 809" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="343">
   <path class="kv35" d="M 903.9 744.63 L 903.9 762.69" stroke-width="1" zvalue="10396"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="344@0" LinkObjectIDznd="347@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 903.9 744.63 L 903.9 762.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="342">
   <path class="kv35" d="M 903.87 725 L 903.87 702.09" stroke-width="1" zvalue="10397"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="344@1" LinkObjectIDznd="166@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 903.87 725 L 903.87 702.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="339">
   <path class="kv35" d="M 920.56 840 L 904.08 840" stroke-width="1" zvalue="10401"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="340@0" LinkObjectIDznd="346" MaxPinNum="2"/>
   </metadata>
  <path d="M 920.56 840 L 904.08 840" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="336">
   <path class="kv35" d="M 891.73 840 L 904.08 840" stroke-width="1" zvalue="10406"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="337@0" LinkObjectIDznd="339" MaxPinNum="2"/>
   </metadata>
  <path d="M 891.73 840 L 904.08 840" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="335">
   <path class="kv35" d="M 872.1 840.03 L 864.1 840.03 L 864.1 856.03" stroke-width="1" zvalue="10407"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="337@1" LinkObjectIDznd="338@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 872.1 840.03 L 864.1 840.03 L 864.1 856.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="366">
   <path class="kv35" d="M 739.08 828.63 L 739 892.15" stroke-width="1" zvalue="10413"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="368@0" LinkObjectIDznd="104@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 739.08 828.63 L 739 892.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="365">
   <path class="kv35" d="M 739.05 788.57 L 739.05 809" stroke-width="1" zvalue="10414"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="367@0" LinkObjectIDznd="368@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 739.05 788.57 L 739.05 809" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="363">
   <path class="kv35" d="M 738.9 744.63 L 738.9 762.69" stroke-width="1" zvalue="10417"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="364@0" LinkObjectIDznd="367@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 738.9 744.63 L 738.9 762.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="362">
   <path class="kv35" d="M 738.87 725 L 738.87 702.09" stroke-width="1" zvalue="10418"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="364@1" LinkObjectIDznd="166@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 738.87 725 L 738.87 702.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="359">
   <path class="kv35" d="M 755.56 840 L 739.06 840" stroke-width="1" zvalue="10422"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="360@0" LinkObjectIDznd="366" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.56 840 L 739.06 840" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="356">
   <path class="kv35" d="M 726.73 840 L 739.08 840" stroke-width="1" zvalue="10427"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="357@0" LinkObjectIDznd="359" MaxPinNum="2"/>
   </metadata>
  <path d="M 726.73 840 L 739.08 840" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="355">
   <path class="kv35" d="M 707.1 840.03 L 699.1 840.03 L 699.1 856.03" stroke-width="1" zvalue="10428"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="357@1" LinkObjectIDznd="358@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 707.1 840.03 L 699.1 840.03 L 699.1 856.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="369">
   <path class="kv110" d="M 1225.33 362.75 L 1225.33 383.09" stroke-width="1" zvalue="10429"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@1" LinkObjectIDznd="48@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1225.33 362.75 L 1225.33 383.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="372">
   <path class="kv110" d="M 1132.42 485.11 L 1132.42 496.51" stroke-width="1" zvalue="10431"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@0" LinkObjectIDznd="370@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.42 485.11 L 1132.42 496.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="373">
   <path class="kv110" d="M 1133.02 518.78 L 1065.6 518.78 L 1065.6 552.8" stroke-width="1" zvalue="10432"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="370@2" LinkObjectIDznd="123@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1133.02 518.78 L 1065.6 518.78 L 1065.6 552.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv110" d="M 907.15 222.98 L 907.15 288.06" stroke-width="1" zvalue="10433"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@1" LinkObjectIDznd="100@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 907.15 222.98 L 907.15 288.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv110" d="M 1225.15 220.98 L 1225.15 286.06" stroke-width="1" zvalue="10434"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@1" LinkObjectIDznd="178@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1225.15 220.98 L 1225.15 286.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="218">
   <use class="kv110" height="40" transform="rotate(0,1453.5,260.341) scale(0.9375,-0.9375) translate(95.65,-539.288)" width="40" x="1434.75" xlink:href="#Accessory:线路PT三绕组_0" y="241.5909090909091" zvalue="9702"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454898679810" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1453.5,260.341) scale(0.9375,-0.9375) translate(95.65,-539.288)" width="40" x="1434.75" y="241.5909090909091"/></g>
  <g id="855">
   <use class="kv35" height="40" transform="rotate(0,780.222,594.917) scale(-0.9375,-0.9375) translate(-1613.71,-1230.74)" width="40" x="761.4724880605656" xlink:href="#Accessory:线路PT三绕组_0" y="576.1666666666666" zvalue="9771"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454898024450" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,780.222,594.917) scale(-0.9375,-0.9375) translate(-1613.71,-1230.74)" width="40" x="761.4724880605656" y="576.1666666666666"/></g>
  <g id="42">
   <use class="kv110" height="30" transform="rotate(90,940.342,155.313) scale(1.46781,-1.48949) translate(-291.51,-252.244)" width="35" x="914.6557615957269" xlink:href="#Accessory:线路PT99_0" y="132.9710619052636" zvalue="10041"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454898614274" ObjectName="110kV南昔线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,940.342,155.313) scale(1.46781,-1.48949) translate(-291.51,-252.244)" width="35" x="914.6557615957269" y="132.9710619052636"/></g>
  <g id="143">
   <use class="kv110" height="30" transform="rotate(90,1274.34,153.313) scale(1.46781,-1.48949) translate(-397.96,-248.901)" width="35" x="1248.655761595727" xlink:href="#Accessory:线路PT99_0" y="130.9710619052636" zvalue="10282"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454900187139" ObjectName="110kV勐乃河新二级线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1274.34,153.313) scale(1.46781,-1.48949) translate(-397.96,-248.901)" width="35" x="1248.655761595727" y="130.9710619052636"/></g>
  <g id="239">
   <use class="kv35" height="40" transform="rotate(180,1539,810) scale(1,1) translate(0,0)" width="20" x="1529" xlink:href="#Accessory:线路PT带避雷器0904_0" y="790" zvalue="10298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454900973570" ObjectName="昔灰线"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,1539,810) scale(1,1) translate(0,0)" width="20" x="1529" y="790"/></g>
  <g id="265">
   <use class="kv35" height="40" transform="rotate(180,1366,811) scale(1,1) translate(0,0)" width="20" x="1356" xlink:href="#Accessory:线路PT带避雷器0904_0" y="791" zvalue="10324"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454901432322" ObjectName="昔那线"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,1366,811) scale(1,1) translate(0,0)" width="20" x="1356" y="791"/></g>
  <g id="294">
   <use class="kv35" height="40" transform="rotate(180,1201,810) scale(1,1) translate(0,0)" width="20" x="1191" xlink:href="#Accessory:线路PT带避雷器0904_0" y="790" zvalue="10351"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454902153218" ObjectName="昔勐Ⅰ线"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,1201,810) scale(1,1) translate(0,0)" width="20" x="1191" y="790"/></g>
  <g id="317">
   <use class="kv35" height="40" transform="rotate(180,1036.1,873.109) scale(1,-1) translate(0,-1746.22)" width="20" x="1026.097995832712" xlink:href="#Accessory:线路PT带避雷器0904_0" y="853.1085539312394" zvalue="10378"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454902611970" ObjectName="昔勐Ⅱ线"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,1036.1,873.109) scale(1,-1) translate(0,-1746.22)" width="20" x="1026.097995832712" y="853.1085539312394"/></g>
  <g id="338">
   <use class="kv35" height="40" transform="rotate(180,864.098,875.109) scale(1,-1) translate(0,-1750.22)" width="20" x="854.0979958327121" xlink:href="#Accessory:线路PT带避雷器0904_0" y="855.1085539312394" zvalue="10403"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454903070722" ObjectName="昔铝Ⅱ线"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,864.098,875.109) scale(1,-1) translate(0,-1750.22)" width="20" x="854.0979958327121" y="855.1085539312394"/></g>
  <g id="358">
   <use class="kv35" height="40" transform="rotate(180,699.098,875.109) scale(1,-1) translate(0,-1750.22)" width="20" x="689.0979958327121" xlink:href="#Accessory:线路PT带避雷器0904_0" y="855.1085539312394" zvalue="10424"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454903529474" ObjectName="昔铝Ⅰ线"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,699.098,875.109) scale(1,-1) translate(0,-1750.22)" width="20" x="689.0979958327121" y="855.1085539312394"/></g>
 </g>
 <g id="BreakerClass">
  <g id="816">
   <use class="kv110" height="20" transform="rotate(180,1132.59,440.209) scale(1.5542,1.35421) translate(-401.089,-111.6)" width="10" x="1124.816710722901" xlink:href="#Breaker:开关_0" y="426.6666962122334" zvalue="9722"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925229412355" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925229412355"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1132.59,440.209) scale(1.5542,1.35421) translate(-401.089,-111.6)" width="10" x="1124.816710722901" y="426.6666962122334"/></g>
  <g id="811">
   <use class="kv35" height="20" transform="rotate(180,1132.77,645.542) scale(1.5542,1.35421) translate(-401.154,-165.307)" width="10" x="1124.997344267822" xlink:href="#Breaker:开关_0" y="632" zvalue="9728"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925229346819" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925229346819"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1132.77,645.542) scale(1.5542,1.35421) translate(-401.154,-165.307)" width="10" x="1124.997344267822" y="632"/></g>
  <g id="100">
   <use class="kv110" height="20" transform="rotate(0,907.205,301.02) scale(1.5542,1.35421) translate(-320.722,-75.1934)" width="10" x="899.4340345345652" xlink:href="#Breaker:开关_0" y="287.4779758524579" zvalue="9966"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925229477891" ObjectName="110kV南昔线161断路器"/>
   <cge:TPSR_Ref TObjectID="6473925229477891"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,907.205,301.02) scale(1.5542,1.35421) translate(-320.722,-75.1934)" width="10" x="899.4340345345652" y="287.4779758524579"/></g>
  <g id="235">
   <use class="kv35" height="20" transform="rotate(180,1579,773.618) scale(1.5542,1.35421) translate(-560.272,-198.807)" width="10" x="1571.229003203369" xlink:href="#Breaker:开关_0" y="760.0758972316265" zvalue="10177"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925229543427" ObjectName="35kV昔灰线366断路器"/>
   <cge:TPSR_Ref TObjectID="6473925229543427"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1579,773.618) scale(1.5542,1.35421) translate(-560.272,-198.807)" width="10" x="1571.229003203369" y="760.0758972316265"/></g>
  <g id="178">
   <use class="kv110" height="20" transform="rotate(0,1225.21,299.02) scale(1.5542,1.35421) translate(-434.115,-74.6703)" width="10" x="1217.434034534565" xlink:href="#Breaker:开关_0" y="285.4779758524579" zvalue="10266"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925229608963" ObjectName="110kV勐乃河新二级线162断路器"/>
   <cge:TPSR_Ref TObjectID="6473925229608963"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1225.21,299.02) scale(1.5542,1.35421) translate(-434.115,-74.6703)" width="10" x="1217.434034534565" y="285.4779758524579"/></g>
  <g id="274">
   <use class="kv35" height="20" transform="rotate(180,1406,774.618) scale(1.5542,1.35421) translate(-498.583,-199.069)" width="10" x="1398.229003203369" xlink:href="#Breaker:开关_0" y="761.0758972316265" zvalue="10311"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925229674499" ObjectName="35kV昔那线365断路器"/>
   <cge:TPSR_Ref TObjectID="6473925229674499"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1406,774.618) scale(1.5542,1.35421) translate(-498.583,-199.069)" width="10" x="1398.229003203369" y="761.0758972316265"/></g>
  <g id="303">
   <use class="kv35" height="20" transform="rotate(180,1241,773.618) scale(1.5542,1.35421) translate(-439.747,-198.807)" width="10" x="1233.229003203369" xlink:href="#Breaker:开关_0" y="760.0758972316265" zvalue="10338"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925229740035" ObjectName="35kV昔勐Ⅰ线364断路器"/>
   <cge:TPSR_Ref TObjectID="6473925229740035"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1241,773.618) scale(1.5542,1.35421) translate(-439.747,-198.807)" width="10" x="1233.229003203369" y="760.0758972316265"/></g>
  <g id="326">
   <use class="kv35" height="20" transform="rotate(180,1076,773.618) scale(1.5542,1.35421) translate(-380.911,-198.807)" width="10" x="1068.229003203369" xlink:href="#Breaker:开关_0" y="760.0758972316265" zvalue="10365"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925229805571" ObjectName="35kV昔勐Ⅱ线363断路器"/>
   <cge:TPSR_Ref TObjectID="6473925229805571"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1076,773.618) scale(1.5542,1.35421) translate(-380.911,-198.807)" width="10" x="1068.229003203369" y="760.0758972316265"/></g>
  <g id="347">
   <use class="kv35" height="20" transform="rotate(180,904,775.618) scale(1.5542,1.35421) translate(-319.579,-199.33)" width="10" x="896.2290032033686" xlink:href="#Breaker:开关_0" y="762.0758972316265" zvalue="10390"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925229871107" ObjectName="35kV昔铝Ⅱ线362断路器"/>
   <cge:TPSR_Ref TObjectID="6473925229871107"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,904,775.618) scale(1.5542,1.35421) translate(-319.579,-199.33)" width="10" x="896.2290032033686" y="762.0758972316265"/></g>
  <g id="367">
   <use class="kv35" height="20" transform="rotate(180,739,775.618) scale(1.5542,1.35421) translate(-260.743,-199.33)" width="10" x="731.2290032033687" xlink:href="#Breaker:开关_0" y="762.0758972316265" zvalue="10411"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925229936643" ObjectName="35kV昔铝Ⅰ线361断路器"/>
   <cge:TPSR_Ref TObjectID="6473925229936643"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,739,775.618) scale(1.5542,1.35421) translate(-260.743,-199.33)" width="10" x="731.2290032033687" y="762.0758972316265"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,521.25) scale(1,1) translate(1.24653e-14,0)" writing-mode="lr" x="137.75" xml:space="preserve" y="526.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136746725377" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="67">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,544.25) scale(1,1) translate(1.24653e-14,5.96467e-14)" writing-mode="lr" x="137.75" xml:space="preserve" y="549.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136746790913" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,567.25) scale(1,1) translate(1.24653e-14,-1.244e-13)" writing-mode="lr" x="137.75" xml:space="preserve" y="572.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136746856449" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,495.75) scale(1,1) translate(1.24653e-14,-1.08524e-13)" writing-mode="lr" x="137.75" xml:space="preserve" y="500.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136746987521" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,142,203.111) scale(1,1) translate(0,0)" writing-mode="lr" x="142.15" xml:space="preserve" y="209.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136747642881" ObjectName="F"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,521.25) scale(1,1) translate(2.57217e-14,0)" writing-mode="lr" x="257.15" xml:space="preserve" y="526.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136747249665" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="58">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,544.25) scale(1,1) translate(2.57217e-14,5.96467e-14)" writing-mode="lr" x="257.15" xml:space="preserve" y="549.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136747315201" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,567.25) scale(1,1) translate(2.57217e-14,-1.244e-13)" writing-mode="lr" x="257.15" xml:space="preserve" y="572.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136747380737" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,495.75) scale(1,1) translate(2.57217e-14,-1.08524e-13)" writing-mode="lr" x="257.15" xml:space="preserve" y="500.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136747511809" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="54" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,142,228) scale(1,1) translate(0,0)" writing-mode="lr" x="142.15" xml:space="preserve" y="234.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136747118593" ObjectName="F"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,148.091,250.104) scale(1,1) translate(0,0)" writing-mode="lr" x="148.24" xml:space="preserve" y="256.38" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136762454017" ObjectName="油温"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,148.091,273.104) scale(1,1) translate(0,-3.54521e-13)" writing-mode="lr" x="148.24" xml:space="preserve" y="279.38" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136762060801" ObjectName="Tap"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,594.25) scale(1,1) translate(1.24653e-14,1.30396e-13)" writing-mode="lr" x="137.75" xml:space="preserve" y="599.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136747184129" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,594.25) scale(1,1) translate(2.57217e-14,1.30396e-13)" writing-mode="lr" x="257.15" xml:space="preserve" y="599.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136747708417" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,908.5,18.25) scale(1,1) translate(0,0)" writing-mode="lr" x="908.6799999999999" xml:space="preserve" y="23.12" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136749346817" ObjectName="P"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="18" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,908.5,39.25) scale(1,1) translate(0,0)" writing-mode="lr" x="908.6799999999999" xml:space="preserve" y="44.12" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136749412353" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,908.5,60.25) scale(1,1) translate(0,0)" writing-mode="lr" x="908.6799999999999" xml:space="preserve" y="65.12" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136749477889" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="28">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="28" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1225.34,18.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1224.91" xml:space="preserve" y="22.9" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136752754689" ObjectName="P"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="33" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1225.34,39.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1224.91" xml:space="preserve" y="43.9" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136752820225" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="34">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="34" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1225.34,60.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1224.91" xml:space="preserve" y="64.90000000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136752885761" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="35" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1406.16,956.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1405.69" xml:space="preserve" y="960.9299999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136754327553" ObjectName="P"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="38" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1406.16,976.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1405.69" xml:space="preserve" y="981.0599999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136754393089" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="40">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="40" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1404.16,996.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1403.69" xml:space="preserve" y="1001.18" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136754458625" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="21" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,144,178) scale(1,1) translate(0,0)" writing-mode="lr" x="144.15" xml:space="preserve" y="184.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136770383873" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="46">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="46" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,306,179) scale(1,1) translate(0,0)" writing-mode="lr" x="306.15" xml:space="preserve" y="185.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136770449409" ObjectName="LOAD_QSUM"/>
   </metadata>
  </g>
  <g id="69">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="69" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1025,409.5) scale(1,1) translate(0,0)" writing-mode="lr" x="974.21" xml:space="preserve" y="415.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136761536513" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="110">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="110" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1025,446.5) scale(1,1) translate(0,0)" writing-mode="lr" x="974.21" xml:space="preserve" y="452.36" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136761602049" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="111" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1018,606.278) scale(1,1) translate(0,0)" writing-mode="lr" x="967.21" xml:space="preserve" y="612.14" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136761667585" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="112">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="112" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1018,643.278) scale(1,1) translate(0,0)" writing-mode="lr" x="967.21" xml:space="preserve" y="649.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136761733121" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="115" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1025,483.5) scale(1,1) translate(0,0)" writing-mode="lr" x="974.21" xml:space="preserve" y="489.36" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136761798657" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="116" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1018,679.278) scale(1,1) translate(0,0)" writing-mode="lr" x="967.21" xml:space="preserve" y="685.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136762126337" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(0,170,255)" font-family="SimSun" font-size="16" id="30" stroke="rgb(0,170,255)" text-anchor="start" transform="rotate(0,787.5,344.5) scale(1,1) translate(0,0)" writing-mode="lr" x="752.52" xml:space="preserve" y="350.36" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136762519553" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="50">
   <text Format="f5.2" Plane="0" fill="rgb(0,170,255)" font-family="SimSun" font-size="16" id="50" stroke="rgb(0,170,255)" text-anchor="start" transform="rotate(0,619.5,665.5) scale(1,1) translate(0,0)" writing-mode="lr" x="585.48" xml:space="preserve" y="671.36" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136763437057" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="61">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="61" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,739,956.25) scale(1,1) translate(0,0)" writing-mode="lr" x="695.88" xml:space="preserve" y="960.66" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136760094721" ObjectName="P"/>
   </metadata>
  </g>
  <g id="83">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="83" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,739,976.375) scale(1,1) translate(0,0)" writing-mode="lr" x="695.88" xml:space="preserve" y="980.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136760160257" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="85" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,739,996.5) scale(1,1) translate(0,0)" writing-mode="lr" x="695.88" xml:space="preserve" y="1000.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136760225793" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="91" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,904.077,956.25) scale(1,1) translate(0,0)" writing-mode="lr" x="860.95" xml:space="preserve" y="960.66" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136758652929" ObjectName="P"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="97" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,904.077,976.375) scale(1,1) translate(0,0)" writing-mode="lr" x="860.95" xml:space="preserve" y="980.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136758718465" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="99" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,904.077,996.5) scale(1,1) translate(0,0)" writing-mode="lr" x="860.95" xml:space="preserve" y="1000.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136758784001" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="101" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1076.5,956.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1033.38" xml:space="preserve" y="960.66" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136757211137" ObjectName="P"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="102" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1076.5,976.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1033.38" xml:space="preserve" y="980.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136757276673" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="117" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1076.5,996.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1033.38" xml:space="preserve" y="1000.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136757342209" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="125">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="125" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1241.5,956.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1198.38" xml:space="preserve" y="960.66" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136755769345" ObjectName="P"/>
   </metadata>
  </g>
  <g id="131">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="131" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1241.5,976.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1198.38" xml:space="preserve" y="980.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136755834881" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="141" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1241.5,996.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1198.38" xml:space="preserve" y="1000.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136755900417" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="147">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="147" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1579.5,956.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1536.38" xml:space="preserve" y="960.66" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136750395393" ObjectName="P"/>
   </metadata>
  </g>
  <g id="157">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="157" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1579.5,976.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1536.38" xml:space="preserve" y="980.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136750460929" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="159">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="159" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1579.5,996.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1536.38" xml:space="preserve" y="1000.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136750526465" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="182">
   <use class="kv110" height="40" transform="rotate(0,908,126.75) scale(1.25,1.25) translate(-177.85,-20.35)" width="30" x="889.25" xlink:href="#ACLineSegment:线路带壁雷器_0" y="101.75" zvalue="10139"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249333628930" ObjectName="110kV南昔线"/>
   <cge:TPSR_Ref TObjectID="8444249333628930_5066549685780481"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,908,126.75) scale(1.25,1.25) translate(-177.85,-20.35)" width="30" x="889.25" y="101.75"/></g>
  <g id="142">
   <use class="kv110" height="40" transform="rotate(0,1225.34,124.75) scale(-1.25,1.25) translate(-2201.86,-19.95)" width="30" x="1206.586337421313" xlink:href="#ACLineSegment:线路带壁雷器_0" y="99.75" zvalue="10283"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249311739910" ObjectName="110kV勐乃河新二级线"/>
   <cge:TPSR_Ref TObjectID="8444249311739910_5066549685780481"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1225.34,124.75) scale(-1.25,1.25) translate(-2201.86,-19.95)" width="30" x="1206.586337421313" y="99.75"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="243">
   <use class="kv35" height="30" transform="rotate(0,1400.33,611.125) scale(2.91667,-2.475) translate(-901.05,-835.919)" width="20" x="1371.16230774317" xlink:href="#EnergyConsumer:站用变无融断_0" y="574" zvalue="10193"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454899335170" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1400.33,611.125) scale(2.91667,-2.475) translate(-901.05,-835.919)" width="20" x="1371.16230774317" y="574"/></g>
 </g>
 <g id="StateClass">
  <g id="1056">
   <use height="30" transform="rotate(0,264.286,320.071) scale(0.708333,0.665547) translate(104.449,155.827)" width="30" x="253.66" xlink:href="#State:红绿圆(方形)_0" y="310.09" zvalue="10247"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,264.286,320.071) scale(0.708333,0.665547) translate(104.449,155.827)" width="30" x="253.66" y="310.09"/></g>
  <g id="1057">
   <use height="30" transform="rotate(0,351.911,319.071) scale(0.708333,0.665547) translate(140.529,155.324)" width="30" x="341.29" xlink:href="#State:红绿圆(方形)_0" y="309.09" zvalue="10249"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374897446915" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,351.911,319.071) scale(0.708333,0.665547) translate(140.529,155.324)" width="30" x="341.29" y="309.09"/></g>
  <g id="1131">
   <use height="30" transform="rotate(0,88.2857,328.539) scale(0.910937,0.8) translate(5.06921,79.1347)" width="80" x="51.85" xlink:href="#State:间隔模板_0" y="316.54" zvalue="10505"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629499756052483" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,88.2857,328.539) scale(0.910937,0.8) translate(5.06921,79.1347)" width="80" x="51.85" y="316.54"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="370">
   <g id="3700">
    <use class="kv110" height="30" transform="rotate(0,1133,541.889) scale(3.33333,3.25926) translate(-765.1,-341.739)" width="24" x="1093" xlink:href="#PowerTransformer2:可调两卷变_0" y="493" zvalue="10430"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874597335042" ObjectName="110"/>
    </metadata>
   </g>
   <g id="3701">
    <use class="kv35" height="30" transform="rotate(0,1133,541.889) scale(3.33333,3.25926) translate(-765.1,-341.739)" width="24" x="1093" xlink:href="#PowerTransformer2:可调两卷变_1" y="493" zvalue="10430"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874597400578" ObjectName="35"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399535689730" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399535689730"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1133,541.889) scale(3.33333,3.25926) translate(-765.1,-341.739)" width="24" x="1093" y="493"/></g>
 </g>
</svg>