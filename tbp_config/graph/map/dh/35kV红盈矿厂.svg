<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549590753282" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-y不带中性点_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="1" x="10" xlink:href="#terminal" y="2.75"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.02838478538902" x2="10.02838478538902" y1="6.047799292079558" y2="8.646787392096492"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.434837295917547" x2="10.028384785389" y1="11.24577549211341" y2="8.646787392096474"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.62193227486047" x2="10.02838478538902" y1="11.24577549211341" y2="8.646787392096474"/>
   <ellipse cx="10.03" cy="9.050000000000001" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-y不带中性点_1" viewBox="0,0,20,30">
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.434837295917547" x2="10.028384785389" y1="22.74577549211341" y2="20.14678739209647"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.02838478538902" x2="10.02838478538902" y1="17.54779929207956" y2="20.14678739209649"/>
   <use terminal-index="1" type="1" x="10.08333333333333" xlink:href="#terminal" y="26.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.62193227486047" x2="10.02838478538902" y1="22.74577549211341" y2="20.14678739209647"/>
   <ellipse cx="10.03" cy="19.8" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV红盈矿厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="71.94" y="327" zvalue="31"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="1" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" width="72.88" x="71.94" y="327" zvalue="31"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" writing-mode="lr" x="108.38" xml:space="preserve" y="343.5" zvalue="31">信号一览</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="269.25" x="66.95999999999999" xlink:href="logo.png" y="45.64"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.589,75.6429) scale(1,1) translate(0,0)" writing-mode="lr" x="201.59" xml:space="preserve" y="79.14" zvalue="138"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,203.298,75.3332) scale(1,1) translate(9.43293e-15,0)" writing-mode="lr" x="203.3" xml:space="preserve" y="84.33" zvalue="139">35kV红盈矿厂</text>
  <line fill="none" id="26" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="388.75" x2="388.75" y1="11" y2="1039.25" zvalue="6"/>
  <line fill="none" id="24" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.22692454998003" x2="331.8381846035992" y1="168.8779458827686" y2="168.8779458827686" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="927.8127909390723" y2="927.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="979.1522909390724" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="361.3219285714284" y1="927.8127909390723" y2="927.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="361.3219285714284" y1="979.1522909390724" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3219285714284" x2="361.3219285714284" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5593285714284" x2="279.5593285714284" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="279.5592285714284" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3213285714285" x2="361.3213285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5593285714284" x2="279.5593285714284" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="279.5592285714284" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3213285714285" x2="361.3213285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,208.338,957.386) scale(1,1) translate(0,1.05089e-13)" writing-mode="lr" x="60.68" xml:space="preserve" y="963.39" zvalue="10">参考图号      HongYing-01-2019</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,132.899,994.755) scale(1,1) translate(-6.25312e-14,-1.52933e-12)" writing-mode="lr" x="70.40000000000001" xml:space="preserve" y="1000.75" zvalue="11">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,276.202,995.755) scale(1,1) translate(1.38222e-13,-1.53089e-12)" writing-mode="lr" x="207.5" xml:space="preserve" y="1001.75" zvalue="12">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.9923,1024.31) scale(1,1) translate(-6.10858e-14,-1.57527e-12)" writing-mode="lr" x="83.98999999999999" xml:space="preserve" y="1030.31" zvalue="13">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,280.836,1022.31) scale(1,1) translate(-4.58902e-14,1.12298e-13)" writing-mode="lr" x="206.67" xml:space="preserve" y="1028.31" zvalue="14">更新日期  </text>
  <line fill="none" id="17" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="54.60611355802337" x2="359.2173736116425" y1="623.6445306693694" y2="623.6445306693694" zvalue="15"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,97.6707,640.686) scale(1,1) translate(5.99964e-15,-1.38109e-13)" writing-mode="lr" x="97.67070806204492" xml:space="preserve" y="645.1863811688671" zvalue="17">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="172.1071428571429" y2="172.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="172.1071428571429" y2="172.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="290.6071428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="290.6071428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" x="139.5390625" xml:space="preserve" y="467.109375" zvalue="20">35kV母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="139.5390625" xml:space="preserve" y="483.109375" zvalue="20">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,502.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="507.1071428571429" zvalue="23">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,528.107) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="532.6071428571429" zvalue="24">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,553.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="558.1071428571429" zvalue="25">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,579.107) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="583.6071428571429" zvalue="26">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,604.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="609.1071428571429" zvalue="27">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.3214,186.107) scale(1,1) translate(0,0)" writing-mode="lr" x="54.32" xml:space="preserve" y="191.61" zvalue="28">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.571,186.107) scale(1,1) translate(0,0)" writing-mode="lr" x="240.57" xml:space="preserve" y="191.61" zvalue="29">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,65.0089,210.357) scale(1,1) translate(0,0)" writing-mode="lr" x="65.01000000000001" xml:space="preserve" y="214.86" zvalue="30">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,208.399,340.5) scale(1,1) translate(0,0)" writing-mode="lr" x="208.4" xml:space="preserve" y="345" zvalue="379">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,296.399,340.5) scale(1,1) translate(0,0)" writing-mode="lr" x="296.4" xml:space="preserve" y="345" zvalue="380">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" x="1036.8203125" xml:space="preserve" y="593.7447911898295" zvalue="482">厂变 </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1036.8203125" xml:space="preserve" y="609.7447911898295" zvalue="482">1600kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1101.99,173) scale(1,1) translate(0,0)" writing-mode="lr" x="1101.99" xml:space="preserve" y="177.5" zvalue="485">35kV新巨线红盈T线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1064.83,393.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1064.83" xml:space="preserve" y="397.83" zvalue="487">3611</text>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="34">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="34" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,161.571,210.968) scale(1,1) translate(0,0)" writing-mode="lr" x="161.72" xml:space="preserve" y="217.4" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="33" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,161.571,186) scale(1,1) translate(0,0)" writing-mode="lr" x="161.72" xml:space="preserve" y="192.43" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126258933764" ObjectName=""/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="35" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,343.571,185.857) scale(1,1) translate(0,0)" writing-mode="lr" x="343.72" xml:space="preserve" y="192.29" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126258999300" ObjectName=""/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,146.071,528.593) scale(1,1) translate(0,0)" writing-mode="lr" x="146.2" xml:space="preserve" y="533.5" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="143" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,146.071,553.718) scale(1,1) translate(0,-1.20619e-13)" writing-mode="lr" x="146.2" xml:space="preserve" y="558.63" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,147.071,577.843) scale(1,1) translate(0,0)" writing-mode="lr" x="147.2" xml:space="preserve" y="582.75" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,147.5,503.468) scale(1,1) translate(0,1.09461e-13)" writing-mode="lr" x="147.63" xml:space="preserve" y="508.38" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="148" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,146.321,602.968) scale(1,1) translate(0,0)" writing-mode="lr" x="146.45" xml:space="preserve" y="607.88" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="132">
   <use height="30" transform="rotate(0,329.768,340.5) scale(0.708333,0.665547) translate(131.412,166.093)" width="30" x="319.14" xlink:href="#State:红绿圆(方形)_0" y="330.52" zvalue="381"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.768,340.5) scale(0.708333,0.665547) translate(131.412,166.093)" width="30" x="319.14" y="330.52"/></g>
  <g id="94">
   <use height="30" transform="rotate(0,247.143,340.5) scale(0.708333,0.665547) translate(97.3897,166.093)" width="30" x="236.52" xlink:href="#State:红绿圆(方形)_0" y="330.52" zvalue="382"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,247.143,340.5) scale(0.708333,0.665547) translate(97.3897,166.093)" width="30" x="236.52" y="330.52"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="68">
   <g id="680">
    <use class="kv35" height="30" transform="rotate(0,1101.99,596.5) scale(4.16667,4.16667) translate(-805.843,-405.84)" width="20" x="1060.32" xlink:href="#PowerTransformer2:Y-y不带中性点_0" y="534" zvalue="481"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874449158147" ObjectName="35"/>
    </metadata>
   </g>
   <g id="681">
    <use class="kv10" height="30" transform="rotate(0,1101.99,596.5) scale(4.16667,4.16667) translate(-805.843,-405.84)" width="20" x="1060.32" xlink:href="#PowerTransformer2:Y-y不带中性点_1" y="534" zvalue="481"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874449223683" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399456522243" ObjectName="厂变"/>
   <cge:TPSR_Ref TObjectID="6755399456522243"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1101.99,596.5) scale(4.16667,4.16667) translate(-805.843,-405.84)" width="20" x="1060.32" y="534"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="80">
   <use class="kv10" height="30" transform="rotate(0,1102.33,694.667) scale(2.08333,-2.05556) translate(-566.713,-1016.78)" width="12" x="1089.833333333333" xlink:href="#EnergyConsumer:负荷_0" y="663.8333333333334" zvalue="482"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449964474373" ObjectName="厂变低压负荷"/>
   <cge:TPSR_Ref TObjectID="6192449964474373"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1102.33,694.667) scale(2.08333,-2.05556) translate(-566.713,-1016.78)" width="12" x="1089.833333333333" y="663.8333333333334"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="91">
   <path class="kv10" d="M 1102.33 666.92 L 1102.33 643.03" stroke-width="1" zvalue="483"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="68@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1102.33 666.92 L 1102.33 643.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv35" d="M 1101.99 268.19 L 1101.99 372.25" stroke-width="1" zvalue="487"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="96@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1101.99 268.19 L 1101.99 372.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv35" d="M 1102.53 414.75 L 1102.53 545.46" stroke-width="1" zvalue="488"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@1" LinkObjectIDznd="68@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1102.53 414.75 L 1102.53 545.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="96">
   <use class="kv35" height="30" transform="rotate(0,1102.67,394.333) scale(1.66667,1.66667) translate(-436.067,-147.733)" width="15" x="1090.166666666667" xlink:href="#Disconnector:令克_0" y="369.3333333333333" zvalue="486"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449964605445" ObjectName="3611"/>
   <cge:TPSR_Ref TObjectID="6192449964605445"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1102.67,394.333) scale(1.66667,1.66667) translate(-436.067,-147.733)" width="15" x="1090.166666666667" y="369.3333333333333"/></g>
 </g>
</svg>