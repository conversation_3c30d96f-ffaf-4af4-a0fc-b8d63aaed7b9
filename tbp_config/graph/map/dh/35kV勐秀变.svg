<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549680537601" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.998992968246015" x2="4.998992968246015" y1="14.41666666666666" y2="17.66666666666666"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="1.08333333333333" y2="5.749999999999999"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="6" y2="14"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="14" y2="6"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.750000000000004" y2="4.166666666666668"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="32"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="1" y1="27" y2="11"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="27" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2019.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:PT象达_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15.5" xlink:href="#terminal" y="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="18.25" y1="12.08333333333333" y2="12.08333333333333"/>
   <ellipse cx="15.65" cy="12.68" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.9" cy="18.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="18.25" y1="18.33333333333333" y2="18.33333333333333"/>
  </symbol>
  <symbol id="Accessory:4绕组母线PT_0" viewBox="0,0,25,20">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0"/>
   <ellipse cx="12.75" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.75" cy="9.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.67" cy="14.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.82" cy="9.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="5.583333333333334" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333336" x2="5.583333333333336" y1="12" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="5.5" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.75" x2="12.75" y1="7.500000000000002" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="17.25" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="19.31481481481482" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.94444444444444" x2="20.62962962962963" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21.94444444444444" y1="10.21612466124661" y2="10.21612466124661"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Compensator:电容器组2_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="2.300000000000001"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="8" y1="24" y2="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="22" y1="26" y2="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="22" y1="24" y2="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.25" x2="19" y1="18.65" y2="18.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.08333333333333" x2="19.08333333333334" y1="16.65" y2="16.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.91666666666666" x2="25.33333333333334" y1="13.65" y2="13.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.4" x2="25.4" y1="13.65" y2="14.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.09999999999999" x2="25.09999999999999" y1="19.84999999999999" y2="21.84999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15" x2="15" y1="18.73333333333333" y2="26.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15" x2="25" y1="21.65" y2="21.65"/>
   <path d="M 25.2667 14.7333 A 0.85 1.66667 270 0 1 25.2667 16.4333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 25.1833 16.4833 A 0.85 1.66667 270 0 1 25.1833 18.1833" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 25.1833 18.2333 A 0.85 1.66667 270 0 1 25.1833 19.9333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.33333333333333" x2="15" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="16.58333333333333" y2="8.583333333333334"/>
   <path d="M 10.5833 8.625 A 4.125 4.375 -90 1 0 14.9583 4.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="4.5" y2="2.5"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变无融断_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <path d="M 10 9 L 10 0" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="26.83333333333333" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="10.8868007916835" y2="12.55855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="14.23031840279781" y2="12.55855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变1节点_0" viewBox="0,0,26,38">
   <use terminal-index="0" type="0" x="9.116666666666667" xlink:href="#terminal" y="0.2500000000000071"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.45" x2="25.45" y1="30" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.45" x2="24.45" y1="31" y2="31"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="33" y2="38"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.45" x2="23.45" y1="32" y2="32"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="23" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="19" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="23" y1="24" y2="24"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="2.25" y2="0.25"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="9" y1="30" y2="37"/>
   <ellipse cx="9.210000000000001" cy="10.97" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="6.6" y2="10.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="10.73394833233988" y2="13.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="10.68990500916851" y2="13.5"/>
   <ellipse cx="9.210000000000001" cy="24.47" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="20.1" y2="24.20894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="24.23394833233988" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="24.18990500916851" y2="27"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV勐秀变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="56.31" xlink:href="logo.png" y="26.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="334" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,181.153,67.7136) scale(1,1) translate(-1.25026e-14,0)" writing-mode="lr" x="181.15" xml:space="preserve" y="71.20999999999999" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,210,66.2618) scale(1,1) translate(0,0)" writing-mode="lr" x="210" xml:space="preserve" y="75.26000000000001" zvalue="3">35kV勐秀变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="367" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,298.438,402.25) scale(1,1) translate(0,-2.59959e-13)" width="72.88" x="262" y="390.25" zvalue="1050"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,298.438,402.25) scale(1,1) translate(0,-2.59959e-13)" writing-mode="lr" x="298.44" xml:space="preserve" y="406.75" zvalue="1050">小电流接地</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="28" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,94.625,321.25) scale(1,1) translate(0,0)" width="72.88" x="58.19" y="309.25" zvalue="1051"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94.625,321.25) scale(1,1) translate(0,0)" writing-mode="lr" x="94.63" xml:space="preserve" y="325.75" zvalue="1051">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="366" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,196.531,402.25) scale(1,1) translate(0,0)" width="72.88" x="160.09" y="390.25" zvalue="1052"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.531,402.25) scale(1,1) translate(0,0)" writing-mode="lr" x="196.53" xml:space="preserve" y="406.75" zvalue="1052">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="62" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,94.625,402.25) scale(1,1) translate(0,0)" width="72.88" x="58.19" y="390.25" zvalue="1053"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94.625,402.25) scale(1,1) translate(0,0)" writing-mode="lr" x="94.63" xml:space="preserve" y="406.75" zvalue="1053">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="41" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,94.625,361.75) scale(1,1) translate(0,0)" width="72.88" x="58.19" y="349.75" zvalue="1054"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94.625,361.75) scale(1,1) translate(0,0)" writing-mode="lr" x="94.63" xml:space="preserve" y="366.25" zvalue="1054">信号一览</text>
  <line fill="none" id="331" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.000000000000455" x2="373" y1="152.8704926140824" y2="152.8704926140824" zvalue="5"/>
  <line fill="none" id="330" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="374" x2="374" y1="11" y2="1041" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="260.75" y2="283.5"/>
  <line fill="none" id="328" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.000000000000455" x2="373" y1="622.8704926140824" y2="622.8704926140824" zvalue="8"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="58" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3873" x2="221.3873" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="221.3872" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339" x2="339" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="58" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3873" x2="221.3873" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="221.3872" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339" x2="339" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="58" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3873" x2="221.3873" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="221.3872" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339" x2="339" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="58" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3873" x2="221.3873" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="221.3872" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339" x2="339" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="58" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3873" x2="221.3873" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="221.3872" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339" x2="339" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="58" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3873" x2="221.3873" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="221.3872" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339" x2="339" y1="582" y2="606.6794"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="6" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="366" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="366" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366" x2="366" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="6" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="186" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="186" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186" x2="186" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="276.0000000000001" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="276.0000000000001" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="186.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.0000000000001" x2="276.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="366" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="366" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="276" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366" x2="366" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="6" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="186" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="186" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186" x2="186" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="276.0000000000001" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="276.0000000000001" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="186.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.0000000000001" x2="276.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="366" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="366" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="276" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366" x2="366" y1="1005.0816" y2="1033"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="324" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51,958) scale(1,1) translate(0,0)" writing-mode="lr" x="51" xml:space="preserve" y="964" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="323" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48,992) scale(1,1) translate(0,0)" writing-mode="lr" x="48" xml:space="preserve" y="998" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="322" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230,992) scale(1,1) translate(0,0)" writing-mode="lr" x="230" xml:space="preserve" y="998" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="321" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="47" xml:space="preserve" y="1026" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="320" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="229" xml:space="preserve" y="1026" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="319" stroke="rgb(255,255,255)" text-anchor="middle" x="134.765625" xml:space="preserve" y="462.3993055555555" zvalue="17">35kV Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="319" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="134.765625" xml:space="preserve" y="479.3993055555555" zvalue="17">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,563.344,343.361) scale(1,1) translate(0,0)" writing-mode="lr" x="563.34" xml:space="preserve" y="347.86" zvalue="19">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,781.125,290.722) scale(1,1) translate(0,0)" writing-mode="lr" x="781.13" xml:space="preserve" y="295.22" zvalue="34">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,467.067,659.917) scale(1,1) translate(0,0)" writing-mode="lr" x="467.07" xml:space="preserve" y="664.42" zvalue="39">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1848.04,660.083) scale(1,1) translate(0,0)" writing-mode="lr" x="1848.04" xml:space="preserve" y="664.58" zvalue="40">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="311" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1238.32,575.681) scale(1,1) translate(0,-1.23426e-13)" writing-mode="lr" x="1238.31746031746" xml:space="preserve" y="580.1805555555555" zvalue="43">10kV分段 012</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="303" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,71.5,652.5) scale(1,1) translate(0,0)" writing-mode="lr" x="71.5" xml:space="preserve" y="657" zvalue="46">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,204.399,319.841) scale(1,1) translate(0,0)" writing-mode="lr" x="204.4" xml:space="preserve" y="324.34" zvalue="47">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,309.399,319.841) scale(1,1) translate(0,0)" writing-mode="lr" x="309.4" xml:space="preserve" y="324.34" zvalue="48">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="296" stroke="rgb(255,255,255)" text-anchor="middle" x="250.96875" xml:space="preserve" y="460.8368055555555" zvalue="50">10kV Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="296" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="250.96875" xml:space="preserve" y="477.8368055555555" zvalue="50">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="295" stroke="rgb(255,255,255)" text-anchor="middle" x="308" xml:space="preserve" y="460.8368055555555" zvalue="51">10kV Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="295" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="308" xml:space="preserve" y="477.8368055555555" zvalue="51">母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="294" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,495.75) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="500.25" zvalue="52">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="293" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,521.25) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="525.75" zvalue="53">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,544.25) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="548.75" zvalue="54">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,567.25) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="571.75" zvalue="55">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="290" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,594.25) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="598.75" zvalue="56">Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231.054,960) scale(1,1) translate(0,0)" writing-mode="lr" x="231.05" xml:space="preserve" y="966" zvalue="57">MengXiu-01-2020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,141.054,992) scale(1,1) translate(0,0)" writing-mode="lr" x="141.05" xml:space="preserve" y="998" zvalue="58">李宏梅</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,321.054,992) scale(1,1) translate(0,0)" writing-mode="lr" x="321.05" xml:space="preserve" y="998" zvalue="59">20200924</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="286" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45,179) scale(1,1) translate(0,0)" writing-mode="lr" x="45" xml:space="preserve" y="184.5" zvalue="60">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225,179) scale(1,1) translate(0,0)" writing-mode="lr" x="225" xml:space="preserve" y="184.5" zvalue="61">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.6875,203.25) scale(1,1) translate(0,0)" writing-mode="lr" x="48.69" xml:space="preserve" y="207.75" zvalue="62">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.1875,251) scale(1,1) translate(0,0)" writing-mode="lr" x="52.19" xml:space="preserve" y="256.5" zvalue="64">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="281" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.75,250.5) scale(1,1) translate(0,0)" writing-mode="lr" x="233.75" xml:space="preserve" y="256" zvalue="65">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="280" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.1875,274) scale(1,1) translate(0,0)" writing-mode="lr" x="52.19" xml:space="preserve" y="279.5" zvalue="66">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="279" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.75,273.5) scale(1,1) translate(0,0)" writing-mode="lr" x="233.75" xml:space="preserve" y="279" zvalue="67">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="276" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.6875,227.25) scale(1,1) translate(0,0)" writing-mode="lr" x="49.69" xml:space="preserve" y="231.75" zvalue="72">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="275" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.75,227) scale(1,1) translate(0,0)" writing-mode="lr" x="232.75" xml:space="preserve" y="231.5" zvalue="73">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" x="747.3671875" xml:space="preserve" y="499.1961811913385" zvalue="81">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="747.3671875" xml:space="preserve" y="515.1961811913385" zvalue="81">2500KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1147.6,655.933) scale(1,1) translate(0,0)" writing-mode="lr" x="1147.6" xml:space="preserve" y="660.4299999999999" zvalue="99">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,784.475,447.667) scale(1,1) translate(0,0)" writing-mode="lr" x="784.47" xml:space="preserve" y="452.17" zvalue="131">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,791.256,389.694) scale(1,1) translate(0,0)" writing-mode="lr" x="791.26" xml:space="preserve" y="394.19" zvalue="134">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" x="1371.765625" xml:space="preserve" y="499.4635425143772" zvalue="147">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1371.765625" xml:space="preserve" y="515.4635425143772" zvalue="147">10000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1407.13,447.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1407.13" xml:space="preserve" y="452.17" zvalue="150">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1415.72,387.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1415.72" xml:space="preserve" y="392.19" zvalue="153">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,506.183,826.015) scale(1,1) translate(0,0)" writing-mode="lr" x="506.18" xml:space="preserve" y="830.52" zvalue="284">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,487.811,953.25) scale(1,1) translate(2.97849e-13,0)" writing-mode="lr" x="487.81" xml:space="preserve" y="957.75" zvalue="294">10kV南京里线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="365" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1192.15,953.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1192.15" xml:space="preserve" y="957.88" zvalue="436">10kV2号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="392" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,465.767,737.62) scale(1,1) translate(0,0)" writing-mode="lr" x="465.77" xml:space="preserve" y="742.12" zvalue="442">081</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="395" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,514.972,790.111) scale(1,1) translate(0,0)" writing-mode="lr" x="514.97" xml:space="preserve" y="794.61" zvalue="449">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="407" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,836.392,642.62) scale(1,1) translate(0,0)" writing-mode="lr" x="836.39" xml:space="preserve" y="647.12" zvalue="456">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="412" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1460.77,646.12) scale(1,1) translate(3.37775e-12,5.6632e-13)" writing-mode="lr" x="1460.77" xml:space="preserve" y="650.62" zvalue="460">002</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="414" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,812.683,177.625) scale(1,1) translate(0,0)" writing-mode="lr" x="812.6799999999999" xml:space="preserve" y="182.13" zvalue="463">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="420" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,757.75,317.778) scale(1,1) translate(0,0)" writing-mode="lr" x="757.75" xml:space="preserve" y="322.28" zvalue="467">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="421" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,757.75,262.778) scale(1,1) translate(0,0)" writing-mode="lr" x="757.75" xml:space="preserve" y="267.28" zvalue="471">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="427" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,750.25,420.278) scale(1,1) translate(0,0)" writing-mode="lr" x="750.25" xml:space="preserve" y="424.78" zvalue="475">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="428" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1372.75,414.419) scale(1,1) translate(0,0)" writing-mode="lr" x="1372.75" xml:space="preserve" y="418.92" zvalue="478">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="443" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1035.88,99.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1035.88" xml:space="preserve" y="104.13" zvalue="489">35kV汉秀线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="447" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1018.72,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1018.72" xml:space="preserve" y="267.67" zvalue="492">381</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="452" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1019,312.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1019" xml:space="preserve" y="316.94" zvalue="496">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="456" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1020.99,211.194) scale(1,1) translate(0,0)" writing-mode="lr" x="1020.99" xml:space="preserve" y="215.69" zvalue="500">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="459" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1090.36,288.528) scale(1,1) translate(0,0)" writing-mode="lr" x="1090.36" xml:space="preserve" y="293.03" zvalue="504">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="463" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1090.36,239.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1090.36" xml:space="preserve" y="244.42" zvalue="508">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="467" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1088.86,187.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1088.86" xml:space="preserve" y="192.17" zvalue="512">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="474" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1072.69,143.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1072.69" xml:space="preserve" y="148.42" zvalue="516">9</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="476" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,591.474,553.375) scale(1,1) translate(0,0)" writing-mode="lr" x="591.47" xml:space="preserve" y="557.88" zvalue="521">10kVⅠ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="482" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,562,643.679) scale(1,1) translate(0,0)" writing-mode="lr" x="562" xml:space="preserve" y="648.1799999999999" zvalue="525">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="484" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1661.97,561.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1661.97" xml:space="preserve" y="565.88" zvalue="529">10kVⅡ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="483" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1635.78,642.679) scale(1,1) translate(0,0)" writing-mode="lr" x="1635.78" xml:space="preserve" y="647.1799999999999" zvalue="532">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1015.54,735.626) scale(1,1) translate(-2.22831e-13,0)" writing-mode="lr" x="1015.54" xml:space="preserve" y="740.13" zvalue="646">085</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1019.25,801.028) scale(1,1) translate(0,0)" writing-mode="lr" x="1019.25" xml:space="preserve" y="805.53" zvalue="652">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1059.93,812.515) scale(1,1) translate(0,0)" writing-mode="lr" x="1059.93" xml:space="preserve" y="817.02" zvalue="655">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1041,951.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1041" xml:space="preserve" y="956.38" zvalue="658">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1020.11,860.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1020.11" xml:space="preserve" y="864.61" zvalue="670">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="166" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1254.88,96.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1254.88" xml:space="preserve" y="101" zvalue="768">35kV丽秀线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1237.72,260.042) scale(1,1) translate(0,0)" writing-mode="lr" x="1237.72" xml:space="preserve" y="264.54" zvalue="770">382</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1238,309.319) scale(1,1) translate(0,0)" writing-mode="lr" x="1238" xml:space="preserve" y="313.82" zvalue="772">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1239.99,208.069) scale(1,1) translate(0,0)" writing-mode="lr" x="1239.99" xml:space="preserve" y="212.57" zvalue="777">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1309.36,285.403) scale(1,1) translate(0,0)" writing-mode="lr" x="1309.36" xml:space="preserve" y="289.9" zvalue="780">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1309.36,236.792) scale(1,1) translate(0,0)" writing-mode="lr" x="1309.36" xml:space="preserve" y="241.29" zvalue="783">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1307.86,184.542) scale(1,1) translate(0,0)" writing-mode="lr" x="1307.86" xml:space="preserve" y="189.04" zvalue="786">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1291.69,140.792) scale(1,1) translate(0,0)" writing-mode="lr" x="1291.69" xml:space="preserve" y="145.29" zvalue="790">9</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,673.198,481.5) scale(1,1) translate(0,0)" writing-mode="lr" x="673.2" xml:space="preserve" y="486" zvalue="798">35kV#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,672.5,499.5) scale(1,1) translate(0,0)" writing-mode="lr" x="672.5" xml:space="preserve" y="504" zvalue="803">50kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,463.111,881.111) scale(1,1) translate(0,0)" writing-mode="lr" x="463.11" xml:space="preserve" y="885.61" zvalue="807">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,551.479,979.398) scale(1,1) translate(0,0)" writing-mode="lr" x="551.48" xml:space="preserve" y="983.9" zvalue="812">10kV#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,668.183,824.515) scale(1,1) translate(0,0)" writing-mode="lr" x="668.1799999999999" xml:space="preserve" y="829.02" zvalue="818">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="221" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,652.881,942.5) scale(1,1) translate(0,0)" writing-mode="lr" x="652.88" xml:space="preserve" y="947" zvalue="821">10kV小街线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,627.767,736.12) scale(1,1) translate(0,0)" writing-mode="lr" x="627.77" xml:space="preserve" y="740.62" zvalue="823">082</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="206" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,676.972,788.611) scale(1,1) translate(0,0)" writing-mode="lr" x="676.97" xml:space="preserve" y="793.11" zvalue="826">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="205" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,625.111,879.611) scale(1,1) translate(0,0)" writing-mode="lr" x="625.11" xml:space="preserve" y="884.11" zvalue="830">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,788.183,826.015) scale(1,1) translate(0,0)" writing-mode="lr" x="788.1799999999999" xml:space="preserve" y="830.52" zvalue="836">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,772.881,944) scale(1,1) translate(0,0)" writing-mode="lr" x="772.88" xml:space="preserve" y="948.5" zvalue="839">10kV备用一回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="224" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,747.767,737.62) scale(1,1) translate(0,0)" writing-mode="lr" x="747.77" xml:space="preserve" y="742.12" zvalue="842">083</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="223" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,796.972,790.111) scale(1,1) translate(0,0)" writing-mode="lr" x="796.97" xml:space="preserve" y="794.61" zvalue="845">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="222" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,745.111,881.111) scale(1,1) translate(0,0)" writing-mode="lr" x="745.11" xml:space="preserve" y="885.61" zvalue="849">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="246" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,908.802,822.515) scale(1,1) translate(0,0)" writing-mode="lr" x="908.8" xml:space="preserve" y="827.02" zvalue="854">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="242" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,893.5,940.5) scale(1,1) translate(0,0)" writing-mode="lr" x="893.5" xml:space="preserve" y="945" zvalue="857">10kV备用二回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="241" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,868.385,734.12) scale(1,1) translate(0,0)" writing-mode="lr" x="868.39" xml:space="preserve" y="738.62" zvalue="860">084</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,917.591,786.611) scale(1,1) translate(0,0)" writing-mode="lr" x="917.59" xml:space="preserve" y="791.11" zvalue="863">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="239" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,865.73,877.611) scale(1,1) translate(0,0)" writing-mode="lr" x="865.73" xml:space="preserve" y="882.11" zvalue="867">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1161.5,735.126) scale(1,1) translate(0,0)" writing-mode="lr" x="1161.5" xml:space="preserve" y="739.63" zvalue="872">086</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1165.21,800.528) scale(1,1) translate(0,0)" writing-mode="lr" x="1165.21" xml:space="preserve" y="805.03" zvalue="875">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1205.89,812.015) scale(1,1) translate(0,0)" writing-mode="lr" x="1205.89" xml:space="preserve" y="816.52" zvalue="878">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1166.07,859.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1166.07" xml:space="preserve" y="864.11" zvalue="882">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="344" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1846.29,949.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1846.29" xml:space="preserve" y="953.63" zvalue="888">10kV4号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="343" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1682.54,730.126) scale(1,1) translate(0,0)" writing-mode="lr" x="1682.54" xml:space="preserve" y="734.63" zvalue="890">091</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="342" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1686.25,795.528) scale(1,1) translate(0,0)" writing-mode="lr" x="1686.25" xml:space="preserve" y="800.03" zvalue="893">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="341" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1726.93,807.015) scale(1,1) translate(0,0)" writing-mode="lr" x="1726.93" xml:space="preserve" y="811.52" zvalue="896">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="340" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1708,947.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1708" xml:space="preserve" y="952.13" zvalue="899">10kV3号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="339" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1687.11,854.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1687.11" xml:space="preserve" y="859.11" zvalue="902">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="338" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1335.18,819.015) scale(1,1) translate(0,0)" writing-mode="lr" x="1335.18" xml:space="preserve" y="823.52" zvalue="905">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="337" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1319.88,937) scale(1,1) translate(0,0)" writing-mode="lr" x="1319.88" xml:space="preserve" y="941.5" zvalue="908">10kV勐平线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="336" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1294.77,730.62) scale(1,1) translate(0,0)" writing-mode="lr" x="1294.77" xml:space="preserve" y="735.12" zvalue="911">087</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="335" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1343.97,783.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1343.97" xml:space="preserve" y="787.61" zvalue="914">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="318" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1292.11,874.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1292.11" xml:space="preserve" y="878.61" zvalue="918">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1451.56,819.515) scale(1,1) translate(0,0)" writing-mode="lr" x="1451.56" xml:space="preserve" y="824.02" zvalue="922">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="316" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1439.88,938.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1439.88" xml:space="preserve" y="943" zvalue="925">10kV备用Ⅳ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1411.77,732.12) scale(1,1) translate(0,0)" writing-mode="lr" x="1411.77" xml:space="preserve" y="736.62" zvalue="927">088</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1464.75,783.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1464.75" xml:space="preserve" y="787.61" zvalue="931">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1572.18,816.015) scale(1,1) translate(0,0)" writing-mode="lr" x="1572.18" xml:space="preserve" y="820.52" zvalue="939">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="310" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1560.5,935) scale(1,1) translate(0,0)" writing-mode="lr" x="1560.5" xml:space="preserve" y="939.5" zvalue="942">10kV备用Ⅴ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1535.39,729.62) scale(1,1) translate(0,0)" writing-mode="lr" x="1535.39" xml:space="preserve" y="734.12" zvalue="944">089</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1585.37,783.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1585.37" xml:space="preserve" y="787.61" zvalue="948">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="304" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1828.5,729.626) scale(1,1) translate(0,0)" writing-mode="lr" x="1828.5" xml:space="preserve" y="734.13" zvalue="956">092</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="300" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1832.21,795.028) scale(1,1) translate(0,0)" writing-mode="lr" x="1832.21" xml:space="preserve" y="799.53" zvalue="959">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="299" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1872.89,806.515) scale(1,1) translate(0,0)" writing-mode="lr" x="1872.89" xml:space="preserve" y="811.02" zvalue="962">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="298" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1833.07,854.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1833.07" xml:space="preserve" y="858.61" zvalue="966">67</text>
  <ellipse cx="1274.28" cy="255.03" fill="rgb(255,0,0)" fill-opacity="1" id="23" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1062"/>
  <ellipse cx="1454.37" cy="453.37" fill="rgb(255,0,0)" fill-opacity="1" id="24" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1064"/>
 </g>
 <g id="ButtonClass">
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="262" y="390.25" zvalue="1050"/></g>
  <g href="全站公用20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="58.19" y="309.25" zvalue="1051"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="160.09" y="390.25" zvalue="1052"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="58.19" y="390.25" zvalue="1053"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="58.19" y="349.75" zvalue="1054"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="508">
   <path class="kv35" d="M 606 344.61 L 1582.59 344.61" stroke-width="6" zvalue="18"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674404073475" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674404073475"/></metadata>
  <path d="M 606 344.61 L 1582.59 344.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="429">
   <path class="kv10" d="M 462.33 690.5 L 1209.75 690.5" stroke-width="6" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674404007939" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674404007939"/></metadata>
  <path d="M 462.33 690.5 L 1209.75 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="426">
   <path class="kv10" d="M 1261 691.75 L 1894 691.75" stroke-width="6" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674404139011" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674404139011"/></metadata>
  <path d="M 1261 691.75 L 1894 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="449">
   <use class="kv35" height="30" transform="rotate(0,809.75,289.944) scale(0.833333,0.833333) translate(160.7,55.4889)" width="15" x="803.5" xlink:href="#Disconnector:刀闸_0" y="277.444442987442" zvalue="33"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453999722499" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453999722499"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,809.75,289.944) scale(0.833333,0.833333) translate(160.7,55.4889)" width="15" x="803.5" y="277.444442987442"/></g>
  <g id="574">
   <use class="kv10" height="36" transform="rotate(0,1168.98,643.679) scale(1.30867,1.22026) translate(-273.564,-112.22)" width="14" x="1159.821428571429" xlink:href="#Disconnector:手车刀闸_0" y="621.7142857142857" zvalue="98"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453999656963" ObjectName="10kV分段0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453999656963"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1168.98,643.679) scale(1.30867,1.22026) translate(-273.564,-112.22)" width="14" x="1159.821428571429" y="621.7142857142857"/></g>
  <g id="633">
   <use class="kv35" height="30" transform="rotate(0,809.728,388.694) scale(0.833333,0.833333) translate(160.696,75.2389)" width="15" x="803.4778228110375" xlink:href="#Disconnector:刀闸_0" y="376.1944428814782" zvalue="132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453999591427" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453999591427"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,809.728,388.694) scale(0.833333,0.833333) translate(160.696,75.2389)" width="15" x="803.4778228110375" y="376.1944428814782"/></g>
  <g id="664">
   <use class="kv35" height="30" transform="rotate(0,1434.19,388.694) scale(0.833333,0.833333) translate(285.589,75.2389)" width="15" x="1427.944444444445" xlink:href="#Disconnector:刀闸_0" y="376.1944427490234" zvalue="151"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453999525891" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453999525891"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1434.19,388.694) scale(0.833333,0.833333) translate(285.589,75.2389)" width="15" x="1427.944444444445" y="376.1944427490234"/></g>
  <g id="162">
   <use class="kv10" height="30" transform="rotate(0,490.808,826.015) scale(0.833333,-0.833333) translate(96.9116,-1819.73)" width="15" x="484.5582010582013" xlink:href="#Disconnector:刀闸_0" y="813.5153504566865" zvalue="283"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453999460355" ObjectName="10kV南京里线0816隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453999460355"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,490.808,826.015) scale(0.833333,-0.833333) translate(96.9116,-1819.73)" width="15" x="484.5582010582013" y="813.5153504566865"/></g>
  <g id="448">
   <use class="kv35" height="30" transform="rotate(0,1037.75,313.444) scale(0.833333,0.833333) translate(206.3,60.1889)" width="15" x="1031.5" xlink:href="#Disconnector:刀闸_0" y="300.9444428814782" zvalue="495"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454000771075" ObjectName="35kV汉秀线3811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454000771075"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1037.75,313.444) scale(0.833333,0.833333) translate(206.3,60.1889)" width="15" x="1031.5" y="300.9444428814782"/></g>
  <g id="453">
   <use class="kv35" height="30" transform="rotate(0,1037.61,212.194) scale(0.833333,0.833333) translate(206.273,39.9389)" width="15" x="1031.363103720653" xlink:href="#Disconnector:刀闸_0" y="199.6944428814782" zvalue="499"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454000836611" ObjectName="35kV汉秀线3816隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454000836611"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1037.61,212.194) scale(0.833333,0.833333) translate(206.273,39.9389)" width="15" x="1031.363103720653" y="199.6944428814782"/></g>
  <g id="470">
   <use class="kv35" height="30" transform="rotate(270,1073.69,159.667) scale(-0.833333,-0.833333) translate(-2363.36,-353.767)" width="15" x="1067.436251263037" xlink:href="#Disconnector:刀闸_0" y="147.1666666666665" zvalue="515"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454001950723" ObjectName="35kV汉秀线3819隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454001950723"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1073.69,159.667) scale(-0.833333,-0.833333) translate(-2363.36,-353.767)" width="15" x="1067.436251263037" y="147.1666666666665"/></g>
  <g id="479">
   <use class="kv10" height="36" transform="rotate(0,595.063,643.679) scale(1.30867,1.22026) translate(-138.195,-112.22)" width="14" x="585.9025971615879" xlink:href="#Disconnector:手车刀闸_0" y="621.7142857142857" zvalue="524"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454001360899" ObjectName="10kVⅠ段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454001360899"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,595.063,643.679) scale(1.30867,1.22026) translate(-138.195,-112.22)" width="14" x="585.9025971615879" y="621.7142857142857"/></g>
  <g id="487">
   <use class="kv10" height="36" transform="rotate(0,1665.06,643.679) scale(1.30867,1.22026) translate(-390.574,-112.22)" width="14" x="1655.902597161588" xlink:href="#Disconnector:手车刀闸_0" y="621.7142857142857" zvalue="530"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454001426435" ObjectName="10kVⅡ段母线电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454001426435"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1665.06,643.679) scale(1.30867,1.22026) translate(-390.574,-112.22)" width="14" x="1655.902597161588" y="621.7142857142857"/></g>
  <g id="58">
   <use class="kv10" height="30" transform="rotate(0,1047.31,813.515) scale(0.833333,-0.833333) translate(208.212,-1792.23)" width="15" x="1041.058201058201" xlink:href="#Disconnector:刀闸_0" y="801.015342827292" zvalue="653"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454001623043" ObjectName="10kV1号电容器0856隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454001623043"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1047.31,813.515) scale(0.833333,-0.833333) translate(208.212,-1792.23)" width="15" x="1041.058201058201" y="801.015342827292"/></g>
  <g id="184">
   <use class="kv35" height="30" transform="rotate(0,1256.75,310.319) scale(0.833333,0.833333) translate(250.1,59.5639)" width="15" x="1250.5" xlink:href="#Disconnector:刀闸_0" y="297.8194428814782" zvalue="771"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454002802691" ObjectName="35kV丽秀线3821隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454002802691"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1256.75,310.319) scale(0.833333,0.833333) translate(250.1,59.5639)" width="15" x="1250.5" y="297.8194428814782"/></g>
  <g id="181">
   <use class="kv35" height="30" transform="rotate(0,1256.61,209.069) scale(0.833333,0.833333) translate(250.073,39.3139)" width="15" x="1250.363103720653" xlink:href="#Disconnector:刀闸_0" y="196.5694428814782" zvalue="775"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454002737155" ObjectName="35kV丽秀线3826隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454002737155"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1256.61,209.069) scale(0.833333,0.833333) translate(250.073,39.3139)" width="15" x="1250.363103720653" y="196.5694428814782"/></g>
  <g id="171">
   <use class="kv35" height="30" transform="rotate(270,1292.69,156.542) scale(-0.833333,-0.833333) translate(-2845.16,-346.892)" width="15" x="1286.436251263037" xlink:href="#Disconnector:刀闸_0" y="144.0416666666665" zvalue="789"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454002212867" ObjectName="35kV丽秀线3829隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454002212867"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1292.69,156.542) scale(-0.833333,-0.833333) translate(-2845.16,-346.892)" width="15" x="1286.436251263037" y="144.0416666666665"/></g>
  <g id="191">
   <use class="kv35" height="30" transform="rotate(0,672,384) scale(1,1) translate(0,0)" width="15" x="664.5" xlink:href="#Disconnector:令克_0" y="369" zvalue="799"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454002999299" ObjectName="35kV#1站用变隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454002999299"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,672,384) scale(1,1) translate(0,0)" width="15" x="664.5" y="369"/></g>
  <g id="203">
   <use class="kv10" height="30" transform="rotate(0,555.125,867.75) scale(1.25,-1.25) translate(-109.15,-1558.2)" width="15" x="545.75" xlink:href="#Disconnector:令克_0" y="849" zvalue="809"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454003326979" ObjectName="10kV#2站用变隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454003326979"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,555.125,867.75) scale(1.25,-1.25) translate(-109.15,-1558.2)" width="15" x="545.75" y="849"/></g>
  <g id="218">
   <use class="kv10" height="30" transform="rotate(0,652.808,824.515) scale(0.833333,-0.833333) translate(129.312,-1816.43)" width="15" x="646.5582010582013" xlink:href="#Disconnector:刀闸_0" y="812.0153504566865" zvalue="817"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454003720195" ObjectName="10kV小街线0826隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454003720195"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,652.808,824.515) scale(0.833333,-0.833333) translate(129.312,-1816.43)" width="15" x="646.5582010582013" y="812.0153504566865"/></g>
  <g id="238">
   <use class="kv10" height="30" transform="rotate(0,772.808,826.015) scale(0.833333,-0.833333) translate(153.312,-1819.73)" width="15" x="766.5582010582013" xlink:href="#Disconnector:刀闸_0" y="813.5153504566865" zvalue="835"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454004244483" ObjectName="10kV备用一回线0836隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454004244483"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,772.808,826.015) scale(0.833333,-0.833333) translate(153.312,-1819.73)" width="15" x="766.5582010582013" y="813.5153504566865"/></g>
  <g id="261">
   <use class="kv10" height="30" transform="rotate(0,893.427,822.515) scale(0.833333,-0.833333) translate(177.435,-1812.03)" width="15" x="887.176852457616" xlink:href="#Disconnector:刀闸_0" y="810.0153504566865" zvalue="853"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454004703235" ObjectName="10kV备用二回线0846隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454004703235"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,893.427,822.515) scale(0.833333,-0.833333) translate(177.435,-1812.03)" width="15" x="887.176852457616" y="810.0153504566865"/></g>
  <g id="272">
   <use class="kv10" height="30" transform="rotate(0,1193.27,813.015) scale(0.833333,-0.833333) translate(237.403,-1791.13)" width="15" x="1187.017169471115" xlink:href="#Disconnector:刀闸_0" y="800.515342827292" zvalue="876"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454004899843" ObjectName="10kV2号电容器0866隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454004899843"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1193.27,813.015) scale(0.833333,-0.833333) translate(237.403,-1791.13)" width="15" x="1187.017169471115" y="800.515342827292"/></g>
  <g id="506">
   <use class="kv10" height="30" transform="rotate(0,1714.31,808.015) scale(0.833333,-0.833333) translate(341.612,-1780.13)" width="15" x="1708.058201058201" xlink:href="#Disconnector:刀闸_0" y="795.515342827292" zvalue="894"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454006996995" ObjectName="10kV3号电容器0916隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454006996995"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1714.31,808.015) scale(0.833333,-0.833333) translate(341.612,-1780.13)" width="15" x="1708.058201058201" y="795.515342827292"/></g>
  <g id="477">
   <use class="kv10" height="30" transform="rotate(0,1319.81,819.015) scale(0.833333,-0.833333) translate(262.712,-1804.33)" width="15" x="1313.558201058201" xlink:href="#Disconnector:刀闸_0" y="806.5153504566865" zvalue="904"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454006734851" ObjectName="10kV勐平线0876隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454006734851"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1319.81,819.015) scale(0.833333,-0.833333) translate(262.712,-1804.33)" width="15" x="1313.558201058201" y="806.5153504566865"/></g>
  <g id="404">
   <use class="kv10" height="30" transform="rotate(0,1439.81,820.515) scale(0.833333,-0.833333) translate(286.712,-1807.63)" width="15" x="1433.558201058201" xlink:href="#Disconnector:刀闸_0" y="808.0153504566865" zvalue="921"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454006276099" ObjectName="10kV备用Ⅳ回线0886隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454006276099"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1439.81,820.515) scale(0.833333,-0.833333) translate(286.712,-1807.63)" width="15" x="1433.558201058201" y="808.0153504566865"/></g>
  <g id="372">
   <use class="kv10" height="30" transform="rotate(0,1560.43,817.015) scale(0.833333,-0.833333) translate(310.835,-1799.93)" width="15" x="1554.176852457616" xlink:href="#Disconnector:刀闸_0" y="804.5153504566865" zvalue="938"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454005817347" ObjectName="10kV备用Ⅴ回线0896隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454005817347"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1560.43,817.015) scale(0.833333,-0.833333) translate(310.835,-1799.93)" width="15" x="1554.176852457616" y="804.5153504566865"/></g>
  <g id="351">
   <use class="kv10" height="30" transform="rotate(0,1860.27,807.515) scale(0.833333,-0.833333) translate(370.803,-1779.03)" width="15" x="1854.017169471115" xlink:href="#Disconnector:刀闸_0" y="795.015342827292" zvalue="960"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454005227523" ObjectName="10kV4号电容器0926隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454005227523"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1860.27,807.515) scale(0.833333,-0.833333) translate(370.803,-1779.03)" width="15" x="1854.017169471115" y="795.015342827292"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="309">
   <path class="kv10" d="M 1168.98 664.42 L 1168.98 690.5" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="574@1" LinkObjectIDznd="429@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1168.98 664.42 L 1168.98 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="264">
   <path class="kv10" d="M 1168.98 622.93 L 1168.98 605.09 L 1213.64 605.09" stroke-width="1" zvalue="101"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="574@0" LinkObjectIDznd="576@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1168.98 622.93 L 1168.98 605.09 L 1213.64 605.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="252">
   <path class="kv35" d="M 810.09 493.99 L 810.09 468.4" stroke-width="1" zvalue="130"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="549@0" LinkObjectIDznd="630@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.09 493.99 L 810.09 468.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv35" d="M 810.11 441.16 L 810.11 400.98" stroke-width="1" zvalue="133"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="630@0" LinkObjectIDznd="633@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.11 441.16 L 810.11 400.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv35" d="M 809.8 376.61 L 809.8 344.61" stroke-width="1" zvalue="135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="633@0" LinkObjectIDznd="508@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.8 376.61 L 809.8 344.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="245">
   <path class="kv35" d="M 1433.36 493.99 L 1433.36 468.37" stroke-width="1" zvalue="149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="667@0" LinkObjectIDznd="666@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.36 493.99 L 1433.36 468.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv35" d="M 1433.86 440.26 L 1433.86 400.98" stroke-width="1" zvalue="152"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="666@0" LinkObjectIDznd="664@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.86 440.26 L 1433.86 400.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="243">
   <path class="kv35" d="M 1434.27 376.61 L 1434.27 344.61" stroke-width="1" zvalue="154"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="664@0" LinkObjectIDznd="508@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1434.27 376.61 L 1434.27 344.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv10" d="M 490.88 891.65 L 490.88 838.1" stroke-width="1" zvalue="287"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@0" LinkObjectIDznd="162@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 490.88 891.65 L 490.88 838.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="283">
   <path class="kv10" d="M 1259.35 605.22 L 1310.44 605.22 L 1310.44 691.75" stroke-width="1" zvalue="400"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="576@1" LinkObjectIDznd="426@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1259.35 605.22 L 1310.44 605.22 L 1310.44 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="390">
   <path class="kv10" d="M 490.86 813.73 L 490.86 757.46" stroke-width="1" zvalue="442"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162@1" LinkObjectIDznd="389@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 490.86 813.73 L 490.86 757.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="397">
   <path class="kv10" d="M 504.92 772.17 L 490.86 772.17" stroke-width="1" zvalue="450"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="396@0" LinkObjectIDznd="390" MaxPinNum="2"/>
   </metadata>
  <path d="M 504.92 772.17 L 490.86 772.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="405">
   <path class="kv10" d="M 810.06 566.71 L 810.06 625.29" stroke-width="1" zvalue="456"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="549@1" LinkObjectIDznd="403@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.06 566.71 L 810.06 625.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="406">
   <path class="kv10" d="M 810.06 662.46 L 810.06 690.5" stroke-width="1" zvalue="457"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="403@0" LinkObjectIDznd="429@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.06 662.46 L 810.06 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="410">
   <path class="kv10" d="M 1433.33 566.71 L 1433.33 628.79" stroke-width="1" zvalue="460"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="667@1" LinkObjectIDznd="409@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.33 566.71 L 1433.33 628.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="411">
   <path class="kv10" d="M 1433.81 665.96 L 1433.81 691.75" stroke-width="1" zvalue="461"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="409@0" LinkObjectIDznd="426@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1433.81 665.96 L 1433.81 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="415">
   <path class="kv35" d="M 809.8 302.23 L 809.8 344.61" stroke-width="1" zvalue="463"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="449@1" LinkObjectIDznd="508@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.8 302.23 L 809.8 344.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="416">
   <path class="kv35" d="M 809.82 277.86 L 809.75 255.3" stroke-width="1" zvalue="464"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="449@0" LinkObjectIDznd="413@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.82 277.86 L 809.75 255.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="419">
   <path class="kv35" d="M 794.83 316.92 L 809.8 316.92" stroke-width="1" zvalue="468"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="417@0" LinkObjectIDznd="415" MaxPinNum="2"/>
   </metadata>
  <path d="M 794.83 316.92 L 809.8 316.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="423">
   <path class="kv35" d="M 794.83 261.92 L 809.77 261.92" stroke-width="1" zvalue="472"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="422@0" LinkObjectIDznd="416" MaxPinNum="2"/>
   </metadata>
  <path d="M 794.83 261.92 L 809.77 261.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="425">
   <path class="kv35" d="M 792.33 419.42 L 810.11 419.42" stroke-width="1" zvalue="475"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="424@0" LinkObjectIDznd="251" MaxPinNum="2"/>
   </metadata>
  <path d="M 792.33 419.42 L 810.11 419.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="431">
   <path class="kv35" d="M 1414.83 413.56 L 1433.86 413.56" stroke-width="1" zvalue="479"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="430@0" LinkObjectIDznd="244" MaxPinNum="2"/>
   </metadata>
  <path d="M 1414.83 413.56 L 1433.86 413.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="450">
   <path class="kv35" d="M 1037.82 271.37 L 1037.82 301.36" stroke-width="1" zvalue="496"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="444@1" LinkObjectIDznd="448@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.82 271.37 L 1037.82 301.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="454">
   <path class="kv35" d="M 1037.69 148.07 L 1037.69 200.11" stroke-width="1" zvalue="500"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="441@0" LinkObjectIDznd="453@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.69 148.07 L 1037.69 200.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="455">
   <path class="kv35" d="M 1037.66 224.48 L 1037.66 244.41" stroke-width="1" zvalue="501"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="453@1" LinkObjectIDznd="444@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.66 224.48 L 1037.66 244.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="461">
   <path class="kv35" d="M 1053.28 287.67 L 1037.82 287.67" stroke-width="1" zvalue="505"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="460@0" LinkObjectIDznd="450" MaxPinNum="2"/>
   </metadata>
  <path d="M 1053.28 287.67 L 1037.82 287.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="465">
   <path class="kv35" d="M 1053.28 237.67 L 1037.66 237.67" stroke-width="1" zvalue="509"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="462@0" LinkObjectIDznd="455" MaxPinNum="2"/>
   </metadata>
  <path d="M 1053.28 237.67 L 1037.66 237.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="468">
   <path class="kv35" d="M 1054.53 186.67 L 1037.69 186.67" stroke-width="1" zvalue="512"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="466@0" LinkObjectIDznd="454" MaxPinNum="2"/>
   </metadata>
  <path d="M 1054.53 186.67 L 1037.69 186.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="480">
   <path class="kv10" d="M 595.06 610 L 595.06 622.93" stroke-width="1" zvalue="525"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="475@0" LinkObjectIDznd="479@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 595.06 610 L 595.06 622.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="481">
   <path class="kv10" d="M 595.06 664.42 L 595.06 690.5" stroke-width="1" zvalue="526"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="479@1" LinkObjectIDznd="429@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 595.06 664.42 L 595.06 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="486">
   <path class="kv10" d="M 1665.06 610 L 1665.06 622.93" stroke-width="1" zvalue="531"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="488@0" LinkObjectIDznd="487@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1665.06 610 L 1665.06 622.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="485">
   <path class="kv10" d="M 1665.06 664.42 L 1665.06 691.75" stroke-width="1" zvalue="533"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="487@1" LinkObjectIDznd="426@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1665.06 664.42 L 1665.06 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv10" d="M 1047.81 719.79 L 1047.81 690.5" stroke-width="1" zvalue="650"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@1" LinkObjectIDznd="429@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1047.81 719.79 L 1047.81 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv10" d="M 1046.44 867.73 L 1046.44 825.6" stroke-width="1" zvalue="654"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="58@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1046.44 867.73 L 1046.44 825.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv10" d="M 1047.36 801.23 L 1047.36 756.96" stroke-width="1" zvalue="656"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@1" LinkObjectIDznd="63@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1047.36 801.23 L 1047.36 756.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv10" d="M 1030.08 785.42 L 1047.36 785.42" stroke-width="1" zvalue="662"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="56" MaxPinNum="2"/>
   </metadata>
  <path d="M 1030.08 785.42 L 1047.36 785.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv10" d="M 1030.94 839.5 L 1046.44 839.5" stroke-width="1" zvalue="670"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="57" MaxPinNum="2"/>
   </metadata>
  <path d="M 1030.94 839.5 L 1046.44 839.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv35" d="M 1085.77 159.74 L 1112.5 159.74" stroke-width="1" zvalue="759"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="470@0" LinkObjectIDznd="469@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1085.77 159.74 L 1112.5 159.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv35" d="M 1015.71 159.72 L 1037.69 159.72" stroke-width="1" zvalue="764"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@0" LinkObjectIDznd="454" MaxPinNum="2"/>
   </metadata>
  <path d="M 1015.71 159.72 L 1037.69 159.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv35" d="M 1061.4 159.72 L 1049.54 159.72 L 1049.54 159.72 L 1037.69 159.72" stroke-width="1" zvalue="765"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="470@1" LinkObjectIDznd="155" MaxPinNum="2"/>
   </metadata>
  <path d="M 1061.4 159.72 L 1049.54 159.72 L 1049.54 159.72 L 1037.69 159.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv35" d="M 1256.82 268.24 L 1256.82 298.23" stroke-width="1" zvalue="773"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@1" LinkObjectIDznd="184@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1256.82 268.24 L 1256.82 298.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv35" d="M 1256.8 322.61 L 1256.8 344.61" stroke-width="1" zvalue="774"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@1" LinkObjectIDznd="508@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1256.8 322.61 L 1256.8 344.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv35" d="M 1256.69 144.95 L 1256.69 196.98" stroke-width="1" zvalue="776"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="181@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1256.69 144.95 L 1256.69 196.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv35" d="M 1256.66 221.36 L 1256.66 241.28" stroke-width="1" zvalue="778"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@1" LinkObjectIDznd="185@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1256.66 221.36 L 1256.66 241.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv35" d="M 1272.28 284.54 L 1256.82 284.54" stroke-width="1" zvalue="781"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="183" MaxPinNum="2"/>
   </metadata>
  <path d="M 1272.28 284.54 L 1256.82 284.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv35" d="M 1272.28 234.54 L 1256.66 234.54" stroke-width="1" zvalue="784"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 1272.28 234.54 L 1256.66 234.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv35" d="M 1273.53 183.54 L 1256.69 183.54" stroke-width="1" zvalue="787"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="180" MaxPinNum="2"/>
   </metadata>
  <path d="M 1273.53 183.54 L 1256.69 183.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv35" d="M 1304.77 156.61 L 1331.5 156.61" stroke-width="1" zvalue="791"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="172@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1304.77 156.61 L 1331.5 156.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv35" d="M 1234.71 156.59 L 1256.69 156.59" stroke-width="1" zvalue="793"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@0" LinkObjectIDznd="180" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.71 156.59 L 1256.69 156.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv35" d="M 1280.4 156.59 L 1256.69 156.59" stroke-width="1" zvalue="794"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@1" LinkObjectIDznd="168" MaxPinNum="2"/>
   </metadata>
  <path d="M 1280.4 156.59 L 1256.69 156.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv35" d="M 1037.8 325.73 L 1037.8 344.61" stroke-width="1" zvalue="795"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="448@1" LinkObjectIDznd="508@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.8 325.73 L 1037.8 344.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="kv35" d="M 671.92 412.4 L 671.92 396.25" stroke-width="1" zvalue="800"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@0" LinkObjectIDznd="191@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 671.92 412.4 L 671.92 396.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv35" d="M 672.08 370.75 L 672.08 344.61" stroke-width="1" zvalue="801"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="508@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 672.08 370.75 L 672.08 344.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv10" d="M 473.94 860.5 L 490.88 860.5" stroke-width="1" zvalue="806"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="158" MaxPinNum="2"/>
   </metadata>
  <path d="M 473.94 860.5 L 490.88 860.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv10" d="M 555.23 908.31 L 555.23 884.31" stroke-width="1" zvalue="811"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="203@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 555.23 908.31 L 555.23 884.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="kv10" d="M 529.09 909.92 L 529.09 896.31 L 555.23 896.31" stroke-width="1" zvalue="814"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="200@0" LinkObjectIDznd="201" MaxPinNum="2"/>
   </metadata>
  <path d="M 529.09 909.92 L 529.09 896.31 L 555.23 896.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="kv10" d="M 555.02 852.44 L 490.88 852.43" stroke-width="1" zvalue="815"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@1" LinkObjectIDznd="158" MaxPinNum="2"/>
   </metadata>
  <path d="M 555.02 852.44 L 490.88 852.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv10" d="M 652.88 890.15 L 652.88 836.6" stroke-width="1" zvalue="819"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="216@0" LinkObjectIDznd="218@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 652.88 890.15 L 652.88 836.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv10" d="M 652.86 812.23 L 652.86 755.96" stroke-width="1" zvalue="822"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@1" LinkObjectIDznd="215@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 652.86 812.23 L 652.86 755.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv10" d="M 652.06 718.79 L 652.06 690.5" stroke-width="1" zvalue="824"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@1" LinkObjectIDznd="429@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 652.06 718.79 L 652.06 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv10" d="M 666.92 770.67 L 652.86 770.67" stroke-width="1" zvalue="827"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="214" MaxPinNum="2"/>
   </metadata>
  <path d="M 666.92 770.67 L 652.86 770.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv10" d="M 635.94 859 L 652.88 859" stroke-width="1" zvalue="829"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="210@0" LinkObjectIDznd="217" MaxPinNum="2"/>
   </metadata>
  <path d="M 635.94 859 L 652.88 859" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="220">
   <path class="kv10" d="M 680.54 859 L 652.88 859" stroke-width="1" zvalue="833"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@0" LinkObjectIDznd="217" MaxPinNum="2"/>
   </metadata>
  <path d="M 680.54 859 L 652.88 859" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="237">
   <path class="kv10" d="M 772.88 891.65 L 772.88 838.1" stroke-width="1" zvalue="837"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="236@0" LinkObjectIDznd="238@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.88 891.65 L 772.88 838.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="234">
   <path class="kv10" d="M 772.86 813.73 L 772.86 757.46" stroke-width="1" zvalue="841"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="238@1" LinkObjectIDznd="235@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.86 813.73 L 772.86 757.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="kv10" d="M 772.06 720.29 L 772.06 690.5" stroke-width="1" zvalue="843"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@1" LinkObjectIDznd="429@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.06 720.29 L 772.06 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="kv10" d="M 786.92 772.17 L 772.86 772.17" stroke-width="1" zvalue="846"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="232@0" LinkObjectIDznd="234" MaxPinNum="2"/>
   </metadata>
  <path d="M 786.92 772.17 L 772.86 772.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="229">
   <path class="kv10" d="M 755.94 860.5 L 772.88 860.5" stroke-width="1" zvalue="848"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="237" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.94 860.5 L 772.88 860.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="227">
   <path class="kv10" d="M 800.54 860.5 L 772.88 860.5" stroke-width="1" zvalue="851"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="228@0" LinkObjectIDznd="237" MaxPinNum="2"/>
   </metadata>
  <path d="M 800.54 860.5 L 772.88 860.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="260">
   <path class="kv10" d="M 893.5 888.15 L 893.5 834.6" stroke-width="1" zvalue="855"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@0" LinkObjectIDznd="261@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 893.5 888.15 L 893.5 834.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="257">
   <path class="kv10" d="M 893.48 810.23 L 893.48 753.96" stroke-width="1" zvalue="859"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@1" LinkObjectIDznd="258@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 893.48 810.23 L 893.48 753.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="256">
   <path class="kv10" d="M 892.68 716.79 L 892.68 690.5" stroke-width="1" zvalue="861"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="258@1" LinkObjectIDznd="429@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 892.68 716.79 L 892.68 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="254">
   <path class="kv10" d="M 907.54 768.67 L 893.48 768.67" stroke-width="1" zvalue="864"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="255@0" LinkObjectIDznd="257" MaxPinNum="2"/>
   </metadata>
  <path d="M 907.54 768.67 L 893.48 768.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="249">
   <path class="kv10" d="M 876.56 857 L 893.5 857" stroke-width="1" zvalue="866"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@0" LinkObjectIDznd="260" MaxPinNum="2"/>
   </metadata>
  <path d="M 876.56 857 L 893.5 857" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="247">
   <path class="kv10" d="M 921.16 857 L 893.5 857" stroke-width="1" zvalue="869"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@0" LinkObjectIDznd="260" MaxPinNum="2"/>
   </metadata>
  <path d="M 921.16 857 L 893.5 857" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="274">
   <path class="kv10" d="M 1193.77 719.29 L 1193.77 690.5" stroke-width="1" zvalue="873"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="277@1" LinkObjectIDznd="429@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1193.77 719.29 L 1193.77 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="271">
   <path class="kv10" d="M 1192.59 868.23 L 1192.59 825.1" stroke-width="1" zvalue="877"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="375@0" LinkObjectIDznd="272@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1192.59 868.23 L 1192.59 825.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv10" d="M 1193.32 800.73 L 1193.32 756.46" stroke-width="1" zvalue="879"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@1" LinkObjectIDznd="277@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1193.32 800.73 L 1193.32 756.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv10" d="M 1176.04 784.92 L 1193.32 784.92" stroke-width="1" zvalue="880"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@0" LinkObjectIDznd="270" MaxPinNum="2"/>
   </metadata>
  <path d="M 1176.04 784.92 L 1193.32 784.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="267">
   <path class="kv10" d="M 1176.9 839 L 1192.59 839" stroke-width="1" zvalue="883"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="271" MaxPinNum="2"/>
   </metadata>
  <path d="M 1176.9 839 L 1192.59 839" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="kv10" d="M 490.06 720.29 L 490.06 690.5" stroke-width="1" zvalue="884"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="389@1" LinkObjectIDznd="429@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 490.06 720.29 L 490.06 690.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="511">
   <path class="kv10" d="M 1714.81 714.29 L 1714.81 691.75" stroke-width="1" zvalue="891"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="517@1" LinkObjectIDznd="426@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1714.81 714.29 L 1714.81 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="502">
   <path class="kv10" d="M 1713.44 863.48 L 1713.44 820.1" stroke-width="1" zvalue="895"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="495@0" LinkObjectIDznd="506@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1713.44 863.48 L 1713.44 820.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="496">
   <path class="kv10" d="M 1714.36 795.73 L 1714.36 751.46" stroke-width="1" zvalue="897"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="506@1" LinkObjectIDznd="517@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1714.36 795.73 L 1714.36 751.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="492">
   <path class="kv10" d="M 1697.08 779.92 L 1714.36 779.92" stroke-width="1" zvalue="900"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="510@0" LinkObjectIDznd="496" MaxPinNum="2"/>
   </metadata>
  <path d="M 1697.08 779.92 L 1714.36 779.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="478">
   <path class="kv10" d="M 1697.94 834 L 1713.44 834" stroke-width="1" zvalue="903"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="489@0" LinkObjectIDznd="502" MaxPinNum="2"/>
   </metadata>
  <path d="M 1697.94 834 L 1713.44 834" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="471">
   <path class="kv10" d="M 1319.88 884.65 L 1319.88 831.1" stroke-width="1" zvalue="906"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="464@0" LinkObjectIDznd="477@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1319.88 884.65 L 1319.88 831.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="457">
   <path class="kv10" d="M 1319.86 806.73 L 1319.86 750.46" stroke-width="1" zvalue="910"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="477@1" LinkObjectIDznd="458@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1319.86 806.73 L 1319.86 750.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="446">
   <path class="kv10" d="M 1319.06 713.29 L 1319.06 691.75" stroke-width="1" zvalue="912"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="458@1" LinkObjectIDznd="426@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1319.06 713.29 L 1319.06 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="442">
   <path class="kv10" d="M 1333.92 765.17 L 1319.86 765.17" stroke-width="1" zvalue="915"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="445@0" LinkObjectIDznd="457" MaxPinNum="2"/>
   </metadata>
  <path d="M 1333.92 765.17 L 1319.86 765.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="436">
   <path class="kv10" d="M 1302.94 853.5 L 1319.88 853.5" stroke-width="1" zvalue="917"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="437@0" LinkObjectIDznd="471" MaxPinNum="2"/>
   </metadata>
  <path d="M 1302.94 853.5 L 1319.88 853.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="408">
   <path class="kv10" d="M 1347.54 853.5 L 1319.88 853.5" stroke-width="1" zvalue="920"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="418@0" LinkObjectIDznd="471" MaxPinNum="2"/>
   </metadata>
  <path d="M 1347.54 853.5 L 1319.88 853.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="402">
   <path class="kv10" d="M 1439.88 886.15 L 1439.88 832.6" stroke-width="1" zvalue="923"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="401@0" LinkObjectIDznd="404@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1439.88 886.15 L 1439.88 832.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="399">
   <path class="kv10" d="M 1439.86 808.23 L 1439.86 751.96" stroke-width="1" zvalue="927"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="404@1" LinkObjectIDznd="400@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1439.86 808.23 L 1439.86 751.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="398">
   <path class="kv10" d="M 1439.06 714.79 L 1439.06 691.75" stroke-width="1" zvalue="929"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="400@1" LinkObjectIDznd="426@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1439.06 714.79 L 1439.06 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="393">
   <path class="kv10" d="M 1453.92 766.67 L 1439.86 766.67" stroke-width="1" zvalue="932"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="394@0" LinkObjectIDznd="399" MaxPinNum="2"/>
   </metadata>
  <path d="M 1453.92 766.67 L 1439.86 766.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="378">
   <path class="kv10" d="M 1422.94 855 L 1439.88 855" stroke-width="1" zvalue="934"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="379@0" LinkObjectIDznd="402" MaxPinNum="2"/>
   </metadata>
  <path d="M 1422.94 855 L 1439.88 855" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="376">
   <path class="kv10" d="M 1467.54 855 L 1439.88 855" stroke-width="1" zvalue="937"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="377@0" LinkObjectIDznd="402" MaxPinNum="2"/>
   </metadata>
  <path d="M 1467.54 855 L 1439.88 855" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="370">
   <path class="kv10" d="M 1560.5 882.65 L 1560.5 829.1" stroke-width="1" zvalue="940"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="369@0" LinkObjectIDznd="372@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1560.5 882.65 L 1560.5 829.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="362">
   <path class="kv10" d="M 1560.48 804.73 L 1560.48 748.46" stroke-width="1" zvalue="944"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="372@1" LinkObjectIDznd="363@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1560.48 804.73 L 1560.48 748.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="361">
   <path class="kv10" d="M 1559.68 711.29 L 1559.68 691.75" stroke-width="1" zvalue="946"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="363@1" LinkObjectIDznd="426@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1559.68 711.29 L 1559.68 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="359">
   <path class="kv10" d="M 1574.54 763.17 L 1560.48 763.17" stroke-width="1" zvalue="949"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="360@0" LinkObjectIDznd="362" MaxPinNum="2"/>
   </metadata>
  <path d="M 1574.54 763.17 L 1560.48 763.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="357">
   <path class="kv10" d="M 1543.56 851.5 L 1560.5 851.5" stroke-width="1" zvalue="951"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="358@0" LinkObjectIDznd="370" MaxPinNum="2"/>
   </metadata>
  <path d="M 1543.56 851.5 L 1560.5 851.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="355">
   <path class="kv10" d="M 1588.16 851.5 L 1560.5 851.5" stroke-width="1" zvalue="954"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="356@0" LinkObjectIDznd="370" MaxPinNum="2"/>
   </metadata>
  <path d="M 1588.16 851.5 L 1560.5 851.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="353">
   <path class="kv10" d="M 1860.77 713.79 L 1860.77 691.75" stroke-width="1" zvalue="957"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="354@1" LinkObjectIDznd="426@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1860.77 713.79 L 1860.77 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="350">
   <path class="kv10" d="M 1859.59 863.98 L 1859.59 819.6" stroke-width="1" zvalue="961"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="528@0" LinkObjectIDznd="351@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1859.59 863.98 L 1859.59 819.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="349">
   <path class="kv10" d="M 1860.32 795.23 L 1860.32 750.96" stroke-width="1" zvalue="963"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="351@1" LinkObjectIDznd="354@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1860.32 795.23 L 1860.32 750.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="348">
   <path class="kv10" d="M 1843.04 779.42 L 1860.32 779.42" stroke-width="1" zvalue="964"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="352@0" LinkObjectIDznd="349" MaxPinNum="2"/>
   </metadata>
  <path d="M 1843.04 779.42 L 1860.32 779.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="346">
   <path class="kv10" d="M 1843.9 833.5 L 1859.59 833.5" stroke-width="1" zvalue="967"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="347@0" LinkObjectIDznd="350" MaxPinNum="2"/>
   </metadata>
  <path d="M 1843.9 833.5 L 1859.59 833.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="549">
   <g id="5490">
    <use class="kv35" height="30" transform="rotate(0,810.063,530.194) scale(2.58333,2.6) translate(-477.49,-302.274)" width="24" x="779.0599999999999" xlink:href="#PowerTransformer2:可调不带中性点_0" y="491.19" zvalue="80"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874573742082" ObjectName="35"/>
    </metadata>
   </g>
   <g id="5491">
    <use class="kv10" height="30" transform="rotate(0,810.063,530.194) scale(2.58333,2.6) translate(-477.49,-302.274)" width="24" x="779.0599999999999" xlink:href="#PowerTransformer2:可调不带中性点_1" y="491.19" zvalue="80"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874573807618" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399526383618" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399526383618"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,810.063,530.194) scale(2.58333,2.6) translate(-477.49,-302.274)" width="24" x="779.0599999999999" y="491.19"/></g>
  <g id="667">
   <g id="6670">
    <use class="kv35" height="30" transform="rotate(0,1433.33,530.194) scale(2.58333,2.6) translate(-859.495,-302.274)" width="24" x="1402.33" xlink:href="#PowerTransformer2:可调不带中性点_0" y="491.19" zvalue="146"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874573611010" ObjectName="35"/>
    </metadata>
   </g>
   <g id="6671">
    <use class="kv10" height="30" transform="rotate(0,1433.33,530.194) scale(2.58333,2.6) translate(-859.495,-302.274)" width="24" x="1402.33" xlink:href="#PowerTransformer2:可调不带中性点_1" y="491.19" zvalue="146"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874573676546" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399526318082" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399526318082"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1433.33,530.194) scale(2.58333,2.6) translate(-859.495,-302.274)" width="24" x="1402.33" y="491.19"/></g>
 </g>
 <g id="BreakerClass">
  <g id="576">
   <use class="kv10" height="20" transform="rotate(270,1236.56,605.222) scale(2.72698,2.5322) translate(-774.472,-350.89)" width="10" x="1222.925170068027" xlink:href="#Breaker:母联小车开关_0" y="579.9002267573696" zvalue="100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925121146883" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473925121146883"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1236.56,605.222) scale(2.72698,2.5322) translate(-774.472,-350.89)" width="10" x="1222.925170068027" y="579.9002267573696"/></g>
  <g id="630">
   <use class="kv35" height="20" transform="rotate(0,810.164,454.788) scale(1.5673,1.42482) translate(-290.41,-131.35)" width="10" x="802.3270073427254" xlink:href="#Breaker:开关_0" y="440.5402636805579" zvalue="129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925121081347" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925121081347"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,810.164,454.788) scale(1.5673,1.42482) translate(-290.41,-131.35)" width="10" x="802.3270073427254" y="440.5402636805579"/></g>
  <g id="666">
   <use class="kv35" height="20" transform="rotate(0,1433.91,454.331) scale(1.61761,1.47055) translate(-544.385,-140.673)" width="10" x="1425.823903622073" xlink:href="#Breaker:开关_0" y="439.6255296171045" zvalue="148"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925121015811" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925121015811"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1433.91,454.331) scale(1.61761,1.47055) translate(-544.385,-140.673)" width="10" x="1425.823903622073" y="439.6255296171045"/></g>
  <g id="389">
   <use class="kv10" height="20" transform="rotate(0,490.063,738.62) scale(2.24069,-2.03699) translate(-265.149,-1090.85)" width="10" x="478.8598412698415" xlink:href="#Breaker:小车断路器_0" y="718.25" zvalue="441"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925121212419" ObjectName="10kV南京里线081断路器"/>
   <cge:TPSR_Ref TObjectID="6473925121212419"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,490.063,738.62) scale(2.24069,-2.03699) translate(-265.149,-1090.85)" width="10" x="478.8598412698415" y="718.25"/></g>
  <g id="403">
   <use class="kv10" height="20" transform="rotate(0,810.063,643.62) scale(2.24069,-2.03699) translate(-442.336,-949.215)" width="10" x="798.8598412698416" xlink:href="#Breaker:小车断路器_0" y="623.25" zvalue="455"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925121277955" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925121277955"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,810.063,643.62) scale(2.24069,-2.03699) translate(-442.336,-949.215)" width="10" x="798.8598412698416" y="623.25"/></g>
  <g id="409">
   <use class="kv10" height="20" transform="rotate(0,1433.81,647.12) scale(2.24069,-2.03699) translate(-787.713,-954.434)" width="10" x="1422.609841269842" xlink:href="#Breaker:小车断路器_0" y="626.75" zvalue="459"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925121343491" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473925121343491"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1433.81,647.12) scale(2.24069,-2.03699) translate(-787.713,-954.434)" width="10" x="1422.609841269842" y="626.75"/></g>
  <g id="444">
   <use class="kv35" height="20" transform="rotate(0,1037.72,257.898) scale(1.55118,1.41016) translate(-365.975,-70.9113)" width="10" x="1029.95987833038" xlink:href="#Breaker:开关_0" y="243.7967208894656" zvalue="491"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925121409027" ObjectName="35kV汉秀线381断路器"/>
   <cge:TPSR_Ref TObjectID="6473925121409027"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1037.72,257.898) scale(1.55118,1.41016) translate(-365.975,-70.9113)" width="10" x="1029.95987833038" y="243.7967208894656"/></g>
  <g id="63">
   <use class="kv10" height="20" transform="rotate(0,1047.81,738.12) scale(2.24069,-2.03699) translate(-573.981,-1090.11)" width="10" x="1036.609841269842" xlink:href="#Breaker:小车断路器_0" y="717.75" zvalue="645"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925121474563" ObjectName="10kV1号电容器085断路器"/>
   <cge:TPSR_Ref TObjectID="6473925121474563"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1047.81,738.12) scale(2.24069,-2.03699) translate(-573.981,-1090.11)" width="10" x="1036.609841269842" y="717.75"/></g>
  <g id="185">
   <use class="kv35" height="20" transform="rotate(0,1256.72,254.773) scale(1.55118,1.41016) translate(-443.792,-70.0024)" width="10" x="1248.95987833038" xlink:href="#Breaker:开关_0" y="240.6717208894656" zvalue="769"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925121540099" ObjectName="35kV丽秀线382断路器"/>
   <cge:TPSR_Ref TObjectID="6473925121540099"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1256.72,254.773) scale(1.55118,1.41016) translate(-443.792,-70.0024)" width="10" x="1248.95987833038" y="240.6717208894656"/></g>
  <g id="215">
   <use class="kv10" height="20" transform="rotate(0,652.063,737.12) scale(2.24069,-2.03699) translate(-354.85,-1088.62)" width="10" x="640.8598412698416" xlink:href="#Breaker:小车断路器_0" y="716.75" zvalue="821"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925121605635" ObjectName="10kV小街线082断路器"/>
   <cge:TPSR_Ref TObjectID="6473925121605635"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,652.063,737.12) scale(2.24069,-2.03699) translate(-354.85,-1088.62)" width="10" x="640.8598412698416" y="716.75"/></g>
  <g id="235">
   <use class="kv10" height="20" transform="rotate(0,772.063,738.62) scale(2.24069,-2.03699) translate(-421.295,-1090.85)" width="10" x="760.8598412698416" xlink:href="#Breaker:小车断路器_0" y="718.25" zvalue="840"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925121671171" ObjectName="10kV备用一回线083断路器"/>
   <cge:TPSR_Ref TObjectID="6473925121671171"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,772.063,738.62) scale(2.24069,-2.03699) translate(-421.295,-1090.85)" width="10" x="760.8598412698416" y="718.25"/></g>
  <g id="258">
   <use class="kv10" height="20" transform="rotate(0,892.682,735.12) scale(2.24069,-2.03699) translate(-488.083,-1085.63)" width="10" x="881.4784926692562" xlink:href="#Breaker:小车断路器_0" y="714.75" zvalue="858"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925121736707" ObjectName="10kV备用二回线084断路器"/>
   <cge:TPSR_Ref TObjectID="6473925121736707"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,892.682,735.12) scale(2.24069,-2.03699) translate(-488.083,-1085.63)" width="10" x="881.4784926692562" y="714.75"/></g>
  <g id="277">
   <use class="kv10" height="20" transform="rotate(0,1193.77,737.62) scale(2.24069,-2.03699) translate(-654.8,-1089.36)" width="10" x="1182.568809682755" xlink:href="#Breaker:小车断路器_0" y="717.25" zvalue="871"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925121802243" ObjectName="10kV2号电容器086断路器"/>
   <cge:TPSR_Ref TObjectID="6473925121802243"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1193.77,737.62) scale(2.24069,-2.03699) translate(-654.8,-1089.36)" width="10" x="1182.568809682755" y="717.25"/></g>
  <g id="517">
   <use class="kv10" height="20" transform="rotate(0,1714.81,732.62) scale(2.24069,-2.03699) translate(-943.305,-1081.91)" width="10" x="1703.609841269842" xlink:href="#Breaker:小车断路器_0" y="712.25" zvalue="889"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925122129923" ObjectName="10kV3号电容器091断路器"/>
   <cge:TPSR_Ref TObjectID="6473925122129923"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1714.81,732.62) scale(2.24069,-2.03699) translate(-943.305,-1081.91)" width="10" x="1703.609841269842" y="712.25"/></g>
  <g id="458">
   <use class="kv10" height="20" transform="rotate(0,1319.06,731.62) scale(2.24069,-2.03699) translate(-724.175,-1080.42)" width="10" x="1307.859841269842" xlink:href="#Breaker:小车断路器_0" y="711.25" zvalue="909"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925122064387" ObjectName="10kV勐平线087断路器"/>
   <cge:TPSR_Ref TObjectID="6473925122064387"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1319.06,731.62) scale(2.24069,-2.03699) translate(-724.175,-1080.42)" width="10" x="1307.859841269842" y="711.25"/></g>
  <g id="400">
   <use class="kv10" height="20" transform="rotate(0,1439.06,733.12) scale(2.24069,-2.03699) translate(-790.62,-1082.65)" width="10" x="1427.859841269842" xlink:href="#Breaker:小车断路器_0" y="712.75" zvalue="926"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925121998851" ObjectName="10kV备用Ⅳ回线088断路器"/>
   <cge:TPSR_Ref TObjectID="6473925121998851"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1439.06,733.12) scale(2.24069,-2.03699) translate(-790.62,-1082.65)" width="10" x="1427.859841269842" y="712.75"/></g>
  <g id="363">
   <use class="kv10" height="20" transform="rotate(0,1559.68,729.62) scale(2.24069,-2.03699) translate(-857.408,-1077.43)" width="10" x="1548.478492669256" xlink:href="#Breaker:小车断路器_0" y="709.25" zvalue="943"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925121933315" ObjectName="10kV备用Ⅴ回线089断路器"/>
   <cge:TPSR_Ref TObjectID="6473925121933315"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1559.68,729.62) scale(2.24069,-2.03699) translate(-857.408,-1077.43)" width="10" x="1548.478492669256" y="709.25"/></g>
  <g id="354">
   <use class="kv10" height="20" transform="rotate(0,1860.77,732.12) scale(2.24069,-2.03699) translate(-1024.12,-1081.16)" width="10" x="1849.568809682755" xlink:href="#Breaker:小车断路器_0" y="711.75" zvalue="955"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925121867779" ObjectName="10kV4号电容器092断路器"/>
   <cge:TPSR_Ref TObjectID="6473925121867779"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1860.77,732.12) scale(2.24069,-2.03699) translate(-1024.12,-1081.16)" width="10" x="1849.568809682755" y="711.75"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="153">
   <use class="kv10" height="30" transform="rotate(0,490.881,911) scale(1.79167,-1.43333) translate(-212.151,-1540.08)" width="12" x="480.1313486005853" xlink:href="#EnergyConsumer:负荷_0" y="889.5" zvalue="293"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453999394819" ObjectName="10kV南京里线"/>
   <cge:TPSR_Ref TObjectID="6192453999394819"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,490.881,911) scale(1.79167,-1.43333) translate(-212.151,-1540.08)" width="12" x="480.1313486005853" y="889.5"/></g>
  <g id="188">
   <use class="kv35" height="38" transform="rotate(0,679.198,442.469) scale(1.875,1.60362) translate(-305.584,-155.081)" width="26" x="654.8229166666667" xlink:href="#EnergyConsumer:站用变1节点_0" y="412" zvalue="797"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454002933763" ObjectName="35kV#1站用变"/>
   <cge:TPSR_Ref TObjectID="6192454002933763"/></metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,679.198,442.469) scale(1.875,1.60362) translate(-305.584,-155.081)" width="26" x="654.8229166666667" y="412"/></g>
  <g id="202">
   <use class="kv10" height="30" transform="rotate(0,555.229,937.055) scale(2.4375,2.03125) translate(-313.068,-460.267)" width="20" x="530.8541666666667" xlink:href="#EnergyConsumer:站用变无融断_0" y="906.5859375" zvalue="810"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454003261443" ObjectName="10kV#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,555.229,937.055) scale(2.4375,2.03125) translate(-313.068,-460.267)" width="20" x="530.8541666666667" y="906.5859375"/></g>
  <g id="216">
   <use class="kv10" height="30" transform="rotate(0,652.881,909.5) scale(1.79167,-1.43333) translate(-283.732,-1537.53)" width="12" x="642.1313486005853" xlink:href="#EnergyConsumer:负荷_0" y="888" zvalue="820"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454003654659" ObjectName="10kV小街线"/>
   <cge:TPSR_Ref TObjectID="6192454003654659"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,652.881,909.5) scale(1.79167,-1.43333) translate(-283.732,-1537.53)" width="12" x="642.1313486005853" y="888"/></g>
  <g id="236">
   <use class="kv10" height="30" transform="rotate(0,772.881,911) scale(1.79167,-1.43333) translate(-336.756,-1540.08)" width="12" x="762.1313486005853" xlink:href="#EnergyConsumer:负荷_0" y="889.5" zvalue="838"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454004178947" ObjectName="10kV备用一回线"/>
   <cge:TPSR_Ref TObjectID="6192454004178947"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,772.881,911) scale(1.79167,-1.43333) translate(-336.756,-1540.08)" width="12" x="762.1313486005853" y="889.5"/></g>
  <g id="259">
   <use class="kv10" height="30" transform="rotate(0,893.5,907.5) scale(1.79167,-1.43333) translate(-390.052,-1534.14)" width="12" x="882.75" xlink:href="#EnergyConsumer:负荷_0" y="886" zvalue="856"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454004637699" ObjectName="10kV备用二回线"/>
   <cge:TPSR_Ref TObjectID="6192454004637699"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,893.5,907.5) scale(1.79167,-1.43333) translate(-390.052,-1534.14)" width="12" x="882.75" y="886"/></g>
  <g id="464">
   <use class="kv10" height="30" transform="rotate(0,1319.88,904) scale(1.79167,-1.43333) translate(-578.453,-1528.2)" width="12" x="1309.131348600585" xlink:href="#EnergyConsumer:负荷_0" y="882.5" zvalue="907"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454006669315" ObjectName="10kV勐平线"/>
   <cge:TPSR_Ref TObjectID="6192454006669315"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1319.88,904) scale(1.79167,-1.43333) translate(-578.453,-1528.2)" width="12" x="1309.131348600585" y="882.5"/></g>
  <g id="401">
   <use class="kv10" height="30" transform="rotate(0,1439.88,905.5) scale(1.79167,-1.43333) translate(-631.477,-1530.74)" width="12" x="1429.131348600585" xlink:href="#EnergyConsumer:负荷_0" y="884" zvalue="924"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454006210563" ObjectName="10kV备用Ⅳ回线"/>
   <cge:TPSR_Ref TObjectID="6192454006210563"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1439.88,905.5) scale(1.79167,-1.43333) translate(-631.477,-1530.74)" width="12" x="1429.131348600585" y="884"/></g>
  <g id="369">
   <use class="kv10" height="30" transform="rotate(0,1560.5,902) scale(1.79167,-1.43333) translate(-684.773,-1524.8)" width="12" x="1549.75" xlink:href="#EnergyConsumer:负荷_0" y="880.5" zvalue="941"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454005751811" ObjectName="10kV备用Ⅴ回线"/>
   <cge:TPSR_Ref TObjectID="6192454005751811"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1560.5,902) scale(1.79167,-1.43333) translate(-684.773,-1524.8)" width="12" x="1549.75" y="880.5"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="375">
   <use class="kv10" height="30" transform="rotate(0,1192.59,895.75) scale(1.52083,2.16667) translate(-400.607,-464.827)" width="30" x="1169.773223600585" xlink:href="#Compensator:电容器组2_0" y="863.25" zvalue="435"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453999788035" ObjectName="10kV2号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453999788035"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1192.59,895.75) scale(1.52083,2.16667) translate(-400.607,-464.827)" width="30" x="1169.773223600585" y="863.25"/></g>
  <g id="55">
   <use class="kv10" height="30" transform="rotate(0,1046.44,895.25) scale(1.52083,2.16667) translate(-350.557,-464.558)" width="30" x="1023.625" xlink:href="#Compensator:电容器组2_0" y="862.75" zvalue="657"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454001557507" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192454001557507"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1046.44,895.25) scale(1.52083,2.16667) translate(-350.557,-464.558)" width="30" x="1023.625" y="862.75"/></g>
  <g id="528">
   <use class="kv10" height="30" transform="rotate(0,1859.59,891.5) scale(1.52083,2.16667) translate(-629.032,-462.538)" width="30" x="1836.773223600585" xlink:href="#Compensator:电容器组2_0" y="859" zvalue="887"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454007193603" ObjectName="10kV4号电容器"/>
   <cge:TPSR_Ref TObjectID="6192454007193603"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1859.59,891.5) scale(1.52083,2.16667) translate(-629.032,-462.538)" width="30" x="1836.773223600585" y="859"/></g>
  <g id="495">
   <use class="kv10" height="30" transform="rotate(0,1713.44,891) scale(1.52083,2.16667) translate(-578.981,-462.269)" width="30" x="1690.625" xlink:href="#Compensator:电容器组2_0" y="858.5" zvalue="898"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454006931459" ObjectName="10kV3号电容器"/>
   <cge:TPSR_Ref TObjectID="6192454006931459"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1713.44,891) scale(1.52083,2.16667) translate(-578.981,-462.269)" width="30" x="1690.625" y="858.5"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="396">
   <use class="kv10" height="20" transform="rotate(270,515.75,772.222) scale(1.11111,1.11111) translate(-51.0194,-76.1111)" width="10" x="510.1944444444445" xlink:href="#GroundDisconnector:地刀_0" y="761.1111111111111" zvalue="448"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453999919107" ObjectName="10kV南京里线08160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453999919107"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,515.75,772.222) scale(1.11111,1.11111) translate(-51.0194,-76.1111)" width="10" x="510.1944444444445" y="761.1111111111111"/></g>
  <g id="417">
   <use class="kv35" height="20" transform="rotate(90,784,316.972) scale(-1.11111,1.11111) translate(-1489.04,-30.5861)" width="10" x="778.4444444444445" xlink:href="#GroundDisconnector:地刀_0" y="305.8611111111111" zvalue="466"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454000115715" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454000115715"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,784,316.972) scale(-1.11111,1.11111) translate(-1489.04,-30.5861)" width="10" x="778.4444444444445" y="305.8611111111111"/></g>
  <g id="422">
   <use class="kv35" height="20" transform="rotate(90,784,261.972) scale(-1.11111,1.11111) translate(-1489.04,-25.0861)" width="10" x="778.4444444444445" xlink:href="#GroundDisconnector:地刀_0" y="250.8611111111111" zvalue="470"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454000246787" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454000246787"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,784,261.972) scale(-1.11111,1.11111) translate(-1489.04,-25.0861)" width="10" x="778.4444444444445" y="250.8611111111111"/></g>
  <g id="424">
   <use class="kv35" height="20" transform="rotate(90,781.5,419.472) scale(-1.11111,1.11111) translate(-1484.29,-40.8361)" width="10" x="775.9444444444445" xlink:href="#GroundDisconnector:地刀_0" y="408.3611111111111" zvalue="474"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454000377859" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454000377859"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,781.5,419.472) scale(-1.11111,1.11111) translate(-1484.29,-40.8361)" width="10" x="775.9444444444445" y="408.3611111111111"/></g>
  <g id="430">
   <use class="kv35" height="20" transform="rotate(90,1404,413.613) scale(-1.11111,1.11111) translate(-2667.04,-40.2502)" width="10" x="1398.444444444444" xlink:href="#GroundDisconnector:地刀_0" y="402.5018867479586" zvalue="477"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454000508931" ObjectName="#2主变35kV侧30217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454000508931"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1404,413.613) scale(-1.11111,1.11111) translate(-2667.04,-40.2502)" width="10" x="1398.444444444444" y="402.5018867479586"/></g>
  <g id="460">
   <use class="kv35" height="20" transform="rotate(270,1064.11,287.722) scale(1.11111,1.11111) translate(-105.856,-27.6611)" width="10" x="1058.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="276.6111111111111" zvalue="503"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454000967683" ObjectName="35kV汉秀线38117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454000967683"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1064.11,287.722) scale(1.11111,1.11111) translate(-105.856,-27.6611)" width="10" x="1058.555555555556" y="276.6111111111111"/></g>
  <g id="462">
   <use class="kv35" height="20" transform="rotate(270,1064.11,237.722) scale(1.11111,1.11111) translate(-105.856,-22.6611)" width="10" x="1058.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="226.6111111111111" zvalue="507"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454001098755" ObjectName="35kV汉秀线38160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454001098755"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1064.11,237.722) scale(1.11111,1.11111) translate(-105.856,-22.6611)" width="10" x="1058.555555555556" y="226.6111111111111"/></g>
  <g id="466">
   <use class="kv35" height="20" transform="rotate(270,1065.36,186.722) scale(1.11111,1.11111) translate(-105.981,-17.5611)" width="10" x="1059.805555555556" xlink:href="#GroundDisconnector:地刀_0" y="175.6111111111111" zvalue="511"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454001229827" ObjectName="35kV汉秀线38167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454001229827"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1065.36,186.722) scale(1.11111,1.11111) translate(-105.981,-17.5611)" width="10" x="1059.805555555556" y="175.6111111111111"/></g>
  <g id="59">
   <use class="kv10" height="20" transform="rotate(90,1019.25,785.472) scale(-1.11111,1.11111) translate(-1936.02,-77.4361)" width="10" x="1013.694444444444" xlink:href="#GroundDisconnector:地刀_0" y="774.3611111111111" zvalue="651"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454001754115" ObjectName="10kV1号电容器08560接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454001754115"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1019.25,785.472) scale(-1.11111,1.11111) translate(-1936.02,-77.4361)" width="10" x="1013.694444444444" y="774.3611111111111"/></g>
  <g id="69">
   <use class="kv10" height="20" transform="rotate(90,1020.11,839.556) scale(-1.11111,1.11111) translate(-1937.66,-82.8444)" width="10" x="1014.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="828.4444444444443" zvalue="669"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454001885187" ObjectName="10kV1号电容器08567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454001885187"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1020.11,839.556) scale(-1.11111,1.11111) translate(-1937.66,-82.8444)" width="10" x="1014.555555555556" y="828.4444444444443"/></g>
  <g id="178">
   <use class="kv35" height="20" transform="rotate(270,1283.11,284.597) scale(1.11111,1.11111) translate(-127.756,-27.3486)" width="10" x="1277.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="273.4861111111111" zvalue="779"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454002671619" ObjectName="35kV丽秀线38217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454002671619"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1283.11,284.597) scale(1.11111,1.11111) translate(-127.756,-27.3486)" width="10" x="1277.555555555556" y="273.4861111111111"/></g>
  <g id="176">
   <use class="kv35" height="20" transform="rotate(270,1283.11,234.597) scale(1.11111,1.11111) translate(-127.756,-22.3486)" width="10" x="1277.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="223.4861111111111" zvalue="782"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454002540547" ObjectName="35kV丽秀线38260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454002540547"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1283.11,234.597) scale(1.11111,1.11111) translate(-127.756,-22.3486)" width="10" x="1277.555555555556" y="223.4861111111111"/></g>
  <g id="174">
   <use class="kv35" height="20" transform="rotate(270,1284.36,183.597) scale(1.11111,1.11111) translate(-127.881,-17.2486)" width="10" x="1278.805555555556" xlink:href="#GroundDisconnector:地刀_0" y="172.4861111111111" zvalue="785"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454002409475" ObjectName="35kV丽秀线38267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454002409475"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1284.36,183.597) scale(1.11111,1.11111) translate(-127.881,-17.2486)" width="10" x="1278.805555555556" y="172.4861111111111"/></g>
  <g id="196">
   <use class="kv10" height="20" transform="rotate(90,463.111,860.556) scale(-1.11111,1.11111) translate(-879.356,-84.9444)" width="10" x="457.5555555555555" xlink:href="#GroundDisconnector:地刀_0" y="849.4444444444443" zvalue="805"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454003130371" ObjectName="10kV南京里线08167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454003130371"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,463.111,860.556) scale(-1.11111,1.11111) translate(-879.356,-84.9444)" width="10" x="457.5555555555555" y="849.4444444444443"/></g>
  <g id="212">
   <use class="kv10" height="20" transform="rotate(270,677.75,770.722) scale(1.11111,1.11111) translate(-67.2194,-75.9611)" width="10" x="672.1944444444445" xlink:href="#GroundDisconnector:地刀_0" y="759.6111111111111" zvalue="825"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454003589123" ObjectName="10kV小街线08260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454003589123"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,677.75,770.722) scale(1.11111,1.11111) translate(-67.2194,-75.9611)" width="10" x="672.1944444444445" y="759.6111111111111"/></g>
  <g id="210">
   <use class="kv10" height="20" transform="rotate(90,625.111,859.056) scale(-1.11111,1.11111) translate(-1187.16,-84.7944)" width="10" x="619.5555555555555" xlink:href="#GroundDisconnector:地刀_0" y="847.9444444444443" zvalue="828"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454003458051" ObjectName="10kV小街线08267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454003458051"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,625.111,859.056) scale(-1.11111,1.11111) translate(-1187.16,-84.7944)" width="10" x="619.5555555555555" y="847.9444444444443"/></g>
  <g id="232">
   <use class="kv10" height="20" transform="rotate(270,797.75,772.222) scale(1.11111,1.11111) translate(-79.2194,-76.1111)" width="10" x="792.1944444444445" xlink:href="#GroundDisconnector:地刀_0" y="761.1111111111111" zvalue="844"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454004113411" ObjectName="10kV备用一回线08360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454004113411"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,797.75,772.222) scale(1.11111,1.11111) translate(-79.2194,-76.1111)" width="10" x="792.1944444444445" y="761.1111111111111"/></g>
  <g id="230">
   <use class="kv10" height="20" transform="rotate(90,745.111,860.556) scale(-1.11111,1.11111) translate(-1415.16,-84.9444)" width="10" x="739.5555555555555" xlink:href="#GroundDisconnector:地刀_0" y="849.4444444444443" zvalue="847"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454003982339" ObjectName="10kV备用一回线08367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454003982339"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,745.111,860.556) scale(-1.11111,1.11111) translate(-1415.16,-84.9444)" width="10" x="739.5555555555555" y="849.4444444444443"/></g>
  <g id="255">
   <use class="kv10" height="20" transform="rotate(270,918.369,768.722) scale(1.11111,1.11111) translate(-91.2813,-75.7611)" width="10" x="912.8130958438592" xlink:href="#GroundDisconnector:地刀_0" y="757.6111111111111" zvalue="862"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454004572163" ObjectName="10kV备用二回线08460接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454004572163"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,918.369,768.722) scale(1.11111,1.11111) translate(-91.2813,-75.7611)" width="10" x="912.8130958438592" y="757.6111111111111"/></g>
  <g id="253">
   <use class="kv10" height="20" transform="rotate(90,865.73,857.056) scale(-1.11111,1.11111) translate(-1644.33,-84.5944)" width="10" x="860.1742069549703" xlink:href="#GroundDisconnector:地刀_0" y="845.9444444444443" zvalue="865"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454004441091" ObjectName="10kV备用二回线08467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454004441091"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,865.73,857.056) scale(-1.11111,1.11111) translate(-1644.33,-84.5944)" width="10" x="860.1742069549703" y="845.9444444444443"/></g>
  <g id="273">
   <use class="kv10" height="20" transform="rotate(90,1165.21,784.972) scale(-1.11111,1.11111) translate(-2213.34,-77.3861)" width="10" x="1159.653412857358" xlink:href="#GroundDisconnector:地刀_0" y="773.8611111111111" zvalue="874"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454005030915" ObjectName="10kV2号电容器08660接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454005030915"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1165.21,784.972) scale(-1.11111,1.11111) translate(-2213.34,-77.3861)" width="10" x="1159.653412857358" y="773.8611111111111"/></g>
  <g id="268">
   <use class="kv10" height="20" transform="rotate(90,1166.07,839.056) scale(-1.11111,1.11111) translate(-2214.98,-82.7944)" width="10" x="1160.514523968469" xlink:href="#GroundDisconnector:地刀_0" y="827.9444444444443" zvalue="881"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454004834307" ObjectName="10kV2号电容器08667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454004834307"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1166.07,839.056) scale(-1.11111,1.11111) translate(-2214.98,-82.7944)" width="10" x="1160.514523968469" y="827.9444444444443"/></g>
  <g id="510">
   <use class="kv10" height="20" transform="rotate(90,1686.25,779.972) scale(-1.11111,1.11111) translate(-3203.32,-76.8861)" width="10" x="1680.694444444444" xlink:href="#GroundDisconnector:地刀_0" y="768.8611111111111" zvalue="892"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454007128067" ObjectName="10kV3号电容器09160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454007128067"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1686.25,779.972) scale(-1.11111,1.11111) translate(-3203.32,-76.8861)" width="10" x="1680.694444444444" y="768.8611111111111"/></g>
  <g id="489">
   <use class="kv10" height="20" transform="rotate(90,1687.11,834.056) scale(-1.11111,1.11111) translate(-3204.96,-82.2944)" width="10" x="1681.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="822.9444444444443" zvalue="901"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454006865923" ObjectName="10kV3号电容器09167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454006865923"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1687.11,834.056) scale(-1.11111,1.11111) translate(-3204.96,-82.2944)" width="10" x="1681.555555555556" y="822.9444444444443"/></g>
  <g id="445">
   <use class="kv10" height="20" transform="rotate(270,1344.75,765.222) scale(1.11111,1.11111) translate(-133.919,-75.4111)" width="10" x="1339.194444444444" xlink:href="#GroundDisconnector:地刀_0" y="754.1111111111111" zvalue="913"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454006603779" ObjectName="10kV勐平线08760接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454006603779"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1344.75,765.222) scale(1.11111,1.11111) translate(-133.919,-75.4111)" width="10" x="1339.194444444444" y="754.1111111111111"/></g>
  <g id="437">
   <use class="kv10" height="20" transform="rotate(90,1292.11,853.556) scale(-1.11111,1.11111) translate(-2454.46,-84.2444)" width="10" x="1286.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="842.4444444444443" zvalue="916"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454006472707" ObjectName="10kV勐平线08767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454006472707"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1292.11,853.556) scale(-1.11111,1.11111) translate(-2454.46,-84.2444)" width="10" x="1286.555555555556" y="842.4444444444443"/></g>
  <g id="394">
   <use class="kv10" height="20" transform="rotate(270,1464.75,766.722) scale(1.11111,1.11111) translate(-145.919,-75.5611)" width="10" x="1459.194444444444" xlink:href="#GroundDisconnector:地刀_0" y="755.6111111111111" zvalue="930"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454006145027" ObjectName="10kV备用Ⅳ回线08860接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454006145027"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1464.75,766.722) scale(1.11111,1.11111) translate(-145.919,-75.5611)" width="10" x="1459.194444444444" y="755.6111111111111"/></g>
  <g id="379">
   <use class="kv10" height="20" transform="rotate(90,1412.11,855.056) scale(-1.11111,1.11111) translate(-2682.46,-84.3944)" width="10" x="1406.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="843.9444444444443" zvalue="933"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454006013955" ObjectName="10kV备用Ⅳ回线08867接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454006013955"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1412.11,855.056) scale(-1.11111,1.11111) translate(-2682.46,-84.3944)" width="10" x="1406.555555555556" y="843.9444444444443"/></g>
  <g id="360">
   <use class="kv10" height="20" transform="rotate(270,1585.37,763.222) scale(1.11111,1.11111) translate(-157.981,-75.2111)" width="10" x="1579.813095843859" xlink:href="#GroundDisconnector:地刀_0" y="752.1111111111111" zvalue="947"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454005686275" ObjectName="10kV备用Ⅴ回线08960接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454005686275"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1585.37,763.222) scale(1.11111,1.11111) translate(-157.981,-75.2111)" width="10" x="1579.813095843859" y="752.1111111111111"/></g>
  <g id="358">
   <use class="kv10" height="20" transform="rotate(90,1532.73,851.556) scale(-1.11111,1.11111) translate(-2911.63,-84.0444)" width="10" x="1527.17420695497" xlink:href="#GroundDisconnector:地刀_0" y="840.4444444444443" zvalue="950"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454005555203" ObjectName="10kV备用Ⅴ回线08967接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454005555203"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1532.73,851.556) scale(-1.11111,1.11111) translate(-2911.63,-84.0444)" width="10" x="1527.17420695497" y="840.4444444444443"/></g>
  <g id="352">
   <use class="kv10" height="20" transform="rotate(90,1832.21,779.472) scale(-1.11111,1.11111) translate(-3480.64,-76.8361)" width="10" x="1826.653412857358" xlink:href="#GroundDisconnector:地刀_0" y="768.3611111111111" zvalue="958"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454005358595" ObjectName="10kV4号电容器09260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454005358595"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1832.21,779.472) scale(-1.11111,1.11111) translate(-3480.64,-76.8361)" width="10" x="1826.653412857358" y="768.3611111111111"/></g>
  <g id="347">
   <use class="kv10" height="20" transform="rotate(90,1833.07,833.556) scale(-1.11111,1.11111) translate(-3482.28,-82.2444)" width="10" x="1827.514523968469" xlink:href="#GroundDisconnector:地刀_0" y="822.4444444444443" zvalue="965"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454005161987" ObjectName="10kV4号电容器09267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454005161987"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1833.07,833.556) scale(-1.11111,1.11111) translate(-3482.28,-82.2444)" width="10" x="1827.514523968469" y="822.4444444444443"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="413">
   <use class="kv35" height="42" transform="rotate(0,824.201,224.375) scale(1.51786,1.51786) translate(-273.43,-65.6765)" width="30" x="801.433470691534" xlink:href="#Accessory:4卷PT带容断器_0" y="192.5" zvalue="462"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453999984643" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,824.201,224.375) scale(1.51786,1.51786) translate(-273.43,-65.6765)" width="30" x="801.433470691534" y="192.5"/></g>
  <g id="469">
   <use class="kv35" height="30" transform="rotate(90,1122.84,159) scale(1.25,-1.41046) translate(-220.819,-265.572)" width="30" x="1104.093125631518" xlink:href="#Accessory:PT象达_0" y="137.8431256315183" zvalue="513"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454002016259" ObjectName="35kV汉秀线线路PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1122.84,159) scale(1.25,-1.41046) translate(-220.819,-265.572)" width="30" x="1104.093125631518" y="137.8431256315183"/></g>
  <g id="475">
   <use class="kv10" height="20" transform="rotate(0,595.063,591) scale(1.22857,-1.9) translate(-107.852,-893.053)" width="25" x="579.7061685901592" xlink:href="#Accessory:4绕组母线PT_0" y="572" zvalue="520"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454001295363" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,595.063,591) scale(1.22857,-1.9) translate(-107.852,-893.053)" width="25" x="579.7061685901592" y="572"/></g>
  <g id="488">
   <use class="kv10" height="20" transform="rotate(0,1665.06,592) scale(1.02857,-1.8) translate(-45.8946,-912.889)" width="25" x="1652.206168590159" xlink:href="#Accessory:4绕组母线PT_0" y="574" zvalue="528"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454001491971" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1665.06,592) scale(1.02857,-1.8) translate(-45.8946,-912.889)" width="25" x="1652.206168590159" y="574"/></g>
  <g id="154">
   <use class="kv35" height="26" transform="rotate(90,1000.25,159.686) scale(0.9375,1.25) translate(66.3083,-28.6873)" width="12" x="994.625" xlink:href="#Accessory:避雷器1_0" y="143.4363870200881" zvalue="763"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454002081795" ObjectName="35kV汉秀线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1000.25,159.686) scale(0.9375,1.25) translate(66.3083,-28.6873)" width="12" x="994.625" y="143.4363870200881"/></g>
  <g id="172">
   <use class="kv35" height="30" transform="rotate(90,1341.84,155.875) scale(1.25,-1.41046) translate(-264.619,-260.232)" width="30" x="1323.093125631518" xlink:href="#Accessory:PT象达_0" y="134.7181256315183" zvalue="788"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454002278403" ObjectName="35kV丽秀线线路PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1341.84,155.875) scale(1.25,-1.41046) translate(-264.619,-260.232)" width="30" x="1323.093125631518" y="134.7181256315183"/></g>
  <g id="169">
   <use class="kv35" height="26" transform="rotate(90,1219.25,156.561) scale(0.9375,1.25) translate(80.9083,-28.0623)" width="12" x="1213.625" xlink:href="#Accessory:避雷器1_0" y="140.3113870200881" zvalue="792"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454002147331" ObjectName="35kV丽秀线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1219.25,156.561) scale(0.9375,1.25) translate(80.9083,-28.0623)" width="12" x="1213.625" y="140.3113870200881"/></g>
  <g id="200">
   <use class="kv10" height="26" transform="rotate(0,529.125,925.375) scale(-0.9375,1.25) translate(-1093.9,-181.825)" width="12" x="523.5" xlink:href="#Accessory:避雷器1_0" y="909.125" zvalue="813"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454003195907" ObjectName="10kV#2站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,529.125,925.375) scale(-0.9375,1.25) translate(-1093.9,-181.825)" width="12" x="523.5" y="909.125"/></g>
  <g id="219">
   <use class="kv10" height="26" transform="rotate(270,696,858.967) scale(-1,1.25) translate(-1392,-168.543)" width="12" x="690" xlink:href="#Accessory:避雷器1_0" y="842.7171666874191" zvalue="832"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454003785731" ObjectName="10kV小街线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,696,858.967) scale(-1,1.25) translate(-1392,-168.543)" width="12" x="690" y="842.7171666874191"/></g>
  <g id="228">
   <use class="kv10" height="26" transform="rotate(270,816,860.467) scale(-1,1.25) translate(-1632,-168.843)" width="12" x="810" xlink:href="#Accessory:避雷器1_0" y="844.2171666874191" zvalue="850"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454003851267" ObjectName="10kV备用一回线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,816,860.467) scale(-1,1.25) translate(-1632,-168.843)" width="12" x="810" y="844.2171666874191"/></g>
  <g id="248">
   <use class="kv10" height="26" transform="rotate(270,936.619,856.967) scale(-1,1.25) translate(-1873.24,-168.143)" width="12" x="930.6186513994147" xlink:href="#Accessory:避雷器1_0" y="840.7171666874191" zvalue="868"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454004310019" ObjectName="10kV备用二回线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,936.619,856.967) scale(-1,1.25) translate(-1873.24,-168.143)" width="12" x="930.6186513994147" y="840.7171666874191"/></g>
  <g id="418">
   <use class="kv10" height="26" transform="rotate(270,1363,853.467) scale(-1,1.25) translate(-2726,-167.443)" width="12" x="1357" xlink:href="#Accessory:避雷器1_0" y="837.2171666874191" zvalue="919"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454006341635" ObjectName="10kV勐平线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1363,853.467) scale(-1,1.25) translate(-2726,-167.443)" width="12" x="1357" y="837.2171666874191"/></g>
  <g id="377">
   <use class="kv10" height="26" transform="rotate(270,1483,854.967) scale(-1,1.25) translate(-2966,-167.743)" width="12" x="1477" xlink:href="#Accessory:避雷器1_0" y="838.7171666874191" zvalue="936"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454005882883" ObjectName="10kV备用Ⅳ回线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1483,854.967) scale(-1,1.25) translate(-2966,-167.743)" width="12" x="1477" y="838.7171666874191"/></g>
  <g id="356">
   <use class="kv10" height="26" transform="rotate(270,1603.62,851.467) scale(-1,1.25) translate(-3207.24,-167.043)" width="12" x="1597.618651399415" xlink:href="#Accessory:避雷器1_0" y="835.2171666874191" zvalue="953"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454005424131" ObjectName="10kV备用Ⅴ回线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1603.62,851.467) scale(-1,1.25) translate(-3207.24,-167.043)" width="12" x="1597.618651399415" y="835.2171666874191"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="441">
   <use class="kv35" height="30" transform="rotate(0,1037.69,130.625) scale(4.08692,1.175) translate(-772.978,-16.8298)" width="7" x="1023.382018458804" xlink:href="#ACLineSegment:线路_0" y="113" zvalue="488"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249323143172" ObjectName="35kV汉秀线"/>
   <cge:TPSR_Ref TObjectID="8444249323143172_5066549680537601"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1037.69,130.625) scale(4.08692,1.175) translate(-772.978,-16.8298)" width="7" x="1023.382018458804" y="113"/></g>
  <g id="186">
   <use class="kv35" height="30" transform="rotate(0,1256.69,127.5) scale(4.08692,1.175) translate(-938.392,-16.3644)" width="7" x="1242.382018458804" xlink:href="#ACLineSegment:线路_0" y="109.875" zvalue="767"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249322422276" ObjectName="35kV丽秀线"/>
   <cge:TPSR_Ref TObjectID="8444249322422276_5066549680537601"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1256.69,127.5) scale(4.08692,1.175) translate(-938.392,-16.3644)" width="7" x="1242.382018458804" y="109.875"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="72">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="72" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1037.69,20.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1037.88" xml:space="preserve" y="25.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133896695811" ObjectName="P"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="73" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1037.69,43.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1037.88" xml:space="preserve" y="48.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133896761349" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="74">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="74" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1037.69,66.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1037.88" xml:space="preserve" y="71.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133896826885" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="75">
   <text Format="f4.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="75" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1195.09,984.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1195.28" xml:space="preserve" y="989.3" zvalue="1">Q:dd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133893812227" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f4.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="78" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1047.94,983.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1048.13" xml:space="preserve" y="988.8" zvalue="1">Q:dd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133898268675" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="79">
   <text Format="f4.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="79" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1193.09,1001.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1193.28" xml:space="preserve" y="1006.3" zvalue="1">Ia:dd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133893877763" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f4.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="85" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1046.94,1000.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1047.13" xml:space="preserve" y="1005.8" zvalue="1">Ia:dd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133898334211" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="86" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,490.881,967.5) scale(1,1) translate(0,0)" writing-mode="lr" x="491.08" xml:space="preserve" y="972.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133885292547" ObjectName="P"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="94" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,490.881,990.5) scale(1,1) translate(0,0)" writing-mode="lr" x="491.08" xml:space="preserve" y="995.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133885358083" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="101" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,490.881,1013.5) scale(1,1) translate(0,0)" writing-mode="lr" x="491.08" xml:space="preserve" y="1018.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133885423619" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="111" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,128.233,521.25) scale(1,1) translate(1.14211e-14,0)" writing-mode="lr" x="128.34" xml:space="preserve" y="526.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133892763651" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="112">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="112" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,128.233,544.25) scale(1,1) translate(1.14211e-14,5.96467e-14)" writing-mode="lr" x="128.34" xml:space="preserve" y="549.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133892829187" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="113">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="113" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,128.233,567.25) scale(1,1) translate(1.14211e-14,-1.244e-13)" writing-mode="lr" x="128.34" xml:space="preserve" y="572.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133892894723" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="114">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="114" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,809.094,155.611) scale(1,1) translate(0,0)" writing-mode="lr" x="809.29" xml:space="preserve" y="160.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133893025795" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,128.233,495.75) scale(1,1) translate(1.14211e-14,-1.08524e-13)" writing-mode="lr" x="128.34" xml:space="preserve" y="500.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133893025795" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="118">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="118" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,135,204.111) scale(1,1) translate(0,0)" writing-mode="lr" x="135.15" xml:space="preserve" y="210.54" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133893156867" ObjectName="F"/>
   </metadata>
  </g>
  <g id="119">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,251.042,521.25) scale(1,1) translate(2.50556e-14,0)" writing-mode="lr" x="251.15" xml:space="preserve" y="526.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133892239363" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="120">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="120" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,251.042,544.25) scale(1,1) translate(2.50556e-14,5.96467e-14)" writing-mode="lr" x="251.15" xml:space="preserve" y="549.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133892304899" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="121">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,251.042,567.25) scale(1,1) translate(2.50556e-14,-1.244e-13)" writing-mode="lr" x="251.15" xml:space="preserve" y="572.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133892370435" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="122">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,598.333,531.5) scale(1,1) translate(0,0)" writing-mode="lr" x="598.53" xml:space="preserve" y="536.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133892501507" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="123">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,251.042,495.75) scale(1,1) translate(2.50556e-14,-1.08524e-13)" writing-mode="lr" x="251.15" xml:space="preserve" y="500.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133892501507" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="125">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="125" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,135,229) scale(1,1) translate(0,0)" writing-mode="lr" x="135.15" xml:space="preserve" y="235.43" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133892632579" ObjectName="F"/>
   </metadata>
  </g>
  <g id="126">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="126" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,314,522.25) scale(1,1) translate(3.20454e-14,0)" writing-mode="lr" x="314.11" xml:space="preserve" y="527.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133893287939" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="127">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="127" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,314,545.25) scale(1,1) translate(3.20454e-14,-1.19516e-13)" writing-mode="lr" x="314.11" xml:space="preserve" y="550.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133893353475" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="128">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="128" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,314,568.25) scale(1,1) translate(3.20454e-14,-1.24623e-13)" writing-mode="lr" x="314.11" xml:space="preserve" y="573.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133893419011" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="129">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="129" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,324,229.25) scale(1,1) translate(0,0)" writing-mode="lr" x="324.15" xml:space="preserve" y="235.68" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133893681155" ObjectName="F"/>
   </metadata>
  </g>
  <g id="130">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1662,536.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1662.2" xml:space="preserve" y="541.66" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133893550083" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="131">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="131" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,314,496.75) scale(1,1) translate(3.20454e-14,-1.08746e-13)" writing-mode="lr" x="314.11" xml:space="preserve" y="501.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133893550083" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="133">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="133" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,888.563,378.694) scale(1,1) translate(0,0)" writing-mode="lr" x="888.8" xml:space="preserve" y="384.68" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133890207747" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="134" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,888.563,405.694) scale(1,1) translate(0,-2.6192e-13)" writing-mode="lr" x="888.8" xml:space="preserve" y="411.68" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133890273285" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="135">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="135" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,888.563,432.694) scale(1,1) translate(0,0)" writing-mode="lr" x="888.8" xml:space="preserve" y="438.68" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133890469891" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="136" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,903.563,602.694) scale(1,1) translate(0,0)" writing-mode="lr" x="903.8" xml:space="preserve" y="608.6799999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133890338821" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="137" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,903.563,629.694) scale(1,1) translate(0,0)" writing-mode="lr" x="903.8" xml:space="preserve" y="635.6799999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133890404355" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="138" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,903.563,656.694) scale(1,1) translate(0,0)" writing-mode="lr" x="903.8" xml:space="preserve" y="662.6799999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133890797571" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="139">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,135,251.194) scale(1,1) translate(0,0)" writing-mode="lr" x="135.15" xml:space="preserve" y="257.62" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133890666499" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,135,274.194) scale(1,1) translate(0,-3.50645e-13)" writing-mode="lr" x="135.15" xml:space="preserve" y="280.62" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133890732035" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="141" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1509.83,388.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.07" xml:space="preserve" y="394.68" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133886734339" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="142" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1509.83,415.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.07" xml:space="preserve" y="421.68" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133886799875" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="143" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1509.83,442.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.07" xml:space="preserve" y="448.68" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133886996483" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="144" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1526.83,599.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1527.07" xml:space="preserve" y="605.6799999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133886865413" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="145" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1526.83,626.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1527.07" xml:space="preserve" y="632.6799999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133886930949" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="146">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="146" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1526.83,653.694) scale(1,1) translate(0,-4.27121e-13)" writing-mode="lr" x="1527.07" xml:space="preserve" y="659.6799999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133887324163" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="147">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,324,250.194) scale(1,1) translate(0,0)" writing-mode="lr" x="324.15" xml:space="preserve" y="256.62" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133887193091" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,320,273.194) scale(1,1) translate(0,-5.82189e-14)" writing-mode="lr" x="320.15" xml:space="preserve" y="279.62" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133887258627" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="149">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,128.233,594.25) scale(1,1) translate(1.14211e-14,1.30396e-13)" writing-mode="lr" x="128.34" xml:space="preserve" y="599.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133893222403" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="150" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,251.042,594.25) scale(1,1) translate(2.50556e-14,1.30396e-13)" writing-mode="lr" x="251.15" xml:space="preserve" y="599.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133892698115" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="151">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="151" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,314,595.25) scale(1,1) translate(3.20454e-14,1.30618e-13)" writing-mode="lr" x="314.11" xml:space="preserve" y="600.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133893746691" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="117" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,135,179) scale(1,1) translate(0,0)" writing-mode="lr" x="135.15" xml:space="preserve" y="185.43" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133915439107" ObjectName=""/>
   </metadata>
  </g>
  <g id="124">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="124" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,323,178.5) scale(1,1) translate(0,0)" writing-mode="lr" x="323.15" xml:space="preserve" y="184.93" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133915504643" ObjectName=""/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1256.69,29.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1256.88" xml:space="preserve" y="34.29" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133899710469" ObjectName="P"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="2" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1256.69,52.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1256.88" xml:space="preserve" y="57.29" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133899776005" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1256.69,75.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1256.88" xml:space="preserve" y="80.29000000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133899841541" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="4" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1859.59,959.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1859.78" xml:space="preserve" y="964.41" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133913210883" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="5" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1713.44,959) scale(1,1) translate(0,0)" writing-mode="lr" x="1713.63" xml:space="preserve" y="963.91" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133912293381" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="6" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1859.59,982.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1859.78" xml:space="preserve" y="987.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133913276419" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="7" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1713.44,982) scale(1,1) translate(0,0)" writing-mode="lr" x="1713.63" xml:space="preserve" y="986.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133912358915" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="8" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1319.88,967.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1320.08" xml:space="preserve" y="972.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133911375877" ObjectName="P"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="10" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,652.881,967.5) scale(1,1) translate(0,0)" writing-mode="lr" x="653.08" xml:space="preserve" y="972.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133903118339" ObjectName="P"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="11" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1319.88,990.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1320.08" xml:space="preserve" y="995.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133911441413" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="13" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,669.151,990.5) scale(1,1) translate(0,0)" writing-mode="lr" x="669.35" xml:space="preserve" y="995.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133903183875" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="14" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1319.88,1013.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1320.08" xml:space="preserve" y="1018.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133911506949" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="16" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,652.881,1013.5) scale(1,1) translate(0,0)" writing-mode="lr" x="653.08" xml:space="preserve" y="1018.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133903249411" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="17" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1236.56,507.087) scale(1,1) translate(0,0)" writing-mode="lr" x="1236.76" xml:space="preserve" y="512" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133889290243" ObjectName="P"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="18" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1236.56,526.087) scale(1,1) translate(0,0)" writing-mode="lr" x="1236.76" xml:space="preserve" y="531" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133889355779" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1236.56,543.087) scale(1,1) translate(0,0)" writing-mode="lr" x="1236.76" xml:space="preserve" y="548" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133889421315" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,342.625,320.5) scale(0.708333,0.665547) translate(136.706,156.042)" width="30" x="332" xlink:href="#State:红绿圆(方形)_0" y="310.52" zvalue="751"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374925299713" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,342.625,320.5) scale(0.708333,0.665547) translate(136.706,156.042)" width="30" x="332" y="310.52"/></g>
  <g id="1035">
   <use height="30" transform="rotate(0,247,320.5) scale(0.708333,0.665547) translate(97.3309,156.042)" width="30" x="236.38" xlink:href="#State:红绿圆(方形)_0" y="310.52" zvalue="752"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562954796728326" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,247,320.5) scale(0.708333,0.665547) translate(97.3309,156.042)" width="30" x="236.38" y="310.52"/></g>
 </g>
</svg>