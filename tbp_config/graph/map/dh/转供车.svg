<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549596585986" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:PAS_STATUS_0" viewBox="0,0,20,10">
   <rect Plane="0" fill="rgb(39,117,0)" fill-opacity="1" height="9.17" rx="0" ry="0" stroke="rgb(0,130,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,10,5) scale(1,1) translate(0,0)" width="19.17" x="0.42" y="0.42"/>
  </symbol>
  <symbol id="State:PAS_STATUS_1" viewBox="0,0,20,10">
   <rect Plane="0" fill="none" height="9.17" rx="0" ry="0" stroke="rgb(0,130,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,10,5) scale(1,1) translate(0,0)" width="19.17" x="0.42" y="0.42"/>
  </symbol>
  <symbol id="State:模拟隔刀_0" viewBox="0,0,20,10">
   <line Plane="0" fill="none" stroke="rgb(0,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0" x2="5" y1="5" y2="5"/>
   <line Plane="0" fill="none" stroke="rgb(0,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="14.91666666666667" y1="5" y2="5"/>
   <line Plane="0" fill="none" stroke="rgb(0,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="15" y1="5" y2="5"/>
  </symbol>
  <symbol id="State:模拟隔刀_1" viewBox="0,0,20,10">
   <line Plane="0" fill="none" stroke="rgb(0,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="14" y1="5" y2="1.166666666666666"/>
   <line Plane="0" fill="none" stroke="rgb(0,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0" x2="5" y1="5" y2="5"/>
   <line Plane="0" fill="none" stroke="rgb(0,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="15" y1="5" y2="5"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="State:模拟小车_0" viewBox="0,0,20,10">
   <line Plane="0" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.33333333333333" x2="19.16666666666667" y1="5.100155602990633" y2="5.100155602990633"/>
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,9.99,5.17) scale(1,1) translate(0,0)" width="5" x="7.49" y="0.75"/>
   <line Plane="0" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.000000000000004" x2="5.66666666666667" y1="5.100155602990633" y2="5.100155602990633"/>
   <path Plane="0" d="M 3.05571 1.41677 L 0.833227 5.10196 L 3.05571 8.71559" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path Plane="0" d="M 16.9166 1.53463 L 19.139 5.21981 L 16.9166 8.83344" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path Plane="0" d="M 15.4744 1.53463 L 17.7985 5.18403 L 15.4744 8.83344" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path Plane="0" d="M 4.30571 1.41677 L 2.08323 5.10196 L 4.30571 8.71559" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="State:模拟小车_1" viewBox="0,0,20,10">
   <line Plane="0" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.33333333333333" x2="17.83333333333334" y1="5.100155602990633" y2="5.100155602990633"/>
   <rect Plane="0" fill="none" fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,9.99,5.17) scale(1,1) translate(0,0)" width="5" x="7.49" y="0.75"/>
   <line Plane="0" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.000000000000003" x2="5.666666666666669" y1="5.100155602990633" y2="5.100155602990633"/>
   <path Plane="0" d="M 3.05571 1.41677 L 0.833227 5.10196 L 3.05571 8.71559" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path Plane="0" d="M 16.9166 1.53463 L 19.139 5.21981 L 16.9166 8.83344" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path Plane="0" d="M 15.4744 1.53463 L 17.7985 5.18403 L 15.4744 8.83344" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path Plane="0" d="M 4.30571 1.41677 L 2.08323 5.10196 L 4.30571 8.71559" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2019.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变20230624_0" viewBox="0,0,30,75">
   <use terminal-index="0" type="0" x="15.10404825606729" xlink:href="#terminal" y="1.017113514928901"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.1662155658394" x2="15.1662155658394" y1="57.05589301390916" y2="69.89818101506718"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.10404825606729" x2="15.10404825606729" y1="15.96057141020351" y2="0.6249991208314611"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.13,8.75) scale(1,1) translate(0,0)" width="4.5" x="12.87" y="4.25"/>
   <path d="M 9.71621 32.4415 L 21.0099 32.4415 L 15.104 23.0238 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.1" cy="26.56" fill-opacity="0" rx="10.4" ry="10.74" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.1" cy="46.46" fill-opacity="0" rx="10.4" ry="10.74" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.747583266368155" x2="15.1040482560673" y1="52.02599688012225" y2="45.81889101289587"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00043607311378" x2="22.25328887985942" y1="45.70880540384262" y2="51.91897781344593"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00043607311379" x2="15.00043607311379" y1="45.71187194621954" y2="39.0766898122879"/>
   <path d="M 13.0318 58.8752 L 17.2799 58.8752 L 15.2077 63.049 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 14.5445 69.8982 L 15.8915 69.8982 L 15.1662 74.5 z" fill="rgb(0,255,127)" fill-opacity="1" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="EnergyConsumer:厂用变负荷_0" viewBox="0,0,24,49">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.05" x2="12.05" y1="34.5" y2="44.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="2.5" y2="0.5"/>
   <path d="M 7.66667 15.3333 L 16.75 15.3333 L 12 8 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.083333333333333" x2="12" y1="30.58333333333333" y2="25.75"/>
   <ellipse cx="12" cy="10.75" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12" cy="26.25" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.91666666666667" x2="17.75" y1="25.66427881928568" y2="30.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.91666666666667" x2="11.91666666666667" y1="25.66666666666666" y2="20.5"/>
   <path d="M 11.55 44.5 L 12.6333 44.5 L 12.05 48.0833 z" fill="rgb(0,255,127)" fill-opacity="1" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 10.3333 35.9167 L 13.75 35.9167 L 12.0833 39.1667 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_0" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="3.333333333333332" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="24.08333333333333" y2="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="7" y1="19.5" y2="24.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_1" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="18.25" y2="32.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_2" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="4" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="10" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="25.83333333333333" y2="32.41666666666667"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0.25" y2="25.75"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="转供车" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="全站公用20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="38" y="308" zvalue="1589"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="38" y="381" zvalue="1590"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="38" y="343.5" zvalue="1592"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="140" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,74.4375,320) scale(1,1) translate(0,0)" width="72.88" x="38" y="308" zvalue="1589"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.4375,320) scale(1,1) translate(0,0)" writing-mode="lr" x="74.44" xml:space="preserve" y="324.5" zvalue="1589">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="139" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,74.4375,393) scale(1,1) translate(0,0)" width="72.88" x="38" y="381" zvalue="1590"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.4375,393) scale(1,1) translate(0,0)" writing-mode="lr" x="74.44" xml:space="preserve" y="397.5" zvalue="1590">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="137" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,74.4375,355.5) scale(1,1) translate(0,0)" width="72.88" x="38" y="343.5" zvalue="1592"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.4375,355.5) scale(1,1) translate(0,0)" writing-mode="lr" x="74.44" xml:space="preserve" y="360" zvalue="1592">信号一览</text>
  <rect fill="none" fill-opacity="0" height="117" id="57" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1197.25,134.5) scale(1,1) translate(0,0)" width="1027.5" x="683.5" y="76" zvalue="1"/>
  <line fill="none" id="67" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382" x2="382" y1="11" y2="1041" zvalue="59"/>
  <line fill="none" id="666" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="146.8704926140824" y2="146.8704926140824" zvalue="1057"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="159" y2="159"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="159" y2="159"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="277.5" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="254.75" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="254.75" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="277.5" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="254.75" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="254.75" y2="277.5"/>
  <line fill="none" id="613" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="616.8704926140824" y2="616.8704926140824" zvalue="1059"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="999.0816" y2="1027"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="609" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.75,952.875) scale(1,1) translate(0,0)" writing-mode="lr" x="54.75" xml:space="preserve" y="958.88" zvalue="1063">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="608" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.5,984.75) scale(1,1) translate(0,0)" writing-mode="lr" x="51.5" xml:space="preserve" y="990.75" zvalue="1064">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="607" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.5,984.75) scale(1,1) translate(4.30489e-14,-1.94669e-12)" writing-mode="lr" x="234.5" xml:space="preserve" y="990.75" zvalue="1065">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="606" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.5,1012.88) scale(1,1) translate(0,0)" writing-mode="lr" x="51.5" xml:space="preserve" y="1018.88" zvalue="1066">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="605" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.5,1012.75) scale(1,1) translate(4.28269e-14,-2.00265e-12)" writing-mode="lr" x="233.5" xml:space="preserve" y="1018.75" zvalue="1067">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="602" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.25,646.5) scale(1,1) translate(0,0)" writing-mode="lr" x="82.25" xml:space="preserve" y="651" zvalue="1070">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="598" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,328.5,458.5) scale(1,1) translate(0,0)" writing-mode="lr" x="328.5" xml:space="preserve" y="463" zvalue="1074">10kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="596" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.5,489.5) scale(1,1) translate(0,0)" writing-mode="lr" x="85.5" xml:space="preserve" y="494" zvalue="1076">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="595" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.5,515) scale(1,1) translate(1.4877e-14,0)" writing-mode="lr" x="85.5" xml:space="preserve" y="519.5" zvalue="1077">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="594" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.5,540.5) scale(1,1) translate(1.4877e-14,0)" writing-mode="lr" x="85.5" xml:space="preserve" y="545" zvalue="1078">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="593" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.5,565) scale(1,1) translate(1.4877e-14,0)" writing-mode="lr" x="85.5" xml:space="preserve" y="569.5" zvalue="1079">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="592" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.5,591.5) scale(1,1) translate(1.4877e-14,0)" writing-mode="lr" x="85.5" xml:space="preserve" y="596" zvalue="1080">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,145.554,1012.75) scale(1,1) translate(0,0)" writing-mode="lr" x="145.55" xml:space="preserve" y="1018.75" zvalue="1082">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,325.375,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="325.38" xml:space="preserve" y="1020" zvalue="1083">20230625</text>
  <image height="76.26000000000001" id="4" preserveAspectRatio="xMidYMid slice" width="237.88" x="59.12" xlink:href="logo.png" y="34.74"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,178.06,72.8706) scale(1,1) translate(-1.31273e-14,0)" writing-mode="lr" x="178.06" xml:space="preserve" y="77.37" zvalue="1440"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,190.961,320.591) scale(1,1) translate(0,0)" writing-mode="lr" x="190.96" xml:space="preserve" y="325.09" zvalue="1584">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,284.481,320.591) scale(1,1) translate(0,0)" writing-mode="lr" x="284.48" xml:space="preserve" y="325.09" zvalue="1585">通道</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="19" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,200.56,70.3081) scale(1,1) translate(-6.13376e-14,0)" writing-mode="lr" x="200.5600396517016" xml:space="preserve" y="76.80807264220238" zvalue="1711">转供车</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,486.938,510.875) scale(1,1) translate(0,0)" writing-mode="lr" x="486.94" xml:space="preserve" y="515.38" zvalue="2239">10kV临时Ⅰ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1829.62,508.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1829.63" xml:space="preserve" y="512.75" zvalue="2241">10kV临时Ⅱ段母线</text>
  <line fill="none" id="15" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="798.5000000000001" x2="798.5000000000001" y1="338.5769230769232" y2="275.5000000000001" zvalue="2249"/>
  <line fill="none" id="16" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="798.3333333333333" x2="798.3333333333333" y1="247.8547008547009" y2="184.7777777777778" zvalue="2251"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,798.857,157.714) scale(1,1) translate(7.90162e-14,-2.18492e-13)" writing-mode="lr" x="798.86" xml:space="preserve" y="162.21" zvalue="2254">至110kV平原变#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,836,261.286) scale(1,1) translate(0,2.48944e-13)" writing-mode="lr" x="836" xml:space="preserve" y="265.79" zvalue="2256">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,828.857,358.429) scale(1,1) translate(-3.59839e-13,-7.68909e-14)" writing-mode="lr" x="828.86" xml:space="preserve" y="362.93" zvalue="2258">0011</text>
  <line fill="none" id="41" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1587.333312988281" x2="1587.333312988281" y1="285" y2="184.7777777777778" zvalue="2279"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1622.27,349.468) scale(1,1) translate(-2.49265e-12,-7.49011e-14)" writing-mode="lr" x="1622.27" xml:space="preserve" y="353.97" zvalue="2281">002</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" x="447" xml:space="preserve" y="738.5" zvalue="2292">10kV临时Ⅰ段母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="447" xml:space="preserve" y="754.5" zvalue="2292">线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" x="517.75" xml:space="preserve" y="768.75" zvalue="2294">10kV3号站</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="517.75" xml:space="preserve" y="784.75" zvalue="2294">用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,508.75,574) scale(1,1) translate(0,0)" writing-mode="lr" x="508.75" xml:space="preserve" y="578.5" zvalue="2297">0903</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" x="563.25" xml:space="preserve" y="857.25" zvalue="2300">10kV工业园</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="563.25" xml:space="preserve" y="873.25" zvalue="2300">区线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" x="623.5" xml:space="preserve" y="858" zvalue="2302">10kV1号站用</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="623.5" xml:space="preserve" y="874" zvalue="2302">变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,615.75,624) scale(1,1) translate(0,0)" writing-mode="lr" x="615.75" xml:space="preserve" y="628.5" zvalue="2307">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,604.5,573) scale(1,1) translate(0,0)" writing-mode="lr" x="604.5" xml:space="preserve" y="577.5" zvalue="2311">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,633.75,702) scale(1,1) translate(0,0)" writing-mode="lr" x="633.75" xml:space="preserve" y="706.5" zvalue="2316">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,716.75,624) scale(1,1) translate(0,0)" writing-mode="lr" x="716.75" xml:space="preserve" y="628.5" zvalue="2350">052</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,705.5,573) scale(1,1) translate(0,0)" writing-mode="lr" x="705.5" xml:space="preserve" y="577.5" zvalue="2352">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,734.75,702) scale(1,1) translate(0,0)" writing-mode="lr" x="734.75" xml:space="preserve" y="706.5" zvalue="2359">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" x="695.0078125" xml:space="preserve" y="857.9999997615814" zvalue="2362">10kV平原硅厂</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="695.0078125" xml:space="preserve" y="873.9999997615814" zvalue="2362">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,809.75,624) scale(1,1) translate(0,0)" writing-mode="lr" x="809.75" xml:space="preserve" y="628.5" zvalue="2394">053</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,798.5,573) scale(1,1) translate(0,0)" writing-mode="lr" x="798.5" xml:space="preserve" y="577.5" zvalue="2396">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,827.75,702) scale(1,1) translate(0,0)" writing-mode="lr" x="827.75" xml:space="preserve" y="706.5" zvalue="2403">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" x="788.0078125" xml:space="preserve" y="857.9999997615814" zvalue="2405">10kV盈江水泥</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="788.0078125" xml:space="preserve" y="873.9999997615814" zvalue="2405">厂线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,898.75,624) scale(1,1) translate(0,0)" writing-mode="lr" x="898.75" xml:space="preserve" y="628.5" zvalue="2409">054</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,887.5,573) scale(1,1) translate(0,0)" writing-mode="lr" x="887.5" xml:space="preserve" y="577.5" zvalue="2411">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,916.75,702) scale(1,1) translate(0,0)" writing-mode="lr" x="916.75" xml:space="preserve" y="706.5" zvalue="2418">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" x="877.0078125" xml:space="preserve" y="857.9999997615814" zvalue="2420">10kV平原糖厂</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="877.0078125" xml:space="preserve" y="873.9999997615814" zvalue="2420">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,939.083,859.75) scale(1,1) translate(0,0)" writing-mode="lr" x="939.0833333333334" xml:space="preserve" y="864.25" zvalue="2423">10kV并网线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" x="988.984375" xml:space="preserve" y="857.25" zvalue="2426">10kV勐腊路</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="988.984375" xml:space="preserve" y="873.25" zvalue="2426">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1010.75,623.333) scale(1,1) translate(3.329e-13,0)" writing-mode="lr" x="1010.75" xml:space="preserve" y="627.83" zvalue="2429">055</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,999.5,572.333) scale(1,1) translate(0,0)" writing-mode="lr" x="999.5" xml:space="preserve" y="576.83" zvalue="2431">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1028.75,701.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1028.75" xml:space="preserve" y="705.83" zvalue="2438">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" x="1037.484375" xml:space="preserve" y="857.234375" zvalue="2441">10kV象城路</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1037.484375" xml:space="preserve" y="873.234375" zvalue="2441">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" x="1102" xml:space="preserve" y="857" zvalue="2444">10kV永盛</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1102" xml:space="preserve" y="873" zvalue="2444">路线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="178" stroke="rgb(255,255,255)" text-anchor="middle" x="1163.75" xml:space="preserve" y="856.75" zvalue="2447">10kV平胜Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="178" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1163.75" xml:space="preserve" y="872.75" zvalue="2447">回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="177" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1156.25,623.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1156.25" xml:space="preserve" y="628.25" zvalue="2450">056</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1145,572.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1145" xml:space="preserve" y="577.25" zvalue="2452">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="175" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1174.25,701.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1174.25" xml:space="preserve" y="706.25" zvalue="2459">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1338.11,611) scale(1,1) translate(0,0)" writing-mode="lr" x="1338.11" xml:space="preserve" y="615.5" zvalue="2463">057</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1353.75,702) scale(1,1) translate(0,0)" writing-mode="lr" x="1353.75" xml:space="preserve" y="706.5" zvalue="2472">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" x="1313.9921875" xml:space="preserve" y="857.9999997615814" zvalue="2474">10kV2号站用</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1313.9921875" xml:space="preserve" y="873.9999997615814" zvalue="2474">变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="213" stroke="rgb(255,255,255)" text-anchor="middle" x="1387" xml:space="preserve" y="857" zvalue="2477">10kV岗勐</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="213" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1387" xml:space="preserve" y="873" zvalue="2477">乡线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="212" stroke="rgb(255,255,255)" text-anchor="middle" x="1448.75" xml:space="preserve" y="856.75" zvalue="2480">10kV平胜Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="212" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1448.75" xml:space="preserve" y="872.75" zvalue="2480">回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1440.25,611.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1440.25" xml:space="preserve" y="615.75" zvalue="2483">058</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1459.25,701.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1459.25" xml:space="preserve" y="706.25" zvalue="2492">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1539.75,611.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1539.75" xml:space="preserve" y="615.75" zvalue="2496">059</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="227" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1558.75,702) scale(1,1) translate(0,0)" writing-mode="lr" x="1558.75" xml:space="preserve" y="706.5" zvalue="2505">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="middle" x="1518.9921875" xml:space="preserve" y="857.9999997615814" zvalue="2507">10kV目瑙纵歌</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1518.9921875" xml:space="preserve" y="873.9999997615814" zvalue="2507">路线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="249" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1637.75,610.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1637.75" xml:space="preserve" y="614.75" zvalue="2511">061</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1651.75,702) scale(1,1) translate(0,0)" writing-mode="lr" x="1651.75" xml:space="preserve" y="706.5" zvalue="2520">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="246" stroke="rgb(255,255,255)" text-anchor="middle" x="1611.9921875" xml:space="preserve" y="857.9999997615814" zvalue="2522">10kV华盛砖厂</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="246" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1611.9921875" xml:space="preserve" y="873.9999997615814" zvalue="2522">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1734.12,611.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1734.13" xml:space="preserve" y="615.75" zvalue="2530">0622</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" x="1704.3203125" xml:space="preserve" y="854.9999997615814" zvalue="2537">10kV4号站</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1704.3203125" xml:space="preserve" y="870.9999997615814" zvalue="2537">用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="277" stroke="rgb(255,255,255)" text-anchor="middle" x="1809" xml:space="preserve" y="740" zvalue="2542">10kV临时Ⅱ段母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="277" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1809" xml:space="preserve" y="756" zvalue="2542">线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="280" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1833.36,613.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1833.36" xml:space="preserve" y="618" zvalue="2544">0904</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,203.5,458.5) scale(1,1) translate(0,0)" writing-mode="lr" x="203.5" xml:space="preserve" y="463" zvalue="2603">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,197.5) scale(1,1) translate(-4.21885e-14,-4.19645e-14)" writing-mode="lr" x="236" xml:space="preserve" y="202" zvalue="2610">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,197.5) scale(1,1) translate(0,-4.19645e-14)" writing-mode="lr" x="57" xml:space="preserve" y="202" zvalue="2611">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1198.88,129.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1198.88" xml:space="preserve" y="136.13" zvalue="2612">110kV平原变</text>
  <rect fill="none" fill-opacity="0" height="806.75" id="59" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,811.625,623.375) scale(1,1) translate(0,0)" width="806.25" x="408.5" y="220" zvalue="2614"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,483.625,999.625) scale(1,1) translate(0,0)" writing-mode="lr" x="483.63" xml:space="preserve" y="1006.13" zvalue="2616">10kV1号配电车</text>
  <rect fill="none" fill-opacity="0" height="806.75" id="61" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1567,623.25) scale(1,1) translate(0,0)" width="649.5" x="1242.25" y="219.88" zvalue="2618"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="19" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1818.75,999.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1818.75" xml:space="preserve" y="1006.13" zvalue="2620">10kV2号配电车</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1586.89,160.393) scale(1,1) translate(-6.66023e-13,-2.22655e-13)" writing-mode="lr" x="1586.89" xml:space="preserve" y="164.89" zvalue="2622">至110kV平原变#2主变</text>
  <text fill="rgb(255,85,255)" font-family="FangSong" font-size="13" id="88" stroke="rgb(255,85,255)" text-anchor="middle" x="593.75" xml:space="preserve" y="913.5" zvalue="2628">10kV1号站用变及工</text>
  <text fill="rgb(255,85,255)" font-family="FangSong" font-size="13" id="88" stroke="rgb(255,85,255)" text-anchor="middle" writing-mode="lr" x="593.75" xml:space="preserve" y="930.5" zvalue="2628">业园区线051断路器</text>
  <text fill="rgb(255,85,255)" font-family="FangSong" font-size="13" id="88" stroke="rgb(255,85,255)" text-anchor="middle" writing-mode="lr" x="593.75" xml:space="preserve" y="947.5" zvalue="2628">间隔</text>
  <text fill="rgb(255,85,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,85,255)" text-anchor="middle" x="989" xml:space="preserve" y="913.5" zvalue="2630">10kV并网、勐腊路及</text>
  <text fill="rgb(255,85,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,85,255)" text-anchor="middle" writing-mode="lr" x="989" xml:space="preserve" y="930.5" zvalue="2630">象城线055断路器间</text>
  <text fill="rgb(255,85,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,85,255)" text-anchor="middle" writing-mode="lr" x="989" xml:space="preserve" y="947.5" zvalue="2630">隔</text>
  <text fill="rgb(255,85,255)" font-family="FangSong" font-size="13" id="90" stroke="rgb(255,85,255)" text-anchor="middle" x="1139.25" xml:space="preserve" y="913.5" zvalue="2632">10kV永盛路及平胜</text>
  <text fill="rgb(255,85,255)" font-family="FangSong" font-size="13" id="90" stroke="rgb(255,85,255)" text-anchor="middle" writing-mode="lr" x="1139.25" xml:space="preserve" y="930.5" zvalue="2632">Ⅰ回线056断路器间</text>
  <text fill="rgb(255,85,255)" font-family="FangSong" font-size="13" id="90" stroke="rgb(255,85,255)" text-anchor="middle" writing-mode="lr" x="1139.25" xml:space="preserve" y="947.5" zvalue="2632">隔</text>
  <text fill="rgb(255,85,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,85,255)" text-anchor="middle" x="1420.25" xml:space="preserve" y="913.5" zvalue="2634">10kV岗勐乡及平胜</text>
  <text fill="rgb(255,85,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,85,255)" text-anchor="middle" writing-mode="lr" x="1420.25" xml:space="preserve" y="930.5" zvalue="2634">Ⅱ回线058断路器间</text>
  <text fill="rgb(255,85,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,85,255)" text-anchor="middle" writing-mode="lr" x="1420.25" xml:space="preserve" y="947.5" zvalue="2634">隔</text>
  <text fill="rgb(255,85,255)" font-family="FangSong" font-size="13" id="92" stroke="rgb(255,85,255)" text-anchor="middle" x="483.5" xml:space="preserve" y="823" zvalue="2636">10kV临时Ⅰ段母线电压互</text>
  <text fill="rgb(255,85,255)" font-family="FangSong" font-size="13" id="92" stroke="rgb(255,85,255)" text-anchor="middle" writing-mode="lr" x="483.5" xml:space="preserve" y="840" zvalue="2636">感器0903隔离开关间隔</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="start" x="873.015625" xml:space="preserve" y="289.5" zvalue="2638">001断路器、0011隔离开属1号配</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="start" writing-mode="lr" x="873.015625" xml:space="preserve" y="306.5" zvalue="2638">电车设备，自动化信号由平原变远</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="start" writing-mode="lr" x="873.015625" xml:space="preserve" y="323.5" zvalue="2638">动转发。</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="start" x="1390" xml:space="preserve" y="289" zvalue="2640">002断路器属2号配电车设备，自</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="start" writing-mode="lr" x="1390" xml:space="preserve" y="306" zvalue="2640">动化信号由平原变远动转发。</text>
  <rect fill="none" fill-opacity="0" height="10" id="26" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,777.299,235.647) scale(1,1) translate(0,0)" width="20.83" x="766.88" y="230.65" zvalue="4533"/>
  <line fill="none" id="27" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="798.2745098039218" x2="777.607843137255" y1="235.6470588036612" y2="235.6470588036612" zvalue="4534"/>
  <line fill="none" id="28" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="766.6931144550845" x2="760.4140446876427" y1="235.6935676125919" y2="235.6935676125919" zvalue="4535"/>
  <line fill="none" id="29" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="760.5006839945281" x2="760.5006839945281" y1="230.7665270289165" y2="239.836294470777" zvalue="4536"/>
  <line fill="none" id="30" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="753.9890560875515" x2="753.9890560875515" y1="232.6269921517799" y2="237.9758293610822" zvalue="4537"/>
  <line fill="none" id="31" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="756.7797537619701" x2="756.7797537619701" y1="231.6967595953731" y2="238.9060619209544" zvalue="4538"/>
  <rect fill="none" fill-opacity="0" height="10" id="10" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1566.3,239.647) scale(1,1) translate(8.46414e-12,0)" width="20.83" x="1555.88" y="234.65" zvalue="4545"/>
  <line fill="none" id="9" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1587.274509803922" x2="1566.607843137255" y1="239.6470588036612" y2="239.6470588036612" zvalue="4546"/>
  <line fill="none" id="8" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1555.693114455084" x2="1549.414044687643" y1="239.6935676125919" y2="239.6935676125919" zvalue="4547"/>
  <line fill="none" id="7" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1549.500683994528" x2="1549.500683994528" y1="234.7665270289165" y2="243.836294470777" zvalue="4548"/>
  <line fill="none" id="6" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1542.989056087551" x2="1542.989056087551" y1="236.6269921517799" y2="241.9758293610822" zvalue="4549"/>
  <line fill="none" id="1" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1545.77975376197" x2="1545.77975376197" y1="235.6967595953731" y2="242.9060619209544" zvalue="4550"/>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,329.188,320.591) scale(0.708333,0.665547) translate(131.173,156.088)" width="30" x="318.56" xlink:href="#State:红绿圆(方形)_0" y="310.61" zvalue="1586"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.188,320.591) scale(0.708333,0.665547) translate(131.173,156.088)" width="30" x="318.56" y="310.61"/></g>
  <g id="1035">
   <use height="30" transform="rotate(0,233.562,321.25) scale(0.708333,0.665547) translate(91.7978,156.419)" width="30" x="222.94" xlink:href="#State:红绿圆(方形)_0" y="311.27" zvalue="1587"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,233.562,321.25) scale(0.708333,0.665547) translate(91.7978,156.419)" width="30" x="222.94" y="311.27"/></g>
  <g id="388">
   <use height="10" stroke="rgb(0,170,0)" transform="rotate(270,798.414,262.018) scale(1.42857,1.85714) translate(-235.238,-116.646)" width="20" x="789.13" xlink:href="#State:PAS_STATUS_0" y="247.73" zvalue="2234"/>
   <metadata>
    <cge:Meas_Ref ObjectID="15762602088136708" ObjectName=""/>
   </metadata>
  <rect fill="white" height="10" opacity="0" stroke="white" transform="rotate(270,798.414,262.018) scale(1.42857,1.85714) translate(-235.238,-116.646)" width="20" x="789.13" y="247.73"/></g>
  <g id="391">
   <use height="10" stroke="rgb(0,170,0)" transform="rotate(270,798.505,353.878) scale(2.52538,2.34117) translate(-467.059,-196.017)" width="20" x="786.8" xlink:href="#State:模拟隔刀_0" y="328.62" zvalue="2237"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="10" opacity="0" stroke="white" transform="rotate(270,798.505,353.878) scale(2.52538,2.34117) translate(-467.059,-196.017)" width="20" x="786.8" y="328.62"/></g>
  <g id="36">
   <use height="10" stroke="rgb(0,255,0)" transform="rotate(90,1587.31,312.071) scale(-2.92857,3.35714) translate(-2110.03,-207.328)" width="20" x="1570.52" xlink:href="#State:模拟小车_0" y="282.79" zvalue="2273"/>
   <metadata>
    <cge:Meas_Ref ObjectID="15762602088202244" ObjectName=""/>
   </metadata>
  <rect fill="white" height="10" opacity="0" stroke="white" transform="rotate(90,1587.31,312.071) scale(-2.92857,3.35714) translate(-2110.03,-207.328)" width="20" x="1570.52" y="282.79"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="138" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,145,196.5) scale(1,1) translate(0,0)" writing-mode="lr" x="145.2" xml:space="preserve" y="201.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127991574532" ObjectName="F"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,325,197.5) scale(1,1) translate(0,0)" writing-mode="lr" x="325.2" xml:space="preserve" y="202.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127960248326" ObjectName="F"/>
   </metadata>
  </g>
  <g id="154">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,329.222,489.667) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="494.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127960117254" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="153">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,329.222,513.5) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="518.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127959855108" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="152">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="152" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,329.222,537.5) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="542.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127959920647" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="151">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="151" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,329.222,561.5) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="566.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127959986182" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="150" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,329.222,586.5) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="591.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127960313862" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="188">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="188" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,799.311,133.802) scale(1,1) translate(0,-1.11175e-13)" writing-mode="lr" x="798.9" xml:space="preserve" y="138.44" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134917681154" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="189">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="189" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,799.311,103.994) scale(1,1) translate(0,-8.47007e-14)" writing-mode="lr" x="798.9" xml:space="preserve" y="108.63" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134918008834" ObjectName="P"/>
   </metadata>
  </g>
  <g id="196">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="196" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,799.311,118.898) scale(1,1) translate(0,-9.79379e-14)" writing-mode="lr" x="798.9" xml:space="preserve" y="123.54" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134918074370" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="239">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="239" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1587.16,129.318) scale(1,1) translate(0,-5.27659e-14)" writing-mode="lr" x="1586.69" xml:space="preserve" y="133.98" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134920302594" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="240" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1588.16,96.3182) scale(1,1) translate(0,0)" writing-mode="lr" x="1587.69" xml:space="preserve" y="100.98" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134920630274" ObjectName="P"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="241" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1588.16,112.818) scale(1,1) translate(0,0)" writing-mode="lr" x="1587.69" xml:space="preserve" y="117.48" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134920695810" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="294">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="11" id="294" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,554.364,635.437) scale(1,1) translate(5.28577e-13,4.87127e-13)" writing-mode="lr" x="554.1" xml:space="preserve" y="639.0700000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127963131908" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="293">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="11" id="293" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,554.364,605.63) scale(1,1) translate(0,1.32561e-13)" writing-mode="lr" x="554.1" xml:space="preserve" y="609.27" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127963328516" ObjectName="P"/>
   </metadata>
  </g>
  <g id="292">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="11" id="292" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,554.364,620.534) scale(1,1) translate(5.28577e-13,-5.4348e-13)" writing-mode="lr" x="554.1" xml:space="preserve" y="624.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127963394052" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="297">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="11" id="297" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,693.364,920.437) scale(1,1) translate(2.96652e-13,-8.09848e-13)" writing-mode="lr" x="693.1" xml:space="preserve" y="924.0700000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127966932998" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="296">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="11" id="296" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,693.364,890.63) scale(1,1) translate(0,1.95843e-13)" writing-mode="lr" x="693.1" xml:space="preserve" y="894.27" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127966801926" ObjectName="P"/>
   </metadata>
  </g>
  <g id="295">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="11" id="295" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,693.364,905.534) scale(1,1) translate(2.96652e-13,-7.9661e-13)" writing-mode="lr" x="693.1" xml:space="preserve" y="909.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127966867462" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="300">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="11" id="300" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,785.364,920.437) scale(1,1) translate(-1.68754e-13,-8.09848e-13)" writing-mode="lr" x="785.1" xml:space="preserve" y="924.0700000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127967850502" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="299">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="11" id="299" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,785.364,890.63) scale(1,1) translate(0,1.95843e-13)" writing-mode="lr" x="785.1" xml:space="preserve" y="894.27" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127967719430" ObjectName="P"/>
   </metadata>
  </g>
  <g id="298">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="11" id="298" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,785.364,905.534) scale(1,1) translate(-1.68754e-13,-7.9661e-13)" writing-mode="lr" x="785.1" xml:space="preserve" y="909.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127967784966" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="303">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="11" id="303" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,874.364,920.437) scale(1,1) translate(-1.88516e-13,-8.09848e-13)" writing-mode="lr" x="874.1" xml:space="preserve" y="924.0700000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127969292294" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="302">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="11" id="302" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,874.364,890.63) scale(1,1) translate(0,1.95843e-13)" writing-mode="lr" x="874.1" xml:space="preserve" y="894.27" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127969161222" ObjectName="P"/>
   </metadata>
  </g>
  <g id="301">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="11" id="301" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,874.364,905.534) scale(1,1) translate(-1.88516e-13,-7.9661e-13)" writing-mode="lr" x="874.1" xml:space="preserve" y="909.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127969226758" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="306">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="11" id="306" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,946.364,637.437) scale(1,1) translate(-2.04503e-13,4.88681e-13)" writing-mode="lr" x="946.1" xml:space="preserve" y="641.0700000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127985217540" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="305">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="11" id="305" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,946.364,607.63) scale(1,1) translate(0,1.33005e-13)" writing-mode="lr" x="946.1" xml:space="preserve" y="611.27" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127985414148" ObjectName="P"/>
   </metadata>
  </g>
  <g id="304">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="11" id="304" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,946.364,622.534) scale(1,1) translate(-2.04503e-13,-5.45256e-13)" writing-mode="lr" x="946.1" xml:space="preserve" y="626.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127985479684" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="309">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="11" id="309" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1092.36,638.437) scale(1,1) translate(-2.36922e-13,4.89459e-13)" writing-mode="lr" x="1092.1" xml:space="preserve" y="642.0700000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127973879814" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="308">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="11" id="308" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1092.36,608.63) scale(1,1) translate(0,1.33227e-13)" writing-mode="lr" x="1092.1" xml:space="preserve" y="612.27" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127974076422" ObjectName="P"/>
   </metadata>
  </g>
  <g id="307">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="11" id="307" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1092.36,623.534) scale(1,1) translate(-2.36922e-13,-5.46144e-13)" writing-mode="lr" x="1092.1" xml:space="preserve" y="627.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127974141958" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="312">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="11" id="312" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1312.36,920.437) scale(1,1) translate(1.28597e-12,-8.09848e-13)" writing-mode="lr" x="1312.1" xml:space="preserve" y="924.0700000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127976370182" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="311">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="11" id="311" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1312.36,890.63) scale(1,1) translate(0,1.95843e-13)" writing-mode="lr" x="1312.1" xml:space="preserve" y="894.27" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127976239110" ObjectName="P"/>
   </metadata>
  </g>
  <g id="310">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="11" id="310" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1312.36,905.534) scale(1,1) translate(1.28597e-12,-7.9661e-13)" writing-mode="lr" x="1312.1" xml:space="preserve" y="909.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127976304646" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="315">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="11" id="315" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1379.36,625.437) scale(1,1) translate(1.35292e-12,-5.47835e-13)" writing-mode="lr" x="1379.1" xml:space="preserve" y="629.0700000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127977680902" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="314">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="11" id="314" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1379.36,595.63) scale(1,1) translate(0,1.3034e-13)" writing-mode="lr" x="1379.1" xml:space="preserve" y="599.27" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127977877510" ObjectName="P"/>
   </metadata>
  </g>
  <g id="313">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="11" id="313" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1379.36,610.534) scale(1,1) translate(1.35292e-12,-5.34598e-13)" writing-mode="lr" x="1379.1" xml:space="preserve" y="614.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127977943046" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="318">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="11" id="318" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1517.36,921.437) scale(1,1) translate(1.49081e-12,-8.10736e-13)" writing-mode="lr" x="1517.1" xml:space="preserve" y="925.0700000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127980171268" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="317">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="11" id="317" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1517.36,891.63) scale(1,1) translate(0,1.96065e-13)" writing-mode="lr" x="1517.1" xml:space="preserve" y="895.27" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127980040196" ObjectName="P"/>
   </metadata>
  </g>
  <g id="316">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="11" id="316" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1517.36,906.534) scale(1,1) translate(1.49081e-12,-7.97499e-13)" writing-mode="lr" x="1517.1" xml:space="preserve" y="910.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127980105732" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="321">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="11" id="321" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1611.36,921.437) scale(1,1) translate(1.58473e-12,-8.10736e-13)" writing-mode="lr" x="1611.1" xml:space="preserve" y="925.0700000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127981613060" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="320">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="11" id="320" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1611.36,891.63) scale(1,1) translate(0,1.96065e-13)" writing-mode="lr" x="1611.1" xml:space="preserve" y="895.27" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127981481988" ObjectName="P"/>
   </metadata>
  </g>
  <g id="319">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="11" id="319" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1611.36,906.534) scale(1,1) translate(1.58473e-12,-7.97499e-13)" writing-mode="lr" x="1611.1" xml:space="preserve" y="910.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127981547524" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="49">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,206.222,489.667) scale(1,1) translate(-3.982e-14,0)" writing-mode="lr" x="206.34" xml:space="preserve" y="494.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127991377924" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="48">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,205.222,514.5) scale(1,1) translate(-3.9598e-14,0)" writing-mode="lr" x="205.34" xml:space="preserve" y="519.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127991115780" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="45">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="45" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,206.222,537.5) scale(1,1) translate(-3.982e-14,0)" writing-mode="lr" x="206.34" xml:space="preserve" y="542.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127991246852" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="44">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,206.222,561.5) scale(1,1) translate(-3.982e-14,0)" writing-mode="lr" x="206.34" xml:space="preserve" y="566.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127991312388" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,484.889,489.5) scale(1,1) translate(-1.01696e-13,0)" writing-mode="lr" x="485.01" xml:space="preserve" y="494.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127991377924" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1831.89,487.5) scale(1,1) translate(-4.00791e-13,0)" writing-mode="lr" x="1832.01" xml:space="preserve" y="492.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127960117254" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="BusbarSectionClass">
  <g id="1">
   <path class="kv10" d="M 432 534.25 L 1179 534.25" stroke-width="6" zvalue="2238"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674262777861" ObjectName="10kV临时Ⅰ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674262777861"/></metadata>
  <path d="M 432 534.25 L 1179 534.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv10" d="M 1280 534.25 L 1857 534.25" stroke-width="6" zvalue="2240"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674262712325" ObjectName="10kV临时Ⅱ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674262712325"/></metadata>
  <path d="M 1280 534.25 L 1857 534.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="46">
   <use class="kv10" height="42" transform="rotate(0,451,692) scale(1,-1) translate(0,-1384)" width="30" x="436" xlink:href="#Accessory:4卷PT带容断器_0" y="671" zvalue="2291"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450281406469" ObjectName="10kV临时Ⅰ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,451,692) scale(1,-1) translate(0,-1384)" width="30" x="436" y="671"/></g>
  <g id="82">
   <use class="kv10" height="20" transform="rotate(0,573,698) scale(1.6,1.6) translate(-208.875,-255.75)" width="20" x="557" xlink:href="#Accessory:线路PT3_0" y="682" zvalue="2313"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450281799685" ObjectName="1号站用变及工业园区线避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,573,698) scale(1.6,1.6) translate(-208.875,-255.75)" width="20" x="557" y="682"/></g>
  <g id="23">
   <use class="kv10" height="20" transform="rotate(0,674,698) scale(1.6,1.6) translate(-246.75,-255.75)" width="20" x="658" xlink:href="#Accessory:线路PT3_0" y="682" zvalue="2355"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450282258437" ObjectName="平原硅厂线避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,674,698) scale(1.6,1.6) translate(-246.75,-255.75)" width="20" x="658" y="682"/></g>
  <g id="117">
   <use class="kv10" height="20" transform="rotate(0,767,698) scale(1.6,1.6) translate(-281.625,-255.75)" width="20" x="751" xlink:href="#Accessory:线路PT3_0" y="682" zvalue="2399"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450282651653" ObjectName="盈江水泥厂线避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,767,698) scale(1.6,1.6) translate(-281.625,-255.75)" width="20" x="751" y="682"/></g>
  <g id="131">
   <use class="kv10" height="20" transform="rotate(0,856,698) scale(1.6,1.6) translate(-315,-255.75)" width="20" x="840" xlink:href="#Accessory:线路PT3_0" y="682" zvalue="2414"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450282979333" ObjectName="平原糖厂线避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,856,698) scale(1.6,1.6) translate(-315,-255.75)" width="20" x="840" y="682"/></g>
  <g id="163">
   <use class="kv10" height="20" transform="rotate(0,968,697.333) scale(1.6,1.6) translate(-357,-255.5)" width="20" x="952" xlink:href="#Accessory:线路PT3_0" y="681.3333333333333" zvalue="2434"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450285469701" ObjectName="并网、勐腊路及象城线避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,968,697.333) scale(1.6,1.6) translate(-357,-255.5)" width="20" x="952" y="681.3333333333333"/></g>
  <g id="183">
   <use class="kv10" height="20" transform="rotate(0,1113.5,697.75) scale(1.6,1.6) translate(-411.563,-255.656)" width="20" x="1097.5" xlink:href="#Accessory:线路PT3_0" y="681.75" zvalue="2455"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450283700229" ObjectName="永盛路及平胜Ⅰ回线避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1113.5,697.75) scale(1.6,1.6) translate(-411.563,-255.656)" width="20" x="1097.5" y="681.75"/></g>
  <g id="203">
   <use class="kv10" height="20" transform="rotate(0,1293,698) scale(1.6,1.6) translate(-478.875,-255.75)" width="20" x="1277" xlink:href="#Accessory:线路PT3_0" y="682" zvalue="2468"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450284158981" ObjectName="10kV2号站用变避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1293,698) scale(1.6,1.6) translate(-478.875,-255.75)" width="20" x="1277" y="682"/></g>
  <g id="217">
   <use class="kv10" height="20" transform="rotate(0,1398.5,697.75) scale(1.6,1.6) translate(-518.438,-255.656)" width="20" x="1382.5" xlink:href="#Accessory:线路PT3_0" y="681.75" zvalue="2488"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450284355589" ObjectName="岗勐乡及平胜Ⅱ回线避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1398.5,697.75) scale(1.6,1.6) translate(-518.438,-255.656)" width="20" x="1382.5" y="681.75"/></g>
  <g id="234">
   <use class="kv10" height="20" transform="rotate(0,1498,698) scale(1.6,1.6) translate(-555.75,-255.75)" width="20" x="1482" xlink:href="#Accessory:线路PT3_0" y="682" zvalue="2501"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450284748805" ObjectName="10kV目瑙纵歌路线避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1498,698) scale(1.6,1.6) translate(-555.75,-255.75)" width="20" x="1482" y="682"/></g>
  <g id="254">
   <use class="kv10" height="20" transform="rotate(0,1591,698) scale(1.6,1.6) translate(-590.625,-255.75)" width="20" x="1575" xlink:href="#Accessory:线路PT3_0" y="682" zvalue="2516"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450285010949" ObjectName="10kV华盛砖厂线避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1591,698) scale(1.6,1.6) translate(-590.625,-255.75)" width="20" x="1575" y="682"/></g>
  <g id="276">
   <use class="kv10" height="42" transform="rotate(0,1809,692) scale(1,-1) translate(0,-1384)" width="30" x="1794" xlink:href="#Accessory:4卷PT带容断器_0" y="671" zvalue="2541"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450285207558" ObjectName="10kV临时Ⅱ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1809,692) scale(1,-1) translate(0,-1384)" width="30" x="1794" y="671"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="50">
   <use class="kv10" height="75" transform="rotate(0,517,707.5) scale(1,1) translate(0,0)" width="30" x="502" xlink:href="#EnergyConsumer:站用变20230624_0" y="670" zvalue="2293"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450281472005" ObjectName="10kV3号站用变"/>
   <cge:TPSR_Ref TObjectID="6192450281472005"/></metadata>
  <rect fill="white" height="75" opacity="0" stroke="white" transform="rotate(0,517,707.5) scale(1,1) translate(0,0)" width="30" x="502" y="670"/></g>
  <g id="66">
   <use class="kv10" height="30" transform="rotate(0,562,818) scale(1.25,-1.23333) translate(-110.9,-1477.74)" width="12" x="554.5" xlink:href="#EnergyConsumer:负荷_0" y="799.5" zvalue="2299"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450281603077" ObjectName="10kV工业园区线"/>
   <cge:TPSR_Ref TObjectID="6192450281603077"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,562,818) scale(1.25,-1.23333) translate(-110.9,-1477.74)" width="12" x="554.5" y="799.5"/></g>
  <g id="69">
   <use class="kv10" height="30" transform="rotate(0,623.5,818) scale(1.25,-1.23333) translate(-123.2,-1477.74)" width="12" x="616" xlink:href="#EnergyConsumer:负荷_0" y="799.5" zvalue="2301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450281668613" ObjectName="10kV1号站用变"/>
   <cge:TPSR_Ref TObjectID="6192450281668613"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,623.5,818) scale(1.25,-1.23333) translate(-123.2,-1477.74)" width="12" x="616" y="799.5"/></g>
  <g id="38">
   <use class="kv10" height="30" transform="rotate(0,694.167,818) scale(1.25,-1.23333) translate(-137.333,-1477.74)" width="12" x="686.6666666666667" xlink:href="#EnergyConsumer:负荷_0" y="799.5000003178914" zvalue="2361"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450282389509" ObjectName="10kV平原硅厂线"/>
   <cge:TPSR_Ref TObjectID="6192450282389509"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,694.167,818) scale(1.25,-1.23333) translate(-137.333,-1477.74)" width="12" x="686.6666666666667" y="799.5000003178914"/></g>
  <g id="113">
   <use class="kv10" height="30" transform="rotate(0,787.167,818) scale(1.25,-1.23333) translate(-155.933,-1477.74)" width="12" x="779.6666666666667" xlink:href="#EnergyConsumer:负荷_0" y="799.5000003178914" zvalue="2404"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450282455045" ObjectName="10kV盈江水泥厂线"/>
   <cge:TPSR_Ref TObjectID="6192450282455045"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,787.167,818) scale(1.25,-1.23333) translate(-155.933,-1477.74)" width="12" x="779.6666666666667" y="799.5000003178914"/></g>
  <g id="127">
   <use class="kv10" height="30" transform="rotate(0,876.167,818) scale(1.25,-1.23333) translate(-173.733,-1477.74)" width="12" x="868.6666666666667" xlink:href="#EnergyConsumer:负荷_0" y="799.5000003178914" zvalue="2419"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450282782725" ObjectName="10kV平原糖厂线"/>
   <cge:TPSR_Ref TObjectID="6192450282782725"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,876.167,818) scale(1.25,-1.23333) translate(-173.733,-1477.74)" width="12" x="868.6666666666667" y="799.5000003178914"/></g>
  <g id="171">
   <use class="kv10" height="30" transform="rotate(0,939,818) scale(1.25,-1.23333) translate(-186.3,-1477.74)" width="12" x="931.5" xlink:href="#EnergyConsumer:负荷_0" y="799.5" zvalue="2422"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450283438085" ObjectName="10kV并网线"/>
   <cge:TPSR_Ref TObjectID="6192450283438085"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,939,818) scale(1.25,-1.23333) translate(-186.3,-1477.74)" width="12" x="931.5" y="799.5"/></g>
  <g id="170">
   <use class="kv10" height="30" transform="rotate(0,988.5,818) scale(1.25,-1.23333) translate(-196.2,-1477.74)" width="12" x="981" xlink:href="#EnergyConsumer:负荷_0" y="799.5" zvalue="2424"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450283372549" ObjectName="10kV勐腊路线"/>
   <cge:TPSR_Ref TObjectID="6192450283372549"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,988.5,818) scale(1.25,-1.23333) translate(-196.2,-1477.74)" width="12" x="981" y="799.5"/></g>
  <g id="172">
   <use class="kv10" height="30" transform="rotate(0,1037.5,818) scale(1.25,-1.23333) translate(-206,-1477.74)" width="12" x="1030" xlink:href="#EnergyConsumer:负荷_0" y="799.5" zvalue="2440"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450283503621" ObjectName="10kV象城路线"/>
   <cge:TPSR_Ref TObjectID="6192450283503621"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1037.5,818) scale(1.25,-1.23333) translate(-206,-1477.74)" width="12" x="1030" y="799.5"/></g>
  <g id="193">
   <use class="kv10" height="30" transform="rotate(0,1102.5,817.75) scale(1.25,-1.23333) translate(-219,-1477.29)" width="12" x="1095" xlink:href="#EnergyConsumer:负荷_0" y="799.25" zvalue="2443"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450283896837" ObjectName="10kV永盛路线"/>
   <cge:TPSR_Ref TObjectID="6192450283896837"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1102.5,817.75) scale(1.25,-1.23333) translate(-219,-1477.29)" width="12" x="1095" y="799.25"/></g>
  <g id="192">
   <use class="kv10" height="30" transform="rotate(0,1164,817.75) scale(1.25,-1.23333) translate(-231.3,-1477.29)" width="12" x="1156.5" xlink:href="#EnergyConsumer:负荷_0" y="799.25" zvalue="2445"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450283831301" ObjectName="10kV平胜Ⅰ回线"/>
   <cge:TPSR_Ref TObjectID="6192450283831301"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1164,817.75) scale(1.25,-1.23333) translate(-231.3,-1477.29)" width="12" x="1156.5" y="799.25"/></g>
  <g id="199">
   <use class="kv10" height="30" transform="rotate(0,1313.17,818) scale(1.25,-1.23333) translate(-261.133,-1477.74)" width="12" x="1305.666666666667" xlink:href="#EnergyConsumer:负荷_0" y="799.5000003178914" zvalue="2473"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450283962373" ObjectName="10kV2号站用变"/>
   <cge:TPSR_Ref TObjectID="6192450283962373"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1313.17,818) scale(1.25,-1.23333) translate(-261.133,-1477.74)" width="12" x="1305.666666666667" y="799.5000003178914"/></g>
  <g id="225">
   <use class="kv10" height="30" transform="rotate(0,1387.5,817.75) scale(1.25,-1.23333) translate(-276,-1477.29)" width="12" x="1380" xlink:href="#EnergyConsumer:负荷_0" y="799.25" zvalue="2476"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450284486661" ObjectName="10kV岗勐乡线"/>
   <cge:TPSR_Ref TObjectID="6192450284486661"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1387.5,817.75) scale(1.25,-1.23333) translate(-276,-1477.29)" width="12" x="1380" y="799.25"/></g>
  <g id="224">
   <use class="kv10" height="30" transform="rotate(0,1449,817.75) scale(1.25,-1.23333) translate(-288.3,-1477.29)" width="12" x="1441.5" xlink:href="#EnergyConsumer:负荷_0" y="799.25" zvalue="2478"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450284421125" ObjectName="10kV平胜Ⅱ回线"/>
   <cge:TPSR_Ref TObjectID="6192450284421125"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1449,817.75) scale(1.25,-1.23333) translate(-288.3,-1477.29)" width="12" x="1441.5" y="799.25"/></g>
  <g id="230">
   <use class="kv10" height="30" transform="rotate(0,1518.17,818) scale(1.25,-1.23333) translate(-302.133,-1477.74)" width="12" x="1510.666666666667" xlink:href="#EnergyConsumer:负荷_0" y="799.5000003178914" zvalue="2506"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450284552197" ObjectName="10kV目瑙纵歌路线"/>
   <cge:TPSR_Ref TObjectID="6192450284552197"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1518.17,818) scale(1.25,-1.23333) translate(-302.133,-1477.74)" width="12" x="1510.666666666667" y="799.5000003178914"/></g>
  <g id="250">
   <use class="kv10" height="30" transform="rotate(0,1611.17,818) scale(1.25,-1.23333) translate(-320.733,-1477.74)" width="12" x="1603.666666666667" xlink:href="#EnergyConsumer:负荷_0" y="799.5000003178914" zvalue="2521"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450284814341" ObjectName="10kV华盛砖厂线"/>
   <cge:TPSR_Ref TObjectID="6192450284814341"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1611.17,818) scale(1.25,-1.23333) translate(-320.733,-1477.74)" width="12" x="1603.666666666667" y="799.5000003178914"/></g>
  <g id="268">
   <use class="kv10" height="49" transform="rotate(0,1703,796.583) scale(1.5625,1.54762) translate(-606.33,-268.451)" width="24" x="1684.25" xlink:href="#EnergyConsumer:厂用变负荷_0" y="758.666666454739" zvalue="2536"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450285076485" ObjectName="10kV4号站用变"/>
   <cge:TPSR_Ref TObjectID="6192450285076485"/></metadata>
  <rect fill="white" height="49" opacity="0" stroke="white" transform="rotate(0,1703,796.583) scale(1.5625,1.54762) translate(-606.33,-268.451)" width="24" x="1684.25" y="758.666666454739"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="52">
   <path class="kv10" d="M 441.48 671.63 L 441.48 634 L 517.1 634 L 517.1 671.02" stroke-width="1" zvalue="2294"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="50@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 441.48 671.63 L 441.48 634 L 517.1 634 L 517.1 671.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv10" d="M 479.06 634 L 479.06 584.81" stroke-width="1" zvalue="2297"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52" LinkObjectIDznd="54@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 479.06 634 L 479.06 584.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv10" d="M 479.09 563.36 L 479.09 534.25" stroke-width="1" zvalue="2298"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@0" LinkObjectIDznd="1@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 479.09 563.36 L 479.09 534.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv10" d="M 562 801.35 L 562 738 L 623.5 738 L 623.5 801.35" stroke-width="1" zvalue="2302"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="69@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 562 801.35 L 562 738 L 623.5 738 L 623.5 801.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv10" d="M 593.1 738 L 593.1 635.89" stroke-width="1" zvalue="2307"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71" LinkObjectIDznd="74@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 593.1 738 L 593.1 635.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv10" d="M 592.95 610.09 L 592.95 584.81" stroke-width="1" zvalue="2311"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="78@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 592.95 610.09 L 592.95 584.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv10" d="M 592.59 563.36 L 592.59 534.25" stroke-width="1" zvalue="2312"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="1@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 592.59 563.36 L 592.59 534.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv10" d="M 573 684 L 573 672 L 593.1 672" stroke-width="1" zvalue="2314"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="76" MaxPinNum="2"/>
   </metadata>
  <path d="M 573 684 L 573 672 L 593.1 672" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv10" d="M 616.08 692.28 L 616.08 672 L 593.1 672" stroke-width="1" zvalue="2316"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="83" MaxPinNum="2"/>
   </metadata>
  <path d="M 616.08 692.28 L 616.08 672 L 593.1 672" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv10" d="M 1587.43 404.84 L 1587.43 534.25" stroke-width="1" zvalue="2337"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="4@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1587.43 404.84 L 1587.43 534.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv10" d="M 798.5 391.18 L 798.5 534.25" stroke-width="1" zvalue="2340"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 798.5 391.18 L 798.5 534.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv10" d="M 694.17 801.35 L 694.1 635.89" stroke-width="1" zvalue="2349"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="35@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 694.17 801.35 L 694.1 635.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv10" d="M 693.95 610.09 L 693.95 584.81" stroke-width="1" zvalue="2353"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="32@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 693.95 610.09 L 693.95 584.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv10" d="M 693.59 563.36 L 693.59 534.25" stroke-width="1" zvalue="2354"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="1@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 693.59 563.36 L 693.59 534.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv10" d="M 674 684 L 674 672 L 694.11 672" stroke-width="1" zvalue="2356"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="23@0" LinkObjectIDznd="33" MaxPinNum="2"/>
   </metadata>
  <path d="M 674 684 L 674 672 L 694.11 672" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv10" d="M 717.08 692.28 L 717.08 672 L 694.1 672" stroke-width="1" zvalue="2358"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@0" LinkObjectIDznd="22" MaxPinNum="2"/>
   </metadata>
  <path d="M 717.08 692.28 L 717.08 672 L 694.1 672" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv10" d="M 787.17 801.35 L 787.1 635.89" stroke-width="1" zvalue="2393"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="122@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 787.17 801.35 L 787.1 635.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv10" d="M 786.95 610.09 L 786.95 584.81" stroke-width="1" zvalue="2397"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@0" LinkObjectIDznd="120@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 786.95 610.09 L 786.95 584.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv10" d="M 786.59 563.36 L 786.59 534.25" stroke-width="1" zvalue="2398"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="1@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 786.59 563.36 L 786.59 534.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="kv10" d="M 767 684 L 767 672 L 787.11 672" stroke-width="1" zvalue="2400"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="121" MaxPinNum="2"/>
   </metadata>
  <path d="M 767 684 L 767 672 L 787.11 672" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv10" d="M 810.08 692.28 L 810.08 672 L 787.1 672" stroke-width="1" zvalue="2402"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="116" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.08 692.28 L 810.08 672 L 787.1 672" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="kv10" d="M 876.17 801.35 L 876.1 635.89" stroke-width="1" zvalue="2408"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="149@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 876.17 801.35 L 876.1 635.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv10" d="M 875.95 610.09 L 875.95 584.81" stroke-width="1" zvalue="2412"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="149@0" LinkObjectIDznd="134@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 875.95 610.09 L 875.95 584.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="kv10" d="M 875.59 563.36 L 875.59 534.25" stroke-width="1" zvalue="2413"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@0" LinkObjectIDznd="1@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 875.59 563.36 L 875.59 534.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="kv10" d="M 856 684 L 856 672 L 876.11 672" stroke-width="1" zvalue="2415"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="141" MaxPinNum="2"/>
   </metadata>
  <path d="M 856 684 L 856 672 L 876.11 672" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv10" d="M 899.08 692.28 L 899.08 672 L 876.1 672" stroke-width="1" zvalue="2417"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="130" MaxPinNum="2"/>
   </metadata>
  <path d="M 899.08 692.28 L 899.08 672 L 876.1 672" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv10" d="M 939 801.35 L 939 738 L 988.5 738 L 988.5 801.35" stroke-width="1" zvalue="2425"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="170@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 939 801.35 L 939 738 L 988.5 738 L 988.5 801.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv10" d="M 988.1 738 L 988.1 635.23" stroke-width="1" zvalue="2428"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="173" LinkObjectIDznd="168@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 988.1 738 L 988.1 635.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="kv10" d="M 987.95 609.42 L 987.95 584.15" stroke-width="1" zvalue="2432"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@0" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 987.95 609.42 L 987.95 584.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv10" d="M 987.59 562.7 L 987.59 534.25" stroke-width="1" zvalue="2433"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="1@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 987.59 562.7 L 987.59 534.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv10" d="M 968 683.33 L 968 671.33 L 988.1 671.33" stroke-width="1" zvalue="2435"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@0" LinkObjectIDznd="167" MaxPinNum="2"/>
   </metadata>
  <path d="M 968 683.33 L 968 671.33 L 988.1 671.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv10" d="M 1011.08 691.61 L 1011.08 671.33 L 988.1 671.33" stroke-width="1" zvalue="2437"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="162" MaxPinNum="2"/>
   </metadata>
  <path d="M 1011.08 691.61 L 1011.08 671.33 L 988.1 671.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv10" d="M 988.5 738 L 1037.5 738 L 1037.5 801.35" stroke-width="1" zvalue="2441"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169" LinkObjectIDznd="172@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 988.5 738 L 1037.5 738 L 1037.5 801.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="kv10" d="M 1102.5 801.1 L 1102.5 737.75 L 1164 737.75 L 1164 801.1" stroke-width="1" zvalue="2446"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@0" LinkObjectIDznd="192@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1102.5 801.1 L 1102.5 737.75 L 1164 737.75 L 1164 801.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv10" d="M 1133.6 737.75 L 1133.6 635.64" stroke-width="1" zvalue="2449"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191" LinkObjectIDznd="190@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1133.6 737.75 L 1133.6 635.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv10" d="M 1133.45 609.84 L 1133.45 584.56" stroke-width="1" zvalue="2453"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@0" LinkObjectIDznd="186@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1133.45 609.84 L 1133.45 584.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="184">
   <path class="kv10" d="M 1133.09 563.11 L 1133.09 534.25" stroke-width="1" zvalue="2454"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="1@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1133.09 563.11 L 1133.09 534.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv10" d="M 1113.5 683.75 L 1113.5 671.75 L 1133.6 671.75" stroke-width="1" zvalue="2456"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@0" LinkObjectIDznd="187" MaxPinNum="2"/>
   </metadata>
  <path d="M 1113.5 683.75 L 1113.5 671.75 L 1133.6 671.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv10" d="M 1156.58 692.03 L 1156.58 671.75 L 1133.6 671.75" stroke-width="1" zvalue="2458"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="182" MaxPinNum="2"/>
   </metadata>
  <path d="M 1156.58 692.03 L 1156.58 671.75 L 1133.6 671.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="207">
   <path class="kv10" d="M 1313.17 801.35 L 1313.17 633.42" stroke-width="1" zvalue="2462"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@0" LinkObjectIDznd="208@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1313.17 801.35 L 1313.17 633.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv10" d="M 1293 684 L 1293 662.76 L 1313.17 662.76" stroke-width="1" zvalue="2469"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@0" LinkObjectIDznd="207" MaxPinNum="2"/>
   </metadata>
  <path d="M 1293 684 L 1293 662.76 L 1313.17 662.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="kv10" d="M 1336.07 692.28 L 1336.07 662.76 L 1313.1 662.76" stroke-width="1" zvalue="2471"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@0" LinkObjectIDznd="202" MaxPinNum="2"/>
   </metadata>
  <path d="M 1336.07 692.28 L 1336.07 662.76 L 1313.1 662.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="223">
   <path class="kv10" d="M 1387.5 801.1 L 1387.5 737.75 L 1449 737.75 L 1449 801.1" stroke-width="1" zvalue="2479"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="225@0" LinkObjectIDznd="224@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1387.5 801.1 L 1387.5 737.75 L 1449 737.75 L 1449 801.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="kv10" d="M 1417.5 737.75 L 1417.5 633.42" stroke-width="1" zvalue="2482"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223" LinkObjectIDznd="222@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1417.5 737.75 L 1417.5 633.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv10" d="M 1398.5 683.75 L 1398.5 663 L 1417.5 663" stroke-width="1" zvalue="2489"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@0" LinkObjectIDznd="221" MaxPinNum="2"/>
   </metadata>
  <path d="M 1398.5 683.75 L 1398.5 663 L 1417.5 663" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv10" d="M 1441.58 692.03 L 1441.58 663 L 1418 663" stroke-width="1" zvalue="2491"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@0" LinkObjectIDznd="216" MaxPinNum="2"/>
   </metadata>
  <path d="M 1441.58 692.03 L 1441.58 663 L 1418 663" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="kv10" d="M 1518.17 801.35 L 1518.17 633.42" stroke-width="1" zvalue="2495"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="243@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1518.17 801.35 L 1518.17 633.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="kv10" d="M 1498 684 L 1498 663.11 L 1518.17 663.11" stroke-width="1" zvalue="2502"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@0" LinkObjectIDznd="242" MaxPinNum="2"/>
   </metadata>
  <path d="M 1498 684 L 1498 663.11 L 1518.17 663.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="kv10" d="M 1541.07 692.28 L 1541.07 663.11 L 1518.1 663.11" stroke-width="1" zvalue="2504"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="232@0" LinkObjectIDznd="233" MaxPinNum="2"/>
   </metadata>
  <path d="M 1541.07 692.28 L 1541.07 663.11 L 1518.1 663.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="258">
   <path class="kv10" d="M 1611.17 801.35 L 1611.17 633.42" stroke-width="1" zvalue="2510"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@0" LinkObjectIDznd="259@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1611.17 801.35 L 1611.17 633.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="253">
   <path class="kv10" d="M 1591 684 L 1591 663.11 L 1611.17 663.11" stroke-width="1" zvalue="2517"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="254@0" LinkObjectIDznd="258" MaxPinNum="2"/>
   </metadata>
  <path d="M 1591 684 L 1591 663.11 L 1611.17 663.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv10" d="M 1634.07 692.28 L 1634.07 663.11 L 1611.1 663.11" stroke-width="1" zvalue="2519"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252@0" LinkObjectIDznd="253" MaxPinNum="2"/>
   </metadata>
  <path d="M 1634.07 692.28 L 1634.07 663.11 L 1611.1 663.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="260">
   <path class="kv10" d="M 1313.17 586.43 L 1313.17 534.25" stroke-width="1" zvalue="2523"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@0" LinkObjectIDznd="4@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1313.17 586.43 L 1313.17 534.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="261">
   <path class="kv10" d="M 1417.5 586.43 L 1417.5 534.25" stroke-width="1" zvalue="2524"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="222@0" LinkObjectIDznd="4@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1417.5 586.43 L 1417.5 534.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="262">
   <path class="kv10" d="M 1517 586.43 L 1517 534.25" stroke-width="1" zvalue="2525"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243@0" LinkObjectIDznd="4@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517 586.43 L 1517 534.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="263">
   <path class="kv10" d="M 1610 586.43 L 1610 534.25" stroke-width="1" zvalue="2526"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@0" LinkObjectIDznd="4@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1610 586.43 L 1610 534.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="273">
   <path class="kv10" d="M 1703 759.44 L 1703 635.22" stroke-width="1" zvalue="2529"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="274@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1703 759.44 L 1703 635.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="267">
   <path class="kv10" d="M 1702.98 584.81 L 1702.98 534.25" stroke-width="1" zvalue="2538"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="4@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1702.98 584.81 L 1702.98 534.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="281">
   <path class="kv10" d="M 1799.48 671.63 L 1799.48 631.94" stroke-width="1" zvalue="2544"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="279@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1799.48 671.63 L 1799.48 631.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="282">
   <path class="kv10" d="M 1799.33 584.64 L 1799.33 534.25" stroke-width="1" zvalue="2545"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="279@1" LinkObjectIDznd="4@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1799.33 584.64 L 1799.33 534.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="54">
   <use class="kv10" height="30" transform="rotate(0,479,574) scale(1,0.733333) translate(0,204.727)" width="15" x="471.5" xlink:href="#Disconnector:刀闸_0" y="563" zvalue="2296"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450281537541" ObjectName="10kV临时Ⅰ段母线电压互感器0903隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450281537541"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,479,574) scale(1,0.733333) translate(0,204.727)" width="15" x="471.5" y="563"/></g>
  <g id="78">
   <use class="kv10" height="30" transform="rotate(0,592.5,574) scale(1,0.733333) translate(0,204.727)" width="15" x="585" xlink:href="#Disconnector:刀闸_0" y="563" zvalue="2310"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450281734149" ObjectName="10kV1号站用变及工业园区线0511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450281734149"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,592.5,574) scale(1,0.733333) translate(0,204.727)" width="15" x="585" y="563"/></g>
  <g id="32">
   <use class="kv10" height="30" transform="rotate(0,693.5,574) scale(1,0.733333) translate(0,204.727)" width="15" x="686" xlink:href="#Disconnector:刀闸_0" y="563" zvalue="2351"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450282323973" ObjectName="10kV平原硅厂线0521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450282323973"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,693.5,574) scale(1,0.733333) translate(0,204.727)" width="15" x="686" y="563"/></g>
  <g id="120">
   <use class="kv10" height="30" transform="rotate(0,786.5,574) scale(1,0.733333) translate(0,204.727)" width="15" x="779" xlink:href="#Disconnector:刀闸_0" y="563" zvalue="2395"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450282717189" ObjectName="10kV盈江水泥厂线0531隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450282717189"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,786.5,574) scale(1,0.733333) translate(0,204.727)" width="15" x="779" y="563"/></g>
  <g id="134">
   <use class="kv10" height="30" transform="rotate(0,875.5,574) scale(1,0.733333) translate(0,204.727)" width="15" x="868" xlink:href="#Disconnector:刀闸_0" y="563" zvalue="2410"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450283044869" ObjectName="10kV平原糖厂线0541隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450283044869"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,875.5,574) scale(1,0.733333) translate(0,204.727)" width="15" x="868" y="563"/></g>
  <g id="166">
   <use class="kv10" height="30" transform="rotate(0,987.5,573.333) scale(1,0.733333) translate(0,204.485)" width="15" x="980" xlink:href="#Disconnector:刀闸_0" y="562.3333333333334" zvalue="2430"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450285535238" ObjectName="10kV并网、勐腊路及象城线0551隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450285535238"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,987.5,573.333) scale(1,0.733333) translate(0,204.485)" width="15" x="980" y="562.3333333333334"/></g>
  <g id="186">
   <use class="kv10" height="30" transform="rotate(0,1133,573.75) scale(1,0.733333) translate(0,204.636)" width="15" x="1125.5" xlink:href="#Disconnector:刀闸_0" y="562.75" zvalue="2451"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450283765765" ObjectName="10kV永盛路及平胜Ⅰ回线0561隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450283765765"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1133,573.75) scale(1,0.733333) translate(0,204.636)" width="15" x="1125.5" y="562.75"/></g>
  <g id="274">
   <use class="kv10" height="33" transform="rotate(0,1703,610.25) scale(2.04365,1.56061) translate(-862.382,-209.966)" width="14" x="1688.694444444444" xlink:href="#Disconnector:手车隔离开关13_0" y="584.5" zvalue="2528"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450285142021" ObjectName="10kV4号站用变0622断路器"/>
   <cge:TPSR_Ref TObjectID="6192450285142021"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,1703,610.25) scale(2.04365,1.56061) translate(-862.382,-209.966)" width="14" x="1688.694444444444" y="584.5"/></g>
  <g id="279">
   <use class="kv10" height="26" transform="rotate(0,1799.33,608.25) scale(1.82692,1.82692) translate(-809.47,-264.563)" width="12" x="1788.364002378537" xlink:href="#Disconnector:小车隔刀熔断器_0" y="584.5" zvalue="2543"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450285273094" ObjectName="10kV临时Ⅱ段母线电压互感器0904隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450285273094"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1799.33,608.25) scale(1.82692,1.82692) translate(-809.47,-264.563)" width="12" x="1788.364002378537" y="584.5"/></g>
 </g>
 <g id="BreakerClass">
  <g id="74">
   <use class="kv10" height="20" transform="rotate(0,593,623) scale(1.5,1.35) translate(-195.167,-158.019)" width="10" x="585.5" xlink:href="#Breaker:开关_0" y="609.5" zvalue="2306"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924590632964" ObjectName="10kV1号站用变及工业园区线051断路器"/>
   <cge:TPSR_Ref TObjectID="6473924590632964"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,593,623) scale(1.5,1.35) translate(-195.167,-158.019)" width="10" x="585.5" y="609.5"/></g>
  <g id="35">
   <use class="kv10" height="20" transform="rotate(0,694,623) scale(1.5,1.35) translate(-228.833,-158.019)" width="10" x="686.5" xlink:href="#Breaker:开关_0" y="609.5" zvalue="2348"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924590698500" ObjectName="10kV平原硅厂线052断路器"/>
   <cge:TPSR_Ref TObjectID="6473924590698500"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,694,623) scale(1.5,1.35) translate(-228.833,-158.019)" width="10" x="686.5" y="609.5"/></g>
  <g id="122">
   <use class="kv10" height="20" transform="rotate(0,787,623) scale(1.5,1.35) translate(-259.833,-158.019)" width="10" x="779.5" xlink:href="#Breaker:开关_0" y="609.5" zvalue="2392"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924590764036" ObjectName="10kV盈江水泥厂线053断路器"/>
   <cge:TPSR_Ref TObjectID="6473924590764036"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,787,623) scale(1.5,1.35) translate(-259.833,-158.019)" width="10" x="779.5" y="609.5"/></g>
  <g id="149">
   <use class="kv10" height="20" transform="rotate(0,876,623) scale(1.5,1.35) translate(-289.5,-158.019)" width="10" x="868.5" xlink:href="#Breaker:开关_0" y="609.5" zvalue="2407"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924590829572" ObjectName="10kV平原糖厂线054断路器"/>
   <cge:TPSR_Ref TObjectID="6473924590829572"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,876,623) scale(1.5,1.35) translate(-289.5,-158.019)" width="10" x="868.5" y="609.5"/></g>
  <g id="168">
   <use class="kv10" height="20" transform="rotate(0,988,622.333) scale(1.5,1.35) translate(-326.833,-157.846)" width="10" x="980.5" xlink:href="#Breaker:开关_0" y="608.8333333333334" zvalue="2427"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924591288324" ObjectName="10kV并网、勐腊路及象城线055断路器"/>
   <cge:TPSR_Ref TObjectID="6473924591288324"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,988,622.333) scale(1.5,1.35) translate(-326.833,-157.846)" width="10" x="980.5" y="608.8333333333334"/></g>
  <g id="190">
   <use class="kv10" height="20" transform="rotate(0,1133.5,622.75) scale(1.5,1.35) translate(-375.333,-157.954)" width="10" x="1126" xlink:href="#Breaker:开关_0" y="609.25" zvalue="2448"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924590960644" ObjectName="10kV永盛路及平胜Ⅰ回线056断路器"/>
   <cge:TPSR_Ref TObjectID="6473924590960644"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1133.5,622.75) scale(1.5,1.35) translate(-375.333,-157.954)" width="10" x="1126" y="609.25"/></g>
  <g id="208">
   <use class="kv10" height="20" transform="rotate(0,1313.17,610.25) scale(2.86111,2.575) translate(-844.89,-357.51)" width="10" x="1298.861111111111" xlink:href="#Breaker:小车断路器_0" y="584.5" zvalue="2461"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924591026180" ObjectName="10kV2号站用变057断路器"/>
   <cge:TPSR_Ref TObjectID="6473924591026180"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1313.17,610.25) scale(2.86111,2.575) translate(-844.89,-357.51)" width="10" x="1298.861111111111" y="584.5"/></g>
  <g id="222">
   <use class="kv10" height="20" transform="rotate(0,1417.5,610.25) scale(2.86111,2.575) translate(-912.758,-357.51)" width="10" x="1403.194444444444" xlink:href="#Breaker:小车断路器_0" y="584.5" zvalue="2481"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924591091716" ObjectName="10kV岗勐乡及平胜Ⅱ回线058断路器"/>
   <cge:TPSR_Ref TObjectID="6473924591091716"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1417.5,610.25) scale(2.86111,2.575) translate(-912.758,-357.51)" width="10" x="1403.194444444444" y="584.5"/></g>
  <g id="243">
   <use class="kv10" height="20" transform="rotate(0,1517,610.25) scale(2.86111,2.575) translate(-977.481,-357.51)" width="10" x="1502.694444444444" xlink:href="#Breaker:小车断路器_0" y="584.5" zvalue="2494"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924591157252" ObjectName="10kV目瑙纵歌路线059断路器"/>
   <cge:TPSR_Ref TObjectID="6473924591157252"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1517,610.25) scale(2.86111,2.575) translate(-977.481,-357.51)" width="10" x="1502.694444444444" y="584.5"/></g>
  <g id="259">
   <use class="kv10" height="20" transform="rotate(0,1610,610.25) scale(2.86111,2.575) translate(-1037.98,-357.51)" width="10" x="1595.694444444444" xlink:href="#Breaker:小车断路器_0" y="584.5" zvalue="2509"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924591222788" ObjectName="10kV华盛砖厂线061断路器"/>
   <cge:TPSR_Ref TObjectID="6473924591222788"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1610,610.25) scale(2.86111,2.575) translate(-1037.98,-357.51)" width="10" x="1595.694444444444" y="584.5"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="84">
   <use class="kv10" height="20" transform="rotate(0,616,703) scale(1.5,1.1) translate(-202.833,-62.9091)" width="10" x="608.5" xlink:href="#GroundDisconnector:地刀_0" y="692" zvalue="2315"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450281930757" ObjectName="10kV1号站用变及工业园区线05167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450281930757"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,616,703) scale(1.5,1.1) translate(-202.833,-62.9091)" width="10" x="608.5" y="692"/></g>
  <g id="21">
   <use class="kv10" height="20" transform="rotate(0,717,703) scale(1.5,1.1) translate(-236.5,-62.9091)" width="10" x="709.5" xlink:href="#GroundDisconnector:地刀_0" y="692" zvalue="2357"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450282192901" ObjectName="10kV平原硅厂线05267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450282192901"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,717,703) scale(1.5,1.1) translate(-236.5,-62.9091)" width="10" x="709.5" y="692"/></g>
  <g id="115">
   <use class="kv10" height="20" transform="rotate(0,810,703) scale(1.5,1.1) translate(-267.5,-62.9091)" width="10" x="802.5" xlink:href="#GroundDisconnector:地刀_0" y="692" zvalue="2401"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450282586117" ObjectName="10kV盈江水泥厂线05367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450282586117"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,810,703) scale(1.5,1.1) translate(-267.5,-62.9091)" width="10" x="802.5" y="692"/></g>
  <g id="129">
   <use class="kv10" height="20" transform="rotate(0,899,703) scale(1.5,1.1) translate(-297.167,-62.9091)" width="10" x="891.5" xlink:href="#GroundDisconnector:地刀_0" y="692" zvalue="2416"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450282913797" ObjectName="10kV平原糖厂线05467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450282913797"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,899,703) scale(1.5,1.1) translate(-297.167,-62.9091)" width="10" x="891.5" y="692"/></g>
  <g id="161">
   <use class="kv10" height="20" transform="rotate(0,1011,702.333) scale(1.5,1.1) translate(-334.5,-62.8485)" width="10" x="1003.5" xlink:href="#GroundDisconnector:地刀_0" y="691.3333333333333" zvalue="2436"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450285404166" ObjectName="10kV并网、勐腊路及象城线05567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450285404166"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1011,702.333) scale(1.5,1.1) translate(-334.5,-62.8485)" width="10" x="1003.5" y="691.3333333333333"/></g>
  <g id="181">
   <use class="kv10" height="20" transform="rotate(0,1156.5,702.75) scale(1.5,1.1) translate(-383,-62.8864)" width="10" x="1149" xlink:href="#GroundDisconnector:地刀_0" y="691.75" zvalue="2457"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450283634693" ObjectName="10kV永盛路及平胜Ⅰ回线05667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450283634693"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1156.5,702.75) scale(1.5,1.1) translate(-383,-62.8864)" width="10" x="1149" y="691.75"/></g>
  <g id="201">
   <use class="kv10" height="20" transform="rotate(0,1336,703) scale(1.5,1.1) translate(-442.833,-62.9091)" width="10" x="1328.5" xlink:href="#GroundDisconnector:地刀_0" y="692" zvalue="2470"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450284093445" ObjectName="10kV2号站用变05767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450284093445"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1336,703) scale(1.5,1.1) translate(-442.833,-62.9091)" width="10" x="1328.5" y="692"/></g>
  <g id="215">
   <use class="kv10" height="20" transform="rotate(0,1441.5,702.75) scale(1.5,1.1) translate(-478,-62.8864)" width="10" x="1434" xlink:href="#GroundDisconnector:地刀_0" y="691.75" zvalue="2490"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450284290053" ObjectName="10kV岗勐乡及平胜Ⅱ回线05867接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450284290053"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1441.5,702.75) scale(1.5,1.1) translate(-478,-62.8864)" width="10" x="1434" y="691.75"/></g>
  <g id="232">
   <use class="kv10" height="20" transform="rotate(0,1541,703) scale(1.5,1.1) translate(-511.167,-62.9091)" width="10" x="1533.5" xlink:href="#GroundDisconnector:地刀_0" y="692" zvalue="2503"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450284683269" ObjectName="10kV目瑙纵歌路线05967接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450284683269"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1541,703) scale(1.5,1.1) translate(-511.167,-62.9091)" width="10" x="1533.5" y="692"/></g>
  <g id="252">
   <use class="kv10" height="20" transform="rotate(0,1634,703) scale(1.5,1.1) translate(-542.167,-62.9091)" width="10" x="1626.5" xlink:href="#GroundDisconnector:地刀_0" y="692" zvalue="2518"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450284945413" ObjectName="10kV华盛砖厂线06167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450284945413"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1634,703) scale(1.5,1.1) translate(-542.167,-62.9091)" width="10" x="1626.5" y="692"/></g>
 </g>
</svg>