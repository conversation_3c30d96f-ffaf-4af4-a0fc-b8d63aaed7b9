<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549587935234" height="1045" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1045" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:Y-Y站用_0" viewBox="0,0,17,26">
   <use terminal-index="0" type="0" x="8.666666666666666" xlink:href="#terminal" y="0.1666666666666714"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.597025948103791" x2="8.597025948103791" y1="1.79293756914176" y2="0.2382945839350814"/>
   <ellipse cx="8.42" cy="8.35" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.5" cy="18.92" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.611718118722354" x2="8.611718118722354" y1="16.35874008086165" y2="18.95772818087859"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.2052656081938" x2="8.611718118722351" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.018170629250881" x2="8.611718118722335" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.611718118722354" x2="8.611718118722354" y1="5.503560677018262" y2="8.102548777035198"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.2052656081938" x2="8.611718118722351" y1="10.70153687705211" y2="8.102548777035178"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.018170629250881" x2="8.611718118722335" y1="10.70153687705211" y2="8.102548777035178"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:电压互感器20210927_0" viewBox="0,0,40,51">
   <use terminal-index="0" type="0" x="20.12011246003193" xlink:href="#terminal" y="50.57408113958125"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.73049710944126" x2="34.83187388292585" y1="15.34244615561894" y2="20.8493300230419"/>
   <rect fill-opacity="0" height="17.07" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,33.77,15.98) scale(1,-1) translate(0,-699.43)" width="7.71" x="29.91" y="7.45"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.73049710944125" x2="32.62912033595666" y1="15.34244615561894" y2="20.8493300230419"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.04547346298002" x2="19.04547346298002" y1="16.44382292910353" y2="12.03831583516516"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.04547346298002" x2="21.24822700994921" y1="16.44382292910353" y2="15.34244615561894"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.86983709943278" x2="15.86983709943278" y1="23.84793483977311" y2="43.94806095586693"/>
   <rect fill-opacity="0" height="9.91" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.87,32.93) scale(1,1) translate(0,0)" width="5.51" x="13.12" y="27.98"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.7780557016424" x2="33.76720966855741" y1="43.94806095586693" y2="43.94806095586693"/>
   <path d="M 12.015 14.4862 L 6.50813 14.4862 L 6.50813 26.6014" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.76720966855741" x2="33.76720966855741" y1="43.94806095586693" y2="15.03692065189637"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="20" y1="43.94806095586693" y2="50.55632159677449"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.04547346298002" x2="21.24822700994921" y1="12.03831583516516" y2="13.13969260864975"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.551939044133071" x2="11.46433000549441" y1="36.23842354147478" y2="27.42740935359804"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.68948948017626" x2="33.68948948017626" y1="7.44924594564602" y2="3.981873611425563"/>
   <ellipse cx="15.93" cy="19.44" fill-opacity="0" rx="4.59" ry="4.59" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.92504625192508" x2="31.2146606161194" y1="3.907080727688545" y2="3.907080727688545"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.00723227402126" x2="32.13247459402323" y1="2.530359760832809" y2="2.530359760832809"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.36476248948858" x2="32.77494437855592" y1="1.15363879397707" y2="1.15363879397707"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.90552346468356" x2="13.26221920832054" y1="19.67000481372812" y2="21.03300260875202"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.90552346468357" x2="15.90552346468357" y1="16.94400922368032" y2="19.6700048137281"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.90552346468357" x2="18.54882772104659" y1="19.67000481372812" y2="21.03300260875202"/>
   <ellipse cx="12.26" cy="14.21" fill-opacity="0" rx="4.59" ry="4.59" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.23426755306826" x2="12.23426755306826" y1="11.7124695496285" y2="14.43846513967629"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.23426755306826" x2="14.87757180943128" y1="14.43846513967631" y2="15.8014629347002"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.23426755306825" x2="9.590963296705237" y1="14.43846513967631" y2="15.8014629347002"/>
   <ellipse cx="19.61" cy="14.3" fill-opacity="0" rx="4.59" ry="4.59" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <rect fill-opacity="0" height="9.779999999999999" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6.65,31.37) scale(1,1) translate(0,0)" width="5.78" x="3.75" y="26.48"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.587516480213441" x2="6.587516480213441" y1="36.23295913719932" y2="38.44117708844396"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.847578158800184" x2="5.148000732395751" y1="38.29434019027886" y2="38.29434019027886"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.321568169818923" x2="5.674010721377011" y1="39.08335517375075" y2="39.08335517375075"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.953361177532043" x2="6.042217713663891" y1="39.87237015722263" y2="39.87237015722263"/>
  </symbol>
  <symbol id="Compensator:西郊变电容_0" viewBox="0,0,24,40">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="2.449999999999999"/>
   <rect fill-opacity="0" height="5.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12,23) scale(1,1) translate(0,0)" width="3" x="10.5" y="20.17"/>
   <path d="M 12 17.85 L 17.85 17.85 L 17.85 22.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="6.5" y1="33.10833333333333" y2="33.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.471296296296289" x2="6.471296296296289" y1="30.88611111111111" y2="33.12685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.50833333333333" x2="6.50833333333333" y1="20.10833333333333" y2="17.90462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="29.60833333333333" y2="33.12685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="22.60833333333333" y2="28.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="17.79351851851851" y2="22.525"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.94090594744122" x2="6.94090594744122" y1="36.94166666666666" y2="34.94166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.416666666666666" x2="12.00833333333334" y1="17.85833333333333" y2="17.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="33.10833333333333" y2="37.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.09166666666667" x2="17.09166666666667" y1="34.94166666666666" y2="36.94166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.758333333333324" x2="2.758333333333324" y1="19.85833333333333" y2="31.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.08333333333333" x2="7.000000000000002" y1="37.10833333333333" y2="37.10833333333333"/>
   <path d="M 6.50833 23.7072 A 2.96392 1.81747 180 0 1 6.50833 20.0723" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.50833 27.3421 A 2.96392 1.81747 180 0 1 6.50833 23.7072" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.50833 30.8771 A 2.96392 1.81747 180 0 1 6.50833 27.2421" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="29.60833333333333" y2="29.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="28.60833333333333" y2="28.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="12" y1="2.25" y2="4.916666666666668"/>
   <path d="M 7.26667 9.85 A 4.91667 4.75 -450 1 0 12.0167 4.93333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 7.33333 9.83333 L 12 9.91667 L 12 18" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="20.56666666666668" y2="21.08015580397416"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.28450340888154" x2="18.47116270499357" y1="26.7156109584975" y2="26.7156109584975"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510778" x2="17.14262023217246" y1="23.75922956383932" y2="22.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.50700202690252" x2="18.32283029297954" y1="27.0857461490052" y2="27.0857461490052"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="25.36667381975841" y2="26.33114037330986"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.98783858485353" x2="18.76782752902158" y1="26.34547576798984" y2="26.34547576798984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="21.08015580397418" y2="23.73243882624067"/>
   <rect fill-opacity="0" height="4.29" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,17.83,23.22) scale(1,1) translate(0,0)" width="2.33" x="16.67" y="21.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510781" x2="18.53565505804314" y1="23.75922956383932" y2="22.95550743587977"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="1"/>
   <ellipse cx="14.99" cy="7.93" fill-opacity="0" rx="7.43" ry="6.85" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.93" cy="17.56" fill-opacity="0" rx="7.43" ry="6.85" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.9834232682896" x2="14.9834232682896" y1="14.95787681993586" y2="18.2114918126664"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.98342326828961" x2="11.56276000329148" y1="18.23128772350278" y2="20.61950731913853"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00726151837816" x2="18.51522958638884" y1="18.19641261556525" y2="20.42154821077479"/>
   <path d="M 11.6895 9.29402 L 18.7806 9.29402 L 15.2653 3.08877 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:接地站用变两卷_0" viewBox="0,0,18,30">
   <use terminal-index="0" type="0" x="8.949999999999999" xlink:href="#terminal" y="0.5"/>
   <ellipse cx="9.039999999999999" cy="8.970000000000001" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.970000000000001" cy="21.14" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.035256190670173" x2="9.035256190670173" y1="4.6" y2="8.708948332339869"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.035256190670172" x2="5.05" y1="8.733948332339875" y2="11.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.063029030724982" x2="13.15" y1="8.689905009168513" y2="11.5"/>
   <path d="M 9 19.8333 L 4.08333 25.4167 L 14.0833 25.4167 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV景罕糖厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1045" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="60.19" y="313.25" zvalue="534"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="4" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,96.625,325.25) scale(1,1) translate(0,0)" width="72.88" x="60.19" y="313.25" zvalue="534"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,96.625,325.25) scale(1,1) translate(0,0)" writing-mode="lr" x="96.63" xml:space="preserve" y="329.75" zvalue="534">信号一览</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="273.81" x="55.44" xlink:href="logo.png" y="44.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,192.347,74.75) scale(1,1) translate(-1.23111e-14,0)" writing-mode="lr" x="192.35" xml:space="preserve" y="78.25" zvalue="1119"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,185.833,72.8153) scale(1,1) translate(2.25375e-14,0)" writing-mode="lr" x="185.83" xml:space="preserve" y="81.81999999999999" zvalue="1120">10kV景罕糖厂</text>
  <line fill="none" id="130" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="375" x2="375" y1="49.5" y2="1039.5" zvalue="210"/>
  <line fill="none" id="136" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.47692454998014" x2="318.0881846035993" y1="169.1279458827686" y2="169.1279458827686" zvalue="501"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="928.0627909390723" y2="928.0627909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="979.4022909390724" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="41.57142857142856" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0114285714285" x2="113.0114285714285" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="347.5719285714285" y1="928.0627909390723" y2="928.0627909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="347.5719285714285" y1="979.4022909390724" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="113.0119285714286" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.5719285714285" x2="347.5719285714285" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="41.57142857142856" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0114285714285" x2="113.0114285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="113.0119285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8093285714285" x2="265.8093285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="265.8092285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.5713285714286" x2="347.5713285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="41.57142857142856" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0114285714285" x2="113.0114285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="113.0119285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8093285714285" x2="265.8093285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="265.8092285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.5713285714286" x2="347.5713285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="134" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,188.624,957.636) scale(1,1) translate(-1.04198e-14,1.05117e-13)" writing-mode="lr" x="46.93" xml:space="preserve" y="963.64" zvalue="503">参考图号     JingHan-01-2017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="109" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,119.149,995.005) scale(1,1) translate(5.03187e-14,-1.52972e-12)" writing-mode="lr" x="56.65" xml:space="preserve" y="1001" zvalue="504">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="98" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,270.875,996.005) scale(1,1) translate(-4.30209e-14,1.09377e-13)" writing-mode="lr" x="193.75" xml:space="preserve" y="1002" zvalue="505">绘制日期    20210927</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.2423,1024.56) scale(1,1) translate(-4.58202e-14,1.01293e-12)" writing-mode="lr" x="70.23999999999999" xml:space="preserve" y="1030.56" zvalue="506">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="96" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,270.746,1022.56) scale(1,1) translate(0,1.12325e-13)" writing-mode="lr" x="193.92" xml:space="preserve" y="1028.56" zvalue="507">更新日期    </text>
  <line fill="none" id="91" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="40.85611355802348" x2="345.4673736116426" y1="623.8945306693694" y2="623.8945306693694" zvalue="508"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.9207,640.936) scale(1,1) translate(2.94653e-15,-1.38164e-13)" writing-mode="lr" x="83.92070806204504" xml:space="preserve" y="645.4363811688671" zvalue="510">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="172.3571428571429" y2="172.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="172.3571428571429" y2="172.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="290.8571428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="290.8571428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" x="130.578125" xml:space="preserve" y="467.359375" zvalue="513">35kV     母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="130.578125" xml:space="preserve" y="483.359375" zvalue="513">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.97,327.199) scale(1,1) translate(0,0)" writing-mode="lr" x="199.97" xml:space="preserve" y="331.7" zvalue="514">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,304.97,327.199) scale(1,1) translate(0,0)" writing-mode="lr" x="304.97" xml:space="preserve" y="331.7" zvalue="515">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,502.857) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="507.3571428571429" zvalue="517">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,528.357) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="532.8571428571429" zvalue="518">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,553.857) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="558.3571428571429" zvalue="519">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,579.357) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="583.8571428571429" zvalue="520">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,604.857) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="609.3571428571429" zvalue="521">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,40.5714,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="40.57" xml:space="preserve" y="191.86" zvalue="522">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.821,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="226.82" xml:space="preserve" y="191.86" zvalue="523">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.2589,210.607) scale(1,1) translate(0,0)" writing-mode="lr" x="44.26" xml:space="preserve" y="215.11" zvalue="524">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,445.75,488) scale(1,1) translate(0,0)" writing-mode="lr" x="445.75" xml:space="preserve" y="492.5" zvalue="662">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,513,165.5) scale(1,1) translate(0,0)" writing-mode="lr" x="513" xml:space="preserve" y="170" zvalue="664">10kV景罕糖厂Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,540.95,305.5) scale(1,1) translate(5.86087e-14,0)" writing-mode="lr" x="540.95" xml:space="preserve" y="310" zvalue="700">042</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,498,763) scale(1,1) translate(0,0)" writing-mode="lr" x="498" xml:space="preserve" y="767.5" zvalue="710">#2压榨变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="151" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,777.5,761) scale(1,1) translate(0,0)" writing-mode="lr" x="777.5" xml:space="preserve" y="765.5" zvalue="779">6000kW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,773.75,743) scale(1,1) translate(0,0)" writing-mode="lr" x="773.75" xml:space="preserve" y="747.5" zvalue="781">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,538.156,345.5) scale(1,1) translate(0,0)" writing-mode="lr" x="538.16" xml:space="preserve" y="350" zvalue="874">0421</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,541.073,431) scale(1,1) translate(5.86224e-14,0)" writing-mode="lr" x="541.0700000000001" xml:space="preserve" y="435.5" zvalue="878">041</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,537,385.5) scale(1,1) translate(0,0)" writing-mode="lr" x="537" xml:space="preserve" y="390" zvalue="883">0416</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,541,476.5) scale(1,1) translate(0,0)" writing-mode="lr" x="541" xml:space="preserve" y="481" zvalue="887">0411</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,566,763) scale(1,1) translate(0,0)" writing-mode="lr" x="566" xml:space="preserve" y="767.5" zvalue="897">#1压榨变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,634,763) scale(1,1) translate(0,0)" writing-mode="lr" x="634" xml:space="preserve" y="767.5" zvalue="905">制炼变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,702,763) scale(1,1) translate(0,0)" writing-mode="lr" x="702" xml:space="preserve" y="767.5" zvalue="913">取水变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" x="604.96875" xml:space="preserve" y="356.5" zvalue="926">10kVⅠ段母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="604.96875" xml:space="preserve" y="372.5" zvalue="926">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,630.5,478.5) scale(1,1) translate(0,0)" writing-mode="lr" x="630.5" xml:space="preserve" y="483" zvalue="928">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,705.073,431) scale(1,1) translate(7.683e-14,0)" writing-mode="lr" x="705.0700000000001" xml:space="preserve" y="435.5" zvalue="932">043</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,693,476.5) scale(1,1) translate(0,0)" writing-mode="lr" x="693" xml:space="preserve" y="481" zvalue="936">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" x="676.375" xml:space="preserve" y="321.5" zvalue="939">10kV1号电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="676.375" xml:space="preserve" y="337.5" zvalue="939">容器</text>
  
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,747,334.5) scale(1,1) translate(0,0)" writing-mode="lr" x="747" xml:space="preserve" y="339" zvalue="947">切撕机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,807,334.5) scale(1,1) translate(0,0)" writing-mode="lr" x="807" xml:space="preserve" y="339" zvalue="954">#2热电变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,866,335.5) scale(1,1) translate(0,0)" writing-mode="lr" x="866" xml:space="preserve" y="340" zvalue="962">#1热电变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,940.5,499) scale(1,1) translate(0,0)" writing-mode="lr" x="940.5" xml:space="preserve" y="503.5" zvalue="966">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1330,498) scale(1,1) translate(0,0)" writing-mode="lr" x="1330" xml:space="preserve" y="502.5" zvalue="968">10kVⅢ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,869,607) scale(1,1) translate(0,0)" writing-mode="lr" x="869" xml:space="preserve" y="611.5" zvalue="970">012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,859,552.5) scale(1,1) translate(0,0)" writing-mode="lr" x="859" xml:space="preserve" y="557" zvalue="971">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,860.539,653.5) scale(1,1) translate(0,0)" writing-mode="lr" x="860.54" xml:space="preserve" y="658" zvalue="975">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1006,588.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1006" xml:space="preserve" y="593" zvalue="976">021</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,920.5,661) scale(1,1) translate(0,0)" writing-mode="lr" x="920.5" xml:space="preserve" y="665.5" zvalue="980">10kV分段</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1012.34,339) scale(1,1) translate(1.10986e-13,0)" writing-mode="lr" x="1012.34" xml:space="preserve" y="343.5" zvalue="986">046</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1012.5,388.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1012.5" xml:space="preserve" y="393" zvalue="987">0462</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1015,456.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1015" xml:space="preserve" y="461" zvalue="991">045</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,774.5,241) scale(1,1) translate(0,0)" writing-mode="lr" x="774.5" xml:space="preserve" y="245.5" zvalue="995">10kV景罕糖厂内联线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1061,705.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1061" xml:space="preserve" y="710" zvalue="1000">压榨Ⅰ回</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="171" stroke="rgb(255,255,255)" text-anchor="middle" x="1140.953125" xml:space="preserve" y="672.5" zvalue="1004">10kVⅡ段母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="171" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1140.953125" xml:space="preserve" y="688.5" zvalue="1004">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="170" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1162,553.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1162" xml:space="preserve" y="558" zvalue="1007">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1248,588.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1248" xml:space="preserve" y="593" zvalue="1013">023</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1100,323.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1100" xml:space="preserve" y="328" zvalue="1016">污水系统</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1200.48,323.5) scale(1,1) translate(7.85024e-13,0)" writing-mode="lr" x="1200.48" xml:space="preserve" y="328" zvalue="1018">热电Ⅰ回</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1284.5,620) scale(1,1) translate(0,0)" writing-mode="lr" x="1284.5" xml:space="preserve" y="624.5" zvalue="1027">10kV分段</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1413.5,761) scale(1,1) translate(0,0)" writing-mode="lr" x="1413.5" xml:space="preserve" y="765.5" zvalue="1029">6000kW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="196" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1409.75,743) scale(1,1) translate(0,0)" writing-mode="lr" x="1409.75" xml:space="preserve" y="747.5" zvalue="1031">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1433,585.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1433" xml:space="preserve" y="590" zvalue="1041">047</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="220" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1489.5,761) scale(1,1) translate(0,0)" writing-mode="lr" x="1489.5" xml:space="preserve" y="765.5" zvalue="1046">6000kW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="213" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1485.75,743) scale(1,1) translate(0,0)" writing-mode="lr" x="1485.75" xml:space="preserve" y="747.5" zvalue="1048">#3发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="212" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1510.5,585.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.5" xml:space="preserve" y="590" zvalue="1051">048</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="224" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1589.5,585.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1589.5" xml:space="preserve" y="590" zvalue="1057">049</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="223" stroke="rgb(255,255,255)" text-anchor="middle" x="1562.6875" xml:space="preserve" y="732.1614583333334" zvalue="1059">10kV2号电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="223" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1562.6875" xml:space="preserve" y="748.1614583333334" zvalue="1059">容器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="middle" x="1380.953125" xml:space="preserve" y="356.5" zvalue="1063">10kVⅢ段母线电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1380.953125" xml:space="preserve" y="372.5" zvalue="1063">压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="227" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1408,478.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1408" xml:space="preserve" y="483" zvalue="1066">0903</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="233" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1464.48,323.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1464.48" xml:space="preserve" y="328" zvalue="1070">锅炉房</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1544.48,323.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1544.48" xml:space="preserve" y="328" zvalue="1076">热电Ⅱ回</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="243" stroke="rgb(255,255,255)" text-anchor="middle" x="1627.75" xml:space="preserve" y="316.75" zvalue="1083">制炼循环水站</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="243" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1627.75" xml:space="preserve" y="332.75" zvalue="1083">Ⅱ回</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="248" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1711.75,319.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1711.75" xml:space="preserve" y="323.75" zvalue="1089">压榨Ⅱ回</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="254" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1660.78,743) scale(1,1) translate(0,0)" writing-mode="lr" x="1660.78" xml:space="preserve" y="747.5" zvalue="1095">制炼澄清车间</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="268" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1750.78,742.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1750.78" xml:space="preserve" y="747" zvalue="1104">制炼成糖车间</text>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="51">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="51" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,127.571,210.468) scale(1,1) translate(0,0)" writing-mode="lr" x="127.72" xml:space="preserve" y="216.9" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="8" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,127.571,186.5) scale(1,1) translate(0,0)" writing-mode="lr" x="127.72" xml:space="preserve" y="192.93" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125492752388" ObjectName=""/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="13" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,304.571,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="304.72" xml:space="preserve" y="192.79" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125492817924" ObjectName=""/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,125.071,528.093) scale(1,1) translate(0,0)" writing-mode="lr" x="125.2" xml:space="preserve" y="533" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="143" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,125.071,553.218) scale(1,1) translate(0,-1.20508e-13)" writing-mode="lr" x="125.2" xml:space="preserve" y="558.13" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,125.071,578.343) scale(1,1) translate(0,-2.52173e-13)" writing-mode="lr" x="125.2" xml:space="preserve" y="583.25" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,126.5,502.968) scale(1,1) translate(0,0)" writing-mode="lr" x="126.63" xml:space="preserve" y="507.88" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="148" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,124.321,603.468) scale(1,1) translate(0,0)" writing-mode="lr" x="124.45" xml:space="preserve" y="608.38" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="160">
   <use height="30" transform="rotate(0,334.196,327.857) scale(0.708333,0.665547) translate(133.235,159.739)" width="30" x="323.57" xlink:href="#State:红绿圆(方形)_0" y="317.87" zvalue="538"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.196,327.857) scale(0.708333,0.665547) translate(133.235,159.739)" width="30" x="323.57" y="317.87"/></g>
  <g id="54">
   <use height="30" transform="rotate(0,238.571,327.857) scale(0.708333,0.665547) translate(93.8603,159.739)" width="30" x="227.95" xlink:href="#State:红绿圆(方形)_0" y="317.87" zvalue="539"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,238.571,327.857) scale(0.708333,0.665547) translate(93.8603,159.739)" width="30" x="227.95" y="317.87"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="19">
   <path class="kv10" d="M 460 515 L 898 515" stroke-width="4" zvalue="661"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674245738500" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674245738500"/></metadata>
  <path d="M 460 515 L 898 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv10" d="M 943 514 L 1239 514" stroke-width="4" zvalue="965"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674245804036" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674245804036"/></metadata>
  <path d="M 943 514 L 1239 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv10" d="M 1316 514 L 1800 514" stroke-width="4" zvalue="967"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674245869572" ObjectName="10kVⅢ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674245869572"/></metadata>
  <path d="M 1316 514 L 1800 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="42">
   <use class="kv10" height="20" transform="rotate(0,514.05,306.5) scale(1.5,1.35) translate(-168.85,-75.963)" width="10" x="506.55" xlink:href="#Breaker:开关_0" y="293" zvalue="699"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924517232645" ObjectName="10kV景罕糖厂Ⅰ回线042断路器"/>
   <cge:TPSR_Ref TObjectID="6473924517232645"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,514.05,306.5) scale(1.5,1.35) translate(-168.85,-75.963)" width="10" x="506.55" y="293"/></g>
  <g id="26">
   <use class="kv10" height="20" transform="rotate(0,514.173,432) scale(1.5,1.35) translate(-168.891,-108.5)" width="10" x="506.6733873732451" xlink:href="#Breaker:开关_0" y="418.5" zvalue="877"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924517298181" ObjectName="10kV景罕糖厂Ⅰ回线041断路器"/>
   <cge:TPSR_Ref TObjectID="6473924517298181"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,514.173,432) scale(1.5,1.35) translate(-168.891,-108.5)" width="10" x="506.6733873732451" y="418.5"/></g>
  <g id="41">
   <use class="kv10" height="20" transform="rotate(0,499.5,608) scale(1.5,1.35) translate(-164,-154.13)" width="10" x="492" xlink:href="#Breaker:开关_0" y="594.5" zvalue="890"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924517363717" ObjectName="#2压榨变断路器"/>
   <cge:TPSR_Ref TObjectID="6473924517363717"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,499.5,608) scale(1.5,1.35) translate(-164,-154.13)" width="10" x="492" y="594.5"/></g>
  <g id="55">
   <use class="kv10" height="20" transform="rotate(0,567.5,608) scale(1.5,1.35) translate(-186.667,-154.13)" width="10" x="560" xlink:href="#Breaker:开关_0" y="594.5" zvalue="898"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924517429253" ObjectName="#1压榨变断路器"/>
   <cge:TPSR_Ref TObjectID="6473924517429253"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,567.5,608) scale(1.5,1.35) translate(-186.667,-154.13)" width="10" x="560" y="594.5"/></g>
  <g id="72">
   <use class="kv10" height="20" transform="rotate(0,635.5,608) scale(1.5,1.35) translate(-209.333,-154.13)" width="10" x="628" xlink:href="#Breaker:开关_0" y="594.5" zvalue="906"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924517494789" ObjectName="制炼变断路器"/>
   <cge:TPSR_Ref TObjectID="6473924517494789"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,635.5,608) scale(1.5,1.35) translate(-209.333,-154.13)" width="10" x="628" y="594.5"/></g>
  <g id="82">
   <use class="kv10" height="20" transform="rotate(0,703.5,608) scale(1.5,1.35) translate(-232,-154.13)" width="10" x="696" xlink:href="#Breaker:开关_0" y="594.5" zvalue="914"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924517560325" ObjectName="取水变断路器"/>
   <cge:TPSR_Ref TObjectID="6473924517560325"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,703.5,608) scale(1.5,1.35) translate(-232,-154.13)" width="10" x="696" y="594.5"/></g>
  <g id="93">
   <use class="kv10" height="20" transform="rotate(0,771.5,608) scale(1.5,1.35) translate(-254.667,-154.13)" width="10" x="764" xlink:href="#Breaker:开关_0" y="594.5" zvalue="920"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924517625861" ObjectName="#1发电机断路器"/>
   <cge:TPSR_Ref TObjectID="6473924517625861"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,771.5,608) scale(1.5,1.35) translate(-254.667,-154.13)" width="10" x="764" y="594.5"/></g>
  <g id="111">
   <use class="kv10" height="20" transform="rotate(0,678.173,432) scale(1.5,1.35) translate(-223.558,-108.5)" width="10" x="670.6733873732451" xlink:href="#Breaker:开关_0" y="418.5" zvalue="931"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924517691397" ObjectName="10kV1号电容器043断路器"/>
   <cge:TPSR_Ref TObjectID="6473924517691397"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,678.173,432) scale(1.5,1.35) translate(-223.558,-108.5)" width="10" x="670.6733873732451" y="418.5"/></g>
  <g id="120">
   <use class="kv10" height="20" transform="rotate(0,746.173,432) scale(1.5,1.35) translate(-246.224,-108.5)" width="10" x="738.6733873732451" xlink:href="#Breaker:开关_0" y="418.5" zvalue="940"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924517756933" ObjectName="切撕机断路器"/>
   <cge:TPSR_Ref TObjectID="6473924517756933"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,746.173,432) scale(1.5,1.35) translate(-246.224,-108.5)" width="10" x="738.6733873732451" y="418.5"/></g>
  <g id="131">
   <use class="kv10" height="20" transform="rotate(0,806.173,432) scale(1.5,1.35) translate(-266.224,-108.5)" width="10" x="798.6733873732451" xlink:href="#Breaker:开关_0" y="418.5" zvalue="949"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924517822470" ObjectName="#2热电变断路器"/>
   <cge:TPSR_Ref TObjectID="6473924517822470"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,806.173,432) scale(1.5,1.35) translate(-266.224,-108.5)" width="10" x="798.6733873732451" y="418.5"/></g>
  <g id="147">
   <use class="kv10" height="20" transform="rotate(0,866.173,432) scale(1.5,1.35) translate(-286.224,-108.5)" width="10" x="858.6733873732451" xlink:href="#Breaker:开关_0" y="418.5" zvalue="957"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924517888006" ObjectName="#1热电变断路器"/>
   <cge:TPSR_Ref TObjectID="6473924517888006"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,866.173,432) scale(1.5,1.35) translate(-286.224,-108.5)" width="10" x="858.6733873732451" y="418.5"/></g>
  <g id="16">
   <use class="kv10" height="20" transform="rotate(0,847.5,608) scale(1.5,1.35) translate(-280,-154.13)" width="10" x="840" xlink:href="#Breaker:开关_0" y="594.5" zvalue="969"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924517953541" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473924517953541"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,847.5,608) scale(1.5,1.35) translate(-280,-154.13)" width="10" x="840" y="594.5"/></g>
  <g id="23">
   <use class="kv10" height="20" transform="rotate(0,982,589.5) scale(2.2,2.2) translate(-529.636,-309.545)" width="10" x="971" xlink:href="#Breaker:小车断路器_0" y="567.5" zvalue="975"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924518019077" ObjectName="联络线021断路器"/>
   <cge:TPSR_Ref TObjectID="6473924518019077"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,982,589.5) scale(2.2,2.2) translate(-529.636,-309.545)" width="10" x="971" y="567.5"/></g>
  <g id="149">
   <use class="kv10" height="20" transform="rotate(0,990.173,340) scale(1.5,1.35) translate(-327.558,-84.6481)" width="10" x="982.6733873732451" xlink:href="#Breaker:开关_0" y="326.5" zvalue="985"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924518084613" ObjectName="10kV景罕糖厂内联线046断路器"/>
   <cge:TPSR_Ref TObjectID="6473924518084613"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,990.173,340) scale(1.5,1.35) translate(-327.558,-84.6481)" width="10" x="982.6733873732451" y="326.5"/></g>
  <g id="157">
   <use class="kv10" height="20" transform="rotate(0,991,457.5) scale(2.2,2.2) translate(-534.545,-237.545)" width="10" x="980" xlink:href="#Breaker:小车断路器_0" y="435.5" zvalue="990"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924518150149" ObjectName="10kV景罕糖厂内联线045断路器"/>
   <cge:TPSR_Ref TObjectID="6473924518150149"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,991,457.5) scale(2.2,2.2) translate(-534.545,-237.545)" width="10" x="980" y="435.5"/></g>
  <g id="165">
   <use class="kv10" height="20" transform="rotate(0,1062,589.5) scale(2.2,2.2) translate(-573.273,-309.545)" width="10" x="1051" xlink:href="#Breaker:小车断路器_0" y="567.5" zvalue="997"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924518215685" ObjectName="压榨Ⅰ回断路器"/>
   <cge:TPSR_Ref TObjectID="6473924518215685"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1062,589.5) scale(2.2,2.2) translate(-573.273,-309.545)" width="10" x="1051" y="567.5"/></g>
  <g id="180">
   <use class="kv10" height="20" transform="rotate(0,1222,589.5) scale(2.2,2.2) translate(-660.545,-309.545)" width="10" x="1211" xlink:href="#Breaker:小车断路器_0" y="567.5" zvalue="1012"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924518281221" ObjectName="10kV分段023断路器"/>
   <cge:TPSR_Ref TObjectID="6473924518281221"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1222,589.5) scale(2.2,2.2) translate(-660.545,-309.545)" width="10" x="1211" y="567.5"/></g>
  <g id="183">
   <use class="kv10" height="20" transform="rotate(0,1103,457.5) scale(2.2,2.2) translate(-595.636,-237.545)" width="10" x="1092" xlink:href="#Breaker:小车断路器_0" y="435.5" zvalue="1014"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924518346757" ObjectName="污水系统断路器"/>
   <cge:TPSR_Ref TObjectID="6473924518346757"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1103,457.5) scale(2.2,2.2) translate(-595.636,-237.545)" width="10" x="1092" y="435.5"/></g>
  <g id="188">
   <use class="kv10" height="20" transform="rotate(0,1201,457.5) scale(2.2,2.2) translate(-649.091,-237.545)" width="10" x="1190" xlink:href="#Breaker:小车断路器_0" y="435.5" zvalue="1019"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924518412293" ObjectName="热电Ⅰ回断路器"/>
   <cge:TPSR_Ref TObjectID="6473924518412293"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1201,457.5) scale(2.2,2.2) translate(-649.091,-237.545)" width="10" x="1190" y="435.5"/></g>
  <g id="206">
   <use class="kv10" height="20" transform="rotate(0,1408,586.5) scale(2.2,2.2) translate(-762,-307.909)" width="10" x="1397" xlink:href="#Breaker:小车断路器_0" y="564.5" zvalue="1040"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924518477829" ObjectName="#2发电机047断路器"/>
   <cge:TPSR_Ref TObjectID="6473924518477829"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1408,586.5) scale(2.2,2.2) translate(-762,-307.909)" width="10" x="1397" y="564.5"/></g>
  <g id="218">
   <use class="kv10" height="20" transform="rotate(0,1484,586.5) scale(2.2,2.2) translate(-803.455,-307.909)" width="10" x="1473" xlink:href="#Breaker:小车断路器_0" y="564.5" zvalue="1049"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924518543365" ObjectName="#3发电机048断路器"/>
   <cge:TPSR_Ref TObjectID="6473924518543365"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1484,586.5) scale(2.2,2.2) translate(-803.455,-307.909)" width="10" x="1473" y="564.5"/></g>
  <g id="221">
   <use class="kv10" height="20" transform="rotate(0,1564,586.5) scale(2.2,2.2) translate(-847.091,-307.909)" width="10" x="1553" xlink:href="#Breaker:小车断路器_0" y="564.5" zvalue="1056"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924518608901" ObjectName="10kV2号电容器049断路器"/>
   <cge:TPSR_Ref TObjectID="6473924518608901"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1564,586.5) scale(2.2,2.2) translate(-847.091,-307.909)" width="10" x="1553" y="564.5"/></g>
  <g id="236">
   <use class="kv10" height="20" transform="rotate(0,1465,457.5) scale(2.2,2.2) translate(-793.091,-237.545)" width="10" x="1454" xlink:href="#Breaker:小车断路器_0" y="435.5" zvalue="1071"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924518674437" ObjectName="锅炉房断路器"/>
   <cge:TPSR_Ref TObjectID="6473924518674437"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1465,457.5) scale(2.2,2.2) translate(-793.091,-237.545)" width="10" x="1454" y="435.5"/></g>
  <g id="241">
   <use class="kv10" height="20" transform="rotate(0,1545,457.5) scale(2.2,2.2) translate(-836.727,-237.545)" width="10" x="1534" xlink:href="#Breaker:小车断路器_0" y="435.5" zvalue="1077"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924518739973" ObjectName="热电Ⅱ回断路器"/>
   <cge:TPSR_Ref TObjectID="6473924518739973"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1545,457.5) scale(2.2,2.2) translate(-836.727,-237.545)" width="10" x="1534" y="435.5"/></g>
  <g id="247">
   <use class="kv10" height="20" transform="rotate(0,1631,457.5) scale(2.2,2.2) translate(-883.636,-237.545)" width="10" x="1620" xlink:href="#Breaker:小车断路器_0" y="435.5" zvalue="1081"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924518805509" ObjectName="制炼循环水站Ⅱ回断路器"/>
   <cge:TPSR_Ref TObjectID="6473924518805509"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1631,457.5) scale(2.2,2.2) translate(-883.636,-237.545)" width="10" x="1620" y="435.5"/></g>
  <g id="252">
   <use class="kv10" height="20" transform="rotate(0,1715,457.5) scale(2.2,2.2) translate(-929.455,-237.545)" width="10" x="1704" xlink:href="#Breaker:小车断路器_0" y="435.5" zvalue="1087"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924518871045" ObjectName="压榨Ⅱ回断路器"/>
   <cge:TPSR_Ref TObjectID="6473924518871045"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1715,457.5) scale(2.2,2.2) translate(-929.455,-237.545)" width="10" x="1704" y="435.5"/></g>
  <g id="259">
   <use class="kv10" height="20" transform="rotate(0,1656,586.5) scale(2.2,2.2) translate(-897.273,-307.909)" width="10" x="1645" xlink:href="#Breaker:小车断路器_0" y="564.5" zvalue="1096"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924518936581" ObjectName="制炼澄清车间断路器"/>
   <cge:TPSR_Ref TObjectID="6473924518936581"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1656,586.5) scale(2.2,2.2) translate(-897.273,-307.909)" width="10" x="1645" y="564.5"/></g>
  <g id="266">
   <use class="kv10" height="20" transform="rotate(0,1748,586.5) scale(2.2,2.2) translate(-947.455,-307.909)" width="10" x="1737" xlink:href="#Breaker:小车断路器_0" y="564.5" zvalue="1104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924519002117" ObjectName="制炼成糖车间断路器"/>
   <cge:TPSR_Ref TObjectID="6473924519002117"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1748,586.5) scale(2.2,2.2) translate(-947.455,-307.909)" width="10" x="1737" y="564.5"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="59">
   <use class="kv10" height="26" transform="rotate(0,499.1,709.5) scale(3.12941,2.92308) translate(-321.513,-441.776)" width="17" x="472.5" xlink:href="#EnergyConsumer:Y-Y站用_0" y="671.5" zvalue="709"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449826586630" ObjectName="#2压榨变"/>
   <cge:TPSR_Ref TObjectID="6192449826586630"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,499.1,709.5) scale(3.12941,2.92308) translate(-321.513,-441.776)" width="17" x="472.5" y="671.5"/></g>
  <g id="56">
   <use class="kv10" height="26" transform="rotate(0,567.1,709.5) scale(3.12941,2.92308) translate(-367.784,-441.776)" width="17" x="540.5" xlink:href="#EnergyConsumer:Y-Y站用_0" y="671.5" zvalue="896"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449827045382" ObjectName="#1压榨变"/>
   <cge:TPSR_Ref TObjectID="6192449827045382"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,567.1,709.5) scale(3.12941,2.92308) translate(-367.784,-441.776)" width="17" x="540.5" y="671.5"/></g>
  <g id="73">
   <use class="kv10" height="26" transform="rotate(0,635.1,709.5) scale(3.12941,2.92308) translate(-414.055,-441.776)" width="17" x="608.5" xlink:href="#EnergyConsumer:Y-Y站用_0" y="671.5" zvalue="904"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449827176454" ObjectName="制炼变"/>
   <cge:TPSR_Ref TObjectID="6192449827176454"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,635.1,709.5) scale(3.12941,2.92308) translate(-414.055,-441.776)" width="17" x="608.5" y="671.5"/></g>
  <g id="85">
   <use class="kv10" height="26" transform="rotate(0,703.1,709.5) scale(3.12941,2.92308) translate(-460.325,-441.776)" width="17" x="676.5" xlink:href="#EnergyConsumer:Y-Y站用_0" y="671.5" zvalue="912"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449827307526" ObjectName="取水变"/>
   <cge:TPSR_Ref TObjectID="6192449827307526"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,703.1,709.5) scale(3.12941,2.92308) translate(-460.325,-441.776)" width="17" x="676.5" y="671.5"/></g>
  <g id="121">
   <use class="kv10" height="30" transform="rotate(0,747,380.5) scale(1.25,1.23333) translate(-147.9,-68.4865)" width="12" x="739.5" xlink:href="#EnergyConsumer:负荷_0" y="362" zvalue="946"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449827766278" ObjectName="切撕机"/>
   <cge:TPSR_Ref TObjectID="6192449827766278"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,747,380.5) scale(1.25,1.23333) translate(-147.9,-68.4865)" width="12" x="739.5" y="362"/></g>
  <g id="126">
   <use class="kv10" height="30" transform="rotate(0,807,380.5) scale(1.25,1.23333) translate(-159.9,-68.4865)" width="12" x="799.5" xlink:href="#EnergyConsumer:负荷_0" y="362" zvalue="953"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449827831814" ObjectName="#2热电变"/>
   <cge:TPSR_Ref TObjectID="6192449827831814"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,807,380.5) scale(1.25,1.23333) translate(-159.9,-68.4865)" width="12" x="799.5" y="362"/></g>
  <g id="138">
   <use class="kv10" height="30" transform="rotate(0,866,381.5) scale(1.25,1.23333) translate(-171.7,-68.6757)" width="12" x="858.5" xlink:href="#EnergyConsumer:负荷_0" y="363" zvalue="961"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449827962886" ObjectName="#1热电变"/>
   <cge:TPSR_Ref TObjectID="6192449827962886"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,866,381.5) scale(1.25,1.23333) translate(-171.7,-68.6757)" width="12" x="858.5" y="363"/></g>
  <g id="166">
   <use class="kv10" height="30" transform="rotate(0,1062,665.5) scale(1.25,-1.23333) translate(-210.9,-1201.59)" width="12" x="1054.5" xlink:href="#EnergyConsumer:负荷_0" y="647" zvalue="999"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449828290566" ObjectName="压榨Ⅰ回"/>
   <cge:TPSR_Ref TObjectID="6192449828290566"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1062,665.5) scale(1.25,-1.23333) translate(-210.9,-1201.59)" width="12" x="1054.5" y="647"/></g>
  <g id="184">
   <use class="kv10" height="30" transform="rotate(0,1103.5,373) scale(2.5,-2.5) translate(-639.6,-499.7)" width="30" x="1066" xlink:href="#EnergyConsumer:站用变DY_0" y="335.5" zvalue="1015"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449828487174" ObjectName="污水系统"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1103.5,373) scale(2.5,-2.5) translate(-639.6,-499.7)" width="30" x="1066" y="335.5"/></g>
  <g id="186">
   <use class="kv10" height="26" transform="rotate(0,1200.48,381.5) scale(3.12941,-2.92308) translate(-798.767,-487.013)" width="17" x="1173.878431372549" xlink:href="#EnergyConsumer:Y-Y站用_0" y="343.5" zvalue="1017"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449828552710" ObjectName="热电Ⅰ回"/>
   <cge:TPSR_Ref TObjectID="6192449828552710"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1200.48,381.5) scale(3.12941,-2.92308) translate(-798.767,-487.013)" width="17" x="1173.878431372549" y="343.5"/></g>
  <g id="237">
   <use class="kv10" height="26" transform="rotate(0,1464.48,381.5) scale(3.12941,-2.92308) translate(-978.406,-487.013)" width="17" x="1437.878431372549" xlink:href="#EnergyConsumer:Y-Y站用_0" y="343.5" zvalue="1069"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449828945926" ObjectName="锅炉房"/>
   <cge:TPSR_Ref TObjectID="6192449828945926"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1464.48,381.5) scale(3.12941,-2.92308) translate(-978.406,-487.013)" width="17" x="1437.878431372549" y="343.5"/></g>
  <g id="242">
   <use class="kv10" height="26" transform="rotate(0,1544.48,381.5) scale(3.12941,-2.92308) translate(-1032.84,-487.013)" width="17" x="1517.878431372549" xlink:href="#EnergyConsumer:Y-Y站用_0" y="343.5" zvalue="1075"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449829011462" ObjectName="热电Ⅱ回"/>
   <cge:TPSR_Ref TObjectID="6192449829011462"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1544.48,381.5) scale(3.12941,-2.92308) translate(-1032.84,-487.013)" width="17" x="1517.878431372549" y="343.5"/></g>
  <g id="246">
   <use class="kv10" height="30" transform="rotate(0,1631.5,373) scale(2.5,-2.5) translate(-956.4,-499.7)" width="30" x="1594" xlink:href="#EnergyConsumer:站用变DY_0" y="335.5" zvalue="1082"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449829076998" ObjectName="制炼循环水站Ⅱ回"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1631.5,373) scale(2.5,-2.5) translate(-956.4,-499.7)" width="30" x="1594" y="335.5"/></g>
  <g id="251">
   <use class="kv10" height="30" transform="rotate(0,1715.5,373) scale(1.25,1.23333) translate(-341.6,-67.0676)" width="12" x="1708" xlink:href="#EnergyConsumer:负荷_0" y="354.5" zvalue="1088"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449829142534" ObjectName="压榨Ⅱ回"/>
   <cge:TPSR_Ref TObjectID="6192449829142534"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1715.5,373) scale(1.25,1.23333) translate(-341.6,-67.0676)" width="12" x="1708" y="354.5"/></g>
  <g id="260">
   <use class="kv10" height="30" transform="rotate(0,1656.12,681.5) scale(2.31459,2.13333) translate(-928.773,-345.047)" width="18" x="1635.284404283802" xlink:href="#EnergyConsumer:接地站用变两卷_0" y="649.5" zvalue="1094"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449829208070" ObjectName="制炼澄清车间"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1656.12,681.5) scale(2.31459,2.13333) translate(-928.773,-345.047)" width="18" x="1635.284404283802" y="649.5"/></g>
  <g id="267">
   <use class="kv10" height="30" transform="rotate(0,1748.12,681.5) scale(2.31459,2.13333) translate(-981.026,-345.047)" width="18" x="1727.284404283802" xlink:href="#EnergyConsumer:接地站用变两卷_0" y="649.5" zvalue="1103"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449829273606" ObjectName="制炼成糖车间"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1748.12,681.5) scale(2.31459,2.13333) translate(-981.026,-345.047)" width="18" x="1727.284404283802" y="649.5"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="150">
   <use class="kv10" height="30" transform="rotate(0,771,695.447) scale(1.51667,1.59649) translate(-254.898,-250.89)" width="30" x="748.25" xlink:href="#Generator:发电机_0" y="671.5" zvalue="780"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449826652166" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449826652166"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,771,695.447) scale(1.51667,1.59649) translate(-254.898,-250.89)" width="30" x="748.25" y="671.5"/></g>
  <g id="202">
   <use class="kv10" height="30" transform="rotate(0,1407,695.447) scale(1.51667,1.59649) translate(-471.558,-250.89)" width="30" x="1384.25" xlink:href="#Generator:发电机_0" y="671.5" zvalue="1030"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449828618246" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449828618246"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1407,695.447) scale(1.51667,1.59649) translate(-471.558,-250.89)" width="30" x="1384.25" y="671.5"/></g>
  <g id="219">
   <use class="kv10" height="30" transform="rotate(0,1483,695.447) scale(1.51667,1.59649) translate(-497.448,-250.89)" width="30" x="1460.25" xlink:href="#Generator:发电机_0" y="671.5" zvalue="1047"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449828683782" ObjectName="#3发电机"/>
   <cge:TPSR_Ref TObjectID="6192449828683782"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1483,695.447) scale(1.51667,1.59649) translate(-497.448,-250.89)" width="30" x="1460.25" y="671.5"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="3">
   <path class="kv10" d="M 514 226.82 L 514 293.59" stroke-width="1" zvalue="782"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="42@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 514 226.82 L 514 293.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv10" d="M 514.15 319.39 L 514.15 335.86" stroke-width="1" zvalue="874"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@1" LinkObjectIDznd="7@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 514.15 319.39 L 514.15 335.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv10" d="M 514.56 490.31 L 514.56 515" stroke-width="1" zvalue="888"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@1" LinkObjectIDznd="19@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 514.56 490.31 L 514.56 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv10" d="M 500.06 542.69 L 500.06 515" stroke-width="1" zvalue="892"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@1" LinkObjectIDznd="19@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 500.06 542.69 L 500.06 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv10" d="M 500.09 564.14 L 500.09 595.09" stroke-width="1" zvalue="893"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="41@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 500.09 564.14 L 500.09 595.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv10" d="M 499.6 620.89 L 499.6 671.99" stroke-width="1" zvalue="894"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@1" LinkObjectIDznd="59@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 499.6 620.89 L 499.6 671.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv10" d="M 568.06 542.69 L 568.06 515" stroke-width="1" zvalue="900"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="19@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 568.06 542.69 L 568.06 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv10" d="M 568.09 564.14 L 568.09 595.09" stroke-width="1" zvalue="901"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="55@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 568.09 564.14 L 568.09 595.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv10" d="M 567.6 620.89 L 567.62 671.99" stroke-width="1" zvalue="902"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@1" LinkObjectIDznd="56@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 567.6 620.89 L 567.62 671.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv10" d="M 636.06 542.69 L 636.06 515" stroke-width="1" zvalue="908"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@1" LinkObjectIDznd="19@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 636.06 542.69 L 636.06 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv10" d="M 636.09 564.14 L 636.09 595.09" stroke-width="1" zvalue="909"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="72@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 636.09 564.14 L 636.09 595.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv10" d="M 635.6 620.89 L 635.62 671.99" stroke-width="1" zvalue="910"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@1" LinkObjectIDznd="73@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 635.6 620.89 L 635.62 671.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv10" d="M 704.06 542.69 L 704.06 515" stroke-width="1" zvalue="916"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@1" LinkObjectIDznd="19@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 704.06 542.69 L 704.06 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv10" d="M 704.09 564.14 L 704.09 595.09" stroke-width="1" zvalue="917"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@0" LinkObjectIDznd="82@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 704.09 564.14 L 704.09 595.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv10" d="M 703.6 620.89 L 703.62 671.99" stroke-width="1" zvalue="918"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@1" LinkObjectIDznd="85@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 703.6 620.89 L 703.62 671.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv10" d="M 772.06 542.69 L 772.06 515" stroke-width="1" zvalue="922"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@1" LinkObjectIDznd="19@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.06 542.69 L 772.06 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv10" d="M 772.09 564.14 L 772.09 595.09" stroke-width="1" zvalue="923"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="93@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.09 564.14 L 772.09 595.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv10" d="M 771.6 620.89 L 771.6 671.9" stroke-width="1" zvalue="924"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@1" LinkObjectIDznd="150@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 771.6 620.89 L 771.6 671.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv10" d="M 606.56 490.31 L 606.56 515" stroke-width="1" zvalue="928"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@1" LinkObjectIDznd="19@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 606.56 490.31 L 606.56 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv10" d="M 606.59 468.86 L 606.59 432.57" stroke-width="1" zvalue="929"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@0" LinkObjectIDznd="95@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 606.59 468.86 L 606.59 432.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 678.12 397.64 L 678.12 419.09" stroke-width="1" zvalue="933"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="111@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 678.12 397.64 L 678.12 419.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="kv10" d="M 678.27 444.89 L 678.27 468.86" stroke-width="1" zvalue="935"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@1" LinkObjectIDznd="108@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 678.27 444.89 L 678.27 468.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv10" d="M 678.56 490.31 L 678.56 515" stroke-width="1" zvalue="937"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@1" LinkObjectIDznd="19@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 678.56 490.31 L 678.56 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv10" d="M 746.27 444.89 L 746.27 468.86" stroke-width="1" zvalue="943"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@1" LinkObjectIDznd="119@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 746.27 444.89 L 746.27 468.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv10" d="M 746.56 490.31 L 746.56 515" stroke-width="1" zvalue="945"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@1" LinkObjectIDznd="19@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 746.56 490.31 L 746.56 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv10" d="M 747 397.15 L 747 419.09" stroke-width="1" zvalue="947"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@0" LinkObjectIDznd="120@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 747 397.15 L 747 419.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv10" d="M 806.27 444.89 L 806.27 468.86" stroke-width="1" zvalue="951"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@1" LinkObjectIDznd="129@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 806.27 444.89 L 806.27 468.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv10" d="M 806.56 490.31 L 806.56 515" stroke-width="1" zvalue="952"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@1" LinkObjectIDznd="19@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 806.56 490.31 L 806.56 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv10" d="M 807 397.15 L 807 419.09" stroke-width="1" zvalue="955"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="131@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 807 397.15 L 807 419.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="kv10" d="M 866.27 444.89 L 866.27 468.86" stroke-width="1" zvalue="959"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="147@1" LinkObjectIDznd="145@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.27 444.89 L 866.27 468.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv10" d="M 866.56 490.31 L 866.56 515" stroke-width="1" zvalue="960"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@1" LinkObjectIDznd="19@10" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.56 490.31 L 866.56 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv10" d="M 848.06 542.69 L 848.06 515" stroke-width="1" zvalue="971"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="14@1" LinkObjectIDznd="19@11" MaxPinNum="2"/>
   </metadata>
  <path d="M 848.06 542.69 L 848.06 515" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv10" d="M 848.09 564.14 L 848.09 595.09" stroke-width="1" zvalue="972"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="14@0" LinkObjectIDznd="16@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 848.09 564.14 L 848.09 595.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv10" d="M 847.6 643.69 L 847.6 620.89" stroke-width="1" zvalue="976"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@1" LinkObjectIDznd="16@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 847.6 643.69 L 847.6 620.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv10" d="M 847.63 665.14 L 847.63 676.5 L 982 676.5 L 982 609.3" stroke-width="1" zvalue="977"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@0" LinkObjectIDznd="23@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 847.63 665.14 L 847.63 676.5 L 982 676.5 L 982 609.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv10" d="M 982 569.15 L 982 514" stroke-width="1" zvalue="978"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="23@0" LinkObjectIDznd="153@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 982 569.15 L 982 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv10" d="M 514.59 468.86 L 514.59 456.88 L 514.27 456.88 L 514.27 444.89" stroke-width="1" zvalue="981"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="26@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 514.59 468.86 L 514.59 456.88 L 514.27 456.88 L 514.27 444.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv10" d="M 514.12 419.09 L 514.12 396.31" stroke-width="1" zvalue="982"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@0" LinkObjectIDznd="33@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 514.12 419.09 L 514.12 396.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="kv10" d="M 514.59 374.86 L 514.59 357.31" stroke-width="1" zvalue="983"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="7@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 514.59 374.86 L 514.59 357.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv10" d="M 990.27 352.89 L 990.27 376.86" stroke-width="1" zvalue="987"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="149@1" LinkObjectIDznd="116@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 990.27 352.89 L 990.27 376.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="kv10" d="M 990.56 398.31 L 990.56 437.15" stroke-width="1" zvalue="991"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@1" LinkObjectIDznd="157@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 990.56 398.31 L 990.56 437.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="kv10" d="M 991 477.3 L 991 514" stroke-width="1" zvalue="992"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@1" LinkObjectIDznd="153@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 991 477.3 L 991 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv10" d="M 990.12 327.08 L 990.12 259.5 L 514 259.5" stroke-width="1" zvalue="993"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="149@0" LinkObjectIDznd="3" MaxPinNum="2"/>
   </metadata>
  <path d="M 990.12 327.08 L 990.12 259.5 L 514 259.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv10" d="M 1062 569.15 L 1062 514" stroke-width="1" zvalue="1000"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="153@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062 569.15 L 1062 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv10" d="M 1062 609.3 L 1062 648.85" stroke-width="1" zvalue="1001"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@1" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062 609.3 L 1062 648.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv10" d="M 1136.56 543.69 L 1136.56 514" stroke-width="1" zvalue="1009"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@1" LinkObjectIDznd="153@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1136.56 543.69 L 1136.56 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv10" d="M 1136.59 565.14 L 1136.59 581.78 L 1136.59 581.78 L 1136.59 598.43" stroke-width="1" zvalue="1010"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@0" LinkObjectIDznd="177@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1136.59 565.14 L 1136.59 581.78 L 1136.59 581.78 L 1136.59 598.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv10" d="M 1103 477.3 L 1103 514" stroke-width="1" zvalue="1020"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@1" LinkObjectIDznd="153@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1103 477.3 L 1103 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv10" d="M 1103 437.15 L 1103 408" stroke-width="1" zvalue="1021"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@0" LinkObjectIDznd="184@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1103 437.15 L 1103 408" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="kv10" d="M 1201 477.3 L 1201 514" stroke-width="1" zvalue="1022"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@1" LinkObjectIDznd="153@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1201 477.3 L 1201 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="kv10" d="M 1201 419.01 L 1201 437.15" stroke-width="1" zvalue="1023"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="188@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1201 419.01 L 1201 437.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv10" d="M 1222 569.15 L 1222 514" stroke-width="1" zvalue="1024"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@0" LinkObjectIDznd="153@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1222 569.15 L 1222 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 1222 609.3 L 1222 633.5 L 1334 633.5 L 1334 514" stroke-width="1" zvalue="1025"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@1" LinkObjectIDznd="154@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1222 609.3 L 1222 633.5 L 1334 633.5 L 1334 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv10" d="M 1564 566.15 L 1564 514" stroke-width="1" zvalue="1059"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="154@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1564 566.15 L 1564 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv10" d="M 1564 606.3 L 1564 664.69" stroke-width="1" zvalue="1060"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@1" LinkObjectIDznd="222@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1564 606.3 L 1564 664.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="230">
   <path class="kv10" d="M 1382.56 490.31 L 1382.56 514" stroke-width="1" zvalue="1065"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@1" LinkObjectIDznd="154@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1382.56 490.31 L 1382.56 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="229">
   <path class="kv10" d="M 1382.59 468.86 L 1382.59 432.57" stroke-width="1" zvalue="1067"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@0" LinkObjectIDznd="232@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1382.59 468.86 L 1382.59 432.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="235">
   <path class="kv10" d="M 1465 477.3 L 1465 514" stroke-width="1" zvalue="1072"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="236@1" LinkObjectIDznd="154@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1465 477.3 L 1465 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="234">
   <path class="kv10" d="M 1465 419.01 L 1465 437.15" stroke-width="1" zvalue="1073"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@0" LinkObjectIDznd="236@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1465 419.01 L 1465 437.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv10" d="M 1545 477.3 L 1545 514" stroke-width="1" zvalue="1078"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="241@1" LinkObjectIDznd="154@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1545 477.3 L 1545 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="kv10" d="M 1545 419.01 L 1545 437.15" stroke-width="1" zvalue="1079"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="242@0" LinkObjectIDznd="241@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1545 419.01 L 1545 437.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="245">
   <path class="kv10" d="M 1631 477.3 L 1631 514" stroke-width="1" zvalue="1084"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247@1" LinkObjectIDznd="154@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1631 477.3 L 1631 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv10" d="M 1631 437.15 L 1631 408" stroke-width="1" zvalue="1085"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247@0" LinkObjectIDznd="246@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1631 437.15 L 1631 408" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv10" d="M 1715 477.3 L 1715 514" stroke-width="1" zvalue="1090"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252@1" LinkObjectIDznd="154@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1715 477.3 L 1715 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="249">
   <path class="kv10" d="M 1715 437.15 L 1715 389.65" stroke-width="1" zvalue="1091"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252@0" LinkObjectIDznd="251@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1715 437.15 L 1715 389.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv10" d="M 1408 566.15 L 1408 514" stroke-width="1" zvalue="1109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@0" LinkObjectIDznd="154@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1408 566.15 L 1408 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv10" d="M 1408 606.3 L 1408 671.9" stroke-width="1" zvalue="1110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@1" LinkObjectIDznd="202@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1408 606.3 L 1408 671.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="271">
   <path class="kv10" d="M 1484 566.15 L 1484 514" stroke-width="1" zvalue="1111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@0" LinkObjectIDznd="154@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1484 566.15 L 1484 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="272">
   <path class="kv10" d="M 1484 606.3 L 1484 671.9" stroke-width="1" zvalue="1112"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@1" LinkObjectIDznd="219@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1484 606.3 L 1484 671.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="273">
   <path class="kv10" d="M 1656 566.15 L 1656 514" stroke-width="1" zvalue="1113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@0" LinkObjectIDznd="154@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1656 566.15 L 1656 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="274">
   <path class="kv10" d="M 1656 606.3 L 1656 650.57" stroke-width="1" zvalue="1114"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@1" LinkObjectIDznd="260@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1656 606.3 L 1656 650.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="275">
   <path class="kv10" d="M 1748 566.15 L 1748 514" stroke-width="1" zvalue="1115"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266@0" LinkObjectIDznd="154@10" MaxPinNum="2"/>
   </metadata>
  <path d="M 1748 566.15 L 1748 514" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="276">
   <path class="kv10" d="M 1748 606.3 L 1748 650.57" stroke-width="1" zvalue="1116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266@1" LinkObjectIDznd="267@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1748 606.3 L 1748 650.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv10" d="M 866 398.15 L 866 419.09" stroke-width="1" zvalue="1117"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@0" LinkObjectIDznd="147@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 866 398.15 L 866 419.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="7">
   <use class="kv10" height="30" transform="rotate(0,514.062,346.5) scale(1,0.733333) translate(0,122)" width="15" x="506.5622229491393" xlink:href="#Disconnector:刀闸_0" y="335.5" zvalue="873"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449826717702" ObjectName="10kV景罕糖厂Ⅰ回线0421隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449826717702"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,514.062,346.5) scale(1,0.733333) translate(0,122)" width="15" x="506.5622229491393" y="335.5"/></g>
  <g id="33">
   <use class="kv10" height="30" transform="rotate(0,514.5,385.5) scale(1,0.733333) translate(0,136.182)" width="15" x="507" xlink:href="#Disconnector:刀闸_0" y="374.5" zvalue="882"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449826783238" ObjectName="10kV景罕糖厂Ⅰ回线0416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449826783238"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,514.5,385.5) scale(1,0.733333) translate(0,136.182)" width="15" x="507" y="374.5"/></g>
  <g id="37">
   <use class="kv10" height="30" transform="rotate(0,514.5,479.5) scale(1,0.733333) translate(0,170.364)" width="15" x="507" xlink:href="#Disconnector:刀闸_0" y="468.5" zvalue="886"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449826848774" ObjectName="10kV景罕糖厂Ⅰ回线0411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449826848774"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,514.5,479.5) scale(1,0.733333) translate(0,170.364)" width="15" x="507" y="468.5"/></g>
  <g id="43">
   <use class="kv10" height="30" transform="rotate(0,500,553.5) scale(1,-0.733333) translate(0,-1312.27)" width="15" x="492.5" xlink:href="#Disconnector:刀闸_0" y="542.5" zvalue="891"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449826914310" ObjectName="#2压榨变隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449826914310"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,500,553.5) scale(1,-0.733333) translate(0,-1312.27)" width="15" x="492.5" y="542.5"/></g>
  <g id="53">
   <use class="kv10" height="30" transform="rotate(0,568,553.5) scale(1,-0.733333) translate(0,-1312.27)" width="15" x="560.5" xlink:href="#Disconnector:刀闸_0" y="542.5" zvalue="899"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449826979846" ObjectName="#1压榨变隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449826979846"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,568,553.5) scale(1,-0.733333) translate(0,-1312.27)" width="15" x="560.5" y="542.5"/></g>
  <g id="71">
   <use class="kv10" height="30" transform="rotate(0,636,553.5) scale(1,-0.733333) translate(0,-1312.27)" width="15" x="628.5" xlink:href="#Disconnector:刀闸_0" y="542.5" zvalue="907"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449827110918" ObjectName="制炼变隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449827110918"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,636,553.5) scale(1,-0.733333) translate(0,-1312.27)" width="15" x="628.5" y="542.5"/></g>
  <g id="81">
   <use class="kv10" height="30" transform="rotate(0,704,553.5) scale(1,-0.733333) translate(0,-1312.27)" width="15" x="696.5" xlink:href="#Disconnector:刀闸_0" y="542.5" zvalue="915"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449827241990" ObjectName="取水变隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449827241990"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,704,553.5) scale(1,-0.733333) translate(0,-1312.27)" width="15" x="696.5" y="542.5"/></g>
  <g id="92">
   <use class="kv10" height="30" transform="rotate(0,772,553.5) scale(1,-0.733333) translate(0,-1312.27)" width="15" x="764.5" xlink:href="#Disconnector:刀闸_0" y="542.5" zvalue="921"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449827373062" ObjectName="#1发电机隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449827373062"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,772,553.5) scale(1,-0.733333) translate(0,-1312.27)" width="15" x="764.5" y="542.5"/></g>
  <g id="100">
   <use class="kv10" height="30" transform="rotate(0,606.5,479.5) scale(1,0.733333) translate(0,170.364)" width="15" x="599" xlink:href="#Disconnector:刀闸_0" y="468.5" zvalue="927"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449827504134" ObjectName="10kVⅠ段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449827504134"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,606.5,479.5) scale(1,0.733333) translate(0,170.364)" width="15" x="599" y="468.5"/></g>
  <g id="108">
   <use class="kv10" height="30" transform="rotate(0,678.5,479.5) scale(1,0.733333) translate(0,170.364)" width="15" x="671" xlink:href="#Disconnector:刀闸_0" y="468.5" zvalue="934"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449827569670" ObjectName="10kV1号电容器0431隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449827569670"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,678.5,479.5) scale(1,0.733333) translate(0,170.364)" width="15" x="671" y="468.5"/></g>
  <g id="119">
   <use class="kv10" height="30" transform="rotate(0,746.5,479.5) scale(1,0.733333) translate(0,170.364)" width="15" x="739" xlink:href="#Disconnector:刀闸_0" y="468.5" zvalue="942"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449827700742" ObjectName="切撕机隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449827700742"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,746.5,479.5) scale(1,0.733333) translate(0,170.364)" width="15" x="739" y="468.5"/></g>
  <g id="129">
   <use class="kv10" height="30" transform="rotate(0,806.5,479.5) scale(1,0.733333) translate(0,170.364)" width="15" x="799" xlink:href="#Disconnector:刀闸_0" y="468.5" zvalue="950"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449827897350" ObjectName="#2热电变隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449827897350"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,806.5,479.5) scale(1,0.733333) translate(0,170.364)" width="15" x="799" y="468.5"/></g>
  <g id="145">
   <use class="kv10" height="30" transform="rotate(0,866.5,479.5) scale(1,0.733333) translate(0,170.364)" width="15" x="859" xlink:href="#Disconnector:刀闸_0" y="468.5" zvalue="958"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449828028422" ObjectName="#1热电变隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449828028422"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,866.5,479.5) scale(1,0.733333) translate(0,170.364)" width="15" x="859" y="468.5"/></g>
  <g id="14">
   <use class="kv10" height="30" transform="rotate(0,848,553.5) scale(1,-0.733333) translate(0,-1312.27)" width="15" x="840.5" xlink:href="#Disconnector:刀闸_0" y="542.5" zvalue="970"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449828093958" ObjectName="10kV分段0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449828093958"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,848,553.5) scale(1,-0.733333) translate(0,-1312.27)" width="15" x="840.5" y="542.5"/></g>
  <g id="21">
   <use class="kv10" height="30" transform="rotate(0,847.539,654.5) scale(1,-0.733333) translate(0,-1551)" width="15" x="840.0388355758942" xlink:href="#Disconnector:刀闸_0" y="643.5" zvalue="974"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449828159494" ObjectName="10kV分段0122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449828159494"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,847.539,654.5) scale(1,-0.733333) translate(0,-1551)" width="15" x="840.0388355758942" y="643.5"/></g>
  <g id="116">
   <use class="kv10" height="30" transform="rotate(0,990.5,387.5) scale(1,0.733333) translate(0,136.909)" width="15" x="983" xlink:href="#Disconnector:刀闸_0" y="376.5" zvalue="986"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449828225030" ObjectName="10kV景罕糖厂内联线0462隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449828225030"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,990.5,387.5) scale(1,0.733333) translate(0,136.909)" width="15" x="983" y="376.5"/></g>
  <g id="176">
   <use class="kv10" height="30" transform="rotate(0,1136.5,554.5) scale(1,-0.733333) translate(0,-1314.64)" width="15" x="1129" xlink:href="#Disconnector:刀闸_0" y="543.5" zvalue="1005"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449828356102" ObjectName="10kVⅡ段母线电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449828356102"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1136.5,554.5) scale(1,-0.733333) translate(0,-1314.64)" width="15" x="1129" y="543.5"/></g>
  <g id="231">
   <use class="kv10" height="30" transform="rotate(0,1382.5,479.5) scale(1,0.733333) translate(0,170.364)" width="15" x="1375" xlink:href="#Disconnector:刀闸_0" y="468.5" zvalue="1064"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449828814854" ObjectName="10kVⅢ段母线电压互感器0903隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449828814854"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1382.5,479.5) scale(1,0.733333) translate(0,170.364)" width="15" x="1375" y="468.5"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="95">
   <use class="kv10" height="51" transform="rotate(0,606.468,407.5) scale(1,1) translate(0,0)" width="40" x="586.4676645908289" xlink:href="#Accessory:电压互感器20210927_0" y="382" zvalue="925"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449827438598" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="51" opacity="0" stroke="white" transform="rotate(0,606.468,407.5) scale(1,1) translate(0,0)" width="40" x="586.4676645908289" y="382"/></g>
  <g id="177">
   <use class="kv10" height="51" transform="rotate(0,1136.47,623.5) scale(1,-1) translate(0,-1247)" width="40" x="1116.467664590829" xlink:href="#Accessory:电压互感器20210927_0" y="598" zvalue="1003"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449828421638" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="51" opacity="0" stroke="white" transform="rotate(0,1136.47,623.5) scale(1,-1) translate(0,-1247)" width="40" x="1116.467664590829" y="598"/></g>
  <g id="232">
   <use class="kv10" height="51" transform="rotate(0,1382.47,407.5) scale(1,1) translate(0,0)" width="40" x="1362.467664590829" xlink:href="#Accessory:电压互感器20210927_0" y="382" zvalue="1062"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449828880390" ObjectName="10kVⅢ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="51" opacity="0" stroke="white" transform="rotate(0,1382.47,407.5) scale(1,1) translate(0,0)" width="40" x="1362.467664590829" y="382"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="112">
   <use class="kv10" height="40" transform="rotate(0,678.123,377.167) scale(1.16667,-1.16667) translate(-94.8748,-697.119)" width="24" x="664.1233873732451" xlink:href="#Compensator:西郊变电容_0" y="353.8333333333334" zvalue="938"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449827635206" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192449827635206"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,678.123,377.167) scale(1.16667,-1.16667) translate(-94.8748,-697.119)" width="24" x="664.1233873732451" y="353.8333333333334"/></g>
  <g id="222">
   <use class="kv10" height="40" transform="rotate(0,1564,685.167) scale(1.16667,1.16667) translate(-221.429,-94.5476)" width="24" x="1550" xlink:href="#Compensator:西郊变电容_0" y="661.8333333333333" zvalue="1058"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449828749318" ObjectName="10kV2号电容器"/>
   <cge:TPSR_Ref TObjectID="6192449828749318"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1564,685.167) scale(1.16667,1.16667) translate(-221.429,-94.5476)" width="24" x="1550" y="661.8333333333333"/></g>
 </g>
</svg>