<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549678964737" height="1045" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1045" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Breaker:母联开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill="rgb(170,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.333333333333333" x2="9.75" y1="0.4166666666666696" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.666666666666668" x2="0.4166666666666661" y1="0.4999999999999982" y2="19.66666666666667"/>
   <rect fill-opacity="0" height="19.58" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.04,10.04) scale(1,1) translate(0,0)" width="9.58" x="0.25" y="0.25"/>
  </symbol>
  <symbol id="Accessory:PT象达_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15.5" xlink:href="#terminal" y="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="18.25" y1="12.08333333333333" y2="12.08333333333333"/>
   <ellipse cx="15.65" cy="12.68" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.9" cy="18.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="18.25" y1="18.33333333333333" y2="18.33333333333333"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:间隔模板_0" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(85,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="State:间隔模板_1" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(185,185,185)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="Accessory:10kV母线PT带消谐装置_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="17.56245852479325" xlink:href="#terminal" y="28.12373692455963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.61245852479333" x2="34.61245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.86245852479331" x2="30.86245852479331" y1="22.68299618381883" y2="19.84040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.6124585247933" x2="26.6124585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.61245852479332" x2="34.61245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666667" x2="31" y1="22.69225544307809" y2="22.69225544307809"/>
   <rect fill-opacity="0" height="3.55" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,30.81,15.38) scale(1,1) translate(0,0)" width="8.92" x="26.34" y="13.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.647721108666" x2="30.647721108666" y1="10.83674821859629" y2="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.71438777533266" x2="28.71438777533266" y1="7.51612768565195" y2="7.51612768565195"/>
   <ellipse cx="14.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.05048569403981" x2="22.05048569403981" y1="34.53084700683308" y2="34.53084700683308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.7608382776216" x2="28.48402243293775" y1="7.754208141873304" y2="7.754208141873304"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.01083827762157" x2="29.40068909960439" y1="6.50420814187332" y2="6.50420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.51083827762159" x2="30.06735576627107" y1="5.004208141873296" y2="5.004208141873296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.89410730945214" x2="23.0877735452272" y1="24.15299989806297" y2="21.53208583485582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58713830216066" x2="22.81883560169719" y1="21.54040359122622" y2="21.54040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51457106407813" x2="19.32090482830307" y1="24.05654513693459" y2="21.43563107372743"/>
   <ellipse cx="14.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.3644003886642" x2="22.3644003886642" y1="30.19213090500035" y2="30.19213090500035"/>
   <ellipse cx="21.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.19220382559271" x2="17.19220382559271" y1="23.07076893362116" y2="23.07076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.4422038255927" x2="16.4422038255927" y1="16.82076893362115" y2="16.82076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.92264294445647" x2="20.87303257583197" y1="16.99992879670395" y2="15.47611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.873032575832" x2="20.37063985073817" y1="15.47611741162252" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.87303257583199" x2="23.32581493230132" y1="15.47611741162254" y2="16.60543752773795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.94220382559271" x2="16.94220382559271" y1="16.32076893362115" y2="16.32076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="16.07581493230131" y1="22.72611741162255" y2="23.85543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="13.12063985073816" y1="22.72611741162255" y2="20.07298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583197" y1="24.24992879670398" y2="22.72611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="16.07581493230131" y1="15.47611741162254" y2="16.60543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="13.12063985073816" y1="15.47611741162253" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583196" y1="16.99992879670396" y2="15.47611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.59103363843511" x2="17.59103363843511" y1="25.15822158129307" y2="28.16666666666667"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.08333333333333" x2="9.833333333333332" y1="16.5" y2="18.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14" y1="9.75" y2="16.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="11" y1="11.58333333333333" y2="9.666666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="9.75" y1="11.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="15.08333333333333" y1="8.416666666666668" y2="25.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_1" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.96,13) scale(1,1) translate(0,0)" width="4.42" x="12.75" y="9"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="5.416666666666668" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_2" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="20" y1="6" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="10.08333333333333" y1="6.000000000000004" y2="25.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Compensator:弄彦电容_0" viewBox="0,0,35,55">
   <use terminal-index="0" type="0" x="17.53671071983855" xlink:href="#terminal" y="8.722466802575759"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.3588305601526" x2="23.3588305601526" y1="38.91966241999671" y2="30.10908965874193"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.385462453274684" x2="3.18281926296099" y1="32.54772397780221" y2="32.54772397780221"/>
   <rect fill-opacity="0" height="4.41" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,17.5,37.41) scale(1,1) translate(0,0)" width="2.2" x="16.4" y="35.21"/>
   <path d="M 17.5 51.4537 L 17.5 53.6564 L 30.7159 53.6564 L 30.7159 0.792951" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.50917767995964" x2="17.50917767995964" y1="46.89243775472014" y2="51.29772413534752"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.10389129933225" x2="13.10389129933225" y1="32.57525701768114" y2="30.14827053946512"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.5917767995964" x2="13.09369387715487" y1="46.89243775472014" y2="46.89243775472014"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.117737706732679" x2="3.117737706732679" y1="51.38950093494393" y2="44.47870792533473"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.06310161062273" x2="13.06310161062273" y1="44.44505643214938" y2="46.9128325990749"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.50917767995964" x2="17.50917767995964" y1="30.02590147333657" y2="43.19383273098506"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.50917767995964" x2="17.50917767995964" y1="44.57048472493113" y2="46.9128325990749"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.13448356586439" x2="23.46549197376626" y1="30.09728342857823" y2="30.09728342857823"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.55690165776185" x2="22.55690165776185" y1="49.18685774463025" y2="51.38950093494394"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.249265716283279" x2="9.249265716283279" y1="32.29992661889193" y2="45.24045536198488"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.54772397780222" x2="3.091042463364582" y1="51.29772413534752" y2="51.29772413534752"/>
   <path d="M 13.1039 36.5387 A 3.26423 2.00162 180 0 1 13.1039 32.5355" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 13.1039 40.542 A 3.26423 2.00162 180 0 1 13.1039 36.5387" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 13.1039 44.4351 A 3.26423 2.00162 180 0 1 13.1039 40.4318" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.16116007269491" x2="15.85719528722437" y1="44.41446416561723" y2="44.41446416561723"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.16116007269491" x2="15.85719528722437" y1="43.31314257046039" y2="43.31314257046039"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.53671071983855" x2="17.53671071983855" y1="8.502202483544391" y2="11.43906007062932"/>
   <path d="M 22.7496 16.8722 A 5.41483 5.23128 -450 1 1 17.5184 11.4574" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 22.6395 17.0374 L 17.5 17.1292 L 17.5 30.0698" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.31710412727505" x2="22.35676386392235" y1="41.93455759015309" y2="41.93455759015309"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.74533821713054" x2="22.84684892976055" y1="42.74983334702029" y2="42.74983334702029"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.37955008828028" x2="22.61246042001183" y1="39.0862687951297" y2="38.20111225910244"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.37955008828028" x2="23.37955008828028" y1="40.85658186718422" y2="41.91876971041693"/>
   <rect fill-opacity="0" height="4.72" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,23.39,38.5) scale(-1,1) translate(-2113.78,0)" width="2.57" x="22.1" y="36.14"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.9903807500496" x2="22.68348724114779" y1="42.34219546858666" y2="42.34219546858666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.37955008828031" x2="24.14663975654877" y1="39.0862687951297" y2="38.20111225910244"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.385462453274684" x2="3.18281926296099" y1="44.38693112573831" y2="44.38693112573831"/>
   <path d="M 5.39464 36.5387 A 3.26423 2.00162 180 0 0 5.39464 32.5355" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 5.39464 40.542 A 3.26423 2.00162 180 0 0 5.39464 36.5387" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 5.39464 44.4351 A 3.26423 2.00162 180 0 0 5.39464 40.4318" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Accessory:中间电缆_0" viewBox="0,0,8,21">
   <use terminal-index="0" type="0" x="4" xlink:href="#terminal" y="10.5"/>
   <path d="M 1.08333 0.5 L 7 0.5 L 4 7.13889 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4" x2="4" y1="20.83333333333333" y2="0.5000000000000036"/>
   <path d="M 1.08333 20.6389 L 7 20.6389 L 4 14 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:支那变双联地刀_0" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="20.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="13" y1="28.83333333333334" y2="28.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.927164662603633" x2="5.927164662603633" y1="35.73783949080951" y2="33.38156647584609"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.940012628342391" x2="9.914316696864876" y1="35.81657844392075" y2="35.81657844392075"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.461425904573654" x2="8.392903420633612" y1="37.61600124906283" y2="37.61600124906283"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.48434686602373" x2="7.369982459183538" y1="39.33429284309519" y2="39.33429284309519"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.6833333333333336" x2="5.927164662603635" y1="23.72291938246423" y2="33.38156647584608"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.83108493932905" x2="8.028087080656674" y1="23.60113107469012" y2="23.60113107469012"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.97259166003632" x2="5.97259166003632" y1="23.55945108167042" y2="20.52711096774605"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.57716466260364" x2="15.57716466260364" y1="35.62739518973012" y2="33.27112217476669"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.47836350249219" x2="19.45266757101467" y1="35.70613414284136" y2="35.70613414284136"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="12.99977677872345" x2="17.93125429478341" y1="37.50555694798344" y2="37.50555694798344"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.18103107350686" x2="16.75" y1="39.2238485420158" y2="39.2238485420158"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.33333333333333" x2="15.57716466260364" y1="23.61247508138485" y2="33.2711221747667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.62259166003632" x2="15.62259166003632" y1="23.44900678059104" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.48108493932905" x2="17.67808708065667" y1="23.49068677361073" y2="23.49068677361073"/>
  </symbol>
  <symbol id="GroundDisconnector:支那变双联地刀_1" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="20.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.916666666666667" x2="15.75" y1="28.83333333333333" y2="28.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.927164662603633" x2="5.927164662603633" y1="35.73783949080951" y2="23.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.070030169158852" x2="10.04433423768134" y1="35.73324511058742" y2="35.73324511058742"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.591443445390117" x2="8.522920961450074" y1="37.61600124906283" y2="37.61600124906283"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.781031073506858" x2="7.333333333333333" y1="39.33429284309519" y2="39.33429284309519"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.83108493932905" x2="8.028087080656674" y1="23.60113107469012" y2="23.60113107469012"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.97259166003632" x2="5.97259166003632" y1="23.55945108167042" y2="20.52711096774605"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.66049799593697" x2="15.66049799593697" y1="35.66666666666666" y2="23.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.64503016915885" x2="19.61933423768134" y1="35.70613414284136" y2="35.70613414284136"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.16644344539012" x2="18.09792096145007" y1="37.50555694798344" y2="37.50555694798344"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.43103107350686" x2="16.83333333333333" y1="39.2238485420158" y2="39.2238485420158"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.62259166003632" x2="15.62259166003632" y1="23.44900678059104" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.48108493932905" x2="17.67808708065667" y1="23.49068677361073" y2="23.49068677361073"/>
  </symbol>
  <symbol id="GroundDisconnector:支那变双联地刀_2" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="20.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.177164662603633" x2="15.66666666666667" y1="33.98783949080951" y2="24.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.070030169158852" x2="10.04433423768134" y1="35.73324511058742" y2="35.73324511058742"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.591443445390117" x2="8.522920961450074" y1="37.61600124906283" y2="37.61600124906283"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.781031073506858" x2="7.333333333333333" y1="39.33429284309519" y2="39.33429284309519"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.97259166003632" x2="5.97259166003632" y1="23.55945108167042" y2="20.52711096774605"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.83108493932905" x2="8.028087080656674" y1="23.60113107469012" y2="23.60113107469012"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.41049799593697" x2="6.333333333333334" y1="34.00000000000001" y2="25.00000000000001"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.64503016915885" x2="19.61933423768134" y1="35.70613414284136" y2="35.70613414284136"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.16644344539012" x2="18.09792096145007" y1="37.50555694798344" y2="37.50555694798344"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.43103107350686" x2="16.83333333333333" y1="39.2238485420158" y2="39.2238485420158"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.62259166003632" x2="15.62259166003632" y1="23.44900678059104" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.48108493932905" x2="17.67808708065667" y1="23.49068677361073" y2="23.49068677361073"/>
  </symbol>
  <symbol id="Accessory:20210316PT_0" viewBox="0,0,15,26">
   <use terminal-index="0" type="0" x="7.45" xlink:href="#terminal" y="0.09999999999999964"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="8.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="6.833333333333331" y2="0"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="4.916666666666666" y1="11.25" y2="13.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="10.5" y1="11.25" y2="13.25"/>
   <ellipse cx="7.53" cy="11.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.5" cy="20.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333333" x2="7.583333333333333" y1="17.5" y2="20.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333332" x2="4.999999999999998" y1="20.5" y2="22.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="10.58333333333334" y1="20.5" y2="22.5"/>
  </symbol>
  <symbol id="Accessory:轩岗变互感器_0" viewBox="0,0,40,51">
   <use terminal-index="0" type="0" x="15.15500006468773" xlink:href="#terminal" y="50.35526637437116"/>
   <rect fill-opacity="0" height="14.28" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.16,25.62) scale(1,-1) translate(0,-1049.72)" width="6.12" x="12.1" y="18.48"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56550008590984" x2="8.465500154002186" y1="10.34576690855561" y2="10.34576690855561"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.15500006468773" x2="15.15500006468773" y1="14.23026685669194" y2="50.61026637096654"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.68999987062454" x2="18.72500001702308" y1="4.625266984932516" y2="4.625266984932516"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56550008590984" x2="11.52550011314678" y1="10.34576690855561" y2="13.4057668677002"/>
   <text fill="rgb(0,0,0)" font-family="宋体" font-size="3" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" text-anchor="middle" x="36.61749977813245" xml:space="preserve" y="16.86250012199878">u</text>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.465500154002186" x2="10.50550012676525" y1="10.34576690855561" y2="13.4057668677002"/>
   <path d="M 23.57 28.05 L 33.77 20.91 L 37.85 20.91" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.332000182487485" x2="6.332000182487485" y1="45.03299973920632" y2="48.09299969835091"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.230000183849333" x2="15.41000006128311" y1="48.19499969698906" y2="48.19499969698906"/>
   <ellipse cx="18.7" cy="11.65" fill-opacity="0" rx="4.25" ry="4.25" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.6730496456298" x2="16.22504967831414" y1="11.86307778238926" y2="13.12536854957501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.67304964562981" x2="18.67304964562981" y1="9.338496248017751" y2="11.86307778238924"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.67304964562981" x2="21.12104961294548" y1="11.86307778238926" y2="13.12536854957501"/>
   <ellipse cx="11.56" cy="11.65" fill-opacity="0" rx="4.25" ry="4.25" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="18.7" cy="4.51" fill-opacity="0" rx="4.25" ry="4.25" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.67304964562981" x2="21.12104961294548" y1="4.723077877718548" y2="5.985368644904295"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.44804974209396" x2="9.00004977477829" y1="4.723077877718545" y2="5.985368644904291"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.44804974209396" x2="11.44804974209396" y1="2.198496343347035" y2="4.723077877718524"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.44804974209396" x2="13.89604970940963" y1="4.723077877718545" y2="5.985368644904291"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.67304964562981" x2="18.67304964562981" y1="2.198496343347035" y2="4.723077877718524"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.6730496456298" x2="16.22504967831414" y1="4.723077877718548" y2="5.985368644904295"/>
   <ellipse cx="11.56" cy="4.51" fill-opacity="0" rx="4.25" ry="4.25" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.77183322956816" x2="31.35599984838104" y1="40.15526651055585" y2="40.15526651055585"/>
   <rect fill-opacity="0" height="16.47" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,28.97,25.27) scale(1,1) translate(0,0)" width="7.73" x="25.1" y="17.04"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.23099987675285" x2="29.23099987675285" y1="33.61026659794103" y2="37.28226654891454"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.49099992668724" x2="33.31099982227897" y1="37.33684549555427" y2="37.33684549555427"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.51999987289429" x2="29.17999987743378" y1="4.67500027804374" y2="17.08500011235237"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.7943332426192" x2="32.00766650634702" y1="38.74605600305506" y2="38.74605600305506"/>
   <rect fill-opacity="0" height="16.47" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.19,36.81) scale(1,-1) translate(0,-1488.72)" width="7.73" x="2.32" y="28.58"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.451000180898664" x2="6.451000180898664" y1="28.7312666630827" y2="25.05926671210919"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.711000230833044" x2="10.53100012642478" y1="25.00468776546946" y2="25.00468776546946"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.45100018089866" x2="9.511000140043254" y1="34.85126658137189" y2="37.91126654051648"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.451000180898664" x2="3.391000221754069" y1="34.85126658137189" y2="37.91126654051648"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.991833533713965" x2="8.576000152526849" y1="22.18626675046788" y2="22.18626675046788"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.014333546765" x2="9.227666810492828" y1="23.59547725796867" y2="23.59547725796867"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.451000180898664" x2="6.451000180898664" y1="45.0512664451872" y2="34.95326658001004"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV勐嘎变" InitShowingPlane="" fill="rgb(0,0,0)" height="1045" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="63.56" xlink:href="logo.png" y="34.68"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="340" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,188.403,75.9636) scale(1,1) translate(-1.41124e-14,0)" writing-mode="lr" x="188.4" xml:space="preserve" y="79.45999999999999" zvalue="1145"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,217.25,75.9403) scale(1,1) translate(0,0)" writing-mode="lr" x="217.25" xml:space="preserve" y="84.94" zvalue="1146">35kV勐嘎变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="227" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,208.531,408.75) scale(1,1) translate(0,0)" width="72.88" x="172.09" y="396.75" zvalue="1263"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,208.531,408.75) scale(1,1) translate(0,0)" writing-mode="lr" x="208.53" xml:space="preserve" y="413.25" zvalue="1263">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="223" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,106.625,408.75) scale(1,1) translate(0,0)" width="72.88" x="70.19" y="396.75" zvalue="1264"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,106.625,408.75) scale(1,1) translate(0,0)" writing-mode="lr" x="106.63" xml:space="preserve" y="413.25" zvalue="1264">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="205" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,106.625,368.25) scale(1,1) translate(0,0)" width="72.88" x="70.19" y="356.25" zvalue="1265"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,106.625,368.25) scale(1,1) translate(0,0)" writing-mode="lr" x="106.63" xml:space="preserve" y="372.75" zvalue="1265">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,528.076,312.591) scale(1,1) translate(0,0)" writing-mode="lr" x="528.08" xml:space="preserve" y="317.09" zvalue="7">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1455.11,569.042) scale(1,1) translate(0,0)" writing-mode="lr" x="1455.11" xml:space="preserve" y="573.54" zvalue="45">001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1406.6,158.238) scale(1,1) translate(0,0)" writing-mode="lr" x="1406.6" xml:space="preserve" y="162.74" zvalue="60">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,868.149,240.409) scale(1,1) translate(9.10548e-14,0)" writing-mode="lr" x="868.15" xml:space="preserve" y="244.91" zvalue="62">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="244" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,873.5,151.123) scale(1,1) translate(0,0)" writing-mode="lr" x="873.5" xml:space="preserve" y="155.62" zvalue="172">3318</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1000.09,76.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1000.09" xml:space="preserve" y="81" zvalue="299">35kV帕嘎线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,933.309,236.332) scale(1,1) translate(0,0)" writing-mode="lr" x="933.3099999999999" xml:space="preserve" y="240.83" zvalue="300">331</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,936.797,173.442) scale(1,1) translate(0,0)" writing-mode="lr" x="936.8" xml:space="preserve" y="177.94" zvalue="302">3316</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,989.732,150.003) scale(1,1) translate(0,0)" writing-mode="lr" x="989.73" xml:space="preserve" y="154.5" zvalue="304">33167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,936.925,295.237) scale(1,1) translate(0,0)" writing-mode="lr" x="936.92" xml:space="preserve" y="299.74" zvalue="307">3311</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,989.732,278.003) scale(1,1) translate(0,0)" writing-mode="lr" x="989.73" xml:space="preserve" y="282.5" zvalue="316">33117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1069.64,99.768) scale(1,1) translate(0,0)" writing-mode="lr" x="1069.64" xml:space="preserve" y="104.27" zvalue="320">PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1466.34,247.82) scale(1,1) translate(0,0)" writing-mode="lr" x="1466.34" xml:space="preserve" y="252.32" zvalue="354">39017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1466.34,314.82) scale(1,1) translate(0,0)" writing-mode="lr" x="1466.34" xml:space="preserve" y="319.32" zvalue="358">39010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1405.03,406.37) scale(1,1) translate(0,0)" writing-mode="lr" x="1405.03" xml:space="preserve" y="410.87" zvalue="366">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1403.41,364.465) scale(1,1) translate(0,0)" writing-mode="lr" x="1403.41" xml:space="preserve" y="368.97" zvalue="368">3011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" x="1341" xml:space="preserve" y="463.0711803436279" zvalue="551">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1341" xml:space="preserve" y="479.0711803436279" zvalue="551">(SZ20-5000/35)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,989.732,210.003) scale(1,1) translate(0,0)" writing-mode="lr" x="989.73" xml:space="preserve" y="214.5" zvalue="554">33160</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="307" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1397.62,266.878) scale(1,1) translate(0,0)" writing-mode="lr" x="1397.62" xml:space="preserve" y="271.38" zvalue="568">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="246" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,991,120.577) scale(1,1) translate(0,0)" writing-mode="lr" x="991" xml:space="preserve" y="125.08" zvalue="727">3319</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1450,607.28) scale(1,1) translate(0,0)" writing-mode="lr" x="1450" xml:space="preserve" y="611.78" zvalue="733">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,847.486,568.042) scale(1,1) translate(0,0)" writing-mode="lr" x="847.49" xml:space="preserve" y="572.54" zvalue="737">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="175" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,796.456,401.37) scale(1,1) translate(0,-3.48939e-13)" writing-mode="lr" x="796.46" xml:space="preserve" y="405.87" zvalue="739">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,793.242,359.465) scale(1,1) translate(1.72529e-13,0)" writing-mode="lr" x="793.24" xml:space="preserve" y="363.97" zvalue="741">3021</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" x="737.8203125" xml:space="preserve" y="468.5173611111111" zvalue="747">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="737.8203125" xml:space="preserve" y="484.5173611111111" zvalue="747">(SZ20-5000/35)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,842.751,606.28) scale(1,1) translate(0,0)" writing-mode="lr" x="842.75" xml:space="preserve" y="610.78" zvalue="752">0022</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1559.58,663.596) scale(1,1) translate(0,0)" writing-mode="lr" x="1559.58" xml:space="preserve" y="668.1" zvalue="768">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1687.5,675.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1687.5" xml:space="preserve" y="680" zvalue="789">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,465,668.5) scale(1,1) translate(0,0)" writing-mode="lr" x="465" xml:space="preserve" y="673" zvalue="791">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,643.103,818.974) scale(1,1) translate(0,0)" writing-mode="lr" x="643.1" xml:space="preserve" y="823.47" zvalue="872">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,648.239,766.618) scale(1,1) translate(0,0)" writing-mode="lr" x="648.24" xml:space="preserve" y="771.12" zvalue="874">032</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,626.679,927.125) scale(1,1) translate(0,0)" writing-mode="lr" x="626.6799999999999" xml:space="preserve" y="931.63" zvalue="878">10kV中山线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,644.603,722.974) scale(1,1) translate(0,0)" writing-mode="lr" x="644.6" xml:space="preserve" y="727.47" zvalue="882">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,779.103,818.974) scale(1,1) translate(0,0)" writing-mode="lr" x="779.1" xml:space="preserve" y="823.47" zvalue="891">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,784.239,766.618) scale(1,1) translate(0,0)" writing-mode="lr" x="784.24" xml:space="preserve" y="771.12" zvalue="893">033</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,780.603,722.974) scale(1,1) translate(0,0)" writing-mode="lr" x="780.6" xml:space="preserve" y="727.47" zvalue="901">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1037.22,656.568) scale(1,1) translate(0,0)" writing-mode="lr" x="1037.22" xml:space="preserve" y="661.0700000000001" zvalue="930">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1191.72,656.568) scale(1,1) translate(0,0)" writing-mode="lr" x="1191.72" xml:space="preserve" y="661.0700000000001" zvalue="932">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1102,596.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1102" xml:space="preserve" y="601" zvalue="936">012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,499.04,822.974) scale(1,1) translate(0,0)" writing-mode="lr" x="499.04" xml:space="preserve" y="827.47" zvalue="952">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,506.252,759.951) scale(1,1) translate(0,0)" writing-mode="lr" x="506.25" xml:space="preserve" y="764.45" zvalue="954">031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,498.867,721.974) scale(1,1) translate(0,0)" writing-mode="lr" x="498.87" xml:space="preserve" y="726.47" zvalue="959">2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,464.417,958.173) scale(1,1) translate(0,0)" writing-mode="lr" x="464.42" xml:space="preserve" y="962.67" zvalue="964">10kV2号电容器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1577.73,527) scale(1,1) translate(0,0)" writing-mode="lr" x="1577.73" xml:space="preserve" y="531.5" zvalue="975">10kVⅠ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,561.934,663.596) scale(1,1) translate(0,0)" writing-mode="lr" x="561.9299999999999" xml:space="preserve" y="668.1" zvalue="979">0902</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,586,532.75) scale(1,1) translate(0,0)" writing-mode="lr" x="586" xml:space="preserve" y="537.25" zvalue="983">10kVⅡ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,884.103,818.974) scale(1,1) translate(0,0)" writing-mode="lr" x="884.1" xml:space="preserve" y="823.47" zvalue="1013">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,889.239,766.618) scale(1,1) translate(0,0)" writing-mode="lr" x="889.24" xml:space="preserve" y="771.12" zvalue="1015">034</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,867.679,927.125) scale(1,1) translate(0,0)" writing-mode="lr" x="867.6799999999999" xml:space="preserve" y="931.63" zvalue="1019">10kV备用Ⅰ线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,885.603,722.974) scale(1,1) translate(0,0)" writing-mode="lr" x="885.6" xml:space="preserve" y="727.47" zvalue="1023">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,997.103,818.974) scale(1,1) translate(0,0)" writing-mode="lr" x="997.1" xml:space="preserve" y="823.47" zvalue="1029">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1002.24,766.618) scale(1,1) translate(0,0)" writing-mode="lr" x="1002.24" xml:space="preserve" y="771.12" zvalue="1031">035</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,980.679,927.125) scale(1,1) translate(0,0)" writing-mode="lr" x="980.6799999999999" xml:space="preserve" y="931.63" zvalue="1035">10kV备用Ⅱ线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,998.603,722.974) scale(1,1) translate(0,0)" writing-mode="lr" x="998.6" xml:space="preserve" y="727.47" zvalue="1039">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1337.1,819.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1337.1" xml:space="preserve" y="824.47" zvalue="1049">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1342.24,767.618) scale(1,1) translate(0,0)" writing-mode="lr" x="1342.24" xml:space="preserve" y="772.12" zvalue="1051">036</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1320.68,928.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1320.68" xml:space="preserve" y="932.63" zvalue="1055">10kV勐戛线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1338.6,723.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1338.6" xml:space="preserve" y="728.47" zvalue="1059">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1497.1,819.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1497.1" xml:space="preserve" y="824.47" zvalue="1064">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1502.24,767.618) scale(1,1) translate(0,0)" writing-mode="lr" x="1502.24" xml:space="preserve" y="772.12" zvalue="1066">037</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1498.6,723.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.6" xml:space="preserve" y="728.47" zvalue="1074">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,519.818,849.462) scale(1,1) translate(0,0)" writing-mode="lr" x="519.8200000000001" xml:space="preserve" y="853.96" zvalue="1137">67</text>
  <line fill="none" id="337" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.25000000000045" x2="384.25" y1="153.1204926140824" y2="153.1204926140824" zvalue="1148"/>
  <line fill="none" id="336" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="385.25" x2="385.25" y1="11.25" y2="1041.25" zvalue="1149"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18.25" x2="199.25" y1="165.25" y2="165.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18.25" x2="199.25" y1="191.25" y2="191.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18.25" x2="18.25" y1="165.25" y2="191.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="199.25" y1="165.25" y2="191.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="380.25" y1="165.25" y2="165.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="380.25" y1="191.25" y2="191.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="199.25" y1="165.25" y2="191.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="380.25" x2="380.25" y1="165.25" y2="191.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18.25" x2="199.25" y1="191.25" y2="191.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18.25" x2="199.25" y1="215.5" y2="215.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18.25" x2="18.25" y1="191.25" y2="215.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="199.25" y1="191.25" y2="215.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="380.25" y1="191.25" y2="191.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="380.25" y1="215.5" y2="215.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="199.25" y1="191.25" y2="215.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="380.25" x2="380.25" y1="191.25" y2="215.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18.25" x2="199.25" y1="215.5" y2="215.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18.25" x2="199.25" y1="238.25" y2="238.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18.25" x2="18.25" y1="215.5" y2="238.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="199.25" y1="215.5" y2="238.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="380.25" y1="215.5" y2="215.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="380.25" y1="238.25" y2="238.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="199.25" y1="215.5" y2="238.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="380.25" x2="380.25" y1="215.5" y2="238.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18.25" x2="199.25" y1="238.25" y2="238.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18.25" x2="199.25" y1="261" y2="261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18.25" x2="18.25" y1="238.25" y2="261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="199.25" y1="238.25" y2="261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="380.25" y1="238.25" y2="238.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="380.25" y1="261" y2="261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="199.25" y1="238.25" y2="261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="380.25" x2="380.25" y1="238.25" y2="261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18.25" x2="199.25" y1="261" y2="261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18.25" x2="199.25" y1="283.75" y2="283.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="18.25" x2="18.25" y1="261" y2="283.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="199.25" y1="261" y2="283.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="380.25" y1="261" y2="261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="380.25" y1="283.75" y2="283.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.25" x2="199.25" y1="261" y2="283.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="380.25" x2="380.25" y1="261" y2="283.75"/>
  <line fill="none" id="334" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.25000000000045" x2="384.25" y1="623.1204926140824" y2="623.1204926140824" zvalue="1151"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="69.25" x2="115.0245" y1="445.25" y2="445.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="69.25" x2="115.0245" y1="483.5323" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="69.25" x2="69.25" y1="445.25" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="115.0245" y1="445.25" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="173.8309" y1="445.25" y2="445.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="173.8309" y1="483.5323" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="115.0245" y1="445.25" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="173.8309" y1="445.25" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="232.6373" y1="445.25" y2="445.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="232.6373" y1="483.5323" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="173.8309" y1="445.25" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6373" x2="232.6373" y1="445.25" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6372" x2="291.4436000000001" y1="445.25" y2="445.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6372" x2="291.4436000000001" y1="483.5323" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6372" x2="232.6372" y1="445.25" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="291.4436000000001" y1="445.25" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="350.25" y1="445.25" y2="445.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="350.25" y1="483.5323" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="291.4436000000001" y1="445.25" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="350.25" x2="350.25" y1="445.25" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="69.25" x2="115.0245" y1="483.5323" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="69.25" x2="115.0245" y1="508.2117" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="69.25" x2="69.25" y1="483.5323" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="115.0245" y1="483.5323" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="173.8309" y1="483.5323" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="173.8309" y1="508.2117" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="115.0245" y1="483.5323" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="173.8309" y1="483.5323" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="232.6373" y1="483.5323" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="232.6373" y1="508.2117" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="173.8309" y1="483.5323" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6373" x2="232.6373" y1="483.5323" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6372" x2="291.4436000000001" y1="483.5323" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6372" x2="291.4436000000001" y1="508.2117" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6372" x2="232.6372" y1="483.5323" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="291.4436000000001" y1="483.5323" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="350.25" y1="483.5323" y2="483.5323"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="350.25" y1="508.2117" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="291.4436000000001" y1="483.5323" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="350.25" x2="350.25" y1="483.5323" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="69.25" x2="115.0245" y1="508.2117" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="69.25" x2="115.0245" y1="532.8911000000001" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="69.25" x2="69.25" y1="508.2117" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="115.0245" y1="508.2117" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="173.8309" y1="508.2117" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="173.8309" y1="532.8911000000001" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="115.0245" y1="508.2117" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="173.8309" y1="508.2117" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="232.6373" y1="508.2117" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="232.6373" y1="532.8911000000001" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="173.8309" y1="508.2117" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6373" x2="232.6373" y1="508.2117" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6372" x2="291.4436000000001" y1="508.2117" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6372" x2="291.4436000000001" y1="532.8911000000001" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6372" x2="232.6372" y1="508.2117" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="291.4436000000001" y1="508.2117" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="350.25" y1="508.2117" y2="508.2117"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="350.25" y1="532.8911000000001" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="291.4436000000001" y1="508.2117" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="350.25" x2="350.25" y1="508.2117" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="69.25" x2="115.0245" y1="532.8911000000001" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="69.25" x2="115.0245" y1="557.5705" y2="557.5705"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="69.25" x2="69.25" y1="532.8911000000001" y2="557.5705"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="115.0245" y1="532.8911000000001" y2="557.5705"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="173.8309" y1="532.8911000000001" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="173.8309" y1="557.5705" y2="557.5705"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="115.0245" y1="532.8911000000001" y2="557.5705"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="173.8309" y1="532.8911000000001" y2="557.5705"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="232.6373" y1="532.8911000000001" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="232.6373" y1="557.5705" y2="557.5705"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="173.8309" y1="532.8911000000001" y2="557.5705"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6373" x2="232.6373" y1="532.8911000000001" y2="557.5705"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6372" x2="291.4436000000001" y1="532.8911000000001" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6372" x2="291.4436000000001" y1="557.5705" y2="557.5705"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6372" x2="232.6372" y1="532.8911000000001" y2="557.5705"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="291.4436000000001" y1="532.8911000000001" y2="557.5705"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="350.25" y1="532.8911000000001" y2="532.8911000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="350.25" y1="557.5705" y2="557.5705"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="291.4436000000001" y1="532.8911000000001" y2="557.5705"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="350.25" x2="350.25" y1="532.8911000000001" y2="557.5705"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="69.25" x2="115.0245" y1="557.5706" y2="557.5706"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="69.25" x2="115.0245" y1="582.25" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="69.25" x2="69.25" y1="557.5706" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="115.0245" y1="557.5706" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="173.8309" y1="557.5706" y2="557.5706"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="173.8309" y1="582.25" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="115.0245" y1="557.5706" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="173.8309" y1="557.5706" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="232.6373" y1="557.5706" y2="557.5706"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="232.6373" y1="582.25" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="173.8309" y1="557.5706" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6373" x2="232.6373" y1="557.5706" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6372" x2="291.4436000000001" y1="557.5706" y2="557.5706"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6372" x2="291.4436000000001" y1="582.25" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6372" x2="232.6372" y1="557.5706" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="291.4436000000001" y1="557.5706" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="350.25" y1="557.5706" y2="557.5706"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="350.25" y1="582.25" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="291.4436000000001" y1="557.5706" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="350.25" x2="350.25" y1="557.5706" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="69.25" x2="115.0245" y1="582.25" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="69.25" x2="115.0245" y1="606.9294" y2="606.9294"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="69.25" x2="69.25" y1="582.25" y2="606.9294"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="115.0245" y1="582.25" y2="606.9294"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="173.8309" y1="582.25" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="173.8309" y1="606.9294" y2="606.9294"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="115.0245" x2="115.0245" y1="582.25" y2="606.9294"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="173.8309" y1="582.25" y2="606.9294"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="232.6373" y1="582.25" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="232.6373" y1="606.9294" y2="606.9294"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.8309" x2="173.8309" y1="582.25" y2="606.9294"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6373" x2="232.6373" y1="582.25" y2="606.9294"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6372" x2="291.4436000000001" y1="582.25" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6372" x2="291.4436000000001" y1="606.9294" y2="606.9294"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.6372" x2="232.6372" y1="582.25" y2="606.9294"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="291.4436000000001" y1="582.25" y2="606.9294"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="350.25" y1="582.25" y2="582.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="350.25" y1="606.9294" y2="606.9294"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.4436000000001" x2="291.4436000000001" y1="582.25" y2="606.9294"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="350.25" x2="350.25" y1="582.25" y2="606.9294"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17.25" x2="107.25" y1="938.25" y2="938.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17.25" x2="107.25" y1="977.4132999999999" y2="977.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17.25" x2="17.25" y1="938.25" y2="977.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.25" x2="107.25" y1="938.25" y2="977.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.25" x2="377.25" y1="938.25" y2="938.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.25" x2="377.25" y1="977.4132999999999" y2="977.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.25" x2="107.25" y1="938.25" y2="977.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="377.25" x2="377.25" y1="938.25" y2="977.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17.25" x2="107.25" y1="977.41327" y2="977.41327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17.25" x2="107.25" y1="1005.33167" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17.25" x2="17.25" y1="977.41327" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.25" x2="107.25" y1="977.41327" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.25" x2="197.25" y1="977.41327" y2="977.41327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.25" x2="197.25" y1="1005.33167" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.25" x2="107.25" y1="977.41327" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.25" x2="197.25" y1="977.41327" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.2500000000001" x2="287.2500000000001" y1="977.41327" y2="977.41327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.2500000000001" x2="287.2500000000001" y1="1005.33167" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.2500000000001" x2="197.2500000000001" y1="977.41327" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287.2500000000001" x2="287.2500000000001" y1="977.41327" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287.25" x2="377.25" y1="977.41327" y2="977.41327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287.25" x2="377.25" y1="1005.33167" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287.25" x2="287.25" y1="977.41327" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="377.25" x2="377.25" y1="977.41327" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17.25" x2="107.25" y1="1005.3316" y2="1005.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17.25" x2="107.25" y1="1033.25" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="17.25" x2="17.25" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.25" x2="107.25" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.25" x2="197.25" y1="1005.3316" y2="1005.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.25" x2="197.25" y1="1033.25" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.25" x2="107.25" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.25" x2="197.25" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.2500000000001" x2="287.2500000000001" y1="1005.3316" y2="1005.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.2500000000001" x2="287.2500000000001" y1="1033.25" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.2500000000001" x2="197.2500000000001" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287.2500000000001" x2="287.2500000000001" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287.25" x2="377.25" y1="1005.3316" y2="1005.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287.25" x2="377.25" y1="1033.25" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="287.25" x2="287.25" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="377.25" x2="377.25" y1="1005.3316" y2="1033.25"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="330" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.25,958.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.25" xml:space="preserve" y="964.25" zvalue="1155">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="329" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.25,991.25) scale(1,1) translate(0,0)" writing-mode="lr" x="58.25" xml:space="preserve" y="997.25" zvalue="1156">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="328" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241.25,992.25) scale(1,1) translate(0,0)" writing-mode="lr" x="241.25" xml:space="preserve" y="998.25" zvalue="1157">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="327" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.25,1020.25) scale(1,1) translate(0,0)" writing-mode="lr" x="58.25" xml:space="preserve" y="1026.25" zvalue="1158">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="326" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.25,1020.25) scale(1,1) translate(0,0)" writing-mode="lr" x="240.25" xml:space="preserve" y="1026.25" zvalue="1159">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="325" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,141.778,463.861) scale(1,1) translate(0,9.97782e-14)" writing-mode="lr" x="141.7778049045139" xml:space="preserve" y="468.3611111111111" zvalue="1160">35kV     母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="323" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.75,652.75) scale(1,1) translate(0,0)" writing-mode="lr" x="82.75" xml:space="preserve" y="657.25" zvalue="1162">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="322" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,215.649,320.091) scale(1,1) translate(0,0)" writing-mode="lr" x="215.65" xml:space="preserve" y="324.59" zvalue="1163">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="321" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,320.649,320.091) scale(1,1) translate(0,0)" writing-mode="lr" x="320.65" xml:space="preserve" y="324.59" zvalue="1164">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="320" stroke="rgb(255,255,255)" text-anchor="middle" x="261.1875" xml:space="preserve" y="460.875" zvalue="1165">10kV     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="320" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="261.1875" xml:space="preserve" y="477.875" zvalue="1165">Ⅰ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="319" stroke="rgb(255,255,255)" text-anchor="middle" x="320.25" xml:space="preserve" y="459.75" zvalue="1166">10kV      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="319" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="320.25" xml:space="preserve" y="476.75" zvalue="1166">Ⅱ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="318" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94.25,496) scale(1,1) translate(0,0)" writing-mode="lr" x="94.25" xml:space="preserve" y="500.5" zvalue="1167">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="316" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94.25,521.5) scale(1,1) translate(0,0)" writing-mode="lr" x="94.25" xml:space="preserve" y="526" zvalue="1168">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="315" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94.25,544.5) scale(1,1) translate(0,0)" writing-mode="lr" x="94.25" xml:space="preserve" y="549" zvalue="1169">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="314" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94.25,567.5) scale(1,1) translate(0,0)" writing-mode="lr" x="94.25" xml:space="preserve" y="572" zvalue="1170">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="313" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94.25,594.5) scale(1,1) translate(0,0)" writing-mode="lr" x="94.25" xml:space="preserve" y="599" zvalue="1171">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="312" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,243.96,957.25) scale(1,1) translate(0,0)" writing-mode="lr" x="243.96" xml:space="preserve" y="963.25" zvalue="1172">MengGa-02-2024</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="310" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.25,179.25) scale(1,1) translate(0,0)" writing-mode="lr" x="56.25" xml:space="preserve" y="184.75" zvalue="1174">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="309" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236.25,179.25) scale(1,1) translate(0,0)" writing-mode="lr" x="236.25" xml:space="preserve" y="184.75" zvalue="1175">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="308" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.9375,203.5) scale(1,1) translate(0,0)" writing-mode="lr" x="59.94" xml:space="preserve" y="208" zvalue="1176">35kV母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="303" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.4375,251.25) scale(1,1) translate(0,0)" writing-mode="lr" x="63.44" xml:space="preserve" y="256.75" zvalue="1177">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="299" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,245,250.75) scale(1,1) translate(0,0)" writing-mode="lr" x="245" xml:space="preserve" y="256.25" zvalue="1178">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="298" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.4375,274.25) scale(1,1) translate(0,0)" writing-mode="lr" x="63.44" xml:space="preserve" y="279.75" zvalue="1179">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,245,273.75) scale(1,1) translate(0,0)" writing-mode="lr" x="245" xml:space="preserve" y="279.25" zvalue="1180">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.9375,227.5) scale(1,1) translate(0,0)" writing-mode="lr" x="60.94" xml:space="preserve" y="232" zvalue="1181">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,244,227.25) scale(1,1) translate(0,0)" writing-mode="lr" x="244" xml:space="preserve" y="231.75" zvalue="1182">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,154.25,1019.25) scale(1,1) translate(0,0)" writing-mode="lr" x="154.25" xml:space="preserve" y="1025.25" zvalue="1267">李文杰</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,762,927.125) scale(1,1) translate(0,0)" writing-mode="lr" x="762" xml:space="preserve" y="931.63" zvalue="1278">10kV勐稳线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1479.43,934.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1479.43" xml:space="preserve" y="938.63" zvalue="1280">10kV三仙洞线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,309.235,409.5) scale(1,1) translate(0,0)" writing-mode="lr" x="309.2350463867188" xml:space="preserve" y="414" zvalue="1303">小电流接地</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="268" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,561,626.5) scale(1,1) translate(0,0)" writing-mode="lr" x="561" xml:space="preserve" y="631" zvalue="1314">0904</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="347" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1559,626.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1559" xml:space="preserve" y="631" zvalue="1320">0903</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="364" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1636.04,822.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1636.04" xml:space="preserve" y="827.47" zvalue="1336">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="363" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1643.25,759.951) scale(1,1) translate(0,0)" writing-mode="lr" x="1643.25" xml:space="preserve" y="764.45" zvalue="1338">041</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="362" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1635.87,721.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1635.87" xml:space="preserve" y="726.47" zvalue="1341">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="361" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1601.42,958.173) scale(1,1) translate(0,0)" writing-mode="lr" x="1601.42" xml:space="preserve" y="962.67" zvalue="1345">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="360" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1656.82,849.462) scale(1,1) translate(0,0)" writing-mode="lr" x="1656.82" xml:space="preserve" y="853.96" zvalue="1347">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="380" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1426.9,914.409) scale(1,1) translate(-9.18534e-13,0)" writing-mode="lr" x="1426.9" xml:space="preserve" y="918.91" zvalue="1356">10kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="379" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1409.5,849.123) scale(1,1) translate(0,0)" writing-mode="lr" x="1409.5" xml:space="preserve" y="853.62" zvalue="1358">0378</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="393" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,702.5,856.123) scale(1,1) translate(0,0)" writing-mode="lr" x="702.5" xml:space="preserve" y="860.62" zvalue="1366">0339</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,333.054,1019.5) scale(1,1) translate(0,0)" writing-mode="lr" x="333.05" xml:space="preserve" y="1025.5" zvalue="1375">20241118</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,105.437,331.591) scale(1,1) translate(0,0)" writing-mode="lr" x="105.4374999999995" xml:space="preserve" y="336.0913848876953" zvalue="1378">全站公用</text>
  <ellipse cx="834.66" cy="358.16" fill="rgb(255,0,0)" fill-opacity="1" id="171" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1380"/>
  <ellipse cx="1440.66" cy="363.16" fill="rgb(255,0,0)" fill-opacity="1" id="185" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1382"/>
  <ellipse cx="1600.66" cy="723.16" fill="rgb(255,0,0)" fill-opacity="1" id="193" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1384"/>
 </g>
 <g id="ButtonClass">
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="172.09" y="396.75" zvalue="1263"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="70.19" y="396.75" zvalue="1264"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="70.19" y="356.25" zvalue="1265"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="128">
   <path class="kv35" d="M 537.67 325.59 L 1620 325.59" stroke-width="6" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674399223811" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674399223811"/></metadata>
  <path d="M 537.67 325.59 L 1620 325.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="543">
   <path class="kv10" d="M 1143 689.5 L 1720 689.5" stroke-width="4" zvalue="788"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674399354883" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674399354883"/></metadata>
  <path d="M 1143 689.5 L 1720 689.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="542">
   <path class="kv10" d="M 422 689.5 L 1052 689.5" stroke-width="4" zvalue="790"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674399289347" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674399289347"/></metadata>
  <path d="M 422 689.5 L 1052 689.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="461">
   <use class="kv10" height="20" transform="rotate(180,1426.77,565.042) scale(1.5542,1.35421) translate(-505.989,-144.252)" width="10" x="1418.997344267822" xlink:href="#Breaker:开关_0" y="551.5" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925091590148" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925091590148"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1426.77,565.042) scale(1.5542,1.35421) translate(-505.989,-144.252)" width="10" x="1418.997344267822" y="551.5"/></g>
  <g id="69">
   <use class="kv35" height="20" transform="rotate(0,954.705,236.708) scale(1.5542,1.35421) translate(-337.66,-58.3718)" width="10" x="946.9340345345652" xlink:href="#Breaker:开关_0" y="223.165750510661" zvalue="298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925091655684" ObjectName="35kV帕嘎线331断路器"/>
   <cge:TPSR_Ref TObjectID="6473925091655684"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,954.705,236.708) scale(1.5542,1.35421) translate(-337.66,-58.3718)" width="10" x="946.9340345345652" y="223.165750510661"/></g>
  <g id="170">
   <use class="kv35" height="20" transform="rotate(180,1426.63,405.995) scale(1.5542,1.35421) translate(-505.94,-102.651)" width="10" x="1418.861028219732" xlink:href="#Breaker:开关_0" y="392.4524104979477" zvalue="365"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925091721220" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925091721220"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1426.63,405.995) scale(1.5542,1.35421) translate(-505.94,-102.651)" width="10" x="1418.861028219732" y="392.4524104979477"/></g>
  <g id="235">
   <use class="kv10" height="20" transform="rotate(180,819.149,564.042) scale(1.5542,1.35421) translate(-289.323,-143.99)" width="10" x="811.3781961509221" xlink:href="#Breaker:开关_0" y="550.5" zvalue="736"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925091852292" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473925091852292"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,819.149,564.042) scale(1.5542,1.35421) translate(-289.323,-143.99)" width="10" x="811.3781961509221" y="550.5"/></g>
  <g id="234">
   <use class="kv35" height="20" transform="rotate(180,819.016,400.995) scale(1.5542,1.35421) translate(-289.275,-101.343)" width="10" x="811.2452821514723" xlink:href="#Breaker:开关_0" y="387.4524104979477" zvalue="738"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925091786756" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925091786756"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,819.016,400.995) scale(1.5542,1.35421) translate(-289.275,-101.343)" width="10" x="811.2452821514723" y="387.4524104979477"/></g>
  <g id="478">
   <use class="kv10" height="20" transform="rotate(180,625.402,765.868) scale(1.5542,1.35421) translate(-220.236,-196.78)" width="10" x="617.6311232494973" xlink:href="#Breaker:开关_0" y="752.3258941321849" zvalue="873"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925092114436" ObjectName="10kV中山线032断路器"/>
   <cge:TPSR_Ref TObjectID="6473925092114436"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,625.402,765.868) scale(1.5542,1.35421) translate(-220.236,-196.78)" width="10" x="617.6311232494973" y="752.3258941321849"/></g>
  <g id="464">
   <use class="kv10" height="20" transform="rotate(180,761.402,765.868) scale(1.5542,1.35421) translate(-268.731,-196.78)" width="10" x="753.6311232494973" xlink:href="#Breaker:开关_0" y="752.3258941321849" zvalue="892"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925092048900" ObjectName="10kV勐稳线033断路器"/>
   <cge:TPSR_Ref TObjectID="6473925092048900"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,761.402,765.868) scale(1.5542,1.35421) translate(-268.731,-196.78)" width="10" x="753.6311232494973" y="752.3258941321849"/></g>
  <g id="425">
   <use class="kv10" height="20" transform="rotate(90,1104.5,614.25) scale(1.65,1.25) translate(-431.856,-120.35)" width="10" x="1096.25" xlink:href="#Breaker:母联开关_0" y="601.75" zvalue="934"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925091983364" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473925091983364"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1104.5,614.25) scale(1.65,1.25) translate(-431.856,-120.35)" width="10" x="1096.25" y="601.75"/></g>
  <g id="301">
   <use class="kv10" height="20" transform="rotate(180,479.667,759.201) scale(1.5542,1.35421) translate(-168.269,-195.036)" width="10" x="471.8956698700359" xlink:href="#Breaker:开关_0" y="745.6592274655184" zvalue="953"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925091917828" ObjectName="10kV2号电容器031断路器"/>
   <cge:TPSR_Ref TObjectID="6473925091917828"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,479.667,759.201) scale(1.5542,1.35421) translate(-168.269,-195.036)" width="10" x="471.8956698700359" y="745.6592274655184"/></g>
  <g id="66">
   <use class="kv10" height="20" transform="rotate(180,866.402,765.868) scale(1.5542,1.35421) translate(-306.172,-196.78)" width="10" x="858.6311232494973" xlink:href="#Breaker:开关_0" y="752.3258941321849" zvalue="1014"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925092179972" ObjectName="10kV备用1线034断路器"/>
   <cge:TPSR_Ref TObjectID="6473925092179972"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,866.402,765.868) scale(1.5542,1.35421) translate(-306.172,-196.78)" width="10" x="858.6311232494973" y="752.3258941321849"/></g>
  <g id="166">
   <use class="kv10" height="20" transform="rotate(180,979.402,765.868) scale(1.5542,1.35421) translate(-346.466,-196.78)" width="10" x="971.6311232494973" xlink:href="#Breaker:开关_0" y="752.3258941321849" zvalue="1030"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925092245508" ObjectName="10kV备用2线035断路器"/>
   <cge:TPSR_Ref TObjectID="6473925092245508"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,979.402,765.868) scale(1.5542,1.35421) translate(-346.466,-196.78)" width="10" x="971.6311232494973" y="752.3258941321849"/></g>
  <g id="239">
   <use class="kv10" height="20" transform="rotate(180,1319.4,766.868) scale(1.5542,1.35421) translate(-467.704,-197.042)" width="10" x="1311.631123249497" xlink:href="#Breaker:开关_0" y="753.3258941321849" zvalue="1050"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925092376580" ObjectName="10kV勐戛线036断路器"/>
   <cge:TPSR_Ref TObjectID="6473925092376580"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1319.4,766.868) scale(1.5542,1.35421) translate(-467.704,-197.042)" width="10" x="1311.631123249497" y="753.3258941321849"/></g>
  <g id="221">
   <use class="kv10" height="20" transform="rotate(180,1479.4,766.868) scale(1.5542,1.35421) translate(-524.757,-197.042)" width="10" x="1471.631123249497" xlink:href="#Breaker:开关_0" y="753.3258941321849" zvalue="1065"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925092311044" ObjectName="10kV三仙洞线037断路器"/>
   <cge:TPSR_Ref TObjectID="6473925092311044"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1479.4,766.868) scale(1.5542,1.35421) translate(-524.757,-197.042)" width="10" x="1471.631123249497" y="753.3258941321849"/></g>
  <g id="377">
   <use class="kv10" height="20" transform="rotate(180,1616.67,759.201) scale(1.5542,1.35421) translate(-573.703,-195.036)" width="10" x="1608.895669870036" xlink:href="#Breaker:开关_0" y="745.6592274655184" zvalue="1337"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924608065541" ObjectName="10kV1号电容器041断路器"/>
   <cge:TPSR_Ref TObjectID="6473924608065541"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1616.67,759.201) scale(1.5542,1.35421) translate(-573.703,-195.036)" width="10" x="1608.895669870036" y="745.6592274655184"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="218">
   <use class="kv35" height="51" transform="rotate(0,1412.01,198.368) scale(-1.29959,1.01929) translate(-2492.52,-3.2614)" width="40" x="1386.016457182463" xlink:href="#Accessory:轩岗变互感器_0" y="172.3761755485894" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453779259396" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="51" opacity="0" stroke="white" transform="rotate(0,1412.01,198.368) scale(-1.29959,1.01929) translate(-2492.52,-3.2614)" width="40" x="1386.016457182463" y="172.3761755485894"/></g>
  <g id="15">
   <use class="kv35" height="26" transform="rotate(90,920.306,103.466) scale(0.838049,0.927421) translate(176.876,7.15362)" width="12" x="915.2781809263649" xlink:href="#Accessory:避雷器1_0" y="91.4095937318383" zvalue="312"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453779324932" ObjectName="35kV允姐线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,920.306,103.466) scale(0.838049,0.927421) translate(176.876,7.15362)" width="12" x="915.2781809263649" y="91.4095937318383"/></g>
  <g id="35">
   <use class="kv35" height="30" transform="rotate(270,1042.59,103.768) scale(1.12267,1.49689) translate(-112.081,-26.9924)" width="30" x="1025.75" xlink:href="#Accessory:PT象达_0" y="81.31462834439208" zvalue="319"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453779849220" ObjectName="35kV帕嘎线3319PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1042.59,103.768) scale(1.12267,1.49689) translate(-112.081,-26.9924)" width="30" x="1025.75" y="81.31462834439208"/></g>
  <g id="477">
   <use class="kv10" height="26" transform="rotate(0,652.459,879.719) scale(-0.838049,0.927421) translate(-1431.98,67.9024)" width="12" x="647.4303009724935" xlink:href="#Accessory:避雷器1_0" y="867.6626333680678" zvalue="875"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453782142980" ObjectName="10kV中山线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,652.459,879.719) scale(-0.838049,0.927421) translate(-1431.98,67.9024)" width="12" x="647.4303009724935" y="867.6626333680678"/></g>
  <g id="463">
   <use class="kv10" height="26" transform="rotate(0,788.459,879.719) scale(-0.838049,0.927421) translate(-1730.26,67.9024)" width="12" x="783.4303009724936" xlink:href="#Accessory:避雷器1_0" y="867.6626333680678" zvalue="894"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453781880836" ObjectName="10kV勐稳线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,788.459,879.719) scale(-0.838049,0.927421) translate(-1730.26,67.9024)" width="12" x="783.4303009724936" y="867.6626333680678"/></g>
  <g id="251">
   <use class="kv10" height="30" transform="rotate(180,1583.73,568.5) scale(-1.6,-1.86667) translate(-2563.07,-860.054)" width="35" x="1555.732488060566" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="540.5" zvalue="974"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453781159940" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1583.73,568.5) scale(-1.6,-1.86667) translate(-2563.07,-860.054)" width="35" x="1555.732488060566" y="540.5"/></g>
  <g id="250">
   <use class="kv10" height="26" transform="rotate(0,1618.03,632.781) scale(-0.838049,-0.927421) translate(-3549.71,-1316.03)" width="12" x="1613.00159487896" xlink:href="#Accessory:避雷器1_0" y="620.7244189120901" zvalue="976"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453781094404" ObjectName="10kVⅠ段母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1618.03,632.781) scale(-0.838049,-0.927421) translate(-3549.71,-1316.03)" width="12" x="1613.00159487896" y="620.7244189120901"/></g>
  <g id="245">
   <use class="kv10" height="30" transform="rotate(180,586,578.5) scale(-1.6,-1.86667) translate(-941.75,-875.411)" width="35" x="558" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="550.5" zvalue="982"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453780963332" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,586,578.5) scale(-1.6,-1.86667) translate(-941.75,-875.411)" width="35" x="558" y="550.5"/></g>
  <g id="569">
   <use class="kv10" height="26" transform="rotate(180,728,889.581) scale(-1.2,-1.07067) translate(-1333.17,-1719.53)" width="15" x="719" xlink:href="#Accessory:20210316PT_0" y="875.6626333680678" zvalue="995"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453782339588" ObjectName="10kV勐稳线PT"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,728,889.581) scale(-1.2,-1.07067) translate(-1333.17,-1719.53)" width="15" x="719" y="875.6626333680678"/></g>
  <g id="65">
   <use class="kv10" height="26" transform="rotate(0,893.459,879.719) scale(-0.838049,0.927421) translate(-1960.55,67.9024)" width="12" x="888.4303009724933" xlink:href="#Accessory:避雷器1_0" y="867.6626333680678" zvalue="1016"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453782536196" ObjectName="10kV备用1线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,893.459,879.719) scale(-0.838049,0.927421) translate(-1960.55,67.9024)" width="12" x="888.4303009724933" y="867.6626333680678"/></g>
  <g id="157">
   <use class="kv10" height="26" transform="rotate(0,1006.46,879.719) scale(-0.838049,0.927421) translate(-2208.39,67.9024)" width="12" x="1001.430300972493" xlink:href="#Accessory:避雷器1_0" y="867.6626333680678" zvalue="1032"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453782798340" ObjectName="10kV备用2线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1006.46,879.719) scale(-0.838049,0.927421) translate(-2208.39,67.9024)" width="12" x="1001.430300972493" y="867.6626333680678"/></g>
  <g id="238">
   <use class="kv10" height="26" transform="rotate(0,1346.46,880.719) scale(-0.838049,0.927421) translate(-2954.09,67.9806)" width="12" x="1341.430300972494" xlink:href="#Accessory:避雷器1_0" y="868.6626333680678" zvalue="1052"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453783388164" ObjectName="10kV勐戛线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1346.46,880.719) scale(-0.838049,0.927421) translate(-2954.09,67.9806)" width="12" x="1341.430300972494" y="868.6626333680678"/></g>
  <g id="217">
   <use class="kv10" height="26" transform="rotate(0,1506.46,880.719) scale(-0.838049,0.927421) translate(-3305.01,67.9806)" width="12" x="1501.430300972494" xlink:href="#Accessory:避雷器1_0" y="868.6626333680678" zvalue="1067"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453783126020" ObjectName="10kV三仙洞线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1506.46,880.719) scale(-0.838049,0.927421) translate(-3305.01,67.9806)" width="12" x="1501.430300972494" y="868.6626333680678"/></g>
  <g id="258">
   <use class="kv10" height="26" transform="rotate(0,615.778,632.781) scale(-0.838049,-0.927421) translate(-1351.53,-1316.03)" width="12" x="610.75" xlink:href="#Accessory:避雷器1_0" y="620.7244182542896" zvalue="1140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453836996610" ObjectName="10kVⅡ段母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,615.778,632.781) scale(-0.838049,-0.927421) translate(-1351.53,-1316.03)" width="12" x="610.75" y="620.7244182542896"/></g>
  <g id="355">
   <use class="kv10" height="21" transform="rotate(0,480,796.5) scale(1,1) translate(0,0)" width="8" x="476" xlink:href="#Accessory:中间电缆_0" y="786" zvalue="1329"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450426109957" ObjectName="10kV2号电容器电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,480,796.5) scale(1,1) translate(0,0)" width="8" x="476" y="786"/></g>
  <g id="367">
   <use class="kv10" height="21" transform="rotate(0,1617,796.5) scale(1,1) translate(0,0)" width="8" x="1613" xlink:href="#Accessory:中间电缆_0" y="786" zvalue="1351"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450426175493" ObjectName="10kV1号电容器电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,1617,796.5) scale(1,1) translate(0,0)" width="8" x="1613" y="786"/></g>
  <g id="386">
   <use class="kv10" height="21" transform="rotate(90,1460,837.955) scale(-1.36364,0.952381) translate(-2529.21,41.3977)" width="8" x="1454.545454545455" xlink:href="#Accessory:中间电缆_0" y="827.9545454545455" zvalue="1362"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450426699781" ObjectName="10kV2号站用变电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(90,1460,837.955) scale(-1.36364,0.952381) translate(-2529.21,41.3977)" width="8" x="1454.545454545455" y="827.9545454545455"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="533">
   <use class="kv35" height="30" transform="rotate(180,899.058,202.919) scale(1.53571,-1.53571) translate(-306.125,-327.016)" width="28" x="877.5577567270731" xlink:href="#EnergyConsumer:站用变DY接地_0" y="179.8831160161405" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453780504580" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,899.058,202.919) scale(1.53571,-1.53571) translate(-306.125,-327.016)" width="28" x="877.5577567270731" y="179.8831160161405"/></g>
  <g id="476">
   <use class="kv10" height="30" transform="rotate(0,625.429,893.25) scale(1.25,-1.25) translate(-123.586,-1604.1)" width="12" x="617.9287060935333" xlink:href="#EnergyConsumer:负荷_0" y="874.5" zvalue="876"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453782077444" ObjectName="10kV中山线"/>
   <cge:TPSR_Ref TObjectID="6192453782077444"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,625.429,893.25) scale(1.25,-1.25) translate(-123.586,-1604.1)" width="12" x="617.9287060935333" y="874.5"/></g>
  <g id="64">
   <use class="kv10" height="30" transform="rotate(0,866.429,893.25) scale(1.25,-1.25) translate(-171.786,-1604.1)" width="12" x="858.9287060935333" xlink:href="#EnergyConsumer:负荷_0" y="874.5" zvalue="1017"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453782470660" ObjectName="10kV备用1线"/>
   <cge:TPSR_Ref TObjectID="6192453782470660"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,866.429,893.25) scale(1.25,-1.25) translate(-171.786,-1604.1)" width="12" x="858.9287060935333" y="874.5"/></g>
  <g id="155">
   <use class="kv10" height="30" transform="rotate(0,979.429,893.25) scale(1.25,-1.25) translate(-194.386,-1604.1)" width="12" x="971.9287060935333" xlink:href="#EnergyConsumer:负荷_0" y="874.5" zvalue="1033"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453782732804" ObjectName="10kV备用2线"/>
   <cge:TPSR_Ref TObjectID="6192453782732804"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,979.429,893.25) scale(1.25,-1.25) translate(-194.386,-1604.1)" width="12" x="971.9287060935333" y="874.5"/></g>
  <g id="237">
   <use class="kv10" height="30" transform="rotate(0,1319.43,894.25) scale(1.25,-1.25) translate(-262.386,-1605.9)" width="12" x="1311.928706093533" xlink:href="#EnergyConsumer:负荷_0" y="875.5" zvalue="1053"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453783322628" ObjectName="10kV勐戛线"/>
   <cge:TPSR_Ref TObjectID="6192453783322628"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1319.43,894.25) scale(1.25,-1.25) translate(-262.386,-1605.9)" width="12" x="1311.928706093533" y="875.5"/></g>
  <g id="6">
   <use class="kv10" height="30" transform="rotate(0,762,901) scale(0.583333,-0.5) translate(541.786,-2710.5)" width="12" x="758.5" xlink:href="#EnergyConsumer:负荷_0" y="893.5" zvalue="1277"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453781815300" ObjectName="10kV勐稳线"/>
   <cge:TPSR_Ref TObjectID="6192453781815300"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,762,901) scale(0.583333,-0.5) translate(541.786,-2710.5)" width="12" x="758.5" y="893.5"/></g>
  <g id="383">
   <use class="kv10" height="30" transform="rotate(180,1435.03,888.87) scale(1.26579,-1.26579) translate(-297.61,-1587.11)" width="28" x="1417.311836849839" xlink:href="#EnergyConsumer:站用变DY接地_0" y="869.8831160161405" zvalue="1355"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450426634245" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1435.03,888.87) scale(1.26579,-1.26579) translate(-297.61,-1587.11)" width="28" x="1417.311836849839" y="869.8831160161405"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="53">
   <use class="kv35" height="30" transform="rotate(0,899,152.123) scale(1,1) translate(0,0)" width="15" x="891.5000000000001" xlink:href="#Disconnector:令克_0" y="137.1233757564003" zvalue="171"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453780439044" ObjectName="35kV1号站用变3318隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453780439044"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,899,152.123) scale(1,1) translate(0,0)" width="15" x="891.5000000000001" y="137.1233757564003"/></g>
  <g id="68">
   <use class="kv35" height="30" transform="rotate(0,954.711,171.276) scale(-0.947693,0.6712) translate(-1962.51,78.9704)" width="15" x="947.6034928921569" xlink:href="#Disconnector:刀闸_0" y="161.2075673790615" zvalue="301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453779587076" ObjectName="35kV帕嘎线3316隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453779587076"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,954.711,171.276) scale(-0.947693,0.6712) translate(-1962.51,78.9704)" width="15" x="947.6034928921569" y="161.2075673790615"/></g>
  <g id="20">
   <use class="kv35" height="30" transform="rotate(180,954.892,294.544) scale(0.947693,-0.6712) translate(52.3121,-738.308)" width="15" x="947.7841334259955" xlink:href="#Disconnector:刀闸_0" y="284.4763051659212" zvalue="306"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453779390468" ObjectName="35kV帕嘎线3311隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453779390468"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,954.892,294.544) scale(0.947693,-0.6712) translate(52.3121,-738.308)" width="15" x="947.7841334259955" y="284.4763051659212"/></g>
  <g id="133">
   <use class="kv35" height="30" transform="rotate(0,1426.45,364.158) scale(0.947693,-0.6712) translate(78.3391,-911.637)" width="15" x="1419.337529150985" xlink:href="#Disconnector:刀闸_0" y="354.0900530849659" zvalue="367"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453780176900" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453780176900"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1426.45,364.158) scale(0.947693,-0.6712) translate(78.3391,-911.637)" width="15" x="1419.337529150985" y="354.0900530849659"/></g>
  <g id="304">
   <use class="kv35" height="30" transform="rotate(180,1418.39,267.878) scale(0.947693,-0.6712) translate(77.8946,-671.912)" width="15" x="1411.284133425996" xlink:href="#Disconnector:刀闸_0" y="257.8096384992546" zvalue="567"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453780570116" ObjectName="35kV母线PT3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453780570116"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1418.39,267.878) scale(0.947693,-0.6712) translate(77.8946,-671.912)" width="15" x="1411.284133425996" y="257.8096384992546"/></g>
  <g id="4">
   <use class="kv35" height="30" transform="rotate(270,995,103.577) scale(1,1) translate(0,0)" width="15" x="987.5000000000001" xlink:href="#Disconnector:令克_0" y="88.57733499222041" zvalue="726"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453780635652" ObjectName="35kV帕嘎线3319隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453780635652"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,995,103.577) scale(1,1) translate(0,0)" width="15" x="987.5000000000001" y="88.57733499222041"/></g>
  <g id="121">
   <use class="kv10" height="30" transform="rotate(180,1426.39,608.28) scale(-0.947693,0.6712) translate(-2931.91,293.045)" width="15" x="1419.286649615592" xlink:href="#Disconnector:刀闸_0" y="598.2119653011533" zvalue="732"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453780701188" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453780701188"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1426.39,608.28) scale(-0.947693,0.6712) translate(-2931.91,293.045)" width="15" x="1419.286649615592" y="598.2119653011533"/></g>
  <g id="220">
   <use class="kv35" height="30" transform="rotate(0,819.016,359.158) scale(0.947693,-0.6712) translate(44.8126,-899.188)" width="15" x="811.9085819644126" xlink:href="#Disconnector:刀闸_0" y="349.0900530849659" zvalue="740"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453780897796" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453780897796"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,819.016,359.158) scale(0.947693,-0.6712) translate(44.8126,-899.188)" width="15" x="811.9085819644126" y="349.0900530849659"/></g>
  <g id="192">
   <use class="kv10" height="30" transform="rotate(180,819.143,607.28) scale(-0.947693,0.6712) translate(-1683.89,292.555)" width="15" x="812.0353376160135" xlink:href="#Disconnector:刀闸_0" y="597.2119653011533" zvalue="750"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453780766724" ObjectName="#2主变10kV侧0022隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453780766724"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,819.143,607.28) scale(-0.947693,0.6712) translate(-1683.89,292.555)" width="15" x="812.0353376160135" y="597.2119653011533"/></g>
  <g id="864">
   <use class="kv10" height="30" transform="rotate(180,1583.7,662.904) scale(0.947693,0.6712) translate(87.0185,319.803)" width="15" x="1576.589422933504" xlink:href="#Disconnector:刀闸_0" y="652.8355139911936" zvalue="767"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453782274052" ObjectName="10kVⅠ段母线0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453782274052"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1583.7,662.904) scale(0.947693,0.6712) translate(87.0185,319.803)" width="15" x="1576.589422933504" y="652.8355139911936"/></g>
  <g id="479">
   <use class="kv10" height="30" transform="rotate(180,625.371,818.391) scale(-0.947693,-0.6712) translate(-1285.65,-2042.62)" width="15" x="618.2630441166826" xlink:href="#Disconnector:刀闸_0" y="808.323089599609" zvalue="871"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453782208516" ObjectName="10kV中山线0326隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453782208516"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,625.371,818.391) scale(-0.947693,-0.6712) translate(-1285.65,-2042.62)" width="15" x="618.2630441166826" y="808.323089599609"/></g>
  <g id="472">
   <use class="kv10" height="30" transform="rotate(180,623.689,722.391) scale(-0.947693,-0.6712) translate(-1282.19,-1803.59)" width="15" x="616.5809425293291" xlink:href="#Disconnector:刀闸_0" y="712.3230764122643" zvalue="881"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453782011908" ObjectName="10kV中山线0322隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453782011908"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,623.689,722.391) scale(-0.947693,-0.6712) translate(-1282.19,-1803.59)" width="15" x="616.5809425293291" y="712.3230764122643"/></g>
  <g id="465">
   <use class="kv10" height="30" transform="rotate(180,761.371,818.391) scale(-0.947693,-0.6712) translate(-1565.16,-2042.62)" width="15" x="754.2630441166826" xlink:href="#Disconnector:刀闸_0" y="808.3230764122643" zvalue="890"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453781946372" ObjectName="10kV勐稳线0336隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453781946372"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,761.371,818.391) scale(-0.947693,-0.6712) translate(-1565.16,-2042.62)" width="15" x="754.2630441166826" y="808.3230764122643"/></g>
  <g id="457">
   <use class="kv10" height="30" transform="rotate(180,759.689,722.391) scale(-0.947693,-0.6712) translate(-1561.7,-1803.59)" width="15" x="752.5809425293291" xlink:href="#Disconnector:刀闸_0" y="712.3230764122643" zvalue="900"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453781749764" ObjectName="10kV勐稳线0332隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453781749764"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,759.689,722.391) scale(-0.947693,-0.6712) translate(-1561.7,-1803.59)" width="15" x="752.5809425293291" y="712.3230764122643"/></g>
  <g id="436">
   <use class="kv10" height="30" transform="rotate(0,1026.11,657.568) scale(0.947693,-0.6712) translate(56.2428,-1642.19)" width="15" x="1019" xlink:href="#Disconnector:刀闸_0" y="647.5" zvalue="928"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453781684228" ObjectName="10kV分段0122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453781684228"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1026.11,657.568) scale(0.947693,-0.6712) translate(56.2428,-1642.19)" width="15" x="1019" y="647.5"/></g>
  <g id="434">
   <use class="kv10" height="30" transform="rotate(0,1179.11,657.568) scale(0.947693,-0.6712) translate(64.6875,-1642.19)" width="15" x="1172" xlink:href="#Disconnector:刀闸_0" y="647.5" zvalue="931"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453781618692" ObjectName="10kV分段0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453781618692"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1179.11,657.568) scale(0.947693,-0.6712) translate(64.6875,-1642.19)" width="15" x="1172" y="647.5"/></g>
  <g id="302">
   <use class="kv10" height="30" transform="rotate(180,479.667,822.391) scale(-0.947693,0.6712) translate(-986.2,397.931)" width="15" x="472.5589697813775" xlink:href="#Disconnector:刀闸_0" y="812.3230897637047" zvalue="951"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453781422084" ObjectName="10kV2号电容器0316隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453781422084"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,479.667,822.391) scale(-0.947693,0.6712) translate(-986.2,397.931)" width="15" x="472.5589697813775" y="812.3230897637047"/></g>
  <g id="296">
   <use class="kv10" height="30" transform="rotate(180,477.953,721.391) scale(-0.947693,-0.6712) translate(-982.679,-1801.1)" width="15" x="470.8454891498677" xlink:href="#Disconnector:刀闸_0" y="711.3230764122643" zvalue="958"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453781291012" ObjectName="10kV2号电容器0312隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453781291012"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,477.953,721.391) scale(-0.947693,-0.6712) translate(-982.679,-1801.1)" width="15" x="470.8454891498677" y="711.3230764122643"/></g>
  <g id="248">
   <use class="kv10" height="30" transform="rotate(180,585.965,662.904) scale(0.947693,0.6712) translate(31.9495,319.803)" width="15" x="578.8569348729384" xlink:href="#Disconnector:刀闸_0" y="652.8355143967946" zvalue="978"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453781028868" ObjectName="10kVⅡ段母线0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453781028868"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,585.965,662.904) scale(0.947693,0.6712) translate(31.9495,319.803)" width="15" x="578.8569348729384" y="652.8355143967946"/></g>
  <g id="81">
   <use class="kv10" height="30" transform="rotate(180,866.371,818.391) scale(-0.947693,-0.6712) translate(-1780.95,-2042.62)" width="15" x="859.2630441166826" xlink:href="#Disconnector:刀闸_0" y="808.323089599609" zvalue="1012"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453782601732" ObjectName="10kV备用1线0346隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453782601732"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,866.371,818.391) scale(-0.947693,-0.6712) translate(-1780.95,-2042.62)" width="15" x="859.2630441166826" y="808.323089599609"/></g>
  <g id="60">
   <use class="kv10" height="30" transform="rotate(180,864.689,722.391) scale(-0.947693,-0.6712) translate(-1777.5,-1803.59)" width="15" x="857.5809425293291" xlink:href="#Disconnector:刀闸_0" y="712.3230764122643" zvalue="1022"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453782405124" ObjectName="10kV备用1线0342隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453782405124"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,864.689,722.391) scale(-0.947693,-0.6712) translate(-1777.5,-1803.59)" width="15" x="857.5809425293291" y="712.3230764122643"/></g>
  <g id="182">
   <use class="kv10" height="30" transform="rotate(180,979.371,818.391) scale(-0.947693,-0.6712) translate(-2013.19,-2042.62)" width="15" x="972.2630441166826" xlink:href="#Disconnector:刀闸_0" y="808.323089599609" zvalue="1028"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453782863876" ObjectName="10kV备用2线0356隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453782863876"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,979.371,818.391) scale(-0.947693,-0.6712) translate(-2013.19,-2042.62)" width="15" x="972.2630441166826" y="808.323089599609"/></g>
  <g id="105">
   <use class="kv10" height="30" transform="rotate(180,977.689,722.391) scale(-0.947693,-0.6712) translate(-2009.73,-1803.59)" width="15" x="970.5809425293291" xlink:href="#Disconnector:刀闸_0" y="712.3230764122643" zvalue="1038"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453782667268" ObjectName="10kV备用2线0352隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453782667268"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,977.689,722.391) scale(-0.947693,-0.6712) translate(-2009.73,-1803.59)" width="15" x="970.5809425293291" y="712.3230764122643"/></g>
  <g id="240">
   <use class="kv10" height="30" transform="rotate(180,1319.37,819.391) scale(-0.947693,-0.6712) translate(-2711.96,-2045.11)" width="15" x="1312.263044116683" xlink:href="#Disconnector:刀闸_0" y="809.323089599609" zvalue="1048"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453783453700" ObjectName="10kV勐戛线0366隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453783453700"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1319.37,819.391) scale(-0.947693,-0.6712) translate(-2711.96,-2045.11)" width="15" x="1312.263044116683" y="809.323089599609"/></g>
  <g id="231">
   <use class="kv10" height="30" transform="rotate(180,1317.69,723.391) scale(-0.947693,-0.6712) translate(-2708.5,-1806.08)" width="15" x="1310.580942529329" xlink:href="#Disconnector:刀闸_0" y="713.3230764122643" zvalue="1058"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453783257092" ObjectName="10kV勐戛线0361隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453783257092"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1317.69,723.391) scale(-0.947693,-0.6712) translate(-2708.5,-1806.08)" width="15" x="1310.580942529329" y="713.3230764122643"/></g>
  <g id="222">
   <use class="kv10" height="30" transform="rotate(180,1479.37,819.391) scale(-0.947693,-0.6712) translate(-3040.79,-2045.11)" width="15" x="1472.263044116683" xlink:href="#Disconnector:刀闸_0" y="809.3230764122643" zvalue="1063"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453783191556" ObjectName="10kV三仙洞线0376隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453783191556"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1479.37,819.391) scale(-0.947693,-0.6712) translate(-3040.79,-2045.11)" width="15" x="1472.263044116683" y="809.3230764122643"/></g>
  <g id="211">
   <use class="kv10" height="30" transform="rotate(180,1477.69,723.391) scale(-0.947693,-0.6712) translate(-3037.33,-1806.08)" width="15" x="1470.580942529329" xlink:href="#Disconnector:刀闸_0" y="713.3230764122643" zvalue="1073"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453782994948" ObjectName="10kV三仙洞线0371隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453782994948"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1477.69,723.391) scale(-0.947693,-0.6712) translate(-3037.33,-1806.08)" width="15" x="1470.580942529329" y="713.3230764122643"/></g>
  <g id="252">
   <use class="kv10" height="30" transform="rotate(0,586,623.5) scale(1,-1) translate(0,-1247)" width="30" x="571" xlink:href="#Disconnector:跌落刀闸_0" y="608.5" zvalue="1313"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450425978885" ObjectName="10kVⅡ段母线电压互感器0904隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450425978885"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,586,623.5) scale(1,-1) translate(0,-1247)" width="30" x="571" y="608.5"/></g>
  <g id="344">
   <use class="kv10" height="30" transform="rotate(0,1584,623.5) scale(1,-1) translate(0,-1247)" width="30" x="1569" xlink:href="#Disconnector:跌落刀闸_0" y="608.5" zvalue="1319"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450426044421" ObjectName="10kVⅠ段母线电压互感器0903隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450426044421"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1584,623.5) scale(1,-1) translate(0,-1247)" width="30" x="1569" y="608.5"/></g>
  <g id="378">
   <use class="kv10" height="30" transform="rotate(180,1616.67,822.391) scale(-0.947693,0.6712) translate(-3322.96,397.931)" width="15" x="1609.558969781378" xlink:href="#Disconnector:刀闸_0" y="812.3230897637047" zvalue="1335"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450426503173" ObjectName="10kV1号电容器0416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450426503173"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1616.67,822.391) scale(-0.947693,0.6712) translate(-3322.96,397.931)" width="15" x="1609.558969781378" y="812.3230897637047"/></g>
  <g id="375">
   <use class="kv10" height="30" transform="rotate(180,1614.95,721.391) scale(-0.947693,-0.6712) translate(-3319.43,-1801.1)" width="15" x="1607.845489149868" xlink:href="#Disconnector:刀闸_0" y="711.3230764122643" zvalue="1340"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450426437637" ObjectName="10kV1号电容器0411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450426437637"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1614.95,721.391) scale(-0.947693,-0.6712) translate(-3319.43,-1801.1)" width="15" x="1607.845489149868" y="711.3230764122643"/></g>
  <g id="382">
   <use class="kv10" height="30" transform="rotate(0,1435,850.123) scale(1,1) translate(0,0)" width="15" x="1427.5" xlink:href="#Disconnector:令克_0" y="835.1233757564003" zvalue="1357"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450426568709" ObjectName="10kV2号站用变0378隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450426568709"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1435,850.123) scale(1,1) translate(0,0)" width="15" x="1427.5" y="835.1233757564003"/></g>
  <g id="388">
   <use class="kv10" height="30" transform="rotate(0,728,857.123) scale(1,1) translate(0,0)" width="15" x="720.5000000000001" xlink:href="#Disconnector:令克_0" y="842.1233757564003" zvalue="1365"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450426765317" ObjectName="10kV勐稳线0339隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450426765317"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,728,857.123) scale(1,1) translate(0,0)" width="15" x="720.5000000000001" y="842.1233757564003"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="70">
   <use class="kv35" height="30" transform="rotate(0,954.628,81.5317) scale(1.98323,0.522926) translate(-469.836,67.2264)" width="7" x="947.6867041796982" xlink:href="#ACLineSegment:线路_0" y="73.68776459659705" zvalue="297"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249366462469" ObjectName="35kV帕嘎线"/>
   <cge:TPSR_Ref TObjectID="8444249366462469_5066549678964737"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,954.628,81.5317) scale(1.98323,0.522926) translate(-469.836,67.2264)" width="7" x="947.6867041796982" y="73.68776459659705"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="67">
   <use class="kv35" height="20" transform="rotate(90,984.499,136.002) scale(1.24619,-1.0068) translate(-193.259,-271.018)" width="10" x="978.2682271410732" xlink:href="#GroundDisconnector:地刀_0" y="125.9342824715292" zvalue="303"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453779521540" ObjectName="35kV帕嘎线33167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453779521540"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,984.499,136.002) scale(1.24619,-1.0068) translate(-193.259,-271.018)" width="10" x="978.2682271410732" y="125.9342824715292"/></g>
  <g id="33">
   <use class="kv35" height="20" transform="rotate(90,984.499,264.002) scale(1.24619,-1.0068) translate(-193.259,-526.153)" width="10" x="978.2682271410732" xlink:href="#GroundDisconnector:地刀_0" y="253.9342824715292" zvalue="315"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453779783684" ObjectName="35kV帕嘎线33117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453779783684"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,984.499,264.002) scale(1.24619,-1.0068) translate(-193.259,-526.153)" width="10" x="978.2682271410732" y="253.9342824715292"/></g>
  <g id="103">
   <use class="kv35" height="20" transform="rotate(90,1464.32,229.82) scale(1.24619,-1.0068) translate(-288.048,-458.021)" width="10" x="1458.086408959255" xlink:href="#GroundDisconnector:地刀_0" y="219.752464289711" zvalue="353"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453779980292" ObjectName="35kV母线PT39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453779980292"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1464.32,229.82) scale(1.24619,-1.0068) translate(-288.048,-458.021)" width="10" x="1458.086408959255" y="219.752464289711"/></g>
  <g id="114">
   <use class="kv35" height="20" transform="rotate(90,1464.32,297.82) scale(1.24619,-1.0068) translate(-288.048,-593.561)" width="10" x="1458.086408959255" xlink:href="#GroundDisconnector:地刀_0" y="287.752464289711" zvalue="357"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453780111364" ObjectName="35kV母线PT39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453780111364"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1464.32,297.82) scale(1.24619,-1.0068) translate(-288.048,-593.561)" width="10" x="1458.086408959255" y="287.752464289711"/></g>
  <g id="294">
   <use class="kv35" height="20" transform="rotate(90,984.499,196.002) scale(1.24619,-1.0068) translate(-193.259,-390.613)" width="10" x="978.2682271410732" xlink:href="#GroundDisconnector:地刀_0" y="185.9342824715292" zvalue="553"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453780373508" ObjectName="35kV帕嘎线33160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453780373508"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,984.499,196.002) scale(1.24619,-1.0068) translate(-193.259,-390.613)" width="10" x="978.2682271410732" y="185.9342824715292"/></g>
  <g id="254">
   <use class="kv10" height="40" transform="rotate(0,495.919,855.453) scale(1.00815,-0.814487) translate(-3.92582,-1909.46)" width="20" x="485.8370727258789" xlink:href="#GroundDisconnector:支那变双联地刀_0" y="839.1629272741211" zvalue="1136"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453836931074" ObjectName="10kV2号电容器03167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453836931074"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,495.919,855.453) scale(1.00815,-0.814487) translate(-3.92582,-1909.46)" width="20" x="485.8370727258789" y="839.1629272741211"/></g>
  <g id="371">
   <use class="kv10" height="40" transform="rotate(0,1632.92,855.453) scale(1.00815,-0.814487) translate(-13.1134,-1909.46)" width="20" x="1622.837072725879" xlink:href="#GroundDisconnector:支那变双联地刀_0" y="839.1629272741211" zvalue="1346"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450426306565" ObjectName="10kV1号电容器04167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450426306565"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1632.92,855.453) scale(1.00815,-0.814487) translate(-13.1134,-1909.46)" width="20" x="1622.837072725879" y="839.1629272741211"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="21">
   <path class="kv35" d="M 954.65 223.75 L 954.65 181.17" stroke-width="1" zvalue="305"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="68@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 954.65 223.75 L 954.65 181.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv35" d="M 954.63 161.54 L 954.63 89.3" stroke-width="1" zvalue="308"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="70@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 954.63 161.54 L 954.63 89.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv35" d="M 931.78 103.49 L 954.63 103.49" stroke-width="1" zvalue="309"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 931.78 103.49 L 954.63 103.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv35" d="M 954.81 249.64 L 954.81 284.81" stroke-width="1" zvalue="310"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@1" LinkObjectIDznd="20@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 954.81 249.64 L 954.81 284.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv35" d="M 974.68 136.06 L 954.63 136.06" stroke-width="1" zvalue="311"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 974.68 136.06 L 954.63 136.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv35" d="M 954.83 304.44 L 954.83 325.59" stroke-width="1" zvalue="313"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@1" LinkObjectIDznd="128@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 954.83 304.44 L 954.83 325.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv35" d="M 974.68 264.06 L 954.81 264.06" stroke-width="1" zvalue="317"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="17" MaxPinNum="2"/>
   </metadata>
  <path d="M 974.68 264.06 L 954.81 264.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv35" d="M 1454.5 229.88 L 1418.31 229.88" stroke-width="1" zvalue="355"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="305" MaxPinNum="2"/>
   </metadata>
  <path d="M 1454.5 229.88 L 1418.31 229.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv35" d="M 1454.5 297.88 L 1418.33 297.88" stroke-width="1" zvalue="359"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="306" MaxPinNum="2"/>
   </metadata>
  <path d="M 1454.5 297.88 L 1418.33 297.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="kv35" d="M 1426.5 354.26 L 1426.5 325.59" stroke-width="1" zvalue="369"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@1" LinkObjectIDznd="128@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426.5 354.26 L 1426.5 325.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv35" d="M 1426.53 393.06 L 1426.53 373.89" stroke-width="1" zvalue="370"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@1" LinkObjectIDznd="133@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426.53 393.06 L 1426.53 373.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="293">
   <path class="kv35" d="M 974.68 196.06 L 954.65 196.06" stroke-width="1" zvalue="555"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="294@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 974.68 196.06 L 954.65 196.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="305">
   <path class="kv35" d="M 1418.3 223.7 L 1418.31 258.14" stroke-width="1" zvalue="568"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@0" LinkObjectIDznd="304@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1418.3 223.7 L 1418.31 258.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="306">
   <path class="kv35" d="M 1418.33 277.77 L 1418.33 325.59" stroke-width="1" zvalue="569"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="304@1" LinkObjectIDznd="128@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1418.33 277.77 L 1418.33 325.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv35" d="M 898.92 164.37 L 898.92 180.8" stroke-width="1" zvalue="723"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="533@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.92 164.37 L 898.92 180.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv35" d="M 954.63 122.5 L 899.08 122.5 L 899.08 138.87" stroke-width="1" zvalue="724"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="19" LinkObjectIDznd="53@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 954.63 122.5 L 899.08 122.5 L 899.08 138.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv35" d="M 1031.61 103.21 L 1007.25 103.21" stroke-width="1" zvalue="727"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="4@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1031.61 103.21 L 1007.25 103.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv35" d="M 981.75 103.49 L 954.63 103.49" stroke-width="1" zvalue="728"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="4@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 981.75 103.49 L 954.63 103.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv10" d="M 1426.45 598.38 L 1426.45 578" stroke-width="1" zvalue="733"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@1" LinkObjectIDznd="461@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426.45 598.38 L 1426.45 578" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="219">
   <path class="kv35" d="M 819.07 349.26 L 819.07 325.59" stroke-width="1" zvalue="742"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@1" LinkObjectIDznd="128@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 819.07 349.26 L 819.07 325.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="kv35" d="M 818.91 388.06 L 818.91 368.89" stroke-width="1" zvalue="743"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@1" LinkObjectIDznd="220@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.91 388.06 L 818.91 368.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv35" d="M 819.07 438.23 L 819.07 413.95" stroke-width="1" zvalue="748"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="234@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 819.07 438.23 L 819.07 413.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="kv10" d="M 819.2 597.38 L 819.2 577" stroke-width="1" zvalue="751"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@1" LinkObjectIDznd="235@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 819.2 597.38 L 819.2 577" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="475">
   <path class="kv10" d="M 625.43 828.29 L 625.43 876.38" stroke-width="1" zvalue="877"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="479@1" LinkObjectIDznd="476@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 625.43 828.29 L 625.43 876.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="474">
   <path class="kv10" d="M 652.43 868.25 L 652.43 854.33 L 625.43 854.33" stroke-width="1" zvalue="879"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="477@0" LinkObjectIDznd="475" MaxPinNum="2"/>
   </metadata>
  <path d="M 652.43 868.25 L 652.43 854.33 L 625.43 854.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="473">
   <path class="kv10" d="M 625.45 778.82 L 625.45 808.66" stroke-width="1" zvalue="880"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="478@0" LinkObjectIDznd="479@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 625.45 778.82 L 625.45 808.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="471">
   <path class="kv10" d="M 623.75 732.29 L 623.75 752.94" stroke-width="1" zvalue="883"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="472@1" LinkObjectIDznd="478@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 623.75 732.29 L 623.75 752.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="470">
   <path class="kv10" d="M 623.77 712.66 L 623.77 689.5" stroke-width="1" zvalue="884"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="472@0" LinkObjectIDznd="542@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 623.77 712.66 L 623.77 689.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="460">
   <path class="kv10" d="M 761.43 828.29 L 761.43 894.25" stroke-width="1" zvalue="896"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="465@1" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 761.43 828.29 L 761.43 894.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="458">
   <path class="kv10" d="M 761.45 778.82 L 761.45 808.66" stroke-width="1" zvalue="899"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="464@0" LinkObjectIDznd="465@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 761.45 778.82 L 761.45 808.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="456">
   <path class="kv10" d="M 759.75 732.29 L 759.75 752.94" stroke-width="1" zvalue="902"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="457@1" LinkObjectIDznd="464@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 759.75 732.29 L 759.75 752.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="455">
   <path class="kv10" d="M 759.77 712.66 L 759.77 689.5" stroke-width="1" zvalue="903"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="457@0" LinkObjectIDznd="542@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 759.77 712.66 L 759.77 689.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="424">
   <path class="kv10" d="M 1026.17 647.67 L 1026.17 614.41 L 1092.13 614.41" stroke-width="1" zvalue="935"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="436@1" LinkObjectIDznd="425@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1026.17 647.67 L 1026.17 614.41 L 1092.13 614.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="421">
   <path class="kv10" d="M 1116.77 614.34 L 1179.17 614.34 L 1179.17 647.67" stroke-width="1" zvalue="937"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="425@1" LinkObjectIDznd="434@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1116.77 614.34 L 1179.17 614.34 L 1179.17 647.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="420">
   <path class="kv10" d="M 1583.61 672.64 L 1583.61 689.5" stroke-width="1" zvalue="938"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="864@0" LinkObjectIDznd="543@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1583.61 672.64 L 1583.61 689.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="297">
   <path class="kv10" d="M 479.72 772.16 L 479.72 812.5" stroke-width="1" zvalue="957"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="301@0" LinkObjectIDznd="302@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 479.72 772.16 L 479.72 812.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="295">
   <path class="kv10" d="M 478.01 731.29 L 478.01 746.27" stroke-width="1" zvalue="960"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="296@1" LinkObjectIDznd="301@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 478.01 731.29 L 478.01 746.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="291">
   <path class="kv10" d="M 478.04 711.66 L 478.04 689.5" stroke-width="1" zvalue="961"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="296@0" LinkObjectIDznd="542@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 478.04 711.66 L 478.04 689.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="567">
   <path class="kv10" d="M 585.88 672.64 L 585.88 689.5" stroke-width="1" zvalue="992"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@0" LinkObjectIDznd="542@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 585.88 672.64 L 585.88 689.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="568">
   <path class="kv10" d="M 819.23 617.02 L 819.23 689.5" stroke-width="1" zvalue="993"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="542@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 819.23 617.02 L 819.23 689.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv10" d="M 866.43 828.29 L 866.43 876.38" stroke-width="1" zvalue="1018"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@1" LinkObjectIDznd="64@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.43 828.29 L 866.43 876.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv10" d="M 893.43 868.25 L 893.43 854.33 L 866.43 854.33" stroke-width="1" zvalue="1020"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="63" MaxPinNum="2"/>
   </metadata>
  <path d="M 893.43 868.25 L 893.43 854.33 L 866.43 854.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv10" d="M 866.45 778.82 L 866.45 808.66" stroke-width="1" zvalue="1021"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="81@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.45 778.82 L 866.45 808.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv10" d="M 864.75 732.29 L 864.75 752.94" stroke-width="1" zvalue="1024"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@1" LinkObjectIDznd="66@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 864.75 732.29 L 864.75 752.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv10" d="M 864.77 712.66 L 864.77 689.5" stroke-width="1" zvalue="1025"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="542@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 864.77 712.66 L 864.77 689.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv10" d="M 979.43 828.29 L 979.43 876.38" stroke-width="1" zvalue="1034"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@1" LinkObjectIDznd="155@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 979.43 828.29 L 979.43 876.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv10" d="M 1006.43 868.25 L 1006.43 854.33 L 979.43 854.33" stroke-width="1" zvalue="1036"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="150" MaxPinNum="2"/>
   </metadata>
  <path d="M 1006.43 868.25 L 1006.43 854.33 L 979.43 854.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv10" d="M 979.45 778.82 L 979.45 808.66" stroke-width="1" zvalue="1037"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="182@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 979.45 778.82 L 979.45 808.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv10" d="M 977.75 732.29 L 977.75 752.94" stroke-width="1" zvalue="1040"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@1" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 977.75 732.29 L 977.75 752.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv10" d="M 977.77 712.66 L 977.77 689.5" stroke-width="1" zvalue="1041"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="542@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 977.77 712.66 L 977.77 689.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv10" d="M 1026.19 667.3 L 1026.19 678.4 L 1026.19 678.4 L 1026.19 689.5" stroke-width="1" zvalue="1044"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="436@0" LinkObjectIDznd="542@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1026.19 667.3 L 1026.19 678.4 L 1026.19 678.4 L 1026.19 689.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv10" d="M 1179.19 667.3 L 1179.19 689.5" stroke-width="1" zvalue="1045"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="434@0" LinkObjectIDznd="543@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1179.19 667.3 L 1179.19 689.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv10" d="M 1426.48 618.02 L 1426.48 689.5" stroke-width="1" zvalue="1046"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@0" LinkObjectIDznd="543@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426.48 618.02 L 1426.48 689.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="kv10" d="M 1346.43 869.25 L 1346.43 853.5 L 1319.43 853.5" stroke-width="1" zvalue="1056"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="238@0" LinkObjectIDznd="384" MaxPinNum="2"/>
   </metadata>
  <path d="M 1346.43 869.25 L 1346.43 853.5 L 1319.43 853.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="kv10" d="M 1319.45 779.82 L 1319.45 809.66" stroke-width="1" zvalue="1057"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="239@0" LinkObjectIDznd="240@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1319.45 779.82 L 1319.45 809.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="230">
   <path class="kv10" d="M 1317.75 733.29 L 1317.75 753.94" stroke-width="1" zvalue="1060"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@1" LinkObjectIDznd="239@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1317.75 733.29 L 1317.75 753.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="kv10" d="M 1317.77 713.66 L 1317.77 689.5" stroke-width="1" zvalue="1061"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@0" LinkObjectIDznd="543@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1317.77 713.66 L 1317.77 689.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv10" d="M 1479.43 829.29 L 1479.43 893.58" stroke-width="1" zvalue="1069"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="222@1" LinkObjectIDznd="34@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1479.43 829.29 L 1479.43 893.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv10" d="M 1506.43 869.25 L 1506.43 855.33 L 1479.43 855.33" stroke-width="1" zvalue="1071"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@0" LinkObjectIDznd="215" MaxPinNum="2"/>
   </metadata>
  <path d="M 1506.43 869.25 L 1506.43 855.33 L 1479.43 855.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv10" d="M 1479.45 779.82 L 1479.45 809.66" stroke-width="1" zvalue="1072"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="222@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1479.45 779.82 L 1479.45 809.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv10" d="M 1477.75 733.29 L 1477.75 753.94" stroke-width="1" zvalue="1075"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@1" LinkObjectIDznd="221@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1477.75 733.29 L 1477.75 753.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv10" d="M 1477.77 713.66 L 1477.77 689.5" stroke-width="1" zvalue="1076"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@0" LinkObjectIDznd="543@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1477.77 713.66 L 1477.77 689.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv35" d="M 1426.93 433.89 L 1426.93 418.95" stroke-width="1" zvalue="1256"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="290@0" LinkObjectIDznd="170@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426.93 433.89 L 1426.93 418.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="kv10" d="M 1426.66 552.11 L 1426.66 501.41" stroke-width="1" zvalue="1258"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="461@1" LinkObjectIDznd="290@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426.66 552.11 L 1426.66 501.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="253">
   <path class="kv10" d="M 819.05 551.11 L 819.05 505.75" stroke-width="1" zvalue="1298"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@1" LinkObjectIDznd="202@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 819.05 551.11 L 819.05 505.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="333">
   <path class="kv10" d="M 585.91 653.01 L 586 638.5" stroke-width="1" zvalue="1314"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@1" LinkObjectIDznd="252@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 585.91 653.01 L 586 638.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="341">
   <path class="kv10" d="M 586 609.5 L 586.1 603" stroke-width="1" zvalue="1315"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252@1" LinkObjectIDznd="245@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 586 609.5 L 586.1 603" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="343">
   <path class="kv10" d="M 615.75 644.25 L 615.75 650.49 L 585.92 650.49" stroke-width="1" zvalue="1317"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="258@0" LinkObjectIDznd="333" MaxPinNum="2"/>
   </metadata>
  <path d="M 615.75 644.25 L 615.75 650.49 L 585.92 650.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="348">
   <path class="kv10" d="M 1584 638.5 L 1584 653.01" stroke-width="1" zvalue="1322"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="344@0" LinkObjectIDznd="864@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1584 638.5 L 1584 653.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="349">
   <path class="kv10" d="M 1584 609.5 L 1584 593" stroke-width="1" zvalue="1323"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="344@1" LinkObjectIDznd="251@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1584 609.5 L 1584 593" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="350">
   <path class="kv10" d="M 1618 644.25 L 1618 650.5 L 1583.64 650.5" stroke-width="1" zvalue="1324"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@0" LinkObjectIDznd="348" MaxPinNum="2"/>
   </metadata>
  <path d="M 1618 644.25 L 1618 650.5 L 1583.64 650.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="354">
   <path class="kv10" d="M 479.75 832.13 L 479.75 874.22" stroke-width="1" zvalue="1328"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="302@0" LinkObjectIDznd="285@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 479.75 832.13 L 479.75 874.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="358">
   <path class="kv10" d="M 491.89 855.05 L 491.89 869.5 L 479.75 869.5" stroke-width="1" zvalue="1332"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="254@0" LinkObjectIDznd="354" MaxPinNum="2"/>
   </metadata>
  <path d="M 491.89 855.05 L 491.89 869.5 L 479.75 869.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="359">
   <path class="kv10" d="M 480 796.5 L 479.72 796.5" stroke-width="1" zvalue="1333"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="355@0" LinkObjectIDznd="297" MaxPinNum="2"/>
   </metadata>
  <path d="M 480 796.5 L 479.72 796.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="376">
   <path class="kv10" d="M 1616.72 772.16 L 1616.72 812.5" stroke-width="1" zvalue="1339"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="377@0" LinkObjectIDznd="378@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1616.72 772.16 L 1616.72 812.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="374">
   <path class="kv10" d="M 1615.01 731.29 L 1615.01 746.27" stroke-width="1" zvalue="1342"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="375@1" LinkObjectIDznd="377@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1615.01 731.29 L 1615.01 746.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="373">
   <path class="kv10" d="M 1615.04 711.66 L 1615.04 689.5" stroke-width="1" zvalue="1343"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="375@0" LinkObjectIDznd="543@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1615.04 711.66 L 1615.04 689.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="368">
   <path class="kv10" d="M 1616.75 832.13 L 1616.75 874.22" stroke-width="1" zvalue="1350"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="378@0" LinkObjectIDznd="372@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1616.75 832.13 L 1616.75 874.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="366">
   <path class="kv10" d="M 1628.89 855.05 L 1628.89 869.5 L 1616.75 869.5" stroke-width="1" zvalue="1352"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="371@0" LinkObjectIDznd="368" MaxPinNum="2"/>
   </metadata>
  <path d="M 1628.89 855.05 L 1628.89 869.5 L 1616.75 869.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="365">
   <path class="kv10" d="M 1617 796.5 L 1616.72 796.5" stroke-width="1" zvalue="1353"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="367@0" LinkObjectIDznd="376" MaxPinNum="2"/>
   </metadata>
  <path d="M 1617 796.5 L 1616.72 796.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="381">
   <path class="kv10" d="M 1434.92 862.37 L 1434.92 870.64" stroke-width="1" zvalue="1359"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="382@1" LinkObjectIDznd="383@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1434.92 862.37 L 1434.92 870.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="384">
   <path class="kv10" d="M 1319.43 829.29 L 1319.43 877.38" stroke-width="1" zvalue="1360"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="240@1" LinkObjectIDznd="237@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1319.43 829.29 L 1319.43 877.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="387">
   <path class="kv10" d="M 1460 837.95 L 1460 836.87" stroke-width="1" zvalue="1363"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="386@0" LinkObjectIDznd="55" MaxPinNum="2"/>
   </metadata>
  <path d="M 1460 837.95 L 1460 836.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="389">
   <path class="kv10" d="M 788.43 868.25 L 788.43 836.5 L 761.43 836.5" stroke-width="1" zvalue="1366"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="463@0" LinkObjectIDznd="460" MaxPinNum="2"/>
   </metadata>
  <path d="M 788.43 868.25 L 788.43 836.5 L 761.43 836.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="391">
   <path class="kv10" d="M 727.94 875.77 L 727.92 869.37" stroke-width="1" zvalue="1368"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="569@0" LinkObjectIDznd="388@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 727.94 875.77 L 727.92 869.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="392">
   <path class="kv10" d="M 728.08 843.87 L 728.08 837.5 L 761.43 837.5" stroke-width="1" zvalue="1369"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="388@0" LinkObjectIDznd="460" MaxPinNum="2"/>
   </metadata>
  <path d="M 728.08 843.87 L 728.08 837.5 L 761.43 837.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv10" d="M 1435.08 836.87 L 1458 836.87 L 1479.43 836.87" stroke-width="1" zvalue="1376"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="382@0" LinkObjectIDznd="215" MaxPinNum="2"/>
   </metadata>
  <path d="M 1435.08 836.87 L 1458 836.87 L 1479.43 836.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="290">
   <g id="2900">
    <use class="kv35" height="30" transform="rotate(0,1426.9,467.5) scale(2.05085,2.41423) translate(-718.533,-252.643)" width="24" x="1402.29" xlink:href="#PowerTransformer2:可调不带中性点_0" y="431.29" zvalue="550"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874564763650" ObjectName="35"/>
    </metadata>
   </g>
   <g id="2901">
    <use class="kv10" height="30" transform="rotate(0,1426.9,467.5) scale(2.05085,2.41423) translate(-718.533,-252.643)" width="24" x="1402.29" xlink:href="#PowerTransformer2:可调不带中性点_1" y="431.29" zvalue="550"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874564829186" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399523565570" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399523565570"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1426.9,467.5) scale(2.05085,2.41423) translate(-718.533,-252.643)" width="24" x="1402.29" y="431.29"/></g>
  <g id="202">
   <g id="2020">
    <use class="kv35" height="30" transform="rotate(0,819.046,471.842) scale(2.05085,2.41423) translate(-407.067,-255.187)" width="24" x="794.4400000000001" xlink:href="#PowerTransformer2:可调不带中性点_0" y="435.63" zvalue="746"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874564894722" ObjectName="35"/>
    </metadata>
   </g>
   <g id="2021">
    <use class="kv10" height="30" transform="rotate(0,819.046,471.842) scale(2.05085,2.41423) translate(-407.067,-255.187)" width="24" x="794.4400000000001" xlink:href="#PowerTransformer2:可调不带中性点_1" y="435.63" zvalue="746"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874564960258" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399523631106" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399523631106"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,819.046,471.842) scale(2.05085,2.41423) translate(-407.067,-255.187)" width="24" x="794.4400000000001" y="435.63"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="285">
   <use class="kv10" height="55" transform="rotate(0,479.792,899.086) scale(1.61191,1.32413) translate(-171.429,-211.172)" width="35" x="451.5831856894674" xlink:href="#Compensator:弄彦电容_0" y="862.6728099342654" zvalue="962"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453781225476" ObjectName="10kV2号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453781225476"/></metadata>
  <rect fill="white" height="55" opacity="0" stroke="white" transform="rotate(0,479.792,899.086) scale(1.61191,1.32413) translate(-171.429,-211.172)" width="35" x="451.5831856894674" y="862.6728099342654"/></g>
  <g id="372">
   <use class="kv10" height="55" transform="rotate(0,1616.79,899.086) scale(1.61191,1.32413) translate(-603.054,-211.172)" width="35" x="1588.583185689467" xlink:href="#Compensator:弄彦电容_0" y="862.6728099342654" zvalue="1344"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450426372101" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192450426372101"/></metadata>
  <rect fill="white" height="55" opacity="0" stroke="white" transform="rotate(0,1616.79,899.086) scale(1.61191,1.32413) translate(-603.054,-211.172)" width="35" x="1588.583185689467" y="862.6728099342654"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="2" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,625.429,950) scale(1,1) translate(0,0)" writing-mode="lr" x="624.96" xml:space="preserve" y="954.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132995837956" ObjectName="P"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="7" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,866.429,950) scale(1,1) translate(0,0)" writing-mode="lr" x="865.96" xml:space="preserve" y="954.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132998328323" ObjectName="P"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="8" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,979.429,950) scale(1,1) translate(0,0)" writing-mode="lr" x="978.96" xml:space="preserve" y="954.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132999770116" ObjectName="P"/>
   </metadata>
  </g>
  <g id="25">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="25" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1319.43,951) scale(1,1) translate(0,0)" writing-mode="lr" x="1318.96" xml:space="preserve" y="955.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133002653699" ObjectName="P"/>
   </metadata>
  </g>
  <g id="26">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="26" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,625.429,973) scale(1,1) translate(0,0)" writing-mode="lr" x="624.96" xml:space="preserve" y="977.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132995903491" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="39" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,866.429,973) scale(1,1) translate(0,0)" writing-mode="lr" x="865.96" xml:space="preserve" y="977.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132998393860" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="40">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="40" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,979.429,973) scale(1,1) translate(0,0)" writing-mode="lr" x="978.96" xml:space="preserve" y="977.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132999835651" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="43">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="43" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1319.43,974) scale(1,1) translate(0,0)" writing-mode="lr" x="1318.96" xml:space="preserve" y="978.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133002719235" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="44">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="44" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,625.429,996) scale(1,1) translate(0,0)" writing-mode="lr" x="624.96" xml:space="preserve" y="1000.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132995969027" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="57" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,866.429,996) scale(1,1) translate(0,0)" writing-mode="lr" x="865.96" xml:space="preserve" y="1000.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132998459396" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="72">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="72" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,979.429,996) scale(1,1) translate(0,0)" writing-mode="lr" x="978.96" xml:space="preserve" y="1000.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132999901187" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="77">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="77" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1319.43,997) scale(1,1) translate(0,0)" writing-mode="lr" x="1318.96" xml:space="preserve" y="1001.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133002784771" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="78" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,953.628,12.6878) scale(1,1) translate(0,5.82938e-15)" writing-mode="lr" x="953.16" xml:space="preserve" y="17.35" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132985024516" ObjectName="P"/>
   </metadata>
  </g>
  <g id="79">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="79" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,953.628,35.6878) scale(1,1) translate(0,0)" writing-mode="lr" x="953.16" xml:space="preserve" y="40.35" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132985090052" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="80" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,953.628,58.6878) scale(1,1) translate(0,0)" writing-mode="lr" x="953.16" xml:space="preserve" y="63.35" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132985155588" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="94" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,703.577,353.444) scale(1,1) translate(0,0)" writing-mode="lr" x="703" xml:space="preserve" y="359.14" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132989218820" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="97" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,703.577,380.444) scale(1,1) translate(0,0)" writing-mode="lr" x="703" xml:space="preserve" y="386.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132989284356" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="98" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,703.577,407.444) scale(1,1) translate(0,0)" writing-mode="lr" x="703" xml:space="preserve" y="413.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132989480964" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="99" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,717.577,551.556) scale(1,1) translate(0,-1.19694e-13)" writing-mode="lr" x="717" xml:space="preserve" y="557.25" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132989349892" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="100" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,717.577,578.556) scale(1,1) translate(0,0)" writing-mode="lr" x="717" xml:space="preserve" y="584.25" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132989415428" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="107" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,717.577,605.556) scale(1,1) translate(0,0)" writing-mode="lr" x="717" xml:space="preserve" y="611.25" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132989808643" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="112">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="112" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1312.65,363.861) scale(1,1) translate(0,0)" writing-mode="lr" x="1312.07" xml:space="preserve" y="369.56" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132986597379" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="115" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1312.65,390.861) scale(1,1) translate(0,0)" writing-mode="lr" x="1312.07" xml:space="preserve" y="396.56" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132986662915" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="116" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1312.65,417.861) scale(1,1) translate(0,0)" writing-mode="lr" x="1312.07" xml:space="preserve" y="423.56" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132986859523" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="117" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1316.65,526.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1316.07" xml:space="preserve" y="531.8099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132986728451" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="118">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="118" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1316.65,553.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1316.07" xml:space="preserve" y="558.8099999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132986793987" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="119">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="119" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1316.65,580.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1316.07" xml:space="preserve" y="585.8099999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132987187204" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="260">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="260" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,465.75,974.173) scale(1,1) translate(2.80276e-13,0)" writing-mode="lr" x="465.28" xml:space="preserve" y="978.84" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132992299011" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="261">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="261" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,465.75,995.923) scale(1,1) translate(2.80276e-13,0)" writing-mode="lr" x="465.28" xml:space="preserve" y="1000.59" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132992364548" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="286">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="286" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,142.889,521) scale(1,1) translate(1.30482e-14,0)" writing-mode="lr" x="142.62" xml:space="preserve" y="525.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132983975939" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="284">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="284" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,142.889,545.5) scale(1,1) translate(1.30482e-14,0)" writing-mode="lr" x="142.62" xml:space="preserve" y="550.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132984041475" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="283">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="283" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,142.889,570) scale(1,1) translate(1.30482e-14,0)" writing-mode="lr" x="142.62" xml:space="preserve" y="574.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132984107011" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="282">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="282" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,142.889,496.5) scale(1,1) translate(1.30482e-14,0)" writing-mode="lr" x="142.62" xml:space="preserve" y="501.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132984238083" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="281">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="281" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,147.25,203.361) scale(1,1) translate(0,0)" writing-mode="lr" x="146.89" xml:space="preserve" y="208.04" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132984369155" ObjectName="F"/>
   </metadata>
  </g>
  <g id="280">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="280" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,262.292,520.625) scale(1,1) translate(2.63046e-14,0)" writing-mode="lr" x="262.03" xml:space="preserve" y="525.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132997804036" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="279">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="279" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,262.292,545.25) scale(1,1) translate(2.63046e-14,-1.18627e-13)" writing-mode="lr" x="262.03" xml:space="preserve" y="549.92" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132997869572" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="278">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="278" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,262.292,569.875) scale(1,1) translate(2.63046e-14,0)" writing-mode="lr" x="262.03" xml:space="preserve" y="574.55" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132997935108" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="277">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="277" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,262.292,496) scale(1,1) translate(2.63046e-14,-1.07692e-13)" writing-mode="lr" x="262.03" xml:space="preserve" y="500.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132998066180" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="276">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="276" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,147.25,228.25) scale(1,1) translate(0,0)" writing-mode="lr" x="146.89" xml:space="preserve" y="232.93" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132998197251" ObjectName="F"/>
   </metadata>
  </g>
  <g id="275">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="275" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,320.25,520.625) scale(1,1) translate(3.27392e-14,0)" writing-mode="lr" x="319.99" xml:space="preserve" y="525.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132997279747" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="274">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="274" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,320.25,545.25) scale(1,1) translate(3.27392e-14,0)" writing-mode="lr" x="319.99" xml:space="preserve" y="549.92" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132997345283" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="273">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="273" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,320.25,569.875) scale(1,1) translate(3.27392e-14,0)" writing-mode="lr" x="319.99" xml:space="preserve" y="574.55" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132997410819" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="272">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="272" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,324.25,227.5) scale(1,1) translate(0,0)" writing-mode="lr" x="323.89" xml:space="preserve" y="232.18" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132997672963" ObjectName="F"/>
   </metadata>
  </g>
  <g id="270">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="270" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,320.25,496) scale(1,1) translate(3.27392e-14,0)" writing-mode="lr" x="319.99" xml:space="preserve" y="500.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132997541891" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="269">
   <text Format="f5.1" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="269" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,147.25,250.444) scale(1,1) translate(0,0)" writing-mode="lr" x="146.89" xml:space="preserve" y="255.13" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132987056132" ObjectName="YW1"/>
   </metadata>
  </g>
  <g id="267">
   <text Format="f5.1" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="267" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,324.25,249.444) scale(1,1) translate(0,0)" writing-mode="lr" x="323.89" xml:space="preserve" y="254.13" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132989677571" ObjectName="YW1"/>
   </metadata>
  </g>
  <g id="266">
   <text Format="f3.0" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="266" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,324.25,272.444) scale(1,1) translate(0,-1.73824e-13)" writing-mode="lr" x="323.89" xml:space="preserve" y="277.13" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132989743107" ObjectName="Tap"/>
   </metadata>
  </g>
  <g id="265">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="265" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,142.889,594.5) scale(1,1) translate(1.30482e-14,-1.29563e-13)" writing-mode="lr" x="142.62" xml:space="preserve" y="599.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132984434691" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="264">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="264" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,262.292,594.5) scale(1,1) translate(2.63046e-14,-1.29563e-13)" writing-mode="lr" x="262.03" xml:space="preserve" y="599.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132998262787" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="263">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="263" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,320.25,594.5) scale(1,1) translate(3.27392e-14,-1.29563e-13)" writing-mode="lr" x="319.99" xml:space="preserve" y="599.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132997738499" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="262">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="262" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,147.25,178.75) scale(1,1) translate(0,0)" writing-mode="lr" x="146.89" xml:space="preserve" y="183.43" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133004095491" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="249">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="249" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,324.25,178.75) scale(1,1) translate(0,0)" writing-mode="lr" x="323.89" xml:space="preserve" y="183.43" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133004161027" ObjectName=""/>
   </metadata>
  </g>
  <g id="108">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="108" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1101.5,537.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1101.03" xml:space="preserve" y="542.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132993544195" ObjectName="P"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="111" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1101.5,556.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1101.03" xml:space="preserve" y="561.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132993609731" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="127">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="127" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1101.5,573.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1101.03" xml:space="preserve" y="578.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132993675267" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="45">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="45" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,762,950) scale(1,1) translate(0,0)" writing-mode="lr" x="761.53" xml:space="preserve" y="954.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132994396163" ObjectName="P"/>
   </metadata>
  </g>
  <g id="76">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="76" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,762,973) scale(1,1) translate(0,0)" writing-mode="lr" x="761.53" xml:space="preserve" y="977.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132994461699" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="83">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="83" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,762,996) scale(1,1) translate(0,0)" writing-mode="lr" x="761.53" xml:space="preserve" y="1000.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132994527236" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="161">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="161" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1479.43,950) scale(1,1) translate(0,0)" writing-mode="lr" x="1478.96" xml:space="preserve" y="954.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133001211907" ObjectName="P"/>
   </metadata>
  </g>
  <g id="165">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="165" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1479.43,973) scale(1,1) translate(0,0)" writing-mode="lr" x="1478.96" xml:space="preserve" y="977.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133001277443" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="169">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="169" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1479.43,996) scale(1,1) translate(0,0)" writing-mode="lr" x="1478.96" xml:space="preserve" y="1000.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133001342979" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1416.36,110.5) scale(1,1) translate(1.54432e-13,0)" writing-mode="lr" x="1416.1" xml:space="preserve" y="115.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132984238083" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="241" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1581.36,505.5) scale(1,1) translate(1.72751e-13,0)" writing-mode="lr" x="1581.1" xml:space="preserve" y="510.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132998066180" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="243">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="243" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,588.361,510.5) scale(1,1) translate(6.25056e-14,0)" writing-mode="lr" x="588.1" xml:space="preserve" y="515.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132997541891" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="370">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="370" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1610.75,974.173) scale(1,1) translate(0,0)" writing-mode="lr" x="1610.28" xml:space="preserve" y="978.84" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128551645187" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="369">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="369" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1610.75,995.923) scale(1,1) translate(0,0)" writing-mode="lr" x="1610.28" xml:space="preserve" y="1000.59" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128551710723" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f3.0" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,145.25,272.444) scale(1,1) translate(0,-1.73824e-13)" writing-mode="lr" x="144.89" xml:space="preserve" y="277.13" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132987121668" ObjectName="Tap"/>
   </metadata>
  </g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,353.875,320.091) scale(0.708333,0.665547) translate(141.338,155.837)" width="30" x="343.25" xlink:href="#State:红绿圆(方形)_0" y="310.11" zvalue="1202"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374922219521" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,353.875,320.091) scale(0.708333,0.665547) translate(141.338,155.837)" width="30" x="343.25" y="310.11"/></g>
  <g id="1035">
   <use height="30" transform="rotate(0,258.25,320.091) scale(0.708333,0.665547) translate(101.963,155.837)" width="30" x="247.63" xlink:href="#State:红绿圆(方形)_0" y="310.11" zvalue="1203"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562959731195908" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,258.25,320.091) scale(0.708333,0.665547) translate(101.963,155.837)" width="30" x="247.63" y="310.11"/></g>
  <g id="830">
   <use height="30" transform="rotate(0,322.812,134.964) scale(1.27778,1.03333) translate(-57.6766,-3.85367)" width="90" x="265.31" xlink:href="#State:全站检修_0" y="119.46" zvalue="1293"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549678964737" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,322.812,134.964) scale(1.27778,1.03333) translate(-57.6766,-3.85367)" width="90" x="265.31" y="119.46"/></g>
  <g id="772">
   <use height="30" transform="rotate(0,309.235,409.5) scale(0.910937,0.8) translate(26.6715,99.375)" width="80" x="272.8" xlink:href="#State:间隔模板_0" y="397.5" zvalue="1301"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629499717189635" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,309.235,409.5) scale(0.910937,0.8) translate(26.6715,99.375)" width="80" x="272.8" y="397.5"/></g>
  <g id="1082">
   <use height="30" transform="rotate(0,107.286,331.039) scale(0.910937,0.8) translate(6.92684,79.7597)" width="80" x="70.84999999999999" xlink:href="#State:间隔模板_0" y="319.04" zvalue="1302"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629500340961282" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,107.286,331.039) scale(0.910937,0.8) translate(6.92684,79.7597)" width="80" x="70.84999999999999" y="319.04"/></g>
 </g>
</svg>