<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549683945473" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:五卷PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="25.16666666666667" y2="1"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,14) scale(1,1) translate(0,0)" width="6" x="7" y="7"/>
   <path d="M 5.11667 33.0667 L 5.11667 37.0667 L 8.11667 35.0667 L 5.11667 33.0667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.66666666666667" x2="9.999999999999998" y1="4" y2="4"/>
   <ellipse cx="13.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <ellipse cx="13.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.44906826265991" x2="6.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659912" x2="8.849068262659907" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659907" x2="4.049068262659912" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="36.96252026861409" y2="34.48744029990989"/>
   <ellipse cx="6.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.11944444444444" x2="29.63333333333333" y1="28.26666666666667" y2="28.26666666666667"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,27.29,13.93) scale(1,1) translate(0,0)" width="7.58" x="23.5" y="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="3.93333333333333" y2="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="5.85" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="24.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="30.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.16111111111111" x2="30.27222222222222" y1="26.88508771929826" y2="26.88508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="21.85" y2="25.45"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.88333333333333" x2="31.55" y1="25.50350877192984" y2="25.50350877192984"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(130,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(130,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:四卷PT_0" viewBox="0,0,18,18">
   <use terminal-index="0" type="0" x="9" xlink:href="#terminal" y="0.2666666666666639"/>
   <ellipse cx="9.25" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.25" cy="9.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.08" cy="12.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.82" cy="9.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="11.75" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.25" x2="9.25" y1="7.500000000000001" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="15.25" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="12.31481481481481" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.94444444444444" x2="13.62962962962963" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14.94444444444444" y1="9.966124661246614" y2="9.966124661246614"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="ACLineSegment:线路2020_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="7.124999999999995" xlink:href="#terminal" y="39.42214187789629"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25" x2="15.25" y1="22" y2="9.333333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.75" x2="17.75" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25" x2="16.25" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.75" x2="16.75" y1="32.25" y2="32.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.91666666666666" x2="29.91666666666666" y1="19.91666666666667" y2="19.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="30.5" y1="28.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="15.75" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25" x2="15.25" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.160223367697554" x2="7.160223367697554" y1="39.5" y2="1.499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="27.33333333333334" x2="7.166666666666663" y1="9.258752830905545" y2="9.258752830905545"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25" x2="14.25" y1="22" y2="16"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.29,21.17) scale(1,1) translate(0,0)" width="6.08" x="12.25" y="14"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="27.25545055364641" x2="27.25545055364641" y1="9.291388644475965" y2="15.41666666666666"/>
   <ellipse cx="27.18" cy="19.99" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="27.51" cy="28.33" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.32589728904158" x2="22.32589728904158" y1="9.247588122517026" y2="9.247588122517026"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.00000000000001" x2="37.00000000000001" y1="24.66666666666667" y2="24.66666666666667"/>
   <ellipse cx="33.53" cy="24.44" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV大盈江二级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="286" x="53" xlink:href="logo.png" y="43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196,73) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="76.5" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,195.5,72.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="195.5" xml:space="preserve" y="81.69" zvalue="3">110kV大盈江二级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="220" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,82.625,185.25) scale(1,1) translate(0,0)" width="72.88" x="46.19" y="173.25" zvalue="330"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.625,185.25) scale(1,1) translate(0,0)" writing-mode="lr" x="82.63" xml:space="preserve" y="189.75" zvalue="330">信号一览</text>
  <line fill="none" id="36" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="387" x2="387" y1="11" y2="1041" zvalue="4"/>
  <line fill="none" id="34" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00000000000045" x2="380" y1="146.8704926140824" y2="146.8704926140824" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1648.22,342.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1648.22" xml:space="preserve" y="347.39" zvalue="57">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,798.333,244.111) scale(1,1) translate(0,0)" writing-mode="lr" x="798.33" xml:space="preserve" y="248.61" zvalue="60">151</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,789.556,291.667) scale(1,1) translate(0,0)" writing-mode="lr" x="789.5599999999999" xml:space="preserve" y="296.17" zvalue="61">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,791.222,183.667) scale(1,1) translate(0,0)" writing-mode="lr" x="791.22" xml:space="preserve" y="188.17" zvalue="64">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,777.932,94.4444) scale(1,1) translate(0,0)" writing-mode="lr" x="777.9299999999999" xml:space="preserve" y="98.94" zvalue="69">110kV革下线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,837.692,300.212) scale(1,1) translate(0,0)" writing-mode="lr" x="837.6900000000001" xml:space="preserve" y="304.71" zvalue="71">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,858.783,237.606) scale(1,1) translate(0,0)" writing-mode="lr" x="858.78" xml:space="preserve" y="242.11" zvalue="73">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,835.51,173.162) scale(1,1) translate(0,0)" writing-mode="lr" x="835.51" xml:space="preserve" y="177.66" zvalue="75">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1562.78,364.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1562.78" xml:space="preserve" y="368.72" zvalue="87">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1572.11,404.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1572.11" xml:space="preserve" y="408.72" zvalue="89">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1479.56,548.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1479.56" xml:space="preserve" y="553.17" zvalue="93">1020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1502.44,393.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1502.44" xml:space="preserve" y="397.72" zvalue="98">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1623.09,531.139) scale(1,1) translate(-1.4094e-12,0)" writing-mode="lr" x="1623.092044611865" xml:space="preserve" y="535.6388888888889" zvalue="109">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1420.2,244.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1420.2" xml:space="preserve" y="248.61" zvalue="113">152</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1415.15,291.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1415.15" xml:space="preserve" y="296.17" zvalue="114">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1416.52,183.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1416.52" xml:space="preserve" y="188.17" zvalue="115">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1404.09,86.9167) scale(1,1) translate(0,0)" writing-mode="lr" x="1404.09" xml:space="preserve" y="91.42" zvalue="120">备用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1468.98,284.495) scale(1,1) translate(0,0)" writing-mode="lr" x="1468.98" xml:space="preserve" y="288.99" zvalue="122">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1457.34,180.586) scale(1,1) translate(0,0)" writing-mode="lr" x="1457.34" xml:space="preserve" y="185.09" zvalue="123">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1457.34,119.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1457.34" xml:space="preserve" y="124.28" zvalue="124">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,899.778,365.222) scale(1,1) translate(0,0)" writing-mode="lr" x="899.78" xml:space="preserve" y="369.72" zvalue="128">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,909.111,429.222) scale(1,1) translate(0,0)" writing-mode="lr" x="909.11" xml:space="preserve" y="433.72" zvalue="130">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,816.556,549.667) scale(1,1) translate(0,0)" writing-mode="lr" x="816.5599999999999" xml:space="preserve" y="554.17" zvalue="134">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,947.626,408.884) scale(1,1) translate(0,0)" writing-mode="lr" x="947.63" xml:space="preserve" y="413.38" zvalue="139">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,969.852,533.25) scale(1,1) translate(0,0)" writing-mode="lr" x="969.8517248634122" xml:space="preserve" y="537.75" zvalue="144">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1207.4,568.068) scale(1,1) translate(0,0)" writing-mode="lr" x="1207.401303871124" xml:space="preserve" y="572.5681818181819" zvalue="146">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1208.93,431.889) scale(1,1) translate(1.19447e-12,0)" writing-mode="lr" x="1208.93" xml:space="preserve" y="436.39" zvalue="166">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1145.26,387.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1145.26" xml:space="preserve" y="391.72" zvalue="170">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="175" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,918.5,690) scale(1,1) translate(0,0)" writing-mode="lr" x="918.5" xml:space="preserve" y="694.5" zvalue="183">011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1576,690) scale(1,1) translate(0,0)" writing-mode="lr" x="1576" xml:space="preserve" y="694.5" zvalue="189">021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1149.11,487.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1149.11" xml:space="preserve" y="491.61" zvalue="191">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,891.885,916.5) scale(1,1) translate(0,0)" writing-mode="lr" x="891.88" xml:space="preserve" y="921" zvalue="194">#1发电机  35MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1554,929.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1554" xml:space="preserve" y="934" zvalue="196">#2发电机   35MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1023.26,792.67) scale(1,1) translate(0,0)" writing-mode="lr" x="1023.26" xml:space="preserve" y="797.17" zvalue="199">1号励磁变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1680.26,797.67) scale(1,1) translate(0,0)" writing-mode="lr" x="1680.26" xml:space="preserve" y="802.17" zvalue="202">2号励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1052,648.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1052" xml:space="preserve" y="652.72" zvalue="207">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1102.72,698.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1102.72" xml:space="preserve" y="702.61" zvalue="214">091</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1126.26,766.67) scale(1,1) translate(0,0)" writing-mode="lr" x="1126.26" xml:space="preserve" y="771.17" zvalue="217">1号厂用电</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="151" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1734,636.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1734" xml:space="preserve" y="640.72" zvalue="220">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1778.72,685.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1778.72" xml:space="preserve" y="689.61" zvalue="223">092</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1758.26,788.67) scale(1,1) translate(0,0)" writing-mode="lr" x="1758.26" xml:space="preserve" y="793.17" zvalue="225">2号厂用电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1831.26,789.67) scale(1,1) translate(0,0)" writing-mode="lr" x="1831.26" xml:space="preserve" y="794.17" zvalue="230">3号厂用电</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,933.5,786) scale(1,1) translate(0,0)" writing-mode="lr" x="933.5" xml:space="preserve" y="790.5" zvalue="234">0914</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,764.5,795) scale(1,1) translate(0,0)" writing-mode="lr" x="764.5" xml:space="preserve" y="799.5" zvalue="238">0913</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1603.5,798) scale(1,1) translate(0,0)" writing-mode="lr" x="1603.5" xml:space="preserve" y="802.5" zvalue="244">0924</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="166" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1450.5,793) scale(1,1) translate(0,0)" writing-mode="lr" x="1450.5" xml:space="preserve" y="797.5" zvalue="246">0923</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1107.5,588) scale(1,1) translate(0,0)" writing-mode="lr" x="1107.5" xml:space="preserve" y="592.5" zvalue="251">0912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1779.5,591) scale(1,1) translate(0,0)" writing-mode="lr" x="1779.5" xml:space="preserve" y="595.5" zvalue="254">0922</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="247" y2="247"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="273" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="247" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="247" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="247" y2="247"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="273" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="247" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="247" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="273" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="297.25" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="273" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="273" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="273" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="297.25" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="273" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="273" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="297.25" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="320" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="297.25" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="297.25" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="297.25" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="320" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="297.25" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="297.25" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="320" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="342.75" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="320" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="320" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="320" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="342.75" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="320" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="320" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="342.75" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="365.5" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="342.75" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="342.75" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="342.75" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="365.5" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="342.75" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="342.75" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="365.5" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="388.25" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="365.5" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="365.5" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="365.5" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="388.25" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="365.5" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="365.5" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="388.25" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="411" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="388.25" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="388.25" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="388.25" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="411" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="388.25" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="388.25" y2="411"/>
  <line fill="none" id="255" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="492.8704926140824" y2="492.8704926140824" zvalue="304"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="999.0816" y2="1027"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,952) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="958" zvalue="306">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50,986) scale(1,1) translate(0,0)" writing-mode="lr" x="50" xml:space="preserve" y="992" zvalue="307">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232,986) scale(1,1) translate(0,0)" writing-mode="lr" x="232" xml:space="preserve" y="992" zvalue="308">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="250" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="49" xml:space="preserve" y="1020" zvalue="309">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="249" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="231" xml:space="preserve" y="1020" zvalue="310">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="242" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.5,566.5) scale(1,1) translate(0,0)" writing-mode="lr" x="73.5" xml:space="preserve" y="571" zvalue="312">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="241" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,198.399,185.841) scale(1,1) translate(0,0)" writing-mode="lr" x="198.4" xml:space="preserve" y="190.34" zvalue="313">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="239" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,303.399,185.841) scale(1,1) translate(0,0)" writing-mode="lr" x="303.4" xml:space="preserve" y="190.34" zvalue="314">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,55.5,260) scale(1,1) translate(0,0)" writing-mode="lr" x="13" xml:space="preserve" y="264.5" zvalue="318">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,236,260) scale(1,1) translate(0,0)" writing-mode="lr" x="193.5" xml:space="preserve" y="264.5" zvalue="319">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="227" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.6875,333.25) scale(1,1) translate(0,0)" writing-mode="lr" x="58.69" xml:space="preserve" y="337.75" zvalue="320">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="219" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.5,286) scale(1,1) translate(0,0)" writing-mode="lr" x="12" xml:space="preserve" y="290.5" zvalue="331">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="218" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235,286) scale(1,1) translate(0,0)" writing-mode="lr" x="192.5" xml:space="preserve" y="290.5" zvalue="332">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="217" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,379.25) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="383.75" zvalue="335">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="215" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,223.688,378.25) scale(1,1) translate(0,0)" writing-mode="lr" x="223.69" xml:space="preserve" y="382.75" zvalue="337">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="213" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,402.25) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="406.75" zvalue="339">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="212" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,223.688,401.25) scale(1,1) translate(0,0)" writing-mode="lr" x="223.69" xml:space="preserve" y="405.75" zvalue="341">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.5,309) scale(1,1) translate(0,0)" writing-mode="lr" x="12" xml:space="preserve" y="313.5" zvalue="343">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,234.5,308) scale(1,1) translate(0,0)" writing-mode="lr" x="192" xml:space="preserve" y="312.5" zvalue="345">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,195,953.5) scale(1,1) translate(0,0)" writing-mode="lr" x="195" xml:space="preserve" y="959.5" zvalue="348">DaYingJiang2-01-2011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,970,556) scale(1,1) translate(0,0)" writing-mode="lr" x="970" xml:space="preserve" y="560.5" zvalue="357">45MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1624,554) scale(1,1) translate(0,0)" writing-mode="lr" x="1624" xml:space="preserve" y="558.5" zvalue="358">45MVA</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="46.19" y="173.25" zvalue="330"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="117">
   <path class="kv110" d="M 604 325.67 L 1667 325.67" stroke-width="4" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674420326403" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674420326403"/></metadata>
  <path d="M 604 325.67 L 1667 325.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="116">
   <use class="kv110" height="20" transform="rotate(0,777,245.111) scale(1.22222,1.11111) translate(-140.162,-23.4)" width="10" x="770.8888888888889" xlink:href="#Breaker:开关_0" y="234" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925212438531" ObjectName="110kV革下线151断路器"/>
   <cge:TPSR_Ref TObjectID="6473925212438531"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,777,245.111) scale(1.22222,1.11111) translate(-140.162,-23.4)" width="10" x="770.8888888888889" y="234"/></g>
  <g id="101">
   <use class="kv110" height="20" transform="rotate(0,1551.33,405.222) scale(1.22222,1.11111) translate(-280.949,-39.4111)" width="10" x="1545.222232407994" xlink:href="#Breaker:开关_0" y="394.1111111111111" zvalue="88"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925212372995" ObjectName="#2主变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473925212372995"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1551.33,405.222) scale(1.22222,1.11111) translate(-280.949,-39.4111)" width="10" x="1545.222232407994" y="394.1111111111111"/></g>
  <g id="269">
   <use class="kv110" height="20" transform="rotate(0,1403,245.111) scale(1.22222,1.11111) translate(-253.98,-23.4)" width="10" x="1396.888888888889" xlink:href="#Breaker:开关_0" y="234" zvalue="112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925212307459" ObjectName="110kV备用线152断路器"/>
   <cge:TPSR_Ref TObjectID="6473925212307459"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1403,245.111) scale(1.22222,1.11111) translate(-253.98,-23.4)" width="10" x="1396.888888888889" y="234"/></g>
  <g id="185">
   <use class="kv110" height="20" transform="rotate(0,888.333,430.222) scale(1.22222,1.11111) translate(-160.404,-41.9111)" width="10" x="882.2222324079938" xlink:href="#Breaker:开关_0" y="419.1111111111111" zvalue="129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925212241923" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925212241923"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,888.333,430.222) scale(1.22222,1.11111) translate(-160.404,-41.9111)" width="10" x="882.2222324079938" y="419.1111111111111"/></g>
  <g id="67">
   <use class="v10500" height="20" transform="rotate(0,889,691) scale(2.2,2.2) translate(-478.909,-364.909)" width="10" x="878" xlink:href="#Breaker:小车断路器_0" y="669" zvalue="182"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925212504067" ObjectName="#1主变10.5kV侧011断路器"/>
   <cge:TPSR_Ref TObjectID="6473925212504067"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,889,691) scale(2.2,2.2) translate(-478.909,-364.909)" width="10" x="878" y="669"/></g>
  <g id="74">
   <use class="v10500" height="20" transform="rotate(0,1553,691) scale(2.2,2.2) translate(-841.091,-364.909)" width="10" x="1542" xlink:href="#Breaker:小车断路器_0" y="669" zvalue="188"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925212569603" ObjectName="#2主变10.5kV侧021断路器"/>
   <cge:TPSR_Ref TObjectID="6473925212569603"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1553,691) scale(2.2,2.2) translate(-841.091,-364.909)" width="10" x="1542" y="669"/></g>
  <g id="139">
   <use class="v10500" height="20" transform="rotate(0,1077.11,698.111) scale(1.22222,1.11111) translate(-194.727,-68.7)" width="10" x="1071" xlink:href="#Breaker:开关_0" y="687" zvalue="213"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925212635139" ObjectName="#1发电机091断路器"/>
   <cge:TPSR_Ref TObjectID="6473925212635139"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1077.11,698.111) scale(1.22222,1.11111) translate(-194.727,-68.7)" width="10" x="1071" y="687"/></g>
  <g id="147">
   <use class="v10500" height="20" transform="rotate(0,1760.11,686.111) scale(1.22222,1.11111) translate(-318.909,-67.5)" width="10" x="1754" xlink:href="#Breaker:开关_0" y="675" zvalue="221"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925212700675" ObjectName="#2发电机092断路器"/>
   <cge:TPSR_Ref TObjectID="6473925212700675"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1760.11,686.111) scale(1.22222,1.11111) translate(-318.909,-67.5)" width="10" x="1754" y="675"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="115">
   <use class="kv110" height="30" transform="rotate(0,777,292.667) scale(-1.11111,-0.814815) translate(-1475.47,-654.626)" width="15" x="768.6666666666666" xlink:href="#Disconnector:刀闸_0" y="280.4444580078125" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454757974018" ObjectName="110kV革下线1511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454757974018"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,777,292.667) scale(-1.11111,-0.814815) translate(-1475.47,-654.626)" width="15" x="768.6666666666666" y="280.4444580078125"/></g>
  <g id="113">
   <use class="kv110" height="30" transform="rotate(0,777,184.667) scale(-1.11111,-0.814815) translate(-1475.47,-414.081)" width="15" x="768.6666666931577" xlink:href="#Disconnector:刀闸_0" y="172.4444444444445" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454757908482" ObjectName="110kV革下线1516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454757908482"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,777,184.667) scale(-1.11111,-0.814815) translate(-1475.47,-414.081)" width="15" x="768.6666666931577" y="172.4444444444445"/></g>
  <g id="99">
   <use class="kv110" height="30" transform="rotate(0,1551.33,365.222) scale(1.11111,0.814815) translate(-154.3,80.2273)" width="15" x="1543.000010172526" xlink:href="#Disconnector:刀闸_0" y="353" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454757384194" ObjectName="#2主变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454757384194"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1551.33,365.222) scale(1.11111,0.814815) translate(-154.3,80.2273)" width="15" x="1543.000010172526" y="353"/></g>
  <g id="268">
   <use class="kv110" height="30" transform="rotate(0,1403,292.667) scale(-1.11111,-0.814815) translate(-2664.87,-654.626)" width="15" x="1394.666666666667" xlink:href="#Disconnector:刀闸_0" y="280.4444580078125" zvalue="113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454756990978" ObjectName="110kV备用线1521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454756990978"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1403,292.667) scale(-1.11111,-0.814815) translate(-2664.87,-654.626)" width="15" x="1394.666666666667" y="280.4444580078125"/></g>
  <g id="267">
   <use class="kv110" height="30" transform="rotate(0,1403,184.667) scale(-1.11111,-0.814815) translate(-2664.87,-414.081)" width="15" x="1394.666666693158" xlink:href="#Disconnector:刀闸_0" y="172.4444444444445" zvalue="114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454756925442" ObjectName="110kV备用线1526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454756925442"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1403,184.667) scale(-1.11111,-0.814815) translate(-2664.87,-414.081)" width="15" x="1394.666666693158" y="172.4444444444445"/></g>
  <g id="186">
   <use class="kv110" height="30" transform="rotate(0,888.333,366.222) scale(1.11111,0.814815) translate(-88,80.4545)" width="15" x="880.0000101725263" xlink:href="#Disconnector:刀闸_0" y="354" zvalue="127"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454756401154" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454756401154"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,888.333,366.222) scale(1.11111,0.814815) translate(-88,80.4545)" width="15" x="880.0000101725263" y="354"/></g>
  <g id="140">
   <use class="kv110" height="30" transform="rotate(0,1195.15,432.889) scale(1.11111,0.814815) translate(-118.682,95.6061)" width="15" x="1186.818191884744" xlink:href="#Disconnector:刀闸_0" y="420.6666666666667" zvalue="165"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454758170626" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454758170626"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1195.15,432.889) scale(1.11111,0.814815) translate(-118.682,95.6061)" width="15" x="1186.818191884744" y="420.6666666666667"/></g>
  <g id="98">
   <use class="v10500" height="30" transform="rotate(0,1076.33,649.222) scale(1.11111,0.814815) translate(-106.8,144.773)" width="15" x="1068" xlink:href="#Disconnector:刀闸_0" y="637" zvalue="206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454758629378" ObjectName="#1发电机0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454758629378"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1076.33,649.222) scale(1.11111,0.814815) translate(-106.8,144.773)" width="15" x="1068" y="637"/></g>
  <g id="149">
   <use class="v10500" height="30" transform="rotate(0,1758.33,637.222) scale(1.11111,0.814815) translate(-175,142.045)" width="15" x="1750" xlink:href="#Disconnector:刀闸_0" y="625" zvalue="219"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454758825986" ObjectName="#2发电机0921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454758825986"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1758.33,637.222) scale(1.11111,0.814815) translate(-175,142.045)" width="15" x="1750" y="625"/></g>
  <g id="152">
   <use class="v10500" height="26" transform="rotate(0,956.5,787) scale(1,1) translate(0,0)" width="12" x="950.5" xlink:href="#Disconnector:单手车刀闸1212_0" y="774" zvalue="233"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454758957058" ObjectName="#1发电机0914隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454758957058"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,956.5,787) scale(1,1) translate(0,0)" width="12" x="950.5" y="774"/></g>
  <g id="159">
   <use class="v10500" height="26" transform="rotate(0,787,796) scale(1,1) translate(0,0)" width="12" x="781" xlink:href="#Disconnector:单手车刀闸1212_0" y="783" zvalue="237"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454759022594" ObjectName="#1发电机0913隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454759022594"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,787,796) scale(1,1) translate(0,0)" width="12" x="781" y="783"/></g>
  <g id="164">
   <use class="v10500" height="26" transform="rotate(0,1626.5,799) scale(1,1) translate(0,0)" width="12" x="1620.5" xlink:href="#Disconnector:单手车刀闸1212_0" y="786" zvalue="243"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454759088130" ObjectName="#2发电机0924隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454759088130"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1626.5,799) scale(1,1) translate(0,0)" width="12" x="1620.5" y="786"/></g>
  <g id="165">
   <use class="v10500" height="26" transform="rotate(0,1473,794) scale(1,1) translate(0,0)" width="12" x="1467" xlink:href="#Disconnector:单手车刀闸1212_0" y="781" zvalue="245"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454759153666" ObjectName="#2发电机0923隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454759153666"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1473,794) scale(1,1) translate(0,0)" width="12" x="1467" y="781"/></g>
  <g id="171">
   <use class="v10500" height="26" transform="rotate(0,1077,590) scale(1,-1) translate(0,-1180)" width="12" x="1071" xlink:href="#Disconnector:单手车刀闸1212_0" y="577" zvalue="250"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454759219202" ObjectName="#1发电机0912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454759219202"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1077,590) scale(1,-1) translate(0,-1180)" width="12" x="1071" y="577"/></g>
  <g id="156">
   <use class="v10500" height="26" transform="rotate(0,1757,592) scale(1,-1) translate(0,-1184)" width="12" x="1751" xlink:href="#Disconnector:单手车刀闸1212_0" y="579" zvalue="253"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454759284738" ObjectName="#1发电机0922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454759284738"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1757,592) scale(1,-1) translate(0,-1184)" width="12" x="1751" y="579"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="111">
   <path class="kv110" d="M 776.9 304.48 L 776.9 325.67" stroke-width="1" zvalue="63"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="117@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 776.9 304.48 L 776.9 325.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv110" d="M 776.93 280.65 L 776.93 255.72" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@1" LinkObjectIDznd="116@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 776.93 280.65 L 776.93 255.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv110" d="M 776.96 234.48 L 776.9 196.48" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="113@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 776.96 234.48 L 776.9 196.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="kv110" d="M 776.93 172.65 L 776.93 152.8" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@1" LinkObjectIDznd="108@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 776.93 172.65 L 776.93 152.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv110" d="M 1551.4 377.24 L 1551.4 394.59" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@1" LinkObjectIDznd="101@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1551.4 377.24 L 1551.4 394.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv110" d="M 1551.37 508.62 L 1420.89 508.62 L 1420.89 536.11" stroke-width="1" zvalue="96"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@2" LinkObjectIDznd="105@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1551.37 508.62 L 1420.89 508.62 L 1420.89 536.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv110" d="M 1513.28 381.06 L 1551.4 381.06" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="91" MaxPinNum="2"/>
   </metadata>
  <path d="M 1513.28 381.06 L 1551.4 381.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv110" d="M 1551.43 353.4 L 1551.43 325.67" stroke-width="1" zvalue="101"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="117@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1551.43 353.4 L 1551.43 325.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv110" d="M 1402.9 304.48 L 1402.9 325.67" stroke-width="1" zvalue="115"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="117@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1402.9 304.48 L 1402.9 325.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv110" d="M 1402.93 280.65 L 1402.93 255.72" stroke-width="1" zvalue="116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@1" LinkObjectIDznd="269@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1402.93 280.65 L 1402.93 255.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv110" d="M 1402.96 234.48 L 1402.9 196.48" stroke-width="1" zvalue="117"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="269@0" LinkObjectIDznd="267@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1402.96 234.48 L 1402.9 196.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv110" d="M 1402.93 172.65 L 1402.93 142.44" stroke-width="1" zvalue="119"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@1" LinkObjectIDznd="263@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1402.93 172.65 L 1402.93 142.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv110" d="M 888.4 378.24 L 888.4 419.59" stroke-width="1" zvalue="135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@1" LinkObjectIDznd="185@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 888.4 378.24 L 888.4 419.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv110" d="M 888.38 510.73 L 757.89 510.73 L 757.89 537.11" stroke-width="1" zvalue="137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@2" LinkObjectIDznd="181@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 888.38 510.73 L 757.89 510.73 L 757.89 537.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv110" d="M 888.43 354.4 L 888.43 325.67" stroke-width="1" zvalue="142"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="117@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 888.43 354.4 L 888.43 325.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv110" d="M 823.23 158.55 L 776.93 158.55" stroke-width="1" zvalue="148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="107" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.23 158.55 L 776.93 158.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv110" d="M 776.93 266.18 L 836.19 266.18 L 836.19 274.66" stroke-width="1" zvalue="151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110" LinkObjectIDznd="106@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 776.93 266.18 L 836.19 266.18 L 836.19 274.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv110" d="M 822.49 214.81 L 776.93 214.81" stroke-width="1" zvalue="153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="109" MaxPinNum="2"/>
   </metadata>
  <path d="M 822.49 214.81 L 776.93 214.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv110" d="M 1446.51 153.1 L 1402.93 153.1" stroke-width="1" zvalue="155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@0" LinkObjectIDznd="75" MaxPinNum="2"/>
   </metadata>
  <path d="M 1446.51 153.1 L 1402.93 153.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv110" d="M 1446.51 213.9 L 1402.93 213.9" stroke-width="1" zvalue="156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="260@0" LinkObjectIDznd="76" MaxPinNum="2"/>
   </metadata>
  <path d="M 1446.51 213.9 L 1402.93 213.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv110" d="M 1402.93 268 L 1455.46 268 L 1455.46 274.66" stroke-width="1" zvalue="157"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77" LinkObjectIDznd="261@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1402.93 268 L 1455.46 268 L 1455.46 274.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="kv110" d="M 936.43 395.76 L 888.4 395.76" stroke-width="1" zvalue="158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@0" LinkObjectIDznd="71" MaxPinNum="2"/>
   </metadata>
  <path d="M 936.43 395.76 L 888.4 395.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv110" d="M 888.41 440.83 L 888.41 482.89" stroke-width="1" zvalue="159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@1" LinkObjectIDznd="163@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 888.41 440.83 L 888.41 482.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv110" d="M 1156.1 375.06 L 1195.25 375.06" stroke-width="1" zvalue="171"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="145" MaxPinNum="2"/>
   </metadata>
  <path d="M 1156.1 375.06 L 1195.25 375.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv110" d="M 1195.25 325.67 L 1195.25 421.07" stroke-width="1" zvalue="174"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@4" LinkObjectIDznd="140@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1195.25 325.67 L 1195.25 421.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv110" d="M 1195.22 444.9 L 1195.22 492.78" stroke-width="1" zvalue="175"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@1" LinkObjectIDznd="112@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1195.22 444.9 L 1195.22 492.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="v10500" d="M 888.35 579.47 L 888.35 670.65" stroke-width="1" zvalue="176"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@1" LinkObjectIDznd="67@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 888.35 579.47 L 888.35 670.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="v10500" d="M 1551.34 577.36 L 1551.34 670.65" stroke-width="1" zvalue="178"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@1" LinkObjectIDznd="74@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1551.34 577.36 L 1551.34 670.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="v10500" d="M 889 710.8 L 889 742 L 787 742 L 787 783.08" stroke-width="1" zvalue="183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@1" LinkObjectIDznd="159@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 889 710.8 L 889 742 L 787 742 L 787 783.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="v10500" d="M 889 742 L 889 843.46" stroke-width="1" zvalue="184"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68" LinkObjectIDznd="850@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 889 742 L 889 843.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv110" d="M 1161.94 465.61 L 1195.22 465.61" stroke-width="1" zvalue="191"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="146" MaxPinNum="2"/>
   </metadata>
  <path d="M 1161.94 465.61 L 1195.22 465.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="v10500" d="M 1553 710.8 L 1553 852.46" stroke-width="1" zvalue="196"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@1" LinkObjectIDznd="65@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1553 710.8 L 1553 852.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="v10500" d="M 889 742.34 L 1023.26 742.34" stroke-width="1" zvalue="199"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70" LinkObjectIDznd="83@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 889 742.34 L 1023.26 742.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="v10500" d="M 1553 747.34 L 1680.26 747.34" stroke-width="1" zvalue="202"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72" LinkObjectIDznd="85@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1553 747.34 L 1680.26 747.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="v10500" d="M 888.35 607 L 1076.43 607 L 1076.43 637.4" stroke-width="1" zvalue="208"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43" LinkObjectIDznd="98@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 888.35 607 L 1076.43 607 L 1076.43 637.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="v10500" d="M 1076.4 661.24 L 1076.4 687.48" stroke-width="1" zvalue="209"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@1" LinkObjectIDznd="139@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1076.4 661.24 L 1076.4 687.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="v10500" d="M 1077.19 708.72 L 1077.19 739.34" stroke-width="1" zvalue="214"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@1" LinkObjectIDznd="143@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1077.19 708.72 L 1077.19 739.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="v10500" d="M 1758.4 649.24 L 1758.4 675.48" stroke-width="1" zvalue="220"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="149@1" LinkObjectIDznd="147@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1758.4 649.24 L 1758.4 675.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="v10500" d="M 1760.19 696.72 L 1760.26 733.34" stroke-width="1" zvalue="222"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="147@1" LinkObjectIDznd="122@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1760.19 696.72 L 1760.26 733.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="v10500" d="M 1551.34 608 L 1758.43 608 L 1758.43 625.4" stroke-width="1" zvalue="226"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49" LinkObjectIDznd="149@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1551.34 608 L 1758.43 608 L 1758.43 625.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="v10500" d="M 1760.23 716 L 1831.26 716 L 1831.26 733.34" stroke-width="1" zvalue="230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138" LinkObjectIDznd="153@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1760.23 716 L 1831.26 716 L 1831.26 733.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="v10500" d="M 956.58 799.97 L 956.58 812.27" stroke-width="1" zvalue="234"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="178@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 956.58 799.97 L 956.58 812.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="v10500" d="M 956.5 742.34 L 956.5 774.08" stroke-width="1" zvalue="235"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84" LinkObjectIDznd="152@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 956.5 742.34 L 956.5 774.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="v10500" d="M 787.07 846.64 L 787.08 808.97" stroke-width="1" zvalue="240"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@0" LinkObjectIDznd="159@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 787.07 846.64 L 787.08 808.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="v10500" d="M 1553 747.34 L 1473 747.34 L 1473 781.08" stroke-width="1" zvalue="247"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92" LinkObjectIDznd="165@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1553 747.34 L 1473 747.34 L 1473 781.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="v10500" d="M 1625 747.34 L 1625 786.08" stroke-width="1" zvalue="248"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92" LinkObjectIDznd="164@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1625 747.34 L 1625 786.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="v10500" d="M 1077 602.92 L 1076.43 607" stroke-width="1" zvalue="251"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@1" LinkObjectIDznd="134" MaxPinNum="2"/>
   </metadata>
  <path d="M 1077 602.92 L 1076.43 607" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="v10500" d="M 1757 604.92 L 1757 608" stroke-width="1" zvalue="254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="156@1" LinkObjectIDznd="150" MaxPinNum="2"/>
   </metadata>
  <path d="M 1757 604.92 L 1757 608" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="184">
   <path class="v10500" d="M 1629 815.27 L 1626.58 811.97" stroke-width="1" zvalue="261"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@0" LinkObjectIDznd="164@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1629 815.27 L 1626.58 811.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="v10500" d="M 1473.08 806.97 L 1473.08 848.64" stroke-width="1" zvalue="264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="187@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1473.08 806.97 L 1473.08 848.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="v10500" d="M 1077.08 577.03 L 1077.08 551.85" stroke-width="1" zvalue="267"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="189@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1077.08 577.03 L 1077.08 551.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="v10500" d="M 1757.08 579.03 L 1757.08 555.07" stroke-width="1" zvalue="270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="156@0" LinkObjectIDznd="191@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1757.08 579.03 L 1757.08 555.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv110" d="M 1551.41 415.83 L 1551.41 480.78" stroke-width="1" zvalue="356"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@1" LinkObjectIDznd="81@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1551.41 415.83 L 1551.41 480.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="108">
   <use class="kv110" height="40" transform="rotate(0,789.932,131.222) scale(1.11111,1.11111) translate(-76.771,-10.9)" width="40" x="767.7098173330401" xlink:href="#ACLineSegment:线路2020_0" y="108.9999999999999" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249306824710" ObjectName="110kV革下线"/>
   <cge:TPSR_Ref TObjectID="8444249306824710_5066549683945473"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,789.932,131.222) scale(1.11111,1.11111) translate(-76.771,-10.9)" width="40" x="767.7098173330401" y="108.9999999999999"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="106">
   <use class="kv110" height="20" transform="rotate(0,836.247,285.495) scale(-1.07879,1.11111) translate(-1611.03,-27.4384)" width="10" x="830.8535347659177" xlink:href="#GroundDisconnector:地刀_0" y="274.3838381141123" zvalue="70"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454757777410" ObjectName="110kV革下线15117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454757777410"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,836.247,285.495) scale(-1.07879,1.11111) translate(-1611.03,-27.4384)" width="10" x="830.8535347659177" y="274.3838381141123"/></g>
  <g id="104">
   <use class="kv110" height="20" transform="rotate(90,833.328,214.869) scale(-1.11111,-1.11111) translate(-1582.77,-407.139)" width="10" x="827.7727277098279" xlink:href="#GroundDisconnector:地刀_0" y="203.757575757576" zvalue="72"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454757646338" ObjectName="110kV革下线15160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454757646338"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,833.328,214.869) scale(-1.11111,-1.11111) translate(-1582.77,-407.139)" width="10" x="827.7727277098279" y="203.757575757576"/></g>
  <g id="102">
   <use class="kv110" height="20" transform="rotate(90,834.066,158.606) scale(-1.11111,-1.11111) translate(-1584.17,-300.24)" width="10" x="828.5101011064318" xlink:href="#GroundDisconnector:地刀_0" y="147.4949494949495" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454757515266" ObjectName="110kV革下线15167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454757515266"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,834.066,158.606) scale(-1.11111,-1.11111) translate(-1584.17,-300.24)" width="10" x="828.5101011064318" y="147.4949494949495"/></g>
  <g id="105">
   <use class="kv110" height="40" transform="rotate(0,1435.78,549.667) scale(1.11111,-1.11111) translate(-141.356,-1042.14)" width="40" x="1413.555555555556" xlink:href="#GroundDisconnector:中性点地刀12_0" y="527.4444444444445" zvalue="92"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454757253122" ObjectName="#2主变110kV侧1020中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454757253122"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1435.78,549.667) scale(1.11111,-1.11111) translate(-141.356,-1042.14)" width="40" x="1413.555555555556" y="527.4444444444445"/></g>
  <g id="114">
   <use class="kv110" height="20" transform="rotate(90,1502.44,381) scale(1.11111,1.11111) translate(-149.689,-36.9889)" width="10" x="1496.888899061415" xlink:href="#GroundDisconnector:地刀_0" y="369.8888888888889" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454757122050" ObjectName="#2主变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454757122050"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1502.44,381) scale(1.11111,1.11111) translate(-149.689,-36.9889)" width="10" x="1496.888899061415" y="369.8888888888889"/></g>
  <g id="261">
   <use class="kv110" height="20" transform="rotate(0,1455.52,285.495) scale(-1.11111,1.11111) translate(-2764.93,-27.4384)" width="10" x="1449.96464657061" xlink:href="#GroundDisconnector:地刀_0" y="274.3838382297092" zvalue="121"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454756794370" ObjectName="110kV备用线15217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454756794370"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1455.52,285.495) scale(-1.11111,1.11111) translate(-2764.93,-27.4384)" width="10" x="1449.96464657061" y="274.3838382297092"/></g>
  <g id="260">
   <use class="kv110" height="20" transform="rotate(270,1457.34,213.96) scale(1.11111,1.11111) translate(-145.178,-20.2848)" width="10" x="1451.782823316978" xlink:href="#GroundDisconnector:地刀_0" y="202.8484848484849" zvalue="122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454756663298" ObjectName="110kV备用线15260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454756663298"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1457.34,213.96) scale(1.11111,1.11111) translate(-145.178,-20.2848)" width="10" x="1451.782823316978" y="202.8484848484849"/></g>
  <g id="259">
   <use class="kv110" height="20" transform="rotate(270,1457.34,153.152) scale(1.11111,1.11111) translate(-145.178,-14.204)" width="10" x="1451.782823350694" xlink:href="#GroundDisconnector:地刀_0" y="142.0404040404039" zvalue="123"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454756532226" ObjectName="110kV备用线15267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454756532226"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1457.34,153.152) scale(1.11111,1.11111) translate(-145.178,-14.204)" width="10" x="1451.782823350694" y="142.0404040404039"/></g>
  <g id="181">
   <use class="kv110" height="40" transform="rotate(0,772.778,550.667) scale(1.11111,-1.11111) translate(-75.0556,-1044.04)" width="40" x="750.5555555555555" xlink:href="#GroundDisconnector:中性点地刀12_0" y="528.4444444444445" zvalue="133"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454756335618" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454756335618"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,772.778,550.667) scale(1.11111,-1.11111) translate(-75.0556,-1044.04)" width="40" x="750.5555555555555" y="528.4444444444445"/></g>
  <g id="177">
   <use class="kv110" height="20" transform="rotate(270,947.263,395.818) scale(1.11111,1.11111) translate(-94.1707,-38.4707)" width="10" x="941.7070808795968" xlink:href="#GroundDisconnector:地刀_0" y="384.7070707070707" zvalue="138"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454756204546" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454756204546"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,947.263,395.818) scale(1.11111,1.11111) translate(-94.1707,-38.4707)" width="10" x="941.7070808795968" y="384.7070707070707"/></g>
  <g id="137">
   <use class="kv110" height="20" transform="rotate(90,1145.26,375) scale(1.11111,1.11111) translate(-113.971,-36.3889)" width="10" x="1139.707080879597" xlink:href="#GroundDisconnector:地刀_0" y="363.8888888888889" zvalue="169"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454758105090" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454758105090"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1145.26,375) scale(1.11111,1.11111) translate(-113.971,-36.3889)" width="10" x="1139.707080879597" y="363.8888888888889"/></g>
  <g id="79">
   <use class="kv110" height="20" transform="rotate(90,1151.11,465.556) scale(1.11111,1.11111) translate(-114.556,-45.4444)" width="10" x="1145.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="454.4444444444445" zvalue="190"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454758301698" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454758301698"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1151.11,465.556) scale(1.11111,1.11111) translate(-114.556,-45.4444)" width="10" x="1145.555555555556" y="454.4444444444445"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="81">
   <g id="810">
    <use class="kv110" height="50" transform="rotate(0,1551.34,528.97) scale(1.91667,1.96323) translate(-728.196,-235.45)" width="30" x="1522.59" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="479.89" zvalue="108"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874590781442" ObjectName="110"/>
    </metadata>
   </g>
   <g id="811">
    <use class="v10500" height="50" transform="rotate(0,1551.34,528.97) scale(1.91667,1.96323) translate(-728.196,-235.45)" width="30" x="1522.59" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="479.89" zvalue="108"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874594058242" ObjectName="10.5"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399532675074" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399532675074"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1551.34,528.97) scale(1.91667,1.96323) translate(-728.196,-235.45)" width="30" x="1522.59" y="479.89"/></g>
  <g id="163">
   <g id="1630">
    <use class="kv110" height="50" transform="rotate(0,888.352,531.081) scale(1.91667,1.96323) translate(-411.114,-236.486)" width="30" x="859.6" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="482" zvalue="143"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874590650370" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1631">
    <use class="v10500" height="50" transform="rotate(0,888.352,531.081) scale(1.91667,1.96323) translate(-411.114,-236.486)" width="30" x="859.6" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="482" zvalue="143"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874593992706" ObjectName="10.5"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399532609538" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399532609538"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,888.352,531.081) scale(1.91667,1.96323) translate(-411.114,-236.486)" width="30" x="859.6" y="482"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="263">
   <use class="kv110" height="30" transform="rotate(0,1402.93,122.444) scale(2.52211,1.48148) translate(-837.547,-32.5722)" width="12" x="1387.799353110965" xlink:href="#EnergyConsumer:负荷_0" y="100.2222222222222" zvalue="118"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454756859906" ObjectName="110kV备用线"/>
   <cge:TPSR_Ref TObjectID="6192454756859906"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1402.93,122.444) scale(2.52211,1.48148) translate(-837.547,-32.5722)" width="12" x="1387.799353110965" y="100.2222222222222"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="112">
   <use class="kv110" height="40" transform="rotate(0,1207.97,518.909) scale(1.375,1.375) translate(-321.946,-134.021)" width="40" x="1180.469485689306" xlink:href="#Accessory:五卷PT_0" y="491.4090909090909" zvalue="145"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454756073474" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1207.97,518.909) scale(1.375,1.375) translate(-321.946,-134.021)" width="40" x="1180.469485689306" y="491.4090909090909"/></g>
  <g id="83">
   <use class="v10500" height="29" transform="rotate(0,1023.26,761.585) scale(1.35068,-1.35068) translate(-260.41,-1320.35)" width="30" x="1003" xlink:href="#Accessory:PT12321_0" y="742" zvalue="198"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454758498306" ObjectName="1号励磁变"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1023.26,761.585) scale(1.35068,-1.35068) translate(-260.41,-1320.35)" width="30" x="1003" y="742"/></g>
  <g id="85">
   <use class="v10500" height="29" transform="rotate(0,1680.26,766.585) scale(1.35068,-1.35068) translate(-430.987,-1329.06)" width="30" x="1660" xlink:href="#Accessory:PT12321_0" y="747" zvalue="201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454758563842" ObjectName="2号励磁变"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1680.26,766.585) scale(1.35068,-1.35068) translate(-430.987,-1329.06)" width="30" x="1660" y="747"/></g>
  <g id="143">
   <use class="v10500" height="29" transform="rotate(0,1078.26,758.585) scale(1.35068,-1.35068) translate(-274.69,-1315.13)" width="30" x="1058" xlink:href="#Accessory:PT12321_0" y="739" zvalue="216"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454758694914" ObjectName="1号厂用电"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1078.26,758.585) scale(1.35068,-1.35068) translate(-274.69,-1315.13)" width="30" x="1058" y="739"/></g>
  <g id="122">
   <use class="v10500" height="29" transform="rotate(0,1760.26,752.585) scale(1.35068,-1.35068) translate(-451.758,-1304.69)" width="30" x="1740" xlink:href="#Accessory:PT12321_0" y="732.9999952316285" zvalue="224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454758760450" ObjectName="2号厂用电"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1760.26,752.585) scale(1.35068,-1.35068) translate(-451.758,-1304.69)" width="30" x="1740" y="732.9999952316285"/></g>
  <g id="153">
   <use class="v10500" height="29" transform="rotate(0,1831.26,752.585) scale(1.35068,-1.35068) translate(-470.192,-1304.69)" width="30" x="1811" xlink:href="#Accessory:PT12321_0" y="732.9999950597539" zvalue="229"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454758891522" ObjectName="3号厂用电"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1831.26,752.585) scale(1.35068,-1.35068) translate(-470.192,-1304.69)" width="30" x="1811" y="732.9999950597539"/></g>
  <g id="178">
   <use class="v10500" height="18" transform="rotate(0,957,821) scale(1,1) translate(0,0)" width="18" x="948" xlink:href="#Accessory:四卷PT_0" y="812" zvalue="255"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454759350274" ObjectName="#1主变四卷PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,957,821) scale(1,1) translate(0,0)" width="18" x="948" y="812"/></g>
  <g id="179">
   <use class="v10500" height="18" transform="rotate(0,788,855) scale(1,1) translate(0,0)" width="15" x="780.5" xlink:href="#Accessory:PT8_0" y="846" zvalue="256"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454759415810" ObjectName="#1主变PT8"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,788,855) scale(1,1) translate(0,0)" width="15" x="780.5" y="846"/></g>
  <g id="183">
   <use class="v10500" height="18" transform="rotate(0,1629,824) scale(1,1) translate(0,0)" width="18" x="1620" xlink:href="#Accessory:四卷PT_0" y="815" zvalue="260"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454759481346" ObjectName="#2主变四卷PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1629,824) scale(1,1) translate(0,0)" width="18" x="1620" y="815"/></g>
  <g id="187">
   <use class="v10500" height="18" transform="rotate(0,1474.5,857) scale(1,1) translate(0,0)" width="15" x="1467" xlink:href="#Accessory:PT8_0" y="848" zvalue="263"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454759546882" ObjectName="#2主变PT8"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1474.5,857) scale(1,1) translate(0,0)" width="15" x="1467" y="848"/></g>
  <g id="189">
   <use class="v10500" height="18" transform="rotate(0,1078.76,536.775) scale(1.80274,-1.80274) translate(-474.338,-827.307)" width="15" x="1065.23993812537" xlink:href="#Accessory:PT8_0" y="520.550742042397" zvalue="266"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454759612418" ObjectName="0912PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1078.76,536.775) scale(1.80274,-1.80274) translate(-474.338,-827.307)" width="15" x="1065.23993812537" y="520.550742042397"/></g>
  <g id="191">
   <use class="v10500" height="18" transform="rotate(0,1758.01,540) scale(1.80274,-1.80274) translate(-776.802,-832.32)" width="15" x="1744.493459899133" xlink:href="#Accessory:PT8_0" y="523.7753710211986" zvalue="269"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454759677954" ObjectName="0922PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1758.01,540) scale(1.80274,-1.80274) translate(-776.802,-832.32)" width="15" x="1744.493459899133" y="523.7753710211986"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v10500" height="30" transform="rotate(0,890.885,870.885) scale(1.85899,1.85899) translate(-398.769,-389.528)" width="30" x="863" xlink:href="#Generator:发电机_0" y="843" zvalue="193"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454758367234" ObjectName="#1发电机  35MW"/>
   <cge:TPSR_Ref TObjectID="6192454758367234"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,890.885,870.885) scale(1.85899,1.85899) translate(-398.769,-389.528)" width="30" x="863" y="843"/></g>
  <g id="65">
   <use class="v10500" height="30" transform="rotate(0,1553,879.885) scale(1.85899,1.85899) translate(-704.715,-393.687)" width="30" x="1525.115146847647" xlink:href="#Generator:发电机_0" y="852" zvalue="195"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454758432770" ObjectName="#2发电机   35MW"/>
   <cge:TPSR_Ref TObjectID="6192454758432770"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1553,879.885) scale(1.85899,1.85899) translate(-704.715,-393.687)" width="30" x="1525.115146847647" y="852"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,775.932,26.2385) scale(1,1) translate(0,9.99201e-15)" writing-mode="lr" x="775.46" xml:space="preserve" y="30.92" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136292233218" ObjectName="P"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="4" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,775.932,50) scale(1,1) translate(0,2.58203e-14)" writing-mode="lr" x="775.46" xml:space="preserve" y="54.68" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136292298754" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="8" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,775.932,73.7615) scale(1,1) translate(0,4.16486e-14)" writing-mode="lr" x="775.46" xml:space="preserve" y="78.44" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136292364290" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="9" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1382.93,13.6009) scale(1,1) translate(0,1.4803e-15)" writing-mode="lr" x="1382.46" xml:space="preserve" y="18.29" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136288235522" ObjectName="P"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="10" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1382.93,38.8333) scale(1,1) translate(0,2.38912e-14)" writing-mode="lr" x="1382.46" xml:space="preserve" y="43.52" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136288301058" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="16" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1382.93,64.0657) scale(1,1) translate(0,4.63021e-14)" writing-mode="lr" x="1382.46" xml:space="preserve" y="68.76000000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136288366594" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="18" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,808.352,413.124) scale(1,1) translate(0,8.82674e-14)" writing-mode="lr" x="807.8" xml:space="preserve" y="417.87" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136285679618" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="182">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="182" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1471.34,411.241) scale(1,1) translate(0,-8.78495e-14)" writing-mode="lr" x="1470.79" xml:space="preserve" y="415.99" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136289677314" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="193">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="193" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,808.352,446.112) scale(1,1) translate(0,9.55922e-14)" writing-mode="lr" x="807.8" xml:space="preserve" y="450.86" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136285745154" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="194">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="194" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1471.34,444.229) scale(1,1) translate(0,9.51743e-14)" writing-mode="lr" x="1470.79" xml:space="preserve" y="448.98" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136289742850" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="195">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="195" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,813.352,644.421) scale(1,1) translate(0,-1.39626e-13)" writing-mode="lr" x="812.8" xml:space="preserve" y="649.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136490217474" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="196">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="196" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1468.84,648.789) scale(1,1) translate(0,-1.40596e-13)" writing-mode="lr" x="1468.29" xml:space="preserve" y="653.54" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136491069442" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="197">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="197" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,813.352,677.41) scale(1,1) translate(0,5.14327e-13)" writing-mode="lr" x="812.8" xml:space="preserve" y="682.16" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136490283010" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="198">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="198" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1468.84,681.777) scale(1,1) translate(0,-4.43762e-13)" writing-mode="lr" x="1468.29" xml:space="preserve" y="686.52" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136491134978" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="199">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="199" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,808.352,479.1) scale(1,1) translate(0,5.14585e-14)" writing-mode="lr" x="807.8" xml:space="preserve" y="483.85" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136285941762" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="200">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="200" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1471.34,477.217) scale(1,1) translate(0,1.02499e-13)" writing-mode="lr" x="1470.79" xml:space="preserve" y="481.97" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136289939458" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="201">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="201" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,813.352,710.398) scale(1,1) translate(0,-4.62827e-13)" writing-mode="lr" x="812.8" xml:space="preserve" y="715.15" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136490348546" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="202">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="202" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1468.84,714.765) scale(1,1) translate(0,-4.65736e-13)" writing-mode="lr" x="1468.29" xml:space="preserve" y="719.51" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136491200514" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="203">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="203" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,888.385,951.884) scale(1,1) translate(0,-8.32241e-13)" writing-mode="lr" x="887.83" xml:space="preserve" y="956.62" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136295378946" ObjectName="P"/>
   </metadata>
  </g>
  <g id="204">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="204" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1550.5,959.529) scale(1,1) translate(0,-8.39031e-13)" writing-mode="lr" x="1549.95" xml:space="preserve" y="964.26" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136296034306" ObjectName="P"/>
   </metadata>
  </g>
  <g id="205">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="205" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,888.385,983.313) scale(1,1) translate(0,-8.60154e-13)" writing-mode="lr" x="887.83" xml:space="preserve" y="988.05" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136295444482" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="206">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="206" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1550.5,990.957) scale(1,1) translate(0,-8.66944e-13)" writing-mode="lr" x="1549.95" xml:space="preserve" y="995.6900000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136296099842" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="207">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="207" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,888.385,1014.74) scale(1,1) translate(0,-8.88068e-13)" writing-mode="lr" x="887.83" xml:space="preserve" y="1019.48" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136295510018" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="208">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="208" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1550.5,1022.39) scale(1,1) translate(0,-8.94858e-13)" writing-mode="lr" x="1549.95" xml:space="preserve" y="1027.12" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136296165378" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="224">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="224" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.611,332.167) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="337.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136294199298" ObjectName="F"/>
   </metadata>
  </g>
  <g id="223">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="223" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,155.611,260.167) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="265.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136297869314" ObjectName="F"/>
   </metadata>
  </g>
  <g id="222">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="222" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,333.222,261.167) scale(1,1) translate(0,0)" writing-mode="lr" x="333.38" xml:space="preserve" y="266.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136297934850" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,155.611,285.167) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="290.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136297738242" ObjectName="F"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,333.222,286.167) scale(1,1) translate(0,0)" writing-mode="lr" x="333.38" xml:space="preserve" y="291.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136297803778" ObjectName="F"/>
   </metadata>
  </g>
  <g id="216">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="216" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.611,377.389) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="382.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136495460354" ObjectName="F"/>
   </metadata>
  </g>
  <g id="214">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="214" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,333.611,377.389) scale(1,1) translate(0,0)" writing-mode="lr" x="333.77" xml:space="preserve" y="382.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136495525890" ObjectName="F"/>
   </metadata>
  </g>
  <g id="236">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.611,400.389) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="405.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136495788034" ObjectName="F"/>
   </metadata>
  </g>
  <g id="238">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="238" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,333.611,400.389) scale(1,1) translate(0,0)" writing-mode="lr" x="333.77" xml:space="preserve" y="405.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136495656962" ObjectName="F"/>
   </metadata>
  </g>
  <g id="210">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="210" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,155.611,309.167) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="314.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127179714565" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,335.611,308.167) scale(1,1) translate(0,0)" writing-mode="lr" x="335.77" xml:space="preserve" y="313.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127179649029" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="6" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1221,617.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1220.53" xml:space="preserve" y="621.9400000000001" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136293806082" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="7" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1221,646.167) scale(1,1) translate(0,-2.79184e-13)" writing-mode="lr" x="1220.53" xml:space="preserve" y="650.9400000000001" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136293871618" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="11" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1221,679.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1220.53" xml:space="preserve" y="683.9400000000001" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136293937154" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="12" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,581,341.667) scale(1,1) translate(0,0)" writing-mode="lr" x="580.53" xml:space="preserve" y="346.44" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136294068226" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="221">
   <use height="30" transform="rotate(0,330.673,186.357) scale(0.708333,0.665547) translate(131.784,88.6322)" width="30" x="320.05" xlink:href="#State:红绿圆(方形)_0" y="176.37" zvalue="328"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374928117761" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,330.673,186.357) scale(0.708333,0.665547) translate(131.784,88.6322)" width="30" x="320.05" y="176.37"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,235.048,186.357) scale(0.708333,0.665547) translate(92.4093,88.6322)" width="30" x="224.42" xlink:href="#State:红绿圆(方形)_0" y="176.37" zvalue="329"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562962171297793" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,235.048,186.357) scale(0.708333,0.665547) translate(92.4093,88.6322)" width="30" x="224.42" y="176.37"/></g>
 </g>
</svg>