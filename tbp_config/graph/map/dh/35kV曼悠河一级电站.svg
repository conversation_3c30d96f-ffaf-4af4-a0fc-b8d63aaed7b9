<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549581905922" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="4.416666666666668" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.249999999999998" y2="20.16666666666667"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:带熔断器35kVPT11_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="1.1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="26.25" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="18.06481481481482" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="20.69444444444444" y1="24.96612466124661" y2="24.96612466124661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69444444444444" x2="19.37962962962963" y1="24.96612466124661" y2="22.5"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,5.71) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="13" y2="1"/>
   <ellipse cx="15.03" cy="18.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.67" cy="23.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="18.5" cy="23.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="20.5" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_0" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3" x2="7" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.25" x2="5.083333333333332" y1="7.083333333333336" y2="19.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_1" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.833333333333333" x2="6.916666666666667" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.05" x2="5.05" y1="5.000000000000004" y2="22.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_2" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.166666666666668" x2="1.333333333333333" y1="5.083333333333332" y2="24.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1" x2="8.833333333333334" y1="4.999999999999998" y2="24.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_0" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="21.05" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="21.05" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.66666666666667" x2="22.58333333333334" y1="6.41666666666667" y2="22.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.08333333333334" x2="21.08333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.09150672768254" x2="21.09150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.46666666666667" x2="22.43333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_1" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="21.05" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="21.05" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.41666666666667" x2="18.5" y1="4.416666666666668" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.08333333333334" x2="21.08333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.09150672768254" x2="21.09150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.46666666666667" x2="22.43333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_2" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="21.05" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="21.05" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.08333333333334" x2="21.08333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.09150672768254" x2="21.09150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.46666666666667" x2="22.43333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.91666666666667" x2="28.25" y1="3.083333333333332" y2="24.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="27.5" x2="13.16666666666667" y1="3.166666666666664" y2="24.83333333333334"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1754.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2334.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变带熔断器DY_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <path d="M 11.5417 13.7917 L 8.54167 13.7917 L 10.0417 11.0417 L 11.5417 13.7917 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <rect fill-opacity="0" height="4.32" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.06,5.25) scale(1,1) translate(0,0)" width="3.43" x="8.34" y="3.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0235180220435" x2="10.0235180220435" y1="0.5833333333333091" y2="9.290410445610181"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="29.16666666666667" y2="23.64357429718876"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV曼悠河一级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="36.25" xlink:href="logo.png" y="34.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="170" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,185.25,64.75) scale(1,1) translate(0,0)" writing-mode="lr" x="185.25" xml:space="preserve" y="68.25" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,181.75,64.4403) scale(1,1) translate(0,0)" writing-mode="lr" x="181.75" xml:space="preserve" y="73.44" zvalue="3">35kV曼悠河一级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="35" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,83.625,203) scale(1,1) translate(0,0)" width="72.88" x="47.19" y="191" zvalue="334"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.625,203) scale(1,1) translate(0,0)" writing-mode="lr" x="83.63" xml:space="preserve" y="207.5" zvalue="334">信号一览</text>
  <line fill="none" id="168" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="378.25" x2="378.25" y1="2.75" y2="1032.75" zvalue="4"/>
  <line fill="none" id="165" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.250000000000455" x2="371.25" y1="138.6204926140824" y2="138.6204926140824" zvalue="6"/>
  <line fill="none" id="163" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.250000000000455" x2="371.25" y1="608.6204926140824" y2="608.6204926140824" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="94.25" y1="923.75" y2="923.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="94.25" y1="962.9132999999999" y2="962.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="4.25" y1="923.75" y2="962.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="94.25" y1="923.75" y2="962.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="364.25" y1="923.75" y2="923.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="364.25" y1="962.9132999999999" y2="962.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="94.25" y1="923.75" y2="962.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="364.25" x2="364.25" y1="923.75" y2="962.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="94.25" y1="962.91327" y2="962.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="94.25" y1="990.83167" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="4.25" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="94.25" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="184.25" y1="962.91327" y2="962.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="184.25" y1="990.83167" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="94.25" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.25" x2="184.25" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.2500000000001" x2="274.2500000000001" y1="962.91327" y2="962.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.2500000000001" x2="274.2500000000001" y1="990.83167" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.2500000000001" x2="184.2500000000001" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.2500000000001" x2="274.2500000000001" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.25" x2="364.25" y1="962.91327" y2="962.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.25" x2="364.25" y1="990.83167" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.25" x2="274.25" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="364.25" x2="364.25" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="94.25" y1="990.8316" y2="990.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="94.25" y1="1018.75" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="4.25" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="94.25" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="184.25" y1="990.8316" y2="990.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="184.25" y1="1018.75" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="94.25" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.25" x2="184.25" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.2500000000001" x2="274.2500000000001" y1="990.8316" y2="990.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.2500000000001" x2="274.2500000000001" y1="1018.75" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.2500000000001" x2="184.2500000000001" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.2500000000001" x2="274.2500000000001" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.25" x2="364.25" y1="990.8316" y2="990.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.25" x2="364.25" y1="1018.75" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.25" x2="274.25" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="364.25" x2="364.25" y1="990.8316" y2="1018.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.25,943.75) scale(1,1) translate(0,0)" writing-mode="lr" x="49.25" xml:space="preserve" y="949.75" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46.25,977.75) scale(1,1) translate(0,0)" writing-mode="lr" x="46.25" xml:space="preserve" y="983.75" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.25,977.75) scale(1,1) translate(0,0)" writing-mode="lr" x="228.25" xml:space="preserve" y="983.75" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.25,1005.75) scale(1,1) translate(0,0)" writing-mode="lr" x="45.25" xml:space="preserve" y="1011.75" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.25,1005.75) scale(1,1) translate(0,0)" writing-mode="lr" x="227.25" xml:space="preserve" y="1011.75" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.75,638.25) scale(1,1) translate(0,0)" writing-mode="lr" x="69.75" xml:space="preserve" y="642.75" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,643.073,243.643) scale(1,1) translate(0,0)" writing-mode="lr" x="643.0700000000001" xml:space="preserve" y="248.14" zvalue="37">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1057.94,315.937) scale(1,1) translate(0,0)" writing-mode="lr" x="1057.94" xml:space="preserve" y="320.44" zvalue="39">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,964.446,487.013) scale(1,1) translate(0,0)" writing-mode="lr" x="964.45" xml:space="preserve" y="491.51" zvalue="45">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1669.14,657.421) scale(1,1) translate(0,0)" writing-mode="lr" x="1669.14" xml:space="preserve" y="661.92" zvalue="48">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,718.684,929.375) scale(1,1) translate(0,3.0263e-13)" writing-mode="lr" x="718.6838304415306" xml:space="preserve" y="933.8753747717902" zvalue="54">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,774.296,232.865) scale(1,1) translate(0,0)" writing-mode="lr" x="774.3" xml:space="preserve" y="237.37" zvalue="63">3411</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,737.672,78.6429) scale(1,1) translate(0,0)" writing-mode="lr" x="737.67" xml:space="preserve" y="83.14" zvalue="70">35kV曼悠河一级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1338.9,202.183) scale(1,1) translate(-2.9372e-13,4.28247e-14)" writing-mode="lr" x="1338.9" xml:space="preserve" y="206.68" zvalue="79">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,966.5,373) scale(1,1) translate(0,0)" writing-mode="lr" x="966.5" xml:space="preserve" y="377.5" zvalue="93">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,794,177) scale(1,1) translate(0,0)" writing-mode="lr" x="794" xml:space="preserve" y="181.5" zvalue="108">34117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1368,150) scale(1,1) translate(0,0)" writing-mode="lr" x="1368" xml:space="preserve" y="154.5" zvalue="114">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1375,210) scale(1,1) translate(0,0)" writing-mode="lr" x="1375" xml:space="preserve" y="214.5" zvalue="116">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1003.95,572.198) scale(1,1) translate(0,0)" writing-mode="lr" x="1003.95" xml:space="preserve" y="576.7" zvalue="122">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,744.768,767.754) scale(1,1) translate(8.12722e-14,0)" writing-mode="lr" x="744.77" xml:space="preserve" y="772.25" zvalue="138">641</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,985.208,630.143) scale(1,1) translate(0,0)" writing-mode="lr" x="985.21" xml:space="preserve" y="634.64" zvalue="220">6011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,990.929,396.031) scale(1,1) translate(0,2.13198e-13)" writing-mode="lr" x="990.9299999999999" xml:space="preserve" y="400.53" zvalue="224"> 301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,970.143,506.692) scale(1,1) translate(0,0)" writing-mode="lr" x="970.14" xml:space="preserve" y="511.19" zvalue="227">8MVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="205" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1312.93,57.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1312.93" xml:space="preserve" y="61.88" zvalue="231">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="217" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,664.94,722.643) scale(1,1) translate(0,0)" writing-mode="lr" x="664.9400000000001" xml:space="preserve" y="727.14" zvalue="236">6411</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="223" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,569.44,860.893) scale(1,1) translate(0,0)" writing-mode="lr" x="569.4400000000001" xml:space="preserve" y="865.39" zvalue="240">6911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="224" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,718.684,949.125) scale(1,1) translate(0,3.09208e-13)" writing-mode="lr" x="718.6838304415306" xml:space="preserve" y="953.6253747717902" zvalue="242">2.5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="277" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,800.19,872.893) scale(1,1) translate(0,0)" writing-mode="lr" x="800.1900000000001" xml:space="preserve" y="877.39" zvalue="246">6912</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="240" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,798.53,725.776) scale(1,1) translate(0,0)" writing-mode="lr" x="798.53" xml:space="preserve" y="730.28" zvalue="252">#1励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="239" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,788.69,782.643) scale(1,1) translate(0,0)" writing-mode="lr" x="788.6900000000001" xml:space="preserve" y="787.14" zvalue="254">6913</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="246" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1306.68,929.375) scale(1,1) translate(0,3.0263e-13)" writing-mode="lr" x="1306.683830441531" xml:space="preserve" y="933.8753747717902" zvalue="258">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1332.77,767.754) scale(1,1) translate(1.46553e-13,0)" writing-mode="lr" x="1332.77" xml:space="preserve" y="772.25" zvalue="261">642</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="244" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1252.94,722.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1252.94" xml:space="preserve" y="727.14" zvalue="266">6421</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="243" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1168.97,878.393) scale(1,1) translate(0,0)" writing-mode="lr" x="1168.97" xml:space="preserve" y="882.89" zvalue="270">6921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1306.68,949.125) scale(1,1) translate(0,3.09208e-13)" writing-mode="lr" x="1306.683830441531" xml:space="preserve" y="953.6253747717902" zvalue="272">2.5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="278" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1454.94,865.893) scale(1,1) translate(0,0)" writing-mode="lr" x="1454.94" xml:space="preserve" y="870.39" zvalue="274">6922</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="242" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1386.53,725.776) scale(1,1) translate(0,0)" writing-mode="lr" x="1386.53" xml:space="preserve" y="730.28" zvalue="279">#2励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="241" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1376.69,781.393) scale(1,1) translate(0,0)" writing-mode="lr" x="1376.69" xml:space="preserve" y="785.89" zvalue="282">6923</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1405.44,612.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1405.44" xml:space="preserve" y="616.64" zvalue="286">6901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1458.73,480.125) scale(1,1) translate(6.18272e-13,0)" writing-mode="lr" x="1458.73" xml:space="preserve" y="484.63" zvalue="289">6.3kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="298" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1527.1,752.143) scale(1,1) translate(-3.34732e-13,0)" writing-mode="lr" x="1527.1" xml:space="preserve" y="756.64" zvalue="294">6431</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="295" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1595.99,895.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1595.99" xml:space="preserve" y="900.25" zvalue="298">#1站用变</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="264.75" y2="264.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="290.75" y2="290.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="264.75" y2="290.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="264.75" y2="290.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="264.75" y2="264.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="290.75" y2="290.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="264.75" y2="290.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="264.75" y2="290.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="290.75" y2="290.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="315" y2="315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="290.75" y2="315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="290.75" y2="315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="290.75" y2="290.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="315" y2="315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="290.75" y2="315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="290.75" y2="315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="315" y2="315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="337.75" y2="337.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="315" y2="337.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="315" y2="337.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="315" y2="315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="337.75" y2="337.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="315" y2="337.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="315" y2="337.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="337.75" y2="337.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="360.5" y2="360.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="337.75" y2="360.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="337.75" y2="360.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="337.75" y2="337.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="360.5" y2="360.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="337.75" y2="360.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="337.75" y2="360.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="360.5" y2="360.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="383.25" y2="383.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="360.5" y2="383.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="360.5" y2="383.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="360.5" y2="360.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="383.25" y2="383.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="360.5" y2="383.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="360.5" y2="383.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="383.25" y2="383.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="406" y2="406"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="383.25" y2="406"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="383.25" y2="406"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="383.25" y2="383.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="406" y2="406"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="383.25" y2="406"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="383.25" y2="406"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="406" y2="406"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="428.75" y2="428.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="406" y2="428.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="406" y2="428.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="406" y2="406"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="428.75" y2="428.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="406" y2="428.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="406" y2="428.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.399,203.591) scale(1,1) translate(0,0)" writing-mode="lr" x="199.4" xml:space="preserve" y="208.09" zvalue="321">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,304.399,203.591) scale(1,1) translate(0,0)" writing-mode="lr" x="304.4" xml:space="preserve" y="208.09" zvalue="322">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,56.5,277.75) scale(1,1) translate(0,0)" writing-mode="lr" x="14" xml:space="preserve" y="282.25" zvalue="323">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,237,277.75) scale(1,1) translate(0,0)" writing-mode="lr" x="194.5" xml:space="preserve" y="282.25" zvalue="324">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,351) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="355.5" zvalue="325">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.875,349.75) scale(1,1) translate(0,0)" writing-mode="lr" x="237.88" xml:space="preserve" y="354.25" zvalue="326">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,55.5,303.75) scale(1,1) translate(0,0)" writing-mode="lr" x="13" xml:space="preserve" y="308.25" zvalue="335">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,236,303.75) scale(1,1) translate(0,0)" writing-mode="lr" x="193.5" xml:space="preserve" y="308.25" zvalue="336">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.6875,397) scale(1,1) translate(0,0)" writing-mode="lr" x="56.69" xml:space="preserve" y="401.5" zvalue="339">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224.688,396) scale(1,1) translate(0,0)" writing-mode="lr" x="224.69" xml:space="preserve" y="400.5" zvalue="341">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.6875,420) scale(1,1) translate(0,0)" writing-mode="lr" x="56.69" xml:space="preserve" y="424.5" zvalue="343">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224.688,419) scale(1,1) translate(0,0)" writing-mode="lr" x="224.69" xml:space="preserve" y="423.5" zvalue="344">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,55.5,326.75) scale(1,1) translate(0,0)" writing-mode="lr" x="13" xml:space="preserve" y="331.25" zvalue="345">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235.5,325.75) scale(1,1) translate(0,0)" writing-mode="lr" x="193" xml:space="preserve" y="330.25" zvalue="347">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,204,944) scale(1,1) translate(0,0)" writing-mode="lr" x="204" xml:space="preserve" y="950" zvalue="353">ManYouHe-01-2012</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="47.19" y="191" zvalue="334"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="173">
   <path class="kv35" d="M 618.75 267.14 L 1579.57 267.14" stroke-width="6" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674234073093" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674234073093"/></metadata>
  <path d="M 618.75 267.14 L 1579.57 267.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="v6300" d="M 465.27 679.67 L 1655 679.67" stroke-width="6" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674234007557" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674234007557"/></metadata>
  <path d="M 465.27 679.67 L 1655 679.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="172">
   <use class="kv35" height="30" transform="rotate(0,1038.5,316.937) scale(1.11111,0.814815) translate(-103.016,69.2532)" width="15" x="1030.162754243136" xlink:href="#Disconnector:刀闸_0" y="304.7142857142858" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449551990790" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449551990790"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1038.5,316.937) scale(1.11111,0.814815) translate(-103.016,69.2532)" width="15" x="1030.162754243136" y="304.7142857142858"/></g>
  <g id="272">
   <use class="kv35" height="30" transform="rotate(0,741.74,225.865) scale(-1.11111,-0.814815) translate(-1408.47,-505.841)" width="15" x="733.4067881196823" xlink:href="#Disconnector:刀闸_0" y="213.6428704942976" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449551794182" ObjectName="35kV曼悠河一级线3411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449551794182"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,741.74,225.865) scale(-1.11111,-0.814815) translate(-1408.47,-505.841)" width="15" x="733.4067881196823" y="213.6428704942976"/></g>
  <g id="219">
   <use class="kv35" height="30" transform="rotate(0,1306.74,201.865) scale(-1.11111,-0.814815) translate(-2481.97,-452.386)" width="15" x="1298.406788119682" xlink:href="#Disconnector:刀闸_0" y="189.6428704942976" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449551663110" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449551663110"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1306.74,201.865) scale(-1.11111,-0.814815) translate(-2481.97,-452.386)" width="15" x="1298.406788119682" y="189.6428704942976"/></g>
  <g id="187">
   <use class="v6300" height="25" transform="rotate(0,1037.21,631.143) scale(1.42857,1.42857) translate(-301.52,-183.986)" width="45" x="1005.065283870998" xlink:href="#Disconnector:特殊刀闸_0" y="613.2857142857142" zvalue="219"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449552056326" ObjectName="#1主变6.3kV侧6011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449552056326"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1037.21,631.143) scale(1.42857,1.42857) translate(-301.52,-183.986)" width="45" x="1005.065283870998" y="613.2857142857142"/></g>
  <g id="211">
   <use class="v6300" height="25" transform="rotate(0,714.708,723.643) scale(1.42857,1.42857) translate(-204.77,-211.736)" width="45" x="682.5652838709976" xlink:href="#Disconnector:特殊刀闸_0" y="705.7857142857142" zvalue="235"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449552252933" ObjectName="#1发电机6411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449552252933"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,714.708,723.643) scale(1.42857,1.42857) translate(-204.77,-211.736)" width="45" x="682.5652838709976" y="705.7857142857142"/></g>
  <g id="221">
   <use class="v6300" height="25" transform="rotate(0,619.208,861.893) scale(1.42857,1.42857) translate(-176.12,-253.211)" width="45" x="587.0652838709975" xlink:href="#Disconnector:特殊刀闸_0" y="844.0357142857142" zvalue="239"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449552318469" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449552318469"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,619.208,861.893) scale(1.42857,1.42857) translate(-176.12,-253.211)" width="45" x="587.0652838709975" y="844.0357142857142"/></g>
  <g id="226">
   <use class="v6300" height="25" transform="rotate(0,839.208,861.893) scale(1.42857,1.42857) translate(-242.12,-253.211)" width="45" x="807.0652838709975" xlink:href="#Disconnector:特殊刀闸_0" y="844.0357142857142" zvalue="245"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449552384005" ObjectName="#1发电机6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449552384005"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,839.208,861.893) scale(1.42857,1.42857) translate(-242.12,-253.211)" width="45" x="807.0652838709975" y="844.0357142857142"/></g>
  <g id="233">
   <use class="v6300" height="25" transform="rotate(0,839.708,783.643) scale(1.42857,1.42857) translate(-242.27,-229.736)" width="45" x="807.5652838709975" xlink:href="#Disconnector:特殊刀闸_0" y="765.7857142857142" zvalue="253"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449552515077" ObjectName="#1发电机6913隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449552515077"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,839.708,783.643) scale(1.42857,1.42857) translate(-242.27,-229.736)" width="45" x="807.5652838709975" y="765.7857142857142"/></g>
  <g id="265">
   <use class="v6300" height="25" transform="rotate(0,1302.71,723.643) scale(1.42857,1.42857) translate(-381.17,-211.736)" width="45" x="1270.565283870998" xlink:href="#Disconnector:特殊刀闸_0" y="705.7857142857142" zvalue="265"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449552842757" ObjectName="#2发电机6421隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449552842757"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1302.71,723.643) scale(1.42857,1.42857) translate(-381.17,-211.736)" width="45" x="1270.565283870998" y="705.7857142857142"/></g>
  <g id="262">
   <use class="v6300" height="25" transform="rotate(0,1207.21,861.893) scale(1.42857,1.42857) translate(-352.52,-253.211)" width="45" x="1175.065283870998" xlink:href="#Disconnector:特殊刀闸_0" y="844.0357142857142" zvalue="269"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449552777221" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449552777221"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1207.21,861.893) scale(1.42857,1.42857) translate(-352.52,-253.211)" width="45" x="1175.065283870998" y="844.0357142857142"/></g>
  <g id="257">
   <use class="v6300" height="25" transform="rotate(0,1427.21,861.893) scale(1.42857,1.42857) translate(-418.52,-253.211)" width="45" x="1395.065283870998" xlink:href="#Disconnector:特殊刀闸_0" y="844.0357142857142" zvalue="273"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449552711685" ObjectName="#2发电机6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449552711685"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1427.21,861.893) scale(1.42857,1.42857) translate(-418.52,-253.211)" width="45" x="1395.065283870998" y="844.0357142857142"/></g>
  <g id="249">
   <use class="v6300" height="25" transform="rotate(0,1427.71,782.393) scale(1.42857,1.42857) translate(-418.67,-229.361)" width="45" x="1395.565283870998" xlink:href="#Disconnector:特殊刀闸_0" y="764.5357142857142" zvalue="280"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449552580613" ObjectName="#2发电机6923隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449552580613"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1427.71,782.393) scale(1.42857,1.42857) translate(-418.67,-229.361)" width="45" x="1395.565283870998" y="764.5357142857142"/></g>
  <g id="280">
   <use class="v6300" height="25" transform="rotate(0,1455.21,613.143) scale(1.42857,1.42857) translate(-426.92,-178.586)" width="45" x="1423.065283870998" xlink:href="#Disconnector:特殊刀闸_0" y="595.2857142857142" zvalue="285"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449553104902" ObjectName="6.3kV母线电压互感器6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449553104902"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1455.21,613.143) scale(1.42857,1.42857) translate(-426.92,-178.586)" width="45" x="1423.065283870998" y="595.2857142857142"/></g>
  <g id="289">
   <use class="v6300" height="25" transform="rotate(0,1583.96,753.143) scale(1.42857,1.42857) translate(-465.545,-220.586)" width="45" x="1551.815283870998" xlink:href="#Disconnector:特殊刀闸_0" y="735.2857142857142" zvalue="293"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449553235974" ObjectName="#1站用变6431隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449553235974"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1583.96,753.143) scale(1.42857,1.42857) translate(-465.545,-220.586)" width="45" x="1551.815283870998" y="735.2857142857142"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="126">
   <path class="kv35" d="M 1038.59 305.12 L 1038.59 267.14" stroke-width="1" zvalue="43"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@0" LinkObjectIDznd="173@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1038.59 305.12 L 1038.59 267.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv35" d="M 741.64 237.68 L 741.64 267.14" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="173@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 741.64 237.68 L 741.64 267.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv35" d="M 1306.64 213.68 L 1306.64 267.14" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@0" LinkObjectIDznd="173@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1306.64 213.68 L 1306.64 267.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv35" d="M 778.68 195.01 L 741.67 195.01" stroke-width="1" zvalue="110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="175" MaxPinNum="2"/>
   </metadata>
  <path d="M 778.68 195.01 L 741.67 195.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv35" d="M 1306.67 168.02 L 1352.68 168.02" stroke-width="1" zvalue="118"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201" LinkObjectIDznd="213@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1306.67 168.02 L 1352.68 168.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="v6300" d="M 713.89 779.37 L 713.89 852.64" stroke-width="1" zvalue="146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@1" LinkObjectIDznd="850@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 713.89 779.37 L 713.89 852.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="v6300" d="M 619.28 845.11 L 619.28 820.75 L 713.89 820.75" stroke-width="1" zvalue="158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="83" MaxPinNum="2"/>
   </metadata>
  <path d="M 619.28 845.11 L 619.28 820.75 L 713.89 820.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv35" d="M 741.67 213.85 L 741.67 137.42" stroke-width="1" zvalue="211"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@1" LinkObjectIDznd="267@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 741.67 213.85 L 741.67 137.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv35" d="M 972.32 354.01 L 1038.56 354.01" stroke-width="1" zvalue="214"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="197" MaxPinNum="2"/>
   </metadata>
  <path d="M 972.32 354.01 L 1038.56 354.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="v6300" d="M 1037.19 534.17 L 1037.19 562.78" stroke-width="1" zvalue="215"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@1" LinkObjectIDznd="237@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.19 534.17 L 1037.19 562.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="v6300" d="M 1037.36 593.16 L 1037.28 614.36" stroke-width="1" zvalue="220"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@1" LinkObjectIDznd="187@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.36 593.16 L 1037.28 614.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="v6300" d="M 1037.28 648.21 L 1037.28 679.67" stroke-width="1" zvalue="221"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@1" LinkObjectIDznd="161@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.28 648.21 L 1037.28 679.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv35" d="M 1038.56 328.95 L 1038.56 375.63" stroke-width="1" zvalue="224"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@1" LinkObjectIDznd="192@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1038.56 328.95 L 1038.56 375.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="kv35" d="M 1037.36 406.02 L 1037.36 442.98" stroke-width="1" zvalue="225"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@1" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.36 406.02 L 1037.36 442.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv35" d="M 1306.67 189.85 L 1306.67 141.44" stroke-width="1" zvalue="228"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@1" LinkObjectIDznd="204@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1306.67 189.85 L 1306.67 141.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv35" d="M 1359.68 224.01 L 1306.64 224.01" stroke-width="1" zvalue="229"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="209@0" LinkObjectIDznd="117" MaxPinNum="2"/>
   </metadata>
  <path d="M 1359.68 224.01 L 1306.64 224.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv35" d="M 702.95 158.31 L 741.67 158.31" stroke-width="1" zvalue="232"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="175" MaxPinNum="2"/>
   </metadata>
  <path d="M 702.95 158.31 L 741.67 158.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="v6300" d="M 714.78 679.67 L 714.78 706.86" stroke-width="1" zvalue="236"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@1" LinkObjectIDznd="211@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 714.78 679.67 L 714.78 706.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="v6300" d="M 714.78 740.71 L 714.78 758.12" stroke-width="1" zvalue="237"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@1" LinkObjectIDznd="259@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 714.78 740.71 L 714.78 758.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="v6300" d="M 619.28 915.63 L 619.28 878.96" stroke-width="1" zvalue="240"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@0" LinkObjectIDznd="221@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 619.28 915.63 L 619.28 878.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="229">
   <path class="v6300" d="M 713.89 822.5 L 839.28 822.5 L 839.28 845.11" stroke-width="1" zvalue="248"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83" LinkObjectIDznd="226@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 713.89 822.5 L 839.28 822.5 L 839.28 845.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="230">
   <path class="v6300" d="M 839.28 878.96 L 839.28 916.38" stroke-width="1" zvalue="249"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@1" LinkObjectIDznd="81@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 839.28 878.96 L 839.28 916.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="234">
   <path class="v6300" d="M 839.78 766.86 L 839.78 747.55" stroke-width="1" zvalue="254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@0" LinkObjectIDznd="232@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 839.78 766.86 L 839.78 747.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="238">
   <path class="v6300" d="M 839.78 800.71 L 839.78 824.29" stroke-width="1" zvalue="255"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@1" LinkObjectIDznd="229" MaxPinNum="2"/>
   </metadata>
  <path d="M 839.78 800.71 L 839.78 824.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="v6300" d="M 1301.89 779.37 L 1301.89 852.64" stroke-width="1" zvalue="262"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@1" LinkObjectIDznd="276@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1301.89 779.37 L 1301.89 852.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="268">
   <path class="v6300" d="M 1207.28 845.11 L 1207.28 820.75 L 1301.89 820.75" stroke-width="1" zvalue="264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="262@0" LinkObjectIDznd="270" MaxPinNum="2"/>
   </metadata>
  <path d="M 1207.28 845.11 L 1207.28 820.75 L 1301.89 820.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="264">
   <path class="v6300" d="M 1302.78 679.67 L 1302.78 706.86" stroke-width="1" zvalue="267"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@2" LinkObjectIDznd="265@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1302.78 679.67 L 1302.78 706.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="263">
   <path class="v6300" d="M 1302.78 740.71 L 1302.78 758.12" stroke-width="1" zvalue="268"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265@1" LinkObjectIDznd="274@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1302.78 740.71 L 1302.78 758.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="260">
   <path class="v6300" d="M 1207.28 915.63 L 1207.28 878.96" stroke-width="1" zvalue="271"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="275@0" LinkObjectIDznd="262@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1207.28 915.63 L 1207.28 878.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="254">
   <path class="v6300" d="M 1301.89 822.5 L 1427.28 822.5 L 1427.28 845.11" stroke-width="1" zvalue="276"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="270" LinkObjectIDznd="257@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1301.89 822.5 L 1427.28 822.5 L 1427.28 845.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="253">
   <path class="v6300" d="M 1427.28 878.96 L 1427.28 916.38" stroke-width="1" zvalue="277"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="257@1" LinkObjectIDznd="269@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1427.28 878.96 L 1427.28 916.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="248">
   <path class="v6300" d="M 1427.78 765.61 L 1427.78 747.55" stroke-width="1" zvalue="281"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@0" LinkObjectIDznd="251@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1427.78 765.61 L 1427.78 747.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="247">
   <path class="v6300" d="M 1427.78 799.46 L 1427.78 824.29" stroke-width="1" zvalue="283"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@1" LinkObjectIDznd="254" MaxPinNum="2"/>
   </metadata>
  <path d="M 1427.78 799.46 L 1427.78 824.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="285">
   <path class="v6300" d="M 1455.28 630.21 L 1455.28 679.67" stroke-width="1" zvalue="290"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@1" LinkObjectIDznd="161@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455.28 630.21 L 1455.28 679.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="286">
   <path class="v6300" d="M 1455.28 596.36 L 1455.28 556.69" stroke-width="1" zvalue="291"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@0" LinkObjectIDznd="282@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455.28 596.36 L 1455.28 556.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="296">
   <path class="v6300" d="M 1584.03 736.36 L 1584.03 679.67" stroke-width="1" zvalue="298"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="289@0" LinkObjectIDznd="161@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1584.03 736.36 L 1584.03 679.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="297">
   <path class="v6300" d="M 1584.03 770.21 L 1584.03 807.34" stroke-width="1" zvalue="299"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="289@1" LinkObjectIDznd="294@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1584.03 770.21 L 1584.03 807.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="166">
   <g id="1660">
    <use class="kv35" height="60" transform="rotate(0,1037.19,488.482) scale(1.6125,1.54462) translate(-381.72,-155.897)" width="40" x="1004.94" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="442.14" zvalue="44"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874420715524" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1661">
    <use class="v6300" height="60" transform="rotate(0,1037.19,488.482) scale(1.6125,1.54462) translate(-381.72,-155.897)" width="40" x="1004.94" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="442.14" zvalue="44"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874420781060" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399442563076" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399442563076"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1037.19,488.482) scale(1.6125,1.54462) translate(-381.72,-155.897)" width="40" x="1004.94" y="442.14"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v6300" height="30" transform="rotate(0,713.894,880.057) scale(1.85899,1.85899) translate(-316.987,-393.766)" width="30" x="686.009530051656" xlink:href="#Generator:发电机_0" y="852.1716568062666" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449551925254" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449551925254"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,713.894,880.057) scale(1.85899,1.85899) translate(-316.987,-393.766)" width="30" x="686.009530051656" y="852.1716568062666"/></g>
  <g id="276">
   <use class="v6300" height="30" transform="rotate(0,1301.89,880.057) scale(1.85899,1.85899) translate(-588.686,-393.766)" width="30" x="1274.009530051656" xlink:href="#Generator:发电机_0" y="852.1716568062666" zvalue="257"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449553039366" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449553039366"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1301.89,880.057) scale(1.85899,1.85899) translate(-588.686,-393.766)" width="30" x="1274.009530051656" y="852.1716568062666"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="185">
   <use class="v6300" height="40" transform="rotate(0,619.28,936.966) scale(1.59068,-1.15324) translate(-221.102,-1746.36)" width="30" x="595.4193543779249" xlink:href="#Accessory:带熔断器的线路PT1_0" y="913.9007917131696" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449551859718" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,619.28,936.966) scale(1.59068,-1.15324) translate(-221.102,-1746.36)" width="30" x="595.4193543779249" y="913.9007917131696"/></g>
  <g id="81">
   <use class="v6300" height="30" transform="rotate(0,839.682,942.786) scale(1.9,1.9) translate(-384.244,-433.083)" width="30" x="811.1818622622146" xlink:href="#Accessory:带熔断器35kVPT11_0" y="914.2857142857142" zvalue="155"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449551073286" ObjectName="#1发电机仪用PT2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,839.682,942.786) scale(1.9,1.9) translate(-384.244,-433.083)" width="30" x="811.1818622622146" y="914.2857142857142"/></g>
  <g id="204">
   <use class="kv35" height="30" transform="rotate(0,1308.87,114.5) scale(1.9375,-1.93333) translate(-619.261,-159.724)" width="30" x="1279.805494315121" xlink:href="#Accessory:避雷器PT带熔断器_0" y="85.5" zvalue="230"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449552121861" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1308.87,114.5) scale(1.9375,-1.93333) translate(-619.261,-159.724)" width="30" x="1279.805494315121" y="85.5"/></g>
  <g id="207">
   <use class="kv35" height="20" transform="rotate(90,686.812,158.312) scale(-1.84375,1.84375) translate(-1050.88,-64.0106)" width="20" x="668.375" xlink:href="#Accessory:线路PT3_0" y="139.875" zvalue="231"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449552187397" ObjectName="35kV曼悠河一级线PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,686.812,158.312) scale(-1.84375,1.84375) translate(-1050.88,-64.0106)" width="20" x="668.375" y="139.875"/></g>
  <g id="232">
   <use class="v6300" height="40" transform="rotate(0,839.78,726.216) scale(1.59068,1.15324) translate(-302.982,-93.4351)" width="30" x="815.9193543779249" xlink:href="#Accessory:带熔断器的线路PT1_0" y="703.1507917131696" zvalue="251"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449552449541" ObjectName="#1励磁变"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,839.78,726.216) scale(1.59068,1.15324) translate(-302.982,-93.4351)" width="30" x="815.9193543779249" y="703.1507917131696"/></g>
  <g id="275">
   <use class="v6300" height="40" transform="rotate(0,1207.28,936.966) scale(1.59068,-1.15324) translate(-439.449,-1746.36)" width="30" x="1183.419354377925" xlink:href="#Accessory:带熔断器的线路PT1_0" y="913.9007917131696" zvalue="259"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449552973830" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1207.28,936.966) scale(1.59068,-1.15324) translate(-439.449,-1746.36)" width="30" x="1183.419354377925" y="913.9007917131696"/></g>
  <g id="269">
   <use class="v6300" height="30" transform="rotate(0,1427.68,942.786) scale(1.9,1.9) translate(-662.77,-433.083)" width="30" x="1399.181862262215" xlink:href="#Accessory:带熔断器35kVPT11_0" y="914.2857142857142" zvalue="263"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449552908293" ObjectName="#2发电机仪用PT2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1427.68,942.786) scale(1.9,1.9) translate(-662.77,-433.083)" width="30" x="1399.181862262215" y="914.2857142857142"/></g>
  <g id="251">
   <use class="v6300" height="40" transform="rotate(0,1427.78,726.216) scale(1.59068,1.15324) translate(-521.329,-93.4351)" width="30" x="1403.919354377925" xlink:href="#Accessory:带熔断器的线路PT1_0" y="703.1507917131696" zvalue="278"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449552646149" ObjectName="#2励磁变"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1427.78,726.216) scale(1.59068,1.15324) translate(-521.329,-93.4351)" width="30" x="1403.919354377925" y="703.1507917131696"/></g>
  <g id="282">
   <use class="v6300" height="30" transform="rotate(0,1457.48,529.75) scale(1.9375,-1.93333) translate(-691.168,-789.759)" width="30" x="1428.412902918617" xlink:href="#Accessory:避雷器PT带熔断器_0" y="500.75" zvalue="288"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449553170438" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1457.48,529.75) scale(1.9375,-1.93333) translate(-691.168,-789.759)" width="30" x="1428.412902918617" y="500.75"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="178">
   <use class="kv35" height="30" transform="rotate(90,958,354) scale(-1,1) translate(-1916,0)" width="10" x="953" xlink:href="#GroundDisconnector:配网地刀_0" y="339" zvalue="92"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449551597574" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449551597574"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,958,354) scale(-1,1) translate(-1916,0)" width="10" x="953" y="339"/></g>
  <g id="196">
   <use class="kv35" height="30" transform="rotate(270,793,195) scale(1,1) translate(0,0)" width="12" x="787" xlink:href="#GroundDisconnector:地刀12_0" y="180" zvalue="106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449551466502" ObjectName="35kV曼悠河一级线34117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449551466502"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,793,195) scale(1,1) translate(0,0)" width="12" x="787" y="180"/></g>
  <g id="213">
   <use class="kv35" height="30" transform="rotate(270,1367,168.005) scale(1,1) translate(0,0)" width="12" x="1361" xlink:href="#GroundDisconnector:地刀12_0" y="153.0049725128926" zvalue="113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449551335430" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449551335430"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1367,168.005) scale(1,1) translate(0,0)" width="12" x="1361" y="153.0049725128926"/></g>
  <g id="209">
   <use class="kv35" height="30" transform="rotate(270,1374,224) scale(1,1) translate(0,0)" width="12" x="1368" xlink:href="#GroundDisconnector:地刀12_0" y="209" zvalue="115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449551204358" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449551204358"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1374,224) scale(1,1) translate(0,0)" width="12" x="1368" y="209"/></g>
 </g>
 <g id="BreakerClass">
  <g id="237">
   <use class="v6300" height="20" transform="rotate(0,1037.25,577.982) scale(1.74845,1.5895) translate(-440.264,-208.461)" width="10" x="1028.502972664238" xlink:href="#Breaker:开关_0" y="562.087301375374" zvalue="120"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924472930309" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924472930309"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1037.25,577.982) scale(1.74845,1.5895) translate(-440.264,-208.461)" width="10" x="1028.502972664238" y="562.087301375374"/></g>
  <g id="259">
   <use class="v6300" height="20" transform="rotate(0,713.813,768.754) scale(1.22222,1.11111) translate(-128.673,-75.7643)" width="10" x="707.7017906114168" xlink:href="#Breaker:开关_0" y="757.6428569309297" zvalue="137"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924472864773" ObjectName="#1发电机641断路器"/>
   <cge:TPSR_Ref TObjectID="6473924472864773"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,713.813,768.754) scale(1.22222,1.11111) translate(-128.673,-75.7643)" width="10" x="707.7017906114168" y="757.6428569309297"/></g>
  <g id="192">
   <use class="kv35" height="20" transform="rotate(0,1037.25,390.839) scale(1.74845,1.5895) translate(-440.264,-139.055)" width="10" x="1028.502972664238" xlink:href="#Breaker:开关_0" y="374.9444442325169" zvalue="223"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924472995845" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924472995845"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1037.25,390.839) scale(1.74845,1.5895) translate(-440.264,-139.055)" width="10" x="1028.502972664238" y="374.9444442325169"/></g>
  <g id="274">
   <use class="v6300" height="20" transform="rotate(0,1301.81,768.754) scale(1.22222,1.11111) translate(-235.582,-75.7643)" width="10" x="1295.701790611417" xlink:href="#Breaker:开关_0" y="757.6428569309297" zvalue="260"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924473061381" ObjectName="#2发电机642断路器"/>
   <cge:TPSR_Ref TObjectID="6473924473061381"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1301.81,768.754) scale(1.22222,1.11111) translate(-235.582,-75.7643)" width="10" x="1295.701790611417" y="757.6428569309297"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="294">
   <use class="v6300" height="30" transform="rotate(0,1584.03,838) scale(2.16667,2.16667) translate(-841.272,-433.731)" width="20" x="1562.362902918617" xlink:href="#EnergyConsumer:站用变带熔断器DY_0" y="805.5" zvalue="297"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449553301510" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1584.03,838) scale(2.16667,2.16667) translate(-841.272,-433.731)" width="20" x="1562.362902918617" y="805.5"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="3" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,740.561,24.3214) scale(1,1) translate(0,-2.56056e-15)" writing-mode="lr" x="740.09" xml:space="preserve" y="29.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124095524868" ObjectName="P"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="4" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,740.561,40.4881) scale(1,1) translate(2.31668e-13,-5.94057e-15)" writing-mode="lr" x="740.09" xml:space="preserve" y="45.21" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124095590404" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="5" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,740.561,53.877) scale(1,1) translate(2.31668e-13,1.02526e-13)" writing-mode="lr" x="740.09" xml:space="preserve" y="58.57" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124095655940" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="6" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1115.41,363.016) scale(1,1) translate(0,0)" writing-mode="lr" x="1114.86" xml:space="preserve" y="367.72" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124097753092" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="7" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1115.41,389.294) scale(1,1) translate(0,2.08801e-13)" writing-mode="lr" x="1114.86" xml:space="preserve" y="394" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124097818628" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="8" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1114.96,562.41) scale(1,1) translate(0,0)" writing-mode="lr" x="1114.41" xml:space="preserve" y="567.1" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124097884164" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="9" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1114.96,587.577) scale(1,1) translate(0,-1.27784e-13)" writing-mode="lr" x="1114.41" xml:space="preserve" y="592.27" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124097949700" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="10" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1115.41,412.794) scale(1,1) translate(0,1.78538e-13)" writing-mode="lr" x="1114.86" xml:space="preserve" y="417.46" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124098015236" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="11" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1114.96,610.521) scale(1,1) translate(0,-1.33409e-13)" writing-mode="lr" x="1114.41" xml:space="preserve" y="615.1799999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124098342916" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="12" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,709.45,981.748) scale(1,1) translate(-1.45761e-13,1.07562e-13)" writing-mode="lr" x="708.9" xml:space="preserve" y="986.45" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124096573444" ObjectName="P"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="13" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1301.89,980.082) scale(1,1) translate(0,1.8231e-12)" writing-mode="lr" x="1301.34" xml:space="preserve" y="984.8099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124101357572" ObjectName="P"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="14" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,709.45,1002.14) scale(1,1) translate(1.45761e-13,-2.19934e-12)" writing-mode="lr" x="708.9" xml:space="preserve" y="1006.82" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124096638980" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="15" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1301.89,1002.47) scale(1,1) translate(0,-2.19145e-13)" writing-mode="lr" x="1301.34" xml:space="preserve" y="1007.22" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124101423108" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="16" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,709.45,1025.3) scale(1,1) translate(0,0)" writing-mode="lr" x="708.9" xml:space="preserve" y="1030.01" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124096704516" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="17" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1301.89,1023.75) scale(1,1) translate(0,1.11904e-13)" writing-mode="lr" x="1301.34" xml:space="preserve" y="1028.5" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124101488644" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="41">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,156.611,349.917) scale(1,1) translate(0,0)" writing-mode="lr" x="156.77" xml:space="preserve" y="354.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124100177924" ObjectName="F"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="39" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,156.611,277.917) scale(1,1) translate(0,0)" writing-mode="lr" x="156.77" xml:space="preserve" y="282.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124103454724" ObjectName="F"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,334.222,278.917) scale(1,1) translate(0,0)" writing-mode="lr" x="334.38" xml:space="preserve" y="283.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124103520260" ObjectName="F"/>
   </metadata>
  </g>
  <g id="36">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,334.222,350.917) scale(1,1) translate(0,0)" writing-mode="lr" x="334.38" xml:space="preserve" y="355.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124097622022" ObjectName="F"/>
   </metadata>
  </g>
  <g id="109">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="109" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,156.611,302.917) scale(1,1) translate(0,0)" writing-mode="lr" x="156.77" xml:space="preserve" y="307.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124103323652" ObjectName="F"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,334.222,303.917) scale(1,1) translate(0,0)" writing-mode="lr" x="334.38" xml:space="preserve" y="308.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124103389188" ObjectName="F"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="103" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,156.611,395.139) scale(1,1) translate(0,0)" writing-mode="lr" x="156.77" xml:space="preserve" y="400.05" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,334.611,395.139) scale(1,1) translate(0,0)" writing-mode="lr" x="334.77" xml:space="preserve" y="400.05" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,156.611,326.917) scale(1,1) translate(0,0)" writing-mode="lr" x="156.77" xml:space="preserve" y="331.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124103323652" ObjectName="F"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,336.611,325.917) scale(1,1) translate(0,0)" writing-mode="lr" x="336.77" xml:space="preserve" y="330.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127183712261" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,156.611,415.139) scale(1,1) translate(0,0)" writing-mode="lr" x="156.77" xml:space="preserve" y="420.05" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="19" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,334.611,415.139) scale(1,1) translate(0,0)" writing-mode="lr" x="334.77" xml:space="preserve" y="420.05" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="21" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1452.27,390.171) scale(1,1) translate(0,0)" writing-mode="lr" x="1451.8" xml:space="preserve" y="394.95" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124097228804" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="43">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="43" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1205.75,79.6429) scale(1,1) translate(0,0)" writing-mode="lr" x="1205.28" xml:space="preserve" y="84.42" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124099784708" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="57" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1452.27,413.671) scale(1,1) translate(0,1.75935e-13)" writing-mode="lr" x="1451.8" xml:space="preserve" y="418.45" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124097294340" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="58">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="58" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1205.75,104.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1205.28" xml:space="preserve" y="108.92" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124099850244" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="59">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="59" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1452.27,437.171) scale(1,1) translate(0,0)" writing-mode="lr" x="1451.8" xml:space="preserve" y="441.95" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124097359876" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="60">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="60" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1205.75,128.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1205.28" xml:space="preserve" y="133.42" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124099915780" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="61">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="61" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,553.268,710.671) scale(1,1) translate(0,0)" writing-mode="lr" x="552.8" xml:space="preserve" y="715.45" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124097490948" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="62" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,585.861,289.143) scale(1,1) translate(0,-2.41267e-13)" writing-mode="lr" x="585.42" xml:space="preserve" y="293.92" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124100046852" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="127">
   <use height="30" transform="rotate(0,331.673,204.107) scale(0.708333,0.665547) translate(132.196,97.552)" width="30" x="321.05" xlink:href="#State:红绿圆(方形)_0" y="194.12" zvalue="332"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374884601860" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,331.673,204.107) scale(0.708333,0.665547) translate(132.196,97.552)" width="30" x="321.05" y="194.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,236.048,204.107) scale(0.708333,0.665547) translate(92.8211,97.552)" width="30" x="225.42" xlink:href="#State:红绿圆(方形)_0" y="194.12" zvalue="333"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950061490184" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,236.048,204.107) scale(0.708333,0.665547) translate(92.8211,97.552)" width="30" x="225.42" y="194.12"/></g>
 </g>
</svg>