<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549684011009" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.45181617405996" x2="1.714850492606708" y1="5.276370517142858" y2="14.05696281619048"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.583333333333333" x2="8.583333333333334" y1="5.166666666666667" y2="14.16666666666667"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:线路PT11带避雷器_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="23.75" y2="23.75"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,11) scale(1,1) translate(0,0)" width="6" x="17" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="20" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="32" y1="17" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.13040845230975" x2="32.13040845230975" y1="16.76788570496156" y2="23.53635804601289"/>
   <rect fill-opacity="0" height="6.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,32.04,24.56) scale(1,1) translate(0,0)" width="15.34" x="24.37" y="21.53"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.98821300514558" x2="19.98821300514558" y1="1.029523490692871" y2="18.75"/>
   <path d="M 30.0147 23.4481 L 34.2865 23.4481 L 32.115 28.6155 L 30.0147 23.4481" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <ellipse cx="19.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.22179295459144" x2="32.22179295459144" y1="32.22550978083666" y2="36.2625308385742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.01788570496155" x2="35.42570020422134" y1="36.23023467011234" y2="36.23023467011234"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.26384963537316" x2="34.53572596821302" y1="37.52208140858838" y2="37.52208140858838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.15382387138148" x2="33.11176719059974" y1="38.49096646244535" y2="38.49096646244535"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="32.91666666666667" y2="32.91666666666667"/>
  </symbol>
  <symbol id="Accessory:厂用变2020_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <path d="M 13 13.5 L 17 13.5 L 15 10.5 L 13 13.5 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="20.66666666666666" y2="22.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="17.91666666666666" y2="20.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="25.41666666666667" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="20.66666666666666" y2="22.66666666666666"/>
   <ellipse cx="14.95" cy="20.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.95" cy="12.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0833 3.5 L 14.0833 4.5 L 16.0833 4.5 L 15.0833 3.5 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="1" y2="7.333333333333331"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_0" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.05" x2="7.05" y1="1.166666666666664" y2="26.33333333333334"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="10.66666666666667" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="26.5417774506975" y2="23.30486537657277"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="26.5417774506975" y2="23.30486537657277"/>
   <rect fill-opacity="0" height="6.25" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,8.63) scale(1,1) translate(0,0)" width="2.83" x="5.58" y="5.5"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_1" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.05" x2="7.05" y1="2.833333333333334" y2="14.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="2.617622152595477" y2="5.500000000000003"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="10.66666666666666" y1="2.617622152595477" y2="5.500000000000003"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="10.66666666666667" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="24.65357874079139" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="26.5417774506975" y2="23.30486537657277"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="24.65357874079139" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="26.5417774506975" y2="23.30486537657277"/>
   <rect fill-opacity="0" height="6.25" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,8.63) scale(1,1) translate(0,0)" width="2.83" x="5.58" y="5.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="7.049999999999999" y1="16" y2="18.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.133333333333333" x2="7.133333333333333" y1="18.16666666666667" y2="24.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_2" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.33333333333333" x2="3.333333333333333" y1="9.166666666666668" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.666666666666667" x2="10.83333333333333" y1="9.124593892873616" y2="18.41666666666667"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="12.5" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="26.56537356321841" y2="21.7550287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="26.56537356321841" y2="21.7550287356322"/>
  </symbol>
  <symbol id="Accessory:空挂线路_0" viewBox="0,0,11,13">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="-0.0833333333333357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="12.91666666666667" y2="0.2500000000000009"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV支那河一级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="45" xlink:href="logo.png" y="43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,194,73) scale(1,1) translate(0,0)" writing-mode="lr" x="194" xml:space="preserve" y="76.5" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,190.5,72.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="190.5" xml:space="preserve" y="81.69" zvalue="3">110kV支那河一级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="206" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,87.625,196) scale(1,1) translate(0,0)" width="72.88" x="51.19" y="184" zvalue="258"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,87.625,196) scale(1,1) translate(0,0)" writing-mode="lr" x="87.63" xml:space="preserve" y="200.5" zvalue="258">信号一览</text>
  <line fill="none" id="36" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="387" x2="387" y1="11" y2="1041" zvalue="4"/>
  <line fill="none" id="34" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00000000000045" x2="380" y1="146.8704926140824" y2="146.8704926140824" zvalue="6"/>
  <line fill="none" id="32" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00000000000045" x2="380" y1="616.8704926140824" y2="616.8704926140824" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13" x2="103" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13" x2="103" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13" x2="13" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103" x2="103" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103" x2="373" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103" x2="373" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103" x2="103" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="373" x2="373" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13" x2="103" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13" x2="103" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13" x2="13" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103" x2="103" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103" x2="193" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103" x2="193" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103" x2="103" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193" x2="193" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.0000000000001" x2="283.0000000000001" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.0000000000001" x2="283.0000000000001" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.0000000000001" x2="193.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.0000000000001" x2="283.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283" x2="373" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283" x2="373" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283" x2="283" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="373" x2="373" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13" x2="103" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13" x2="103" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13" x2="13" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103" x2="103" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103" x2="193" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103" x2="193" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103" x2="103" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193" x2="193" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.0000000000001" x2="283.0000000000001" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.0000000000001" x2="283.0000000000001" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.0000000000001" x2="193.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.0000000000001" x2="283.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283" x2="373" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283" x2="373" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283" x2="283" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="373" x2="373" y1="999.0816" y2="1027"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58,952) scale(1,1) translate(0,0)" writing-mode="lr" x="58" xml:space="preserve" y="958" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55,986) scale(1,1) translate(0,0)" writing-mode="lr" x="55" xml:space="preserve" y="992" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237,986) scale(1,1) translate(0,0)" writing-mode="lr" x="237" xml:space="preserve" y="992" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="1020" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="1020" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5,646.5) scale(1,1) translate(0,0)" writing-mode="lr" x="78.5" xml:space="preserve" y="651" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1646.75,259.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1646.75" xml:space="preserve" y="263.75" zvalue="41">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1099.53,302.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1099.53" xml:space="preserve" y="306.97" zvalue="43">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1108.86,358.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1108.86" xml:space="preserve" y="362.97" zvalue="45">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1131.19,335.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1131.19" xml:space="preserve" y="339.97" zvalue="48">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1170.81,439.919) scale(1,1) translate(-2.53825e-13,4.71388e-14)" writing-mode="lr" x="1170.81" xml:space="preserve" y="444.42" zvalue="51">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,901.617,487.75) scale(1,1) translate(0,0)" writing-mode="lr" x="901.62" xml:space="preserve" y="492.25" zvalue="56">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1120.9,552.861) scale(1,1) translate(0,0)" writing-mode="lr" x="1120.9" xml:space="preserve" y="557.36" zvalue="59">001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1858.62,606.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1858.63" xml:space="preserve" y="611.25" zvalue="63">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,789.306,328.417) scale(1,1) translate(0,0)" writing-mode="lr" x="789.3099999999999" xml:space="preserve" y="332.92" zvalue="65">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,799.333,396.944) scale(1,1) translate(0,0)" writing-mode="lr" x="799.33" xml:space="preserve" y="401.44" zvalue="67">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,806.833,303.111) scale(1,1) translate(0,0)" writing-mode="lr" x="806.83" xml:space="preserve" y="307.61" zvalue="69">10</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,777.25,487.125) scale(1,1) translate(0,0)" writing-mode="lr" x="777.25" xml:space="preserve" y="491.63" zvalue="77">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1088.34,140.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1088.34" xml:space="preserve" y="145.25" zvalue="78">110kV支那河一二级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,657.31,670.615) scale(1,1) translate(0,0)" writing-mode="lr" x="657.3099999999999" xml:space="preserve" y="675.12" zvalue="81">041</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,635.086,913.455) scale(1,1) translate(0,2.97328e-13)" writing-mode="lr" x="635.0860901404425" xml:space="preserve" y="917.9547398511555" zvalue="85">#1发电机  7MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,538.024,815.281) scale(1,1) translate(-2.31225e-13,-5.36482e-13)" writing-mode="lr" x="538.02" xml:space="preserve" y="819.78" zvalue="88">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,700.713,823.571) scale(1,1) translate(3.03199e-13,-5.42004e-13)" writing-mode="lr" x="700.71" xml:space="preserve" y="828.0700000000001" zvalue="90">0912</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,511.5,925.383) scale(1,1) translate(0,0)" writing-mode="lr" x="511.5" xml:space="preserve" y="929.88" zvalue="96">励磁变PT1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,574.046,927.558) scale(1,1) translate(0,0)" writing-mode="lr" x="574.05" xml:space="preserve" y="932.0599999999999" zvalue="98">励磁PT2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,725,918.875) scale(1,1) translate(0,0)" writing-mode="lr" x="725" xml:space="preserve" y="923.38" zvalue="102">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1434.31,670.615) scale(1,1) translate(0,0)" writing-mode="lr" x="1434.31" xml:space="preserve" y="675.12" zvalue="106">042</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1410.84,914.705) scale(1,1) translate(0,2.97744e-13)" writing-mode="lr" x="1410.836090140443" xml:space="preserve" y="919.2047398511555" zvalue="110">#2发电机  7WM</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1315.02,815.281) scale(1,1) translate(-5.76283e-13,-5.36482e-13)" writing-mode="lr" x="1315.02" xml:space="preserve" y="819.78" zvalue="113">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1477.71,823.571) scale(1,1) translate(1.7827e-12,-5.42004e-13)" writing-mode="lr" x="1477.71" xml:space="preserve" y="828.0700000000001" zvalue="115">0922</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1288.5,925.383) scale(1,1) translate(0,0)" writing-mode="lr" x="1288.5" xml:space="preserve" y="929.88" zvalue="121">励磁变PT1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1351.05,927.558) scale(1,1) translate(0,0)" writing-mode="lr" x="1351.05" xml:space="preserve" y="932.0599999999999" zvalue="123">励磁PT2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1502,935.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1502" xml:space="preserve" y="939.63" zvalue="127">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1072.88,889.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1072.88" xml:space="preserve" y="894" zvalue="131">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1138.69,792.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1138.69" xml:space="preserve" y="797.4400000000001" zvalue="139">04367</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1476.53,506.993) scale(1,1) translate(0,0)" writing-mode="lr" x="1476.53" xml:space="preserve" y="511.49" zvalue="141">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1335,504.493) scale(1,1) translate(0,0)" writing-mode="lr" x="1335" xml:space="preserve" y="508.99" zvalue="143">0801</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1042.12,678.243) scale(1,1) translate(0,0)" writing-mode="lr" x="1042.13" xml:space="preserve" y="682.74" zvalue="153">0431</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1692.88,545.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1692.88" xml:space="preserve" y="550.25" zvalue="158">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1758.69,449.194) scale(1,1) translate(0,0)" writing-mode="lr" x="1758.69" xml:space="preserve" y="453.69" zvalue="160">04467</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1663.38,374.243) scale(1,1) translate(0,0)" writing-mode="lr" x="1663.38" xml:space="preserve" y="378.74" zvalue="163">0441</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,589.125,382) scale(1,1) translate(0,0)" writing-mode="lr" x="589.13" xml:space="preserve" y="386.5" zvalue="167">备用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,646.194,524.194) scale(1,1) translate(0,0)" writing-mode="lr" x="646.1900000000001" xml:space="preserve" y="528.6900000000001" zvalue="169">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="166" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,617.905,549.111) scale(1,1) translate(0,0)" writing-mode="lr" x="617.9" xml:space="preserve" y="553.61" zvalue="176">045</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="170" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1697.59,297.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1697.59" xml:space="preserve" y="302" zvalue="182">10kV施工电源电</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="257.75" y2="257.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="283.75" y2="283.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="14" y1="257.75" y2="283.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="257.75" y2="283.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="257.75" y2="257.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="283.75" y2="283.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="257.75" y2="283.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376" x2="376" y1="257.75" y2="283.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="283.75" y2="283.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="308" y2="308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="14" y1="283.75" y2="308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="283.75" y2="308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="283.75" y2="283.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="308" y2="308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="283.75" y2="308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376" x2="376" y1="283.75" y2="308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="308" y2="308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="330.75" y2="330.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="14" y1="308" y2="330.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="308" y2="330.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="308" y2="308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="330.75" y2="330.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="308" y2="330.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376" x2="376" y1="308" y2="330.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="330.75" y2="330.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="353.5" y2="353.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="14" y1="330.75" y2="353.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="330.75" y2="353.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="330.75" y2="330.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="353.5" y2="353.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="330.75" y2="353.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376" x2="376" y1="330.75" y2="353.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="353.5" y2="353.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="376.25" y2="376.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="14" y1="353.5" y2="376.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="353.5" y2="376.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="353.5" y2="353.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="376.25" y2="376.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="353.5" y2="376.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376" x2="376" y1="353.5" y2="376.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="376.25" y2="376.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="399" y2="399"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="14" y1="376.25" y2="399"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="376.25" y2="399"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="376.25" y2="376.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="399" y2="399"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="376.25" y2="399"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376" x2="376" y1="376.25" y2="399"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="399" y2="399"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="421.75" y2="421.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="14" y1="399" y2="421.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="399" y2="421.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="399" y2="399"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="421.75" y2="421.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="399" y2="421.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376" x2="376" y1="399" y2="421.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="217" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,203.399,196.591) scale(1,1) translate(0,0)" writing-mode="lr" x="203.4" xml:space="preserve" y="201.09" zvalue="245">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="216" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,308.399,196.591) scale(1,1) translate(0,0)" writing-mode="lr" x="308.4" xml:space="preserve" y="201.09" zvalue="246">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="215" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,60.5,270.75) scale(1,1) translate(0,0)" writing-mode="lr" x="18" xml:space="preserve" y="275.25" zvalue="247">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="214" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,241,270.75) scale(1,1) translate(0,0)" writing-mode="lr" x="198.5" xml:space="preserve" y="275.25" zvalue="248">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="213" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.6875,344) scale(1,1) translate(0,0)" writing-mode="lr" x="63.69" xml:space="preserve" y="348.5" zvalue="249">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="212" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241.875,342.75) scale(1,1) translate(0,0)" writing-mode="lr" x="241.88" xml:space="preserve" y="347.25" zvalue="250">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,296.75) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="301.25" zvalue="259">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,296.75) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="301.25" zvalue="260">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.6875,390) scale(1,1) translate(0,0)" writing-mode="lr" x="60.69" xml:space="preserve" y="394.5" zvalue="263">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.688,389) scale(1,1) translate(0,0)" writing-mode="lr" x="228.69" xml:space="preserve" y="393.5" zvalue="265">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,319.75) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="324.25" zvalue="269">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="192" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239.5,318.75) scale(1,1) translate(0,0)" writing-mode="lr" x="197" xml:space="preserve" y="323.25" zvalue="271">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,198.5,951) scale(1,1) translate(0,0)" writing-mode="lr" x="198.5" xml:space="preserve" y="957" zvalue="277">ZNH1-01-2010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,148,988) scale(1,1) translate(0,0)" writing-mode="lr" x="148" xml:space="preserve" y="994" zvalue="279">李宏梅</text>
  <text fill="rgb(255,0,0)" font-family="FangSong" font-size="8" id="16" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,704,116) scale(1,1) translate(0,0)" writing-mode="lr" x="704" xml:space="preserve" y="119" zvalue="292">停运</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="51.19" y="184" zvalue="258"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="39">
   <path class="kv110" d="M 554.75 260.25 L 1604.75 260.25" stroke-width="6" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674420391939" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674420391939"/></metadata>
  <path d="M 554.75 260.25 L 1604.75 260.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv10" d="M 476 607.75 L 1821 607.75" stroke-width="6" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674420457475" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674420457475"/></metadata>
  <path d="M 476 607.75 L 1821 607.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="112">
   <use class="kv110" height="30" transform="rotate(0,1088.08,303.472) scale(1.11111,0.814815) translate(-107.975,66.1932)" width="15" x="1079.750010172526" xlink:href="#Disconnector:刀闸_0" y="291.25" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454760267778" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454760267778"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1088.08,303.472) scale(1.11111,0.814815) translate(-107.975,66.1932)" width="15" x="1079.750010172526" y="291.25"/></g>
  <g id="89">
   <use class="kv110" height="30" transform="rotate(0,763.972,329.417) scale(-1.11111,-0.814815) translate(-1450.71,-736.479)" width="15" x="755.6388888888888" xlink:href="#Disconnector:刀闸_0" y="317.1944581137762" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454760595458" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454760595458"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,763.972,329.417) scale(-1.11111,-0.814815) translate(-1450.71,-736.479)" width="15" x="755.6388888888888" y="317.1944581137762"/></g>
  <g id="132">
   <use class="kv10" height="27" transform="rotate(0,510.759,816.52) scale(1.06228,1.19343) translate(-29.5092,-129.727)" width="14" x="503.3225404732252" xlink:href="#Disconnector:带融断手车刀闸_0" y="800.4088615061935" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454761054210" ObjectName="#1发电机0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454761054210"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,510.759,816.52) scale(1.06228,1.19343) translate(-29.5092,-129.727)" width="14" x="503.3225404732252" y="800.4088615061935"/></g>
  <g id="129">
   <use class="kv10" height="27" transform="rotate(0,726.681,823.905) scale(1.06228,1.19343) translate(-42.1684,-130.924)" width="14" x="719.2454530434896" xlink:href="#Disconnector:带融断手车刀闸_0" y="807.794054079156" zvalue="89"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454760988674" ObjectName="#1发电机0912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454760988674"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,726.681,823.905) scale(1.06228,1.19343) translate(-42.1684,-130.924)" width="14" x="719.2454530434896" y="807.794054079156"/></g>
  <g id="116">
   <use class="kv10" height="27" transform="rotate(0,1287.76,816.52) scale(1.06228,1.19343) translate(-75.0637,-129.727)" width="14" x="1280.322540473225" xlink:href="#Disconnector:带融断手车刀闸_0" y="800.4088615061935" zvalue="111"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454761578498" ObjectName="#2发电机0921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454761578498"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1287.76,816.52) scale(1.06228,1.19343) translate(-75.0637,-129.727)" width="14" x="1280.322540473225" y="800.4088615061935"/></g>
  <g id="113">
   <use class="kv10" height="27" transform="rotate(0,1503.68,823.905) scale(1.06228,1.19343) translate(-87.723,-130.924)" width="14" x="1496.24545304349" xlink:href="#Disconnector:带融断手车刀闸_0" y="807.794054079156" zvalue="114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454761512962" ObjectName="#2发电机0922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454761512962"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1503.68,823.905) scale(1.06228,1.19343) translate(-87.723,-130.924)" width="14" x="1496.24545304349" y="807.794054079156"/></g>
  <g id="130">
   <use class="kv10" height="27" transform="rotate(0,1438.53,505.493) scale(2.10268,2.01903) translate(-746.67,-241.372)" width="14" x="1423.8125" xlink:href="#Disconnector:带融断手车刀闸_0" y="478.2361111111111" zvalue="140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454761906178" ObjectName="10kV母线0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454761906178"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1438.53,505.493) scale(2.10268,2.01903) translate(-746.67,-241.372)" width="14" x="1423.8125" y="478.2361111111111"/></g>
  <g id="133">
   <use class="kv10" height="27" transform="rotate(0,1369.84,505.493) scale(2.10268,2.01903) translate(-710.649,-241.372)" width="14" x="1355.125" xlink:href="#Disconnector:带融断手车刀闸_0" y="478.2361111111111" zvalue="142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454761971714" ObjectName="10kV母线0801隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454761971714"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1369.84,505.493) scale(2.10268,2.01903) translate(-710.649,-241.372)" width="14" x="1355.125" y="478.2361111111111"/></g>
  <g id="141">
   <use class="kv10" height="27" transform="rotate(0,1076.34,679.243) scale(2.10268,2.01903) translate(-556.733,-329.066)" width="14" x="1061.625" xlink:href="#Disconnector:带融断手车刀闸_0" y="651.9861111111111" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454762168322" ObjectName="#1站用变0431隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454762168322"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1076.34,679.243) scale(2.10268,2.01903) translate(-556.733,-329.066)" width="14" x="1061.625" y="651.9861111111111"/></g>
  <g id="150">
   <use class="kv10" height="27" transform="rotate(0,1697.59,375.243) scale(2.10268,2.01903) translate(-882.527,-175.633)" width="14" x="1682.875" xlink:href="#Disconnector:带融断手车刀闸_0" y="347.986111111111" zvalue="162"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454762233858" ObjectName="#2站用变0441隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454762233858"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1697.59,375.243) scale(2.10268,2.01903) translate(-882.527,-175.633)" width="14" x="1682.875" y="347.986111111111"/></g>
 </g>
 <g id="BreakerClass">
  <g id="111">
   <use class="kv110" height="20" transform="rotate(0,1088.08,359.472) scale(1.22222,1.11111) translate(-196.722,-34.8361)" width="10" x="1081.972232407994" xlink:href="#Breaker:开关_0" y="348.3611111111111" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925212831747" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925212831747"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1088.08,359.472) scale(1.22222,1.11111) translate(-196.722,-34.8361)" width="10" x="1081.972232407994" y="348.3611111111111"/></g>
  <g id="191">
   <use class="kv10" height="20" transform="rotate(0,1087.78,553.861) scale(2,2) translate(-538.89,-266.931)" width="10" x="1077.779569585283" xlink:href="#Breaker:手车开关_0" y="533.8611128065321" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925230264323" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925230264323"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1087.78,553.861) scale(2,2) translate(-538.89,-266.931)" width="10" x="1077.779569585283" y="533.8611128065321"/></g>
  <g id="341">
   <use class="kv10" height="20" transform="rotate(0,630.297,671.696) scale(2.16108,2.16108) translate(-332.834,-349.271)" width="10" x="619.4912202553933" xlink:href="#Breaker:手车开关_0" y="650.084974364315" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925212897283" ObjectName="#1发电机041断路器"/>
   <cge:TPSR_Ref TObjectID="6473925212897283"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,630.297,671.696) scale(2.16108,2.16108) translate(-332.834,-349.271)" width="10" x="619.4912202553933" y="650.084974364315"/></g>
  <g id="120">
   <use class="kv10" height="20" transform="rotate(0,1407.3,671.696) scale(2.16108,2.16108) translate(-750.292,-349.271)" width="10" x="1396.491220255393" xlink:href="#Breaker:手车开关_0" y="650.084974364315" zvalue="105"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925230329859" ObjectName="#2发电机042断路器"/>
   <cge:TPSR_Ref TObjectID="6473925230329859"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1407.3,671.696) scale(2.16108,2.16108) translate(-750.292,-349.271)" width="10" x="1396.491220255393" y="650.084974364315"/></g>
  <g id="162">
   <use class="kv10" height="20" transform="rotate(0,591.53,550.111) scale(2,2) translate(-290.765,-265.056)" width="10" x="581.5295695852833" xlink:href="#Breaker:手车开关_0" y="530.1111128065321" zvalue="175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925213028355" ObjectName="#1备用045断路器"/>
   <cge:TPSR_Ref TObjectID="6473925213028355"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,591.53,550.111) scale(2,2) translate(-290.765,-265.056)" width="10" x="581.5295695852833" y="530.1111128065321"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="54">
   <path class="kv110" d="M 1088.15 315.49 L 1088.15 348.84" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@1" LinkObjectIDznd="111@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1088.15 315.49 L 1088.15 348.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv110" d="M 1088.18 291.65 L 1088.18 260.25" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="39@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1088.18 291.65 L 1088.18 260.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv110" d="M 1088.16 370.08 L 1088.16 412.09" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@1" LinkObjectIDznd="108@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1088.16 370.08 L 1088.16 412.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv110" d="M 1120.36 323.31 L 1088.15 323.31" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="54" MaxPinNum="2"/>
   </metadata>
  <path d="M 1120.36 323.31 L 1088.15 323.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv110" d="M 1086.29 438.37 L 941.45 438.37 L 941.45 466.15" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@2" LinkObjectIDznd="230@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086.29 438.37 L 941.45 438.37 L 941.45 466.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv10" d="M 1086.25 503.27 L 1086.25 535.36" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@1" LinkObjectIDznd="191@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086.25 503.27 L 1086.25 535.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv10" d="M 1087.78 571.86 L 1087.78 607.75" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@1" LinkObjectIDznd="55@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1087.78 571.86 L 1087.78 607.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv110" d="M 763.9 317.4 L 763.9 260.25" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@1" LinkObjectIDznd="39@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 763.9 317.4 L 763.9 260.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv110" d="M 796 289.61 L 763.9 289.61" stroke-width="1" zvalue="71"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="64" MaxPinNum="2"/>
   </metadata>
  <path d="M 796 289.61 L 763.9 289.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv110" d="M 788.5 380.78 L 763.87 380.78 L 763.87 341.23" stroke-width="1" zvalue="72"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="89@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 788.5 380.78 L 763.87 380.78 L 763.87 341.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv110" d="M 762.25 416.9 L 762.25 380.78" stroke-width="1" zvalue="74"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="62" MaxPinNum="2"/>
   </metadata>
  <path d="M 762.25 416.9 L 762.25 380.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv110" d="M 1088.18 204.31 L 1088.18 260.25" stroke-width="1" zvalue="78"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="39@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1088.18 204.31 L 1088.18 260.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv10" d="M 630.3 651.71 L 630.3 607.75" stroke-width="1" zvalue="82"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@0" LinkObjectIDznd="55@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 630.3 651.71 L 630.3 607.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv10" d="M 630.3 828.72 L 630.3 691.15" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="341@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 630.3 828.72 L 630.3 691.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv10" d="M 510.76 801.3 L 510.76 783.21 L 630.3 783.21" stroke-width="1" zvalue="87"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="82" MaxPinNum="2"/>
   </metadata>
  <path d="M 510.76 801.3 L 510.76 783.21 L 630.3 783.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv10" d="M 726.68 839.42 L 726.68 854.19" stroke-width="1" zvalue="91"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@1" LinkObjectIDznd="84@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 726.68 839.42 L 726.68 854.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv10" d="M 679.67 732.49 L 630.3 732.49" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="82" MaxPinNum="2"/>
   </metadata>
  <path d="M 679.67 732.49 L 630.3 732.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 510.25 873.07 L 510.25 832.03" stroke-width="1" zvalue="100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@0" LinkObjectIDznd="132@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 510.25 873.07 L 510.25 832.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv10" d="M 574.05 869.67 L 574.05 783.21" stroke-width="1" zvalue="102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189@0" LinkObjectIDznd="81" MaxPinNum="2"/>
   </metadata>
  <path d="M 574.05 869.67 L 574.05 783.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv10" d="M 630.3 785.25 L 726.68 785.25 L 726.68 808.69" stroke-width="1" zvalue="103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82" LinkObjectIDznd="129@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 630.3 785.25 L 726.68 785.25 L 726.68 808.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv10" d="M 1406.05 829.97 L 1406.05 691.15" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="120@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1406.05 829.97 L 1406.05 691.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv10" d="M 1287.76 801.3 L 1287.76 783.21 L 1406.05 783.21" stroke-width="1" zvalue="112"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="118" MaxPinNum="2"/>
   </metadata>
  <path d="M 1287.76 801.3 L 1287.76 783.21 L 1406.05 783.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 1503.68 839.42 L 1503.68 870.44" stroke-width="1" zvalue="116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@1" LinkObjectIDznd="102@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1503.68 839.42 L 1503.68 870.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv10" d="M 1456.67 732.49 L 1406.05 732.49" stroke-width="1" zvalue="119"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="118" MaxPinNum="2"/>
   </metadata>
  <path d="M 1456.67 732.49 L 1406.05 732.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv10" d="M 1287.25 873.07 L 1287.25 832.03" stroke-width="1" zvalue="124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="116@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1287.25 873.07 L 1287.25 832.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv10" d="M 1351.05 869.67 L 1351.05 783.21" stroke-width="1" zvalue="126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="115" MaxPinNum="2"/>
   </metadata>
  <path d="M 1351.05 869.67 L 1351.05 783.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv10" d="M 1406.05 785.25 L 1503.68 785.25 L 1503.68 808.69" stroke-width="1" zvalue="128"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118" LinkObjectIDznd="113@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1406.05 785.25 L 1503.68 785.25 L 1503.68 808.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv10" d="M 1126.61 772.06 L 1076.91 772.06" stroke-width="1" zvalue="139"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="143" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.61 772.06 L 1076.91 772.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv10" d="M 1369.84 531.74 L 1369.84 564 L 1407.3 564 L 1407.3 607.75" stroke-width="1" zvalue="143"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@1" LinkObjectIDznd="55@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1369.84 531.74 L 1369.84 564 L 1407.3 564 L 1407.3 607.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv10" d="M 1438.53 531.74 L 1438.53 564 L 1409.5 564" stroke-width="1" zvalue="144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@1" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 1438.53 531.74 L 1438.53 564 L 1409.5 564" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="kv10" d="M 1437.94 439.06 L 1438.53 479.75" stroke-width="1" zvalue="147"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="130@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1437.94 439.06 L 1438.53 479.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="kv10" d="M 1370.5 430.39 L 1369.84 479.75" stroke-width="1" zvalue="150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@0" LinkObjectIDznd="133@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1370.5 430.39 L 1369.84 479.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv10" d="M 1076.91 820.89 L 1076.91 705.49" stroke-width="1" zvalue="154"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="141@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1076.91 820.89 L 1076.91 705.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv10" d="M 1076.34 653.5 L 1076.34 607.75" stroke-width="1" zvalue="155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@0" LinkObjectIDznd="55@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1076.34 653.5 L 1076.34 607.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv10" d="M 591.91 455.11 L 591.91 531.61" stroke-width="1" zvalue="176"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="162@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 591.91 455.11 L 591.91 531.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv10" d="M 591.53 568.11 L 591.53 607.75" stroke-width="1" zvalue="177"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162@1" LinkObjectIDznd="55@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 591.53 568.11 L 591.53 607.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="kv10" d="M 634.11 503.31 L 591.91 503.31" stroke-width="1" zvalue="178"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160@0" LinkObjectIDznd="163" MaxPinNum="2"/>
   </metadata>
  <path d="M 634.11 503.31 L 591.91 503.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv10" d="M 1407.3 651.71 L 1407.3 607.75" stroke-width="1" zvalue="179"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="55@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1407.3 651.71 L 1407.3 607.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv10" d="M 1696.34 324.23 L 1696.34 349.5" stroke-width="1" zvalue="183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@0" LinkObjectIDznd="150@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1696.34 324.23 L 1696.34 349.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv10" d="M 1697.59 401.49 L 1697.59 477.14" stroke-width="1" zvalue="184"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@1" LinkObjectIDznd="153@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1697.59 401.49 L 1697.59 477.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="kv10" d="M 1746.61 428.31 L 1697.59 428.31" stroke-width="1" zvalue="185"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="173" MaxPinNum="2"/>
   </metadata>
  <path d="M 1746.61 428.31 L 1697.59 428.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="114">
   <use class="kv110" height="20" transform="rotate(270,1131.19,323.25) scale(-1.11111,1.11111) translate(-2148.71,-31.2139)" width="10" x="1125.638899061415" xlink:href="#GroundDisconnector:地刀_0" y="312.1388888888889" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454760202242" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454760202242"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1131.19,323.25) scale(-1.11111,1.11111) translate(-2148.71,-31.2139)" width="10" x="1125.638899061415" y="312.1388888888889"/></g>
  <g id="230">
   <use class="kv110" height="40" transform="rotate(0,938.809,481.636) scale(1.01543,-1.26928) translate(-13.9541,-855.705)" width="40" x="918.5" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="456.25" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454760071170" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454760071170"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,938.809,481.636) scale(1.01543,-1.26928) translate(-13.9541,-855.705)" width="40" x="918.5" y="456.25"/></g>
  <g id="91">
   <use class="kv110" height="20" transform="rotate(270,799.333,380.722) scale(-1.11111,1.11111) translate(-1518.18,-36.9611)" width="10" x="793.7777845594618" xlink:href="#GroundDisconnector:地刀_0" y="369.6111008326212" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454760529922" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454760529922"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,799.333,380.722) scale(-1.11111,1.11111) translate(-1518.18,-36.9611)" width="10" x="793.7777845594618" y="369.6111008326212"/></g>
  <g id="80">
   <use class="kv110" height="20" transform="rotate(270,806.833,289.556) scale(-1.11111,1.11111) translate(-1532.43,-27.8444)" width="10" x="801.2777845594618" xlink:href="#GroundDisconnector:地刀_0" y="278.4444444444444" zvalue="68"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454760398850" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454760398850"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,806.833,289.556) scale(-1.11111,1.11111) translate(-1532.43,-27.8444)" width="10" x="801.2777845594618" y="278.4444444444444"/></g>
  <g id="126">
   <use class="kv10" height="20" transform="rotate(270,1137.44,772) scale(-1.11111,1.11111) translate(-2160.59,-76.0889)" width="10" x="1131.888899061415" xlink:href="#GroundDisconnector:地刀_0" y="760.8888888888889" zvalue="138"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454761840642" ObjectName="#1站用变04367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454761840642"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1137.44,772) scale(-1.11111,1.11111) translate(-2160.59,-76.0889)" width="10" x="1131.888899061415" y="760.8888888888889"/></g>
  <g id="152">
   <use class="kv10" height="20" transform="rotate(270,1757.44,428.25) scale(-1.11111,1.11111) translate(-3338.59,-41.7139)" width="10" x="1751.888899061415" xlink:href="#GroundDisconnector:地刀_0" y="417.1388888888889" zvalue="159"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454762364930" ObjectName="#2站用变04467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454762364930"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1757.44,428.25) scale(-1.11111,1.11111) translate(-3338.59,-41.7139)" width="10" x="1751.888899061415" y="417.1388888888889"/></g>
  <g id="160">
   <use class="kv10" height="20" transform="rotate(270,644.944,503.25) scale(-1.11111,1.11111) translate(-1224.84,-49.2139)" width="10" x="639.388899061415" xlink:href="#GroundDisconnector:地刀_0" y="492.1388888888889" zvalue="168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454762561538" ObjectName="#1备用04567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454762561538"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,644.944,503.25) scale(-1.11111,1.11111) translate(-1224.84,-49.2139)" width="10" x="639.388899061415" y="492.1388888888889"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="108">
   <g id="1080">
    <use class="kv110" height="50" transform="rotate(0,1086.25,457.589) scale(2.15,1.85355) translate(-563.767,-189.378)" width="30" x="1054" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="411.25" zvalue="50"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874590912514" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1081">
    <use class="kv10" height="50" transform="rotate(0,1086.25,457.589) scale(2.15,1.85355) translate(-563.767,-189.378)" width="30" x="1054" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="411.25" zvalue="50"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874590978050" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399532740610" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399532740610"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1086.25,457.589) scale(2.15,1.85355) translate(-563.767,-189.378)" width="30" x="1054" y="411.25"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="65">
   <use class="kv110" height="40" transform="rotate(0,762.25,440.75) scale(1.25,1.25) translate(-147.45,-83.15)" width="40" x="737.25" xlink:href="#Accessory:线路PT11带避雷器_0" y="415.75" zvalue="76"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454760660994" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,762.25,440.75) scale(1.25,1.25) translate(-147.45,-83.15)" width="40" x="737.25" y="415.75"/></g>
  <g id="181">
   <use class="kv10" height="26" transform="rotate(270,693.037,732.526) scale(1.08054,1.08054) translate(-51.1748,-53.5545)" width="12" x="686.5535673143577" xlink:href="#Accessory:避雷器1_0" y="718.4786682615629" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454760923138" ObjectName="避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,693.037,732.526) scale(1.08054,1.08054) translate(-51.1748,-53.5545)" width="12" x="686.5535673143577" y="718.4786682615629"/></g>
  <g id="185">
   <use class="kv10" height="29" transform="rotate(0,510.25,890.883) scale(1.25,-1.25) translate(-98.3,-1599.96)" width="30" x="491.5" xlink:href="#Accessory:PT12321_0" y="872.7579345703125" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454760857602" ObjectName="#1发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,510.25,890.883) scale(1.25,-1.25) translate(-98.3,-1599.96)" width="30" x="491.5" y="872.7579345703125"/></g>
  <g id="189">
   <use class="kv10" height="29" transform="rotate(0,574.046,891.487) scale(1.53077,-1.53077) translate(-191.079,-1466.17)" width="30" x="551.0840697067018" xlink:href="#Accessory:厂用变2020_0" y="869.2905701754385" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454760792066" ObjectName="#1发电机励磁PT2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,574.046,891.487) scale(1.53077,-1.53077) translate(-191.079,-1466.17)" width="30" x="551.0840697067018" y="869.2905701754385"/></g>
  <g id="84">
   <use class="kv10" height="30" transform="rotate(0,726.25,872.5) scale(1.25,1.25) translate(-141.5,-170.75)" width="30" x="707.5" xlink:href="#Accessory:PT789_0" y="853.75" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454761185282" ObjectName="#1机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,726.25,872.5) scale(1.25,1.25) translate(-141.5,-170.75)" width="30" x="707.5" y="853.75"/></g>
  <g id="107">
   <use class="kv10" height="26" transform="rotate(270,1470.04,732.526) scale(1.08054,1.08054) translate(-109.091,-53.5545)" width="12" x="1463.553567314358" xlink:href="#Accessory:避雷器1_0" y="718.4786682615629" zvalue="118"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454761447426" ObjectName="避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1470.04,732.526) scale(1.08054,1.08054) translate(-109.091,-53.5545)" width="12" x="1463.553567314358" y="718.4786682615629"/></g>
  <g id="105">
   <use class="kv10" height="29" transform="rotate(0,1287.25,890.883) scale(1.25,-1.25) translate(-253.7,-1599.96)" width="30" x="1268.5" xlink:href="#Accessory:PT12321_0" y="872.7579345703125" zvalue="120"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454761381890" ObjectName="#2发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1287.25,890.883) scale(1.25,-1.25) translate(-253.7,-1599.96)" width="30" x="1268.5" y="872.7579345703125"/></g>
  <g id="104">
   <use class="kv10" height="29" transform="rotate(0,1351.05,891.487) scale(1.53077,-1.53077) translate(-460.491,-1466.17)" width="30" x="1328.084069706702" xlink:href="#Accessory:厂用变2020_0" y="869.2905701754385" zvalue="122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454761316354" ObjectName="#2发电机励磁PT2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1351.05,891.487) scale(1.53077,-1.53077) translate(-460.491,-1466.17)" width="30" x="1328.084069706702" y="869.2905701754385"/></g>
  <g id="102">
   <use class="kv10" height="30" transform="rotate(0,1503.25,888.75) scale(1.25,1.25) translate(-296.9,-174)" width="30" x="1484.5" xlink:href="#Accessory:PT789_0" y="870" zvalue="125"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454761250818" ObjectName="#2机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1503.25,888.75) scale(1.25,1.25) translate(-296.9,-174)" width="30" x="1484.5" y="870"/></g>
  <g id="137">
   <use class="kv10" height="30" transform="rotate(0,1438,420.75) scale(1.25,-1.25) translate(-283.85,-753.6)" width="30" x="1419.25" xlink:href="#Accessory:PT789_0" y="402" zvalue="146"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454762037250" ObjectName="10kV机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1438,420.75) scale(1.25,-1.25) translate(-283.85,-753.6)" width="30" x="1419.25" y="402"/></g>
  <g id="139">
   <use class="kv10" height="26" transform="rotate(180,1370.54,417.026) scale(1.08054,1.08054) translate(-101.675,-30.0375)" width="12" x="1364.053567314358" xlink:href="#Accessory:避雷器1_0" y="402.9786682615629" zvalue="149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454762102786" ObjectName="避雷器3"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1370.54,417.026) scale(1.08054,1.08054) translate(-101.675,-30.0375)" width="12" x="1364.053567314358" y="402.9786682615629"/></g>
  <g id="169">
   <use class="kv10" height="13" transform="rotate(0,1696.34,316) scale(1.25,-1.25) translate(-337.894,-567.175)" width="11" x="1689.46875" xlink:href="#Accessory:空挂线路_0" y="307.875" zvalue="181"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454762692610" ObjectName="10kV施工电源电"/>
   </metadata>
  <rect fill="white" height="13" opacity="0" stroke="white" transform="rotate(0,1696.34,316) scale(1.25,-1.25) translate(-337.894,-567.175)" width="11" x="1689.46875" y="307.875"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="kv10" height="30" transform="rotate(0,630.297,856.136) scale(1.85899,1.85899) translate(-278.359,-382.713)" width="30" x="602.4117897505679" xlink:href="#Generator:发电机_0" y="828.2510218856319" zvalue="84"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454761119746" ObjectName="#1发电机  7MW"/>
   <cge:TPSR_Ref TObjectID="6192454761119746"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,630.297,856.136) scale(1.85899,1.85899) translate(-278.359,-382.713)" width="30" x="602.4117897505679" y="828.2510218856319"/></g>
  <g id="117">
   <use class="kv10" height="30" transform="rotate(0,1406.05,857.386) scale(1.85899,1.85899) translate(-636.812,-383.29)" width="30" x="1378.161789750568" xlink:href="#Generator:发电机_0" y="829.5010218856319" zvalue="109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454761644034" ObjectName="#2发电机  7WM"/>
   <cge:TPSR_Ref TObjectID="6192454761644034"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1406.05,857.386) scale(1.85899,1.85899) translate(-636.812,-383.29)" width="30" x="1378.161789750568" y="829.5010218856319"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="205">
   <use class="kv10" height="30" transform="rotate(0,1076.75,845.5) scale(1.69643,1.70833) translate(-432.284,-339.948)" width="28" x="1053" xlink:href="#EnergyConsumer:站用变DY接地_0" y="819.875" zvalue="130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454761709570" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1076.75,845.5) scale(1.69643,1.70833) translate(-432.284,-339.948)" width="28" x="1053" y="819.875"/></g>
  <g id="153">
   <use class="kv10" height="30" transform="rotate(0,1696.75,501.75) scale(1.69643,1.70833) translate(-686.811,-197.418)" width="28" x="1673" xlink:href="#EnergyConsumer:站用变DY接地_0" y="476.125" zvalue="157"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454762430466" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1696.75,501.75) scale(1.69643,1.70833) translate(-686.811,-197.418)" width="28" x="1673" y="476.125"/></g>
  <g id="161">
   <use class="kv10" height="30" transform="rotate(0,591.75,430.5) scale(1.69643,-1.70833) translate(-233.179,-671.875)" width="28" x="568" xlink:href="#EnergyConsumer:站用变DY接地_0" y="404.875" zvalue="166"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454762627074" ObjectName="备用"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,591.75,430.5) scale(1.69643,-1.70833) translate(-233.179,-671.875)" width="28" x="568" y="404.875"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="11" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1085.18,65.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1084.71" xml:space="preserve" y="69.90000000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136303767554" ObjectName="P"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="12" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1085.18,87.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1084.71" xml:space="preserve" y="92.40000000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136303833090" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="13" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1085.18,110.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1084.71" xml:space="preserve" y="114.9" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136303898626" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="17" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1010.25,315.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1009.7" xml:space="preserve" y="320.53" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136300687362" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="23" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1010.25,342.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1009.7" xml:space="preserve" y="347.53" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136300752898" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="30" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1014.25,527.427) scale(1,1) translate(0,0)" writing-mode="lr" x="1013.7" xml:space="preserve" y="532.21" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136300818434" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="76">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="76" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1014.25,550.427) scale(1,1) translate(0,0)" writing-mode="lr" x="1013.7" xml:space="preserve" y="555.21" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136300883970" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="121">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="121" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1010.25,369.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1009.7" xml:space="preserve" y="374.53" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136300949506" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="123">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="123" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1014.25,573.427) scale(1,1) translate(0,0)" writing-mode="lr" x="1013.7" xml:space="preserve" y="578.21" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136301277186" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="124">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="124" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,630.297,963.521) scale(1,1) translate(0,0)" writing-mode="lr" x="629.74" xml:space="preserve" y="968.3" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136304816130" ObjectName="P"/>
   </metadata>
  </g>
  <g id="125">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="125" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1406.05,968.771) scale(1,1) translate(0,0)" writing-mode="lr" x="1405.49" xml:space="preserve" y="973.55" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136305995778" ObjectName="P"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="142" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,630.297,990.521) scale(1,1) translate(0,0)" writing-mode="lr" x="629.74" xml:space="preserve" y="995.3" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136304881666" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="149">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="149" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1406.05,995.771) scale(1,1) translate(0,0)" writing-mode="lr" x="1405.49" xml:space="preserve" y="1000.55" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136306061314" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="151">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="151" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,630.297,1017.52) scale(1,1) translate(0,0)" writing-mode="lr" x="629.74" xml:space="preserve" y="1022.3" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136304947202" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="154">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="154" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1406.05,1022.77) scale(1,1) translate(0,-1.5625e-12)" writing-mode="lr" x="1405.49" xml:space="preserve" y="1027.55" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136306126850" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="210">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="210" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,160.611,342.917) scale(1,1) translate(0,0)" writing-mode="lr" x="160.77" xml:space="preserve" y="347.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136300032002" ObjectName="F"/>
   </metadata>
  </g>
  <g id="209">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="209" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,160.611,270.917) scale(1,1) translate(0,0)" writing-mode="lr" x="160.77" xml:space="preserve" y="275.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136352133122" ObjectName="F"/>
   </metadata>
  </g>
  <g id="208">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="208" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,338.222,271.917) scale(1,1) translate(0,0)" writing-mode="lr" x="338.38" xml:space="preserve" y="276.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136352198658" ObjectName="F"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="246" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,338.222,342.917) scale(1,1) translate(0,0)" writing-mode="lr" x="338.38" xml:space="preserve" y="347.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136303636482" ObjectName="F"/>
   </metadata>
  </g>
  <g id="202">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="202" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,160.611,295.917) scale(1,1) translate(0,0)" writing-mode="lr" x="160.77" xml:space="preserve" y="300.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136352002050" ObjectName="F"/>
   </metadata>
  </g>
  <g id="201">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="201" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,338.222,296.917) scale(1,1) translate(0,0)" writing-mode="lr" x="338.38" xml:space="preserve" y="301.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136352067586" ObjectName="F"/>
   </metadata>
  </g>
  <g id="193">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="193" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,160.611,319.917) scale(1,1) translate(0,0)" writing-mode="lr" x="160.77" xml:space="preserve" y="324.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136352002050" ObjectName="F"/>
   </metadata>
  </g>
  <g id="190">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="190" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,340.611,318.917) scale(1,1) translate(0,0)" writing-mode="lr" x="340.77" xml:space="preserve" y="323.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136352002050" ObjectName="F"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,777.75,510.75) scale(1,1) translate(0,0)" writing-mode="lr" x="777.28" xml:space="preserve" y="515.53" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136299638786" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1410,327.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1409.53" xml:space="preserve" y="332.03" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136303243266" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="5" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,777.75,539.75) scale(1,1) translate(0,0)" writing-mode="lr" x="777.28" xml:space="preserve" y="544.53" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136299704322" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="6" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1410,352.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1409.53" xml:space="preserve" y="357.03" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136303308802" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="7" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,777.75,568.75) scale(1,1) translate(0,0)" writing-mode="lr" x="777.28" xml:space="preserve" y="573.53" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136299769858" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="8" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1410,377.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1409.53" xml:space="preserve" y="382.03" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136303374338" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="9" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,585.75,285.25) scale(1,1) translate(0,0)" writing-mode="lr" x="585.28" xml:space="preserve" y="290.03" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136299900930" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="10" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,504,636.75) scale(1,1) translate(0,0)" writing-mode="lr" x="503.53" xml:space="preserve" y="641.53" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136303505410" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,149.611,387.5) scale(1,1) translate(0,0)" writing-mode="lr" x="149.77" xml:space="preserve" y="392.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123870081028" ObjectName="一级大坝水位"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,321.611,387.5) scale(1,1) translate(0,0)" writing-mode="lr" x="321.77" xml:space="preserve" y="392.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123869884420" ObjectName="一级大坝雨量"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="207">
   <use height="30" transform="rotate(0,335.673,197.107) scale(0.708333,0.665547) translate(133.843,94.0343)" width="30" x="325.05" xlink:href="#State:红绿圆(方形)_0" y="187.12" zvalue="256"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374928838657" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,335.673,197.107) scale(0.708333,0.665547) translate(133.843,94.0343)" width="30" x="325.05" y="187.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,240.048,197.107) scale(0.708333,0.665547) translate(94.4681,94.0343)" width="30" x="229.42" xlink:href="#State:红绿圆(方形)_0" y="187.12" zvalue="257"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562962231197697" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,240.048,197.107) scale(0.708333,0.665547) translate(94.4681,94.0343)" width="30" x="229.42" y="187.12"/></g>
 </g>
</svg>