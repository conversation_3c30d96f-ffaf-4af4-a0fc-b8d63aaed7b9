<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549586493442" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-Y_0" viewBox="0,0,30,50">
   <ellipse cx="14.99" cy="14.99" fill-opacity="0" rx="14.76" ry="14.76" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.99138374485597" x2="10.08333333333333" y1="11.06149193548387" y2="16.08333333333334"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01984739368999" x2="20" y1="11.06354954865259" y2="16"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00235768175583" x2="15.00235768175583" y1="6.416666666666664" y2="11.06149193548386"/>
   <use terminal-index="0" type="1" x="14.99138374485597" xlink:href="#terminal" y="0.2276829173857209"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-Y_1" viewBox="0,0,30,50">
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00235768175583" x2="15.00235768175583" y1="32" y2="37.04670698924731"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00546553497943" x2="20" y1="37.04651063100137" y2="41.75"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00235768175583" x2="10" y1="37.04670698924731" y2="41.91666666666666"/>
   <ellipse cx="15.01" cy="35.25" fill-opacity="0" rx="14.76" ry="14.76" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="1" x="15.01333161865569" xlink:href="#terminal" y="50.00705645161292"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV平河电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="43.33" xlink:href="logo.png" y="47.67"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,177.958,77.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="177.96" xml:space="preserve" y="81.17" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,179.667,77.357) scale(1,1) translate(6.80937e-15,0)" writing-mode="lr" x="179.67" xml:space="preserve" y="86.36" zvalue="3">10kV平河电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="1" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,73.4375,325) scale(1,1) translate(0,0)" width="72.88" x="37" y="313" zvalue="68"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.4375,325) scale(1,1) translate(0,0)" writing-mode="lr" x="73.44" xml:space="preserve" y="329.5" zvalue="68">信号一览</text>
  <line fill="none" id="35" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.3333333333334" x2="377.3333333333334" y1="15.66666666666674" y2="1045.666666666667" zvalue="4"/>
  <line fill="none" id="33" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.333333333333599" x2="370.3333333333331" y1="151.5371592807491" y2="151.5371592807491" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.333333333333371" x2="185.3333333333334" y1="163.6666666666667" y2="163.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.333333333333371" x2="185.3333333333334" y1="189.6666666666667" y2="189.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.333333333333371" x2="4.333333333333371" y1="163.6666666666667" y2="189.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="185.3333333333334" y1="163.6666666666667" y2="189.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="366.3333333333334" y1="163.6666666666667" y2="163.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="366.3333333333334" y1="189.6666666666667" y2="189.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="185.3333333333334" y1="163.6666666666667" y2="189.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.3333333333334" x2="366.3333333333334" y1="163.6666666666667" y2="189.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.333333333333371" x2="185.3333333333334" y1="189.6666666666667" y2="189.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.333333333333371" x2="185.3333333333334" y1="213.9166666666667" y2="213.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.333333333333371" x2="4.333333333333371" y1="189.6666666666667" y2="213.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="185.3333333333334" y1="189.6666666666667" y2="213.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="366.3333333333334" y1="189.6666666666667" y2="189.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="366.3333333333334" y1="213.9166666666667" y2="213.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="185.3333333333334" y1="189.6666666666667" y2="213.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.3333333333334" x2="366.3333333333334" y1="189.6666666666667" y2="213.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.333333333333371" x2="185.3333333333334" y1="213.9166666666667" y2="213.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.333333333333371" x2="185.3333333333334" y1="236.6666666666667" y2="236.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.333333333333371" x2="4.333333333333371" y1="213.9166666666667" y2="236.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="185.3333333333334" y1="213.9166666666667" y2="236.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="366.3333333333334" y1="213.9166666666667" y2="213.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="366.3333333333334" y1="236.6666666666667" y2="236.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="185.3333333333334" y1="213.9166666666667" y2="236.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.3333333333334" x2="366.3333333333334" y1="213.9166666666667" y2="236.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.333333333333371" x2="185.3333333333334" y1="236.6666666666667" y2="236.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.333333333333371" x2="185.3333333333334" y1="259.4166666666667" y2="259.4166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.333333333333371" x2="4.333333333333371" y1="236.6666666666667" y2="259.4166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="185.3333333333334" y1="236.6666666666667" y2="259.4166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="366.3333333333334" y1="236.6666666666667" y2="236.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="366.3333333333334" y1="259.4166666666667" y2="259.4166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="185.3333333333334" y1="236.6666666666667" y2="259.4166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.3333333333334" x2="366.3333333333334" y1="236.6666666666667" y2="259.4166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.333333333333371" x2="185.3333333333334" y1="259.4166666666667" y2="259.4166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.333333333333371" x2="185.3333333333334" y1="282.1666666666667" y2="282.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.333333333333371" x2="4.333333333333371" y1="259.4166666666667" y2="282.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="185.3333333333334" y1="259.4166666666667" y2="282.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="366.3333333333334" y1="259.4166666666667" y2="259.4166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="366.3333333333334" y1="282.1666666666667" y2="282.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.3333333333334" x2="185.3333333333334" y1="259.4166666666667" y2="282.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.3333333333334" x2="366.3333333333334" y1="259.4166666666667" y2="282.1666666666667"/>
  <line fill="none" id="31" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.333333333333599" x2="370.3333333333331" y1="621.5371592807492" y2="621.5371592807492" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.333333333333371" x2="93.33333333333337" y1="936.666666666667" y2="936.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.333333333333371" x2="93.33333333333337" y1="975.829966666667" y2="975.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.333333333333371" x2="3.333333333333371" y1="936.666666666667" y2="975.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.33333333333337" x2="93.33333333333337" y1="936.666666666667" y2="975.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.33333333333337" x2="363.3333333333334" y1="936.666666666667" y2="936.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.33333333333337" x2="363.3333333333334" y1="975.829966666667" y2="975.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.33333333333337" x2="93.33333333333337" y1="936.666666666667" y2="975.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.3333333333334" x2="363.3333333333334" y1="936.666666666667" y2="975.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.333333333333371" x2="93.33333333333337" y1="975.829936666667" y2="975.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.333333333333371" x2="93.33333333333337" y1="1003.748336666667" y2="1003.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.333333333333371" x2="3.333333333333371" y1="975.829936666667" y2="1003.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.33333333333337" x2="93.33333333333337" y1="975.829936666667" y2="1003.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.33333333333337" x2="183.3333333333334" y1="975.829936666667" y2="975.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.33333333333337" x2="183.3333333333334" y1="1003.748336666667" y2="1003.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.33333333333337" x2="93.33333333333337" y1="975.829936666667" y2="1003.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.3333333333334" x2="183.3333333333334" y1="975.829936666667" y2="1003.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.3333333333335" x2="273.3333333333335" y1="975.829936666667" y2="975.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.3333333333335" x2="273.3333333333335" y1="1003.748336666667" y2="1003.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.3333333333335" x2="183.3333333333335" y1="975.829936666667" y2="1003.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.3333333333335" x2="273.3333333333335" y1="975.829936666667" y2="1003.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.3333333333334" x2="363.3333333333334" y1="975.829936666667" y2="975.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.3333333333334" x2="363.3333333333334" y1="1003.748336666667" y2="1003.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.3333333333334" x2="273.3333333333334" y1="975.829936666667" y2="1003.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.3333333333334" x2="363.3333333333334" y1="975.829936666667" y2="1003.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.333333333333371" x2="93.33333333333337" y1="1003.748266666667" y2="1003.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.333333333333371" x2="93.33333333333337" y1="1031.666666666667" y2="1031.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.333333333333371" x2="3.333333333333371" y1="1003.748266666667" y2="1031.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.33333333333337" x2="93.33333333333337" y1="1003.748266666667" y2="1031.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.33333333333337" x2="183.3333333333334" y1="1003.748266666667" y2="1003.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.33333333333337" x2="183.3333333333334" y1="1031.666666666667" y2="1031.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.33333333333337" x2="93.33333333333337" y1="1003.748266666667" y2="1031.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.3333333333334" x2="183.3333333333334" y1="1003.748266666667" y2="1031.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.3333333333335" x2="273.3333333333335" y1="1003.748266666667" y2="1003.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.3333333333335" x2="273.3333333333335" y1="1031.666666666667" y2="1031.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.3333333333335" x2="183.3333333333335" y1="1003.748266666667" y2="1031.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.3333333333335" x2="273.3333333333335" y1="1003.748266666667" y2="1031.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.3333333333334" x2="363.3333333333334" y1="1003.748266666667" y2="1003.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.3333333333334" x2="363.3333333333334" y1="1031.666666666667" y2="1031.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.3333333333334" x2="273.3333333333334" y1="1003.748266666667" y2="1031.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.3333333333334" x2="363.3333333333334" y1="1003.748266666667" y2="1031.666666666667"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.3333,956.667) scale(1,1) translate(0,0)" writing-mode="lr" x="48.33" xml:space="preserve" y="962.67" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.3333,990.667) scale(1,1) translate(0,0)" writing-mode="lr" x="45.33" xml:space="preserve" y="996.67" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.333,990.667) scale(1,1) translate(0,0)" writing-mode="lr" x="227.33" xml:space="preserve" y="996.67" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.3333,1018.67) scale(1,1) translate(0,0)" writing-mode="lr" x="44.33" xml:space="preserve" y="1024.67" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.333,1018.67) scale(1,1) translate(0,0)" writing-mode="lr" x="226.33" xml:space="preserve" y="1024.67" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,68.8333,651.167) scale(1,1) translate(0,-2.80738e-13)" writing-mode="lr" x="68.83333333333337" xml:space="preserve" y="655.6666666666667" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.387,958.667) scale(1,1) translate(0,0)" writing-mode="lr" x="228.39" xml:space="preserve" y="964.67" zvalue="26">PingHe-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,137.387,1014.67) scale(1,1) translate(0,0)" writing-mode="lr" x="137.39" xml:space="preserve" y="1020.67" zvalue="27">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,42.3333,177.667) scale(1,1) translate(0,0)" writing-mode="lr" x="42.33" xml:space="preserve" y="183.17" zvalue="29">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.333,177.667) scale(1,1) translate(0,0)" writing-mode="lr" x="222.33" xml:space="preserve" y="183.17" zvalue="30">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.5208,249.667) scale(1,1) translate(0,0)" writing-mode="lr" x="49.52" xml:space="preserve" y="254.17" zvalue="31">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.417,204.361) scale(1,1) translate(0,0)" writing-mode="lr" x="234.42" xml:space="preserve" y="208.86" zvalue="32">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,511.167,643.667) scale(1,1) translate(0,-4.2244e-13)" writing-mode="lr" x="511.17" xml:space="preserve" y="648.17" zvalue="40">0.4kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" x="902.1328125" xml:space="preserve" y="974.7743490134186" zvalue="43">#1发电机           </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="902.1328125" xml:space="preserve" y="990.7743490134186" zvalue="43">160KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,878.864,723.314) scale(1,1) translate(0,0)" writing-mode="lr" x="878.86" xml:space="preserve" y="727.8099999999999" zvalue="45">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,923.47,801.736) scale(1,1) translate(0,0)" writing-mode="lr" x="923.47" xml:space="preserve" y="806.24" zvalue="49">401</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" x="774.15625" xml:space="preserve" y="516.6665088383838" zvalue="54">#1主变                 </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="774.15625" xml:space="preserve" y="532.6665088383838" zvalue="54">200KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,811.444,394.414) scale(1,1) translate(0,0)" writing-mode="lr" x="811.4400000000001" xml:space="preserve" y="398.91" zvalue="57">0316</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,853.671,295.222) scale(1,1) translate(0,0)" writing-mode="lr" x="853.67" xml:space="preserve" y="299.72" zvalue="60">031</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,835.556,136.667) scale(1,1) translate(0,0)" writing-mode="lr" x="835.5599999999999" xml:space="preserve" y="141.17" zvalue="62">10kV东勐线平河支线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,189.211,325.591) scale(1,1) translate(0,0)" writing-mode="lr" x="189.21" xml:space="preserve" y="330.09" zvalue="64">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,294.211,325.591) scale(1,1) translate(0,0)" writing-mode="lr" x="294.21" xml:space="preserve" y="330.09" zvalue="65">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="10kV平河电站.svg"><rect fill-opacity="0" height="24" width="72.88" x="37" y="313" zvalue="68"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="38">
   <path class="v400" d="M 553.33 648 L 1354 648" stroke-width="6" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674243182596" ObjectName="0.4kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674243182596"/></metadata>
  <path d="M 553.33 648 L 1354 648" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v400" height="30" transform="rotate(0,897.348,919.957) scale(1.85899,1.85899) translate(-401.756,-412.203)" width="30" x="869.4628110403481" xlink:href="#Generator:发电机_0" y="892.0724504570603" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449796702214" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449796702214"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,897.348,919.957) scale(1.85899,1.85899) translate(-401.756,-412.203)" width="30" x="869.4628110403481" y="892.0724504570603"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="165">
   <use class="v400" height="30" transform="rotate(0,898.082,724.314) scale(1.9625,1.2338) translate(-433.242,-133.746)" width="15" x="883.3635212897802" xlink:href="#Disconnector:刀闸_0" y="705.8075396825394" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449796636678" ObjectName="#1发电机4011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449796636678"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,898.082,724.314) scale(1.9625,1.2338) translate(-433.242,-133.746)" width="15" x="883.3635212897802" y="705.8075396825394"/></g>
  <g id="49">
   <use class="kv10" height="30" transform="rotate(0,833.899,395.414) scale(0.909091,0.909091) translate(82.7081,38.1778)" width="15" x="827.0808084275988" xlink:href="#Disconnector:令克_0" y="381.7777777777778" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449796767750" ObjectName="#1主变10kV侧0316隔离刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449796767750"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,833.899,395.414) scale(0.909091,0.909091) translate(82.7081,38.1778)" width="15" x="827.0808084275988" y="381.7777777777778"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="45">
   <path class="v400" d="M 898.25 706.42 L 898.25 648" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="38@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.25 706.42 L 898.25 648" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="v400" d="M 897.35 892.54 L 897.35 811.04" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="170@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 897.35 892.54 L 897.35 811.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="v400" d="M 898.97 789.8 L 898.97 742.5" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="165@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.97 789.8 L 898.97 742.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="v400" d="M 833.62 560.43 L 833.62 648" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@1" LinkObjectIDznd="38@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 833.62 560.43 L 833.62 648" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv10" d="M 833.82 406.55 L 833.82 481.77" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@1" LinkObjectIDznd="51@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 833.82 406.55 L 833.82 481.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv10" d="M 833.97 306.83 L 833.97 383.37" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@1" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 833.97 306.83 L 833.97 383.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv10" d="M 833.33 196.13 L 833.33 285.59" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@0" LinkObjectIDznd="65@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 833.33 196.13 L 833.33 285.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="170">
   <use class="v400" height="20" transform="rotate(0,899.012,800.428) scale(1.22222,1.11111) translate(-162.346,-78.9317)" width="10" x="892.9011668709484" xlink:href="#Breaker:开关_0" y="789.3171550671549" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924510613509" ObjectName="#1发电机401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924510613509"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,899.012,800.428) scale(1.22222,1.11111) translate(-162.346,-78.9317)" width="10" x="892.9011668709484" y="789.3171550671549"/></g>
  <g id="65">
   <use class="kv10" height="20" transform="rotate(0,833.893,296.222) scale(1.22222,1.11111) translate(-150.506,-28.5111)" width="10" x="827.7821552289456" xlink:href="#Breaker:开关_0" y="285.1111111111111" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924510679045" ObjectName="#1主变10kV侧031断路器"/>
   <cge:TPSR_Ref TObjectID="6473924510679045"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,833.893,296.222) scale(1.22222,1.11111) translate(-150.506,-28.5111)" width="10" x="827.7821552289456" y="285.1111111111111"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="51">
   <g id="510">
    <use class="kv10" height="50" transform="rotate(0,833.599,520.914) scale(1.58,1.58) translate(-297.305,-176.722)" width="30" x="809.9" xlink:href="#PowerTransformer2:Y-Y_0" y="481.41" zvalue="52"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874436378628" ObjectName="10"/>
    </metadata>
   </g>
   <g id="511">
    <use class="v400" height="50" transform="rotate(0,833.599,520.914) scale(1.58,1.58) translate(-297.305,-176.722)" width="30" x="809.9" xlink:href="#PowerTransformer2:Y-Y_1" y="481.41" zvalue="52"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874436444164" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399450427396" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399450427396"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,833.599,520.914) scale(1.58,1.58) translate(-297.305,-176.722)" width="30" x="809.9" y="481.41"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,321.485,326.107) scale(0.708333,0.665547) translate(128.001,158.86)" width="30" x="310.86" xlink:href="#State:红绿圆(方形)_0" y="316.12" zvalue="66"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,321.485,326.107) scale(0.708333,0.665547) translate(128.001,158.86)" width="30" x="310.86" y="316.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,225.86,326.107) scale(0.708333,0.665547) translate(88.6262,158.86)" width="30" x="215.24" xlink:href="#State:红绿圆(方形)_0" y="316.12" zvalue="67"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,225.86,326.107) scale(0.708333,0.665547) translate(88.6262,158.86)" width="30" x="215.24" y="316.12"/></g>
 </g>
</svg>