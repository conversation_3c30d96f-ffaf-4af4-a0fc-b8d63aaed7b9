<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549680472065" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:接地可调两卷变（规范制图）_0" viewBox="0,0,15,20">
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.407783003462016" x2="5.342474279636929" y1="6.303393899561708" y2="4.443405305784673"/>
   <use terminal-index="0" type="1" x="7.400350919212375" xlink:href="#terminal" y="2.255340193223605"/>
   <use terminal-index="2" type="2" x="7.5" xlink:href="#terminal" y="6.249999999999999"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.546899427352102" x2="7.404066961337196" y1="4.317578864636594" y2="6.303393899561695"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.401384443428335" x2="7.401384443428335" y1="6.310758227164332" y2="8.34034281757342"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.91666666666667" x2="12.74547107366039" y1="3.553047675216829" y2="3.553047675216829"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.083333333333337" x2="12.75" y1="9.046663465350886" y2="3.552242201885312"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.7491871157852" x2="11.75" y1="3.553047675216865" y2="5.083333333333333"/>
   <ellipse cx="7.44" cy="6.46" fill-opacity="0" rx="4.25" ry="4.21" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:接地可调两卷变（规范制图）_1" viewBox="0,0,15,20">
   <path d="M 5.2379 14.7101 L 9.85166 14.7101 L 7.43896 11.0192 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="1" x="7.396634877087553" xlink:href="#terminal" y="17.07970653458569"/>
   <ellipse cx="7.44" cy="12.87" fill-opacity="0" rx="4.25" ry="4.21" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="ACLineSegment:线路（规范制图）_0" viewBox="0,0,5,20">
   <use terminal-index="0" type="0" x="2.5" xlink:href="#terminal" y="19.35"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.5" x2="2.5" y1="0.5833333333333321" y2="19.26666666666667"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:五卷PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="25.16666666666667" y2="1"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,14) scale(1,1) translate(0,0)" width="6" x="7" y="7"/>
   <path d="M 5.11667 33.0667 L 5.11667 37.0667 L 8.11667 35.0667 L 5.11667 33.0667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.66666666666667" x2="9.999999999999998" y1="4" y2="4"/>
   <ellipse cx="13.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <ellipse cx="13.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.44906826265991" x2="6.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659912" x2="8.849068262659907" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659907" x2="4.049068262659912" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="36.96252026861409" y2="34.48744029990989"/>
   <ellipse cx="6.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.11944444444444" x2="29.63333333333333" y1="28.26666666666667" y2="28.26666666666667"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,27.29,13.93) scale(1,1) translate(0,0)" width="7.58" x="23.5" y="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="3.93333333333333" y2="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="5.85" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="24.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="30.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.16111111111111" x2="30.27222222222222" y1="26.88508771929826" y2="26.88508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="21.85" y2="25.45"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.88333333333333" x2="31.55" y1="25.50350877192984" y2="25.50350877192984"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷带壁雷器_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="28.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11.75" y1="17.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="14.75" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="12" y1="11.75" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="9" y1="12.75" y2="12.75"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.04,18.25) scale(1,1) translate(0,0)" width="3.25" x="9.42" y="14.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.5" y1="10.83333333333333" y2="10.83333333333333"/>
   <path d="M 5.025 2.775 L 4.16667 9.5 L 5.025 7.60833 L 5.91667 9.5 L 5.025 2.775" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 5 23.75 L 11 23.75 L 11 17.75 L 10.25 20.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="7.627914951989029" y2="28.23902606310013"/>
  </symbol>
  <symbol id="Compensator:并联电容器12121_0" viewBox="0,0,60,30">
   <use terminal-index="0" type="0" x="3.373372509142698" xlink:href="#terminal" y="12.54754746794735"/>
   <rect fill-opacity="0" height="2.8" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,35.96,12.55) scale(1,1) translate(0,0)" width="8.41" x="31.75" y="11.15"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="45.41541591575949" x2="50.34627285851084" y1="12.54754746794735" y2="12.54754746794735"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="47.20609554233762" x2="50.34627285851084" y1="22.75961171517187" y2="22.75961171517187"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.10210217033084" x2="29.01382861144973" y1="22.70770795787974" y2="22.70770795787974"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.92592676741369" x2="55.92592676741369" y1="1.569902800664089" y2="23.73646842528693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.92592676741369" x2="53.12312387363924" y1="23.73646842528693" y2="23.73646842528693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="50.32032097986479" x2="55.92592676741369" y1="12.54754746794735" y2="12.54754746794735"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="53.12312387363924" x2="55.92592676741369" y1="1.569902800664083" y2="1.569902800664083"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="50.32032097986479" x2="50.32032097986479" y1="12.54754746794735" y2="22.72068389720278"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.75175180860903" x2="48.21821880953394" y1="29.01401446887226" y2="29.01401446887226"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.94894891483458" x2="28.94894891483458" y1="22.66878013991066" y2="12.54754746794735"/>
   <path d="M 37.1455 22.7077 A 4.15364 2.547 -90 0 1 32.0515 22.7077" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 42.2395 22.7077 A 4.15364 2.547 -90 0 1 37.1455 22.7077" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 47.1934 22.7077 A 4.15364 2.547 -90 0 1 42.0994 22.7077" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="44.01401446887226" x2="44.01401446887226" y1="10.44544529761651" y2="14.64964963827819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="45.41541591575949" x2="45.41541591575949" y1="10.44544529761651" y2="14.64964963827819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.789455239606355" x2="8.803803115830696" y1="12.51251243177518" y2="12.51251243177518"/>
   <path d="M 15.7174 19.1458 A 6.89022 6.65666 -180 1 0 8.82716 12.4892" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.8108 19.0524 L 15.9276 12.5125 L 44.1308 12.5125" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.750000000000004" y2="4.166666666666668"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="32"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="1" y1="27" y2="11"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="27" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id="State:间隔模板_0" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(85,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="State:间隔模板_1" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(185,185,185)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变11_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="9.25" y2="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="26.83333333333333" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0928507596068" x2="10.0928507596068" y1="10.97013412501683" y2="12.64189293057399"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.77292225201073" x2="10.0928507596068" y1="14.31365173613114" y2="12.64189293057398"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV王子树变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,169.51,51.3772) scale(1,1) translate(-1.29705e-14,0)" writing-mode="lr" x="169.51" xml:space="preserve" y="55.88" zvalue="328"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="1" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,187.482,48.3894) scale(1,1) translate(0,2.92884e-15)" writing-mode="lr" x="187.48" xml:space="preserve" y="55.89" zvalue="329"> 35kV王子树变</text>
  <image height="82.56999999999999" id="2" preserveAspectRatio="xMidYMid slice" width="249.69" x="71.31" xlink:href="logo.png" y="14"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.153,55.2864) scale(1,1) translate(-1.58333e-14,0)" writing-mode="lr" x="196.15" xml:space="preserve" y="58.79" zvalue="419"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,225,55.2631) scale(1,1) translate(0,0)" writing-mode="lr" x="225" xml:space="preserve" y="64.26000000000001" zvalue="420">35kV王子树变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="251" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,195.531,385.25) scale(1,1) translate(0,0)" width="72.88" x="159.09" y="373.25" zvalue="610"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,195.531,385.25) scale(1,1) translate(0,0)" writing-mode="lr" x="195.53" xml:space="preserve" y="389.75" zvalue="610">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="244" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,93.625,385.25) scale(1,1) translate(0,0)" width="72.88" x="57.19" y="373.25" zvalue="611"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93.625,385.25) scale(1,1) translate(0,0)" writing-mode="lr" x="93.63" xml:space="preserve" y="389.75" zvalue="611">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="236" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,93.625,348.75) scale(1,1) translate(0,0)" width="72.88" x="57.19" y="336.75" zvalue="612"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93.625,348.75) scale(1,1) translate(0,0)" writing-mode="lr" x="93.63" xml:space="preserve" y="353.25" zvalue="612">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,752.286,399.857) scale(1,1) translate(0,2.59364e-13)" writing-mode="lr" x="752.29" xml:space="preserve" y="404.36" zvalue="37">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1454,703.857) scale(1,1) translate(1.42286e-12,0)" writing-mode="lr" x="1454" xml:space="preserve" y="708.36" zvalue="39">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1074.57,590.663) scale(1,1) translate(0,0)" writing-mode="lr" x="1074.57" xml:space="preserve" y="595.16" zvalue="40">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1051,522.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1051" xml:space="preserve" y="527" zvalue="43">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1044,466) scale(1,1) translate(0,0)" writing-mode="lr" x="1044" xml:space="preserve" y="470.5" zvalue="46">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1059.63,670.889) scale(1,1) translate(-2.32538e-13,0)" writing-mode="lr" x="1059.63" xml:space="preserve" y="675.39" zvalue="64">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,985.714,511.714) scale(1,1) translate(2.17207e-13,0)" writing-mode="lr" x="985.71" xml:space="preserve" y="516.21" zvalue="72">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,958.75,326.5) scale(1,1) translate(0,0)" writing-mode="lr" x="958.75" xml:space="preserve" y="331" zvalue="75">362</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,948,254) scale(1,1) translate(0,0)" writing-mode="lr" x="948" xml:space="preserve" y="258.5" zvalue="78">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1003.87,359.634) scale(1,1) translate(0,0)" writing-mode="lr" x="1003.87" xml:space="preserve" y="364.13" zvalue="81">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,952.5,386) scale(1,1) translate(0,0)" writing-mode="lr" x="952.5" xml:space="preserve" y="390.5" zvalue="85">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,923.088,91.5) scale(1,1) translate(0,0)" writing-mode="lr" x="923.09" xml:space="preserve" y="96" zvalue="91">35kV西掌线王子树T线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1003.87,211.634) scale(1,1) translate(0,0)" writing-mode="lr" x="1003.87" xml:space="preserve" y="216.13" zvalue="95">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1003.87,291.634) scale(1,1) translate(0,0)" writing-mode="lr" x="1003.87" xml:space="preserve" y="296.13" zvalue="99">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,963,182.5) scale(1,1) translate(0,0)" writing-mode="lr" x="963" xml:space="preserve" y="187" zvalue="102">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1472,354) scale(1,1) translate(0,0)" writing-mode="lr" x="1472" xml:space="preserve" y="358.5" zvalue="110">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1527.87,311.634) scale(1,1) translate(0,0)" writing-mode="lr" x="1527.87" xml:space="preserve" y="316.13" zvalue="114">39017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1525.27,387.531) scale(1,1) translate(0,0)" writing-mode="lr" x="1525.27" xml:space="preserve" y="392.03" zvalue="116">39010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1443.29,223) scale(1,1) translate(6.12085e-13,0)" writing-mode="lr" x="1443.29" xml:space="preserve" y="227.5" zvalue="118">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="222" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,805.25,584.375) scale(1,1) translate(0,0)" writing-mode="lr" x="805.25" xml:space="preserve" y="588.88" zvalue="124">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,782.286,683.857) scale(1,1) translate(0,0)" writing-mode="lr" x="782.29" xml:space="preserve" y="688.36" zvalue="125">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1340.75,774.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1340.75" xml:space="preserve" y="779" zvalue="131">061</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1396.37,837.884) scale(1,1) translate(0,0)" writing-mode="lr" x="1396.37" xml:space="preserve" y="842.38" zvalue="142">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1398.27,881.34) scale(1,1) translate(0,0)" writing-mode="lr" x="1398.27" xml:space="preserve" y="885.84" zvalue="145">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1261,826) scale(1,1) translate(0,0)" writing-mode="lr" x="1261" xml:space="preserve" y="830.5" zvalue="152">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1211.75,774.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1211.75" xml:space="preserve" y="779" zvalue="172">062</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="171" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1199.91,838) scale(1,1) translate(0,0)" writing-mode="lr" x="1199.91" xml:space="preserve" y="842.5" zvalue="177">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1148,867) scale(1,1) translate(0,0)" writing-mode="lr" x="1148" xml:space="preserve" y="871.5" zvalue="188">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,734.426,772) scale(1,1) translate(0,0)" writing-mode="lr" x="734.4299999999999" xml:space="preserve" y="776.5" zvalue="234">065</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,784.022,874) scale(1,1) translate(0,0)" writing-mode="lr" x="784.02" xml:space="preserve" y="878.5" zvalue="240">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,726.5,872.25) scale(1,1) translate(0,0)" writing-mode="lr" x="726.5" xml:space="preserve" y="876.75" zvalue="245">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1083.57,613.214) scale(1,1) translate(2.32512e-13,-1.33893e-13)" writing-mode="lr" x="1083.57" xml:space="preserve" y="617.71" zvalue="265">2500kVA</text>
  <line fill="none" id="149" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="386.3571428571431" x2="386.3571428571431" y1="25.99999999999994" y2="1016" zvalue="330"/>
  <line fill="none" id="145" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.76263883569413" x2="324.3738988893133" y1="159.7708030256258" y2="159.7708030256258" zvalue="332"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.857142857143" x2="119.2971428571429" y1="918.7056480819297" y2="918.7056480819297"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.857142857143" x2="119.2971428571429" y1="970.0451480819296" y2="970.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.857142857143" x2="47.857142857143" y1="918.7056480819297" y2="970.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.2971428571429" x2="119.2971428571429" y1="918.7056480819297" y2="970.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.297642857143" x2="353.857642857143" y1="918.7056480819297" y2="918.7056480819297"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.297642857143" x2="353.857642857143" y1="970.0451480819296" y2="970.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.297642857143" x2="119.297642857143" y1="918.7056480819297" y2="970.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="353.857642857143" x2="353.857642857143" y1="918.7056480819297" y2="970.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.857142857143" x2="119.2971428571429" y1="970.0451280819295" y2="970.0451280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.857142857143" x2="119.2971428571429" y1="997.5226280819296" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.857142857143" x2="47.857142857143" y1="970.0451280819295" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.2971428571429" x2="119.2971428571429" y1="970.0451280819295" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.297642857143" x2="190.3329428571431" y1="970.0451280819295" y2="970.0451280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.297642857143" x2="190.3329428571431" y1="997.5226280819296" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.297642857143" x2="119.297642857143" y1="970.0451280819295" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.3329428571431" x2="190.3329428571431" y1="970.0451280819295" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.3329428571431" x2="272.095042857143" y1="970.0451280819295" y2="970.0451280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.3329428571431" x2="272.095042857143" y1="997.5226280819296" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.3329428571431" x2="190.3329428571431" y1="970.0451280819295" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.095042857143" x2="272.095042857143" y1="970.0451280819295" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.094942857143" x2="353.857042857143" y1="970.0451280819295" y2="970.0451280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.094942857143" x2="353.857042857143" y1="997.5226280819296" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.094942857143" x2="272.094942857143" y1="970.0451280819295" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="353.857042857143" x2="353.857042857143" y1="970.0451280819295" y2="997.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.857142857143" x2="119.2971428571429" y1="997.5225480819296" y2="997.5225480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.857142857143" x2="119.2971428571429" y1="1025.00004808193" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="47.857142857143" x2="47.857142857143" y1="997.5225480819296" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.2971428571429" x2="119.2971428571429" y1="997.5225480819296" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.297642857143" x2="190.3329428571431" y1="997.5225480819296" y2="997.5225480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.297642857143" x2="190.3329428571431" y1="1025.00004808193" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="119.297642857143" x2="119.297642857143" y1="997.5225480819296" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.3329428571431" x2="190.3329428571431" y1="997.5225480819296" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.3329428571431" x2="272.095042857143" y1="997.5225480819296" y2="997.5225480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.3329428571431" x2="272.095042857143" y1="1025.00004808193" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.3329428571431" x2="190.3329428571431" y1="997.5225480819296" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.095042857143" x2="272.095042857143" y1="997.5225480819296" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.094942857143" x2="353.857042857143" y1="997.5225480819296" y2="997.5225480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.094942857143" x2="353.857042857143" y1="1025.00004808193" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.094942857143" x2="272.094942857143" y1="997.5225480819296" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="353.857042857143" x2="353.857042857143" y1="997.5225480819296" y2="1025.00004808193"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,80.1062,948.279) scale(1,1) translate(0,1.04078e-13)" writing-mode="lr" x="53.21" xml:space="preserve" y="952.78" zvalue="334">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,78.4698,985.648) scale(1,1) translate(6.9877e-15,1.08227e-13)" writing-mode="lr" x="62.94" xml:space="preserve" y="990.15" zvalue="335">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,268.738,986.648) scale(1,1) translate(1.3325e-13,-1.51673e-12)" writing-mode="lr" x="200.03" xml:space="preserve" y="991.15" zvalue="336">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,76.5281,1015.21) scale(1,1) translate(-5.27987e-14,-1.56112e-12)" writing-mode="lr" x="76.53" xml:space="preserve" y="1019.71" zvalue="337">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,229.604,1013.21) scale(1,1) translate(0,1.11286e-13)" writing-mode="lr" x="200.21" xml:space="preserve" y="1017.71" zvalue="338">更新日期</text>
  <line fill="none" id="128" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.14182784373793" x2="323.7530878973571" y1="610.5373878122266" y2="610.5373878122266" zvalue="339"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90.2064,631.579) scale(1,1) translate(4.34224e-15,-1.36087e-13)" writing-mode="lr" x="90.20642234775949" xml:space="preserve" y="636.0792383117243" zvalue="341">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="162.9999999999999" y2="162.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="188.9999999999999" y2="188.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="8.857142857143003" y1="162.9999999999999" y2="188.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="162.9999999999999" y2="188.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="162.9999999999999" y2="162.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="188.9999999999999" y2="188.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="162.9999999999999" y2="188.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370.857142857143" x2="370.857142857143" y1="162.9999999999999" y2="188.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="188.9999999999999" y2="188.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="213.2499999999999" y2="213.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="8.857142857143003" y1="188.9999999999999" y2="213.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="188.9999999999999" y2="213.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="188.9999999999999" y2="188.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="213.2499999999999" y2="213.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="188.9999999999999" y2="213.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370.857142857143" x2="370.857142857143" y1="188.9999999999999" y2="213.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="213.2499999999999" y2="213.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="235.9999999999999" y2="235.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="8.857142857143003" y1="213.2499999999999" y2="235.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="213.2499999999999" y2="235.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="213.2499999999999" y2="213.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="235.9999999999999" y2="235.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="213.2499999999999" y2="235.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370.857142857143" x2="370.857142857143" y1="213.2499999999999" y2="235.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="235.9999999999999" y2="235.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="258.7499999999999" y2="258.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="8.857142857143003" y1="235.9999999999999" y2="258.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="235.9999999999999" y2="258.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="235.9999999999999" y2="235.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="258.7499999999999" y2="258.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="235.9999999999999" y2="258.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370.857142857143" x2="370.857142857143" y1="235.9999999999999" y2="258.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="258.7499999999999" y2="258.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="189.857142857143" y1="281.4999999999999" y2="281.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8.857142857143003" x2="8.857142857143003" y1="258.7499999999999" y2="281.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="258.7499999999999" y2="281.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="258.7499999999999" y2="258.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="370.857142857143" y1="281.4999999999999" y2="281.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189.857142857143" x2="189.857142857143" y1="258.7499999999999" y2="281.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370.857142857143" x2="370.857142857143" y1="258.7499999999999" y2="281.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="442.9999999999999" y2="442.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="59.857142857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="442.9999999999999" y2="442.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="442.9999999999999" y2="442.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244442857143" x2="223.244442857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="442.9999999999999" y2="442.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="223.244342857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="442.9999999999999" y2="442.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="340.857142857143" x2="340.857142857143" y1="442.9999999999999" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="59.857142857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244442857143" x2="223.244442857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="223.244342857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="481.2823" y2="481.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="340.857142857143" x2="340.857142857143" y1="481.2823" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="59.857142857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244442857143" x2="223.244442857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="223.244342857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="505.9617" y2="505.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="340.857142857143" x2="340.857142857143" y1="505.9617" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="555.3204999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="59.857142857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="555.3204999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="555.3204999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244442857143" x2="223.244442857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="555.3204999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="223.244342857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="530.6410999999999" y2="530.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="555.3204999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="340.857142857143" x2="340.857142857143" y1="530.6410999999999" y2="555.3204999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="555.3205999999999" y2="555.3205999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="59.857142857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="555.3205999999999" y2="555.3205999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="555.3205999999999" y2="555.3205999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244442857143" x2="223.244442857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="555.3205999999999" y2="555.3205999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="223.244342857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="555.3205999999999" y2="555.3205999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="340.857142857143" x2="340.857142857143" y1="555.3205999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="105.631642857143" y1="604.6794" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.857142857143" x2="59.857142857143" y1="579.9999999999999" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="579.9999999999999" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="164.438042857143" y1="604.6794" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.631642857143" x2="105.631642857143" y1="579.9999999999999" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="579.9999999999999" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="223.244442857143" y1="604.6794" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="164.438042857143" x2="164.438042857143" y1="579.9999999999999" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244442857143" x2="223.244442857143" y1="579.9999999999999" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="282.050742857143" y1="604.6794" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="223.244342857143" x2="223.244342857143" y1="579.9999999999999" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="579.9999999999999" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="579.9999999999999" y2="579.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="340.857142857143" y1="604.6794" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="282.050742857143" x2="282.050742857143" y1="579.9999999999999" y2="604.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="340.857142857143" x2="340.857142857143" y1="579.9999999999999" y2="604.6794"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" x="136.859375" xml:space="preserve" y="458" zvalue="345">35kV     母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="136.859375" xml:space="preserve" y="474" zvalue="345">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,206.256,317.841) scale(1,1) translate(0,0)" writing-mode="lr" x="206.26" xml:space="preserve" y="322.34" zvalue="346">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,311.256,317.841) scale(1,1) translate(0,0)" writing-mode="lr" x="311.26" xml:space="preserve" y="322.34" zvalue="347">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" x="254.203125" xml:space="preserve" y="458" zvalue="348">10kV     母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="254.203125" xml:space="preserve" y="474" zvalue="348">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84.8571,493.5) scale(1,1) translate(0,0)" writing-mode="lr" x="84.857142857143" xml:space="preserve" y="497.9999999999999" zvalue="349">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84.8571,519) scale(1,1) translate(0,0)" writing-mode="lr" x="84.857142857143" xml:space="preserve" y="523.5" zvalue="350">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84.8571,544.5) scale(1,1) translate(0,5.88418e-14)" writing-mode="lr" x="84.857142857143" xml:space="preserve" y="548.9999999999999" zvalue="351">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84.8571,570) scale(1,1) translate(0,0)" writing-mode="lr" x="84.857142857143" xml:space="preserve" y="574.5" zvalue="352">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84.8571,595.5) scale(1,1) translate(0,0)" writing-mode="lr" x="84.857142857143" xml:space="preserve" y="600" zvalue="353">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46.8571,177) scale(1,1) translate(0,0)" writing-mode="lr" x="46.86" xml:space="preserve" y="181.5" zvalue="354">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.857,177) scale(1,1) translate(0,0)" writing-mode="lr" x="226.86" xml:space="preserve" y="181.5" zvalue="355">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50.5446,201.25) scale(1,1) translate(0,0)" writing-mode="lr" x="50.54" xml:space="preserve" y="205.75" zvalue="356">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.0446,249) scale(1,1) translate(0,0)" writing-mode="lr" x="54.04" xml:space="preserve" y="253.5" zvalue="357">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.0446,272) scale(1,1) translate(0,0)" writing-mode="lr" x="54.04" xml:space="preserve" y="276.5" zvalue="358">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.5446,225.25) scale(1,1) translate(0,0)" writing-mode="lr" x="51.54" xml:space="preserve" y="229.75" zvalue="359">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1196.75,326.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1196.75" xml:space="preserve" y="331" zvalue="438">361</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1186,254) scale(1,1) translate(0,0)" writing-mode="lr" x="1186" xml:space="preserve" y="258.5" zvalue="440">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1241.87,359.634) scale(1,1) translate(0,0)" writing-mode="lr" x="1241.87" xml:space="preserve" y="364.13" zvalue="442">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1190.5,386) scale(1,1) translate(0,0)" writing-mode="lr" x="1190.5" xml:space="preserve" y="390.5" zvalue="444">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1161.09,91.5) scale(1,1) translate(2.43269e-13,0)" writing-mode="lr" x="1161.09" xml:space="preserve" y="96" zvalue="446">35kV清王线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1241.87,211.634) scale(1,1) translate(0,0)" writing-mode="lr" x="1241.87" xml:space="preserve" y="216.13" zvalue="448">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1241.87,291.634) scale(1,1) translate(0,0)" writing-mode="lr" x="1241.87" xml:space="preserve" y="296.13" zvalue="450">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1201,182.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1201" xml:space="preserve" y="187" zvalue="452">9</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="269" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,755.958,953.875) scale(1,1) translate(0,0)" writing-mode="lr" x="755.96" xml:space="preserve" y="958.38" zvalue="476">10kV帕盆线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="273" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,900.11,774.5) scale(1,1) translate(0,0)" writing-mode="lr" x="900.11" xml:space="preserve" y="779" zvalue="478">064</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="272" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,902.022,874) scale(1,1) translate(0,0)" writing-mode="lr" x="902.02" xml:space="preserve" y="878.5" zvalue="481">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="271" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,842,871) scale(1,1) translate(0,0)" writing-mode="lr" x="842" xml:space="preserve" y="875.5" zvalue="485">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="270" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,873.958,953.875) scale(1,1) translate(0,0)" writing-mode="lr" x="873.96" xml:space="preserve" y="958.38" zvalue="490">10kV盆都线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1010.04,774.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1010.04" xml:space="preserve" y="779" zvalue="493">063</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="286" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1012.02,874) scale(1,1) translate(0,0)" writing-mode="lr" x="1012.02" xml:space="preserve" y="878.5" zvalue="496">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,953.25,869.5) scale(1,1) translate(0,0)" writing-mode="lr" x="953.25" xml:space="preserve" y="874" zvalue="500">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,983.958,953.875) scale(1,1) translate(0,0)" writing-mode="lr" x="983.96" xml:space="preserve" y="958.38" zvalue="505">10kV王子树街面线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="299" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1181.6,980) scale(1,1) translate(3.77565e-13,0)" writing-mode="lr" x="1181.6" xml:space="preserve" y="984.5" zvalue="507">10kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1303.29,857) scale(1,1) translate(0,0)" writing-mode="lr" x="1303.29" xml:space="preserve" y="861.5" zvalue="522">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1317.42,984.545) scale(1,1) translate(0,0)" writing-mode="lr" x="1317.42" xml:space="preserve" y="989.05" zvalue="531">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1351.24,558.938) scale(1,1) translate(0,0)" writing-mode="lr" x="1351.24" xml:space="preserve" y="563.4400000000001" zvalue="550">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,236.606,947.279) scale(1,1) translate(0,1.03967e-13)" writing-mode="lr" x="170.21" xml:space="preserve" y="951.78" zvalue="614">WangZiShu-01-2015</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,156.97,984.648) scale(1,1) translate(0,1.08116e-13)" writing-mode="lr" x="133.94" xml:space="preserve" y="989.15" zvalue="616">李文杰</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,313.104,1013.21) scale(1,1) translate(0,1.11286e-13)" writing-mode="lr" x="280.21" xml:space="preserve" y="1017.71" zvalue="618">20200902</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,296.235,385) scale(1,1) translate(0,0)" writing-mode="lr" x="296.2350463867188" xml:space="preserve" y="389.5" zvalue="645">小电流接地</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,94.2857,312.539) scale(1,1) translate(0,3.34665e-13)" writing-mode="lr" x="94.28570556640625" xml:space="preserve" y="317.0388641357421" zvalue="646">全站公用</text>
  <ellipse cx="902.66" cy="323.66" fill="rgb(255,0,0)" fill-opacity="1" id="121" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="656"/>
 </g>
 <g id="ButtonClass">
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="159.09" y="373.25" zvalue="610"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="57.19" y="373.25" zvalue="611"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="57.19" y="336.75" zvalue="612"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="35">
   <path class="kv35" d="M 699.57 418.86 L 1500.29 418.86" stroke-width="4" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674403876867" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674403876867"/></metadata>
  <path d="M 699.57 418.86 L 1500.29 418.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv10" d="M 699.57 726.86 L 1500.29 726.86" stroke-width="4" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674403942403" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674403942403"/></metadata>
  <path d="M 699.57 726.86 L 1500.29 726.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="39">
   <g id="390">
    <use class="kv35" height="20" transform="rotate(0,1023.07,586) scale(4.65714,4.8) translate(-775.965,-425.917)" width="15" x="988.14" xlink:href="#PowerTransformer2:接地可调两卷变（规范制图）_0" y="538" zvalue="39"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874573479938" ObjectName="35"/>
    </metadata>
   </g>
   <g id="391">
    <use class="kv10" height="20" transform="rotate(0,1023.07,586) scale(4.65714,4.8) translate(-775.965,-425.917)" width="15" x="988.14" xlink:href="#PowerTransformer2:接地可调两卷变（规范制图）_1" y="538" zvalue="39"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874573545474" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399526252546" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399526252546"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1023.07,586) scale(4.65714,4.8) translate(-775.965,-425.917)" width="15" x="988.14" y="538"/></g>
 </g>
 <g id="BreakerClass">
  <g id="43">
   <use class="kv35" height="20" transform="rotate(0,1023,521) scale(1.5542,1.35421) translate(-362.012,-132.732)" width="10" x="1015.229003203369" xlink:href="#Breaker:开关_0" y="507.4578952882238" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925120425987" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925120425987"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1023,521) scale(1.5542,1.35421) translate(-362.012,-132.732)" width="10" x="1015.229003203369" y="507.4578952882238"/></g>
  <g id="63">
   <use class="kv10" height="20" transform="rotate(0,1022.53,671.889) scale(2.22222,2.22222) translate(-556.283,-357.317)" width="10" x="1011.42282317591" xlink:href="#Breaker:小车断路器_0" y="649.6666666666667" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925120491523" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925120491523"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1022.53,671.889) scale(2.22222,2.22222) translate(-556.283,-357.317)" width="10" x="1011.42282317591" y="649.6666666666667"/></g>
  <g id="78">
   <use class="kv35" height="20" transform="rotate(0,923,325) scale(1.5542,1.35421) translate(-326.354,-81.4657)" width="10" x="915.2290032033688" xlink:href="#Breaker:开关_0" y="311.4578952882238" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925120622595" ObjectName="35kV西掌线王子树T线362断路器"/>
   <cge:TPSR_Ref TObjectID="6473925120622595"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,923,325) scale(1.5542,1.35421) translate(-326.354,-81.4657)" width="10" x="915.2290032033688" y="311.4578952882238"/></g>
  <g id="130">
   <use class="kv10" height="20" transform="rotate(0,1317.75,773) scale(2.22222,2.22222) translate(-718.652,-412.928)" width="10" x="1306.639640280912" xlink:href="#Breaker:小车断路器_0" y="750.7777777777778" zvalue="130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925120557059" ObjectName="10kV1号电容器061断路器"/>
   <cge:TPSR_Ref TObjectID="6473925120557059"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1317.75,773) scale(2.22222,2.22222) translate(-718.652,-412.928)" width="10" x="1306.639640280912" y="750.7777777777778"/></g>
  <g id="188">
   <use class="kv10" height="20" transform="rotate(0,1187,773) scale(2.22222,2.22222) translate(-646.739,-412.928)" width="10" x="1175.888888888889" xlink:href="#Breaker:小车断路器_0" y="750.7777777777778" zvalue="170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925120688131" ObjectName="10kV2号站用变062断路器"/>
   <cge:TPSR_Ref TObjectID="6473925120688131"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1187,773) scale(2.22222,2.22222) translate(-646.739,-412.928)" width="10" x="1175.888888888889" y="750.7777777777778"/></g>
  <g id="242">
   <use class="kv10" height="20" transform="rotate(0,759.037,773) scale(2.22222,2.22222) translate(-411.359,-412.928)" width="10" x="747.9255555555558" xlink:href="#Breaker:小车断路器_0" y="750.7777777777778" zvalue="233"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925120753667" ObjectName="10kV帕盆线065断路器"/>
   <cge:TPSR_Ref TObjectID="6473925120753667"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,759.037,773) scale(2.22222,2.22222) translate(-411.359,-412.928)" width="10" x="747.9255555555558" y="750.7777777777778"/></g>
  <g id="147">
   <use class="kv35" height="20" transform="rotate(0,1161,325) scale(1.5542,1.35421) translate(-411.221,-81.4657)" width="10" x="1153.229003203369" xlink:href="#Breaker:开关_0" y="311.4578952882238" zvalue="437"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925120819203" ObjectName="35kV清王线361断路器"/>
   <cge:TPSR_Ref TObjectID="6473925120819203"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1161,325) scale(1.5542,1.35421) translate(-411.221,-81.4657)" width="10" x="1153.229003203369" y="311.4578952882238"/></g>
  <g id="283">
   <use class="kv10" height="20" transform="rotate(0,877.11,773) scale(2.22222,2.22222) translate(-476.299,-412.928)" width="10" x="865.998888888889" xlink:href="#Breaker:小车断路器_0" y="750.7777777777778" zvalue="477"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925120884739" ObjectName="10kV盆都线064断路器"/>
   <cge:TPSR_Ref TObjectID="6473925120884739"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,877.11,773) scale(2.22222,2.22222) translate(-476.299,-412.928)" width="10" x="865.998888888889" y="750.7777777777778"/></g>
  <g id="297">
   <use class="kv10" height="20" transform="rotate(0,987.037,773) scale(2.22222,2.22222) translate(-536.759,-412.928)" width="10" x="975.9255555555558" xlink:href="#Breaker:小车断路器_0" y="750.7777777777778" zvalue="492"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925120950275" ObjectName="10kV王子树街面线063断路器"/>
   <cge:TPSR_Ref TObjectID="6473925120950275"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,987.037,773) scale(2.22222,2.22222) translate(-536.759,-412.928)" width="10" x="975.9255555555558" y="750.7777777777778"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="45">
   <path class="kv35" d="M 1022.61 548.83 L 1022.61 533.93" stroke-width="1" zvalue="43"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@0" LinkObjectIDznd="43@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1022.61 548.83 L 1022.61 533.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv35" d="M 1022.95 508.04 L 1023.06 477.81" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="47@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1022.95 508.04 L 1023.06 477.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv35" d="M 1023.09 456.36 L 1023.09 418.86" stroke-width="1" zvalue="47"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.09 456.36 L 1023.09 418.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv35" d="M 999.64 495.21 L 1023 495.21" stroke-width="1" zvalue="72"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="49" MaxPinNum="2"/>
   </metadata>
  <path d="M 999.64 495.21 L 1023 495.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv35" d="M 987.5 171 L 973.81 171.06" stroke-width="1" zvalue="104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="99@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 987.5 171 L 973.81 171.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv35" d="M 1446.96 418.86 L 1447.06 365.81" stroke-width="1" zvalue="109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@3" LinkObjectIDznd="108@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1446.96 418.86 L 1447.06 365.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv35" d="M 1447.09 344.36 L 1447.09 288.25" stroke-width="1" zvalue="111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="115@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1447.09 344.36 L 1447.09 288.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv35" d="M 1470.75 310.6 L 1447.09 310.6" stroke-width="1" zvalue="113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="113" MaxPinNum="2"/>
   </metadata>
  <path d="M 1470.75 310.6 L 1447.09 310.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv35" d="M 1470.75 390.6 L 1447.02 390.6" stroke-width="1" zvalue="116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="114" MaxPinNum="2"/>
   </metadata>
  <path d="M 1470.75 390.6 L 1447.02 390.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv35" d="M 1350.55 433.96 L 1350.55 418.86" stroke-width="1" zvalue="120"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="35@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1350.55 433.96 L 1350.55 418.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv10" d="M 818 705.84 L 818 726.86" stroke-width="1" zvalue="126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@0" LinkObjectIDznd="37@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 818 705.84 L 818 726.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv35" d="M 923.09 132.85 L 923.09 244.36" stroke-width="1" zvalue="156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="81@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 923.09 132.85 L 923.09 244.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv35" d="M 923.06 265.81 L 922.95 312.04" stroke-width="1" zvalue="157"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@1" LinkObjectIDznd="78@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 923.06 265.81 L 922.95 312.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv35" d="M 923.1 337.93 L 923.09 376.36" stroke-width="1" zvalue="158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@1" LinkObjectIDznd="83@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 923.1 337.93 L 923.09 376.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv35" d="M 923.06 397.81 L 923.06 418.86" stroke-width="1" zvalue="160"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@1" LinkObjectIDznd="35@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 923.06 397.81 L 923.06 418.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="kv35" d="M 946.75 358.6 L 923.1 358.6" stroke-width="1" zvalue="161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="158" MaxPinNum="2"/>
   </metadata>
  <path d="M 946.75 358.6 L 923.1 358.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv35" d="M 946.75 290.6 L 923 290.6" stroke-width="1" zvalue="162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="157" MaxPinNum="2"/>
   </metadata>
  <path d="M 946.75 290.6 L 923 290.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv35" d="M 946.75 210.6 L 923.09 210.6" stroke-width="1" zvalue="163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="156" MaxPinNum="2"/>
   </metadata>
  <path d="M 946.75 210.6 L 923.09 210.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv35" d="M 952.36 171.09 L 923.09 171.09" stroke-width="1" zvalue="164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="156" MaxPinNum="2"/>
   </metadata>
  <path d="M 952.36 171.09 L 923.09 171.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="kv35" d="M 902.03 154.63 L 902.03 144.18 L 923.09 144.18" stroke-width="1" zvalue="165"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="156" MaxPinNum="2"/>
   </metadata>
  <path d="M 902.03 154.63 L 902.03 144.18 L 923.09 144.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv10" d="M 1148.07 825.79 L 1148.15 810.55 L 1187 810.55" stroke-width="1" zvalue="191"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@0" LinkObjectIDznd="254" MaxPinNum="2"/>
   </metadata>
  <path d="M 1148.07 825.79 L 1148.15 810.55 L 1187 810.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="237">
   <path class="kv10" d="M 759.08 911.44 L 759.08 885.81" stroke-width="1" zvalue="239"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="238@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 759.08 911.44 L 759.08 885.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="kv10" d="M 726.57 827.04 L 726.57 810.48 L 759.05 810.48" stroke-width="1" zvalue="248"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@0" LinkObjectIDznd="247" MaxPinNum="2"/>
   </metadata>
  <path d="M 726.57 827.04 L 726.57 810.48 L 759.05 810.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="247">
   <path class="kv10" d="M 759.11 864.36 L 759.04 793" stroke-width="1" zvalue="314"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="238@0" LinkObjectIDznd="242@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 759.11 864.36 L 759.04 793" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="254">
   <path class="kv10" d="M 1187 824.36 L 1187 793" stroke-width="1" zvalue="319"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="188@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1187 824.36 L 1187 793" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="256">
   <path class="kv10" d="M 1187 752.44 L 1187 726.86" stroke-width="1" zvalue="320"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@0" LinkObjectIDznd="37@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1187 752.44 L 1187 726.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="259">
   <path class="kv10" d="M 1022.53 651.33 L 1022.59 619.98" stroke-width="1" zvalue="323"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="39@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1022.53 651.33 L 1022.59 619.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="260">
   <path class="kv10" d="M 1022.53 691.89 L 1022.53 726.86" stroke-width="1" zvalue="324"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@1" LinkObjectIDznd="37@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1022.53 691.89 L 1022.53 726.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="261">
   <path class="kv10" d="M 817.75 645.25 L 818 663.87" stroke-width="1" zvalue="325"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@0" LinkObjectIDznd="123@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 817.75 645.25 L 818 663.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv10" d="M 1317.25 847.36 L 1317.25 793" stroke-width="1" zvalue="406"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="130@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1317.25 847.36 L 1317.25 793" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv35" d="M 1225.5 171 L 1211.81 171.06" stroke-width="1" zvalue="454"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="33@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1225.5 171 L 1211.81 171.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv35" d="M 1161.09 132.02 L 1161.09 244.36" stroke-width="1" zvalue="456"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@0" LinkObjectIDznd="138@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1161.09 132.02 L 1161.09 244.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv35" d="M 1161.06 265.81 L 1160.95 312.04" stroke-width="1" zvalue="457"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@1" LinkObjectIDznd="147@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1161.06 265.81 L 1160.95 312.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv35" d="M 1161.1 337.93 L 1161.09 376.36" stroke-width="1" zvalue="458"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="147@1" LinkObjectIDznd="134@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1161.1 337.93 L 1161.09 376.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv35" d="M 1161.06 397.81 L 1161.06 418.86" stroke-width="1" zvalue="459"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@1" LinkObjectIDznd="35@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1161.06 397.81 L 1161.06 418.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv35" d="M 1184.75 358.6 L 1161.1 358.6" stroke-width="1" zvalue="460"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="27" MaxPinNum="2"/>
   </metadata>
  <path d="M 1184.75 358.6 L 1161.1 358.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv35" d="M 1184.75 290.6 L 1161 290.6" stroke-width="1" zvalue="461"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@0" LinkObjectIDznd="28" MaxPinNum="2"/>
   </metadata>
  <path d="M 1184.75 290.6 L 1161 290.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv35" d="M 1184.75 210.6 L 1161.09 210.6" stroke-width="1" zvalue="462"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 1184.75 210.6 L 1161.09 210.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv35" d="M 1190.36 171.09 L 1161.09 171.09" stroke-width="1" zvalue="463"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 1190.36 171.09 L 1161.09 171.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv35" d="M 1140.03 154.63 L 1140.03 144.18 L 1161.09 144.18" stroke-width="1" zvalue="464"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="30@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 1140.03 154.63 L 1140.03 144.18 L 1161.09 144.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="281">
   <path class="kv10" d="M 877.08 911.44 L 877.08 885.81" stroke-width="1" zvalue="480"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="282@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 877.08 911.44 L 877.08 885.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="277">
   <path class="kv10" d="M 842.07 825.79 L 842.07 811.25 L 877.11 811.25" stroke-width="1" zvalue="486"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="278@0" LinkObjectIDznd="275" MaxPinNum="2"/>
   </metadata>
  <path d="M 842.07 825.79 L 842.07 811.25 L 877.11 811.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="276">
   <path class="kv10" d="M 877.11 752.44 L 877.11 726.86" stroke-width="1" zvalue="487"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="283@0" LinkObjectIDznd="37@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 877.11 752.44 L 877.11 726.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="275">
   <path class="kv10" d="M 877.11 864.36 L 877.11 793" stroke-width="1" zvalue="488"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="282@0" LinkObjectIDznd="283@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 877.11 864.36 L 877.11 793" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="295">
   <path class="kv10" d="M 987.08 911.44 L 987.08 885.81" stroke-width="1" zvalue="495"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="288@0" LinkObjectIDznd="296@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 987.08 911.44 L 987.08 885.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="291">
   <path class="kv10" d="M 953.32 824.29 L 953.32 812.48 L 987.06 812.48" stroke-width="1" zvalue="501"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="292@0" LinkObjectIDznd="289" MaxPinNum="2"/>
   </metadata>
  <path d="M 953.32 824.29 L 953.32 812.48 L 987.06 812.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="289">
   <path class="kv10" d="M 987.11 864.36 L 987.04 793" stroke-width="1" zvalue="503"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="296@0" LinkObjectIDznd="297@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 987.11 864.36 L 987.04 793" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="305">
   <path class="kv10" d="M 1188.35 906.25 L 1188.35 913.02" stroke-width="1" zvalue="511"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@1" LinkObjectIDznd="298@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1188.35 906.25 L 1188.35 913.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="306">
   <path class="kv10" d="M 1188.59 869.82 L 1188.59 845.81" stroke-width="1" zvalue="512"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@0" LinkObjectIDznd="184@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1188.59 869.82 L 1188.59 845.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="kv10" d="M 1352.75 888.5 L 1334.89 888.5 L 1334.89 868.41 L 1317.03 868.41" stroke-width="1" zvalue="523"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="167" MaxPinNum="2"/>
   </metadata>
  <path d="M 1352.75 888.5 L 1334.89 888.5 L 1334.89 868.41 L 1317.03 868.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="kv10" d="M 1351.25 836.85 L 1317.25 836.85" stroke-width="1" zvalue="524"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="143@0" LinkObjectIDznd="4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1351.25 836.85 L 1317.25 836.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="kv10" d="M 1317.75 752.44 L 1317.75 726.86" stroke-width="1" zvalue="526"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="37@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1317.75 752.44 L 1317.75 726.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv10" d="M 759.04 752.44 L 759.04 726.86" stroke-width="1" zvalue="527"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="242@0" LinkObjectIDznd="37@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 759.04 752.44 L 759.04 726.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv10" d="M 987.04 752.44 L 987.04 726.86" stroke-width="1" zvalue="528"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="297@0" LinkObjectIDznd="37@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 987.04 752.44 L 987.04 726.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv10" d="M 1317.03 896.41 L 1317.03 868.81" stroke-width="1" zvalue="538"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="196@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1317.03 896.41 L 1317.03 868.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 1279.8 913.02 L 1279.8 868.45 L 1317.03 868.45" stroke-width="1" zvalue="547"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="167" MaxPinNum="2"/>
   </metadata>
  <path d="M 1279.8 913.02 L 1279.8 868.45 L 1317.03 868.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="279">
   <path class="kv10" d="M 1166.8 871.25 L 1166.8 856 L 1188.59 856" stroke-width="1" zvalue="632"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@0" LinkObjectIDznd="306" MaxPinNum="2"/>
   </metadata>
  <path d="M 1166.8 871.25 L 1166.8 856 L 1188.59 856" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv10" d="M 785.05 900.48 L 785.05 890 L 759.08 890" stroke-width="1" zvalue="636"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@0" LinkObjectIDznd="237" MaxPinNum="2"/>
   </metadata>
  <path d="M 785.05 900.48 L 785.05 890 L 759.08 890" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="301">
   <path class="kv10" d="M 1018.3 900.48 L 1018.3 891 L 987.08 891" stroke-width="1" zvalue="637"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="294@0" LinkObjectIDznd="295" MaxPinNum="2"/>
   </metadata>
  <path d="M 1018.3 900.48 L 1018.3 891 L 987.08 891" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="302">
   <path class="kv10" d="M 903.05 900.48 L 903.05 893 L 877.08 893" stroke-width="1" zvalue="638"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@0" LinkObjectIDznd="281" MaxPinNum="2"/>
   </metadata>
  <path d="M 903.05 900.48 L 903.05 893 L 877.08 893" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="303">
   <path class="kv10" d="M 1280.07 813.79 L 1280.07 801 L 1317.25 801" stroke-width="1" zvalue="639"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="148@0" LinkObjectIDznd="4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1280.07 813.79 L 1280.07 801 L 1317.25 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv35" d="M 1350.31 486.38 L 1350.31 470.39" stroke-width="1" zvalue="648"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="11@0" LinkObjectIDznd="119@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1350.31 486.38 L 1350.31 470.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="47">
   <use class="kv35" height="30" transform="rotate(0,1023,467) scale(1,0.733333) translate(0,165.818)" width="15" x="1015.5" xlink:href="#Disconnector:刀闸_0" y="456" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453994872835" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453994872835"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1023,467) scale(1,0.733333) translate(0,165.818)" width="15" x="1015.5" y="456"/></g>
  <g id="81">
   <use class="kv35" height="30" transform="rotate(0,923,255) scale(1,0.733333) translate(0,88.7273)" width="15" x="915.5" xlink:href="#Disconnector:刀闸_0" y="244" zvalue="76"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453995069443" ObjectName="35kV西掌线王子树T线3626隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453995069443"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,923,255) scale(1,0.733333) translate(0,88.7273)" width="15" x="915.5" y="244"/></g>
  <g id="83">
   <use class="kv35" height="30" transform="rotate(0,923,387) scale(1,0.733333) translate(0,136.727)" width="15" x="915.5" xlink:href="#Disconnector:刀闸_0" y="376" zvalue="84"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453995134979" ObjectName="35kV西掌线王子树T线3621隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453995134979"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,923,387) scale(1,0.733333) translate(0,136.727)" width="15" x="915.5" y="376"/></g>
  <g id="99">
   <use class="kv35" height="30" transform="rotate(270,963,171) scale(-1,0.733333) translate(-1926,58.1818)" width="15" x="955.5" xlink:href="#Disconnector:刀闸_0" y="160" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453995659267" ObjectName="35kV西掌线王子树T线3629隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453995659267"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,963,171) scale(-1,0.733333) translate(-1926,58.1818)" width="15" x="955.5" y="160"/></g>
  <g id="108">
   <use class="kv35" height="30" transform="rotate(0,1447,355) scale(1,0.733333) translate(0,125.091)" width="15" x="1439.5" xlink:href="#Disconnector:刀闸_0" y="344" zvalue="108"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453995855875" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453995855875"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1447,355) scale(1,0.733333) translate(0,125.091)" width="15" x="1439.5" y="344"/></g>
  <g id="119">
   <use class="kv35" height="30" transform="rotate(0,1350.43,452.889) scale(1.42857,1.42857) translate(-401.914,-129.438)" width="15" x="1339.714285714286" xlink:href="#Disconnector:令克_0" y="431.4603174603175" zvalue="119"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453996249091" ObjectName="35kV1号站用变跌落刀闸"/>
   <cge:TPSR_Ref TObjectID="6192453996249091"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1350.43,452.889) scale(1.42857,1.42857) translate(-401.914,-129.438)" width="15" x="1339.714285714286" y="431.4603174603175"/></g>
  <g id="123">
   <use class="kv10" height="36" transform="rotate(0,818,684.857) scale(1.42857,-1.23457) translate(-242.4,-1235.37)" width="14" x="808" xlink:href="#Disconnector:手车刀闸_0" y="662.6349206349206" zvalue="124"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453996380163" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453996380163"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,818,684.857) scale(1.42857,-1.23457) translate(-242.4,-1235.37)" width="14" x="808" y="662.6349206349206"/></g>
  <g id="184">
   <use class="kv10" height="30" transform="rotate(0,1186.91,835) scale(1,0.733333) translate(0,299.636)" width="15" x="1179.412222949139" xlink:href="#Disconnector:刀闸_0" y="824" zvalue="175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453996838915" ObjectName="10kV2号站用变0626隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453996838915"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1186.91,835) scale(1,0.733333) translate(0,299.636)" width="15" x="1179.412222949139" y="824"/></g>
  <g id="238">
   <use class="kv10" height="30" transform="rotate(0,759.022,875) scale(1,0.733333) translate(0,314.182)" width="15" x="751.5222229491394" xlink:href="#Disconnector:刀闸_0" y="864" zvalue="238"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453997101059" ObjectName="10kV帕盆线0656隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453997101059"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,759.022,875) scale(1,0.733333) translate(0,314.182)" width="15" x="751.5222229491394" y="864"/></g>
  <g id="248">
   <use class="kv10" height="30" transform="rotate(0,1188.47,888.75) scale(1.42857,1.42857) translate(-353.326,-260.196)" width="15" x="1177.754761904762" xlink:href="#Disconnector:令克_0" y="867.3214285714286" zvalue="251"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453997166595" ObjectName="10kV2号站用变跌落刀闸"/>
   <cge:TPSR_Ref TObjectID="6192453997166595"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1188.47,888.75) scale(1.42857,1.42857) translate(-353.326,-260.196)" width="15" x="1177.754761904762" y="867.3214285714286"/></g>
  <g id="138">
   <use class="kv35" height="30" transform="rotate(0,1161,255) scale(1,0.733333) translate(0,88.7273)" width="15" x="1153.5" xlink:href="#Disconnector:刀闸_0" y="244" zvalue="439"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453998215171" ObjectName="35kV清王线3616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453998215171"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1161,255) scale(1,0.733333) translate(0,88.7273)" width="15" x="1153.5" y="244"/></g>
  <g id="134">
   <use class="kv35" height="30" transform="rotate(0,1161,387) scale(1,0.733333) translate(0,136.727)" width="15" x="1153.5" xlink:href="#Disconnector:刀闸_0" y="376" zvalue="443"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453998018563" ObjectName="35kV清王线3611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453998018563"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1161,387) scale(1,0.733333) translate(0,136.727)" width="15" x="1153.5" y="376"/></g>
  <g id="33">
   <use class="kv35" height="30" transform="rotate(270,1201,171) scale(-1,0.733333) translate(-2402,58.1818)" width="15" x="1193.5" xlink:href="#Disconnector:刀闸_0" y="160" zvalue="451"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453997625347" ObjectName="35kV清王线3619隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453997625347"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1201,171) scale(-1,0.733333) translate(-2402,58.1818)" width="15" x="1193.5" y="160"/></g>
  <g id="282">
   <use class="kv10" height="30" transform="rotate(0,877.022,875) scale(1,0.733333) translate(0,314.182)" width="15" x="869.5222229491394" xlink:href="#Disconnector:刀闸_0" y="864" zvalue="479"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453998608387" ObjectName="10kV盆都线0646隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453998608387"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,877.022,875) scale(1,0.733333) translate(0,314.182)" width="15" x="869.5222229491394" y="864"/></g>
  <g id="296">
   <use class="kv10" height="30" transform="rotate(0,987.022,875) scale(1,0.733333) translate(0,314.182)" width="15" x="979.5222229491394" xlink:href="#Disconnector:刀闸_0" y="864" zvalue="494"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453998936067" ObjectName="10kV王子树街面线0636隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453998936067"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,987.022,875) scale(1,0.733333) translate(0,314.182)" width="15" x="979.5222229491394" y="864"/></g>
  <g id="196">
   <use class="kv10" height="30" transform="rotate(0,1317.16,858) scale(1,0.733333) translate(0,308)" width="15" x="1309.662222949139" xlink:href="#Disconnector:刀闸_0" y="847" zvalue="521"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453999067139" ObjectName="10kV1号电容器0616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453999067139"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1317.16,858) scale(1,0.733333) translate(0,308)" width="15" x="1309.662222949139" y="847"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="71">
   <use class="kv35" height="20" transform="rotate(90,985.714,495.143) scale(1.42857,1.42857) translate(-293.571,-144.257)" width="10" x="978.5714285714286" xlink:href="#GroundDisconnector:地刀_0" y="480.8571428571429" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453995003907" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453995003907"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,985.714,495.143) scale(1.42857,1.42857) translate(-293.571,-144.257)" width="10" x="978.5714285714286" y="480.8571428571429"/></g>
  <g id="79">
   <use class="kv35" height="20" transform="rotate(270,960.678,358.531) scale(-1.42857,1.42857) translate(-1631.01,-103.274)" width="10" x="953.5355996625974" xlink:href="#GroundDisconnector:地刀_0" y="344.2455360959887" zvalue="79"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453995331587" ObjectName="35kV西掌线王子树T线36217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453995331587"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,960.678,358.531) scale(-1.42857,1.42857) translate(-1631.01,-103.274)" width="10" x="953.5355996625974" y="344.2455360959887"/></g>
  <g id="94">
   <use class="kv35" height="20" transform="rotate(270,960.678,210.531) scale(-1.42857,1.42857) translate(-1631.01,-58.8737)" width="10" x="953.5355996625974" xlink:href="#GroundDisconnector:地刀_0" y="196.2455360959887" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453995462659" ObjectName="35kV西掌线王子树T线36267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453995462659"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,960.678,210.531) scale(-1.42857,1.42857) translate(-1631.01,-58.8737)" width="10" x="953.5355996625974" y="196.2455360959887"/></g>
  <g id="97">
   <use class="kv35" height="20" transform="rotate(270,960.678,290.531) scale(-1.42857,1.42857) translate(-1631.01,-82.8737)" width="10" x="953.5355996625974" xlink:href="#GroundDisconnector:地刀_0" y="276.2455360959887" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453995593731" ObjectName="35kV西掌线王子树T线36260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453995593731"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,960.678,290.531) scale(-1.42857,1.42857) translate(-1631.01,-82.8737)" width="10" x="953.5355996625974" y="276.2455360959887"/></g>
  <g id="111">
   <use class="kv35" height="20" transform="rotate(270,1484.68,310.531) scale(-1.42857,1.42857) translate(-2521.81,-88.8737)" width="10" x="1477.535599662597" xlink:href="#GroundDisconnector:地刀_0" y="296.2455360959887" zvalue="112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453996118019" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453996118019"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1484.68,310.531) scale(-1.42857,1.42857) translate(-2521.81,-88.8737)" width="10" x="1477.535599662597" y="296.2455360959887"/></g>
  <g id="110">
   <use class="kv35" height="20" transform="rotate(270,1484.68,390.531) scale(-1.42857,1.42857) translate(-2521.81,-112.874)" width="10" x="1477.535599662597" xlink:href="#GroundDisconnector:地刀_0" y="376.2455360959887" zvalue="115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453995986947" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453995986947"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1484.68,390.531) scale(-1.42857,1.42857) translate(-2521.81,-112.874)" width="10" x="1477.535599662597" y="376.2455360959887"/></g>
  <g id="143">
   <use class="kv10" height="20" transform="rotate(270,1365.18,836.781) scale(-1.42857,1.42857) translate(-2318.66,-246.749)" width="10" x="1358.035599662597" xlink:href="#GroundDisconnector:地刀_0" y="822.4955360959887" zvalue="140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453997297667" ObjectName="10kV1号电容器06160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453997297667"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1365.18,836.781) scale(-1.42857,1.42857) translate(-2318.66,-246.749)" width="10" x="1358.035599662597" y="822.4955360959887"/></g>
  <g id="140">
   <use class="kv10" height="20" transform="rotate(270,1366.68,888.429) scale(-1.42857,1.42857) translate(-2321.21,-262.243)" width="10" x="1359.535599662597" xlink:href="#GroundDisconnector:地刀_0" y="874.1428571428571" zvalue="143"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453997428739" ObjectName="10kV1号电容器06167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453997428739"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1366.68,888.429) scale(-1.42857,1.42857) translate(-2321.21,-262.243)" width="10" x="1359.535599662597" y="874.1428571428571"/></g>
  <g id="148">
   <use class="kv10" height="20" transform="rotate(0,1280,827.714) scale(1.42857,1.42857) translate(-381.857,-244.029)" width="10" x="1272.857142857143" xlink:href="#GroundDisconnector:地刀_0" y="813.4285714285713" zvalue="151"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453996576771" ObjectName="10kV1号电容器06117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453996576771"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1280,827.714) scale(1.42857,1.42857) translate(-381.857,-244.029)" width="10" x="1272.857142857143" y="813.4285714285713"/></g>
  <g id="175">
   <use class="kv10" height="20" transform="rotate(0,1148,839.714) scale(1.42857,1.42857) translate(-342.257,-247.629)" width="10" x="1140.857142857143" xlink:href="#GroundDisconnector:地刀_0" y="825.4285714285713" zvalue="187"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453996707843" ObjectName="10kV2号站用变06267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453996707843"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1148,839.714) scale(1.42857,1.42857) translate(-342.257,-247.629)" width="10" x="1140.857142857143" y="825.4285714285713"/></g>
  <g id="233">
   <use class="kv10" height="20" transform="rotate(0,726.5,840.964) scale(1.42857,1.42857) translate(-215.807,-248.004)" width="10" x="719.3571428571429" xlink:href="#GroundDisconnector:地刀_0" y="826.6785714285714" zvalue="244"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453996969987" ObjectName="10kV帕盆线06567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453996969987"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,726.5,840.964) scale(1.42857,1.42857) translate(-215.807,-248.004)" width="10" x="719.3571428571429" y="826.6785714285714"/></g>
  <g id="137">
   <use class="kv35" height="20" transform="rotate(270,1198.68,358.531) scale(-1.42857,1.42857) translate(-2035.61,-103.274)" width="10" x="1191.535599662597" xlink:href="#GroundDisconnector:地刀_0" y="344.2455360959887" zvalue="441"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453998149635" ObjectName="35kV清王线36117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453998149635"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1198.68,358.531) scale(-1.42857,1.42857) translate(-2035.61,-103.274)" width="10" x="1191.535599662597" y="344.2455360959887"/></g>
  <g id="41">
   <use class="kv35" height="20" transform="rotate(270,1198.68,210.531) scale(-1.42857,1.42857) translate(-2035.61,-58.8737)" width="10" x="1191.535599662597" xlink:href="#GroundDisconnector:地刀_0" y="196.2455360959887" zvalue="447"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453997887491" ObjectName="35kV清王线36167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453997887491"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1198.68,210.531) scale(-1.42857,1.42857) translate(-2035.61,-58.8737)" width="10" x="1191.535599662597" y="196.2455360959887"/></g>
  <g id="34">
   <use class="kv35" height="20" transform="rotate(270,1198.68,290.531) scale(-1.42857,1.42857) translate(-2035.61,-82.8737)" width="10" x="1191.535599662597" xlink:href="#GroundDisconnector:地刀_0" y="276.2455360959887" zvalue="449"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453997756419" ObjectName="35kV清王线36160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453997756419"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1198.68,290.531) scale(-1.42857,1.42857) translate(-2035.61,-82.8737)" width="10" x="1191.535599662597" y="276.2455360959887"/></g>
  <g id="278">
   <use class="kv10" height="20" transform="rotate(0,842,839.714) scale(1.42857,1.42857) translate(-250.457,-247.629)" width="10" x="834.8571428571429" xlink:href="#GroundDisconnector:地刀_0" y="825.4285714285714" zvalue="484"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453998477315" ObjectName="10kV盆都线06467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453998477315"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,842,839.714) scale(1.42857,1.42857) translate(-250.457,-247.629)" width="10" x="834.8571428571429" y="825.4285714285714"/></g>
  <g id="292">
   <use class="kv10" height="20" transform="rotate(0,953.25,838.214) scale(1.42857,1.42857) translate(-283.832,-247.179)" width="10" x="946.1071428571429" xlink:href="#GroundDisconnector:地刀_0" y="823.9285714285714" zvalue="499"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453998804995" ObjectName="10kV王子树街面线06367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453998804995"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,953.25,838.214) scale(1.42857,1.42857) translate(-283.832,-247.179)" width="10" x="946.1071428571429" y="823.9285714285714"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="90">
   <use class="kv35" height="30" transform="rotate(0,923.088,118) scale(1,1) translate(0,0)" width="7" x="919.5877770508608" xlink:href="#ACLineSegment:线路_0" y="103" zvalue="90"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249309249542" ObjectName="35kV西掌线王子树T线"/>
   <cge:TPSR_Ref TObjectID="8444249309249542_5066549680472065"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,923.088,118) scale(1,1) translate(0,0)" width="7" x="919.5877770508608" y="103"/></g>
  <g id="42">
   <use class="kv35" height="20" transform="rotate(0,1161.09,118) scale(1.4,1.5) translate(-330.739,-34.3333)" width="5" x="1157.587777050861" xlink:href="#ACLineSegment:线路（规范制图）_0" y="103" zvalue="445"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249322749956" ObjectName="35kV清王线"/>
   <cge:TPSR_Ref TObjectID="8444249322749956_5066549680472065"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1161.09,118) scale(1.4,1.5) translate(-330.739,-34.3333)" width="5" x="1157.587777050861" y="103"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="101">
   <use class="kv35" height="40" transform="rotate(90,1006,171) scale(1,1) translate(0,0)" width="30" x="991" xlink:href="#Accessory:带熔断器的线路PT1_0" y="151" zvalue="103"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453995724803" ObjectName="35kV西掌线王子树T线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1006,171) scale(1,1) translate(0,0)" width="30" x="991" y="151"/></g>
  <g id="104">
   <use class="kv35" height="26" transform="rotate(0,902,167) scale(1,1) translate(0,0)" width="12" x="896" xlink:href="#Accessory:避雷器1_0" y="154" zvalue="105"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453995790339" ObjectName="35kV西掌线王子树T线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,902,167) scale(1,1) translate(0,0)" width="12" x="896" y="154"/></g>
  <g id="115">
   <use class="kv35" height="40" transform="rotate(180,1434.59,264.5) scale(1.25,1.25) translate(-281.918,-47.9)" width="40" x="1409.587777050861" xlink:href="#Accessory:五卷PT_0" y="239.5" zvalue="117"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453996183555" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,1434.59,264.5) scale(1.25,1.25) translate(-281.918,-47.9)" width="40" x="1409.587777050861" y="239.5"/></g>
  <g id="122">
   <use class="kv10" height="40" transform="rotate(180,805.25,621.5) scale(1.25,1.25) translate(-156.05,-119.3)" width="40" x="780.25" xlink:href="#Accessory:五卷PT_0" y="596.5" zvalue="123"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453996314627" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,805.25,621.5) scale(1.25,1.25) translate(-156.05,-119.3)" width="40" x="780.25" y="596.5"/></g>
  <g id="235">
   <use class="kv10" height="26" transform="rotate(0,785.088,912.842) scale(-1,1) translate(-1570.18,0)" width="12" x="779.0877770508607" xlink:href="#Accessory:避雷器1_0" y="899.8423767927778" zvalue="242"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453997035523" ObjectName="10kV帕盆线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,785.088,912.842) scale(-1,1) translate(-1570.18,0)" width="12" x="779.0877770508607" y="899.8423767927778"/></g>
  <g id="32">
   <use class="kv35" height="40" transform="rotate(90,1244,171) scale(1,1) translate(0,0)" width="30" x="1229" xlink:href="#Accessory:带熔断器的线路PT1_0" y="151" zvalue="453"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453997559811" ObjectName="35kV清王线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1244,171) scale(1,1) translate(0,0)" width="30" x="1229" y="151"/></g>
  <g id="30">
   <use class="kv35" height="26" transform="rotate(0,1140,167) scale(1,1) translate(0,0)" width="12" x="1134" xlink:href="#Accessory:避雷器1_0" y="154" zvalue="455"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453997494275" ObjectName="35kV清王线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1140,167) scale(1,1) translate(0,0)" width="12" x="1134" y="154"/></g>
  <g id="280">
   <use class="kv10" height="26" transform="rotate(0,903.088,912.842) scale(-1,1) translate(-1806.18,0)" width="12" x="897.0877770508607" xlink:href="#Accessory:避雷器1_0" y="899.8423767010371" zvalue="482"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453998542851" ObjectName="10kV盆都线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,903.088,912.842) scale(-1,1) translate(-1806.18,0)" width="12" x="897.0877770508607" y="899.8423767010371"/></g>
  <g id="294">
   <use class="kv10" height="26" transform="rotate(0,1018.34,912.842) scale(-1,1) translate(-2036.68,0)" width="12" x="1012.337777050861" xlink:href="#Accessory:避雷器1_0" y="899.8423767089844" zvalue="497"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453998870531" ObjectName="10kV王子树街面线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1018.34,912.842) scale(-1,1) translate(-2036.68,0)" width="12" x="1012.337777050861" y="899.8423767089844"/></g>
  <g id="181">
   <use class="kv10" height="26" transform="rotate(0,1279.84,925.388) scale(-1,1) translate(-2559.68,0)" width="12" x="1273.837777050861" xlink:href="#Accessory:避雷器1_0" y="912.3882138457001" zvalue="541"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453999198211" ObjectName="10kV1号电容器避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1279.84,925.388) scale(-1,1) translate(-2559.68,0)" width="12" x="1273.837777050861" y="912.3882138457001"/></g>
  <g id="183">
   <use class="kv10" height="26" transform="rotate(0,1166.84,883.617) scale(-1,1) translate(-2333.68,0)" width="12" x="1160.837777050861" xlink:href="#Accessory:避雷器1_0" y="870.6166666666667" zvalue="543"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453999263747" ObjectName="10kV2号站用变避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1166.84,883.617) scale(-1,1) translate(-2333.68,0)" width="12" x="1160.837777050861" y="870.6166666666667"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="205">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="205" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,134.357,520.111) scale(1,1) translate(0,0)" writing-mode="lr" x="134.55" xml:space="preserve" y="525.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133868843011" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="204">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="204" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,134.357,543.111) scale(1,1) translate(0,0)" writing-mode="lr" x="134.55" xml:space="preserve" y="548.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133868908547" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="203">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,134.357,566.111) scale(1,1) translate(0,0)" writing-mode="lr" x="134.55" xml:space="preserve" y="571.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133868974083" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,134.357,494.611) scale(1,1) translate(0,0)" writing-mode="lr" x="134.55" xml:space="preserve" y="499.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133869105155" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.857,201.111) scale(1,1) translate(0,0)" writing-mode="lr" x="138.05" xml:space="preserve" y="207.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133869236227" ObjectName="F"/>
   </metadata>
  </g>
  <g id="200">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,252.19,520) scale(1,1) translate(0,0)" writing-mode="lr" x="252.39" xml:space="preserve" y="524.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133869367299" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,252.19,543) scale(1,1) translate(0,0)" writing-mode="lr" x="252.39" xml:space="preserve" y="547.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133869432835" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="61">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,252.19,566) scale(1,1) translate(0,0)" writing-mode="lr" x="252.39" xml:space="preserve" y="570.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133869498371" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="60">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,252.19,493.5) scale(1,1) translate(0,0)" writing-mode="lr" x="252.39" xml:space="preserve" y="498.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133869629443" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="59">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="59" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.857,226) scale(1,1) translate(0,0)" writing-mode="lr" x="138.05" xml:space="preserve" y="232.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133869760515" ObjectName="F"/>
   </metadata>
  </g>
  <g id="58">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,137.857,248.194) scale(1,1) translate(-1.89601e-14,0)" writing-mode="lr" x="138.08" xml:space="preserve" y="254.68" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133870350339" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,137.857,271.194) scale(1,1) translate(0,0)" writing-mode="lr" x="138.05" xml:space="preserve" y="277.68" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133870415875" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,134.607,594.111) scale(1,1) translate(0,0)" writing-mode="lr" x="134.8" xml:space="preserve" y="599.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133869301763" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,252.899,593) scale(1,1) translate(0,0)" writing-mode="lr" x="253.09" xml:space="preserve" y="597.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133869826051" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="53" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,137.857,178) scale(1,1) translate(0,0)" writing-mode="lr" x="138.01" xml:space="preserve" y="184.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133915176963" ObjectName=""/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="52" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,314.857,177) scale(1,1) translate(0,0)" writing-mode="lr" x="315.01" xml:space="preserve" y="183.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133915242499" ObjectName=""/>
   </metadata>
  </g>
  <g id="159">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="159" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,923.088,14.5) scale(1,1) translate(0,0)" writing-mode="lr" x="923.28" xml:space="preserve" y="19.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133872971779" ObjectName="P"/>
   </metadata>
  </g>
  <g id="169">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="169" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,923.088,35.5) scale(1,1) translate(0,0)" writing-mode="lr" x="923.28" xml:space="preserve" y="40.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133873037315" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="170">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="170" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,923.088,56.5) scale(1,1) translate(0,0)" writing-mode="lr" x="923.28" xml:space="preserve" y="61.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133873102851" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="178">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="178" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,923.088,76.5) scale(1,1) translate(0,0)" writing-mode="lr" x="923.28" xml:space="preserve" y="81.41" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133873823747" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="179">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="179" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1450.57,203.857) scale(1,1) translate(-9.36299e-13,0)" writing-mode="lr" x="1450.77" xml:space="preserve" y="208.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133869105155" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="182">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="182" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1129.64,458.877) scale(1,1) translate(0,0)" writing-mode="lr" x="1129.84" xml:space="preserve" y="463.79" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133869891587" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="185">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="185" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1130.64,486.877) scale(1,1) translate(0,0)" writing-mode="lr" x="1130.84" xml:space="preserve" y="491.79" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133869957123" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="186">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="186" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1130.64,513.877) scale(1,1) translate(0,0)" writing-mode="lr" x="1130.84" xml:space="preserve" y="518.79" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133870153731" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="187">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="187" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1134.64,643.877) scale(1,1) translate(0,0)" writing-mode="lr" x="1134.84" xml:space="preserve" y="648.79" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133870022659" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="189">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="189" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1134.64,670.877) scale(1,1) translate(0,0)" writing-mode="lr" x="1134.84" xml:space="preserve" y="675.79" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133870088195" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="190">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="190" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1134.64,697.877) scale(1,1) translate(0,0)" writing-mode="lr" x="1134.84" xml:space="preserve" y="702.79" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133870481411" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="207">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="207" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,810.571,561.857) scale(1,1) translate(0,0)" writing-mode="lr" x="810.77" xml:space="preserve" y="566.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133869629443" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="117" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,756.958,979.75) scale(1,1) translate(0,0)" writing-mode="lr" x="757.15" xml:space="preserve" y="984.66" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133877690371" ObjectName="P"/>
   </metadata>
  </g>
  <g id="174">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="174" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,874.958,979.75) scale(1,1) translate(0,0)" writing-mode="lr" x="875.15" xml:space="preserve" y="984.66" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133878607877" ObjectName="P"/>
   </metadata>
  </g>
  <g id="191">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="191" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,984.958,979.75) scale(1,1) translate(0,0)" writing-mode="lr" x="985.15" xml:space="preserve" y="984.66" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133880049667" ObjectName="P"/>
   </metadata>
  </g>
  <g id="192">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="192" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,756.958,1002.75) scale(1,1) translate(0,0)" writing-mode="lr" x="757.15" xml:space="preserve" y="1007.66" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133877755907" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="195">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="195" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,874.958,1002.75) scale(1,1) translate(0,0)" writing-mode="lr" x="875.15" xml:space="preserve" y="1007.66" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133878673413" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="197">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="197" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,983.958,1003.75) scale(1,1) translate(0,0)" writing-mode="lr" x="984.15" xml:space="preserve" y="1008.66" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133880115205" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="201">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="201" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,756.958,1025.75) scale(1,1) translate(0,0)" writing-mode="lr" x="757.15" xml:space="preserve" y="1030.66" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133877821443" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="202">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="202" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,874.958,1025.75) scale(1,1) translate(0,0)" writing-mode="lr" x="875.15" xml:space="preserve" y="1030.66" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133878738949" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="206">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="206" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,983.958,1026.75) scale(1,1) translate(0,0)" writing-mode="lr" x="984.15" xml:space="preserve" y="1031.66" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133880180741" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="208">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="208" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1161.09,22.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1161.28" xml:space="preserve" y="27.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133876117507" ObjectName="P"/>
   </metadata>
  </g>
  <g id="209">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="209" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1161.09,45.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1161.28" xml:space="preserve" y="50.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133876183043" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="210">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="210" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1161.09,68.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1161.28" xml:space="preserve" y="73.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133876248579" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="263">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="263" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1178.6,987.75) scale(1,1) translate(3.77565e-13,0)" writing-mode="lr" x="1178.13" xml:space="preserve" y="992.42" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133881491459" ObjectName="P"/>
   </metadata>
  </g>
  <g id="264">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="264" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1178.6,1009.12) scale(1,1) translate(3.77565e-13,0)" writing-mode="lr" x="1178.13" xml:space="preserve" y="1013.77" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133881556997" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="265">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="265" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1178.6,1030.75) scale(1,1) translate(3.77565e-13,0)" writing-mode="lr" x="1178.13" xml:space="preserve" y="1035.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133881622533" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="266">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="266" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1317.76,1008) scale(1,1) translate(-8.47829e-13,0)" writing-mode="lr" x="1317.29" xml:space="preserve" y="1012.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133882408963" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="267">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="267" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1317.76,1029) scale(1,1) translate(-8.47829e-13,0)" writing-mode="lr" x="1317.29" xml:space="preserve" y="1033.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133882474499" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,344.482,318.5) scale(0.708333,0.665547) translate(137.471,155.037)" width="30" x="333.86" xlink:href="#State:红绿圆(方形)_0" y="308.52" zvalue="372"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374924120065" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,344.482,318.5) scale(0.708333,0.665547) translate(137.471,155.037)" width="30" x="333.86" y="308.52"/></g>
  <g id="56">
   <use height="30" transform="rotate(0,248.857,318.5) scale(0.708333,0.665547) translate(98.0956,155.037)" width="30" x="238.23" xlink:href="#State:红绿圆(方形)_0" y="308.52" zvalue="373"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562957063225351" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,248.857,318.5) scale(0.708333,0.665547) translate(98.0956,155.037)" width="30" x="238.23" y="308.52"/></g>
  <g id="830">
   <use height="30" transform="rotate(0,326.812,141.464) scale(1.27778,1.03333) translate(-58.5462,-4.06335)" width="90" x="269.31" xlink:href="#State:全站检修_0" y="125.96" zvalue="641"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549680472065" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,326.812,141.464) scale(1.27778,1.03333) translate(-58.5462,-4.06335)" width="90" x="269.31" y="125.96"/></g>
  <g id="772">
   <use height="30" transform="rotate(0,296.235,385) scale(0.910937,0.8) translate(25.4004,93.25)" width="80" x="259.8" xlink:href="#State:间隔模板_0" y="373" zvalue="643"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629499605450757" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,296.235,385) scale(0.910937,0.8) translate(25.4004,93.25)" width="80" x="259.8" y="373"/></g>
  <g id="1082">
   <use height="30" transform="rotate(0,94.2857,312.539) scale(0.910937,0.8) translate(5.65583,75.1347)" width="80" x="57.85" xlink:href="#State:间隔模板_0" y="300.54" zvalue="644"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629500398370818" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,94.2857,312.539) scale(0.910937,0.8) translate(5.65583,75.1347)" width="80" x="57.85" y="300.54"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="EnergyConsumerClass">
  <g id="268">
   <use class="kv10" height="30" transform="rotate(180,755.958,928) scale(1.25,1.25) translate(-149.317,-181.85)" width="15" x="746.5833873732452" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="909.25" zvalue="475"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453998280707" ObjectName="10kV帕盆线"/>
   <cge:TPSR_Ref TObjectID="6192453998280707"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,755.958,928) scale(1.25,1.25) translate(-149.317,-181.85)" width="15" x="746.5833873732452" y="909.25"/></g>
  <g id="274">
   <use class="kv10" height="30" transform="rotate(180,873.958,928) scale(1.25,1.25) translate(-172.917,-181.85)" width="15" x="864.5833873732452" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="909.25" zvalue="489"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453998346243" ObjectName="10kV盆都线"/>
   <cge:TPSR_Ref TObjectID="6192453998346243"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,873.958,928) scale(1.25,1.25) translate(-172.917,-181.85)" width="15" x="864.5833873732452" y="909.25"/></g>
  <g id="288">
   <use class="kv10" height="30" transform="rotate(180,983.958,928) scale(1.25,1.25) translate(-194.917,-181.85)" width="15" x="974.5833873732452" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="909.25" zvalue="504"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453998673923" ObjectName="10kV王子树街面线"/>
   <cge:TPSR_Ref TObjectID="6192453998673923"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,983.958,928) scale(1.25,1.25) translate(-194.917,-181.85)" width="15" x="974.5833873732452" y="909.25"/></g>
  <g id="298">
   <use class="kv10" height="30" transform="rotate(0,1188.35,940.438) scale(2.325,1.9375) translate(-663.982,-440.988)" width="20" x="1165.1" xlink:href="#EnergyConsumer:站用变11_0" y="911.375" zvalue="506"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453999001603" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1188.35,940.438) scale(2.325,1.9375) translate(-663.982,-440.988)" width="20" x="1165.1" y="911.375"/></g>
  <g id="11">
   <use class="kv35" height="30" transform="rotate(0,1350.31,515.653) scale(-2.48224,2.06854) translate(-1879.47,-250.341)" width="20" x="1325.487083160172" xlink:href="#EnergyConsumer:站用变11_0" y="484.625" zvalue="549"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453999329283" ObjectName="35kV1号站用变"/>
   <cge:TPSR_Ref TObjectID="6192453999329283"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1350.31,515.653) scale(-2.48224,2.06854) translate(-1879.47,-250.341)" width="20" x="1325.487083160172" y="484.625"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="9">
   <use class="kv10" height="30" transform="rotate(90,1313.76,931.25) scale(1.30833,1.33106) translate(-300.363,-226.654)" width="60" x="1274.511363636364" xlink:href="#Compensator:并联电容器12121_0" y="911.2840909090911" zvalue="530"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453999132675" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453999132675"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1313.76,931.25) scale(1.30833,1.33106) translate(-300.363,-226.654)" width="60" x="1274.511363636364" y="911.2840909090911"/></g>
 </g>
</svg>