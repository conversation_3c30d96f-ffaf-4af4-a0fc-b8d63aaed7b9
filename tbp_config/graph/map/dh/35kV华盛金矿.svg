<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549589114882" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:T接线（中间连接点）_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="15.1"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.666666666666667" x2="3.666666666666667" y1="0.3500000000000032" y2="30.01666666666667"/>
  </symbol>
  <symbol id="Accessory:接地变20201012_0" viewBox="0,0,23,35">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.05" x2="21.05" y1="24.5" y2="24.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="20.5" y1="17.5" y2="17.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.5" x2="21.5" y1="23.5" y2="23.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5" x2="20.5" y1="17.5" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="16.5" y1="17.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="7.5" y1="25.5" y2="29.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.2" x2="7.2" y1="24.25" y2="32.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.5" x2="22.5" y1="22.5" y2="22.5"/>
   <path d="M 6.25 29.75 L 8.25 29.75 L 7.25 31.75 L 6.25 29.75 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="7.07" cy="6.66" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.061718118722352" x2="7.061718118722352" y1="3.664465958746224" y2="6.263454058763156"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.655265608193801" x2="7.061718118722348" y1="8.862442158780071" y2="6.263454058763138"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.468170629250878" x2="7.061718118722331" y1="8.862442158780071" y2="6.263454058763138"/>
   <ellipse cx="7.07" cy="17.66" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.061718118722352" x2="7.061718118722352" y1="14.66446595874622" y2="17.26345405876316"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.655265608193801" x2="7.061718118722348" y1="19.86244215878007" y2="17.26345405876314"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.468170629250878" x2="7.061718118722331" y1="19.86244215878007" y2="17.26345405876314"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="4.416666666666668" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.249999999999998" y2="20.16666666666667"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.08333333333333" x2="9.833333333333332" y1="16.5" y2="18.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14" y1="9.75" y2="16.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="11" y1="11.58333333333333" y2="9.666666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="9.75" y1="11.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="15.08333333333333" y1="8.416666666666668" y2="25.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_1" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.96,13) scale(1,1) translate(0,0)" width="4.42" x="12.75" y="9"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="5.416666666666668" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_2" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="20" y1="6" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="10.08333333333333" y1="6.000000000000004" y2="25.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="Accessory:电压互感器22_0" viewBox="0,0,45,48">
   <use terminal-index="0" type="0" x="25.25" xlink:href="#terminal" y="47.78457520218115"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.5,30.95) scale(1,-1) translate(0,-1334.05)" width="6" x="10.5" y="23.95"/>
   <path d="M 11.1417 7.99291 L 6.83333 8.25 L 7.91667 6.08333 L 9.00709 4.10958 L 10.1738 6.10958 L 11.1417 7.99291" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="13.5" y1="19.78457520218114" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.25" x2="25.25" y1="40.5" y2="47.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="37.25" x2="13.58333333333333" y1="40.5" y2="40.5"/>
   <ellipse cx="18.48" cy="7" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.48286746466329" x2="18.48286746466329" y1="4.73872160023371" y2="7.412907200077882"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.48286746466327" x2="15.7990682626599" y1="7.412907200077907" y2="8.749999999999991"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.48286746466329" x2="21.16666666666666" y1="7.412907200077907" y2="8.749999999999991"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,36.96,25.94) scale(1,-1) translate(0,-1133.72)" width="7.58" x="33.17" y="17.87"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="34.01790853551448" y2="24.11790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.7861111111111" x2="39.3" y1="11.60124186884782" y2="11.60124186884782"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="40.43457520218114" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.82777777777777" x2="39.93888888888888" y1="12.98282081621623" y2="12.98282081621623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.55" x2="41.21666666666666" y1="14.36439976358464" y2="14.36439976358464"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="34.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="18.01790853551448" y2="14.41790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="40.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <ellipse cx="9.17" cy="6.59" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.59" cy="15.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490864" x2="10.91228003290526" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="13.59607923490865" y1="12.74178419246191" y2="15.41596979230608"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="16.27987843691203" y1="15.41596979230611" y2="16.75306259222819"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV华盛金矿" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="72.08" y="305.5" zvalue="41"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="3" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,108.518,317.5) scale(1,1) translate(0,0)" width="72.88" x="72.08" y="305.5" zvalue="41"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,108.518,317.5) scale(1,1) translate(0,0)" writing-mode="lr" x="108.52" xml:space="preserve" y="322" zvalue="41">信号一览</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="244.71" x="60.29" xlink:href="logo.png" y="39.21"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,182.643,69.2143) scale(1,1) translate(0,0)" writing-mode="lr" x="182.64" xml:space="preserve" y="72.70999999999999" zvalue="113"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,168.75,68.4443) scale(1,1) translate(0,0)" writing-mode="lr" x="168.75" xml:space="preserve" y="77.44" zvalue="114">35kV华盛金矿</text>
  <line fill="none" id="39" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.75000000000023" x2="381.7499999999998" y1="148.6204926140824" y2="148.6204926140824" zvalue="5"/>
  <line fill="none" id="38" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382.75" x2="382.75" y1="6.75" y2="1036.75" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="196.75" y1="162.0000000000001" y2="162.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="196.75" y1="188.0000000000001" y2="188.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="15.75" y1="162.0000000000001" y2="188.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="196.75" y1="162.0000000000001" y2="188.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="377.75" y1="162.0000000000001" y2="162.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="377.75" y1="188.0000000000001" y2="188.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="196.75" y1="162.0000000000001" y2="188.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="377.75" x2="377.75" y1="162.0000000000001" y2="188.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="196.75" y1="188.0000000000001" y2="188.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="196.75" y1="212.2500000000001" y2="212.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="15.75" y1="188.0000000000001" y2="212.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="196.75" y1="188.0000000000001" y2="212.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="377.75" y1="188.0000000000001" y2="188.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="377.75" y1="212.2500000000001" y2="212.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="196.75" y1="188.0000000000001" y2="212.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="377.75" x2="377.75" y1="188.0000000000001" y2="212.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="196.75" y1="212.2500000000001" y2="212.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="196.75" y1="235.0000000000001" y2="235.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="15.75" y1="212.2500000000001" y2="235.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="196.75" y1="212.2500000000001" y2="235.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="377.75" y1="212.2500000000001" y2="212.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="377.75" y1="235.0000000000001" y2="235.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="196.75" y1="212.2500000000001" y2="235.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="377.75" x2="377.75" y1="212.2500000000001" y2="235.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="196.75" y1="235.0000000000001" y2="235.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="196.75" y1="257.7500000000001" y2="257.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="15.75" y1="235.0000000000001" y2="257.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="196.75" y1="235.0000000000001" y2="257.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="377.75" y1="235.0000000000001" y2="235.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="377.75" y1="257.7500000000001" y2="257.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="196.75" y1="235.0000000000001" y2="257.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="377.75" x2="377.75" y1="235.0000000000001" y2="257.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="196.75" y1="257.7500000000001" y2="257.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="196.75" y1="280.5000000000001" y2="280.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="15.75" y1="257.7500000000001" y2="280.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="196.75" y1="257.7500000000001" y2="280.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="377.75" y1="257.7500000000001" y2="257.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="377.75" y1="280.5000000000001" y2="280.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="196.75" y1="257.7500000000001" y2="280.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="377.75" x2="377.75" y1="257.7500000000001" y2="280.5000000000001"/>
  <line fill="none" id="36" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.75000000000023" x2="381.7499999999998" y1="618.6204926140824" y2="618.6204926140824" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.75" x2="104.75" y1="933.75" y2="933.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.75" x2="104.75" y1="972.9132999999999" y2="972.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.75" x2="14.75" y1="933.75" y2="972.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="104.75" y1="933.75" y2="972.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="374.75" y1="933.75" y2="933.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="374.75" y1="972.9132999999999" y2="972.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="104.75" y1="933.75" y2="972.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="374.75" x2="374.75" y1="933.75" y2="972.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.75" x2="104.75" y1="972.91327" y2="972.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.75" x2="104.75" y1="1000.83167" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.75" x2="14.75" y1="972.91327" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="104.75" y1="972.91327" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="194.75" y1="972.91327" y2="972.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="194.75" y1="1000.83167" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="104.75" y1="972.91327" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.75" x2="194.75" y1="972.91327" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.7500000000001" x2="284.7500000000001" y1="972.91327" y2="972.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.7500000000001" x2="284.7500000000001" y1="1000.83167" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.7500000000001" x2="194.7500000000001" y1="972.91327" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.7500000000001" x2="284.7500000000001" y1="972.91327" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.75" x2="374.75" y1="972.91327" y2="972.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.75" x2="374.75" y1="1000.83167" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.75" x2="284.75" y1="972.91327" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="374.75" x2="374.75" y1="972.91327" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.75" x2="104.75" y1="1000.8316" y2="1000.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.75" x2="104.75" y1="1028.75" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.75" x2="14.75" y1="1000.8316" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="104.75" y1="1000.8316" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="194.75" y1="1000.8316" y2="1000.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="194.75" y1="1028.75" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="104.75" y1="1000.8316" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.75" x2="194.75" y1="1000.8316" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.7500000000001" x2="284.7500000000001" y1="1000.8316" y2="1000.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.7500000000001" x2="284.7500000000001" y1="1028.75" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.7500000000001" x2="194.7500000000001" y1="1000.8316" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.7500000000001" x2="284.7500000000001" y1="1000.8316" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.75" x2="374.75" y1="1000.8316" y2="1000.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.75" x2="374.75" y1="1028.75" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.75" x2="284.75" y1="1000.8316" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="374.75" x2="374.75" y1="1000.8316" y2="1028.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.75,954.75) scale(1,1) translate(0,0)" writing-mode="lr" x="60.75" xml:space="preserve" y="960.75" zvalue="11">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.75,988.75) scale(1,1) translate(0,0)" writing-mode="lr" x="57.75" xml:space="preserve" y="994.75" zvalue="12">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,239.75,988.75) scale(1,1) translate(0,0)" writing-mode="lr" x="239.75" xml:space="preserve" y="994.75" zvalue="13">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.75,1016.75) scale(1,1) translate(0,0)" writing-mode="lr" x="56.75" xml:space="preserve" y="1022.75" zvalue="14">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.75,1015.75) scale(1,1) translate(0,0)" writing-mode="lr" x="237.75" xml:space="preserve" y="1021.75" zvalue="15">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.25,648.25) scale(1,1) translate(0,2.09582e-13)" writing-mode="lr" x="80.25" xml:space="preserve" y="652.7500000000001" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,213.149,315.591) scale(1,1) translate(0,0)" writing-mode="lr" x="213.15" xml:space="preserve" y="320.09" zvalue="19">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,318.149,315.591) scale(1,1) translate(0,0)" writing-mode="lr" x="318.15" xml:space="preserve" y="320.09" zvalue="20">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,217.304,954.5) scale(1,1) translate(0,0)" writing-mode="lr" x="217.3" xml:space="preserve" y="960.5" zvalue="28">HS JK-01-2017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,151.804,1014.75) scale(1,1) translate(0,0)" writing-mode="lr" x="151.8" xml:space="preserve" y="1020.75" zvalue="29">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.75,174.75) scale(1,1) translate(0,0)" writing-mode="lr" x="53.75" xml:space="preserve" y="180.25" zvalue="30">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.75,174.75) scale(1,1) translate(0,0)" writing-mode="lr" x="233.75" xml:space="preserve" y="180.25" zvalue="31">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" x="755.0078125" xml:space="preserve" y="935.7656225628323" zvalue="53">#2主变（镍矿</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="755.0078125" xml:space="preserve" y="951.7656225628323" zvalue="53">变）S9-630kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,708.984,718.905) scale(1,1) translate(9.30568e-13,0)" writing-mode="lr" x="708.98" xml:space="preserve" y="723.4" zvalue="54">342</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,738.317,621.127) scale(1,1) translate(0,0)" writing-mode="lr" x="738.3200000000001" xml:space="preserve" y="625.63" zvalue="55">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,820.873,647.794) scale(1,1) translate(0,0)" writing-mode="lr" x="820.87" xml:space="preserve" y="652.29" zvalue="56">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,716.095,513.349) scale(1,1) translate(0,0)" writing-mode="lr" x="716.1" xml:space="preserve" y="517.85" zvalue="57">A3R2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1031.2,282.495) scale(1,1) translate(0,0)" writing-mode="lr" x="1031.2" xml:space="preserve" y="286.99" zvalue="66">A3R1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" x="1067.890625" xml:space="preserve" y="902.2031225628323" zvalue="69">#1主变            </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1067.890625" xml:space="preserve" y="918.2031225628323" zvalue="69">S9-2500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1020.45,685.349) scale(1,1) translate(0,0)" writing-mode="lr" x="1020.45" xml:space="preserve" y="689.85" zvalue="70">341</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1053.23,585.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1053.23" xml:space="preserve" y="590.0700000000001" zvalue="73">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1133.76,612.238) scale(1,1) translate(0,0)" writing-mode="lr" x="1133.76" xml:space="preserve" y="616.74" zvalue="74">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1171.54,380.016) scale(1,1) translate(0,0)" writing-mode="lr" x="1171.54" xml:space="preserve" y="384.52" zvalue="82">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1202.76,489.46) scale(1,1) translate(0,0)" writing-mode="lr" x="1202.76" xml:space="preserve" y="493.96" zvalue="84">39017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1225.84,113.615) scale(1,1) translate(0,0)" writing-mode="lr" x="1225.84" xml:space="preserve" y="118.12" zvalue="107">35kV帕放线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,965,191.462) scale(1,1) translate(0,0)" writing-mode="lr" x="965" xml:space="preserve" y="195.96" zvalue="109">35kV帕放线华盛金矿T线</text>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="AccessoryClass">
  <g id="55">
   <use class="kv35" height="35" transform="rotate(0,764.762,838.96) scale(3.25397,3.25397) translate(-503.817,-541.689)" width="23" x="727.3412659357466" xlink:href="#Accessory:接地变20201012_0" y="782.015873015873" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449866825733" ObjectName="#2主变（镍矿变）"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,764.762,838.96) scale(3.25397,3.25397) translate(-503.817,-541.689)" width="23" x="727.3412659357466" y="782.015873015873"/></g>
  <g id="65">
   <use class="kv35" height="20" transform="rotate(0,693.04,607.96) scale(2.19444,2.19444) translate(-365.28,-318.971)" width="20" x="671.0952380952381" xlink:href="#Accessory:线路PT3_0" y="586.015873015873" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449867153413" ObjectName="线路PT1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,693.04,607.96) scale(2.19444,2.19444) translate(-365.28,-318.971)" width="20" x="671.0952380952381" y="586.015873015873"/></g>
  <g id="86">
   <use class="kv35" height="35" transform="rotate(0,1077.65,805.405) scale(3.25397,3.25397) translate(-720.55,-518.446)" width="23" x="1040.230154824636" xlink:href="#Accessory:接地变20201012_0" y="748.4603174603175" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449867546630" ObjectName="#1主变"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1077.65,805.405) scale(3.25397,3.25397) translate(-720.55,-518.446)" width="23" x="1040.230154824636" y="748.4603174603175"/></g>
  <g id="82">
   <use class="kv35" height="20" transform="rotate(0,1005.93,572.405) scale(2.19444,2.19444) translate(-535.586,-299.618)" width="20" x="983.984126984127" xlink:href="#Accessory:线路PT3_0" y="550.4603174603175" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449867284485" ObjectName="线路PT2"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1005.93,572.405) scale(2.19444,2.19444) translate(-535.586,-299.618)" width="20" x="983.984126984127" y="550.4603174603175"/></g>
  <g id="94">
   <use class="kv35" height="48" transform="rotate(0,1348.37,491.534) scale(2.01235,-2.01235) translate(-655.545,-711.498)" width="45" x="1303.095238095238" xlink:href="#Accessory:电压互感器22_0" y="443.2380952380952" zvalue="87"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449867808773" ObjectName="35kV帕放线华盛金矿T线电压互感器"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,1348.37,491.534) scale(2.01235,-2.01235) translate(-655.545,-711.498)" width="45" x="1303.095238095238" y="443.2380952380952"/></g>
 </g>
 <g id="BreakerClass">
  <g id="57">
   <use class="kv35" height="20" transform="rotate(0,751.429,719.905) scale(1.66667,1.5) translate(-297.238,-234.968)" width="10" x="743.0952345984324" xlink:href="#Breaker:开关_0" y="704.9047619047619" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924528046085" ObjectName="#2主变342断路器"/>
   <cge:TPSR_Ref TObjectID="6473924528046085"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,751.429,719.905) scale(1.66667,1.5) translate(-297.238,-234.968)" width="10" x="743.0952345984324" y="704.9047619047619"/></g>
  <g id="85">
   <use class="kv35" height="20" transform="rotate(0,1062.9,686.349) scale(1.66667,1.5) translate(-421.825,-223.783)" width="10" x="1054.563488157969" xlink:href="#Breaker:开关_0" y="671.3492063492063" zvalue="68"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924528111621" ObjectName="#1主变341断路器"/>
   <cge:TPSR_Ref TObjectID="6473924528111621"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1062.9,686.349) scale(1.66667,1.5) translate(-421.825,-223.783)" width="10" x="1054.563488157969" y="671.3492063492063"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="59">
   <use class="kv35" height="30" transform="rotate(0,752.54,622.127) scale(1.11111,0.814815) translate(-74.4206,138.615)" width="15" x="744.2063456565616" xlink:href="#Disconnector:刀闸_0" y="609.9047619047619" zvalue="54"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449866891269" ObjectName="#2主变3426隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449866891269"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,752.54,622.127) scale(1.11111,0.814815) translate(-74.4206,138.615)" width="15" x="744.2063456565616" y="609.9047619047619"/></g>
  <g id="63">
   <use class="kv35" height="30" transform="rotate(0,752.54,514.349) scale(1.11111,1.11111) translate(-73.5873,-49.7683)" width="30" x="735.8730124821741" xlink:href="#Disconnector:跌落刀闸_0" y="497.6825396825397" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449867087877" ObjectName="#2主变A3R2"/>
   <cge:TPSR_Ref TObjectID="6192449867087877"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,752.54,514.349) scale(1.11111,1.11111) translate(-73.5873,-49.7683)" width="30" x="735.8730124821741" y="497.6825396825397"/></g>
  <g id="72">
   <use class="kv35" height="30" transform="rotate(0,1067.09,283.495) scale(1.11111,1.11111) translate(-105.042,-26.6828)" width="30" x="1050.420021029183" xlink:href="#Disconnector:跌落刀闸_0" y="266.8278388278388" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449867218950" ObjectName="35kV帕放线华盛金矿T线3901A3R1"/>
   <cge:TPSR_Ref TObjectID="6192449867218950"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1067.09,283.495) scale(1.11111,1.11111) translate(-105.042,-26.6828)" width="30" x="1050.420021029183" y="266.8278388278388"/></g>
  <g id="84">
   <use class="kv35" height="30" transform="rotate(0,1066.22,586.571) scale(1.11111,0.814815) translate(-105.789,130.534)" width="15" x="1057.886593536773" xlink:href="#Disconnector:刀闸_0" y="574.3492063492064" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449867481094" ObjectName="#1主变3416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449867481094"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1066.22,586.571) scale(1.11111,0.814815) translate(-105.789,130.534)" width="15" x="1057.886593536773" y="574.3492063492064"/></g>
  <g id="87">
   <use class="kv35" height="30" transform="rotate(270,1159.98,415.738) scale(-1.44444,-1.05926) translate(-1959.72,-807.329)" width="15" x="1149.150790101006" xlink:href="#Disconnector:刀闸_0" y="399.8492063492064" zvalue="81"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449867612165" ObjectName="35kV帕放线华盛金矿T线3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449867612165"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1159.98,415.738) scale(-1.44444,-1.05926) translate(-1959.72,-807.329)" width="15" x="1149.150790101006" y="399.8492063492064"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="61">
   <use class="kv35" height="30" transform="rotate(270,819.429,672.127) scale(1.11111,1.11111) translate(-81.2762,-65.546)" width="12" x="812.7619047619048" xlink:href="#GroundDisconnector:地刀12_0" y="655.4603174603176" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449867022341" ObjectName="#2主变34267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449867022341"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,819.429,672.127) scale(1.11111,1.11111) translate(-81.2762,-65.546)" width="12" x="812.7619047619048" y="655.4603174603176"/></g>
  <g id="83">
   <use class="kv35" height="30" transform="rotate(270,1132.32,636.571) scale(1.11111,1.11111) translate(-112.565,-61.9905)" width="12" x="1125.650793650794" xlink:href="#GroundDisconnector:地刀12_0" y="619.9047619047619" zvalue="72"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449867415557" ObjectName="#1主变34167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449867415557"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1132.32,636.571) scale(1.11111,1.11111) translate(-112.565,-61.9905)" width="12" x="1125.650793650794" y="619.9047619047619"/></g>
  <g id="89">
   <use class="kv35" height="30" transform="rotate(180,1239.43,491.016) scale(1.11111,-1.11111) translate(-123.276,-931.263)" width="12" x="1232.761904761905" xlink:href="#GroundDisconnector:地刀12_0" y="474.3492063492064" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449867743237" ObjectName="35kV帕放线华盛金矿T线39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449867743237"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1239.43,491.016) scale(1.11111,-1.11111) translate(-123.276,-931.263)" width="12" x="1232.761904761905" y="474.3492063492064"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="66">
   <path class="kv35" d="M 752.54 529.9 L 752.64 610.31" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@1" LinkObjectIDznd="59@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 752.54 529.9 L 752.64 610.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv35" d="M 752.61 634.14 L 752.61 705.55" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@1" LinkObjectIDznd="57@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 752.61 634.14 L 752.61 705.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv35" d="M 751.54 734.23 L 751.54 782.83" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@1" LinkObjectIDznd="55@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 751.54 734.23 L 751.54 782.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv35" d="M 693.04 592.6 L 693.04 566.57 L 752.58 566.57" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="66" MaxPinNum="2"/>
   </metadata>
  <path d="M 693.04 592.6 L 693.04 566.57 L 752.58 566.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv35" d="M 803.51 672.14 L 752.61 672.14" stroke-width="1" zvalue="63"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="67" MaxPinNum="2"/>
   </metadata>
  <path d="M 803.51 672.14 L 752.61 672.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv35" d="M 1066.29 598.58 L 1066.29 635.29 L 1062.84 635.29 L 1062.84 672" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@1" LinkObjectIDznd="85@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1066.29 598.58 L 1066.29 635.29 L 1062.84 635.29 L 1062.84 672" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv35" d="M 1063.01 700.67 L 1063.01 749.27" stroke-width="1" zvalue="77"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@1" LinkObjectIDznd="86@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.01 700.67 L 1063.01 749.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv35" d="M 1116.4 636.58 L 1063.21 636.58" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@0" LinkObjectIDznd="81" MaxPinNum="2"/>
   </metadata>
  <path d="M 1116.4 636.58 L 1063.21 636.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv35" d="M 1175.35 415.86 L 1353.91 415.86 L 1353.91 443.67" stroke-width="1" zvalue="88"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@0" LinkObjectIDznd="94@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1175.35 415.86 L 1353.91 415.86 L 1353.91 443.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv35" d="M 1239.44 475.1 L 1239.44 415.86" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="95" MaxPinNum="2"/>
   </metadata>
  <path d="M 1239.44 475.1 L 1239.44 415.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv35" d="M 1067.09 299.05 L 1067.09 574.75" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@1" LinkObjectIDznd="84@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1067.09 299.05 L 1067.09 574.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv35" d="M 1005.93 557.04 L 1005.93 525.46 L 1067.09 525.46" stroke-width="1" zvalue="91"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="97" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.93 557.04 L 1005.93 525.46 L 1067.09 525.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv35" d="M 1144.37 415.83 L 1067.09 415.83" stroke-width="1" zvalue="92"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@1" LinkObjectIDznd="97" MaxPinNum="2"/>
   </metadata>
  <path d="M 1144.37 415.83 L 1067.09 415.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv35" d="M 752.54 497.68 L 752.54 353.24 L 1067.09 353.24" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="97" MaxPinNum="2"/>
   </metadata>
  <path d="M 752.54 497.68 L 752.54 353.24 L 1067.09 353.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv35" d="M 1067.86 98 L 1067.86 266.83" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="72@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1067.86 98 L 1067.86 266.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="114">
   <use class="kv35" height="30" transform="rotate(90,1068.96,98) scale(0.769231,11.0513) translate(319.881,61.637)" width="7" x="1066.2687389779" xlink:href="#ACLineSegment:T接线（中间连接点）_0" y="-67.7692307692306" zvalue="106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249310560262" ObjectName="35kV帕放线"/>
   <cge:TPSR_Ref TObjectID="8444249310560262_5066549589114882"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1068.96,98) scale(0.769231,11.0513) translate(319.881,61.637)" width="7" x="1066.2687389779" y="-67.7692307692306"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,348.25,315.983) scale(0.708333,0.665547) translate(139.022,153.772)" width="30" x="337.63" xlink:href="#State:红绿圆(方形)_0" y="306" zvalue="116"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,348.25,315.983) scale(0.708333,0.665547) translate(139.022,153.772)" width="30" x="337.63" y="306"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,252.625,315.983) scale(0.708333,0.665547) translate(99.6471,153.772)" width="30" x="242" xlink:href="#State:红绿圆(方形)_0" y="306" zvalue="117"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,252.625,315.983) scale(0.708333,0.665547) translate(99.6471,153.772)" width="30" x="242" y="306"/></g>
  <g id="830">
   <use height="30" transform="rotate(0,322.812,128.464) scale(1.22222,1.03092) translate(-48.6932,-3.38936)" width="90" x="267.81" xlink:href="#State:全站检修_0" y="113" zvalue="121"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549589114882" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,322.812,128.464) scale(1.22222,1.03092) translate(-48.6932,-3.38936)" width="90" x="267.81" y="113"/></g>
 </g>
</svg>