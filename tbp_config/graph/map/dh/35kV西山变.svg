<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549680406529" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Accessory:线路PT带避雷器0904_0" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="12.91666666666667" y1="23.75" y2="23.75"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,11) scale(1,1) translate(0,0)" width="6" x="7" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.988213005145578" x2="9.988213005145578" y1="1.029523490692871" y2="18.75"/>
   <ellipse cx="9.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="12.91666666666667" y1="32.91666666666667" y2="32.91666666666667"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2019.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="Accessory:带电显示器_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.41666666666667" x2="8.750000000000002" y1="11.08333333333333" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.08333333333333" x2="10.08333333333333" y1="1.5" y2="6.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.08333333333333" x2="10.08333333333333" y1="8.5" y2="10.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333334" x2="12.58333333333333" y1="6.5" y2="6.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333334" x2="12.58333333333333" y1="8.5" y2="8.5"/>
   <ellipse cx="10.08" cy="12.75" fill-opacity="0" rx="2.24" ry="2.24" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.083333333333334" x2="11.08333333333333" y1="19.5" y2="19.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.666666666666668" x2="11.66666666666667" y1="11.08333333333333" y2="14.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.583333333333334" x2="11.58333333333333" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.08333333333333" x2="10.08333333333333" y1="17.5" y2="15.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.083333333333334" x2="12.08333333333333" y1="17.5" y2="17.5"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀-带电显示_0" viewBox="0,0,21,29">
   <use terminal-index="0" type="0" x="10.57144275301999" xlink:href="#terminal" y="0.9988473696347349"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="4.5" y1="4.5" y2="4.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="4.5" y2="8.5"/>
   <ellipse cx="4.5" cy="16.25" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="21.5" y2="19.08333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="1.15" y2="4.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.5" y2="21.5"/>
   <path d="M 2.25 14.4167 L 6.58333 18" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.41666666666667" y2="18.01429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="21.35" y2="24.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.166666666666666" x2="13.5" y1="24.24694619969017" y2="24.24694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.583333333333334" x2="12.5" y1="26.08157590710536" y2="26.08157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.274021844661625" x2="11.93079938068318" y1="27.83348701738202" y2="27.83348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.16666666666667" x2="16.33141025641025" y1="8.166666666666666" y2="18.0142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.66666666666667" x2="18" y1="8.04249548976499" y2="8.04249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="8.000000000000004" y2="4.416666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.41666666666666" x2="4.416666666666666" y1="21.41666666666666" y2="21.41666666666666"/>
   <path d="M 6.66667 14.25 L 2.41667 18.3333" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="10.91666666666667" y2="10.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀-带电显示_1" viewBox="0,0,21,29">
   <use terminal-index="0" type="0" x="10.57144275301999" xlink:href="#terminal" y="0.9988473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.166666666666666" x2="13.5" y1="24.24694619969017" y2="24.24694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.583333333333334" x2="12.5" y1="26.08157590710536" y2="26.08157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.274021844661625" x2="11.93079938068318" y1="27.83348701738202" y2="27.83348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.367489181242" x2="16.367489181242" y1="8.166666666666666" y2="20.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="8.000000000000004" y2="4.416666666666668"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.66666666666667" x2="18" y1="8.04249548976499" y2="8.04249548976499"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="21.5" y2="19.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.367489181242" x2="16.367489181242" y1="20.41666666666667" y2="18.01429160524171"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="1.15" y2="4.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.5" y2="21.5"/>
   <ellipse cx="4.5" cy="16.25" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="4.5" y1="4.5" y2="4.5"/>
   <path d="M 2.25 14.4167 L 6.58333 18" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="4.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="21.35" y2="24.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.367489181242" x2="16.367489181242" y1="8.000000000000004" y2="4.416666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="10.91666666666667" y2="10.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.41666666666666" x2="4.416666666666666" y1="21.41666666666666" y2="21.41666666666666"/>
   <path d="M 6.66667 14.25 L 2.41667 18.3333" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀-带电显示_2" viewBox="0,0,21,29">
   <use terminal-index="0" type="0" x="10.57144275301999" xlink:href="#terminal" y="0.9988473696347349"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.75" x2="12.75" y1="8.5" y2="17.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.41666666666667" y2="18.01429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.166666666666666" x2="13.5" y1="24.24694619969017" y2="24.24694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.583333333333334" x2="12.5" y1="26.08157590710536" y2="26.08157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.274021844661625" x2="11.93079938068318" y1="27.83348701738202" y2="27.83348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.75" x2="20" y1="8.383333333333333" y2="17.8"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="8.000000000000004" y2="4.416666666666668"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.66666666666667" x2="18" y1="8.04249548976499" y2="8.04249548976499"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="21.5" y2="19.08333333333333"/>
   <ellipse cx="4.5" cy="16.25" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="1.15" y2="4.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="21.35" y2="24.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="4.5" y1="4.5" y2="4.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.5" y2="21.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="4.5" y2="8.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="11" y2="13.5"/>
   <path d="M 2.25 14.4167 L 6.58333 18" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.41666666666666" x2="4.416666666666666" y1="21.41666666666666" y2="21.41666666666666"/>
   <path d="M 6.66667 14.25 L 2.41667 18.3333" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="10.91666666666667" y2="10.91666666666667"/>
  </symbol>
  <symbol id="ACLineSegment:线路带壁雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="14.875" xlink:href="#terminal" y="39.83880854456296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.25" x2="6.25" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="5.75" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.25" x2="7.25" y1="32.25" y2="32.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="8.25" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="6.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="4.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.91022336769755" x2="14.91022336769755" y1="39.75" y2="0.8333333333333321"/>
   <path d="M 14.75 9.25 L 5.75 9.25 L 5.75 21.25 L 5.75 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.79,21.17) scale(1,1) translate(0,0)" width="6.08" x="2.75" y="14"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷带壁雷器_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="28.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11.75" y1="17.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="14.75" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="12" y1="11.75" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="9" y1="12.75" y2="12.75"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.04,18.25) scale(1,1) translate(0,0)" width="3.25" x="9.42" y="14.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.5" y1="10.83333333333333" y2="10.83333333333333"/>
   <path d="M 5.025 2.775 L 4.16667 9.5 L 5.025 7.60833 L 5.91667 9.5 L 5.025 2.775" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 5 23.75 L 11 23.75 L 11 17.75 L 10.25 20.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="7.627914951989029" y2="28.23902606310013"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变11_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="9.25" y2="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="26.83333333333333" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0928507596068" x2="10.0928507596068" y1="10.97013412501683" y2="12.64189293057399"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.77292225201073" x2="10.0928507596068" y1="14.31365173613114" y2="12.64189293057398"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Compensator:并联电容器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15.08333333333333" xlink:href="#terminal" y="4"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.15" x2="15.15" y1="6.083333333333332" y2="12.5"/>
   <path d="M 10.4167 17.4333 A 4.91667 4.75 -90 1 0 15.1667 12.5167" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9" x2="21.1" y1="25.5" y2="25.5"/>
   <path d="M 10.4833 17.4167 L 15.15 17.5 L 15.15 25.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9" x2="9" y1="23.41666666666667" y2="25.55"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="21.05" x2="21.05" y1="23.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8" x2="22" y1="6" y2="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8" x2="22" y1="4" y2="4"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV西山变" InitShowingPlane="0" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass">
  
 </g>
 <g id="OtherClass">
  <image height="59.4" id="1" preserveAspectRatio="xMidYMid slice" width="222.19" x="60.56" xlink:href="logo.png" y="40.68"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,171.653,70.3772) scale(1,1) translate(-1.34463e-14,0)" writing-mode="lr" x="171.65" xml:space="preserve" y="74.88" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,189.625,67.3894) scale(1,1) translate(0,5.03827e-15)" writing-mode="lr" x="189.63" xml:space="preserve" y="74.89" zvalue="5"> 35kV西山变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="226" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,73.4375,320) scale(1,1) translate(0,0)" width="72.88" x="37" y="308" zvalue="1663"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.4375,320) scale(1,1) translate(0,0)" writing-mode="lr" x="73.44" xml:space="preserve" y="324.5" zvalue="1663">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="268" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,276.25,403.5) scale(1,1) translate(0,-2.60791e-13)" width="72.88" x="239.81" y="391.5" zvalue="2003"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,276.25,403.5) scale(1,1) translate(0,-2.60791e-13)" writing-mode="lr" x="276.25" xml:space="preserve" y="408" zvalue="2003">小电流接地</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="267" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,175.344,403.5) scale(1,1) translate(0,0)" width="72.88" x="138.91" y="391.5" zvalue="2004"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,175.344,403.5) scale(1,1) translate(0,0)" writing-mode="lr" x="175.34" xml:space="preserve" y="408" zvalue="2004">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="266" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,73.4375,403.5) scale(1,1) translate(0,0)" width="72.88" x="37" y="391.5" zvalue="2005"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.4375,403.5) scale(1,1) translate(0,0)" writing-mode="lr" x="73.44" xml:space="preserve" y="408" zvalue="2005">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="265" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,73.4375,361.75) scale(1,1) translate(0,0)" width="72.88" x="37" y="349.75" zvalue="2006"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.4375,361.75) scale(1,1) translate(0,0)" writing-mode="lr" x="73.44" xml:space="preserve" y="366.25" zvalue="2006">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,797.076,336.091) scale(1,1) translate(0,0)" writing-mode="lr" x="797.08" xml:space="preserve" y="340.59" zvalue="32">35kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1117.98,447.584) scale(1,1) translate(0,0)" writing-mode="lr" x="1117.98" xml:space="preserve" y="452.08" zvalue="38">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1116.37,386.68) scale(1,1) translate(0,0)" writing-mode="lr" x="1116.37" xml:space="preserve" y="391.18" zvalue="40">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,880.02,392.02) scale(1,1) translate(0,0)" writing-mode="lr" x="880.02" xml:space="preserve" y="396.52" zvalue="43">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,902.393,369.144) scale(1,1) translate(0,0)" writing-mode="lr" x="902.39" xml:space="preserve" y="373.64" zvalue="49">10</text>
  <line fill="none" id="124" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="378" x2="378" y1="8" y2="998" zvalue="54"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1033.75,74.8122) scale(1,1) translate(0,0)" writing-mode="lr" x="1033.75" xml:space="preserve" y="79.31" zvalue="59">35kV遮西线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1068.81,242.645) scale(1,1) translate(0,0)" writing-mode="lr" x="1068.81" xml:space="preserve" y="247.14" zvalue="60">351</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1057.74,186.754) scale(1,1) translate(0,0)" writing-mode="lr" x="1057.74" xml:space="preserve" y="191.25" zvalue="62">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1097,162.316) scale(1,1) translate(0,0)" writing-mode="lr" x="1097" xml:space="preserve" y="166.82" zvalue="64">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1057.74,302.549) scale(1,1) translate(0,0)" writing-mode="lr" x="1057.74" xml:space="preserve" y="307.05" zvalue="67">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,657.735,691) scale(1,1) translate(0,0)" writing-mode="lr" x="657.74" xml:space="preserve" y="695.5" zvalue="205">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="349" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1224.65,495.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1224.65001396347" xml:space="preserve" y="501.3749965229415" zvalue="368">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="369" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,753.581,826.474) scale(1,1) translate(0,0)" writing-mode="lr" x="753.58" xml:space="preserve" y="830.97" zvalue="392">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="655" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,713.696,549.212) scale(1,1) translate(0,-1.19951e-13)" writing-mode="lr" x="713.7" xml:space="preserve" y="553.71" zvalue="777">10kVⅠ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="654" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,738.065,627.252) scale(1,1) translate(0,0)" writing-mode="lr" x="738.0599999999999" xml:space="preserve" y="631.75" zvalue="780">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1165.75,631.222) scale(1,1) translate(-1.28231e-12,0)" writing-mode="lr" x="1165.75" xml:space="preserve" y="635.72" zvalue="878">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="431" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,761.081,729.333) scale(1,1) translate(0,0)" writing-mode="lr" x="761.08" xml:space="preserve" y="734.83" zvalue="912">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,794.5,769.5) scale(1,1) translate(0,0)" writing-mode="lr" x="794.5" xml:space="preserve" y="774" zvalue="917">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,851.39,496.591) scale(1,1) translate(0,0)" writing-mode="lr" x="851.39" xml:space="preserve" y="501.09" zvalue="1123">35kVⅠ段母线电压互感器</text>
  <line fill="none" id="246" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.90549597855136" x2="318.5167560321705" y1="161.7708030256257" y2="161.7708030256257" zvalue="1416"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="42" x2="113.4400000000001" y1="920.7056480819297" y2="920.7056480819297"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="42" x2="113.4400000000001" y1="972.0451480819296" y2="972.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="42" x2="42" y1="920.7056480819297" y2="972.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.4400000000001" x2="113.4400000000001" y1="920.7056480819297" y2="972.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.4405" x2="348.0005" y1="920.7056480819297" y2="920.7056480819297"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.4405" x2="348.0005" y1="972.0451480819296" y2="972.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.4405" x2="113.4405" y1="920.7056480819297" y2="972.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="348.0005" x2="348.0005" y1="920.7056480819297" y2="972.0451480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="42" x2="113.4400000000001" y1="972.0451280819295" y2="972.0451280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="42" x2="113.4400000000001" y1="999.5226280819296" y2="999.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="42" x2="42" y1="972.0451280819295" y2="999.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.4400000000001" x2="113.4400000000001" y1="972.0451280819295" y2="999.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.4405" x2="184.4758" y1="972.0451280819295" y2="972.0451280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.4405" x2="184.4758" y1="999.5226280819296" y2="999.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.4405" x2="113.4405" y1="972.0451280819295" y2="999.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.4758" x2="184.4758" y1="972.0451280819295" y2="999.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.4758" x2="266.2379" y1="972.0451280819295" y2="972.0451280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.4758" x2="266.2379" y1="999.5226280819296" y2="999.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.4758" x2="184.4758" y1="972.0451280819295" y2="999.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="266.2379" x2="266.2379" y1="972.0451280819295" y2="999.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="266.2378" x2="347.9999" y1="972.0451280819295" y2="972.0451280819295"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="266.2378" x2="347.9999" y1="999.5226280819296" y2="999.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="266.2378" x2="266.2378" y1="972.0451280819295" y2="999.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.9999" x2="347.9999" y1="972.0451280819295" y2="999.5226280819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="42" x2="113.4400000000001" y1="999.5225480819296" y2="999.5225480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="42" x2="113.4400000000001" y1="1027.00004808193" y2="1027.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="42" x2="42" y1="999.5225480819296" y2="1027.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.4400000000001" x2="113.4400000000001" y1="999.5225480819296" y2="1027.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.4405" x2="184.4758" y1="999.5225480819296" y2="999.5225480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.4405" x2="184.4758" y1="1027.00004808193" y2="1027.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.4405" x2="113.4405" y1="999.5225480819296" y2="1027.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.4758" x2="184.4758" y1="999.5225480819296" y2="1027.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.4758" x2="266.2379" y1="999.5225480819296" y2="999.5225480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.4758" x2="266.2379" y1="1027.00004808193" y2="1027.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.4758" x2="184.4758" y1="999.5225480819296" y2="1027.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="266.2379" x2="266.2379" y1="999.5225480819296" y2="1027.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="266.2378" x2="347.9999" y1="999.5225480819296" y2="999.5225480819296"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="266.2378" x2="347.9999" y1="1027.00004808193" y2="1027.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="266.2378" x2="266.2378" y1="999.5225480819296" y2="1027.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.9999" x2="347.9999" y1="999.5225480819296" y2="1027.00004808193"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="190" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,79.1776,950.279) scale(1,1) translate(0,1.043e-13)" writing-mode="lr" x="47.36" xml:space="preserve" y="956.28" zvalue="1419">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="188" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,78.0412,987.648) scale(1,1) translate(0,1.08449e-13)" writing-mode="lr" x="57.08" xml:space="preserve" y="993.65" zvalue="1420">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="187" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,262.881,988.648) scale(1,1) translate(1.29348e-13,-1.51984e-12)" writing-mode="lr" x="194.18" xml:space="preserve" y="994.65" zvalue="1421">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.6709,1017.21) scale(1,1) translate(-4.6296e-14,-1.56423e-12)" writing-mode="lr" x="70.67" xml:space="preserve" y="1023.21" zvalue="1422">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="175" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,271.175,1015.21) scale(1,1) translate(0,1.11508e-13)" writing-mode="lr" x="194.35" xml:space="preserve" y="1021.21" zvalue="1423">更新日期    20200902</text>
  <line fill="none" id="98" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.28468498659493" x2="317.8959450402141" y1="612.5373878122266" y2="612.5373878122266" zvalue="1437"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84.3493,633.579) scale(1,1) translate(3.04169e-15,-1.36531e-13)" writing-mode="lr" x="84.34927949061648" xml:space="preserve" y="638.0792383117243" zvalue="1451">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1208,230.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1208" xml:space="preserve" y="235" zvalue="1521">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1097,210.545) scale(1,1) translate(0,0)" writing-mode="lr" x="1097" xml:space="preserve" y="215.05" zvalue="1526">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1098,271.545) scale(1,1) translate(0,-4.68151e-13)" writing-mode="lr" x="1098" xml:space="preserve" y="276.05" zvalue="1530">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,904.393,420.098) scale(1,1) translate(0,0)" writing-mode="lr" x="904.39" xml:space="preserve" y="424.6" zvalue="1535">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1083.39,412.144) scale(1,1) translate(0,0)" writing-mode="lr" x="1083.39" xml:space="preserve" y="416.64" zvalue="1538">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,795.5,801.257) scale(1,1) translate(0,0)" writing-mode="lr" x="795.5" xml:space="preserve" y="805.76" zvalue="1548">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,795,855.257) scale(1,1) translate(0,0)" writing-mode="lr" x="795" xml:space="preserve" y="859.76" zvalue="1552">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1020.57,158.083) scale(1,1) translate(0,0)" writing-mode="lr" x="1020.57" xml:space="preserve" y="162.58" zvalue="1627">9</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="3" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365" x2="365" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="3" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365" x2="365" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="3" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365" x2="365" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="3" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365" x2="365" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="3" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365" x2="365" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54" x2="99.77449999999999" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54" x2="99.77449999999999" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54" x2="54" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="99.77449999999999" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="158.5809" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="158.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="99.77449999999999" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="158.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="217.3873" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="217.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="158.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3873" x2="217.3873" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3872" x2="276.1936000000001" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3872" x2="276.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3872" x2="217.3872" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="276.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="335" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="335" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="276.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="335" x2="335" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54" x2="99.77449999999999" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54" x2="99.77449999999999" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54" x2="54" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="99.77449999999999" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="158.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="158.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="99.77449999999999" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="158.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="217.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="217.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="158.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3873" x2="217.3873" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3872" x2="276.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3872" x2="276.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3872" x2="217.3872" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="276.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="335" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="335" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="276.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="335" x2="335" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54" x2="99.77449999999999" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54" x2="99.77449999999999" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54" x2="54" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="99.77449999999999" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="158.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="158.5809" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="99.77449999999999" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="158.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="217.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="217.3873" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="158.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3873" x2="217.3873" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3872" x2="276.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3872" x2="276.1936000000001" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3872" x2="217.3872" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="276.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="335" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="335" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="276.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="335" x2="335" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54" x2="99.77449999999999" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54" x2="99.77449999999999" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54" x2="54" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="99.77449999999999" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="158.5809" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="158.5809" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="99.77449999999999" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="158.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="217.3873" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="217.3873" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="158.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3873" x2="217.3873" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3872" x2="276.1936000000001" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3872" x2="276.1936000000001" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3872" x2="217.3872" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="276.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="335" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="335" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="276.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="335" x2="335" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54" x2="99.77449999999999" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54" x2="99.77449999999999" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54" x2="54" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="99.77449999999999" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="158.5809" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="158.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="99.77449999999999" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="158.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="217.3873" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="217.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="158.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3873" x2="217.3873" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3872" x2="276.1936000000001" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3872" x2="276.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3872" x2="217.3872" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="276.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="335" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="335" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="276.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="335" x2="335" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54" x2="99.77449999999999" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54" x2="99.77449999999999" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="54" x2="54" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="99.77449999999999" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="158.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="158.5809" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.77449999999999" x2="99.77449999999999" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="158.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="217.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="217.3873" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.5809" x2="158.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3873" x2="217.3873" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3872" x2="276.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3872" x2="276.1936000000001" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="217.3872" x2="217.3872" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="276.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="335" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="335" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="276.1936000000001" x2="276.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="335" x2="335" y1="582" y2="606.6794"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="224" stroke="rgb(255,255,255)" text-anchor="middle" x="131" xml:space="preserve" y="460" zvalue="1665">35kVⅠ段母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="224" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="131" xml:space="preserve" y="476" zvalue="1665">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="223" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,200.399,319.841) scale(1,1) translate(0,0)" writing-mode="lr" x="200.4" xml:space="preserve" y="324.34" zvalue="1666">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="222" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,305.399,319.841) scale(1,1) translate(0,0)" writing-mode="lr" x="305.4" xml:space="preserve" y="324.34" zvalue="1667">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="221" stroke="rgb(255,255,255)" text-anchor="middle" x="248.34375" xml:space="preserve" y="460" zvalue="1668">10kVⅠ段母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="221" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="248.34375" xml:space="preserve" y="476" zvalue="1668">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="219" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79,495.5) scale(1,1) translate(0,0)" writing-mode="lr" x="79" xml:space="preserve" y="500" zvalue="1670">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="218" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79,521) scale(1,1) translate(0,0)" writing-mode="lr" x="79" xml:space="preserve" y="525.5" zvalue="1671">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="217" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79,546.5) scale(1,1) translate(0,0)" writing-mode="lr" x="79" xml:space="preserve" y="551" zvalue="1672">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="216" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79,572) scale(1,1) translate(0,0)" writing-mode="lr" x="79" xml:space="preserve" y="576.5" zvalue="1673">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="215" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79,597.5) scale(1,1) translate(0,0)" writing-mode="lr" x="79" xml:space="preserve" y="602" zvalue="1674">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="214" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,41,179) scale(1,1) translate(0,0)" writing-mode="lr" x="41" xml:space="preserve" y="184.5" zvalue="1675">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="213" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,221,179) scale(1,1) translate(0,0)" writing-mode="lr" x="221" xml:space="preserve" y="184.5" zvalue="1676">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="212" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.6875,203.25) scale(1,1) translate(0,0)" writing-mode="lr" x="44.69" xml:space="preserve" y="207.75" zvalue="1677">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.1875,251) scale(1,1) translate(0,0)" writing-mode="lr" x="48.19" xml:space="preserve" y="255.5" zvalue="1678">#1主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.1875,274) scale(1,1) translate(0,0)" writing-mode="lr" x="48.19" xml:space="preserve" y="278.5" zvalue="1680">#1主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.6875,227.25) scale(1,1) translate(0,0)" writing-mode="lr" x="45.69" xml:space="preserve" y="231.75" zvalue="1682">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1370.81,242.645) scale(1,1) translate(0,0)" writing-mode="lr" x="1370.81" xml:space="preserve" y="247.14" zvalue="1722">352</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1359.74,186.754) scale(1,1) translate(0,0)" writing-mode="lr" x="1359.74" xml:space="preserve" y="191.25" zvalue="1724">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1399,162.316) scale(1,1) translate(0,0)" writing-mode="lr" x="1399" xml:space="preserve" y="166.82" zvalue="1726">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1359.74,314.549) scale(1,1) translate(0,0)" writing-mode="lr" x="1359.74" xml:space="preserve" y="319.05" zvalue="1729">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1399,210.545) scale(1,1) translate(0,0)" writing-mode="lr" x="1399" xml:space="preserve" y="215.05" zvalue="1736">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1400,271.545) scale(1,1) translate(0,-4.68151e-13)" writing-mode="lr" x="1400" xml:space="preserve" y="276.05" zvalue="1738">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1322.57,156.083) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.57" xml:space="preserve" y="160.58" zvalue="1740">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1231,517.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1231" xml:space="preserve" y="523.625" zvalue="1750">2500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,754.581,938) scale(1,1) translate(0,0)" writing-mode="lr" x="754.58" xml:space="preserve" y="942.5" zvalue="1762">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="186" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,915.581,826.474) scale(1,1) translate(0,0)" writing-mode="lr" x="915.58" xml:space="preserve" y="830.97" zvalue="1770">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,923.081,729.333) scale(1,1) translate(0,0)" writing-mode="lr" x="923.08" xml:space="preserve" y="734.83" zvalue="1773">052</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,956.5,769.5) scale(1,1) translate(0,0)" writing-mode="lr" x="956.5" xml:space="preserve" y="774" zvalue="1776">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,957.5,801.257) scale(1,1) translate(0,0)" writing-mode="lr" x="957.5" xml:space="preserve" y="805.76" zvalue="1782">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,957,855.257) scale(1,1) translate(0,0)" writing-mode="lr" x="957" xml:space="preserve" y="859.76" zvalue="1784">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,917.042,936) scale(1,1) translate(0,0)" writing-mode="lr" x="917.04" xml:space="preserve" y="940.5" zvalue="1788">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="300" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1073.58,802.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1073.58" xml:space="preserve" y="806.97" zvalue="1795">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="299" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1081.08,729.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1081.08" xml:space="preserve" y="734.83" zvalue="1798">053</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="298" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1114.5,769.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1114.5" xml:space="preserve" y="774" zvalue="1801">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1054.73,906.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1054.73" xml:space="preserve" y="911" zvalue="1811">10kV崩强线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="321" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1235.58,802.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1235.58" xml:space="preserve" y="806.97" zvalue="1820">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="320" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1243.08,729.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1243.08" xml:space="preserve" y="734.83" zvalue="1823">054</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="319" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1276.5,769.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1276.5" xml:space="preserve" y="774" zvalue="1826">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="318" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1220.68,906.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1220.68" xml:space="preserve" y="911" zvalue="1832">10kV毛讲线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="336" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1397.58,802.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1397.58" xml:space="preserve" y="806.97" zvalue="1836">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="335" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1405.08,729.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1405.08" xml:space="preserve" y="734.83" zvalue="1839">055</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="334" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1438.5,769.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1438.5" xml:space="preserve" y="774" zvalue="1842">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="333" stroke="rgb(255,255,255)" text-anchor="middle" x="1376.9609375" xml:space="preserve" y="905" zvalue="1848">10kV邦</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="333" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1376.9609375" xml:space="preserve" y="921" zvalue="1848">角线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="353" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1543.58,802.474) scale(1,1) translate(0,0)" writing-mode="lr" x="1543.58" xml:space="preserve" y="806.97" zvalue="1852">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="352" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1551.08,729.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1551.08" xml:space="preserve" y="734.83" zvalue="1855">056</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="351" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1584.5,769.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1584.5" xml:space="preserve" y="774" zvalue="1858">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="350" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1529.68,905.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1529.68" xml:space="preserve" y="910" zvalue="1864">10kV西山街道线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="368" stroke="rgb(255,255,255)" text-anchor="middle" x="1439.75" xml:space="preserve" y="956.234375" zvalue="1870">10kV2号站用</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="368" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1439.75" xml:space="preserve" y="972.234375" zvalue="1870">变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,746.481,956.5) scale(1,1) translate(0,0)" writing-mode="lr" x="746.48" xml:space="preserve" y="961" zvalue="1873">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,910.967,957.5) scale(1,1) translate(0,0)" writing-mode="lr" x="910.97" xml:space="preserve" y="962" zvalue="1878">10kV2号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1607.06,632.252) scale(1,1) translate(0,0)" writing-mode="lr" x="1607.06" xml:space="preserve" y="636.75" zvalue="1899">0121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1521.74,313.549) scale(1,1) translate(0,0)" writing-mode="lr" x="1521.74" xml:space="preserve" y="318.05" zvalue="1906">3121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1550,271.545) scale(1,1) translate(0,-4.68151e-13)" writing-mode="lr" x="1550" xml:space="preserve" y="276.05" zvalue="1908">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="132" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,208.041,948.648) scale(1,1) translate(-3.28809e-14,1.04119e-13)" writing-mode="lr" x="148.08" xml:space="preserve" y="954.65" zvalue="2008">XiShan-01-2015</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="269" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,149.541,985.648) scale(1,1) translate(0,1.08227e-13)" writing-mode="lr" x="124.08" xml:space="preserve" y="991.65" zvalue="2010">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="271" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1344,72.686) scale(1,1) translate(0,0)" writing-mode="lr" x="1344" xml:space="preserve" y="77.19" zvalue="2012">35kV西掌线</text>
 </g>
 <g id="ButtonClass">
  <g href="35kV西山变_全站公用.svg"><rect fill-opacity="0" height="24" width="72.88" x="37" y="308" zvalue="1663"/></g>
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="239.81" y="391.5" zvalue="2003"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="138.91" y="391.5" zvalue="2004"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="37" y="391.5" zvalue="2005"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="37" y="349.75" zvalue="2006"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="137">
   <path class="kv35" d="M 806.67 349.09 L 1572 349.09" stroke-width="6" zvalue="31"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674403745795" ObjectName="35kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674403745795"/></metadata>
  <path d="M 806.67 349.09 L 1572 349.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv10" d="M 624 674 L 1664.47 674" stroke-width="4" zvalue="204"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674403811331" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674403811331"/></metadata>
  <path d="M 624 674 L 1664.47 674" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="276">
   <use class="kv35" height="20" transform="rotate(180,1139.59,447.209) scale(1.5542,1.35421) translate(-403.585,-113.431)" width="10" x="1131.816710722901" xlink:href="#Breaker:开关_0" y="433.6666962122333" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925119770627" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925119770627"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1139.59,447.209) scale(1.5542,1.35421) translate(-403.585,-113.431)" width="10" x="1131.816710722901" y="433.6666962122333"/></g>
  <g id="120">
   <use class="kv35" height="20" transform="rotate(0,1043.21,242.02) scale(1.5542,1.35421) translate(-369.217,-59.7612)" width="10" x="1035.434034534565" xlink:href="#Breaker:开关_0" y="228.4779663085938" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925119836163" ObjectName="35kV遮西线351断路器"/>
   <cge:TPSR_Ref TObjectID="6473925119836163"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1043.21,242.02) scale(1.5542,1.35421) translate(-369.217,-59.7612)" width="10" x="1035.434034534565" y="228.4779663085938"/></g>
  <g id="233">
   <use class="kv10" height="20" transform="rotate(0,1139.37,632.222) scale(2.22222,2.22222) translate(-620.54,-335.5)" width="10" x="1128.25497640937" xlink:href="#Breaker:小车断路器_0" y="610" zvalue="877"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925119901699" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925119901699"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1139.37,632.222) scale(2.22222,2.22222) translate(-620.54,-335.5)" width="10" x="1128.25497640937" y="610"/></g>
  <g id="189">
   <use class="kv10" height="20" transform="rotate(0,736.111,730.333) scale(2.22222,2.22222) translate(-398.75,-389.461)" width="10" x="725" xlink:href="#Breaker:小车断路器_0" y="708.1111145019531" zvalue="911"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925119967235" ObjectName="10kV1号电容器051断路器"/>
   <cge:TPSR_Ref TObjectID="6473925119967235"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,736.111,730.333) scale(2.22222,2.22222) translate(-398.75,-389.461)" width="10" x="725" y="708.1111145019531"/></g>
  <g id="158">
   <use class="kv35" height="20" transform="rotate(0,1345.21,242.02) scale(1.5542,1.35421) translate(-476.905,-59.7612)" width="10" x="1337.434034534565" xlink:href="#Breaker:开关_0" y="228.4779663085938" zvalue="1720"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925120032771" ObjectName="35kV西掌线352断路器"/>
   <cge:TPSR_Ref TObjectID="6473925120032771"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1345.21,242.02) scale(1.5542,1.35421) translate(-476.905,-59.7612)" width="10" x="1337.434034534565" y="228.4779663085938"/></g>
  <g id="254">
   <use class="kv10" height="20" transform="rotate(0,898.111,730.333) scale(2.22222,2.22222) translate(-487.85,-389.461)" width="10" x="887" xlink:href="#Breaker:小车断路器_0" y="708.1111145019531" zvalue="1771"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925120098307" ObjectName="10kV2号电容器052断路器"/>
   <cge:TPSR_Ref TObjectID="6473925120098307"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,898.111,730.333) scale(2.22222,2.22222) translate(-487.85,-389.461)" width="10" x="887" y="708.1111145019531"/></g>
  <g id="316">
   <use class="kv10" height="20" transform="rotate(0,1056.11,730.333) scale(2.22222,2.22222) translate(-574.75,-389.461)" width="10" x="1045" xlink:href="#Breaker:小车断路器_0" y="708.1111145019531" zvalue="1796"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925120163843" ObjectName="10kV崩强线053断路器"/>
   <cge:TPSR_Ref TObjectID="6473925120163843"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1056.11,730.333) scale(2.22222,2.22222) translate(-574.75,-389.461)" width="10" x="1045" y="708.1111145019531"/></g>
  <g id="331">
   <use class="kv10" height="20" transform="rotate(0,1218.11,730.333) scale(2.22222,2.22222) translate(-663.85,-389.461)" width="10" x="1207" xlink:href="#Breaker:小车断路器_0" y="708.1111145019531" zvalue="1821"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925120229379" ObjectName="10kV毛讲线054断路器"/>
   <cge:TPSR_Ref TObjectID="6473925120229379"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1218.11,730.333) scale(2.22222,2.22222) translate(-663.85,-389.461)" width="10" x="1207" y="708.1111145019531"/></g>
  <g id="346">
   <use class="kv10" height="20" transform="rotate(0,1380.11,730.333) scale(2.22222,2.22222) translate(-752.95,-389.461)" width="10" x="1369" xlink:href="#Breaker:小车断路器_0" y="708.1111145019531" zvalue="1837"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925120294915" ObjectName="10kV邦角线055断路器"/>
   <cge:TPSR_Ref TObjectID="6473925120294915"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1380.11,730.333) scale(2.22222,2.22222) translate(-752.95,-389.461)" width="10" x="1369" y="708.1111145019531"/></g>
  <g id="363">
   <use class="kv10" height="20" transform="rotate(0,1526.11,730.333) scale(2.22222,2.22222) translate(-833.25,-389.461)" width="10" x="1515" xlink:href="#Breaker:小车断路器_0" y="708.1111145019531" zvalue="1853"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925120360451" ObjectName="10kV西山街道线056断路器"/>
   <cge:TPSR_Ref TObjectID="6473925120360451"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1526.11,730.333) scale(2.22222,2.22222) translate(-833.25,-389.461)" width="10" x="1515" y="708.1111145019531"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="272">
   <use class="kv35" height="30" transform="rotate(0,1139.4,386.372) scale(0.947693,-0.6712) translate(62.4959,-966.948)" width="15" x="1132.293211654153" xlink:href="#Disconnector:刀闸_0" y="376.3043387992516" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453989498883" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453989498883"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1139.4,386.372) scale(0.947693,-0.6712) translate(62.4959,-966.948)" width="15" x="1132.293211654153" y="376.3043387992516"/></g>
  <g id="1453">
   <use class="kv35" height="30" transform="rotate(180,854.01,393.854) scale(0.947693,-0.6712) translate(46.744,-985.577)" width="15" x="846.9024010422277" xlink:href="#Disconnector:刀闸_0" y="383.7860480231957" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453991399427" ObjectName="35kVⅠ段母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453991399427"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,854.01,393.854) scale(0.947693,-0.6712) translate(46.744,-985.577)" width="15" x="846.9024010422277" y="383.7860480231957"/></g>
  <g id="119">
   <use class="kv35" height="30" transform="rotate(0,1043.21,184.588) scale(-0.947693,0.6712) translate(-2144.39,85.4916)" width="15" x="1036.103492892157" xlink:href="#Disconnector:刀闸_0" y="174.5197795335134" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453989892099" ObjectName="35kV遮西线3516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453989892099"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1043.21,184.588) scale(-0.947693,0.6712) translate(-2144.39,85.4916)" width="15" x="1036.103492892157" y="174.5197795335134"/></g>
  <g id="112">
   <use class="kv35" height="30" transform="rotate(180,1043.7,301.857) scale(-0.947693,0.6712) translate(-2145.41,142.938)" width="15" x="1036.595940718963" xlink:href="#Disconnector:刀闸_0" y="291.7885325852712" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453989695491" ObjectName="35kV遮西线3511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453989695491"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1043.7,301.857) scale(-0.947693,0.6712) translate(-2145.41,142.938)" width="15" x="1036.595940718963" y="291.7885325852712"/></g>
  <g id="388">
   <use class="kv10" height="30" transform="rotate(180,735.667,825.891) scale(0.947693,-0.6712) translate(40.2122,-2061.29)" width="15" x="728.5589697813775" xlink:href="#Disconnector:刀闸_0" y="815.8230764122643" zvalue="391"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453992579075" ObjectName="10kV1号电容器0516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453992579075"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,735.667,825.891) scale(0.947693,-0.6712) translate(40.2122,-2061.29)" width="15" x="728.5589697813775" y="815.8230764122643"/></g>
  <g id="658">
   <use class="kv10" height="26" transform="rotate(0,711.247,629.252) scale(1.66667,1.42857) translate(-280.499,-183.204)" width="12" x="701.2465984437617" xlink:href="#Disconnector:单手车刀闸1212_0" y="610.6807396958916" zvalue="778"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453990023171" ObjectName="10kVⅠ段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453990023171"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,711.247,629.252) scale(1.66667,1.42857) translate(-280.499,-183.204)" width="12" x="701.2465984437617" y="610.6807396958916"/></g>
  <g id="101">
   <use class="kv35" height="30" transform="rotate(270,1024.4,139.169) scale(-0.947693,0.6712) translate(-2105.74,63.2424)" width="15" x="1017.293226036425" xlink:href="#Disconnector:刀闸_0" y="129.1010127876037" zvalue="1626"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453991071747" ObjectName="35kV遮西线3519隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453991071747"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1024.4,139.169) scale(-0.947693,0.6712) translate(-2105.74,63.2424)" width="15" x="1017.293226036425" y="129.1010127876037"/></g>
  <g id="157">
   <use class="kv35" height="30" transform="rotate(0,1345.21,184.588) scale(-0.947693,0.6712) translate(-2765.06,85.4916)" width="15" x="1338.103492892157" xlink:href="#Disconnector:刀闸_0" y="174.5197795335134" zvalue="1723"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453992185859" ObjectName="35kV西掌线3526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453992185859"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1345.21,184.588) scale(-0.947693,0.6712) translate(-2765.06,85.4916)" width="15" x="1338.103492892157" y="174.5197795335134"/></g>
  <g id="131">
   <use class="kv35" height="30" transform="rotate(180,1345.7,313.857) scale(-0.947693,0.6712) translate(-2766.07,148.816)" width="15" x="1338.595940718963" xlink:href="#Disconnector:刀闸_0" y="303.7885325852712" zvalue="1728"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453991989251" ObjectName="35kV西掌线3521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453991989251"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1345.7,313.857) scale(-0.947693,0.6712) translate(-2766.07,148.816)" width="15" x="1338.595940718963" y="303.7885325852712"/></g>
  <g id="95">
   <use class="kv35" height="30" transform="rotate(270,1326.4,139.169) scale(-0.947693,0.6712) translate(-2726.4,63.2424)" width="15" x="1319.293226036425" xlink:href="#Disconnector:刀闸_0" y="129.1010127876037" zvalue="1739"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453991530499" ObjectName="35kV西掌线3529隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453991530499"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1326.4,139.169) scale(-0.947693,0.6712) translate(-2726.4,63.2424)" width="15" x="1319.293226036425" y="129.1010127876037"/></g>
  <g id="256">
   <use class="kv10" height="30" transform="rotate(180,897.667,825.891) scale(0.947693,-0.6712) translate(49.1536,-2061.29)" width="15" x="890.5589697813774" xlink:href="#Disconnector:刀闸_0" y="815.8230764122643" zvalue="1769"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453993234435" ObjectName="10kV2号电容器0526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453993234435"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,897.667,825.891) scale(0.947693,-0.6712) translate(49.1536,-2061.29)" width="15" x="890.5589697813774" y="815.8230764122643"/></g>
  <g id="317">
   <use class="kv10" height="30" transform="rotate(180,1055.67,801.891) scale(0.947693,-0.6712) translate(57.8743,-2001.53)" width="15" x="1048.558969781378" xlink:href="#Disconnector:刀闸_0" y="791.8230764122643" zvalue="1794"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453993365507" ObjectName="10kV崩强线0536隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453993365507"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1055.67,801.891) scale(0.947693,-0.6712) translate(57.8743,-2001.53)" width="15" x="1048.558969781378" y="791.8230764122643"/></g>
  <g id="332">
   <use class="kv10" height="30" transform="rotate(180,1217.67,801.891) scale(0.947693,-0.6712) translate(66.8157,-2001.53)" width="15" x="1210.558969781378" xlink:href="#Disconnector:刀闸_0" y="791.8230764122643" zvalue="1819"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453993889795" ObjectName="10kV毛讲线0546隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453993889795"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1217.67,801.891) scale(0.947693,-0.6712) translate(66.8157,-2001.53)" width="15" x="1210.558969781378" y="791.8230764122643"/></g>
  <g id="347">
   <use class="kv10" height="30" transform="rotate(180,1379.67,801.891) scale(0.947693,-0.6712) translate(75.7572,-2001.53)" width="15" x="1372.558969781378" xlink:href="#Disconnector:刀闸_0" y="791.8230764122643" zvalue="1835"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453994217475" ObjectName="10kV邦角线0556隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453994217475"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1379.67,801.891) scale(0.947693,-0.6712) translate(75.7572,-2001.53)" width="15" x="1372.558969781378" y="791.8230764122643"/></g>
  <g id="364">
   <use class="kv10" height="30" transform="rotate(180,1525.67,801.891) scale(0.947693,-0.6712) translate(83.8155,-2001.53)" width="15" x="1518.558969781378" xlink:href="#Disconnector:刀闸_0" y="791.8230764122643" zvalue="1851"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453994545155" ObjectName="10kV西山街道线0566隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453994545155"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1525.67,801.891) scale(0.947693,-0.6712) translate(83.8155,-2001.53)" width="15" x="1518.558969781378" y="791.8230764122643"/></g>
  <g id="1">
   <use class="kv35" height="30" transform="rotate(0,1207,317) scale(1,1) translate(0,0)" width="15" x="1199.5" xlink:href="#Disconnector:令克_0" y="302" zvalue="1890"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454089572355" ObjectName="1号站用高压跌落保险"/>
   <cge:TPSR_Ref TObjectID="6192454089572355"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1207,317) scale(1,1) translate(0,0)" width="15" x="1199.5" y="302"/></g>
  <g id="22">
   <use class="kv10" height="30" transform="rotate(0,1429,853) scale(1,-1) translate(0,-1706)" width="15" x="1421.5" xlink:href="#Disconnector:令克_0" y="838" zvalue="1894"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454089637891" ObjectName="2号站用高压跌落保险"/>
   <cge:TPSR_Ref TObjectID="6192454089637891"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1429,853) scale(1,-1) translate(0,-1706)" width="15" x="1421.5" y="838"/></g>
  <g id="86">
   <use class="kv10" height="26" transform="rotate(0,1580.25,634.252) scale(1.66667,1.42857) translate(-628.099,-184.704)" width="12" x="1570.246598443762" xlink:href="#Disconnector:单手车刀闸1212_0" y="615.6807396958916" zvalue="1898"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454089768963" ObjectName="10kV分段0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454089768963"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1580.25,634.252) scale(1.66667,1.42857) translate(-628.099,-184.704)" width="12" x="1570.246598443762" y="615.6807396958916"/></g>
  <g id="104">
   <use class="kv35" height="30" transform="rotate(180,1495.7,313.857) scale(-0.947693,0.6712) translate(-3074.35,148.816)" width="15" x="1488.595940718963" xlink:href="#Disconnector:刀闸_0" y="303.7885325852712" zvalue="1905"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454089965571" ObjectName="35kV分段3121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454089965571"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1495.7,313.857) scale(-0.947693,0.6712) translate(-3074.35,148.816)" width="15" x="1488.595940718963" y="303.7885325852712"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="136">
   <path class="kv35" d="M 1139.46 376.48 L 1139.46 349.09" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@1" LinkObjectIDznd="137@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1139.46 376.48 L 1139.46 349.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv35" d="M 853.93 384.12 L 853.93 349.09" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@0" LinkObjectIDznd="137@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 853.93 384.12 L 853.93 349.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv35" d="M 872.56 370.1 L 853.93 370.1" stroke-width="1" zvalue="47"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 872.56 370.1 L 853.93 370.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="kv35" d="M 853.95 403.75 L 853.95 430.89" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@1" LinkObjectIDznd="480@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 853.95 403.75 L 853.95 430.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv35" d="M 1043.15 229.06 L 1043.15 194.48" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="119@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1043.15 229.06 L 1043.15 194.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv35" d="M 1043.13 174.85 L 1043.13 125.84" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="121@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1043.13 174.85 L 1043.13 125.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv35" d="M 1043.79 311.59 L 1043.79 349.09" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="137@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1043.79 311.59 L 1043.79 349.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="kv35" d="M 1043.31 254.95 L 1043.31 291.96" stroke-width="1" zvalue="71"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@1" LinkObjectIDznd="112@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1043.31 254.95 L 1043.31 291.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv35" d="M 1139.48 434.28 L 1139.48 396.11" stroke-width="1" zvalue="73"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@1" LinkObjectIDznd="272@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1139.48 434.28 L 1139.48 396.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv35" d="M 1139.64 460.16 L 1139.64 480.58" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="348@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1139.64 460.16 L 1139.64 480.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv10" d="M 711.39 647.78 L 711.39 674" stroke-width="1" zvalue="805"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="658@0" LinkObjectIDznd="179@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 711.39 647.78 L 711.39 674" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="234">
   <path class="kv10" d="M 1139.5 567.05 L 1139.37 611.67" stroke-width="1" zvalue="878"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="348@1" LinkObjectIDznd="233@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1139.5 567.05 L 1139.37 611.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv10" d="M 1139.37 652.22 L 1139.37 674" stroke-width="1" zvalue="898"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@1" LinkObjectIDznd="179@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1139.37 652.22 L 1139.37 674" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="kv10" d="M 736.11 674 L 736.11 709.78" stroke-width="1" zvalue="912"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@8" LinkObjectIDznd="189@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 736.11 674 L 736.11 709.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv10" d="M 736.11 750.33 L 736.11 816.16" stroke-width="1" zvalue="1354"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189@1" LinkObjectIDznd="388@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 736.11 750.33 L 736.11 816.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv35" d="M 871.56 421.15 L 853.95 421.15" stroke-width="1" zvalue="1533"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="130" MaxPinNum="2"/>
   </metadata>
  <path d="M 871.56 421.15 L 853.95 421.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv35" d="M 1117.44 411.08 L 1139.48 411.08" stroke-width="1" zvalue="1539"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="105" MaxPinNum="2"/>
   </metadata>
  <path d="M 1117.44 411.08 L 1139.48 411.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv10" d="M 745.41 874.27 L 735.61 874.27" stroke-width="1" zvalue="1545"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="239@0" LinkObjectIDznd="10" MaxPinNum="2"/>
   </metadata>
  <path d="M 745.41 874.27 L 735.61 874.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv35" d="M 1034.3 139.23 L 1043.13 139.23" stroke-width="1" zvalue="1710"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@1" LinkObjectIDznd="111" MaxPinNum="2"/>
   </metadata>
  <path d="M 1034.3 139.23 L 1043.13 139.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv35" d="M 1004.86 139.25 L 1014.67 139.25" stroke-width="1" zvalue="1711"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="101@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004.86 139.25 L 1014.67 139.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv35" d="M 1064.18 273.38 L 1043.31 273.38" stroke-width="1" zvalue="1713"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="107" MaxPinNum="2"/>
   </metadata>
  <path d="M 1064.18 273.38 L 1043.31 273.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv35" d="M 1064.18 211.38 L 1043.15 211.38" stroke-width="1" zvalue="1714"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31@0" LinkObjectIDznd="117" MaxPinNum="2"/>
   </metadata>
  <path d="M 1064.18 211.38 L 1043.15 211.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv35" d="M 1064.18 162.38 L 1043.13 162.38" stroke-width="1" zvalue="1715"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@0" LinkObjectIDznd="111" MaxPinNum="2"/>
   </metadata>
  <path d="M 1064.18 162.38 L 1043.13 162.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv35" d="M 1345.15 229.06 L 1345.15 194.48" stroke-width="1" zvalue="1727"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="157@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1345.15 229.06 L 1345.15 194.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv35" d="M 1345.13 174.85 L 1345.13 126.02" stroke-width="1" zvalue="1730"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="270@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1345.13 174.85 L 1345.13 126.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv35" d="M 1345.31 254.95 L 1345.31 303.96" stroke-width="1" zvalue="1732"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@1" LinkObjectIDznd="131@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1345.31 254.95 L 1345.31 303.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv35" d="M 1336.3 139.23 L 1345.13 139.23" stroke-width="1" zvalue="1741"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@1" LinkObjectIDznd="128" MaxPinNum="2"/>
   </metadata>
  <path d="M 1336.3 139.23 L 1345.13 139.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv35" d="M 1306.86 139.25 L 1316.67 139.25" stroke-width="1" zvalue="1742"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@0" LinkObjectIDznd="95@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1306.86 139.25 L 1316.67 139.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv35" d="M 1366.18 273.38 L 1345.31 273.38" stroke-width="1" zvalue="1744"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="126" MaxPinNum="2"/>
   </metadata>
  <path d="M 1366.18 273.38 L 1345.31 273.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv35" d="M 1366.18 211.38 L 1345.15 211.38" stroke-width="1" zvalue="1745"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="150" MaxPinNum="2"/>
   </metadata>
  <path d="M 1366.18 211.38 L 1345.15 211.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv35" d="M 1366.18 162.38 L 1345.13 162.38" stroke-width="1" zvalue="1746"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="128" MaxPinNum="2"/>
   </metadata>
  <path d="M 1366.18 162.38 L 1345.13 162.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv35" d="M 1345.79 323.59 L 1345.79 349.09" stroke-width="1" zvalue="1747"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="137@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1345.79 323.59 L 1345.79 349.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv10" d="M 1150.65 588.5 L 1139.39 588.5" stroke-width="1" zvalue="1752"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8@0" LinkObjectIDznd="234" MaxPinNum="2"/>
   </metadata>
  <path d="M 1150.65 588.5 L 1139.39 588.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv10" d="M 711.25 610.56 L 711.25 610.79" stroke-width="1" zvalue="1753"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="659@0" LinkObjectIDznd="658@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 711.25 610.56 L 711.25 610.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv10" d="M 700.35 657.5 L 711.39 657.5" stroke-width="1" zvalue="1757"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 700.35 657.5 L 711.39 657.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 747.31 770.56 L 736.11 770.56" stroke-width="1" zvalue="1763"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@0" LinkObjectIDznd="197" MaxPinNum="2"/>
   </metadata>
  <path d="M 747.31 770.56 L 736.11 770.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv10" d="M 755.31 800.07 L 736.11 800.07" stroke-width="1" zvalue="1766"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="197" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.31 800.07 L 736.11 800.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv10" d="M 752.31 856.07 L 735.61 856.07" stroke-width="1" zvalue="1767"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="10" MaxPinNum="2"/>
   </metadata>
  <path d="M 752.31 856.07 L 735.61 856.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="253">
   <path class="kv10" d="M 898.11 674 L 898.11 709.78" stroke-width="1" zvalue="1772"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@2" LinkObjectIDznd="254@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.11 674 L 898.11 709.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="kv10" d="M 898.11 750.33 L 898.11 816.16" stroke-width="1" zvalue="1779"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="254@1" LinkObjectIDznd="256@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.11 750.33 L 898.11 816.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="kv10" d="M 906.83 875.97 L 897.61 875.97" stroke-width="1" zvalue="1780"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="241@0" LinkObjectIDznd="28" MaxPinNum="2"/>
   </metadata>
  <path d="M 906.83 875.97 L 897.61 875.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv10" d="M 909.31 770.56 L 898.11 770.56" stroke-width="1" zvalue="1790"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="244@0" LinkObjectIDznd="232" MaxPinNum="2"/>
   </metadata>
  <path d="M 909.31 770.56 L 898.11 770.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="206">
   <path class="kv10" d="M 917.31 800.07 L 898.11 800.07" stroke-width="1" zvalue="1791"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="232" MaxPinNum="2"/>
   </metadata>
  <path d="M 917.31 800.07 L 898.11 800.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 914.31 856.07 L 897.61 856.07" stroke-width="1" zvalue="1792"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@0" LinkObjectIDznd="28" MaxPinNum="2"/>
   </metadata>
  <path d="M 914.31 856.07 L 897.61 856.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="315">
   <path class="kv10" d="M 1056.11 674 L 1056.11 709.78" stroke-width="1" zvalue="1797"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@6" LinkObjectIDznd="316@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1056.11 674 L 1056.11 709.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="314">
   <path class="kv10" d="M 1055.61 811.79 L 1055.61 847.74" stroke-width="1" zvalue="1799"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="317@1" LinkObjectIDznd="306@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1055.61 811.79 L 1055.61 847.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="310">
   <path class="kv10" d="M 1056.11 750.33 L 1056.11 792.16" stroke-width="1" zvalue="1804"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="316@1" LinkObjectIDznd="317@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1056.11 750.33 L 1056.11 792.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="303">
   <path class="kv10" d="M 1067.31 770.56 L 1056.11 770.56" stroke-width="1" zvalue="1815"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="313@0" LinkObjectIDznd="310" MaxPinNum="2"/>
   </metadata>
  <path d="M 1067.31 770.56 L 1056.11 770.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="330">
   <path class="kv10" d="M 1218.11 674 L 1218.11 709.78" stroke-width="1" zvalue="1822"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@5" LinkObjectIDznd="331@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1218.11 674 L 1218.11 709.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="329">
   <path class="kv10" d="M 1217.61 811.79 L 1217.61 847.74" stroke-width="1" zvalue="1824"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="332@1" LinkObjectIDznd="323@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1217.61 811.79 L 1217.61 847.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="325">
   <path class="kv10" d="M 1218.11 750.33 L 1218.11 792.16" stroke-width="1" zvalue="1829"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="331@1" LinkObjectIDznd="332@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1218.11 750.33 L 1218.11 792.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="322">
   <path class="kv10" d="M 1229.31 770.56 L 1218.11 770.56" stroke-width="1" zvalue="1833"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="328@0" LinkObjectIDznd="325" MaxPinNum="2"/>
   </metadata>
  <path d="M 1229.31 770.56 L 1218.11 770.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="345">
   <path class="kv10" d="M 1380.11 674 L 1380.11 709.78" stroke-width="1" zvalue="1838"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@4" LinkObjectIDznd="346@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1380.11 674 L 1380.11 709.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="344">
   <path class="kv10" d="M 1379.61 811.79 L 1379.61 847.74" stroke-width="1" zvalue="1840"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="347@1" LinkObjectIDznd="338@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1379.61 811.79 L 1379.61 847.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="340">
   <path class="kv10" d="M 1380.11 750.33 L 1380.11 792.16" stroke-width="1" zvalue="1845"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="346@1" LinkObjectIDznd="347@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1380.11 750.33 L 1380.11 792.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="339">
   <path class="kv10" d="M 1457.03 828.88 L 1379.61 828.88" stroke-width="1" zvalue="1846"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="342@0" LinkObjectIDznd="344" MaxPinNum="2"/>
   </metadata>
  <path d="M 1457.03 828.88 L 1379.61 828.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="337">
   <path class="kv10" d="M 1391.31 770.56 L 1380.11 770.56" stroke-width="1" zvalue="1849"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="343@0" LinkObjectIDznd="340" MaxPinNum="2"/>
   </metadata>
  <path d="M 1391.31 770.56 L 1380.11 770.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="362">
   <path class="kv10" d="M 1526.11 674 L 1526.11 709.78" stroke-width="1" zvalue="1854"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@3" LinkObjectIDznd="363@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1526.11 674 L 1526.11 709.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="361">
   <path class="kv10" d="M 1525.61 811.79 L 1525.61 846.74" stroke-width="1" zvalue="1856"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="364@1" LinkObjectIDznd="355@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1525.61 811.79 L 1525.61 846.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="357">
   <path class="kv10" d="M 1526.11 750.33 L 1526.11 792.16" stroke-width="1" zvalue="1861"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="363@1" LinkObjectIDznd="364@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1526.11 750.33 L 1526.11 792.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="354">
   <path class="kv10" d="M 1537.31 770.56 L 1526.11 770.56" stroke-width="1" zvalue="1865"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="360@0" LinkObjectIDznd="357" MaxPinNum="2"/>
   </metadata>
  <path d="M 1537.31 770.56 L 1526.11 770.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv10" d="M 735.61 835.79 L 735.61 891.33" stroke-width="1" zvalue="1873"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="388@1" LinkObjectIDznd="2@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 735.61 835.79 L 735.61 891.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv10" d="M 735.61 891.33 L 735.61 929.25" stroke-width="1" zvalue="1874"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10" LinkObjectIDznd="37@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 735.61 891.33 L 735.61 929.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv10" d="M 897.61 835.79 L 897.61 887.35" stroke-width="1" zvalue="1878"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="256@1" LinkObjectIDznd="13@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 897.61 835.79 L 897.61 887.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv10" d="M 897.61 887.35 L 897.51 927.25" stroke-width="1" zvalue="1879"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28" LinkObjectIDznd="220@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 897.61 887.35 L 897.51 927.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv35" d="M 1206.65 289.98 L 1206.65 303.75" stroke-width="1" zvalue="1891"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@0" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1206.65 289.98 L 1206.65 303.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv35" d="M 1206.92 329.25 L 1206.92 349.09" stroke-width="1" zvalue="1892"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@1" LinkObjectIDznd="137@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1206.92 329.25 L 1206.92 349.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv10" d="M 1429.25 880.72 L 1429.25 866.25" stroke-width="1" zvalue="1895"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="367@0" LinkObjectIDznd="22@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1429.25 880.72 L 1429.25 866.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv10" d="M 1428.92 840.75 L 1428.92 828.88" stroke-width="1" zvalue="1896"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@1" LinkObjectIDznd="339" MaxPinNum="2"/>
   </metadata>
  <path d="M 1428.92 840.75 L 1428.92 828.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv10" d="M 1580.39 652.78 L 1580.39 674" stroke-width="1" zvalue="1900"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="179@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1580.39 652.78 L 1580.39 674" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv10" d="M 1580.25 595.5 L 1580.25 615.79" stroke-width="1" zvalue="1901"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="86@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1580.25 595.5 L 1580.25 615.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv35" d="M 1495.79 323.59 L 1495.79 349.09" stroke-width="1" zvalue="1910"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="137@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1495.79 323.59 L 1495.79 349.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv35" d="M 1516.18 273.38 L 1495.76 273.38 L 1495.76 303.96" stroke-width="1" zvalue="1912"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="104@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1516.18 273.38 L 1495.76 273.38 L 1495.76 303.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv10" d="M 1140.03 588.5 L 1139.39 588.5" stroke-width="1" zvalue="1914"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="17" LinkObjectIDznd="234" MaxPinNum="2"/>
   </metadata>
  <path d="M 1140.03 588.5 L 1139.39 588.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv10" d="M 1140.03 588.5 L 1139.4 588.5" stroke-width="1" zvalue="1915"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56" LinkObjectIDznd="234" MaxPinNum="2"/>
   </metadata>
  <path d="M 1140.03 588.5 L 1139.4 588.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="170">
   <use class="kv35" height="20" transform="rotate(90,882.374,370.039) scale(1.24619,-1.0068) translate(-173.084,-737.511)" width="10" x="876.1432423998622" xlink:href="#GroundDisconnector:地刀_0" y="359.9709996499512" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453991333891" ObjectName="35kVⅠ段母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453991333891"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,882.374,370.039) scale(1.24619,-1.0068) translate(-173.084,-737.511)" width="10" x="876.1432423998622" y="359.9709996499512"/></g>
  <g id="118">
   <use class="kv35" height="20" transform="rotate(90,1074,162.315) scale(1.24619,-1.0068) translate(-210.94,-323.465)" width="10" x="1067.768229048422" xlink:href="#GroundDisconnector:地刀_0" y="152.2465078133259" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453989826563" ObjectName="35kV遮西线35167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453989826563"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1074,162.315) scale(1.24619,-1.0068) translate(-210.94,-323.465)" width="10" x="1067.768229048422" y="152.2465078133259"/></g>
  <g id="237">
   <use class="kv10" height="29" transform="rotate(270,765,770.5) scale(-0.904762,1.31034) translate(-1611.53,-177.987)" width="21" x="755.5" xlink:href="#GroundDisconnector:地刀-带电显示_0" y="751.5" zvalue="916"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453990219779" ObjectName="10kV1号电容器05117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453990219779"/></metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(270,765,770.5) scale(-0.904762,1.31034) translate(-1611.53,-177.987)" width="21" x="755.5" y="751.5"/></g>
  <g id="31">
   <use class="kv35" height="20" transform="rotate(90,1074,211.315) scale(1.24619,-1.0068) translate(-210.94,-421.134)" width="10" x="1067.768229212517" xlink:href="#GroundDisconnector:地刀_0" y="201.2465078133259" zvalue="1525"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453990481923" ObjectName="35kV遮西线35160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453990481923"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1074,211.315) scale(1.24619,-1.0068) translate(-210.94,-421.134)" width="10" x="1067.768229212517" y="201.2465078133259"/></g>
  <g id="36">
   <use class="kv35" height="20" transform="rotate(90,1074,273.315) scale(1.24619,-1.0068) translate(-210.94,-544.715)" width="10" x="1067.768229048422" xlink:href="#GroundDisconnector:地刀_0" y="263.2465078133259" zvalue="1528"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453990612995" ObjectName="35kV遮西线35117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453990612995"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1074,273.315) scale(1.24619,-1.0068) translate(-210.94,-544.715)" width="10" x="1067.768229048422" y="263.2465078133259"/></g>
  <g id="49">
   <use class="kv35" height="20" transform="rotate(90,881.374,421.085) scale(1.24619,-1.0068) translate(-172.886,-839.257)" width="10" x="875.1432423998622" xlink:href="#GroundDisconnector:地刀_0" y="411.0168231782558" zvalue="1534"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453991202819" ObjectName="35kVⅠ段母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453991202819"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,881.374,421.085) scale(1.24619,-1.0068) translate(-172.886,-839.257)" width="10" x="875.1432423998622" y="411.0168231782558"/></g>
  <g id="69">
   <use class="kv35" height="20" transform="rotate(270,1107.63,411.143) scale(1.24619,-1.0068) translate(-217.583,-819.44)" width="10" x="1101.394890349081" xlink:href="#GroundDisconnector:地刀_0" y="401.0748086033776" zvalue="1537"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453990744067" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453990744067"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1107.63,411.143) scale(1.24619,-1.0068) translate(-217.583,-819.44)" width="10" x="1101.394890349081" y="401.0748086033776"/></g>
  <g id="92">
   <use class="kv10" height="20" transform="rotate(270,767.5,800.007) scale(-1.25,1.25) translate(-1380.25,-157.501)" width="10" x="761.25" xlink:href="#GroundDisconnector:地刀_0" y="787.5069444444446" zvalue="1547"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453990875139" ObjectName="10kV1号电容器05160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453990875139"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,767.5,800.007) scale(-1.25,1.25) translate(-1380.25,-157.501)" width="10" x="761.25" y="787.5069444444446"/></g>
  <g id="96">
   <use class="kv10" height="20" transform="rotate(270,764.5,856.007) scale(-1.25,1.25) translate(-1374.85,-168.701)" width="10" x="758.25" xlink:href="#GroundDisconnector:地刀_0" y="843.5069444444446" zvalue="1551"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453991006211" ObjectName="10kV1号电容器05167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453991006211"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,764.5,856.007) scale(-1.25,1.25) translate(-1374.85,-168.701)" width="10" x="758.25" y="843.5069444444446"/></g>
  <g id="155">
   <use class="kv35" height="20" transform="rotate(90,1376,162.315) scale(1.24619,-1.0068) translate(-270.601,-323.465)" width="10" x="1369.768229048422" xlink:href="#GroundDisconnector:地刀_0" y="152.2465078133259" zvalue="1725"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453992120323" ObjectName="35kV西掌线35267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453992120323"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1376,162.315) scale(1.24619,-1.0068) translate(-270.601,-323.465)" width="10" x="1369.768229048422" y="152.2465078133259"/></g>
  <g id="106">
   <use class="kv35" height="20" transform="rotate(90,1376,211.315) scale(1.24619,-1.0068) translate(-270.601,-421.134)" width="10" x="1369.768229212517" xlink:href="#GroundDisconnector:地刀_0" y="201.2465078133259" zvalue="1735"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453991792643" ObjectName="35kV西掌线35260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453991792643"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1376,211.315) scale(1.24619,-1.0068) translate(-270.601,-421.134)" width="10" x="1369.768229212517" y="201.2465078133259"/></g>
  <g id="99">
   <use class="kv35" height="20" transform="rotate(90,1376,273.315) scale(1.24619,-1.0068) translate(-270.601,-544.715)" width="10" x="1369.768229048422" xlink:href="#GroundDisconnector:地刀_0" y="263.2465078133259" zvalue="1737"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453991661571" ObjectName="35kV西掌线35217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453991661571"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1376,273.315) scale(1.24619,-1.0068) translate(-270.601,-544.715)" width="10" x="1369.768229048422" y="263.2465078133259"/></g>
  <g id="37">
   <use class="kv10" height="20" transform="rotate(0,735,939) scale(1,1) translate(0,0)" width="10" x="730" xlink:href="#GroundDisconnector:地刀_0" y="929" zvalue="1761"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453992513539" ObjectName="10kV1号电容器05110接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453992513539"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,735,939) scale(1,1) translate(0,0)" width="10" x="730" y="929"/></g>
  <g id="244">
   <use class="kv10" height="29" transform="rotate(270,927,770.5) scale(-0.904762,1.31034) translate(-1952.58,-177.987)" width="21" x="917.5" xlink:href="#GroundDisconnector:地刀-带电显示_0" y="751.5" zvalue="1775"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453993168899" ObjectName="10kV2号电容器05217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453993168899"/></metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(270,927,770.5) scale(-0.904762,1.31034) translate(-1952.58,-177.987)" width="21" x="917.5" y="751.5"/></g>
  <g id="230">
   <use class="kv10" height="20" transform="rotate(270,929.5,800.007) scale(-1.25,1.25) translate(-1671.85,-157.501)" width="10" x="923.25" xlink:href="#GroundDisconnector:地刀_0" y="787.5069444444446" zvalue="1781"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453992972291" ObjectName="10kV2号电容器05260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453992972291"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,929.5,800.007) scale(-1.25,1.25) translate(-1671.85,-157.501)" width="10" x="923.25" y="787.5069444444446"/></g>
  <g id="229">
   <use class="kv10" height="20" transform="rotate(270,926.5,856.007) scale(-1.25,1.25) translate(-1666.45,-168.701)" width="10" x="920.25" xlink:href="#GroundDisconnector:地刀_0" y="843.5069444444446" zvalue="1783"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453992841219" ObjectName="10kV2号电容器05267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453992841219"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,926.5,856.007) scale(-1.25,1.25) translate(-1666.45,-168.701)" width="10" x="920.25" y="843.5069444444446"/></g>
  <g id="220">
   <use class="kv10" height="20" transform="rotate(0,897.462,937) scale(1,1) translate(0,0)" width="10" x="892.4615384615385" xlink:href="#GroundDisconnector:地刀_0" y="927" zvalue="1787"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453992710147" ObjectName="10kV2号电容器05210接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453992710147"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,897.462,937) scale(1,1) translate(0,0)" width="10" x="892.4615384615385" y="927"/></g>
  <g id="313">
   <use class="kv10" height="29" transform="rotate(270,1085,770.5) scale(-0.904762,1.31034) translate(-2285.21,-177.987)" width="21" x="1075.5" xlink:href="#GroundDisconnector:地刀-带电显示_0" y="751.5" zvalue="1800"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453993496579" ObjectName="10kV崩强线05317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453993496579"/></metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(270,1085,770.5) scale(-0.904762,1.31034) translate(-2285.21,-177.987)" width="21" x="1075.5" y="751.5"/></g>
  <g id="328">
   <use class="kv10" height="29" transform="rotate(270,1247,770.5) scale(-0.904762,1.31034) translate(-2626.26,-177.987)" width="21" x="1237.5" xlink:href="#GroundDisconnector:地刀-带电显示_0" y="751.5" zvalue="1825"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453993824259" ObjectName="10kV毛讲线05417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453993824259"/></metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(270,1247,770.5) scale(-0.904762,1.31034) translate(-2626.26,-177.987)" width="21" x="1237.5" y="751.5"/></g>
  <g id="343">
   <use class="kv10" height="29" transform="rotate(270,1409,770.5) scale(-0.904762,1.31034) translate(-2967.32,-177.987)" width="21" x="1399.5" xlink:href="#GroundDisconnector:地刀-带电显示_0" y="751.5" zvalue="1841"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453994151939" ObjectName="10kV邦角线05517接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453994151939"/></metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(270,1409,770.5) scale(-0.904762,1.31034) translate(-2967.32,-177.987)" width="21" x="1399.5" y="751.5"/></g>
  <g id="360">
   <use class="kv10" height="29" transform="rotate(270,1555,770.5) scale(-0.904762,1.31034) translate(-3274.68,-177.987)" width="21" x="1545.5" xlink:href="#GroundDisconnector:地刀-带电显示_0" y="751.5" zvalue="1857"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453994479619" ObjectName="10kV西山街道线05617接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453994479619"/></metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(270,1555,770.5) scale(-0.904762,1.31034) translate(-3274.68,-177.987)" width="21" x="1545.5" y="751.5"/></g>
  <g id="103">
   <use class="kv35" height="20" transform="rotate(90,1526,273.315) scale(1.24619,-1.0068) translate(-300.233,-544.715)" width="10" x="1519.768229048422" xlink:href="#GroundDisconnector:地刀_0" y="263.2465078133259" zvalue="1907"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454089900035" ObjectName="35kV分段31217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454089900035"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1526,273.315) scale(1.24619,-1.0068) translate(-300.233,-544.715)" width="10" x="1519.768229048422" y="263.2465078133259"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="121">
   <use class="kv35" height="40" transform="rotate(0,1043.26,106.5) scale(1.06044,0.975) translate(-58.5572,2.23075)" width="30" x="1027.35391157708" xlink:href="#ACLineSegment:线路带壁雷器_0" y="87" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249324519428" ObjectName="35kV遮西线"/>
   <cge:TPSR_Ref TObjectID="8444249324519428_5066549680406529"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1043.26,106.5) scale(1.06044,0.975) translate(-58.5572,2.23075)" width="30" x="1027.35391157708" y="87"/></g>
  <g id="270">
   <use class="kv35" height="40" transform="rotate(0,1344,106.186) scale(1,1) translate(0,0)" width="30" x="1329" xlink:href="#ACLineSegment:线路带壁雷器_0" y="86.18598707192189" zvalue="2011"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249309315078" ObjectName="35kV西掌线西掌T线"/>
   <cge:TPSR_Ref TObjectID="8444249309315078_5066549680406529"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1344,106.186) scale(1,1) translate(0,0)" width="30" x="1329" y="86.18598707192189"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="348">
   <g id="3480">
    <use class="kv35" height="30" transform="rotate(0,1139.5,523.629) scale(3.04167,3.09142) translate(-740.37,-322.876)" width="24" x="1103" xlink:href="#PowerTransformer2:可调不带中性点_0" y="477.26" zvalue="367"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874573348866" ObjectName="35"/>
    </metadata>
   </g>
   <g id="3481">
    <use class="kv10" height="30" transform="rotate(0,1139.5,523.629) scale(3.04167,3.09142) translate(-740.37,-322.876)" width="24" x="1103" xlink:href="#PowerTransformer2:可调不带中性点_1" y="477.26" zvalue="367"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874573414402" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399526187010" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399526187010"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1139.5,523.629) scale(3.04167,3.09142) translate(-740.37,-322.876)" width="24" x="1103" y="477.26"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="62">
   <use class="kv35" height="40" transform="rotate(90,980.209,139.252) scale(1.42915,1.29168) translate(-290.047,-25.6114)" width="20" x="965.9170741598458" xlink:href="#Accessory:线路PT带避雷器0904_0" y="113.4186677485551" zvalue="441"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453989564419" ObjectName="35kV遮西线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,980.209,139.252) scale(1.42915,1.29168) translate(-290.047,-25.6114)" width="20" x="965.9170741598458" y="113.4186677485551"/></g>
  <g id="659">
   <use class="kv10" height="42" transform="rotate(180,723.311,584.406) scale(-1.26676,-1.28355) translate(-1290.3,-1033.76)" width="30" x="704.3098420727794" xlink:href="#Accessory:4卷PT带容断器_0" y="557.4514414652258" zvalue="776"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453990088707" ObjectName="10kV母线PT"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(180,723.311,584.406) scale(-1.26676,-1.28355) translate(-1290.3,-1033.76)" width="30" x="704.3098420727794" y="557.4514414652258"/></g>
  <g id="239">
   <use class="kv10" height="26" transform="rotate(270,761.586,874.299) scale(0.833333,1.30769) translate(151.317,-201.717)" width="12" x="756.5858974358974" xlink:href="#Accessory:避雷器1_0" y="857.2989536208445" zvalue="919"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453990285315" ObjectName="10kV1号电容器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,761.586,874.299) scale(0.833333,1.30769) translate(151.317,-201.717)" width="12" x="756.5858974358974" y="857.2989536208445"/></g>
  <g id="480">
   <use class="kv35" height="42" transform="rotate(180,841.891,457.045) scale(1.26676,1.28355) translate(-173.288,-95.0116)" width="30" x="822.8900659381775" xlink:href="#Accessory:4卷PT带容断器_0" y="430.0909118652344" zvalue="1122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453991464963" ObjectName="35kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(180,841.891,457.045) scale(1.26676,1.28355) translate(-173.288,-95.0116)" width="30" x="822.8900659381775" y="430.0909118652344"/></g>
  <g id="122">
   <use class="kv35" height="40" transform="rotate(90,1282.21,139.252) scale(1.42915,1.29168) translate(-380.732,-25.6114)" width="20" x="1267.917074159846" xlink:href="#Accessory:线路PT带避雷器0904_0" y="113.4186677485551" zvalue="1734"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453991858179" ObjectName="35kV西掌线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1282.21,139.252) scale(1.42915,1.29168) translate(-380.732,-25.6114)" width="20" x="1267.917074159846" y="113.4186677485551"/></g>
  <g id="8">
   <use class="kv10" height="20" transform="rotate(270,1165.5,588.5) scale(1.65,1.65) translate(-452.636,-225.333)" width="20" x="1149" xlink:href="#Accessory:带电显示器_0" y="572" zvalue="1751"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453992316931" ObjectName="#1主变10kV侧带电显示器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1165.5,588.5) scale(1.65,1.65) translate(-452.636,-225.333)" width="20" x="1149" y="572"/></g>
  <g id="26">
   <use class="kv10" height="20" transform="rotate(90,685.5,657.5) scale(1.65,1.65) translate(-263.545,-252.515)" width="20" x="669" xlink:href="#Accessory:带电显示器_0" y="641" zvalue="1756"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453992382467" ObjectName="Ⅰ母PT带电显示"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,685.5,657.5) scale(1.65,1.65) translate(-263.545,-252.515)" width="20" x="669" y="641"/></g>
  <g id="241">
   <use class="kv10" height="26" transform="rotate(270,923,876) scale(0.833333,1.30769) translate(183.6,-202.118)" width="12" x="918" xlink:href="#Accessory:避雷器1_0" y="859" zvalue="1777"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453993037827" ObjectName="10kV2号电容器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,923,876) scale(0.833333,1.30769) translate(183.6,-202.118)" width="12" x="918" y="859"/></g>
  <g id="342">
   <use class="kv10" height="26" transform="rotate(0,1457,845.056) scale(0.833333,1.30769) translate(290.4,-194.837)" width="12" x="1452" xlink:href="#Accessory:避雷器1_0" y="828.0555555555557" zvalue="1843"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453994020867" ObjectName="10kV2号站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1457,845.056) scale(0.833333,1.30769) translate(290.4,-194.837)" width="12" x="1452" y="828.0555555555557"/></g>
  <g id="65">
   <use class="kv10" height="20" transform="rotate(90,1565.4,595.5) scale(1.65,1.65) translate(-610.173,-228.091)" width="20" x="1548.900579488035" xlink:href="#Accessory:带电显示器_0" y="579" zvalue="1902"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454089703427" ObjectName="10kV分段带点显示"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1565.4,595.5) scale(1.65,1.65) translate(-610.173,-228.091)" width="20" x="1548.900579488035" y="579"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="477">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="477" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1035.55,14.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1035.08" xml:space="preserve" y="19.42" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133856129027" ObjectName="P"/>
   </metadata>
  </g>
  <g id="478">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="478" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1035.55,29.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1035.08" xml:space="preserve" y="34.42" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133856194563" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="479">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="479" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1034.55,45.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1034.08" xml:space="preserve" y="50.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133856260099" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="53" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1219.53,422.35) scale(1,1) translate(0,0)" writing-mode="lr" x="1218.95" xml:space="preserve" y="428.05" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133853573123" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="54" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1219.53,437.35) scale(1,1) translate(0,0)" writing-mode="lr" x="1218.95" xml:space="preserve" y="443.05" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133853638659" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="55" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1219.53,455.35) scale(1,1) translate(0,0)" writing-mode="lr" x="1218.95" xml:space="preserve" y="461.05" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133853835267" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="61">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="61" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1065.53,606.5) scale(1,1) translate(-2.24271e-13,0)" writing-mode="lr" x="1064.95" xml:space="preserve" y="612.2" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133853704195" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="63" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1065.53,623.5) scale(1,1) translate(-2.24271e-13,0)" writing-mode="lr" x="1064.95" xml:space="preserve" y="629.2" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133853769731" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="64" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1065.53,641.5) scale(1,1) translate(-2.24271e-13,0)" writing-mode="lr" x="1064.95" xml:space="preserve" y="647.2" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133854162947" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="205">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="205" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,128.5,522.111) scale(1,1) translate(0,0)" writing-mode="lr" x="128.03" xml:space="preserve" y="526.78" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133852524547" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="204">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="204" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,128.5,545.111) scale(1,1) translate(0,0)" writing-mode="lr" x="128.03" xml:space="preserve" y="549.78" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133852590083" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="203">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,128.5,568.111) scale(1,1) translate(0,0)" writing-mode="lr" x="128.03" xml:space="preserve" y="572.78" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133852655619" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="202">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="202" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,128.5,496.611) scale(1,1) translate(0,1.07938e-13)" writing-mode="lr" x="128.03" xml:space="preserve" y="501.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133852786691" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="201">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="201" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,132,203.111) scale(1,1) translate(0,0)" writing-mode="lr" x="131.53" xml:space="preserve" y="209.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133852917763" ObjectName="F"/>
   </metadata>
  </g>
  <g id="200">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,246.333,522) scale(1,1) translate(0,0)" writing-mode="lr" x="245.86" xml:space="preserve" y="526.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133853048835" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="199">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="199" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,246.333,545) scale(1,1) translate(0,0)" writing-mode="lr" x="245.86" xml:space="preserve" y="549.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133853114371" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="198">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,246.333,568) scale(1,1) translate(0,0)" writing-mode="lr" x="245.86" xml:space="preserve" y="572.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133853179909" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="196">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="196" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,246.333,495.5) scale(1,1) translate(0,0)" writing-mode="lr" x="245.86" xml:space="preserve" y="500.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133853310979" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="195">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="195" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,132,228) scale(1,1) translate(0,0)" writing-mode="lr" x="131.53" xml:space="preserve" y="234.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133853442053" ObjectName="F"/>
   </metadata>
  </g>
  <g id="164">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,132,250.194) scale(1,1) translate(-1.76596e-14,0)" writing-mode="lr" x="131.45" xml:space="preserve" y="256.39" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133854031875" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="163">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,132,273.194) scale(1,1) translate(0,2.89429e-13)" writing-mode="lr" x="131.53" xml:space="preserve" y="279.39" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133854097411" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="156">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="156" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,128.75,596.111) scale(1,1) translate(0,0)" writing-mode="lr" x="128.28" xml:space="preserve" y="600.78" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133852983299" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="32" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,247.042,595) scale(1,1) translate(0,0)" writing-mode="lr" x="246.57" xml:space="preserve" y="599.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133853507589" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="4" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,132,177.5) scale(1,1) translate(0,0)" writing-mode="lr" x="131.64" xml:space="preserve" y="183.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133868580867" ObjectName=""/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="3" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,309,178.5) scale(1,1) translate(0,0)" writing-mode="lr" x="308.64" xml:space="preserve" y="184.68" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133868646403" ObjectName=""/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,850.5,514.611) scale(1,1) translate(0,0)" writing-mode="lr" x="850.03" xml:space="preserve" y="519.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133852786691" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,712.333,527.5) scale(1,1) translate(2.22267e-13,0)" writing-mode="lr" x="711.86" xml:space="preserve" y="532.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133853310979" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="125">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="125" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1036.26,60.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1035.79" xml:space="preserve" y="65.17" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133856980997" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="141" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,734.526,980.5) scale(1,1) translate(0,0)" writing-mode="lr" x="734.0599999999999" xml:space="preserve" y="985.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133867270149" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="142" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,734.526,998.5) scale(1,1) translate(0,0)" writing-mode="lr" x="734.0599999999999" xml:space="preserve" y="1003.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133867335685" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="145" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,896.526,980.5) scale(1,1) translate(0,0)" writing-mode="lr" x="896.0599999999999" xml:space="preserve" y="985.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133867663363" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="146">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="146" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,896.526,998.5) scale(1,1) translate(0,0)" writing-mode="lr" x="896.0599999999999" xml:space="preserve" y="1003.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133867728899" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="183">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="183" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1043.88,932.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1043.41" xml:space="preserve" y="937.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133861437443" ObjectName="P"/>
   </metadata>
  </g>
  <g id="193">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="193" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1205.88,932.5) scale(1,1) translate(3.8665e-13,0)" writing-mode="lr" x="1205.41" xml:space="preserve" y="937.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133862354949" ObjectName="P"/>
   </metadata>
  </g>
  <g id="210">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="210" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1367.88,931.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1367.41" xml:space="preserve" y="936.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133863796739" ObjectName="P"/>
   </metadata>
  </g>
  <g id="228">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="228" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1514.88,931.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1514.41" xml:space="preserve" y="936.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133865238531" ObjectName="P"/>
   </metadata>
  </g>
  <g id="235">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="235" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1043.88,949.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1043.41" xml:space="preserve" y="954.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133861502979" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="236">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="236" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1205.88,949.5) scale(1,1) translate(3.8665e-13,0)" writing-mode="lr" x="1205.41" xml:space="preserve" y="954.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133862420485" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="238">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="238" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1367.88,949.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1367.41" xml:space="preserve" y="954.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133863862275" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="240" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1514.88,948.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1514.41" xml:space="preserve" y="953.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133865304067" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="242">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="242" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1043.88,966.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1043.41" xml:space="preserve" y="971.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133861568517" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="243">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="243" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1205.88,966.5) scale(1,1) translate(3.8665e-13,0)" writing-mode="lr" x="1205.41" xml:space="preserve" y="971.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133862486021" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="245" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1367.88,966.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1367.41" xml:space="preserve" y="971.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133863927811" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="248">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="248" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1514.88,965.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1514.41" xml:space="preserve" y="970.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133865369603" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="249">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="249" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1050.18,984.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1049.71" xml:space="preserve" y="989.17" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133861961733" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="250">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="250" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1212.18,984.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1211.71" xml:space="preserve" y="989.17" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133862879235" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="251">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="251" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1373.18,985.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1372.71" xml:space="preserve" y="990.17" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133864321027" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="252">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="252" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1519.18,984.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1518.71" xml:space="preserve" y="989.17" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133865762821" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="273">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="273" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1336,14.75) scale(1,1) translate(0,-1.13243e-14)" writing-mode="lr" x="1335.53" xml:space="preserve" y="19.42" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133859340291" ObjectName="P"/>
   </metadata>
  </g>
  <g id="274">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="274" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1336,29.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1335.53" xml:space="preserve" y="34.42" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133859405827" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="275">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="275" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1335,50.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1334.53" xml:space="preserve" y="55.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133859471363" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="EnergyConsumerClass">
  <g id="18">
   <use class="kv35" height="30" transform="rotate(180,1206.82,265.25) scale(1.87021,1.71667) translate(-549.35,-99.9854)" width="28" x="1180.634146341463" xlink:href="#EnergyConsumer:站用变DY接地_0" y="239.5" zvalue="1519"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453990350851" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1206.82,265.25) scale(1.87021,1.71667) translate(-549.35,-99.9854)" width="28" x="1180.634146341463" y="239.5"/></g>
  <g id="306">
   <use class="kv10" height="30" transform="rotate(180,1051.88,868.5) scale(1.09275,1.56667) translate(-88.588,-305.638)" width="15" x="1043.681169291843" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="845" zvalue="1810"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453993562115" ObjectName="10kV崩强线"/>
   <cge:TPSR_Ref TObjectID="6192453993562115"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1051.88,868.5) scale(1.09275,1.56667) translate(-88.588,-305.638)" width="15" x="1043.681169291843" y="845"/></g>
  <g id="323">
   <use class="kv10" height="30" transform="rotate(180,1213.88,868.5) scale(1.09275,1.56667) translate(-102.339,-305.638)" width="15" x="1205.681169291843" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="845" zvalue="1831"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453993627651" ObjectName="10kV毛讲线"/>
   <cge:TPSR_Ref TObjectID="6192453993627651"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1213.88,868.5) scale(1.09275,1.56667) translate(-102.339,-305.638)" width="15" x="1205.681169291843" y="845"/></g>
  <g id="338">
   <use class="kv10" height="30" transform="rotate(180,1375.88,868.5) scale(1.09275,1.56667) translate(-116.089,-305.638)" width="15" x="1367.681169291843" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="845" zvalue="1847"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453993955331" ObjectName="10kV邦角线"/>
   <cge:TPSR_Ref TObjectID="6192453993955331"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1375.88,868.5) scale(1.09275,1.56667) translate(-116.089,-305.638)" width="15" x="1367.681169291843" y="845"/></g>
  <g id="355">
   <use class="kv10" height="30" transform="rotate(180,1522.88,867.5) scale(1.09275,1.56667) translate(-128.567,-305.277)" width="15" x="1514.681169291843" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="844" zvalue="1863"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453994283011" ObjectName="10kV西山街道线"/>
   <cge:TPSR_Ref TObjectID="6192453994283011"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1522.88,867.5) scale(1.09275,1.56667) translate(-128.567,-305.277)" width="15" x="1514.681169291843" y="844"/></g>
  <g id="367">
   <use class="kv10" height="30" transform="rotate(0,1429.25,909.431) scale(1.975,2.02876) translate(-695.829,-445.73)" width="20" x="1409.5" xlink:href="#EnergyConsumer:站用变11_0" y="879" zvalue="1869"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453994676227" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1429.25,909.431) scale(1.975,2.02876) translate(-695.829,-445.73)" width="20" x="1409.5" y="879"/></g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,338.625,320.5) scale(0.708333,0.665547) translate(135.059,156.042)" width="30" x="328" xlink:href="#State:红绿圆(方形)_0" y="310.52" zvalue="1703"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374925430785" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,338.625,320.5) scale(0.708333,0.665547) translate(135.059,156.042)" width="30" x="328" y="310.52"/></g>
  <g id="160">
   <use height="30" transform="rotate(0,243,320.5) scale(0.708333,0.665547) translate(95.6838,156.042)" width="30" x="232.38" xlink:href="#State:红绿圆(方形)_0" y="310.52" zvalue="1704"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562952487698439" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,243,320.5) scale(0.708333,0.665547) translate(95.6838,156.042)" width="30" x="232.38" y="310.52"/></g>
  <g id="830">
   <use height="30" transform="rotate(0,318.812,133.464) scale(1.22222,1.03092) translate(-47.9659,-3.53933)" width="90" x="263.81" xlink:href="#State:全站检修_0" y="118" zvalue="2022"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549680406529" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,318.812,133.464) scale(1.22222,1.03092) translate(-47.9659,-3.53933)" width="90" x="263.81" y="118"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="2">
   <use class="kv10" height="30" transform="rotate(0,735.498,905.983) scale(1.33222,1.33222) translate(-178.431,-220.946)" width="30" x="715.5143497232541" xlink:href="#Compensator:并联电容器_0" y="886" zvalue="1872"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453994741763" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453994741763"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,735.498,905.983) scale(1.33222,1.33222) translate(-178.431,-220.946)" width="30" x="715.5143497232541" y="886"/></g>
  <g id="13">
   <use class="kv10" height="30" transform="rotate(0,897.967,902) scale(1.33222,1.33222) translate(-218.947,-219.952)" width="30" x="877.9833333333333" xlink:href="#Compensator:并联电容器_0" y="882.0166666666667" zvalue="1877"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453994807299" ObjectName="10kV2号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453994807299"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,897.967,902) scale(1.33222,1.33222) translate(-218.947,-219.952)" width="30" x="877.9833333333333" y="882.0166666666667"/></g>
 </g>
</svg>