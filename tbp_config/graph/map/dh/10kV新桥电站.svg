<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549586821122" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="12.0194189818494"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="5.416666666666668" y2="12.02321291373541"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.92126160277786" x2="6.666666666666666" y1="12.10207526123773" y2="18.50000000000001"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.18646053143751" x2="24.5" y1="12.18904818695178" y2="19.00000000000001"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 7.33333 43.5833 L 23 43.5833 L 15 31.5 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV新桥电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="45" xlink:href="logo.png" y="42.67"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,179.625,72.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="179.63" xml:space="preserve" y="76.17" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,181.333,72.357) scale(1,1) translate(6.99441e-15,0)" writing-mode="lr" x="181.33" xml:space="preserve" y="81.36" zvalue="3">10kV新桥电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="2" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,61.4375,370) scale(1,1) translate(0,0)" width="72.88" x="25" y="358" zvalue="111"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,61.4375,370) scale(1,1) translate(0,0)" writing-mode="lr" x="61.44" xml:space="preserve" y="374.5" zvalue="111">信号一览</text>
  <line fill="none" id="35" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="379.0000000000001" x2="379.0000000000001" y1="10.66666666666674" y2="1040.666666666667" zvalue="4"/>
  <line fill="none" id="33" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.000000000000341" x2="371.9999999999999" y1="146.5371592807491" y2="146.5371592807491" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="158.6666666666668" y2="158.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="184.6666666666668" y2="184.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="6.000000000000114" y1="158.6666666666668" y2="184.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="158.6666666666668" y2="184.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="158.6666666666668" y2="158.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="184.6666666666668" y2="184.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="158.6666666666668" y2="184.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0000000000001" x2="368.0000000000001" y1="158.6666666666668" y2="184.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="184.6666666666668" y2="184.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="208.9166666666668" y2="208.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="6.000000000000114" y1="184.6666666666668" y2="208.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="184.6666666666668" y2="208.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="184.6666666666668" y2="184.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="208.9166666666668" y2="208.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="184.6666666666668" y2="208.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0000000000001" x2="368.0000000000001" y1="184.6666666666668" y2="208.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="208.9166666666668" y2="208.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="231.6666666666668" y2="231.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="6.000000000000114" y1="208.9166666666668" y2="231.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="208.9166666666668" y2="231.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="208.9166666666668" y2="208.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="231.6666666666668" y2="231.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="208.9166666666668" y2="231.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0000000000001" x2="368.0000000000001" y1="208.9166666666668" y2="231.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="231.6666666666668" y2="231.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="254.4166666666668" y2="254.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="6.000000000000114" y1="231.6666666666668" y2="254.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="231.6666666666668" y2="254.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="231.6666666666668" y2="231.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="254.4166666666668" y2="254.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="231.6666666666668" y2="254.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0000000000001" x2="368.0000000000001" y1="231.6666666666668" y2="254.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="254.4166666666668" y2="254.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="277.1666666666668" y2="277.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="6.000000000000114" y1="254.4166666666668" y2="277.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="254.4166666666668" y2="277.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="254.4166666666668" y2="254.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="277.1666666666668" y2="277.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="254.4166666666668" y2="277.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0000000000001" x2="368.0000000000001" y1="254.4166666666668" y2="277.1666666666668"/>
  <line fill="none" id="31" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.000000000000341" x2="371.9999999999999" y1="616.5371592807492" y2="616.5371592807492" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000114" x2="95.00000000000011" y1="931.666666666667" y2="931.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000114" x2="95.00000000000011" y1="970.829966666667" y2="970.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000114" x2="5.000000000000114" y1="931.666666666667" y2="970.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="95.00000000000011" y1="931.666666666667" y2="970.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="365.0000000000001" y1="931.666666666667" y2="931.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="365.0000000000001" y1="970.829966666667" y2="970.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="95.00000000000011" y1="931.666666666667" y2="970.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="365.0000000000001" x2="365.0000000000001" y1="931.666666666667" y2="970.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000114" x2="95.00000000000011" y1="970.829936666667" y2="970.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000114" x2="95.00000000000011" y1="998.748336666667" y2="998.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000114" x2="5.000000000000114" y1="970.829936666667" y2="998.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="95.00000000000011" y1="970.829936666667" y2="998.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="185.0000000000001" y1="970.829936666667" y2="970.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="185.0000000000001" y1="998.748336666667" y2="998.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="95.00000000000011" y1="970.829936666667" y2="998.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="970.829936666667" y2="998.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="275.0000000000002" y1="970.829936666667" y2="970.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="275.0000000000002" y1="998.748336666667" y2="998.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="185.0000000000002" y1="970.829936666667" y2="998.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000002" x2="275.0000000000002" y1="970.829936666667" y2="998.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000001" x2="365.0000000000001" y1="970.829936666667" y2="970.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000001" x2="365.0000000000001" y1="998.748336666667" y2="998.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000001" x2="275.0000000000001" y1="970.829936666667" y2="998.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="365.0000000000001" x2="365.0000000000001" y1="970.829936666667" y2="998.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000114" x2="95.00000000000011" y1="998.748266666667" y2="998.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000114" x2="95.00000000000011" y1="1026.666666666667" y2="1026.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000114" x2="5.000000000000114" y1="998.748266666667" y2="1026.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="95.00000000000011" y1="998.748266666667" y2="1026.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="185.0000000000001" y1="998.748266666667" y2="998.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="185.0000000000001" y1="1026.666666666667" y2="1026.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="95.00000000000011" y1="998.748266666667" y2="1026.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="998.748266666667" y2="1026.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="275.0000000000002" y1="998.748266666667" y2="998.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="275.0000000000002" y1="1026.666666666667" y2="1026.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="185.0000000000002" y1="998.748266666667" y2="1026.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000002" x2="275.0000000000002" y1="998.748266666667" y2="1026.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000001" x2="365.0000000000001" y1="998.748266666667" y2="998.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000001" x2="365.0000000000001" y1="1026.666666666667" y2="1026.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000001" x2="275.0000000000001" y1="998.748266666667" y2="1026.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="365.0000000000001" x2="365.0000000000001" y1="998.748266666667" y2="1026.666666666667"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50,951.667) scale(1,1) translate(0,0)" writing-mode="lr" x="50" xml:space="preserve" y="957.67" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47,985.667) scale(1,1) translate(0,0)" writing-mode="lr" x="47" xml:space="preserve" y="991.67" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229,985.667) scale(1,1) translate(0,0)" writing-mode="lr" x="229" xml:space="preserve" y="991.67" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46,1013.67) scale(1,1) translate(0,0)" writing-mode="lr" x="46" xml:space="preserve" y="1019.67" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228,1013.67) scale(1,1) translate(0,0)" writing-mode="lr" x="228" xml:space="preserve" y="1019.67" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.5,646.167) scale(1,1) translate(0,0)" writing-mode="lr" x="70.50000000000011" xml:space="preserve" y="650.6666666666667" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230.054,953.667) scale(1,1) translate(0,0)" writing-mode="lr" x="230.05" xml:space="preserve" y="959.67" zvalue="26">XingQiao-01-2018</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,134.054,1011.67) scale(1,1) translate(0,0)" writing-mode="lr" x="134.05" xml:space="preserve" y="1017.67" zvalue="27">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44,172.667) scale(1,1) translate(0,0)" writing-mode="lr" x="44" xml:space="preserve" y="178.17" zvalue="29">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224,172.667) scale(1,1) translate(0,0)" writing-mode="lr" x="224" xml:space="preserve" y="178.17" zvalue="30">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.1875,244.667) scale(1,1) translate(0,0)" writing-mode="lr" x="51.19" xml:space="preserve" y="249.17" zvalue="31">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.5,197.5) scale(1,1) translate(0,0)" writing-mode="lr" x="54.5" xml:space="preserve" y="202" zvalue="36">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,476.286,692.714) scale(1,1) translate(4.45929e-13,0)" writing-mode="lr" x="476.29" xml:space="preserve" y="697.21" zvalue="40">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" x="829.5703125" xml:space="preserve" y="1024.774349013418" zvalue="43">#1发电机        </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="829.5703125" xml:space="preserve" y="1040.774349013418" zvalue="43">630KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,806.292,773.314) scale(1,1) translate(0,0)" writing-mode="lr" x="806.29" xml:space="preserve" y="777.8099999999999" zvalue="45">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,847.194,851.736) scale(1,1) translate(0,0)" writing-mode="lr" x="847.1900000000001" xml:space="preserve" y="856.24" zvalue="49">651</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" x="1497.890625" xml:space="preserve" y="1021.182688544096" zvalue="53">#2发电机     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1497.890625" xml:space="preserve" y="1037.182688544096" zvalue="53">630KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1474.48,781.754) scale(1,1) translate(0,-1.37446e-12)" writing-mode="lr" x="1474.48" xml:space="preserve" y="786.25" zvalue="54">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1515.38,860.176) scale(1,1) translate(0,0)" writing-mode="lr" x="1515.38" xml:space="preserve" y="864.6799999999999" zvalue="58">652</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,669,780.507) scale(1,1) translate(0,0)" writing-mode="lr" x="669" xml:space="preserve" y="785.01" zvalue="62">6811</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,827.438,638.507) scale(1,1) translate(0,0)" writing-mode="lr" x="827.4400000000001" xml:space="preserve" y="643.01" zvalue="67">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,839.566,574.242) scale(1,1) translate(-3.66876e-13,-1.25341e-13)" writing-mode="lr" x="839.5700000000001" xml:space="preserve" y="578.74" zvalue="70">601</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" x="757.5" xml:space="preserve" y="495.125" zvalue="72">#1主变      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="757.5" xml:space="preserve" y="511.125" zvalue="72">1600KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,841.089,413.507) scale(1,1) translate(3.66857e-13,0)" writing-mode="lr" x="841.09" xml:space="preserve" y="418.01" zvalue="75">0016</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,840.718,352.742) scale(1,1) translate(-3.67011e-13,-7.62686e-14)" writing-mode="lr" x="840.72" xml:space="preserve" y="357.24" zvalue="78">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,841.438,287.507) scale(1,1) translate(0,0)" writing-mode="lr" x="841.4400000000001" xml:space="preserve" y="292.01" zvalue="81">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,840.438,219.507) scale(1,1) translate(0,0)" writing-mode="lr" x="840.4400000000001" xml:space="preserve" y="224.01" zvalue="84">0511</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,837.284,153.562) scale(1,1) translate(5.50749e-13,0)" writing-mode="lr" x="837.28" xml:space="preserve" y="158.06" zvalue="87">051</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,809,51) scale(1,1) translate(0,0)" writing-mode="lr" x="809" xml:space="preserve" y="55.5" zvalue="89">10kV街桥线新桥电站支线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,643.5,885.167) scale(1,1) translate(0,0)" writing-mode="lr" x="643.5" xml:space="preserve" y="889.67" zvalue="92">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,971.5,439.167) scale(1,1) translate(0,0)" writing-mode="lr" x="971.5" xml:space="preserve" y="443.67" zvalue="94">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,940.5,344) scale(1,1) translate(0,0)" writing-mode="lr" x="940.5" xml:space="preserve" y="348.5" zvalue="95">0811</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1624.72,636.253) scale(1,1) translate(0,1.39112e-13)" writing-mode="lr" x="1624.72" xml:space="preserve" y="640.75" zvalue="101">6901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,177.211,370.591) scale(1,1) translate(0,0)" writing-mode="lr" x="177.21" xml:space="preserve" y="375.09" zvalue="107">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,282.211,370.591) scale(1,1) translate(0,0)" writing-mode="lr" x="282.21" xml:space="preserve" y="375.09" zvalue="108">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="10kV新桥电站.svg"><rect fill-opacity="0" height="24" width="72.88" x="25" y="358" zvalue="111"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="38">
   <path class="v6300" d="M 517.14 693.71 L 1772.86 693.71" stroke-width="6" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674243706884" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674243706884"/></metadata>
  <path d="M 517.14 693.71 L 1772.86 693.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v6300" height="30" transform="rotate(0,824.776,969.957) scale(1.85899,1.85899) translate(-368.222,-435.307)" width="30" x="796.8913824689196" xlink:href="#Generator:发电机_0" y="942.0724504570603" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449799323654" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449799323654"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,824.776,969.957) scale(1.85899,1.85899) translate(-368.222,-435.307)" width="30" x="796.8913824689196" y="942.0724504570603"/></g>
  <g id="53">
   <use class="v6300" height="30" transform="rotate(0,1492.96,978.397) scale(1.85899,1.85899) translate(-676.973,-439.206)" width="30" x="1465.076923076923" xlink:href="#Generator:发电机_0" y="950.5120108966207" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449799454725" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449799454725"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1492.96,978.397) scale(1.85899,1.85899) translate(-676.973,-439.206)" width="30" x="1465.076923076923" y="950.5120108966207"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="165">
   <use class="v6300" height="30" transform="rotate(0,825.511,774.314) scale(1.9625,1.2338) translate(-397.65,-143.221)" width="15" x="810.7920927183517" xlink:href="#Disconnector:刀闸_0" y="755.8075396825394" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449799258118" ObjectName="#1发电机6511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449799258118"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,825.511,774.314) scale(1.9625,1.2338) translate(-397.65,-143.221)" width="15" x="810.7920927183517" y="755.8075396825394"/></g>
  <g id="52">
   <use class="v6300" height="30" transform="rotate(0,1493.7,782.754) scale(1.9625,1.2338) translate(-725.358,-144.82)" width="15" x="1478.977633326355" xlink:href="#Disconnector:刀闸_0" y="764.2471001220998" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449799389189" ObjectName="#2发电机6521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449799389189"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1493.7,782.754) scale(1.9625,1.2338) translate(-725.358,-144.82)" width="15" x="1478.977633326355" y="764.2471001220998"/></g>
  <g id="55">
   <use class="v6300" height="30" transform="rotate(0,644.719,779.507) scale(1.9625,1.2338) translate(-308.981,-144.205)" width="15" x="630" xlink:href="#Disconnector:刀闸_0" y="761" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449799520261" ObjectName="#1厂用变6811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449799520261"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,644.719,779.507) scale(1.9625,1.2338) translate(-308.981,-144.205)" width="15" x="630" y="761"/></g>
  <g id="61">
   <use class="v6300" height="30" transform="rotate(0,808.719,639.507) scale(1.9625,-1.2338) translate(-389.414,-1154.32)" width="15" x="794" xlink:href="#Disconnector:刀闸_0" y="621" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449799585797" ObjectName="#1主变6.3kV6011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449799585797"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,808.719,639.507) scale(1.9625,-1.2338) translate(-389.414,-1154.32)" width="15" x="794" y="621"/></g>
  <g id="70">
   <use class="kv10" height="30" transform="rotate(0,809.371,414.507) scale(1.9625,-1.2338) translate(-389.734,-746.961)" width="15" x="794.6517858915952" xlink:href="#Disconnector:刀闸_0" y="396.0000000000001" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449799651333" ObjectName="#1主变10kV0016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449799651333"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,809.371,414.507) scale(1.9625,-1.2338) translate(-389.734,-746.961)" width="15" x="794.6517858915952" y="396.0000000000001"/></g>
  <g id="76">
   <use class="kv10" height="30" transform="rotate(0,810.719,288.507) scale(1.9625,-1.2338) translate(-390.395,-518.837)" width="15" x="796" xlink:href="#Disconnector:刀闸_0" y="270.0000000000001" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449799716869" ObjectName="#1主变10kV0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449799716869"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,810.719,288.507) scale(1.9625,-1.2338) translate(-390.395,-518.837)" width="15" x="796" y="270.0000000000001"/></g>
  <g id="79">
   <use class="kv10" height="30" transform="rotate(0,809.719,220.507) scale(1.9625,-1.2338) translate(-389.904,-395.722)" width="15" x="795" xlink:href="#Disconnector:刀闸_0" y="202.0000000000001" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449799782406" ObjectName="10kV街桥线新桥电站支线0511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449799782406"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,809.719,220.507) scale(1.9625,-1.2338) translate(-389.904,-395.722)" width="15" x="795" y="202.0000000000001"/></g>
  <g id="90">
   <use class="kv10" height="30" transform="rotate(0,966.25,349.5) scale(1.3,1.3) translate(-220.731,-76.1538)" width="15" x="956.5" xlink:href="#Disconnector:令克_0" y="330" zvalue="94"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449800044549" ObjectName="#2厂用变0811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449800044549"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,966.25,349.5) scale(1.3,1.3) translate(-220.731,-76.1538)" width="15" x="956.5" y="330"/></g>
  <g id="96">
   <use class="v6300" height="30" transform="rotate(0,1586.72,634.507) scale(1.9625,-1.2338) translate(-770.981,-1145.27)" width="15" x="1572" xlink:href="#Disconnector:刀闸_0" y="616" zvalue="100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449800175621" ObjectName="6.3kV母线电压互感器6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449800175621"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1586.72,634.507) scale(1.9625,-1.2338) translate(-770.981,-1145.27)" width="15" x="1572" y="616"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="45">
   <path class="v6300" d="M 825.68 756.42 L 825.68 693.71" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="38@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.68 756.42 L 825.68 693.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="v6300" d="M 824.78 942.54 L 824.78 871.54" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="170@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 824.78 942.54 L 824.78 871.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="v6300" d="M 825.63 840.03 L 825.63 792.5" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="165@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.63 840.03 L 825.63 792.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="v6300" d="M 1493.87 764.86 L 1493.87 693.71" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="38@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1493.87 764.86 L 1493.87 693.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="v6300" d="M 1492.96 950.98 L 1492.96 879.98" stroke-width="1" zvalue="57"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="50@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1492.96 950.98 L 1492.96 879.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="v6300" d="M 1493.82 848.47 L 1493.82 800.94" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="52@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1493.82 848.47 L 1493.82 800.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="v6300" d="M 644.89 761.61 L 644.89 693.71" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="38@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 644.89 761.61 L 644.89 693.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="v6300" d="M 643.68 831.88 L 643.68 797.7" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="55@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 643.68 831.88 L 643.68 797.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="v6300" d="M 808.89 657.4 L 808.89 693.71" stroke-width="1" zvalue="67"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="38@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 808.89 657.4 L 808.89 693.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="v6300" d="M 810.19 590.23 L 808.84 621.32" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@1" LinkObjectIDznd="61@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.19 590.23 L 808.84 621.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="v6300" d="M 809.5 530.54 L 810.01 558.71" stroke-width="1" zvalue="72"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@1" LinkObjectIDznd="63@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.5 530.54 L 810.01 558.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv10" d="M 809.54 432.4 L 809.54 466.59" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="65@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.54 432.4 L 809.54 466.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 809.49 367.23 L 809.49 396.32" stroke-width="1" zvalue="78"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@1" LinkObjectIDznd="70@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.49 367.23 L 809.49 396.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv10" d="M 810.89 306.4 L 810.89 335.71" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@0" LinkObjectIDznd="73@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.89 306.4 L 810.89 335.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv10" d="M 809.89 238.4 L 810.84 270.32" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="76@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.89 238.4 L 810.84 270.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv10" d="M 809.84 166.23 L 809.84 202.32" stroke-width="1" zvalue="87"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@1" LinkObjectIDznd="79@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.84 166.23 L 809.84 202.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv10" d="M 809 79.43 L 809.66 134.71" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@0" LinkObjectIDznd="82@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 809 79.43 L 809.66 134.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv10" d="M 966.14 365.43 L 967.68 387.88" stroke-width="1" zvalue="95"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@1" LinkObjectIDznd="88@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.14 365.43 L 967.68 387.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv10" d="M 966.36 332.28 L 966.36 317 L 810.89 317" stroke-width="1" zvalue="96"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="77" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.36 332.28 L 966.36 317 L 810.89 317" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv10" d="M 626.44 329.41 L 626.44 315 L 810.89 315" stroke-width="1" zvalue="98"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="77" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.44 329.41 L 626.44 315 L 810.89 315" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="v6300" d="M 1586.89 652.4 L 1586.89 693.71" stroke-width="1" zvalue="101"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="38@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1586.89 652.4 L 1586.89 693.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="v6300" d="M 1586.84 549.03 L 1586.84 616.32" stroke-width="1" zvalue="103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="96@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1586.84 549.03 L 1586.84 616.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="170">
   <use class="v6300" height="20" transform="rotate(0,825.691,855.801) scale(1.81318,1.64835) translate(-366.242,-330.13)" width="10" x="816.6254171535235" xlink:href="#Breaker:开关_0" y="839.3171550671549" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924511531013" ObjectName="#1发电机651断路器"/>
   <cge:TPSR_Ref TObjectID="6473924511531013"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,825.691,855.801) scale(1.81318,1.64835) translate(-366.242,-330.13)" width="10" x="816.6254171535235" y="839.3171550671549"/></g>
  <g id="50">
   <use class="v6300" height="20" transform="rotate(0,1493.88,864.24) scale(1.81318,1.64835) translate(-665.912,-333.449)" width="10" x="1484.810957761527" xlink:href="#Breaker:开关_0" y="847.7567155067153" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924511596549" ObjectName="#2发电机652断路器"/>
   <cge:TPSR_Ref TObjectID="6473924511596549"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1493.88,864.24) scale(1.81318,1.64835) translate(-665.912,-333.449)" width="10" x="1484.810957761527" y="847.7567155067153"/></g>
  <g id="63">
   <use class="v6300" height="20" transform="rotate(0,810.066,574.483) scale(1.81318,1.64835) translate(-359.235,-219.479)" width="10" x="801" xlink:href="#Breaker:开关_0" y="558" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924511662085" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924511662085"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,810.066,574.483) scale(1.81318,1.64835) translate(-359.235,-219.479)" width="10" x="801" y="558"/></g>
  <g id="73">
   <use class="kv10" height="20" transform="rotate(0,809.37,351.483) scale(1.81318,1.64835) translate(-358.922,-131.766)" width="10" x="800.3037923251467" xlink:href="#Breaker:开关_0" y="335" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924511727621" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924511727621"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,809.37,351.483) scale(1.81318,1.64835) translate(-358.922,-131.766)" width="10" x="800.3037923251467" y="335"/></g>
  <g id="82">
   <use class="kv10" height="20" transform="rotate(0,809.718,150.483) scale(1.81318,1.64835) translate(-359.079,-52.7064)" width="10" x="800.6520064335516" xlink:href="#Breaker:开关_0" y="134" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924511793157" ObjectName="10kV街桥线新桥电站支线051断路器"/>
   <cge:TPSR_Ref TObjectID="6473924511793157"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,809.718,150.483) scale(1.81318,1.64835) translate(-359.079,-52.7064)" width="10" x="800.6520064335516" y="134"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="65">
   <g id="650">
    <use class="kv10" height="50" transform="rotate(0,809.5,498.5) scale(1.3,1.3) translate(-182.308,-107.538)" width="30" x="790" xlink:href="#PowerTransformer2:Y-D_0" y="466" zvalue="71"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874437427204" ObjectName="10"/>
    </metadata>
   </g>
   <g id="651">
    <use class="v6300" height="50" transform="rotate(0,809.5,498.5) scale(1.3,1.3) translate(-182.308,-107.538)" width="30" x="790" xlink:href="#PowerTransformer2:Y-D_1" y="466" zvalue="71"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874437492740" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399450951684" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399450951684"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,809.5,498.5) scale(1.3,1.3) translate(-182.308,-107.538)" width="30" x="790" y="466"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="58">
   <use class="v6300" height="30" transform="rotate(0,643.545,853.167) scale(1.46748,1.47778) translate(-198.463,-268.669)" width="28" x="623" xlink:href="#EnergyConsumer:站用变DY接地_0" y="831" zvalue="91"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449799913478" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,643.545,853.167) scale(1.46748,1.47778) translate(-198.463,-268.669)" width="28" x="623" y="831"/></g>
  <g id="88">
   <use class="kv10" height="30" transform="rotate(0,967.545,409.167) scale(1.46748,1.47778) translate(-301.676,-125.12)" width="28" x="947" xlink:href="#EnergyConsumer:站用变DY接地_0" y="387" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449799979014" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,967.545,409.167) scale(1.46748,1.47778) translate(-301.676,-125.12)" width="28" x="947" y="387"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="94">
   <use class="kv10" height="30" transform="rotate(0,626.5,346.5) scale(1.16667,1.16667) translate(-87,-47)" width="30" x="609" xlink:href="#Accessory:PT789_0" y="329" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449800110085" ObjectName="#1主变10kV侧接地PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,626.5,346.5) scale(1.16667,1.16667) translate(-87,-47)" width="30" x="609" y="329"/></g>
  <g id="99">
   <use class="v6300" height="18" transform="rotate(0,1589.41,525.9) scale(2.76667,-2.76667) translate(-1001.68,-700.084)" width="15" x="1568.661555062647" xlink:href="#Accessory:PT8_0" y="501" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449800241157" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1589.41,525.9) scale(2.76667,-2.76667) translate(-1001.68,-700.084)" width="15" x="1568.661555062647" y="501"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,309.485,371.107) scale(0.708333,0.665547) translate(123.06,181.474)" width="30" x="298.86" xlink:href="#State:红绿圆(方形)_0" y="361.12" zvalue="109"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,309.485,371.107) scale(0.708333,0.665547) translate(123.06,181.474)" width="30" x="298.86" y="361.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,213.86,371.107) scale(0.708333,0.665547) translate(83.685,181.474)" width="30" x="203.24" xlink:href="#State:红绿圆(方形)_0" y="361.12" zvalue="110"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,213.86,371.107) scale(0.708333,0.665547) translate(83.685,181.474)" width="30" x="203.24" y="361.12"/></g>
 </g>
</svg>