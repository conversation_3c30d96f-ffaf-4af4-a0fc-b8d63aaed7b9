<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549683421185" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:电缆1_0" viewBox="0,0,12,7">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.25"/>
   <path d="M 1.16667 0.25 L 10.8333 0.25 L 6 6.88889 L 1.16667 0.25 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:母线PT2020_0" viewBox="0,0,40,51">
   <use terminal-index="0" type="0" x="13.62500008511543" xlink:href="#terminal" y="0.2550003370571083"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.0750001191616" x2="8.015000160017012" y1="43.85999975486756" y2="45.89999972763061"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.0750001191616" x2="8.015000160017012" y1="48.95999968677521" y2="46.91999971401215"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62500008511543" x2="13.62500008511543" y1="36.37999985473633" y2="3.40461728143282e-07"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62500008511543" x2="9.545000139589307" y1="11.22000019065857" y2="14.28000014980316"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.0750001191616" x2="11.0750001191616" y1="43.85999975486756" y2="48.95999968677521"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.64499984452248" x2="13.62500008511543" y1="5.355000268964766" y2="5.355000268964766"/>
   <rect fill-opacity="0" height="14.28" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.63,24.99) scale(1,1) translate(0,0)" width="6.12" x="10.57" y="17.85"/>
   <ellipse cx="17.17" cy="38.96" fill-opacity="0" rx="4.25" ry="4.25" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.14304966605751" x2="17.14304966605751" y1="41.27177046341052" y2="38.74718892903903"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.14304966605751" x2="14.69504969874184" y1="38.74718892903901" y2="37.48489816185327"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.14304966605751" x2="19.59104963337318" y1="38.74718892903901" y2="37.48489816185327"/>
   <ellipse cx="17.17" cy="46.1" fill-opacity="0" rx="4.25" ry="4.25" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.03" cy="38.96" fill-opacity="0" rx="4.25" ry="4.25" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0030497613868" x2="12.45104972870246" y1="38.74718892903901" y2="37.48489816185327"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00304976138679" x2="10.00304976138679" y1="41.27177046341052" y2="38.74718892903903"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.14304966605751" x2="17.14304966605751" y1="48.41177036808124" y2="45.88718883370975"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.14304966605751" x2="19.59104963337318" y1="45.88718883370973" y2="44.62489806652398"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00304976138679" x2="7.555049794071122" y1="38.74718892903901" y2="37.48489816185327"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.14304966605751" x2="14.69504969874184" y1="45.88718883370973" y2="44.62489806652398"/>
   <ellipse cx="10.03" cy="46.1" fill-opacity="0" rx="4.25" ry="4.25" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.5259998461113" x2="31.5259998461113" y1="5.355000268964766" y2="16.67700011779976"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.5259998461113" x2="31.5259998461113" y1="16.67700011779976" y2="26.77499998297691"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.08933321197764" x2="34.30266647570546" y1="38.13278930501829" y2="38.13278930501829"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="30.0668331989266" x2="33.65099981773949" y1="39.54199981251907" y2="39.54199981251907"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.78599989604568" x2="35.60599979163742" y1="36.72357879751749" y2="36.72357879751749"/>
   <rect fill-opacity="0" height="16.47" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,31.26,24.91) scale(1,1) translate(0,0)" width="7.73" x="27.39" y="16.68"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.5259998461113" x2="28.46599988696671" y1="26.87699998161507" y2="23.81700002247047"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.5259998461113" x2="31.5259998461113" y1="32.99699989990425" y2="36.66899985087777"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.5259998461113" x2="34.5859998052559" y1="26.87699998161507" y2="23.81700002247047"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62500008511543" x2="17.70500003064156" y1="11.22000019065857" y2="14.28000014980316"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62500008511543" x2="9.545000139589307" y1="9.180000217895504" y2="12.2400001770401"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62500008511543" x2="17.70500003064156" y1="9.180000217895504" y2="12.2400001770401"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_0" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="3" y1="19" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="4.75" y2="8.75"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="18.75" y2="21.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_1" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="2" y2="24"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_2" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.08333333333333" x2="3.995614035087721" y1="9.083333333333332" y2="17.19627192982456"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.717927631578948" x2="10.16666666666667" y1="9.172423245614038" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="2" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="21.25" y2="24.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV芒市生活垃圾发电厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="285" x="40" xlink:href="logo.png" y="45"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,182.5,75) scale(1,1) translate(0,0)" writing-mode="lr" x="182.5" xml:space="preserve" y="78.5" zvalue="54"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,184.5,75.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="184.5" xml:space="preserve" y="84.69" zvalue="55">10kV芒市生活垃圾发电厂</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="28" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,72.4375,396) scale(1,1) translate(0,0)" width="72.88" x="36" y="384" zvalue="1885"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,72.4375,396) scale(1,1) translate(0,0)" writing-mode="lr" x="72.44" xml:space="preserve" y="400.5" zvalue="1885">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,492.133,470.556) scale(1,1) translate(0,0)" writing-mode="lr" x="492.13" xml:space="preserve" y="475.06" zvalue="16">10kVⅡ段母线</text>
  <line fill="none" id="67" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="386" x2="386" y1="13" y2="1043" zvalue="58"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="309" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,670.03,542.111) scale(1,1) translate(0,0)" writing-mode="lr" x="670.03" xml:space="preserve" y="546.61" zvalue="613">071</text>
  <line fill="none" id="666" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="148.8704926140824" y2="148.8704926140824" zvalue="1056"/>
  <line fill="none" id="613" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="618.8704926140824" y2="618.8704926140824" zvalue="1058"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="65.22236173734677" x2="113.5670617373469" y1="441.6666435058594" y2="441.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="65.22236173734677" x2="113.5670617373469" y1="479.1566435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="65.22236173734677" x2="65.22236173734677" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5670617373469" x2="113.5670617373469" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5673617373468" x2="175.6756617373469" y1="441.6666435058594" y2="441.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5673617373468" x2="175.6756617373469" y1="479.1566435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5673617373468" x2="113.5673617373468" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6756617373469" x2="175.6756617373469" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6751617373468" x2="238.9999617373469" y1="441.6666435058594" y2="441.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6751617373468" x2="238.9999617373469" y1="479.1566435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6751617373468" x2="175.6751617373468" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9999617373469" x2="238.9999617373469" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9998617373468" x2="301.1081617373468" y1="441.6666435058594" y2="441.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9998617373468" x2="301.1081617373468" y1="479.1566435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9998617373468" x2="238.9998617373468" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="301.1081617373468" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="363.2164617373469" y1="441.6666435058594" y2="441.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="363.2164617373469" y1="479.1566435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="301.1081617373468" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.2164617373469" x2="363.2164617373469" y1="441.6666435058594" y2="479.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="65.22236173734677" x2="113.5670617373469" y1="479.1567435058594" y2="479.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="65.22236173734677" x2="113.5670617373469" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="65.22236173734677" x2="65.22236173734677" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5670617373469" x2="113.5670617373469" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5673617373468" x2="175.6756617373469" y1="479.1567435058594" y2="479.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5673617373468" x2="175.6756617373469" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5673617373468" x2="113.5673617373468" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6756617373469" x2="175.6756617373469" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6751617373468" x2="238.9999617373469" y1="479.1567435058594" y2="479.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6751617373468" x2="238.9999617373469" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6751617373468" x2="175.6751617373468" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9999617373469" x2="238.9999617373469" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9998617373468" x2="301.1081617373468" y1="479.1567435058594" y2="479.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9998617373468" x2="301.1081617373468" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9998617373468" x2="238.9998617373468" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="301.1081617373468" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="363.2164617373469" y1="479.1567435058594" y2="479.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="363.2164617373469" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="301.1081617373468" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.2164617373469" x2="363.2164617373469" y1="479.1567435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="65.22236173734677" x2="113.5670617373469" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="65.22236173734677" x2="113.5670617373469" y1="527.4939435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="65.22236173734677" x2="65.22236173734677" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5670617373469" x2="113.5670617373469" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5673617373468" x2="175.6756617373469" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5673617373468" x2="175.6756617373469" y1="527.4939435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5673617373468" x2="113.5673617373468" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6756617373469" x2="175.6756617373469" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6751617373468" x2="238.9999617373469" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6751617373468" x2="238.9999617373469" y1="527.4939435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6751617373468" x2="175.6751617373468" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9999617373469" x2="238.9999617373469" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9998617373468" x2="301.1081617373468" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9998617373468" x2="301.1081617373468" y1="527.4939435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9998617373468" x2="238.9998617373468" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="301.1081617373468" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="363.2164617373469" y1="503.3253435058594" y2="503.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="363.2164617373469" y1="527.4939435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="301.1081617373468" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.2164617373469" x2="363.2164617373469" y1="503.3253435058594" y2="527.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="65.22236173734677" x2="113.5670617373469" y1="527.4939835058594" y2="527.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="65.22236173734677" x2="113.5670617373469" y1="551.6625835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="65.22236173734677" x2="65.22236173734677" y1="527.4939835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5670617373469" x2="113.5670617373469" y1="527.4939835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5673617373468" x2="175.6756617373469" y1="527.4939835058594" y2="527.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5673617373468" x2="175.6756617373469" y1="551.6625835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5673617373468" x2="113.5673617373468" y1="527.4939835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6756617373469" x2="175.6756617373469" y1="527.4939835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6751617373468" x2="238.9999617373469" y1="527.4939835058594" y2="527.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6751617373468" x2="238.9999617373469" y1="551.6625835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6751617373468" x2="175.6751617373468" y1="527.4939835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9999617373469" x2="238.9999617373469" y1="527.4939835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9998617373468" x2="301.1081617373468" y1="527.4939835058594" y2="527.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9998617373468" x2="301.1081617373468" y1="551.6625835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9998617373468" x2="238.9998617373468" y1="527.4939835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="301.1081617373468" y1="527.4939835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="363.2164617373469" y1="527.4939835058594" y2="527.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="363.2164617373469" y1="551.6625835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="301.1081617373468" y1="527.4939835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.2164617373469" x2="363.2164617373469" y1="527.4939835058594" y2="551.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="65.22236173734677" x2="113.5670617373469" y1="551.6627435058595" y2="551.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="65.22236173734677" x2="113.5670617373469" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="65.22236173734677" x2="65.22236173734677" y1="551.6627435058595" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5670617373469" x2="113.5670617373469" y1="551.6627435058595" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5673617373468" x2="175.6756617373469" y1="551.6627435058595" y2="551.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5673617373468" x2="175.6756617373469" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5673617373468" x2="113.5673617373468" y1="551.6627435058595" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6756617373469" x2="175.6756617373469" y1="551.6627435058595" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6751617373468" x2="238.9999617373469" y1="551.6627435058595" y2="551.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6751617373468" x2="238.9999617373469" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6751617373468" x2="175.6751617373468" y1="551.6627435058595" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9999617373469" x2="238.9999617373469" y1="551.6627435058595" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9998617373468" x2="301.1081617373468" y1="551.6627435058595" y2="551.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9998617373468" x2="301.1081617373468" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9998617373468" x2="238.9998617373468" y1="551.6627435058595" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="301.1081617373468" y1="551.6627435058595" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="363.2164617373469" y1="551.6627435058595" y2="551.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="363.2164617373469" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="301.1081617373468" y1="551.6627435058595" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.2164617373469" x2="363.2164617373469" y1="551.6627435058595" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="65.22236173734677" x2="113.5670617373469" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="65.22236173734677" x2="113.5670617373469" y1="599.9999435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="65.22236173734677" x2="65.22236173734677" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5670617373469" x2="113.5670617373469" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5673617373468" x2="175.6756617373469" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5673617373468" x2="175.6756617373469" y1="599.9999435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.5673617373468" x2="113.5673617373468" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6756617373469" x2="175.6756617373469" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6751617373468" x2="238.9999617373469" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6751617373468" x2="238.9999617373469" y1="599.9999435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="175.6751617373468" x2="175.6751617373468" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9999617373469" x2="238.9999617373469" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9998617373468" x2="301.1081617373468" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9998617373468" x2="301.1081617373468" y1="599.9999435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="238.9998617373468" x2="238.9998617373468" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="301.1081617373468" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="363.2164617373469" y1="575.8313435058594" y2="575.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="363.2164617373469" y1="599.9999435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="301.1081617373468" x2="301.1081617373468" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.2164617373469" x2="363.2164617373469" y1="575.8313435058594" y2="599.9999435058594"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="609" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="1062">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="608" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="1063">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="607" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="1064">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="606" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="1065">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="605" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="1066">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="604" stroke="rgb(255,255,255)" text-anchor="middle" x="140" xml:space="preserve" y="456" zvalue="1067">10kVⅠ段母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="604" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="140" xml:space="preserve" y="472" zvalue="1067">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="602" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,648.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="653" zvalue="1069">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="599" stroke="rgb(255,255,255)" text-anchor="middle" x="200.671875" xml:space="preserve" y="456" zvalue="1072">10kVⅡ段母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="599" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="200.671875" xml:space="preserve" y="472" zvalue="1072">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="596" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89,491.5) scale(1,1) translate(0,0)" writing-mode="lr" x="89" xml:space="preserve" y="496" zvalue="1075">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="595" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89,517) scale(1,1) translate(0,0)" writing-mode="lr" x="89" xml:space="preserve" y="521.5" zvalue="1076">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="594" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89,542.5) scale(1,1) translate(0,0)" writing-mode="lr" x="89" xml:space="preserve" y="547" zvalue="1077">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="593" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,88,567) scale(1,1) translate(0,0)" writing-mode="lr" x="88" xml:space="preserve" y="571.5" zvalue="1078">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="592" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89,593.5) scale(1,1) translate(0,0)" writing-mode="lr" x="89" xml:space="preserve" y="598" zvalue="1079">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.054,956) scale(1,1) translate(0,0)" writing-mode="lr" x="237.05" xml:space="preserve" y="960" zvalue="1080">MangShiShengHeLaJiFaDianChang-01-2021</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,147.054,988) scale(1,1) translate(0,0)" writing-mode="lr" x="147.05" xml:space="preserve" y="994" zvalue="1081">李文杰</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,327.054,988) scale(1,1) translate(0,0)" writing-mode="lr" x="327.05" xml:space="preserve" y="994" zvalue="1082">20210220</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="851" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,646,772.25) scale(1,1) translate(0,0)" writing-mode="lr" x="646" xml:space="preserve" y="776.75" zvalue="1261">#1机6000kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="310" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1220.5,562.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1220.5" xml:space="preserve" y="566.72" zvalue="1675">0902</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="316" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1711.5,472.889) scale(1,1) translate(0,1.54006e-13)" writing-mode="lr" x="1711.5" xml:space="preserve" y="477.39" zvalue="1682">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,855.03,542.111) scale(1,1) translate(0,0)" writing-mode="lr" x="855.03" xml:space="preserve" y="546.61" zvalue="1721">073</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,827.5,779) scale(1,1) translate(0,0)" writing-mode="lr" x="827.5" xml:space="preserve" y="783.5" zvalue="1731">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,890,603) scale(1,1) translate(0,0)" writing-mode="lr" x="890" xml:space="preserve" y="607.5" zvalue="1732">0737</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1044.03,542.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1044.03" xml:space="preserve" y="546.61" zvalue="1735">074</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1016.5,779) scale(1,1) translate(0,0)" writing-mode="lr" x="1016.5" xml:space="preserve" y="783.5" zvalue="1743">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1079,603) scale(1,1) translate(0,0)" writing-mode="lr" x="1079" xml:space="preserve" y="607.5" zvalue="1745">0747</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1199.78,676) scale(1,1) translate(-2.49639e-13,0)" writing-mode="lr" x="1199.78" xml:space="preserve" y="680.5" zvalue="1748">10kVⅡ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1322,556) scale(1,1) translate(0,0)" writing-mode="lr" x="1322" xml:space="preserve" y="560.5" zvalue="1750">0122</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1615.03,430.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1615.03" xml:space="preserve" y="434.61" zvalue="1754">075</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1590.03,248) scale(1,1) translate(0,0)" writing-mode="lr" x="1590.03" xml:space="preserve" y="252.5" zvalue="1758">10kV轩海线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1612.94,326) scale(1,1) translate(0,0)" writing-mode="lr" x="1612.94" xml:space="preserve" y="330.5" zvalue="1776">0756</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" x="1756.75" xml:space="preserve" y="329.5" zvalue="1779">备注:10kV轩海线0756刀闸为机械刀闸，位置信</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1756.75" xml:space="preserve" y="345.5" zvalue="1779">号无法采集。</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,188.211,396.591) scale(1,1) translate(0,0)" writing-mode="lr" x="188.21" xml:space="preserve" y="401.09" zvalue="1881">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,293.211,396.591) scale(1,1) translate(0,0)" writing-mode="lr" x="293.21" xml:space="preserve" y="401.09" zvalue="1882">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1423,791) scale(1,1) translate(0,0)" writing-mode="lr" x="1423" xml:space="preserve" y="795.5" zvalue="1886">容量：6MW</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="191.9999999999995" y1="172.6586151123047" y2="172.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="191.9999999999995" y1="198.6586151123047" y2="198.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="10.99999999999955" y1="172.6586151123047" y2="198.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="191.9999999999995" y1="172.6586151123047" y2="198.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="372.9999999999995" y1="172.6586151123047" y2="172.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="372.9999999999995" y1="198.6586151123047" y2="198.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="191.9999999999995" y1="172.6586151123047" y2="198.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.9999999999995" x2="372.9999999999995" y1="172.6586151123047" y2="198.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="191.9999999999995" y1="198.6586151123047" y2="198.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="191.9999999999995" y1="222.9086151123047" y2="222.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="10.99999999999955" y1="198.6586151123047" y2="222.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="191.9999999999995" y1="198.6586151123047" y2="222.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="372.9999999999995" y1="198.6586151123047" y2="198.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="372.9999999999995" y1="222.9086151123047" y2="222.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="191.9999999999995" y1="198.6586151123047" y2="222.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.9999999999995" x2="372.9999999999995" y1="198.6586151123047" y2="222.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="191.9999999999995" y1="222.9086151123047" y2="222.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="191.9999999999995" y1="245.6586151123047" y2="245.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="10.99999999999955" y1="222.9086151123047" y2="245.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="191.9999999999995" y1="222.9086151123047" y2="245.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="372.9999999999995" y1="222.9086151123047" y2="222.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="372.9999999999995" y1="245.6586151123047" y2="245.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="191.9999999999995" y1="222.9086151123047" y2="245.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.9999999999995" x2="372.9999999999995" y1="222.9086151123047" y2="245.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="191.9999999999995" y1="245.6586151123047" y2="245.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="191.9999999999995" y1="268.4086151123047" y2="268.4086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="10.99999999999955" y1="245.6586151123047" y2="268.4086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="191.9999999999995" y1="245.6586151123047" y2="268.4086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="372.9999999999995" y1="245.6586151123047" y2="245.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="372.9999999999995" y1="268.4086151123047" y2="268.4086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="191.9999999999995" y1="245.6586151123047" y2="268.4086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.9999999999995" x2="372.9999999999995" y1="245.6586151123047" y2="268.4086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="191.9999999999995" y1="268.4086151123047" y2="268.4086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="191.9999999999995" y1="291.1586151123047" y2="291.1586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="10.99999999999955" y1="268.4086151123047" y2="291.1586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="191.9999999999995" y1="268.4086151123047" y2="291.1586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="372.9999999999995" y1="268.4086151123047" y2="268.4086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="372.9999999999995" y1="291.1586151123047" y2="291.1586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="191.9999999999995" y1="268.4086151123047" y2="291.1586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.9999999999995" x2="372.9999999999995" y1="268.4086151123047" y2="291.1586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="191.9999999999995" y1="291.1586151123047" y2="291.1586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="191.9999999999995" y1="313.9086151123047" y2="313.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="10.99999999999955" y1="291.1586151123047" y2="313.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="191.9999999999995" y1="291.1586151123047" y2="313.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="372.9999999999995" y1="291.1586151123047" y2="291.1586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="372.9999999999995" y1="313.9086151123047" y2="313.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="191.9999999999995" y1="291.1586151123047" y2="313.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.9999999999995" x2="372.9999999999995" y1="291.1586151123047" y2="313.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="191.9999999999995" y1="313.9086151123047" y2="313.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="191.9999999999995" y1="336.6586151123047" y2="336.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.99999999999955" x2="10.99999999999955" y1="313.9086151123047" y2="336.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="191.9999999999995" y1="313.9086151123047" y2="336.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="372.9999999999995" y1="313.9086151123047" y2="313.9086151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="372.9999999999995" y1="336.6586151123047" y2="336.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.9999999999995" x2="191.9999999999995" y1="313.9086151123047" y2="336.6586151123047"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.9999999999995" x2="372.9999999999995" y1="313.9086151123047" y2="336.6586151123047"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,57.5,185.659) scale(1,1) translate(0,0)" writing-mode="lr" x="15" xml:space="preserve" y="190.16" zvalue="1889">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238,185.659) scale(1,1) translate(0,0)" writing-mode="lr" x="195.5" xml:space="preserve" y="190.16" zvalue="1890">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.6875,258.909) scale(1,1) translate(0,0)" writing-mode="lr" x="60.69" xml:space="preserve" y="263.41" zvalue="1891">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,239.625,257.659) scale(1,1) translate(0,0)" writing-mode="lr" x="239.62" xml:space="preserve" y="262.16" zvalue="1892">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,56.5,211.659) scale(1,1) translate(0,0)" writing-mode="lr" x="14" xml:space="preserve" y="216.16" zvalue="1897">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,237,211.659) scale(1,1) translate(0,0)" writing-mode="lr" x="194.5" xml:space="preserve" y="216.16" zvalue="1898">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.6875,304.909) scale(1,1) translate(0,0)" writing-mode="lr" x="57.69" xml:space="preserve" y="309.41" zvalue="1901">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225.687,303.909) scale(1,1) translate(0,0)" writing-mode="lr" x="225.69" xml:space="preserve" y="308.41" zvalue="1903">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.6875,327.909) scale(1,1) translate(0,0)" writing-mode="lr" x="57.69" xml:space="preserve" y="332.41" zvalue="1904">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225.687,326.909) scale(1,1) translate(0,0)" writing-mode="lr" x="225.69" xml:space="preserve" y="331.41" zvalue="1905">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,56.5,234.659) scale(1,1) translate(0,0)" writing-mode="lr" x="14" xml:space="preserve" y="239.16" zvalue="1906">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,236.5,233.659) scale(1,1) translate(0,0)" writing-mode="lr" x="194" xml:space="preserve" y="238.16" zvalue="1908">厂用电率</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="36" y="384" zvalue="1885"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="15">
   <path class="kv10" d="M 509.13 494.89 L 1301 494.89" stroke-width="4" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674419015683" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674419015683"/></metadata>
  <path d="M 509.13 494.89 L 1301 494.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="315">
   <path class="kv10" d="M 1422 494.89 L 1771 494.89" stroke-width="4" zvalue="1681"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674419081219" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674419081219"/></metadata>
  <path d="M 1422 494.89 L 1771 494.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="341">
   <use class="kv10" height="20" transform="rotate(0,645.03,543.111) scale(2,2) translate(-317.515,-261.556)" width="10" x="635.0295695852833" xlink:href="#Breaker:小车断路器_0" y="523.1111128065321" zvalue="612"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925206016003" ObjectName="#1发电机071断路器"/>
   <cge:TPSR_Ref TObjectID="6473925206016003"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,645.03,543.111) scale(2,2) translate(-317.515,-261.556)" width="10" x="635.0295695852833" y="523.1111128065321"/></g>
  <g id="88">
   <use class="kv10" height="20" transform="rotate(0,830.03,543.111) scale(2,2) translate(-410.015,-261.556)" width="10" x="820.0295695852833" xlink:href="#Breaker:小车断路器_0" y="523.1111128065321" zvalue="1720"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925206081539" ObjectName="#1站用变073断路器"/>
   <cge:TPSR_Ref TObjectID="6473925206081539"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,830.03,543.111) scale(2,2) translate(-410.015,-261.556)" width="10" x="820.0295695852833" y="523.1111128065321"/></g>
  <g id="114">
   <use class="kv10" height="20" transform="rotate(0,1019.03,543.111) scale(2,2) translate(-504.515,-261.556)" width="10" x="1009.029569585283" xlink:href="#Breaker:小车断路器_0" y="523.1111128065321" zvalue="1734"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925206147075" ObjectName="#2站用变074断路器"/>
   <cge:TPSR_Ref TObjectID="6473925206147075"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1019.03,543.111) scale(2,2) translate(-504.515,-261.556)" width="10" x="1009.029569585283" y="523.1111128065321"/></g>
  <g id="7">
   <use class="kv10" height="20" transform="rotate(0,1590.03,431.111) scale(2,2) translate(-790.015,-205.556)" width="10" x="1580.029569585283" xlink:href="#Breaker:小车断路器_0" y="411.1111128065321" zvalue="1753"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925206212611" ObjectName="10kV轩海线075断路器"/>
   <cge:TPSR_Ref TObjectID="6473925206212611"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1590.03,431.111) scale(2,2) translate(-790.015,-205.556)" width="10" x="1580.029569585283" y="411.1111128065321"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="334">
   <path class="kv10" d="M 645.03 524.61 L 645.03 494.89" stroke-width="1" zvalue="623"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@0" LinkObjectIDznd="15@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 645.03 524.61 L 645.03 494.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv10" d="M 645.03 561.11 L 645 703.88" stroke-width="1" zvalue="1572"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@1" LinkObjectIDznd="850@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 645.03 561.11 L 645 703.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv10" d="M 1196.43 550.4 L 1196.43 494.89" stroke-width="1" zvalue="1675"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="308@0" LinkObjectIDznd="15@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1196.43 550.4 L 1196.43 494.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="kv10" d="M 1196.4 574.24 L 1196.4 594.76" stroke-width="1" zvalue="1676"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="308@1" LinkObjectIDznd="115@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1196.4 574.24 L 1196.4 594.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv10" d="M 612.03 620.63 L 612.03 575 L 645.03 575" stroke-width="1" zvalue="1716"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="12@0" LinkObjectIDznd="81" MaxPinNum="2"/>
   </metadata>
  <path d="M 612.03 620.63 L 612.03 575 L 645.03 575" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv10" d="M 645 623.75 L 645.02 623.75" stroke-width="1" zvalue="1718"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@0" LinkObjectIDznd="81" MaxPinNum="2"/>
   </metadata>
  <path d="M 645 623.75 L 645.02 623.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv10" d="M 830.03 524.61 L 830.03 494.89" stroke-width="1" zvalue="1722"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@0" LinkObjectIDznd="15@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 830.03 524.61 L 830.03 494.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv10" d="M 830.03 561.11 L 830.03 687.36" stroke-width="1" zvalue="1725"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@1" LinkObjectIDznd="94@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 830.03 561.11 L 830.03 687.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv10" d="M 797.03 620.63 L 797.03 575 L 830.03 575" stroke-width="1" zvalue="1727"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="82" MaxPinNum="2"/>
   </metadata>
  <path d="M 797.03 620.63 L 797.03 575 L 830.03 575" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv10" d="M 830 623.75 L 830.03 623.75" stroke-width="1" zvalue="1729"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="82" MaxPinNum="2"/>
   </metadata>
  <path d="M 830 623.75 L 830.03 623.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv10" d="M 868.05 594.25 L 868.05 575 L 830 575" stroke-width="1" zvalue="1732"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="73" MaxPinNum="2"/>
   </metadata>
  <path d="M 868.05 594.25 L 868.05 575 L 830 575" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv10" d="M 1019.03 524.61 L 1019.03 494.89" stroke-width="1" zvalue="1736"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="15@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1019.03 524.61 L 1019.03 494.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv10" d="M 1019.03 561.11 L 1019.03 687.36" stroke-width="1" zvalue="1737"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@1" LinkObjectIDznd="107@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1019.03 561.11 L 1019.03 687.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 986.03 620.63 L 986.03 575 L 1019.03 575" stroke-width="1" zvalue="1739"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="112" MaxPinNum="2"/>
   </metadata>
  <path d="M 986.03 620.63 L 986.03 575 L 1019.03 575" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv10" d="M 1019 623.75 L 1019.03 623.75" stroke-width="1" zvalue="1741"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="112" MaxPinNum="2"/>
   </metadata>
  <path d="M 1019 623.75 L 1019.03 623.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv10" d="M 1057.05 594.25 L 1057.05 575 L 1019 575" stroke-width="1" zvalue="1746"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="110" MaxPinNum="2"/>
   </metadata>
  <path d="M 1057.05 594.25 L 1057.05 575 L 1019 575" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv10" d="M 1290.99 543.7 L 1290.99 494.89" stroke-width="1" zvalue="1750"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@0" LinkObjectIDznd="15@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1290.99 543.7 L 1290.99 494.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv10" d="M 1291.02 566.32 L 1291.02 596 L 1455 596 L 1455 494.89" stroke-width="1" zvalue="1751"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@1" LinkObjectIDznd="315@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1291.02 566.32 L 1291.02 596 L 1455 596 L 1455 494.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv10" d="M 1590.03 449.11 L 1590.03 494.89" stroke-width="1" zvalue="1760"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="7@1" LinkObjectIDznd="315@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1590.03 449.11 L 1590.03 494.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv10" d="M 1590.03 296.39 L 1590 295.25" stroke-width="1" zvalue="1762"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="27" LinkObjectIDznd="11@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1590.03 296.39 L 1590 295.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv10" d="M 1632.03 398.63 L 1632.03 369 L 1590.03 369" stroke-width="1" zvalue="1763"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8@0" LinkObjectIDznd="26" MaxPinNum="2"/>
   </metadata>
  <path d="M 1632.03 398.63 L 1632.03 369 L 1590.03 369" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv10" d="M 1590.03 412.61 L 1590.03 337.81" stroke-width="1" zvalue="1776"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="7@0" LinkObjectIDznd="24@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1590.03 412.61 L 1590.03 337.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv10" d="M 1590.03 316.36 L 1590.03 276.43" stroke-width="1" zvalue="1777"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@0" LinkObjectIDznd="9@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1590.03 316.36 L 1590.03 276.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="kv10" height="30" transform="rotate(0,645,726) scale(1.5,1.5) translate(-207.5,-234.5)" width="30" x="622.5" xlink:href="#Generator:发电机_0" y="703.5" zvalue="1260"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454713147395" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192454713147395"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,645,726) scale(1.5,1.5) translate(-207.5,-234.5)" width="30" x="622.5" y="703.5"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="216">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="216" prefix="P:" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,645.5,811) scale(1,1) translate(0,0)" writing-mode="lr" x="645.74" xml:space="preserve" y="817.49" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136113254402" ObjectName="P"/>
   </metadata>
  </g>
  <g id="218">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="218" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,645.5,835) scale(1,1) translate(0,0)" writing-mode="lr" x="645.74" xml:space="preserve" y="841.49" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136113319938" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="220">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="220" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,645.5,860) scale(1,1) translate(0,0)" writing-mode="lr" x="645.74" xml:space="preserve" y="866.49" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136113385474" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="141" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1199.13,708.389) scale(1,1) translate(3.84403e-13,0)" writing-mode="lr" x="1199.33" xml:space="preserve" y="713.3" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136112205826" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="142" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1199.13,731.389) scale(1,1) translate(3.84403e-13,0)" writing-mode="lr" x="1199.33" xml:space="preserve" y="736.3" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136112271362" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1199.13,754.389) scale(1,1) translate(3.84403e-13,0)" writing-mode="lr" x="1199.33" xml:space="preserve" y="759.3" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136112336898" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="154">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="154" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1199.13,772.889) scale(1,1) translate(3.84403e-13,0)" writing-mode="lr" x="1199.33" xml:space="preserve" y="777.8" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136112467970" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(0,170,0)" font-family="SimSun" font-size="16" id="21" prefix="P:" stroke="rgb(0,170,0)" text-anchor="middle" transform="rotate(0,1589.5,181) scale(1,1) translate(0,0)" writing-mode="lr" x="1589.74" xml:space="preserve" y="187.49" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136117186562" ObjectName="P"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="23" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1589.5,202) scale(1,1) translate(0,0)" writing-mode="lr" x="1589.74" xml:space="preserve" y="208.49" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136117252098" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="22" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1589.5,223) scale(1,1) translate(0,0)" writing-mode="lr" x="1589.74" xml:space="preserve" y="229.49" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136117317634" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="32" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,205.889,518) scale(1,1) translate(-3.9746e-14,0)" writing-mode="lr" x="206.01" xml:space="preserve" y="522.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136112205826" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,205.889,492.5) scale(1,1) translate(-3.9746e-14,0)" writing-mode="lr" x="206.01" xml:space="preserve" y="497.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136112467970" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="33" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,205.889,542.333) scale(1,1) translate(-3.9746e-14,0)" writing-mode="lr" x="206.01" xml:space="preserve" y="547.24" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136112271362" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,205.889,566.333) scale(1,1) translate(-3.9746e-14,0)" writing-mode="lr" x="206.01" xml:space="preserve" y="571.24" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136112336898" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="36">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,205.889,590.333) scale(1,1) translate(-3.9746e-14,0)" writing-mode="lr" x="206.01" xml:space="preserve" y="595.24" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136112664578" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="89" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,825.5,814) scale(1,1) translate(0,0)" writing-mode="lr" x="826.08" xml:space="preserve" y="819.4400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136114958338" ObjectName="P"/>
   </metadata>
  </g>
  <g id="90">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="90" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1019.5,814) scale(1,1) translate(0,0)" writing-mode="lr" x="1020.08" xml:space="preserve" y="819.4400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136115548162" ObjectName="P"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="91" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,827.5,838) scale(1,1) translate(0,0)" writing-mode="lr" x="828.08" xml:space="preserve" y="843.4400000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136115023874" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="92" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1019.5,836) scale(1,1) translate(0,0)" writing-mode="lr" x="1020.08" xml:space="preserve" y="841.4400000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136115613698" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="93" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,831.5,863) scale(1,1) translate(0,0)" writing-mode="lr" x="832.08" xml:space="preserve" y="868.4400000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136115089410" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="96" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1019.5,861) scale(1,1) translate(0,0)" writing-mode="lr" x="1020.08" xml:space="preserve" y="866.4400000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136115679234" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="279">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="279" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,157.611,185.825) scale(1,1) translate(0,0)" writing-mode="lr" x="157.77" xml:space="preserve" y="190.74" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136118366210" ObjectName="GEN_PSUM"/>
   </metadata>
  </g>
  <g id="70">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,335.222,186.825) scale(1,1) translate(0,0)" writing-mode="lr" x="335.38" xml:space="preserve" y="191.74" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136118431746" ObjectName="GEN_QSUM"/>
   </metadata>
  </g>
  <g id="69">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,335.222,258.825) scale(1,1) translate(0,0)" writing-mode="lr" x="335.38" xml:space="preserve" y="263.74" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136114302978" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,157.611,210.825) scale(1,1) translate(0,0)" writing-mode="lr" x="157.77" xml:space="preserve" y="215.74" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136118235138" ObjectName="F"/>
   </metadata>
  </g>
  <g id="49">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,335.222,211.825) scale(1,1) translate(0,0)" writing-mode="lr" x="335.38" xml:space="preserve" y="216.74" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136118300674" ObjectName="F"/>
   </metadata>
  </g>
  <g id="247">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="247" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,157.611,234.825) scale(1,1) translate(0,0)" writing-mode="lr" x="157.77" xml:space="preserve" y="239.74" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127190069256" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="45">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,335.222,233.825) scale(1,1) translate(0,0)" writing-mode="lr" x="335.38" xml:space="preserve" y="238.74" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127190003717" ObjectName="厂用电率"/>
   </metadata>
  </g>
 </g>
 <g id="DisconnectorClass">
  <g id="308">
   <use class="kv10" height="30" transform="rotate(0,1196.33,562.222) scale(1.11111,0.814815) translate(-118.8,125)" width="15" x="1188" xlink:href="#Disconnector:刀闸_0" y="550" zvalue="1674"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454793887746" ObjectName="10kVⅡ段母线电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454793887746"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1196.33,562.222) scale(1.11111,0.814815) translate(-118.8,125)" width="15" x="1188" y="550"/></g>
  <g id="2">
   <use class="kv10" height="26" transform="rotate(0,1291,555) scale(1,1) translate(0,0)" width="14" x="1284" xlink:href="#Disconnector:联体手车刀闸_0" y="542" zvalue="1749"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454714130435" ObjectName="10kV分段0122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454714130435"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1291,555) scale(1,1) translate(0,0)" width="14" x="1284" y="542"/></g>
  <g id="24">
   <use class="kv10" height="30" transform="rotate(0,1589.94,327) scale(1,0.733333) translate(0,114.909)" width="15" x="1582.441792534422" xlink:href="#Disconnector:刀闸_0" y="316" zvalue="1775"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454850445314" ObjectName="10kV轩海线0756"/>
   <cge:TPSR_Ref TObjectID="6192454850445314"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1589.94,327) scale(1,0.733333) translate(0,114.909)" width="15" x="1582.441792534422" y="316"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="12">
   <use class="kv10" height="26" transform="rotate(0,612,633) scale(1,1) translate(0,0)" width="12" x="606" xlink:href="#Accessory:避雷器1_0" y="620" zvalue="1715"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454713278467" ObjectName="#1发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,612,633) scale(1,1) translate(0,0)" width="12" x="606" y="620"/></g>
  <g id="18">
   <use class="kv10" height="7" transform="rotate(0,645,627) scale(1,1) translate(0,0)" width="12" x="639" xlink:href="#Accessory:电缆1_0" y="623.5" zvalue="1717"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454713344003" ObjectName="#1发电机电缆"/>
   </metadata>
  <rect fill="white" height="7" opacity="0" stroke="white" transform="rotate(0,645,627) scale(1,1) translate(0,0)" width="12" x="639" y="623.5"/></g>
  <g id="80">
   <use class="kv10" height="26" transform="rotate(0,797,633) scale(1,1) translate(0,0)" width="12" x="791" xlink:href="#Accessory:避雷器1_0" y="620" zvalue="1726"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454713475075" ObjectName="#1站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,797,633) scale(1,1) translate(0,0)" width="12" x="791" y="620"/></g>
  <g id="41">
   <use class="kv10" height="7" transform="rotate(0,830,627) scale(1,1) translate(0,0)" width="12" x="824" xlink:href="#Accessory:电缆1_0" y="623.5" zvalue="1728"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454713409539" ObjectName="#1站用变电缆"/>
   </metadata>
  <rect fill="white" height="7" opacity="0" stroke="white" transform="rotate(0,830,627) scale(1,1) translate(0,0)" width="12" x="824" y="623.5"/></g>
  <g id="111">
   <use class="kv10" height="26" transform="rotate(0,986,633) scale(1,1) translate(0,0)" width="12" x="980" xlink:href="#Accessory:避雷器1_0" y="620" zvalue="1738"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454713999363" ObjectName="#2站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,986,633) scale(1,1) translate(0,0)" width="12" x="980" y="620"/></g>
  <g id="109">
   <use class="kv10" height="7" transform="rotate(0,1019,627) scale(1,1) translate(0,0)" width="12" x="1013" xlink:href="#Accessory:电缆1_0" y="623.5" zvalue="1740"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454713933827" ObjectName="#2站用变电缆"/>
   </metadata>
  <rect fill="white" height="7" opacity="0" stroke="white" transform="rotate(0,1019,627) scale(1,1) translate(0,0)" width="12" x="1013" y="623.5"/></g>
  <g id="115">
   <use class="kv10" height="51" transform="rotate(0,1202.78,620) scale(1,1) translate(0,0)" width="40" x="1182.776293719447" xlink:href="#Accessory:母线PT2020_0" y="594.5" zvalue="1747"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454714064899" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="51" opacity="0" stroke="white" transform="rotate(0,1202.78,620) scale(1,1) translate(0,0)" width="40" x="1182.776293719447" y="594.5"/></g>
  <g id="8">
   <use class="kv10" height="26" transform="rotate(0,1632,411) scale(1,1) translate(0,0)" width="12" x="1626" xlink:href="#Accessory:避雷器1_0" y="398" zvalue="1756"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454714195971" ObjectName="10kV轩海线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1632,411) scale(1,1) translate(0,0)" width="12" x="1626" y="398"/></g>
  <g id="11">
   <use class="kv10" height="7" transform="rotate(0,1590,292) scale(1,-1) translate(0,-584)" width="12" x="1584" xlink:href="#Accessory:电缆1_0" y="288.5" zvalue="1759"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454714327043" ObjectName="10kV轩海线电缆"/>
   </metadata>
  <rect fill="white" height="7" opacity="0" stroke="white" transform="rotate(0,1590,292) scale(1,-1) translate(0,-584)" width="12" x="1584" y="288.5"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="94">
   <use class="kv10" height="30" transform="rotate(0,830,724.658) scale(2.57143,2.58947) translate(-485.222,-420.968)" width="28" x="794" xlink:href="#EnergyConsumer:站用变DY接地_0" y="685.8157894736842" zvalue="1730"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454713540611" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,830,724.658) scale(2.57143,2.58947) translate(-485.222,-420.968)" width="28" x="794" y="685.8157894736842"/></g>
  <g id="107">
   <use class="kv10" height="30" transform="rotate(0,1019,724.658) scale(2.57143,2.58947) translate(-600.722,-420.968)" width="28" x="983" xlink:href="#EnergyConsumer:站用变DY接地_0" y="685.8157894736842" zvalue="1742"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454713868291" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1019,724.658) scale(2.57143,2.58947) translate(-600.722,-420.968)" width="28" x="983" y="685.8157894736842"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="99">
   <use class="kv10" height="20" transform="rotate(0,868,604) scale(1,1) translate(0,0)" width="10" x="863" xlink:href="#GroundDisconnector:地刀_0" y="594" zvalue="1731"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454713671683" ObjectName="#1站用变0737接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454713671683"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,868,604) scale(1,1) translate(0,0)" width="10" x="863" y="594"/></g>
  <g id="106">
   <use class="kv10" height="20" transform="rotate(0,1057,604) scale(1,1) translate(0,0)" width="10" x="1052" xlink:href="#GroundDisconnector:地刀_0" y="594" zvalue="1744"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454713802755" ObjectName="#2站用变0747接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454713802755"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1057,604) scale(1,1) translate(0,0)" width="10" x="1052" y="594"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,320.485,397.107) scale(0.708333,0.665547) translate(127.589,194.539)" width="30" x="309.86" xlink:href="#State:红绿圆(方形)_0" y="387.12" zvalue="1883"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374928052225" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,320.485,397.107) scale(0.708333,0.665547) translate(127.589,194.539)" width="30" x="309.86" y="387.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,224.86,397.107) scale(0.708333,0.665547) translate(88.2145,194.539)" width="30" x="214.24" xlink:href="#State:红绿圆(方形)_0" y="387.12" zvalue="1884"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562962131451905" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,224.86,397.107) scale(0.708333,0.665547) translate(88.2145,194.539)" width="30" x="214.24" y="387.12"/></g>
 </g>
</svg>