<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1000 1056" width="1000">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.45181617405996" x2="1.714850492606708" y1="5.276370517142858" y2="14.05696281619048"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.583333333333333" x2="8.583333333333334" y1="5.166666666666667" y2="14.16666666666667"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV作业变" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1000" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="BusbarSectionClass">
  <g id="2">
   <path class="kv10" d="M 138 666 L 894 666" stroke-width="6" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kVI母"/>
   </metadata>
  <path d="M 138 666 L 894 666" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv35" d="M 165 265 L 886 265" stroke-width="6" zvalue="3"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV I母"/>
   </metadata>
  <path d="M 165 265 L 886 265" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv10" d="M 136 937 L 898 937" stroke-width="5" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV I旁"/>
   </metadata>
  <path d="M 136 937 L 898 937" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,929.5,665) scale(1,1) translate(0,0)" writing-mode="lr" x="929.5" xml:space="preserve" y="669.5" zvalue="3">10kVI母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,942.5,259) scale(1,1) translate(0,0)" writing-mode="lr" x="942.5" xml:space="preserve" y="263.5" zvalue="4">35kV I母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,364,41.5) scale(1,1) translate(0,0)" writing-mode="lr" x="364" xml:space="preserve" y="46" zvalue="14">031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,342,217) scale(1,1) translate(0,0)" writing-mode="lr" x="342" xml:space="preserve" y="221.5" zvalue="16">0311</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,340,111) scale(1,1) translate(0,0)" writing-mode="lr" x="340" xml:space="preserve" y="115.5" zvalue="19">0316</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,383.5,163) scale(1,1) translate(0,0)" writing-mode="lr" x="383.5" xml:space="preserve" y="167.5" zvalue="22">031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,449,79) scale(1,1) translate(0,0)" writing-mode="lr" x="449" xml:space="preserve" y="83.5" zvalue="25">03167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,450,122) scale(1,1) translate(0,0)" writing-mode="lr" x="450" xml:space="preserve" y="126.5" zvalue="28">03160</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,448,174) scale(1,1) translate(0,0)" writing-mode="lr" x="448" xml:space="preserve" y="178.5" zvalue="31">03117</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,443.5,463) scale(1,1) translate(0,0)" writing-mode="lr" x="443.5" xml:space="preserve" y="467.5" zvalue="40">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,536,357.5) scale(1,1) translate(0,0)" writing-mode="lr" x="536" xml:space="preserve" y="362" zvalue="43">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,488.5,297) scale(1,1) translate(0,0)" writing-mode="lr" x="488.5" xml:space="preserve" y="301.5" zvalue="47">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,585,311) scale(1,1) translate(0,0)" writing-mode="lr" x="585" xml:space="preserve" y="315.5" zvalue="51">30117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,532,594.5) scale(1,1) translate(0,0)" writing-mode="lr" x="532" xml:space="preserve" y="599" zvalue="56">050</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,934,937.5) scale(1,1) translate(0,0)" writing-mode="lr" x="934" xml:space="preserve" y="942" zvalue="64">10kV I旁</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,273,1006) scale(1,1) translate(0,0)" writing-mode="lr" x="273" xml:space="preserve" y="1010.5" zvalue="65">051负荷</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,296.82,728.5) scale(1,1) translate(0,0)" writing-mode="lr" x="296.82" xml:space="preserve" y="733" zvalue="75">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,250,814) scale(1,1) translate(0,0)" writing-mode="lr" x="250" xml:space="preserve" y="818.5" zvalue="79">0516</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,298,892) scale(1,1) translate(0,0)" writing-mode="lr" x="298" xml:space="preserve" y="896.5" zvalue="83">0511</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,490,1028) scale(1,1) translate(0,0)" writing-mode="lr" x="490" xml:space="preserve" y="1032.5" zvalue="97">052负荷</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,513.82,730.5) scale(1,1) translate(0,0)" writing-mode="lr" x="513.8200000000001" xml:space="preserve" y="735" zvalue="99">052</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,467,816) scale(1,1) translate(0,0)" writing-mode="lr" x="467" xml:space="preserve" y="820.5" zvalue="103">0526</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,515,894) scale(1,1) translate(0,0)" writing-mode="lr" x="515" xml:space="preserve" y="898.5" zvalue="106">0521</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,724,1029) scale(1,1) translate(0,0)" writing-mode="lr" x="724" xml:space="preserve" y="1033.5" zvalue="111">053负荷</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,747.82,731.5) scale(1,1) translate(0,0)" writing-mode="lr" x="747.8200000000001" xml:space="preserve" y="736" zvalue="113">053</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,701,817) scale(1,1) translate(0,0)" writing-mode="lr" x="701" xml:space="preserve" y="821.5" zvalue="117">0536</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,749,895) scale(1,1) translate(0,0)" writing-mode="lr" x="749" xml:space="preserve" y="899.5" zvalue="120">0531</text>
 </g>
 <g id="ACLineSegmentClass">
  <g id="13">
   <use class="kv35" height="30" transform="rotate(0,364,61) scale(1.85714,0.5) translate(-165,53.5)" width="7" x="357.5" xlink:href="#ACLineSegment:线路_0" y="53.5" zvalue="13"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="031"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,364,61) scale(1.85714,0.5) translate(-165,53.5)" width="7" x="357.5" y="53.5"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="15">
   <use class="kv35" height="30" transform="rotate(0,365,218) scale(1,0.733333) translate(0,75.2727)" width="15" x="357.5" xlink:href="#Disconnector:刀闸_0" y="207" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0311隔离开关"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,365,218) scale(1,0.733333) translate(0,75.2727)" width="15" x="357.5" y="207"/></g>
  <g id="18">
   <use class="kv35" height="30" transform="rotate(0,364,112) scale(1,0.733333) translate(0,36.7273)" width="15" x="356.5" xlink:href="#Disconnector:刀闸_0" y="101" zvalue="18"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0316隔离开关"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,364,112) scale(1,0.733333) translate(0,36.7273)" width="15" x="356.5" y="101"/></g>
  <g id="41">
   <use class="kv35" height="30" transform="rotate(0,511.5,298) scale(1,0.733333) translate(0,104.364)" width="15" x="504" xlink:href="#Disconnector:刀闸_0" y="287" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,511.5,298) scale(1,0.733333) translate(0,104.364)" width="15" x="504" y="287"/></g>
  <g id="67">
   <use class="kv10" height="30" transform="rotate(0,274.5,815) scale(1,0.733333) translate(0,292.364)" width="15" x="267" xlink:href="#Disconnector:刀闸_0" y="804" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0516"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,274.5,815) scale(1,0.733333) translate(0,292.364)" width="15" x="267" y="804"/></g>
  <g id="70">
   <use class="kv10" height="30" transform="rotate(0,321.5,893) scale(1,0.733333) translate(0,320.727)" width="15" x="314" xlink:href="#Disconnector:刀闸_0" y="882" zvalue="82"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0511"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,321.5,893) scale(1,0.733333) translate(0,320.727)" width="15" x="314" y="882"/></g>
  <g id="109">
   <use class="kv10" height="30" transform="rotate(0,491.5,817) scale(1,0.733333) translate(0,293.091)" width="15" x="484" xlink:href="#Disconnector:刀闸_0" y="806" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0526"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,491.5,817) scale(1,0.733333) translate(0,293.091)" width="15" x="484" y="806"/></g>
  <g id="106">
   <use class="kv10" height="30" transform="rotate(0,538.5,895) scale(1,0.733333) translate(0,321.455)" width="15" x="531" xlink:href="#Disconnector:刀闸_0" y="884" zvalue="105"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0521"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,538.5,895) scale(1,0.733333) translate(0,321.455)" width="15" x="531" y="884"/></g>
  <g id="122">
   <use class="kv10" height="30" transform="rotate(0,725.5,818) scale(1,0.733333) translate(0,293.455)" width="15" x="718" xlink:href="#Disconnector:刀闸_0" y="807" zvalue="115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0536"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,725.5,818) scale(1,0.733333) translate(0,293.455)" width="15" x="718" y="807"/></g>
  <g id="119">
   <use class="kv10" height="30" transform="rotate(0,772.5,896) scale(1,0.733333) translate(0,321.818)" width="15" x="765" xlink:href="#Disconnector:刀闸_0" y="885" zvalue="119"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0531"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,772.5,896) scale(1,0.733333) translate(0,321.818)" width="15" x="765" y="885"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="17">
   <path class="kv35" d="M 365.06 228.81 L 365.06 265" stroke-width="1" zvalue="17"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@1" LinkObjectIDznd="3@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 365.06 228.81 L 365.06 265" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv35" d="M 364 68.43 L 364.09 101.36" stroke-width="1" zvalue="19"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="13@0" LinkObjectIDznd="18@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 364 68.43 L 364.09 101.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv35" d="M 364.06 122.81 L 364.06 151.09" stroke-width="1" zvalue="22"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@1" LinkObjectIDznd="21@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 364.06 122.81 L 364.06 151.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv35" d="M 364.1 176.89 L 364.1 207.36" stroke-width="1" zvalue="23"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@1" LinkObjectIDznd="15@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 364.1 176.89 L 364.1 207.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv35" d="M 435.25 91.95 L 364.06 91.95" stroke-width="1" zvalue="25"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 435.25 91.95 L 364.06 91.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv35" d="M 439.25 138.95 L 364.06 138.95" stroke-width="1" zvalue="28"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@0" LinkObjectIDznd="22" MaxPinNum="2"/>
   </metadata>
  <path d="M 439.25 138.95 L 364.06 138.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv35" d="M 437.25 188.95 L 364.1 188.95" stroke-width="1" zvalue="31"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="23" MaxPinNum="2"/>
   </metadata>
  <path d="M 437.25 188.95 L 364.1 188.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv35" d="M 510.04 412.95 L 510.04 368.39" stroke-width="1" zvalue="43"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="38@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 510.04 412.95 L 510.04 368.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv35" d="M 510.45 342.59 L 510.45 308.81" stroke-width="1" zvalue="47"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="41@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 510.45 342.59 L 510.45 308.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv35" d="M 511.59 287.36 L 511.59 265" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="3@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 511.59 287.36 L 511.59 265" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv35" d="M 572.25 326.95 L 510.45 326.95" stroke-width="1" zvalue="51"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="42" MaxPinNum="2"/>
   </metadata>
  <path d="M 572.25 326.95 L 510.45 326.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv10" d="M 570.63 539.97 L 510 539.97" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="50" MaxPinNum="2"/>
   </metadata>
  <path d="M 570.63 539.97 L 510 539.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv10" d="M 510 515.5 L 510 564.51" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@1" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 510 515.5 L 510 564.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv10" d="M 508.34 625.65 L 508.34 666" stroke-width="1" zvalue="57"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@1" LinkObjectIDznd="2@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 508.34 625.65 L 508.34 666" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv10" d="M 274.16 698.51 L 274.16 666" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@0" LinkObjectIDznd="2@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 274.16 698.51 L 274.16 666" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv10" d="M 273 979.35 L 273 825.81" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="67@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 273 979.35 L 273 825.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv10" d="M 274.59 804.36 L 274.59 759.65" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="64@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 274.59 804.36 L 274.59 759.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv10" d="M 321.56 903.81 L 321.56 937" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@1" LinkObjectIDznd="56@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 321.56 903.81 L 321.56 937" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv10" d="M 321.59 882.36 L 321.59 860 L 273 860" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="68" MaxPinNum="2"/>
   </metadata>
  <path d="M 321.59 882.36 L 321.59 860 L 273 860" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 491.16 700.51 L 491.16 666" stroke-width="1" zvalue="100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="2@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 491.16 700.51 L 491.16 666" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv10" d="M 490 981.35 L 490 827.81" stroke-width="1" zvalue="102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="109@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 490 981.35 L 490 827.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="kv10" d="M 491.59 806.36 L 491.59 761.65" stroke-width="1" zvalue="104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="111@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 491.59 806.36 L 491.59 761.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv10" d="M 538.56 905.81 L 538.56 937" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@1" LinkObjectIDznd="56@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 538.56 905.81 L 538.56 937" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv10" d="M 538.59 884.36 L 538.59 862 L 490 862" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="108" MaxPinNum="2"/>
   </metadata>
  <path d="M 538.59 884.36 L 538.59 862 L 490 862" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv10" d="M 725.16 701.51 L 725.16 666" stroke-width="1" zvalue="114"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@0" LinkObjectIDznd="2@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 725.16 701.51 L 725.16 666" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv10" d="M 724 982.35 L 724 828.81" stroke-width="1" zvalue="116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="122@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 724 982.35 L 724 828.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv10" d="M 725.59 807.36 L 725.59 762.65" stroke-width="1" zvalue="118"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@0" LinkObjectIDznd="124@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 725.59 807.36 L 725.59 762.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv10" d="M 772.56 906.81 L 772.56 937" stroke-width="1" zvalue="121"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@1" LinkObjectIDznd="56@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.56 906.81 L 772.56 937" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv10" d="M 772.59 885.36 L 772.59 863 L 724 863" stroke-width="1" zvalue="122"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="121" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.59 885.36 L 772.59 863 L 724 863" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="21">
   <use class="kv35" height="20" transform="rotate(0,364,164) scale(1.5,1.35) translate(-118.833,-39.0185)" width="10" x="356.5" xlink:href="#Breaker:开关_0" y="150.5" zvalue="21"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="031断路器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,364,164) scale(1.5,1.35) translate(-118.833,-39.0185)" width="10" x="356.5" y="150.5"/></g>
  <g id="38">
   <use class="kv35" height="20" transform="rotate(0,510.5,355.5) scale(1.5,1.35) translate(-167.667,-88.6667)" width="10" x="503" xlink:href="#Breaker:开关_0" y="342" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="301"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,510.5,355.5) scale(1.5,1.35) translate(-167.667,-88.6667)" width="10" x="503" y="342"/></g>
  <g id="49">
   <use class="kv10" height="20" transform="rotate(0,508.34,595.5) scale(2.03197,3.35) translate(-253.009,-394.239)" width="10" x="498.1803278688525" xlink:href="#Breaker:手车开关_0" y="562" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="050"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,508.34,595.5) scale(2.03197,3.35) translate(-253.009,-394.239)" width="10" x="498.1803278688525" y="562"/></g>
  <g id="64">
   <use class="kv10" height="20" transform="rotate(0,274.16,729.5) scale(2.03197,3.35) translate(-134.077,-488.239)" width="10" x="264.0000000000001" xlink:href="#Breaker:手车开关_0" y="696" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="051"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,274.16,729.5) scale(2.03197,3.35) translate(-134.077,-488.239)" width="10" x="264.0000000000001" y="696"/></g>
  <g id="111">
   <use class="kv10" height="20" transform="rotate(0,491.16,731.5) scale(2.03197,3.35) translate(-244.284,-489.642)" width="10" x="481" xlink:href="#Breaker:手车开关_0" y="698" zvalue="98"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="052"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,491.16,731.5) scale(2.03197,3.35) translate(-244.284,-489.642)" width="10" x="481" y="698"/></g>
  <g id="124">
   <use class="kv10" height="20" transform="rotate(0,725.16,732.5) scale(2.03197,3.35) translate(-363.124,-490.343)" width="10" x="715" xlink:href="#Breaker:手车开关_0" y="699" zvalue="112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="053"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,725.16,732.5) scale(2.03197,3.35) translate(-363.124,-490.343)" width="10" x="715" y="699"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="24">
   <use class="kv35" height="20" transform="rotate(270,445,92) scale(1,1) translate(0,0)" width="10" x="440" xlink:href="#GroundDisconnector:地刀_0" y="82" zvalue="24"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="03167接地开关"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,445,92) scale(1,1) translate(0,0)" width="10" x="440" y="82"/></g>
  <g id="26">
   <use class="kv35" height="20" transform="rotate(270,449,139) scale(1,1) translate(0,0)" width="10" x="444" xlink:href="#GroundDisconnector:地刀_0" y="129" zvalue="27"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="03160"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,449,139) scale(1,1) translate(0,0)" width="10" x="444" y="129"/></g>
  <g id="28">
   <use class="kv35" height="20" transform="rotate(270,447,189) scale(1,1) translate(0,0)" width="10" x="442" xlink:href="#GroundDisconnector:地刀_0" y="179" zvalue="30"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="03117"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,447,189) scale(1,1) translate(0,0)" width="10" x="442" y="179"/></g>
  <g id="44">
   <use class="kv35" height="20" transform="rotate(270,582,327) scale(1,1) translate(0,0)" width="10" x="577" xlink:href="#GroundDisconnector:地刀_0" y="317" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="30117"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,582,327) scale(1,1) translate(0,0)" width="10" x="577" y="317"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="36">
   <g id="360">
    <use class="kv35" height="30" transform="rotate(0,510,464) scale(3.75,3.66667) translate(-341,-297.455)" width="24" x="465" xlink:href="#PowerTransformer2:可调两卷变_0" y="409" zvalue="39"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="35"/>
    </metadata>
   </g>
   <g id="361">
    <use class="kv10" height="30" transform="rotate(0,510,464) scale(3.75,3.66667) translate(-341,-297.455)" width="24" x="465" xlink:href="#PowerTransformer2:可调两卷变_1" y="409" zvalue="39"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,510,464) scale(3.75,3.66667) translate(-341,-297.455)" width="24" x="465" y="409"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="47">
   <use class="kv10" height="26" transform="rotate(270,583,540) scale(1,1) translate(0,0)" width="12" x="577" xlink:href="#Accessory:避雷器1_0" y="527" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,583,540) scale(1,1) translate(0,0)" width="12" x="577" y="527"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="57">
   <use class="kv10" height="30" transform="rotate(0,273,996) scale(1.25,-1.23333) translate(-53.1,-1800.07)" width="12" x="265.5" xlink:href="#EnergyConsumer:负荷_0" y="977.5" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="051负荷"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,273,996) scale(1.25,-1.23333) translate(-53.1,-1800.07)" width="12" x="265.5" y="977.5"/></g>
  <g id="112">
   <use class="kv10" height="30" transform="rotate(0,490,998) scale(1.25,-1.23333) translate(-96.5,-1803.69)" width="12" x="482.5" xlink:href="#EnergyConsumer:负荷_0" y="979.5" zvalue="96"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="052负荷"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,490,998) scale(1.25,-1.23333) translate(-96.5,-1803.69)" width="12" x="482.5" y="979.5"/></g>
  <g id="125">
   <use class="kv10" height="30" transform="rotate(0,724,999) scale(1.25,-1.23333) translate(-143.3,-1805.5)" width="12" x="716.5" xlink:href="#EnergyConsumer:负荷_0" y="980.5" zvalue="110"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="053负荷"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,724,999) scale(1.25,-1.23333) translate(-143.3,-1805.5)" width="12" x="716.5" y="980.5"/></g>
 </g>
</svg>