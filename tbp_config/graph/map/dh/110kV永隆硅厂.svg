<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549588066306" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="Accessory:电缆1_0" viewBox="0,0,12,7">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.25"/>
   <path d="M 1.16667 0.25 L 10.8333 0.25 L 6 6.88889 L 1.16667 0.25 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Compensator:西郊变电容_0" viewBox="0,0,24,40">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="2.449999999999999"/>
   <rect fill-opacity="0" height="5.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12,23) scale(1,1) translate(0,0)" width="3" x="10.5" y="20.17"/>
   <path d="M 12 17.85 L 17.85 17.85 L 17.85 22.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="6.5" y1="33.10833333333333" y2="33.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.471296296296289" x2="6.471296296296289" y1="30.88611111111111" y2="33.12685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.50833333333333" x2="6.50833333333333" y1="20.10833333333333" y2="17.90462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="29.60833333333333" y2="33.12685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="22.60833333333333" y2="28.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="17.79351851851851" y2="22.525"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.94090594744122" x2="6.94090594744122" y1="36.94166666666666" y2="34.94166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.416666666666666" x2="12.00833333333334" y1="17.85833333333333" y2="17.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="33.10833333333333" y2="37.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.09166666666667" x2="17.09166666666667" y1="34.94166666666666" y2="36.94166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.758333333333324" x2="2.758333333333324" y1="19.85833333333333" y2="31.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.08333333333333" x2="7.000000000000002" y1="37.10833333333333" y2="37.10833333333333"/>
   <path d="M 6.50833 23.7072 A 2.96392 1.81747 180 0 1 6.50833 20.0723" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.50833 27.3421 A 2.96392 1.81747 180 0 1 6.50833 23.7072" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.50833 30.8771 A 2.96392 1.81747 180 0 1 6.50833 27.2421" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="29.60833333333333" y2="29.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="28.60833333333333" y2="28.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="12" y1="2.25" y2="4.916666666666668"/>
   <path d="M 7.26667 9.85 A 4.91667 4.75 -450 1 0 12.0167 4.93333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 7.33333 9.83333 L 12 9.91667 L 12 18" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="20.56666666666668" y2="21.08015580397416"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.28450340888154" x2="18.47116270499357" y1="26.7156109584975" y2="26.7156109584975"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510778" x2="17.14262023217246" y1="23.75922956383932" y2="22.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.50700202690252" x2="18.32283029297954" y1="27.0857461490052" y2="27.0857461490052"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="25.36667381975841" y2="26.33114037330986"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.98783858485353" x2="18.76782752902158" y1="26.34547576798984" y2="26.34547576798984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="21.08015580397418" y2="23.73243882624067"/>
   <rect fill-opacity="0" height="4.29" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,17.83,23.22) scale(1,1) translate(0,0)" width="2.33" x="16.67" y="21.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510781" x2="18.53565505804314" y1="23.75922956383932" y2="22.95550743587977"/>
  </symbol>
  <symbol id="Accessory:线路PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="17.90667828048159" xlink:href="#terminal" y="33.53457520218116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.5" x2="30.5" y1="19" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.58333333333334" x2="32.58333333333334" y1="10.75" y2="10.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.5" x2="18" y1="23" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.93916361389984" x2="17.93916361389984" y1="27.04012345679012" y2="18.96704491718448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.93916361389984" x2="17.93916361389984" y1="30.21630546659505" y2="33.51790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.91963465760507" x2="17.91963465760507" y1="15.99560215515698" y2="9.314303947281834"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.01963465760507" x2="18.01963465760507" y1="19.00831495554599" y2="19.00831495554599"/>
   <ellipse cx="26.87" cy="16.85" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.5565174886495" x2="21.39655620359946" y1="18.92498162221265" y2="18.92498162221265"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.01963465760507" x2="18.01963465760507" y1="30.15122531314907" y2="30.15122531314907"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.63985082198282" x2="21.47988953693279" y1="15.91306207843401" y2="15.91306207843401"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.63985082198283" x2="21.47988953693279" y1="30.15122531314905" y2="30.15122531314905"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.63985082198283" x2="21.47988953693279" y1="27.05597243603708" y2="27.05597243603708"/>
   <ellipse cx="30.46" cy="10.68" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="15.72318415531615" x2="20" y1="9.24639541176734" y2="9.24639541176734"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="16.47318415531615" x2="19.08333333333333" y1="7.99639541176734" y2="7.99639541176734"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="16.97318415531615" x2="18.41666666666666" y1="6.496395411767342" y2="6.496395411767342"/>
   <ellipse cx="34.12" cy="16.85" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.75" x2="28.75" y1="16.75" y2="16.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.58333333333334" x2="36.58333333333334" y1="16.66666666666667" y2="16.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.633333333333335" x2="8.833333333333337" y1="13.29999999999999" y2="17.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.633333333333331" x2="6.250000000000004" y1="13.29999999999999" y2="16.91666666666666"/>
   <rect fill-opacity="0" height="12.12" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,7.62,14.61) scale(1,-1) translate(0,-840.33)" width="6.08" x="4.58" y="8.550000000000001"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.383333333333324" x2="7.383333333333324" y1="8.549999999999994" y2="4.949999999999994"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.633333333333324" x2="7.633333333333324" y1="23" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.75" x2="17.83333333333333" y1="22.98597664515375" y2="22.98597664515375"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.512285139234804" x2="5.235469294550956" y1="4.80515846663106" y2="4.80515846663106"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.67895180590147" x2="6.06880262788429" y1="3.43015846663106" y2="3.43015846663106"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.833333333333332" x2="6.914421100452429" y1="2.05515846663106" y2="2.05515846663106"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器PT（德宏变）_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="31.00905664884499" xlink:href="#terminal" y="3.977945066432934"/>
   <path d="M 10 25 L 4 25 L 4 32" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 5.11667 17.3167 L 5.11667 21.3167 L 8.11667 19.3167 L 5.11667 17.3167" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,31.02,18.92) scale(1,1) translate(0,0)" width="5.87" x="28.08" y="11.92"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31" x2="31" y1="12" y2="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="31" y1="8" y2="8"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31" x2="31" y1="4" y2="12"/>
   <path d="M 31 20 L 32 18" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.75" x2="22.16666666666667" y1="18.54585360194742" y2="18.54585360194742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.15990464876007" x2="22.15990464876007" y1="14.7557301451573" y2="22.82880868476295"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.08490464876007" x2="22.08490464876007" y1="11.65160994962663" y2="7.974999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.17943360505483" x2="22.17943360505483" y1="25.80025144679045" y2="32.48154965466559"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.07943360505483" x2="22.07943360505483" y1="22.78753864640143" y2="22.78753864640143"/>
   <ellipse cx="13.48" cy="11.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.54255077401041" x2="18.70251205906045" y1="22.87087197973477" y2="22.87087197973477"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.07943360505483" x2="22.07943360505483" y1="11.64462828879835" y2="11.64462828879835"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.45921744067708" x2="18.61917872572711" y1="11.64462828879838" y2="11.64462828879838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.45921744067709" x2="18.61917872572711" y1="25.88279152351342" y2="25.88279152351342"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.45921744067708" x2="18.61917872572711" y1="14.73988116591035" y2="14.73988116591035"/>
   <ellipse cx="9.890000000000001" cy="25.37" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.29255077401042" x2="20.01573492932657" y1="32.54945819018009" y2="32.54945819018009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.45921744067708" x2="20.84906826265991" y1="33.79945819018008" y2="33.79945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="22.87588410734375" x2="21.43240159599324" y1="35.29945819018008" y2="35.29945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="11.73744029990987" y2="10.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="14.21252026861409" y2="11.73744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="11.73744029990987" y2="10.49990031555776"/>
   <ellipse cx="6.48" cy="11.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.48" cy="18.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="18.73744029990987" y2="17.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="18.73744029990987" y2="17.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659907" x2="4.049068262659912" y1="11.73744029990987" y2="10.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.44906826265991" x2="6.44906826265991" y1="14.21252026861409" y2="11.73744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="21.21252026861409" y2="18.73744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659912" x2="8.849068262659907" y1="11.73744029990987" y2="10.49990031555776"/>
   <ellipse cx="6.48" cy="18.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.98240159599324" x2="9.98240159599324" y1="27.74585360194743" y2="25.27077363324322"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982401595993235" x2="7.582401595993243" y1="25.2707736332432" y2="24.0332336488911"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982401595993244" x2="12.38240159599323" y1="25.2707736332432" y2="24.0332336488911"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.042550774010419" x2="1.765734929326573" y1="32.04945819018009" y2="32.04945819018009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.209217440677085" x2="2.599068262659905" y1="33.29945819018008" y2="33.29945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.625884107343754" x2="3.182401595993237" y1="34.79945819018008" y2="34.79945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.92943360505483" x2="30.92943360505483" y1="25.80025144679045" y2="32.48154965466559"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.04255077401042" x2="28.76573492932657" y1="32.54945819018009" y2="32.54945819018009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.20921744067708" x2="29.59906826265991" y1="33.79945819018008" y2="33.79945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.62588410734375" x2="30.18240159599324" y1="35.29945819018008" y2="35.29945819018008"/>
   <path d="M 31 20 L 30 18" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:动力变_0" viewBox="0,0,20,30">
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.77838478538902" x2="6.77838478538902" y1="3.797799292079558" y2="6.396787392096492"/>
   <use terminal-index="0" type="1" x="6.75" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="2" x="6.85" xlink:href="#terminal" y="6.65"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.184837295917547" x2="6.778384785389" y1="8.995775492113406" y2="6.396787392096474"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.371932274860468" x2="6.778384785389015" y1="8.995775492113406" y2="6.396787392096474"/>
   <ellipse cx="6.78" cy="6.8" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:动力变_1" viewBox="0,0,20,30">
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.184837295917547" x2="6.778384785389" y1="20.49577549211341" y2="17.89678739209647"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.77838478538902" x2="6.77838478538902" y1="15.29779929207956" y2="17.89678739209649"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.371932274860468" x2="6.778384785389015" y1="20.49577549211341" y2="17.89678739209647"/>
   <ellipse cx="6.78" cy="17.55" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="18" y1="18" y2="18"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="18" y1="18" y2="22"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.5" x2="18.5" y1="24" y2="24"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="19" y1="23" y2="23"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="20" y1="22" y2="22"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="6.75" y1="24" y2="25.66666666666667"/>
   <use terminal-index="2" type="1" x="6.833333333333334" xlink:href="#terminal" y="25.91666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV永隆硅厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="277.25" x="57.75" xlink:href="logo.png" y="36.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.375,66.75) scale(1,1) translate(0,0)" writing-mode="lr" x="196.38" xml:space="preserve" y="70.25" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,191,63.9403) scale(1,1) translate(0,0)" writing-mode="lr" x="191" xml:space="preserve" y="72.94" zvalue="3">110kV永隆硅厂</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="10" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.9688,187.25) scale(1,1) translate(0,0)" width="73.56" x="50.19" y="175.25" zvalue="22"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.9688,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.97" xml:space="preserve" y="191.75" zvalue="22">信号一览</text>
  <line fill="none" id="28" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="391.75" x2="391.75" y1="4.75" y2="1034.75" zvalue="4"/>
  <line fill="none" id="26" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75000000000045" x2="384.75" y1="140.6204926140824" y2="140.6204926140824" zvalue="6"/>
  <line fill="none" id="24" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="926" y2="926"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="965.1632999999999" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="926" y2="926"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="965.1632999999999" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="993.0816" y2="1021"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,946) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="952" zvalue="10">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.8889,978.889) scale(1,1) translate(0,0)" writing-mode="lr" x="52.89" xml:space="preserve" y="984.89" zvalue="11">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.889,978.889) scale(1,1) translate(0,0)" writing-mode="lr" x="234.89" xml:space="preserve" y="984.89" zvalue="12">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.8889,1006.89) scale(1,1) translate(0,0)" writing-mode="lr" x="51.89" xml:space="preserve" y="1012.89" zvalue="13">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.889,1006.89) scale(1,1) translate(0,0)" writing-mode="lr" x="233.89" xml:space="preserve" y="1012.89" zvalue="14">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,560.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="565" zvalue="16">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,545.125,375.25) scale(1,1) translate(0,0)" writing-mode="lr" x="545.13" xml:space="preserve" y="379.75" zvalue="33">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,553.208,448.999) scale(1,1) translate(0,0)" writing-mode="lr" x="553.21" xml:space="preserve" y="453.5" zvalue="35">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,547.299,520.476) scale(1,1) translate(0,0)" writing-mode="lr" x="547.3" xml:space="preserve" y="524.98" zvalue="37">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,543.77,582.73) scale(1,1) translate(0,0)" writing-mode="lr" x="543.77" xml:space="preserve" y="587.23" zvalue="39">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,643.202,463.122) scale(1,1) translate(0,0)" writing-mode="lr" x="643.2" xml:space="preserve" y="467.62" zvalue="43">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,643.202,532.293) scale(1,1) translate(0,0)" writing-mode="lr" x="643.2" xml:space="preserve" y="536.79" zvalue="45">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,643.202,590.075) scale(1,1) translate(0,-2.58271e-13)" writing-mode="lr" x="643.2" xml:space="preserve" y="594.58" zvalue="53">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,474.775,880.376) scale(1,1) translate(0,0)" writing-mode="lr" x="474.77" xml:space="preserve" y="884.88" zvalue="57">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,470.071,827.604) scale(1,1) translate(0,0)" writing-mode="lr" x="470.07" xml:space="preserve" y="832.1" zvalue="58">041</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,662.303,763.788) scale(1,1) translate(0,0)" writing-mode="lr" x="662.3" xml:space="preserve" y="768.29" zvalue="63">1010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,490.719,734.25) scale(1,1) translate(0,0)" writing-mode="lr" x="490.72" xml:space="preserve" y="738.75" zvalue="64">#1炉变15MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,529.512,787.906) scale(1,1) translate(0,0)" writing-mode="lr" x="529.51" xml:space="preserve" y="792.41" zvalue="73">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,577.719,878.909) scale(1,1) translate(0,0)" writing-mode="lr" x="577.72" xml:space="preserve" y="883.41" zvalue="79">#1电炉</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,503,1031.53) scale(1,1) translate(0,0)" writing-mode="lr" x="503" xml:space="preserve" y="1036.03" zvalue="89">#1电容器组9.6Mvar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,801.208,448.999) scale(1,1) translate(0,0)" writing-mode="lr" x="801.21" xml:space="preserve" y="453.5" zvalue="98">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,796.852,520.476) scale(1,1) translate(0,0)" writing-mode="lr" x="796.85" xml:space="preserve" y="524.98" zvalue="100">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,792.807,582.73) scale(1,1) translate(0,0)" writing-mode="lr" x="792.8099999999999" xml:space="preserve" y="587.23" zvalue="102">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,891.202,463.122) scale(1,1) translate(0,0)" writing-mode="lr" x="891.2" xml:space="preserve" y="467.62" zvalue="104">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,892.202,532.293) scale(1,1) translate(0,0)" writing-mode="lr" x="892.2" xml:space="preserve" y="536.79" zvalue="106">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,891.202,590.075) scale(1,1) translate(0,-2.58271e-13)" writing-mode="lr" x="891.2" xml:space="preserve" y="594.58" zvalue="113">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,723.684,882.285) scale(1,1) translate(0,0)" writing-mode="lr" x="723.6799999999999" xml:space="preserve" y="886.78" zvalue="115">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,721.642,829.209) scale(1,1) translate(0,0)" writing-mode="lr" x="721.64" xml:space="preserve" y="833.71" zvalue="117">042</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,910.303,763.788) scale(1,1) translate(-1.98686e-13,0)" writing-mode="lr" x="910.3" xml:space="preserve" y="768.29" zvalue="120">1020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,738.719,734.25) scale(1,1) translate(2.31055e-13,0)" writing-mode="lr" x="738.72" xml:space="preserve" y="738.75" zvalue="121">#2炉变15MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,777.512,787.906) scale(1,1) translate(0,0)" writing-mode="lr" x="777.51" xml:space="preserve" y="792.41" zvalue="128">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,825.719,878.909) scale(1,1) translate(0,0)" writing-mode="lr" x="825.72" xml:space="preserve" y="883.41" zvalue="132">#2电炉</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,751,1031.53) scale(1,1) translate(0,0)" writing-mode="lr" x="751" xml:space="preserve" y="1036.03" zvalue="137">10kV#2电容器组9.6Mvar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1057.21,448.999) scale(1,1) translate(0,0)" writing-mode="lr" x="1057.21" xml:space="preserve" y="453.5" zvalue="146">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1052.21,519.567) scale(1,1) translate(0,0)" writing-mode="lr" x="1052.21" xml:space="preserve" y="524.0700000000001" zvalue="148">103</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1049.21,582.73) scale(1,1) translate(0,0)" writing-mode="lr" x="1049.21" xml:space="preserve" y="587.23" zvalue="150">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1147.2,463.122) scale(1,1) translate(0,0)" writing-mode="lr" x="1147.2" xml:space="preserve" y="467.62" zvalue="152">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1147.2,532.293) scale(1,1) translate(0,0)" writing-mode="lr" x="1147.2" xml:space="preserve" y="536.79" zvalue="154">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,978.775,880.376) scale(1,1) translate(0,0)" writing-mode="lr" x="978.77" xml:space="preserve" y="884.88" zvalue="163">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,977.642,829.209) scale(1,1) translate(0,0)" writing-mode="lr" x="977.64" xml:space="preserve" y="833.71" zvalue="165">043</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1166.3,763.788) scale(1,1) translate(0,0)" writing-mode="lr" x="1166.3" xml:space="preserve" y="768.29" zvalue="168">1030</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,994.719,734.25) scale(1,1) translate(0,0)" writing-mode="lr" x="994.72" xml:space="preserve" y="738.75" zvalue="169">#3炉变15MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1033.51,787.906) scale(1,1) translate(0,0)" writing-mode="lr" x="1033.51" xml:space="preserve" y="792.41" zvalue="176">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1081.72,878.909) scale(1,1) translate(3.53124e-13,0)" writing-mode="lr" x="1081.72" xml:space="preserve" y="883.41" zvalue="180">#3电炉</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1007,1031.53) scale(1,1) translate(0,0)" writing-mode="lr" x="1007" xml:space="preserve" y="1036.03" zvalue="185">10kV#3电容器组9.6Mvar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1315.21,447.999) scale(1,1) translate(0,0)" writing-mode="lr" x="1315.21" xml:space="preserve" y="452.5" zvalue="194">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="189" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1310.85,520.476) scale(1,1) translate(0,0)" writing-mode="lr" x="1310.85" xml:space="preserve" y="524.98" zvalue="196">104</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1309.21,581.619) scale(1,1) translate(0,0)" writing-mode="lr" x="1309.21" xml:space="preserve" y="586.12" zvalue="198">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1405.2,462.122) scale(1,1) translate(0,0)" writing-mode="lr" x="1405.2" xml:space="preserve" y="466.62" zvalue="200">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="186" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1407.2,532.293) scale(1,1) translate(0,0)" writing-mode="lr" x="1407.2" xml:space="preserve" y="536.79" zvalue="202">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1238.77,880.376) scale(1,1) translate(0,0)" writing-mode="lr" x="1238.77" xml:space="preserve" y="884.88" zvalue="211">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1237.64,829.209) scale(1,1) translate(0,0)" writing-mode="lr" x="1237.64" xml:space="preserve" y="833.71" zvalue="213">044</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1426.3,763.788) scale(1,1) translate(0,0)" writing-mode="lr" x="1426.3" xml:space="preserve" y="768.29" zvalue="216">1040</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1254.72,734.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1254.72" xml:space="preserve" y="738.75" zvalue="217">#4炉变15MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1293.51,787.906) scale(1,1) translate(0,0)" writing-mode="lr" x="1293.51" xml:space="preserve" y="792.41" zvalue="224">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1341.72,878.909) scale(1,1) translate(0,0)" writing-mode="lr" x="1341.72" xml:space="preserve" y="883.41" zvalue="228">#4电炉</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="178" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1267,1031.53) scale(1,1) translate(0,0)" writing-mode="lr" x="1267" xml:space="preserve" y="1036.03" zvalue="233">10kV#4电容器组9.6Mvar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="237" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1541.21,448.999) scale(1,1) translate(0,0)" writing-mode="lr" x="1541.21" xml:space="preserve" y="453.5" zvalue="242">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1532.41,516.032) scale(1,1) translate(0,0)" writing-mode="lr" x="1532.41" xml:space="preserve" y="520.53" zvalue="244">105</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1631.2,462.011) scale(1,1) translate(0,0)" writing-mode="lr" x="1631.2" xml:space="preserve" y="466.51" zvalue="248">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1495.92,774.876) scale(1,1) translate(0,0)" writing-mode="lr" x="1495.92" xml:space="preserve" y="779.38" zvalue="264">1050</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="276" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1025.21,232.999) scale(1,1) translate(0,0)" writing-mode="lr" x="1025.21" xml:space="preserve" y="237.5" zvalue="290">1416</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="275" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1020.85,303.567) scale(1,1) translate(0,0)" writing-mode="lr" x="1020.85" xml:space="preserve" y="308.07" zvalue="292">141</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="274" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1017.21,366.73) scale(1,1) translate(0,0)" writing-mode="lr" x="1017.21" xml:space="preserve" y="371.23" zvalue="294">1411</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="273" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,979.202,295.122) scale(1,1) translate(0,0)" writing-mode="lr" x="979.2" xml:space="preserve" y="299.62" zvalue="296">14160</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="272" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,979.202,360.515) scale(1,1) translate(0,0)" writing-mode="lr" x="979.2" xml:space="preserve" y="365.02" zvalue="298">14117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,979.202,220.118) scale(1,1) translate(0,0)" writing-mode="lr" x="979.2" xml:space="preserve" y="224.62" zvalue="306">14167</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1054.5,118.5) scale(1,1) translate(1.10023e-13,0)" writing-mode="lr" x="1054.5" xml:space="preserve" y="123" zvalue="309">110kV永隆硅厂Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1341.41,299.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1341.41" xml:space="preserve" y="303.72" zvalue="317">19010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1341.41,215.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1341.41" xml:space="preserve" y="219.72" zvalue="320">19017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1437.45,278.352) scale(1,1) translate(0,0)" writing-mode="lr" x="1437.45" xml:space="preserve" y="282.85" zvalue="323">1901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1770.16,374) scale(1,1) translate(0,0)" writing-mode="lr" x="1770.16" xml:space="preserve" y="378.5" zvalue="350">T接10kV风平线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1773.23,736.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1773.23" xml:space="preserve" y="740.89" zvalue="375">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1533.2,796) scale(1,1) translate(0,0)" writing-mode="lr" x="1533.2" xml:space="preserve" y="800.5" zvalue="385">#1动力变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233,947.5) scale(1,1) translate(0,0)" writing-mode="lr" x="233" xml:space="preserve" y="952" zvalue="397">YongLong-01-2012</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="399">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="241" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="400">通道</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="236" y2="236"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="262" y2="262"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="14" y1="236" y2="262"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="236" y2="262"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="236" y2="236"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="262" y2="262"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="236" y2="262"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376" x2="376" y1="236" y2="262"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="262" y2="262"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="286.25" y2="286.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="14" y1="262" y2="286.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="262" y2="286.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="262" y2="262"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="286.25" y2="286.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="262" y2="286.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376" x2="376" y1="262" y2="286.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="286.25" y2="286.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="309" y2="309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="14" y1="286.25" y2="309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="286.25" y2="309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="286.25" y2="286.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="309" y2="309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="286.25" y2="309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376" x2="376" y1="286.25" y2="309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="309" y2="309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="331.75" y2="331.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="14" y1="309" y2="331.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="309" y2="331.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="309" y2="309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="331.75" y2="331.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="309" y2="331.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376" x2="376" y1="309" y2="331.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="331.75" y2="331.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="354.5" y2="354.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="14" y1="331.75" y2="354.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="331.75" y2="354.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="331.75" y2="331.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="354.5" y2="354.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="331.75" y2="354.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376" x2="376" y1="331.75" y2="354.5"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="316" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,250) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="254.5" zvalue="405">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="315" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,250) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="254.5" zvalue="406">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="314" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,299.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="303.75" zvalue="407">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="309" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,276) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="280.5" zvalue="412">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="308" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,276) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="280.5" zvalue="413">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,147.889,1006.89) scale(1,1) translate(0,0)" writing-mode="lr" x="147.89" xml:space="preserve" y="1012.89" zvalue="428">杨立超</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="73.56" x="50.19" y="175.25" zvalue="22"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="31">
   <path class="kv110" d="M 505 403.75 L 1640 403.75" stroke-width="6" zvalue="32"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674246000644" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674246000644"/></metadata>
  <path d="M 505 403.75 L 1640 403.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="33">
   <use class="kv110" height="30" transform="rotate(0,577.201,448.99) scale(1.04717,0.767927) translate(-25.6481,132.207)" width="15" x="569.3475211502216" xlink:href="#Disconnector:刀闸_0" y="437.470904747293" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449830584325" ObjectName="#1炉变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449830584325"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,577.201,448.99) scale(1.04717,0.767927) translate(-25.6481,132.207)" width="15" x="569.3475211502216" y="437.470904747293"/></g>
  <g id="38">
   <use class="kv110" height="30" transform="rotate(0,575.763,582.721) scale(1.04717,0.767927) translate(-25.5834,172.621)" width="15" x="567.9096492965623" xlink:href="#Disconnector:刀闸_0" y="571.201672292114" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449830649861" ObjectName="#1炉变110kV侧1016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449830649861"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,575.763,582.721) scale(1.04717,0.767927) translate(-25.5834,172.621)" width="15" x="567.9096492965623" y="571.201672292114"/></g>
  <g id="52">
   <use class="kv10" height="30" transform="rotate(0,497.731,882.09) scale(-1.04717,-0.767927) translate(-972.686,-2034.23)" width="15" x="489.877237477244" xlink:href="#Disconnector:刀闸_0" y="870.5711304201926" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449831108613" ObjectName="10kV#1电容器组0411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449831108613"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,497.731,882.09) scale(-1.04717,-0.767927) translate(-972.686,-2034.23)" width="15" x="489.877237477244" y="870.5711304201926"/></g>
  <g id="70">
   <use class="kv10" height="30" transform="rotate(270,530.421,760.721) scale(-1.04717,-0.767927) translate(-1036.59,-1754.82)" width="15" x="522.5668926496578" xlink:href="#Disconnector:刀闸_0" y="749.2016722921138" zvalue="72"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449861058566" ObjectName="10kV#1电容器组0416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449861058566"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,530.421,760.721) scale(-1.04717,-0.767927) translate(-1036.59,-1754.82)" width="15" x="522.5668926496578" y="749.2016722921138"/></g>
  <g id="130">
   <use class="kv110" height="30" transform="rotate(0,825.201,448.99) scale(1.04717,0.767927) translate(-36.8201,132.207)" width="15" x="817.3475211502216" xlink:href="#Disconnector:刀闸_0" y="437.470904747293" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449832484870" ObjectName="#2炉变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449832484870"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,825.201,448.99) scale(1.04717,0.767927) translate(-36.8201,132.207)" width="15" x="817.3475211502216" y="437.470904747293"/></g>
  <g id="128">
   <use class="kv110" height="30" transform="rotate(0,824.8,582.721) scale(1.04717,0.767927) translate(-36.8021,172.621)" width="15" x="816.9465432717142" xlink:href="#Disconnector:刀闸_0" y="571.201672292114" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449832419333" ObjectName="#2炉变110kV侧1026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449832419333"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,824.8,582.721) scale(1.04717,0.767927) translate(-36.8021,172.621)" width="15" x="816.9465432717142" y="571.201672292114"/></g>
  <g id="119">
   <use class="kv10" height="30" transform="rotate(0,746.64,883.999) scale(-1.04717,-0.767927) translate(-1459.29,-2038.63)" width="15" x="738.7863283863348" xlink:href="#Disconnector:刀闸_0" y="872.4802213292836" zvalue="114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449831960582" ObjectName="10kV#2电容器组0421隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449831960582"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,746.64,883.999) scale(-1.04717,-0.767927) translate(-1459.29,-2038.63)" width="15" x="738.7863283863348" y="872.4802213292836"/></g>
  <g id="110">
   <use class="kv10" height="30" transform="rotate(270,778.421,760.721) scale(-1.04717,-0.767927) translate(-1521.42,-1754.82)" width="15" x="770.5668926496578" xlink:href="#Disconnector:刀闸_0" y="749.2016722921138" zvalue="127"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449831698437" ObjectName="10kV#2电容器组0426隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449831698437"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,778.421,760.721) scale(-1.04717,-0.767927) translate(-1521.42,-1754.82)" width="15" x="770.5668926496578" y="749.2016722921138"/></g>
  <g id="177">
   <use class="kv110" height="30" transform="rotate(0,1081.2,448.99) scale(1.04717,0.767927) translate(-48.3525,132.207)" width="15" x="1073.347521150222" xlink:href="#Disconnector:刀闸_0" y="437.470904747293" zvalue="145"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449833336838" ObjectName="#3炉变110kV侧1031隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449833336838"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1081.2,448.99) scale(1.04717,0.767927) translate(-48.3525,132.207)" width="15" x="1073.347521150222" y="437.470904747293"/></g>
  <g id="175">
   <use class="kv110" height="30" transform="rotate(0,1081.2,582.721) scale(1.04717,0.767927) translate(-48.3525,172.621)" width="15" x="1073.347521150222" xlink:href="#Disconnector:刀闸_0" y="571.201672292114" zvalue="149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449833271302" ObjectName="#3炉变110kV侧1036隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449833271302"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1081.2,582.721) scale(1.04717,0.767927) translate(-48.3525,172.621)" width="15" x="1073.347521150222" y="571.201672292114"/></g>
  <g id="166">
   <use class="kv10" height="30" transform="rotate(0,1001.73,882.09) scale(-1.04717,-0.767927) translate(-1957.98,-2034.23)" width="15" x="993.877237477244" xlink:href="#Disconnector:刀闸_0" y="870.5711304201926" zvalue="162"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449832943621" ObjectName="10kV#3电容器组0431隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449832943621"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1001.73,882.09) scale(-1.04717,-0.767927) translate(-1957.98,-2034.23)" width="15" x="993.877237477244" y="870.5711304201926"/></g>
  <g id="157">
   <use class="kv10" height="30" transform="rotate(270,1034.42,760.721) scale(-1.04717,-0.767927) translate(-2021.89,-1754.82)" width="15" x="1026.566892649658" xlink:href="#Disconnector:刀闸_0" y="749.2016722921138" zvalue="175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449832681478" ObjectName="10kV#3电容器组0436隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449832681478"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1034.42,760.721) scale(-1.04717,-0.767927) translate(-2021.89,-1754.82)" width="15" x="1026.566892649658" y="749.2016722921138"/></g>
  <g id="224">
   <use class="kv110" height="30" transform="rotate(0,1339.2,447.99) scale(1.04717,0.767927) translate(-59.975,131.904)" width="15" x="1331.347521150222" xlink:href="#Disconnector:刀闸_0" y="436.470904747293" zvalue="193"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449834188806" ObjectName="#4炉变110kV侧1041隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449834188806"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1339.2,447.99) scale(1.04717,0.767927) translate(-59.975,131.904)" width="15" x="1331.347521150222" y="436.470904747293"/></g>
  <g id="222">
   <use class="kv110" height="30" transform="rotate(0,1341.2,581.609) scale(1.04717,0.767927) translate(-60.0651,172.285)" width="15" x="1333.347521150222" xlink:href="#Disconnector:刀闸_0" y="570.090561181003" zvalue="197"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449834123270" ObjectName="#4炉变110kV侧1046隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449834123270"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1341.2,581.609) scale(1.04717,0.767927) translate(-60.0651,172.285)" width="15" x="1333.347521150222" y="570.090561181003"/></g>
  <g id="213">
   <use class="kv10" height="30" transform="rotate(0,1261.73,882.09) scale(-1.04717,-0.767927) translate(-2466.27,-2034.23)" width="15" x="1253.877237477244" xlink:href="#Disconnector:刀闸_0" y="870.5711304201926" zvalue="210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449833795590" ObjectName="10kV#4电容器组0441隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449833795590"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1261.73,882.09) scale(-1.04717,-0.767927) translate(-2466.27,-2034.23)" width="15" x="1253.877237477244" y="870.5711304201926"/></g>
  <g id="204">
   <use class="kv10" height="30" transform="rotate(270,1294.42,760.721) scale(-1.04717,-0.767927) translate(-2530.18,-1754.82)" width="15" x="1286.566892649658" xlink:href="#Disconnector:刀闸_0" y="749.2016722921138" zvalue="223"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449833533446" ObjectName="10kV#4电容器组0446隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449833533446"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1294.42,760.721) scale(-1.04717,-0.767927) translate(-2530.18,-1754.82)" width="15" x="1286.566892649658" y="749.2016722921138"/></g>
  <g id="271">
   <use class="kv110" height="30" transform="rotate(0,1565.2,448.99) scale(1.04717,0.767927) translate(-70.1559,132.207)" width="15" x="1557.347521150222" xlink:href="#Disconnector:刀闸_0" y="437.470904747293" zvalue="241"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449834582022" ObjectName="#1动力变110kV侧1051隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449834582022"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1565.2,448.99) scale(1.04717,0.767927) translate(-70.1559,132.207)" width="15" x="1557.347521150222" y="437.470904747293"/></g>
  <g id="286">
   <use class="kv110" height="30" transform="rotate(0,1049.2,232.99) scale(1.04717,0.767927) translate(-46.9109,66.93)" width="15" x="1041.347521150222" xlink:href="#Disconnector:刀闸_0" y="221.470904747293" zvalue="289"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449834975238" ObjectName="110kV永隆硅厂Ⅰ回线1416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449834975238"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1049.2,232.99) scale(1.04717,0.767927) translate(-46.9109,66.93)" width="15" x="1041.347521150222" y="221.470904747293"/></g>
  <g id="284">
   <use class="kv110" height="30" transform="rotate(0,1049.2,366.721) scale(1.04717,0.767927) translate(-46.9109,107.344)" width="15" x="1041.347521150222" xlink:href="#Disconnector:刀闸_0" y="355.201672292114" zvalue="293"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449834909702" ObjectName="110kV永隆硅厂Ⅰ回线1411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449834909702"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1049.2,366.721) scale(1.04717,0.767927) translate(-46.9109,107.344)" width="15" x="1041.347521150222" y="355.201672292114"/></g>
  <g id="160">
   <use class="kv110" height="30" transform="rotate(0,1413.1,278.826) scale(1.04717,0.767927) translate(-63.3038,80.782)" width="15" x="1405.242257992327" xlink:href="#Disconnector:刀闸_0" y="267.306935450009" zvalue="322"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449835565062" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449835565062"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1413.1,278.826) scale(1.04717,0.767927) translate(-63.3038,80.782)" width="15" x="1405.242257992327" y="267.306935450009"/></g>
  <g id="252">
   <use class="kv10" height="30" transform="rotate(0,1769.92,501.968) scale(1.42857,1.42857) translate(-527.762,-144.162)" width="15" x="1759.206349206349" xlink:href="#Disconnector:令克_0" y="480.5396825396825" zvalue="369"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449835958277" ObjectName="令克"/>
   <cge:TPSR_Ref TObjectID="6192449835958277"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1769.92,501.968) scale(1.42857,1.42857) translate(-527.762,-144.162)" width="15" x="1759.206349206349" y="480.5396825396825"/></g>
 </g>
 <g id="BreakerClass">
  <g id="35">
   <use class="kv110" height="20" transform="rotate(0,575.76,518.905) scale(1.42665,1.28399) translate(-170.053,-111.93)" width="10" x="568.6269889975015" xlink:href="#Breaker:开关_0" y="506.0655656862315" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924519788549" ObjectName="#1炉变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473924519788549"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,575.76,518.905) scale(1.42665,1.28399) translate(-170.053,-111.93)" width="10" x="568.6269889975015" y="506.0655656862315"/></g>
  <g id="54">
   <use class="kv10" height="20" transform="rotate(0,497.565,828.67) scale(1.15385,1.03846) translate(-65.5728,-30.3069)" width="10" x="491.7962037962041" xlink:href="#Breaker:开关_0" y="818.2857142857142" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924519854085" ObjectName="10kV#1电容器组041断路器"/>
   <cge:TPSR_Ref TObjectID="6473924519854085"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,497.565,828.67) scale(1.15385,1.03846) translate(-65.5728,-30.3069)" width="10" x="491.7962037962041" y="818.2857142857142"/></g>
  <g id="129">
   <use class="kv110" height="20" transform="rotate(0,825.313,518.905) scale(1.42665,1.28399) translate(-244.684,-111.93)" width="10" x="818.1796581826827" xlink:href="#Breaker:开关_0" y="506.0655656862315" zvalue="99"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924519985157" ObjectName="#2炉变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473924519985157"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,825.313,518.905) scale(1.42665,1.28399) translate(-244.684,-111.93)" width="10" x="818.1796581826827" y="506.0655656862315"/></g>
  <g id="118">
   <use class="kv10" height="20" transform="rotate(0,745.565,828.67) scale(1.15385,1.03846) translate(-98.6395,-30.3069)" width="10" x="739.7962037962041" xlink:href="#Breaker:开关_0" y="818.2857142857142" zvalue="116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924519919621" ObjectName="10kV#2电容器组042断路器"/>
   <cge:TPSR_Ref TObjectID="6473924519919621"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,745.565,828.67) scale(1.15385,1.03846) translate(-98.6395,-30.3069)" width="10" x="739.7962037962041" y="818.2857142857142"/></g>
  <g id="176">
   <use class="kv110" height="20" transform="rotate(0,1080.67,517.996) scale(1.42665,1.28399) translate(-321.051,-111.729)" width="10" x="1073.536079906592" xlink:href="#Breaker:开关_0" y="505.1564747771405" zvalue="147"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924520116229" ObjectName="#3炉变110kV侧103断路器"/>
   <cge:TPSR_Ref TObjectID="6473924520116229"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1080.67,517.996) scale(1.42665,1.28399) translate(-321.051,-111.729)" width="10" x="1073.536079906592" y="505.1564747771405"/></g>
  <g id="165">
   <use class="kv10" height="20" transform="rotate(0,1001.57,828.67) scale(1.15385,1.03846) translate(-132.773,-30.3069)" width="10" x="995.7962037962041" xlink:href="#Breaker:开关_0" y="818.2857142857142" zvalue="164"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924520050693" ObjectName="10kV#3电容器组043断路器"/>
   <cge:TPSR_Ref TObjectID="6473924520050693"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1001.57,828.67) scale(1.15385,1.03846) translate(-132.773,-30.3069)" width="10" x="995.7962037962041" y="818.2857142857142"/></g>
  <g id="223">
   <use class="kv110" height="20" transform="rotate(0,1339.31,518.905) scale(1.42665,1.28399) translate(-398.4,-111.93)" width="10" x="1332.179658182683" xlink:href="#Breaker:开关_0" y="506.0655656862315" zvalue="195"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924520247301" ObjectName="#4炉变110kV侧104断路器"/>
   <cge:TPSR_Ref TObjectID="6473924520247301"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1339.31,518.905) scale(1.42665,1.28399) translate(-398.4,-111.93)" width="10" x="1332.179658182683" y="506.0655656862315"/></g>
  <g id="212">
   <use class="kv10" height="20" transform="rotate(0,1261.57,828.67) scale(1.15385,1.03846) translate(-167.439,-30.3069)" width="10" x="1255.796203796204" xlink:href="#Breaker:开关_0" y="818.2857142857142" zvalue="212"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924520181765" ObjectName="10kV#4电容器组044断路器"/>
   <cge:TPSR_Ref TObjectID="6473924520181765"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1261.57,828.67) scale(1.15385,1.03846) translate(-167.439,-30.3069)" width="10" x="1255.796203796204" y="818.2857142857142"/></g>
  <g id="270">
   <use class="kv110" height="20" transform="rotate(0,1565.31,517.794) scale(1.42665,1.28399) translate(-465.988,-111.684)" width="10" x="1558.179658182683" xlink:href="#Breaker:开关_0" y="504.9544545751203" zvalue="243"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924520312837" ObjectName="#1动力变110kV侧105断路器"/>
   <cge:TPSR_Ref TObjectID="6473924520312837"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1565.31,517.794) scale(1.42665,1.28399) translate(-465.988,-111.684)" width="10" x="1558.179658182683" y="504.9544545751203"/></g>
  <g id="285">
   <use class="kv110" height="20" transform="rotate(0,1049.31,301.996) scale(1.42665,1.28399) translate(-311.673,-63.9547)" width="10" x="1042.179658182683" xlink:href="#Breaker:开关_0" y="289.1564747771404" zvalue="291"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924520378373" ObjectName="110kV永隆硅厂Ⅰ回线141断路器"/>
   <cge:TPSR_Ref TObjectID="6473924520378373"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1049.31,301.996) scale(1.42665,1.28399) translate(-311.673,-63.9547)" width="10" x="1042.179658182683" y="289.1564747771404"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="41">
   <use class="kv110" height="20" transform="rotate(90,641.617,488.052) scale(-1.51312,-1.51312) translate(-1063.09,-805.469)" width="10" x="634.0513084644722" xlink:href="#GroundDisconnector:地刀_0" y="472.921086661114" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449830780933" ObjectName="#1炉变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449830780933"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,641.617,488.052) scale(-1.51312,-1.51312) translate(-1063.09,-805.469)" width="10" x="634.0513084644722" y="472.921086661114"/></g>
  <g id="44">
   <use class="kv110" height="20" transform="rotate(90,641.617,557.223) scale(-1.51312,-1.51312) translate(-1063.09,-920.354)" width="10" x="634.0513084644722" xlink:href="#GroundDisconnector:地刀_0" y="542.0921733222284" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449830912005" ObjectName="#1炉变110kV侧10160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449830912005"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,641.617,557.223) scale(-1.51312,-1.51312) translate(-1063.09,-920.354)" width="10" x="634.0513084644722" y="542.0921733222284"/></g>
  <g id="40">
   <use class="kv110" height="20" transform="rotate(90,641.617,607.006) scale(-1.51312,-1.51312) translate(-1063.09,-1003.04)" width="10" x="634.0513084644722" xlink:href="#GroundDisconnector:地刀_0" y="591.8746657607504" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449831043077" ObjectName="#1炉变110kV侧10167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449831043077"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,641.617,607.006) scale(-1.51312,-1.51312) translate(-1063.09,-1003.04)" width="10" x="634.0513084644722" y="591.8746657607504"/></g>
  <g id="60">
   <use class="kv110" height="40" transform="rotate(0,659.97,726.455) scale(1.11667,-1.11667) translate(-66.6187,-1374.68)" width="40" x="637.6363636363635" xlink:href="#GroundDisconnector:中性点地刀12_0" y="704.1212121212121" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449851490310" ObjectName="#1炉变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449851490310"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,659.97,726.455) scale(1.11667,-1.11667) translate(-66.6187,-1374.68)" width="40" x="637.6363636363635" y="704.1212121212121"/></g>
  <g id="127">
   <use class="kv110" height="20" transform="rotate(90,889.617,488.052) scale(-1.51312,-1.51312) translate(-1474.99,-805.469)" width="10" x="882.0513084644722" xlink:href="#GroundDisconnector:地刀_0" y="472.921086661114" zvalue="103"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449832353797" ObjectName="#2炉变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449832353797"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,889.617,488.052) scale(-1.51312,-1.51312) translate(-1474.99,-805.469)" width="10" x="882.0513084644722" y="472.921086661114"/></g>
  <g id="126">
   <use class="kv110" height="20" transform="rotate(90,890.617,557.223) scale(-1.51312,-1.51312) translate(-1476.65,-920.354)" width="10" x="883.0513084644722" xlink:href="#GroundDisconnector:地刀_0" y="542.0921733222284" zvalue="105"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449832222726" ObjectName="#2炉变110kV侧10260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449832222726"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,890.617,557.223) scale(-1.51312,-1.51312) translate(-1476.65,-920.354)" width="10" x="883.0513084644722" y="542.0921733222284"/></g>
  <g id="120">
   <use class="kv110" height="20" transform="rotate(90,889.617,607.006) scale(-1.51312,-1.51312) translate(-1474.99,-1003.04)" width="10" x="882.0513084644722" xlink:href="#GroundDisconnector:地刀_0" y="591.8746657607504" zvalue="112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449832091653" ObjectName="#2炉变110kV侧10267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449832091653"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,889.617,607.006) scale(-1.51312,-1.51312) translate(-1474.99,-1003.04)" width="10" x="882.0513084644722" y="591.8746657607504"/></g>
  <g id="117">
   <use class="kv110" height="40" transform="rotate(0,907.97,726.455) scale(1.11667,-1.11667) translate(-92.5292,-1374.68)" width="40" x="885.6363636363635" xlink:href="#GroundDisconnector:中性点地刀12_0" y="704.1212121212121" zvalue="118"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449831895045" ObjectName="#2炉变110kV侧1020中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449831895045"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,907.97,726.455) scale(1.11667,-1.11667) translate(-92.5292,-1374.68)" width="40" x="885.6363636363635" y="704.1212121212121"/></g>
  <g id="174">
   <use class="kv110" height="20" transform="rotate(90,1145.62,488.052) scale(-1.51312,-1.51312) translate(-1900.17,-805.469)" width="10" x="1138.051308464472" xlink:href="#GroundDisconnector:地刀_0" y="472.921086661114" zvalue="151"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449833205766" ObjectName="#3炉变110kV侧10317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449833205766"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1145.62,488.052) scale(-1.51312,-1.51312) translate(-1900.17,-805.469)" width="10" x="1138.051308464472" y="472.921086661114"/></g>
  <g id="173">
   <use class="kv110" height="20" transform="rotate(90,1145.62,557.223) scale(-1.51312,-1.51312) translate(-1900.17,-920.354)" width="10" x="1138.051308464472" xlink:href="#GroundDisconnector:地刀_0" y="542.0921733222284" zvalue="153"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449833074693" ObjectName="#3炉变110kV侧10367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449833074693"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1145.62,557.223) scale(-1.51312,-1.51312) translate(-1900.17,-920.354)" width="10" x="1138.051308464472" y="542.0921733222284"/></g>
  <g id="164">
   <use class="kv110" height="40" transform="rotate(0,1163.97,726.455) scale(1.11667,-1.11667) translate(-119.275,-1374.68)" width="40" x="1141.636363636364" xlink:href="#GroundDisconnector:中性点地刀12_0" y="704.1212121212121" zvalue="166"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449832878085" ObjectName="#3炉变110kV侧1030中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449832878085"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1163.97,726.455) scale(1.11667,-1.11667) translate(-119.275,-1374.68)" width="40" x="1141.636363636364" y="704.1212121212121"/></g>
  <g id="221">
   <use class="kv110" height="20" transform="rotate(90,1403.62,487.052) scale(-1.51312,-1.51312) translate(-2328.68,-803.808)" width="10" x="1396.051308464472" xlink:href="#GroundDisconnector:地刀_0" y="471.921086661114" zvalue="199"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449834057734" ObjectName="#4炉变110kV侧10417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449834057734"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1403.62,487.052) scale(-1.51312,-1.51312) translate(-2328.68,-803.808)" width="10" x="1396.051308464472" y="471.921086661114"/></g>
  <g id="220">
   <use class="kv110" height="20" transform="rotate(90,1405.62,557.223) scale(-1.51312,-1.51312) translate(-2332.01,-920.354)" width="10" x="1398.051308464472" xlink:href="#GroundDisconnector:地刀_0" y="542.0921733222284" zvalue="201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449833926662" ObjectName="#4炉变110kV侧10467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449833926662"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1405.62,557.223) scale(-1.51312,-1.51312) translate(-2332.01,-920.354)" width="10" x="1398.051308464472" y="542.0921733222284"/></g>
  <g id="211">
   <use class="kv110" height="40" transform="rotate(0,1423.97,726.455) scale(1.11667,-1.11667) translate(-146.44,-1374.68)" width="40" x="1401.636363636364" xlink:href="#GroundDisconnector:中性点地刀12_0" y="704.1212121212121" zvalue="214"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449833730054" ObjectName="#4炉变110kV侧1040中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449833730054"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1423.97,726.455) scale(1.11667,-1.11667) translate(-146.44,-1374.68)" width="40" x="1401.636363636364" y="704.1212121212121"/></g>
  <g id="268">
   <use class="kv110" height="20" transform="rotate(90,1629.62,486.941) scale(-1.51312,-1.51312) translate(-2704.04,-803.623)" width="10" x="1622.051308464472" xlink:href="#GroundDisconnector:地刀_0" y="471.8099755500029" zvalue="247"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449834516486" ObjectName="#1动力变110kV侧10517接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449834516486"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1629.62,486.941) scale(-1.51312,-1.51312) translate(-2704.04,-803.623)" width="10" x="1622.051308464472" y="471.8099755500029"/></g>
  <g id="258">
   <use class="kv110" height="40" transform="rotate(0,1493.59,737.542) scale(-1.11667,-1.11667) translate(-2828.79,-1395.69)" width="40" x="1471.251748251748" xlink:href="#GroundDisconnector:中性点地刀12_0" y="715.2091242091242" zvalue="262"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449834385414" ObjectName="#1动力变110kV侧1050中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449834385414"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1493.59,737.542) scale(-1.11667,-1.11667) translate(-2828.79,-1395.69)" width="40" x="1471.251748251748" y="715.2091242091242"/></g>
  <g id="283">
   <use class="kv110" height="20" transform="rotate(270,977.617,268.052) scale(1.51312,-1.51312) translate(-328.957,-440.073)" width="10" x="970.0513084764384" xlink:href="#GroundDisconnector:地刀_0" y="252.921086661114" zvalue="295"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449834844166" ObjectName="110kV永隆硅厂Ⅰ回线14160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449834844166"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,977.617,268.052) scale(1.51312,-1.51312) translate(-328.957,-440.073)" width="10" x="970.0513084764384" y="252.921086661114"/></g>
  <g id="282">
   <use class="kv110" height="20" transform="rotate(270,977.617,333.446) scale(1.51312,-1.51312) translate(-328.957,-548.684)" width="10" x="970.0513084677834" xlink:href="#GroundDisconnector:地刀_0" y="318.3143955444506" zvalue="297"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449834713094" ObjectName="110kV永隆硅厂Ⅰ回线14117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449834713094"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,977.617,333.446) scale(1.51312,-1.51312) translate(-328.957,-548.684)" width="10" x="970.0513084677834" y="318.3143955444506"/></g>
  <g id="287">
   <use class="kv110" height="20" transform="rotate(270,977.617,196.052) scale(1.51312,-1.51312) translate(-328.957,-320.49)" width="10" x="970.0513084764384" xlink:href="#GroundDisconnector:地刀_0" y="180.921086661114" zvalue="305"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449835106310" ObjectName="110kV永隆硅厂Ⅰ回线14167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449835106310"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,977.617,196.052) scale(1.51312,-1.51312) translate(-328.957,-320.49)" width="10" x="970.0513084764384" y="180.921086661114"/></g>
  <g id="50">
   <use class="kv110" height="20" transform="rotate(270,1342.46,322.919) scale(1.51312,-1.51312) translate(-452.679,-531.201)" width="10" x="1334.893413730941" xlink:href="#GroundDisconnector:地刀_0" y="307.7880797549769" zvalue="316"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449835368454" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449835368454"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1342.46,322.919) scale(1.51312,-1.51312) translate(-452.679,-531.201)" width="10" x="1334.893413730941" y="307.7880797549769"/></g>
  <g id="138">
   <use class="kv110" height="20" transform="rotate(270,1342.46,238.919) scale(1.51312,-1.51312) translate(-452.679,-391.687)" width="10" x="1334.893413730941" xlink:href="#GroundDisconnector:地刀_0" y="223.7880797549769" zvalue="319"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449837268998" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449837268998"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1342.46,238.919) scale(1.51312,-1.51312) translate(-452.679,-391.687)" width="10" x="1334.893413730941" y="223.7880797549769"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="45">
   <path class="kv110" d="M 577.29 437.85 L 577.29 403.75" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="31@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 577.29 437.85 L 577.29 403.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv110" d="M 577.27 460.31 L 577.27 506.62" stroke-width="1" zvalue="47"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@1" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 577.27 460.31 L 577.27 506.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv110" d="M 575.86 531.17 L 575.86 571.58" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@1" LinkObjectIDznd="38@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 575.86 531.17 L 575.86 571.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv110" d="M 626.86 487.98 L 577.27 487.98" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="46" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.86 487.98 L 577.27 487.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv110" d="M 626.86 557.15 L 575.86 557.42" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="47" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.86 557.15 L 575.86 557.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv110" d="M 575.83 594.04 L 575.83 678.09" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@1" LinkObjectIDznd="62@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 575.83 594.04 L 575.83 678.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv110" d="M 626.86 606.93 L 575.83 606.93" stroke-width="1" zvalue="67"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="65" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.86 606.93 L 575.83 606.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv110" d="M 628.25 646.5 L 575.83 646.5" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="65" MaxPinNum="2"/>
   </metadata>
  <path d="M 628.25 646.5 L 575.83 646.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv110" d="M 576.84 701.72 L 645.01 701.72 L 645.01 712.83" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@2" LinkObjectIDznd="60@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 576.84 701.72 L 645.01 701.72 L 645.01 712.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv10" d="M 519.1 760.78 L 497.53 760.78 L 497.53 818.74" stroke-width="1" zvalue="77"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@1" LinkObjectIDznd="54@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 519.1 760.78 L 497.53 760.78 L 497.53 818.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv10" d="M 497.64 838.59 L 497.67 870.77" stroke-width="1" zvalue="85"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@1" LinkObjectIDznd="52@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 497.64 838.59 L 497.67 870.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv10" d="M 497.64 893.23 L 497.64 920.48" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="57@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 497.64 893.23 L 497.64 920.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv110" d="M 825.29 437.85 L 825.29 403.75" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="31@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.29 437.85 L 825.29 403.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv110" d="M 825.27 460.31 L 825.27 506.62" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@1" LinkObjectIDznd="129@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.27 460.31 L 825.27 506.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv110" d="M 825.41 531.17 L 825.41 571.58" stroke-width="1" zvalue="109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@1" LinkObjectIDznd="128@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.41 531.17 L 825.41 571.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv110" d="M 874.86 487.98 L 825.27 487.98" stroke-width="1" zvalue="110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="124" MaxPinNum="2"/>
   </metadata>
  <path d="M 874.86 487.98 L 825.27 487.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv110" d="M 875.86 557.15 L 825.41 557.42" stroke-width="1" zvalue="111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="123" MaxPinNum="2"/>
   </metadata>
  <path d="M 875.86 557.15 L 825.41 557.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv110" d="M 824.86 594.04 L 824.86 678.09" stroke-width="1" zvalue="123"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@1" LinkObjectIDznd="116@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 824.86 594.04 L 824.86 678.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv110" d="M 874.86 606.93 L 824.86 606.93" stroke-width="1" zvalue="124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="114" MaxPinNum="2"/>
   </metadata>
  <path d="M 874.86 606.93 L 824.86 606.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv110" d="M 873.19 646.5 L 824.86 646.5" stroke-width="1" zvalue="125"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="114" MaxPinNum="2"/>
   </metadata>
  <path d="M 873.19 646.5 L 824.86 646.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv110" d="M 824.84 701.72 L 893.01 701.72 L 893.01 712.83" stroke-width="1" zvalue="126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@2" LinkObjectIDznd="117@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 824.84 701.72 L 893.01 701.72 L 893.01 712.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 789.56 760.81 L 824.81 760.81" stroke-width="1" zvalue="129"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="116@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 789.56 760.81 L 824.81 760.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv10" d="M 767.1 760.78 L 745.53 760.78 L 745.53 818.74" stroke-width="1" zvalue="130"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@1" LinkObjectIDznd="118@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 767.1 760.78 L 745.53 760.78 L 745.53 818.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv10" d="M 745.64 838.59 L 745.64 872.68" stroke-width="1" zvalue="134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@1" LinkObjectIDznd="119@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 745.64 838.59 L 745.64 872.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv10" d="M 746.55 895.14 L 746.55 920.48" stroke-width="1" zvalue="136"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="104@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 746.55 895.14 L 746.55 920.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv110" d="M 1081.29 437.85 L 1081.29 403.75" stroke-width="1" zvalue="155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@0" LinkObjectIDznd="31@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1081.29 437.85 L 1081.29 403.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv110" d="M 1081.27 460.31 L 1081.27 505.71" stroke-width="1" zvalue="156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@1" LinkObjectIDznd="176@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1081.27 460.31 L 1081.27 505.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv110" d="M 1080.76 530.26 L 1080.76 571.58" stroke-width="1" zvalue="157"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@1" LinkObjectIDznd="175@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.76 530.26 L 1080.76 571.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv110" d="M 1130.86 487.98 L 1081.27 487.98" stroke-width="1" zvalue="158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="171" MaxPinNum="2"/>
   </metadata>
  <path d="M 1130.86 487.98 L 1081.27 487.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv110" d="M 1130.86 557.15 L 1080.76 557.42" stroke-width="1" zvalue="159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="173@0" LinkObjectIDznd="170" MaxPinNum="2"/>
   </metadata>
  <path d="M 1130.86 557.15 L 1080.76 557.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="kv110" d="M 1081.27 594.04 L 1081.27 678.09" stroke-width="1" zvalue="171"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@1" LinkObjectIDznd="163@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1081.27 594.04 L 1081.27 678.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="kv110" d="M 1132.25 646.5 L 1081.27 646.5" stroke-width="1" zvalue="173"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162@0" LinkObjectIDznd="161" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.25 646.5 L 1081.27 646.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv110" d="M 1080.84 701.72 L 1149.01 701.72 L 1149.01 712.83" stroke-width="1" zvalue="174"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@2" LinkObjectIDznd="164@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.84 701.72 L 1149.01 701.72 L 1149.01 712.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv10" d="M 1045.56 760.81 L 1080.81 760.81" stroke-width="1" zvalue="177"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="163@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1045.56 760.81 L 1080.81 760.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv10" d="M 1023.1 760.78 L 1001.53 760.78 L 1001.53 818.74" stroke-width="1" zvalue="178"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@1" LinkObjectIDznd="165@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.1 760.78 L 1001.53 760.78 L 1001.53 818.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv10" d="M 1001.64 838.59 L 1001.67 870.77" stroke-width="1" zvalue="182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@1" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1001.64 838.59 L 1001.67 870.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv10" d="M 1001.64 893.23 L 1001.64 920.48" stroke-width="1" zvalue="184"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="151@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1001.64 893.23 L 1001.64 920.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="219">
   <path class="kv110" d="M 1339.29 436.85 L 1339.29 403.75" stroke-width="1" zvalue="203"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="224@0" LinkObjectIDznd="31@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1339.29 436.85 L 1339.29 403.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="kv110" d="M 1339.27 459.31 L 1339.27 506.62" stroke-width="1" zvalue="204"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="224@1" LinkObjectIDznd="223@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1339.27 459.31 L 1339.27 506.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv110" d="M 1339.41 531.17 L 1339.41 570.47" stroke-width="1" zvalue="205"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@1" LinkObjectIDznd="222@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1339.41 531.17 L 1339.41 570.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv110" d="M 1388.86 486.98 L 1339.27 486.98" stroke-width="1" zvalue="206"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="218" MaxPinNum="2"/>
   </metadata>
  <path d="M 1388.86 486.98 L 1339.27 486.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv110" d="M 1390.86 557.15 L 1339.41 557.43" stroke-width="1" zvalue="207"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@0" LinkObjectIDznd="217" MaxPinNum="2"/>
   </metadata>
  <path d="M 1390.86 557.15 L 1339.41 557.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv110" d="M 1341.27 592.93 L 1341.27 678.09" stroke-width="1" zvalue="219"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="222@1" LinkObjectIDznd="210@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1341.27 592.93 L 1341.27 678.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="206">
   <path class="kv110" d="M 1392.25 646.5 L 1341.27 646.5" stroke-width="1" zvalue="221"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="209@0" LinkObjectIDznd="208" MaxPinNum="2"/>
   </metadata>
  <path d="M 1392.25 646.5 L 1341.27 646.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="205">
   <path class="kv110" d="M 1340.84 701.72 L 1409.01 701.72 L 1409.01 712.83" stroke-width="1" zvalue="222"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="210@2" LinkObjectIDznd="211@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1340.84 701.72 L 1409.01 701.72 L 1409.01 712.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="203">
   <path class="kv10" d="M 1305.56 760.81 L 1340.81 760.81" stroke-width="1" zvalue="225"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="204@0" LinkObjectIDznd="210@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1305.56 760.81 L 1340.81 760.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv10" d="M 1283.1 760.78 L 1261.53 760.78 L 1261.53 818.74" stroke-width="1" zvalue="226"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="204@1" LinkObjectIDznd="212@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1283.1 760.78 L 1261.53 760.78 L 1261.53 818.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="kv10" d="M 1261.64 838.59 L 1261.67 870.77" stroke-width="1" zvalue="230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@1" LinkObjectIDznd="213@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1261.64 838.59 L 1261.67 870.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv10" d="M 1261.64 893.23 L 1261.64 920.48" stroke-width="1" zvalue="232"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="213@0" LinkObjectIDznd="198@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1261.64 893.23 L 1261.64 920.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="kv110" d="M 1565.29 437.85 L 1565.29 403.75" stroke-width="1" zvalue="251"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@0" LinkObjectIDznd="31@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1565.29 437.85 L 1565.29 403.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="265">
   <path class="kv110" d="M 1565.27 460.31 L 1565.27 505.51" stroke-width="1" zvalue="252"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@1" LinkObjectIDznd="270@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1565.27 460.31 L 1565.27 505.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="263">
   <path class="kv110" d="M 1614.86 486.87 L 1565.27 486.87" stroke-width="1" zvalue="254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="265" MaxPinNum="2"/>
   </metadata>
  <path d="M 1614.86 486.87 L 1565.27 486.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="253">
   <path class="kv110" d="M 1616.25 646.5 L 1565.41 646.5" stroke-width="1" zvalue="269"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="256@0" LinkObjectIDznd="249" MaxPinNum="2"/>
   </metadata>
  <path d="M 1616.25 646.5 L 1565.41 646.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="281">
   <path class="kv110" d="M 1049.27 244.31 L 1049.27 289.71" stroke-width="1" zvalue="299"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="286@1" LinkObjectIDznd="285@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1049.27 244.31 L 1049.27 289.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="280">
   <path class="kv110" d="M 1049.41 314.26 L 1049.41 355.58" stroke-width="1" zvalue="300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="285@1" LinkObjectIDznd="284@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1049.41 314.26 L 1049.41 355.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="293">
   <path class="kv110" d="M 1048.5 188.25 L 1048.5 221.85" stroke-width="1" zvalue="309"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="291@0" LinkObjectIDznd="286@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1048.5 188.25 L 1048.5 221.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="294">
   <path class="kv110" d="M 992.37 195.98 L 1048.5 195.98" stroke-width="1" zvalue="310"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="287@0" LinkObjectIDznd="293" MaxPinNum="2"/>
   </metadata>
  <path d="M 992.37 195.98 L 1048.5 195.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="295">
   <path class="kv110" d="M 992.37 267.98 L 1049.27 267.98" stroke-width="1" zvalue="311"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="283@0" LinkObjectIDznd="281" MaxPinNum="2"/>
   </metadata>
  <path d="M 992.37 267.98 L 1049.27 267.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="297">
   <path class="kv110" d="M 1111.82 189.74 L 1111.82 195.98 L 1047 195.98" stroke-width="1" zvalue="313"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="290@0" LinkObjectIDznd="294" MaxPinNum="2"/>
   </metadata>
  <path d="M 1111.82 189.74 L 1111.82 195.98 L 1047 195.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="298">
   <path class="kv110" d="M 1049.27 378.04 L 1049.27 403.75" stroke-width="1" zvalue="314"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="284@1" LinkObjectIDznd="31@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1049.27 378.04 L 1049.27 403.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="207">
   <path class="kv110" d="M 1412.39 176.57 L 1412.39 267.69" stroke-width="1" zvalue="324"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@0" LinkObjectIDznd="160@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1412.39 176.57 L 1412.39 267.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv110" d="M 1413.16 290.15 L 1413.16 403.75" stroke-width="1" zvalue="325"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160@1" LinkObjectIDznd="31@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1413.16 290.15 L 1413.16 403.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv110" d="M 1357.21 322.84 L 1413.16 322.84" stroke-width="1" zvalue="326"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="214" MaxPinNum="2"/>
   </metadata>
  <path d="M 1357.21 322.84 L 1413.16 322.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv110" d="M 1357.21 238.84 L 1412.39 238.84" stroke-width="1" zvalue="327"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@0" LinkObjectIDznd="207" MaxPinNum="2"/>
   </metadata>
  <path d="M 1357.21 238.84 L 1412.39 238.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv10" d="M 576.81 760.08 L 576.81 821.9" stroke-width="1" zvalue="339"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@1" LinkObjectIDznd="78@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 576.81 760.08 L 576.81 821.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="246">
   <path class="kv10" d="M 824.81 760.81 L 824.81 821.9" stroke-width="1" zvalue="341"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109" LinkObjectIDznd="107@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 824.81 760.81 L 824.81 821.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="247">
   <path class="kv10" d="M 1080.81 760.81 L 1080.81 821.9" stroke-width="1" zvalue="342"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="156" LinkObjectIDznd="154@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.81 760.81 L 1080.81 821.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="248">
   <path class="kv10" d="M 1340.81 760.81 L 1340.81 821.9" stroke-width="1" zvalue="343"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203" LinkObjectIDznd="201@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1340.81 760.81 L 1340.81 821.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="249">
   <path class="kv110" d="M 1565.41 530.06 L 1565.41 707.39" stroke-width="1" zvalue="344"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="270@1" LinkObjectIDznd="51@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1565.41 530.06 L 1565.41 707.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="300">
   <path class="kv110" d="M 992.37 333.37 L 1049.41 333.37" stroke-width="1" zvalue="357"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="282@0" LinkObjectIDznd="280" MaxPinNum="2"/>
   </metadata>
  <path d="M 992.37 333.37 L 1049.41 333.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="301">
   <path class="kv10" d="M 541.56 760.81 L 576.81 760.81" stroke-width="1" zvalue="358"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="244" MaxPinNum="2"/>
   </metadata>
  <path d="M 541.56 760.81 L 576.81 760.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="255">
   <path class="kv10" d="M 1770.16 432.32 L 1770.16 483.04" stroke-width="1" zvalue="370"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="254@0" LinkObjectIDznd="252@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1770.16 432.32 L 1770.16 483.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="260">
   <path class="kv10" d="M 1769.8 519.47 L 1769.8 656.55" stroke-width="1" zvalue="371"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252@1" LinkObjectIDznd="262@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1769.8 519.47 L 1769.8 656.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="261">
   <path class="kv10" d="M 1724.75 586.5 L 1769.8 586.5" stroke-width="1" zvalue="372"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@0" LinkObjectIDznd="260" MaxPinNum="2"/>
   </metadata>
  <path d="M 1724.75 586.5 L 1769.8 586.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv110" d="M 1508.55 723.92 L 1565.69 723.92" stroke-width="1" zvalue="385"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="258@0" LinkObjectIDznd="51@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1508.55 723.92 L 1565.69 723.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="238">
   <path class="kv10" d="M 1565.64 777.81 L 1565.64 793.35" stroke-width="1" zvalue="390"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@2" LinkObjectIDznd="200@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1565.64 777.81 L 1565.64 793.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="62">
   <g id="620">
    <use class="kv110" height="50" transform="rotate(0,576.81,719) scale(1.66667,1.66667) translate(-220.724,-270.933)" width="30" x="551.8099999999999" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="677.33" zvalue="63"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874440572932" ObjectName="110"/>
    </metadata>
   </g>
   <g id="621">
    <use class="kv10" height="50" transform="rotate(0,576.81,719) scale(1.66667,1.66667) translate(-220.724,-270.933)" width="30" x="551.8099999999999" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="677.33" zvalue="63"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874440638468" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399452524547" ObjectName="#1炉变"/>
   <cge:TPSR_Ref TObjectID="6755399452524547"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,576.81,719) scale(1.66667,1.66667) translate(-220.724,-270.933)" width="30" x="551.8099999999999" y="677.33"/></g>
  <g id="116">
   <g id="1160">
    <use class="kv110" height="50" transform="rotate(0,824.81,719) scale(1.66667,1.66667) translate(-319.924,-270.933)" width="30" x="799.8099999999999" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="677.33" zvalue="119"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874440704004" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1161">
    <use class="kv10" height="50" transform="rotate(0,824.81,719) scale(1.66667,1.66667) translate(-319.924,-270.933)" width="30" x="799.8099999999999" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="677.33" zvalue="119"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874440769540" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399452590083" ObjectName="#2炉变"/>
   <cge:TPSR_Ref TObjectID="6755399452590083"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,824.81,719) scale(1.66667,1.66667) translate(-319.924,-270.933)" width="30" x="799.8099999999999" y="677.33"/></g>
  <g id="163">
   <g id="1630">
    <use class="kv110" height="50" transform="rotate(0,1080.81,719) scale(1.66667,1.66667) translate(-422.324,-270.933)" width="30" x="1055.81" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="677.33" zvalue="167"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874440835076" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1631">
    <use class="kv10" height="50" transform="rotate(0,1080.81,719) scale(1.66667,1.66667) translate(-422.324,-270.933)" width="30" x="1055.81" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="677.33" zvalue="167"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874440900612" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399452655619" ObjectName="#3炉变"/>
   <cge:TPSR_Ref TObjectID="6755399452655619"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1080.81,719) scale(1.66667,1.66667) translate(-422.324,-270.933)" width="30" x="1055.81" y="677.33"/></g>
  <g id="210">
   <g id="2100">
    <use class="kv110" height="50" transform="rotate(0,1340.81,719) scale(1.66667,1.66667) translate(-526.324,-270.933)" width="30" x="1315.81" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="677.33" zvalue="215"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874440966148" ObjectName="110"/>
    </metadata>
   </g>
   <g id="2101">
    <use class="kv10" height="50" transform="rotate(0,1340.81,719) scale(1.66667,1.66667) translate(-526.324,-270.933)" width="30" x="1315.81" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="677.33" zvalue="215"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874441031684" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399452721155" ObjectName="#4炉变"/>
   <cge:TPSR_Ref TObjectID="6755399452721155"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1340.81,719) scale(1.66667,1.66667) translate(-526.324,-270.933)" width="30" x="1315.81" y="677.33"/></g>
  <g id="51">
   <g id="510">
    <use class="kv110" height="30" transform="rotate(0,1574.41,747.563) scale(2.77085,2.77085) translate(-988.499,-451.204)" width="20" x="1546.7" xlink:href="#PowerTransformer2:动力变_0" y="706" zvalue="384"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874441359364" ObjectName="110"/>
    </metadata>
   </g>
   <g id="511">
    <use class="kv10" height="30" transform="rotate(0,1574.41,747.563) scale(2.77085,2.77085) translate(-988.499,-451.204)" width="20" x="1546.7" xlink:href="#PowerTransformer2:动力变_1" y="706" zvalue="384"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874441883652" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399452983299" ObjectName="#1动力变"/>
   <cge:TPSR_Ref TObjectID="6755399452983299"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1574.41,747.563) scale(2.77085,2.77085) translate(-988.499,-451.204)" width="20" x="1546.7" y="706"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="63">
   <use class="kv110" height="20" transform="rotate(270,640.5,646.5) scale(-1.75,1.75) translate(-999,-269.571)" width="20" x="623.0000000000001" xlink:href="#Accessory:线路PT3_0" y="629" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449831305221" ObjectName="#1PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,640.5,646.5) scale(-1.75,1.75) translate(-999,-269.571)" width="20" x="623.0000000000001" y="629"/></g>
  <g id="78">
   <use class="kv10" height="7" transform="rotate(0,576.81,839.7) scale(1.74242,5.47782) translate(-241.316,-670.737)" width="12" x="566.3549783549784" xlink:href="#Accessory:电缆1_0" y="820.5276292335117" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449831436293" ObjectName="#1电炉"/>
   </metadata>
  <rect fill="white" height="7" opacity="0" stroke="white" transform="rotate(0,576.81,839.7) scale(1.74242,5.47782) translate(-241.316,-670.737)" width="12" x="566.3549783549784" y="820.5276292335117"/></g>
  <g id="115">
   <use class="kv110" height="20" transform="rotate(270,888.5,646.5) scale(-1.75,1.75) translate(-1388.71,-269.571)" width="20" x="871.0000000000001" xlink:href="#Accessory:线路PT3_0" y="629" zvalue="122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449831763973" ObjectName="#2PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,888.5,646.5) scale(-1.75,1.75) translate(-1388.71,-269.571)" width="20" x="871.0000000000001" y="629"/></g>
  <g id="107">
   <use class="kv10" height="7" transform="rotate(0,824.81,839.7) scale(1.74242,5.47782) translate(-346.986,-670.737)" width="12" x="814.3549783549784" xlink:href="#Accessory:电缆1_0" y="820.5276292335117" zvalue="131"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449831632901" ObjectName="#2电炉"/>
   </metadata>
  <rect fill="white" height="7" opacity="0" stroke="white" transform="rotate(0,824.81,839.7) scale(1.74242,5.47782) translate(-346.986,-670.737)" width="12" x="814.3549783549784" y="820.5276292335117"/></g>
  <g id="162">
   <use class="kv110" height="20" transform="rotate(270,1144.5,646.5) scale(-1.75,1.75) translate(-1791,-269.571)" width="20" x="1127" xlink:href="#Accessory:线路PT3_0" y="629" zvalue="170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449832747014" ObjectName="#3PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1144.5,646.5) scale(-1.75,1.75) translate(-1791,-269.571)" width="20" x="1127" y="629"/></g>
  <g id="154">
   <use class="kv10" height="7" transform="rotate(0,1080.81,839.7) scale(1.74242,5.47782) translate(-456.064,-670.737)" width="12" x="1070.354978354978" xlink:href="#Accessory:电缆1_0" y="820.5276292335117" zvalue="179"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449832615942" ObjectName="#3电炉"/>
   </metadata>
  <rect fill="white" height="7" opacity="0" stroke="white" transform="rotate(0,1080.81,839.7) scale(1.74242,5.47782) translate(-456.064,-670.737)" width="12" x="1070.354978354978" y="820.5276292335117"/></g>
  <g id="209">
   <use class="kv110" height="20" transform="rotate(270,1404.5,646.5) scale(-1.75,1.75) translate(-2199.57,-269.571)" width="20" x="1387" xlink:href="#Accessory:线路PT3_0" y="629" zvalue="218"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449833598982" ObjectName="#4PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1404.5,646.5) scale(-1.75,1.75) translate(-2199.57,-269.571)" width="20" x="1387" y="629"/></g>
  <g id="201">
   <use class="kv10" height="7" transform="rotate(0,1340.81,839.7) scale(1.74242,5.47782) translate(-566.847,-670.737)" width="12" x="1330.354978354978" xlink:href="#Accessory:电缆1_0" y="820.5276292335117" zvalue="227"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449833467910" ObjectName="#4电炉"/>
   </metadata>
  <rect fill="white" height="7" opacity="0" stroke="white" transform="rotate(0,1340.81,839.7) scale(1.74242,5.47782) translate(-566.847,-670.737)" width="12" x="1330.354978354978" y="820.5276292335117"/></g>
  <g id="256">
   <use class="kv110" height="20" transform="rotate(270,1628.5,646.5) scale(-1.75,1.75) translate(-2551.57,-269.571)" width="20" x="1611" xlink:href="#Accessory:线路PT3_0" y="629" zvalue="266"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449834254342" ObjectName="#5PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1628.5,646.5) scale(-1.75,1.75) translate(-2551.57,-269.571)" width="20" x="1611" y="629"/></g>
  <g id="290">
   <use class="kv110" height="40" transform="rotate(0,1114.25,168.25) scale(1.1625,1.5875) translate(-152.505,-50.5157)" width="40" x="1091" xlink:href="#Accessory:线路PT_0" y="136.5000000000001" zvalue="307"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449835171846" ObjectName="线路PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1114.25,168.25) scale(1.1625,1.5875) translate(-152.505,-50.5157)" width="40" x="1091" y="136.5000000000001"/></g>
  <g id="185">
   <use class="kv110" height="40" transform="rotate(180,1430.62,147.184) scale(1.65625,1.83421) translate(-553.726,-50.2561)" width="40" x="1397.5" xlink:href="#Accessory:避雷器PT（德宏变）_0" y="110.5" zvalue="323"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449835630598" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,1430.62,147.184) scale(1.65625,1.83421) translate(-553.726,-50.2561)" width="40" x="1397.5" y="110.5"/></g>
  <g id="250">
   <use class="kv10" height="20" transform="rotate(90,1712.5,586.5) scale(1.75,1.75) translate(-726.429,-243.857)" width="20" x="1695" xlink:href="#Accessory:线路PT3_0" y="569" zvalue="346"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449835827206" ObjectName="#2站用变PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1712.5,586.5) scale(1.75,1.75) translate(-726.429,-243.857)" width="20" x="1695" y="569"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="57">
   <use class="kv10" height="40" transform="rotate(0,497.333,965.333) scale(2.55556,2.55556) translate(-284.058,-556.483)" width="24" x="466.6666666666665" xlink:href="#Compensator:西郊变电容_0" y="914.2222222222219" zvalue="88"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449831501829" ObjectName="10kV#1电容器组"/>
   <cge:TPSR_Ref TObjectID="6192449831501829"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,497.333,965.333) scale(2.55556,2.55556) translate(-284.058,-556.483)" width="24" x="466.6666666666665" y="914.2222222222219"/></g>
  <g id="104">
   <use class="kv10" height="40" transform="rotate(0,745.333,965.333) scale(2.55556,2.55556) translate(-435.014,-556.483)" width="24" x="714.6666666666665" xlink:href="#Compensator:西郊变电容_0" y="914.2222222222219" zvalue="135"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449831567365" ObjectName="10kV#2电容器组"/>
   <cge:TPSR_Ref TObjectID="6192449831567365"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,745.333,965.333) scale(2.55556,2.55556) translate(-435.014,-556.483)" width="24" x="714.6666666666665" y="914.2222222222219"/></g>
  <g id="151">
   <use class="kv10" height="40" transform="rotate(0,1001.33,965.333) scale(2.55556,2.55556) translate(-590.841,-556.483)" width="24" x="970.6666666666665" xlink:href="#Compensator:西郊变电容_0" y="914.2222222222219" zvalue="183"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449832550406" ObjectName="10kV#3电容器组"/>
   <cge:TPSR_Ref TObjectID="6192449832550406"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1001.33,965.333) scale(2.55556,2.55556) translate(-590.841,-556.483)" width="24" x="970.6666666666665" y="914.2222222222219"/></g>
  <g id="198">
   <use class="kv10" height="40" transform="rotate(0,1261.33,965.333) scale(2.55556,2.55556) translate(-749.101,-556.483)" width="24" x="1230.666666666667" xlink:href="#Compensator:西郊变电容_0" y="914.2222222222219" zvalue="231"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449833402374" ObjectName="10kV#4电容器组"/>
   <cge:TPSR_Ref TObjectID="6192449833402374"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1261.33,965.333) scale(2.55556,2.55556) translate(-749.101,-556.483)" width="24" x="1230.666666666667" y="914.2222222222219"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="64" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,485.56,628.083) scale(1,1) translate(2.40119e-13,0)" writing-mode="lr" x="485.01" xml:space="preserve" y="634.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125494849540" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="68" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,485.56,665.083) scale(1,1) translate(2.40119e-13,0)" writing-mode="lr" x="485.01" xml:space="preserve" y="671.36" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125494915076" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="71">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="71" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,641.81,786.417) scale(1,1) translate(0,0)" writing-mode="lr" x="641.26" xml:space="preserve" y="792.6900000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125494980612" ObjectName="P"/>
   </metadata>
  </g>
  <g id="72">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="72" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,641.81,823.417) scale(1,1) translate(0,0)" writing-mode="lr" x="641.26" xml:space="preserve" y="829.6900000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125495046148" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="74">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="74" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,487.06,697.083) scale(1,1) translate(2.40951e-13,0)" writing-mode="lr" x="486.51" xml:space="preserve" y="703.36" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125495111684" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="81">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="81" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,641.81,860.417) scale(1,1) translate(0,0)" writing-mode="lr" x="641.26" xml:space="preserve" y="866.6900000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125495439364" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="102" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,733.56,628.083) scale(1,1) translate(0,0)" writing-mode="lr" x="733.01" xml:space="preserve" y="634.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125498322948" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="101" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,733.56,665.083) scale(1,1) translate(0,0)" writing-mode="lr" x="733.01" xml:space="preserve" y="671.36" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125498388484" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="100" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,889.81,786.417) scale(1,1) translate(0,0)" writing-mode="lr" x="889.26" xml:space="preserve" y="792.6900000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125498454020" ObjectName="P"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="99" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,889.81,823.417) scale(1,1) translate(0,0)" writing-mode="lr" x="889.26" xml:space="preserve" y="829.6900000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125498519556" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="98" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,735.06,697.083) scale(1,1) translate(0,0)" writing-mode="lr" x="734.51" xml:space="preserve" y="703.36" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125498585092" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="97" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,889.81,860.417) scale(1,1) translate(0,0)" writing-mode="lr" x="889.26" xml:space="preserve" y="866.6900000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125498585092" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="149">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="149" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,989.56,628.083) scale(1,1) translate(0,0)" writing-mode="lr" x="989.01" xml:space="preserve" y="634.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125494849540" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="148" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,989.56,665.083) scale(1,1) translate(0,0)" writing-mode="lr" x="989.01" xml:space="preserve" y="671.36" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125494915076" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="147">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="147" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1145.81,786.417) scale(1,1) translate(0,0)" writing-mode="lr" x="1145.26" xml:space="preserve" y="792.6900000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125502255108" ObjectName="P"/>
   </metadata>
  </g>
  <g id="146">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="146" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1145.81,823.417) scale(1,1) translate(0,0)" writing-mode="lr" x="1145.26" xml:space="preserve" y="829.6900000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125502320644" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="145" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,991.06,697.083) scale(1,1) translate(0,0)" writing-mode="lr" x="990.51" xml:space="preserve" y="703.36" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125495111684" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="144" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1145.81,860.417) scale(1,1) translate(0,0)" writing-mode="lr" x="1145.26" xml:space="preserve" y="866.6900000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125502713860" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="196">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="196" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1249.56,628.083) scale(1,1) translate(0,0)" writing-mode="lr" x="1249.01" xml:space="preserve" y="634.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125494849540" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="195">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="195" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1249.56,665.083) scale(1,1) translate(0,0)" writing-mode="lr" x="1249.01" xml:space="preserve" y="671.36" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125494915076" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="194">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="194" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1405.81,786.417) scale(1,1) translate(0,0)" writing-mode="lr" x="1405.26" xml:space="preserve" y="792.6900000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125506056196" ObjectName="P"/>
   </metadata>
  </g>
  <g id="193">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="193" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1405.81,823.417) scale(1,1) translate(0,0)" writing-mode="lr" x="1405.26" xml:space="preserve" y="829.6900000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125506121732" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="192">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="192" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1249.56,697.083) scale(1,1) translate(0,0)" writing-mode="lr" x="1249.01" xml:space="preserve" y="703.36" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125495111684" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="191">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="191" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1405.81,860.417) scale(1,1) translate(0,0)" writing-mode="lr" x="1405.26" xml:space="preserve" y="866.6900000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125506514948" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="227">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="227" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1047.07,33.3214) scale(1,1) translate(-2.22505e-13,2.6348e-14)" writing-mode="lr" x="1046.6" xml:space="preserve" y="39.6" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125510053892" ObjectName="P"/>
   </metadata>
  </g>
  <g id="228">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="228" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1048.5,63.75) scale(1,1) translate(-2.22822e-13,0)" writing-mode="lr" x="1048.03" xml:space="preserve" y="70.03" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125510119428" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="230">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="230" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1048.5,92.75) scale(1,1) translate(-2.22822e-13,0)" writing-mode="lr" x="1048.03" xml:space="preserve" y="99.03" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125510184967" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="231">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="231" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1304.5,75.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1304.03" xml:space="preserve" y="81.53" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125493276676" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="232">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="232" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1304.5,112.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1304.03" xml:space="preserve" y="118.53" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125493342212" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="233">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="233" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1304.5,149.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1304.03" xml:space="preserve" y="155.53" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125493407748" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="235">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="235" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1304.5,179.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1304.03" xml:space="preserve" y="185.53" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125493735428" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="239">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="239" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1498.91,614.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.34" xml:space="preserve" y="620.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125541904388" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="242">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="242" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1498.91,651.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.34" xml:space="preserve" y="657.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125541969924" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="243">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="243" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1614.91,800.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1614.34" xml:space="preserve" y="806.9" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125600952324" ObjectName="P"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="245" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1614.91,837.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1614.34" xml:space="preserve" y="843.9" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125601017860" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="251">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="251" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1498.91,688.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.34" xml:space="preserve" y="694.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125542166532" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="269">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="269" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1614.91,874.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1614.34" xml:space="preserve" y="880.9" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125601083396" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="312">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="312" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,250.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="255.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125522046980" ObjectName="F"/>
   </metadata>
  </g>
  <g id="311">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="311" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,251.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="256.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125522112516" ObjectName="F"/>
   </metadata>
  </g>
  <g id="307">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="307" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,275.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="280.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125521915911" ObjectName="F"/>
   </metadata>
  </g>
  <g id="306">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="306" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,276.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="281.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125521981447" ObjectName="F"/>
   </metadata>
  </g>
  <g id="267">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="267" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,299.5) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="304.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125493669892" ObjectName="F"/>
   </metadata>
  </g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="254">
   <use class="kv10" height="30" transform="rotate(0,1770.16,409.591) scale(1.68831,1.5303) translate(-719.272,-133.983)" width="7" x="1764.252392344498" xlink:href="#ACLineSegment:线路_0" y="386.6363636363636" zvalue="349"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449835892742" ObjectName="T接10kV风平线"/>
   <cge:TPSR_Ref TObjectID="6192449835892742_5066549588066306"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1770.16,409.591) scale(1.68831,1.5303) translate(-719.272,-133.983)" width="7" x="1764.252392344498" y="386.6363636363636"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="262">
   <use class="kv10" height="30" transform="rotate(0,1770.55,681.934) scale(1.75,1.76228) translate(-748.307,-283.539)" width="28" x="1746.049901165099" xlink:href="#EnergyConsumer:站用变DY接地_0" y="655.4999999999999" zvalue="374"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449836023813" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1770.55,681.934) scale(1.75,1.76228) translate(-748.307,-283.539)" width="28" x="1746.049901165099" y="655.4999999999999"/></g>
  <g id="200">
   <use class="kv10" height="30" transform="rotate(0,1565.64,810) scale(1.25,-1.23333) translate(-311.628,-1463.26)" width="12" x="1558.138940752357" xlink:href="#EnergyConsumer:负荷_0" y="791.5" zvalue="389"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449840545798" ObjectName="动力变负荷"/>
   <cge:TPSR_Ref TObjectID="6192449840545798"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1565.64,810) scale(1.25,-1.23333) translate(-311.628,-1463.26)" width="12" x="1558.138940752357" y="791.5"/></g>
 </g>
 <g id="StateClass">
  <g id="240">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="401"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374887944196" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="153">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="402"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950463029253" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
  <g id="830">
   <use height="30" transform="rotate(0,327.812,118.464) scale(1.27778,1.03333) translate(-58.7636,-3.32141)" width="90" x="270.31" xlink:href="#State:全站检修_0" y="102.96" zvalue="432"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549588066306" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,327.812,118.464) scale(1.27778,1.03333) translate(-58.7636,-3.32141)" width="90" x="270.31" y="102.96"/></g>
 </g>
</svg>