<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549684404225" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:RT1122_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="22.91666666666667" y2="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="18.16666666666666" y2="20.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="18.16666666666666" y2="20.16666666666666"/>
   <path d="M 13 11 L 17 11 L 15 8 L 13 11 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="15.41666666666666" y2="18.16666666666666"/>
   <ellipse cx="14.95" cy="18.02" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.95" cy="9.92" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="PowerTransformer2:两卷变12_0" viewBox="0,0,31,34">
   <use terminal-index="0" type="1" x="9.50731595793324" xlink:href="#terminal" y="1.33736282578875"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.521947873799727" x2="5.455871323769093" y1="9.890146319158664" y2="6.194602328551218"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.73333333333333" x2="9.514631915866484" y1="5.944602328551216" y2="9.890146319158664"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.509350708733425" x2="9.509350708733425" y1="9.904778235025146" y2="13.93728637061797"/>
   <ellipse cx="9.59" cy="9.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:两卷变12_1" viewBox="0,0,31,34">
   <use terminal-index="1" type="1" x="9.583333333333336" xlink:href="#terminal" y="32.30121170553269"/>
   <ellipse cx="21.18" cy="17.36" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="21.27194787379972" x2="17.20587132376909" y1="17.973479652492" y2="14.27793566188455"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.48333333333333" x2="21.26463191586648" y1="14.02793566188455" y2="17.973479652492"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="21.25935070873343" x2="21.25935070873343" y1="17.98811156835848" y2="22.0206197039513"/>
  </symbol>
  <symbol id="Accessory:线路PT99_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="35" xlink:href="#terminal" y="6.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.5" x2="1.5" y1="4" y2="9"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.27377700368697" x2="20.43246971500333" y1="6.765818666751574" y2="6.765818666751574"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.6817302644139" x2="34.98333333333333" y1="6.765818666751574" y2="6.765818666751574"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.55947355115261" x2="24.55947355115261" y1="6.895129129519741" y2="14.09675167759026"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.46102695297582" x2="5.583333333333334" y1="6.846289710456816" y2="6.846289710456816"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="20.47373975336484" x2="20.47373975336484" y1="6.846289710456816" y2="6.846289710456816"/>
   <ellipse cx="24.54" cy="18.35" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="20.47373975336483" x2="20.47373975336483" y1="3.466505874834564" y2="10.30654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.61665011096792" x2="31.61665011096792" y1="6.846289710456816" y2="6.846289710456816"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="17.37848687625285" x2="17.37848687625285" y1="3.466505874834558" y2="10.30654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="31.6166501109679" x2="31.6166501109679" y1="3.466505874834564" y2="10.30654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="28.52139723385593" x2="28.52139723385593" y1="3.466505874834564" y2="10.30654458978453"/>
   <ellipse cx="27.04" cy="22.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="22.04" cy="22.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:设备233_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.097643878606505" xlink:href="#terminal" y="23.59422303238503"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="4" y1="2" y2="4"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="8" y1="2" y2="4"/>
   <rect fill-opacity="0" height="12.18" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.04,12.51) scale(1,1) translate(0,0)" width="7.42" x="2.33" y="6.42"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.5" x2="8.611111111111114" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.5" x2="8.611111111111114" y1="22.41666666666666" y2="22.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="2.166666666666664" y2="6.599999999999997"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Ground:大地_0" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6" x2="6" y1="13.08333333333334" y2="0.1666666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.5833333333333313" x2="11.25" y1="12.99453511141348" y2="12.99453511141348"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.083333333333333" x2="8" y1="17.63116790988687" y2="17.63116790988687"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666667" x2="10.33333333333333" y1="15.40451817731685" y2="15.40451817731685"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_0" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.05" x2="7.05" y1="1.166666666666664" y2="26.33333333333334"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="10.66666666666667" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="26.5417774506975" y2="23.30486537657277"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="26.5417774506975" y2="23.30486537657277"/>
   <rect fill-opacity="0" height="6.25" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,8.63) scale(1,1) translate(0,0)" width="2.83" x="5.58" y="5.5"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_1" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.05" x2="7.05" y1="2.833333333333334" y2="14.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="2.617622152595477" y2="5.500000000000003"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="10.66666666666666" y1="2.617622152595477" y2="5.500000000000003"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="10.66666666666667" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="24.65357874079139" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="26.5417774506975" y2="23.30486537657277"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="24.65357874079139" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="26.5417774506975" y2="23.30486537657277"/>
   <rect fill-opacity="0" height="6.25" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,8.63) scale(1,1) translate(0,0)" width="2.83" x="5.58" y="5.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="7.049999999999999" y1="16" y2="18.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.133333333333333" x2="7.133333333333333" y1="18.16666666666667" y2="24.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_2" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.33333333333333" x2="3.333333333333333" y1="9.166666666666668" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.666666666666667" x2="10.83333333333333" y1="9.124593892873616" y2="18.41666666666667"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="12.5" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="26.56537356321841" y2="21.7550287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="26.56537356321841" y2="21.7550287356322"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="Accessory:PT1515_0" viewBox="0,0,15,23">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="0.2666666666666639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="3.916666666666664" y2="0.2499999999999982"/>
   <ellipse cx="7.67" cy="16.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.57" cy="8.85" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.583333333333334" x2="7.583333333333334" y1="14.63888888888889" y2="16.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333334" x2="7.583333333333334" y1="19.25" y2="16.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333334" x2="7.583333333333334" y1="14.63888888888889" y2="16.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.749999999999996" x2="9.694444444444443" y1="9.549457994579948" y2="9.549457994579948"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.750000000000003" x2="7.064814814814818" y1="9.549457994579944" y2="7.083333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.694444444444443" x2="8.379629629629626" y1="9.549457994579944" y2="7.083333333333332"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV挖苦河二级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="274.75" x="46.5" xlink:href="logo.png" y="39"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,183.875,69) scale(1,1) translate(0,0)" writing-mode="lr" x="183.88" xml:space="preserve" y="72.5" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,179.75,68.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="179.75" xml:space="preserve" y="77.69" zvalue="3">110kV挖苦河二级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="153" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,79.625,206) scale(1,1) translate(0,0)" width="72.88" x="43.19" y="194" zvalue="234"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.625,206) scale(1,1) translate(0,0)" writing-mode="lr" x="79.63" xml:space="preserve" y="210.5" zvalue="234">信号一览</text>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="380.5" x2="380.5" y1="7" y2="1037" zvalue="4"/>
  <line fill="none" id="28" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.500000000000455" x2="373.5" y1="142.8704926140824" y2="142.8704926140824" zvalue="6"/>
  <line fill="none" id="26" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.500000000000455" x2="373.5" y1="612.8704926140824" y2="612.8704926140824" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.5" x2="96.5" y1="928" y2="928"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.5" x2="96.5" y1="967.1632999999999" y2="967.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.5" x2="6.5" y1="928" y2="967.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.5" x2="96.5" y1="928" y2="967.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.5" x2="366.5" y1="928" y2="928"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.5" x2="366.5" y1="967.1632999999999" y2="967.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.5" x2="96.5" y1="928" y2="967.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366.5" x2="366.5" y1="928" y2="967.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.5" x2="96.5" y1="967.16327" y2="967.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.5" x2="96.5" y1="995.08167" y2="995.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.5" x2="6.5" y1="967.16327" y2="995.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.5" x2="96.5" y1="967.16327" y2="995.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.5" x2="186.5" y1="967.16327" y2="967.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.5" x2="186.5" y1="995.08167" y2="995.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.5" x2="96.5" y1="967.16327" y2="995.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.5" x2="186.5" y1="967.16327" y2="995.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.5000000000001" x2="276.5000000000001" y1="967.16327" y2="967.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.5000000000001" x2="276.5000000000001" y1="995.08167" y2="995.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.5000000000001" x2="186.5000000000001" y1="967.16327" y2="995.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.5000000000001" x2="276.5000000000001" y1="967.16327" y2="995.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.5" x2="366.5" y1="967.16327" y2="967.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.5" x2="366.5" y1="995.08167" y2="995.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.5" x2="276.5" y1="967.16327" y2="995.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366.5" x2="366.5" y1="967.16327" y2="995.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.5" x2="96.5" y1="995.0816" y2="995.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.5" x2="96.5" y1="1023" y2="1023"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.5" x2="6.5" y1="995.0816" y2="1023"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.5" x2="96.5" y1="995.0816" y2="1023"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.5" x2="186.5" y1="995.0816" y2="995.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.5" x2="186.5" y1="1023" y2="1023"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.5" x2="96.5" y1="995.0816" y2="1023"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.5" x2="186.5" y1="995.0816" y2="1023"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.5000000000001" x2="276.5000000000001" y1="995.0816" y2="995.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.5000000000001" x2="276.5000000000001" y1="1023" y2="1023"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.5000000000001" x2="186.5000000000001" y1="995.0816" y2="1023"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.5000000000001" x2="276.5000000000001" y1="995.0816" y2="1023"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.5" x2="366.5" y1="995.0816" y2="995.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.5" x2="366.5" y1="1023" y2="1023"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.5" x2="276.5" y1="995.0816" y2="1023"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366.5" x2="366.5" y1="995.0816" y2="1023"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.5,948) scale(1,1) translate(0,0)" writing-mode="lr" x="51.5" xml:space="preserve" y="954" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.5,982) scale(1,1) translate(0,0)" writing-mode="lr" x="48.5" xml:space="preserve" y="988" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230.5,982) scale(1,1) translate(0,0)" writing-mode="lr" x="230.5" xml:space="preserve" y="988" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.5,1010) scale(1,1) translate(0,0)" writing-mode="lr" x="47.5" xml:space="preserve" y="1016" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229.5,1010) scale(1,1) translate(0,0)" writing-mode="lr" x="229.5" xml:space="preserve" y="1016" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,72,642.5) scale(1,1) translate(0,0)" writing-mode="lr" x="72" xml:space="preserve" y="647" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,602.861,231.972) scale(1,1) translate(0,0)" writing-mode="lr" x="602.86" xml:space="preserve" y="236.47" zvalue="36">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,613.211,286.972) scale(1,1) translate(0,0)" writing-mode="lr" x="613.21" xml:space="preserve" y="291.47" zvalue="38">161</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,528.083,273.943) scale(1,1) translate(0,-4.71522e-13)" writing-mode="lr" x="528.08" xml:space="preserve" y="278.44" zvalue="41">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,525.194,228.5) scale(1,1) translate(0,0)" writing-mode="lr" x="525.1900000000001" xml:space="preserve" y="233" zvalue="46">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1802,632.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1802" xml:space="preserve" y="637" zvalue="55">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,564.561,541.419) scale(1,1) translate(6.12843e-14,0)" writing-mode="lr" x="564.5599999999999" xml:space="preserve" y="545.92" zvalue="57">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,603.861,335.972) scale(1,1) translate(0,0)" writing-mode="lr" x="603.86" xml:space="preserve" y="340.47" zvalue="70">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,526.194,396.5) scale(1,1) translate(0,0)" writing-mode="lr" x="526.1900000000001" xml:space="preserve" y="401" zvalue="72">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" x="747.6015625" xml:space="preserve" y="900.7369357854257" zvalue="76">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="747.6015625" xml:space="preserve" y="916.7369357854257" zvalue="76">7.5MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,834.356,899.128) scale(1,1) translate(0,0)" writing-mode="lr" x="834.36" xml:space="preserve" y="903.63" zvalue="78">励磁变PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,832.388,683.212) scale(1,1) translate(2.70246e-13,0)" writing-mode="lr" x="832.39" xml:space="preserve" y="687.71" zvalue="80">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,719.436,730.669) scale(1,1) translate(0,0)" writing-mode="lr" x="719.4400000000001" xml:space="preserve" y="735.17" zvalue="82">661</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,800.517,809.47) scale(1,1) translate(0,0)" writing-mode="lr" x="800.52" xml:space="preserve" y="813.97" zvalue="86">6912</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,682.569,897.59) scale(1,1) translate(0,0)" writing-mode="lr" x="682.5700000000001" xml:space="preserve" y="902.09" zvalue="93">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,629.664,814.041) scale(1,1) translate(0,0)" writing-mode="lr" x="629.66" xml:space="preserve" y="818.54" zvalue="95">6911</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" x="1370.0859375" xml:space="preserve" y="900.7369357854257" zvalue="111">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1370.0859375" xml:space="preserve" y="916.7369357854257" zvalue="111">7.5MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1470.4,899.128) scale(1,1) translate(1.43925e-12,0)" writing-mode="lr" x="1470.4" xml:space="preserve" y="903.63" zvalue="113">励磁变PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1454.89,683.212) scale(1,1) translate(1.91032e-12,0)" writing-mode="lr" x="1454.89" xml:space="preserve" y="687.71" zvalue="115">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1343.37,730.669) scale(1,1) translate(0,0)" writing-mode="lr" x="1343.37" xml:space="preserve" y="735.17" zvalue="117">662</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1421.27,809.47) scale(1,1) translate(0,0)" writing-mode="lr" x="1421.27" xml:space="preserve" y="813.97" zvalue="121">6922</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1305.07,897.59) scale(1,1) translate(0,0)" writing-mode="lr" x="1305.07" xml:space="preserve" y="902.09" zvalue="128">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1267.41,814.041) scale(1,1) translate(2.77556e-13,0)" writing-mode="lr" x="1267.41" xml:space="preserve" y="818.54" zvalue="130">6921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1011.65,693.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1011.65" xml:space="preserve" y="698.25" zvalue="140">6901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1654.88,865.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1654.88" xml:space="preserve" y="869.75" zvalue="153">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1683.37,745.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1683.37" xml:space="preserve" y="750.12" zvalue="155">663</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1194.75,300.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1194.75" xml:space="preserve" y="304.75" zvalue="171">外接高河一级站10kV隔离变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1202.47,547.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1202.47" xml:space="preserve" y="552.25" zvalue="175">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,838.412,926.628) scale(1,1) translate(-1.75396e-13,0)" writing-mode="lr" x="838.41" xml:space="preserve" y="931.13" zvalue="179">高压切机第二轮</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1464.66,924.128) scale(1,1) translate(-9.43354e-13,0)" writing-mode="lr" x="1464.66" xml:space="preserve" y="928.63" zvalue="181">高压切机第一轮</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,660,446) scale(1,1) translate(0,0)" writing-mode="lr" x="660" xml:space="preserve" y="450.5" zvalue="188">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,593.514,105) scale(1,1) translate(0,0)" writing-mode="lr" x="593.51" xml:space="preserve" y="109.5" zvalue="201">110kV挖苦河二三级线</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="267.75" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="293.75" y2="293.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="267.75" y2="293.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="267.75" y2="293.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="267.75" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="293.75" y2="293.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="267.75" y2="293.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="267.75" y2="293.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="293.75" y2="293.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="318" y2="318"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="293.75" y2="318"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="293.75" y2="318"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="293.75" y2="293.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="318" y2="318"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="293.75" y2="318"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="293.75" y2="318"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="318" y2="318"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="340.75" y2="340.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="318" y2="340.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="318" y2="340.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="318" y2="318"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="340.75" y2="340.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="318" y2="340.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="318" y2="340.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="340.75" y2="340.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="363.5" y2="363.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="340.75" y2="363.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="340.75" y2="363.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="340.75" y2="340.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="363.5" y2="363.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="340.75" y2="363.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="340.75" y2="363.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="363.5" y2="363.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="386.25" y2="386.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="363.5" y2="386.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="363.5" y2="386.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="363.5" y2="363.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="386.25" y2="386.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="363.5" y2="386.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="363.5" y2="386.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="386.25" y2="386.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="409" y2="409"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="386.25" y2="409"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="386.25" y2="409"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="386.25" y2="386.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="409" y2="409"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="386.25" y2="409"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="386.25" y2="409"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="409" y2="409"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="431.75" y2="431.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="409" y2="431.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="409" y2="431.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="409" y2="409"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="431.75" y2="431.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="409" y2="431.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="409" y2="431.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,195.399,206.591) scale(1,1) translate(0,0)" writing-mode="lr" x="195.4" xml:space="preserve" y="211.09" zvalue="221">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,300.399,206.591) scale(1,1) translate(0,0)" writing-mode="lr" x="300.4" xml:space="preserve" y="211.09" zvalue="222">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="161" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,52.5,280.75) scale(1,1) translate(0,0)" writing-mode="lr" x="10" xml:space="preserve" y="285.25" zvalue="223">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,233,280.75) scale(1,1) translate(0,0)" writing-mode="lr" x="190.5" xml:space="preserve" y="285.25" zvalue="224">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.6875,378) scale(1,1) translate(0,0)" writing-mode="lr" x="52.69" xml:space="preserve" y="382.5" zvalue="227">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,51.5,306.75) scale(1,1) translate(0,0)" writing-mode="lr" x="9" xml:space="preserve" y="311.25" zvalue="235">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="151" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,232,306.75) scale(1,1) translate(0,0)" writing-mode="lr" x="189.5" xml:space="preserve" y="311.25" zvalue="236">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="150" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.6875,400) scale(1,1) translate(0,0)" writing-mode="lr" x="52.69" xml:space="preserve" y="404.5" zvalue="239">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,220.688,399) scale(1,1) translate(0,0)" writing-mode="lr" x="220.69" xml:space="preserve" y="403.5" zvalue="240">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.6875,423) scale(1,1) translate(0,0)" writing-mode="lr" x="52.69" xml:space="preserve" y="427.5" zvalue="241">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,220.688,422) scale(1,1) translate(0,0)" writing-mode="lr" x="220.69" xml:space="preserve" y="426.5" zvalue="242">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,51.5,329.75) scale(1,1) translate(0,0)" writing-mode="lr" x="9" xml:space="preserve" y="334.25" zvalue="243">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,231.5,328.75) scale(1,1) translate(0,0)" writing-mode="lr" x="189" xml:space="preserve" y="333.25" zvalue="245">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,200,946) scale(1,1) translate(0,0)" writing-mode="lr" x="200" xml:space="preserve" y="952" zvalue="252">WaKuHe2-01-2022</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="43.19" y="194" zvalue="234"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="DisconnectorClass">
  <g id="268">
   <use class="kv110" height="30" transform="rotate(0,591.417,232.972) scale(1.11111,0.814815) translate(-58.3083,50.1705)" width="15" x="583.0833435058597" xlink:href="#Disconnector:刀闸_0" y="220.7499999999999" zvalue="35"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454788710402" ObjectName="110kV挖苦河二三级线1616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454788710402"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,591.417,232.972) scale(1.11111,0.814815) translate(-58.3083,50.1705)" width="15" x="583.0833435058597" y="220.7499999999999"/></g>
  <g id="60">
   <use class="kv110" height="30" transform="rotate(0,592.417,336.972) scale(1.11111,0.814815) translate(-58.4083,73.8068)" width="15" x="584.0833435058597" xlink:href="#Disconnector:刀闸_0" y="324.75" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454789038082" ObjectName="110kV挖苦河二三级线1611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454789038082"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,592.417,336.972) scale(1.11111,0.814815) translate(-58.4083,73.8068)" width="15" x="584.0833435058597" y="324.75"/></g>
  <g id="242">
   <use class="v6300" height="27" transform="rotate(0,836.107,809.656) scale(1.13735,0.86495) translate(-100.012,124.593)" width="14" x="828.1455620613175" xlink:href="#Disconnector:带融断手车刀闸_0" y="797.9796554901828" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454789300226" ObjectName="#1发电机6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454789300226"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,836.107,809.656) scale(1.13735,0.86495) translate(-100.012,124.593)" width="14" x="828.1455620613175" y="797.9796554901828"/></g>
  <g id="301">
   <use class="v6300" height="27" transform="rotate(0,682.482,812.733) scale(1.13735,0.86495) translate(-81.459,125.074)" width="14" x="674.5208348101415" xlink:href="#Disconnector:带融断手车刀闸_0" y="801.0565785671059" zvalue="94"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454789169154" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454789169154"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,682.482,812.733) scale(1.13735,0.86495) translate(-81.459,125.074)" width="14" x="674.5208348101415" y="801.0565785671059"/></g>
  <g id="110">
   <use class="v6300" height="27" transform="rotate(0,1458.61,809.656) scale(1.13735,0.86495) translate(-175.188,124.593)" width="14" x="1450.645562061317" xlink:href="#Disconnector:带融断手车刀闸_0" y="797.9796554901828" zvalue="120"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454789955586" ObjectName="#2发电机6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454789955586"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1458.61,809.656) scale(1.13735,0.86495) translate(-175.188,124.593)" width="14" x="1450.645562061317" y="797.9796554901828"/></g>
  <g id="103">
   <use class="v6300" height="27" transform="rotate(0,1304.97,812.733) scale(1.13735,0.86495) translate(-156.634,125.074)" width="14" x="1297.007905922319" xlink:href="#Disconnector:带融断手车刀闸_0" y="801.0565785671059" zvalue="129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454789824514" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454789824514"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1304.97,812.733) scale(1.13735,0.86495) translate(-156.634,125.074)" width="14" x="1297.007905922319" y="801.0565785671059"/></g>
  <g id="338">
   <use class="v6300" height="27" transform="rotate(0,981.472,694.75) scale(-1.19048,-0.90535) translate(-1804.58,-1463.41)" width="14" x="973.1388888888888" xlink:href="#Disconnector:带融断手车刀闸_0" y="682.5277777777778" zvalue="139"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454790414338" ObjectName="6.3kV母线电压互感器6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454790414338"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,981.472,694.75) scale(-1.19048,-0.90535) translate(-1804.58,-1463.41)" width="14" x="973.1388888888888" y="682.5277777777778"/></g>
  <g id="182">
   <use class="kv6" height="30" transform="rotate(0,1204.1,413) scale(1.25,1.25) translate(-238.946,-78.85)" width="15" x="1194.729166666667" xlink:href="#Disconnector:令克_0" y="394.25" zvalue="169"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454790873090" ObjectName="外接高河一级站10kV隔离变隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454790873090"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1204.1,413) scale(1.25,1.25) translate(-238.946,-78.85)" width="15" x="1194.729166666667" y="394.25"/></g>
 </g>
 <g id="BreakerClass">
  <g id="267">
   <use class="kv110" height="20" transform="rotate(0,592.433,287.972) scale(1.22222,1.11111) translate(-106.604,-27.6861)" width="10" x="586.3216143031125" xlink:href="#Breaker:开关_0" y="276.8611111111111" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925216632835" ObjectName="110kV挖苦河二三级线161断路器"/>
   <cge:TPSR_Ref TObjectID="6473925216632835"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,592.433,287.972) scale(1.22222,1.11111) translate(-106.604,-27.6861)" width="10" x="586.3216143031125" y="276.8611111111111"/></g>
  <g id="249">
   <use class="v6300" height="20" transform="rotate(0,593.784,542.375) scale(1.59229,1.43307) translate(-217.912,-159.572)" width="10" x="585.8229380627334" xlink:href="#Breaker:小车断路器_0" y="528.0439675338483" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925216698371" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473925216698371"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,593.784,542.375) scale(1.59229,1.43307) translate(-217.912,-159.572)" width="10" x="585.8229380627334" y="528.0439675338483"/></g>
  <g id="82">
   <use class="v6300" height="20" transform="rotate(0,746.534,731.625) scale(1.59229,1.43307) translate(-274.731,-216.763)" width="10" x="738.5729380627333" xlink:href="#Breaker:小车断路器_0" y="717.2939675338483" zvalue="81"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925216763907" ObjectName="#1发电机661断路器"/>
   <cge:TPSR_Ref TObjectID="6473925216763907"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,746.534,731.625) scale(1.59229,1.43307) translate(-274.731,-216.763)" width="10" x="738.5729380627333" y="717.2939675338483"/></g>
  <g id="112">
   <use class="v6300" height="20" transform="rotate(0,1369.03,731.625) scale(1.59229,1.43307) translate(-506.286,-216.763)" width="10" x="1361.072938062734" xlink:href="#Breaker:小车断路器_0" y="717.2939675338483" zvalue="116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925216829443" ObjectName="#2发电机662断路器"/>
   <cge:TPSR_Ref TObjectID="6473925216829443"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1369.03,731.625) scale(1.59229,1.43307) translate(-506.286,-216.763)" width="10" x="1361.072938062734" y="717.2939675338483"/></g>
  <g id="195">
   <use class="v6300" height="20" transform="rotate(0,1659.03,746.625) scale(1.59229,1.43307) translate(-614.159,-221.296)" width="10" x="1651.072938062733" xlink:href="#Breaker:小车断路器_0" y="732.2939675338483" zvalue="154"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925216894979" ObjectName="#1站用变663断路器"/>
   <cge:TPSR_Ref TObjectID="6473925216894979"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1659.03,746.625) scale(1.59229,1.43307) translate(-614.159,-221.296)" width="10" x="1651.072938062733" y="732.2939675338483"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="44">
   <path class="kv110" d="M 591.48 244.99 L 591.48 277.34" stroke-width="1" zvalue="39"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@1" LinkObjectIDznd="267@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 591.48 244.99 L 591.48 277.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv110" d="M 591.51 221.15 L 591.51 160.31" stroke-width="1" zvalue="42"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="85@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 591.51 221.15 L 591.51 160.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv110" d="M 540.78 256.21 L 591.48 256.21" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265@0" LinkObjectIDznd="44" MaxPinNum="2"/>
   </metadata>
  <path d="M 540.78 256.21 L 591.48 256.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv110" d="M 536.93 208.32 L 591.51 208.32" stroke-width="1" zvalue="47"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="43" MaxPinNum="2"/>
   </metadata>
  <path d="M 536.93 208.32 L 591.51 208.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="v6300" d="M 746.53 744.52 L 746.53 837.92" stroke-width="1" zvalue="88"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@1" LinkObjectIDznd="850@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 746.53 744.52 L 746.53 837.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="v6300" d="M 836.11 838.62 L 836.11 820.9" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="242@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 836.11 838.62 L 836.11 820.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="v6300" d="M 836.11 798.63 L 836.11 781.36 L 746.53 781.36" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="242@0" LinkObjectIDznd="79" MaxPinNum="2"/>
   </metadata>
  <path d="M 836.11 798.63 L 836.11 781.36 L 746.53 781.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="v6300" d="M 682.5 836.46 L 682.48 823.98" stroke-width="1" zvalue="96"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@0" LinkObjectIDznd="301@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 682.5 836.46 L 682.48 823.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="v6300" d="M 777.38 766.74 L 746.64 766.74" stroke-width="1" zvalue="103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="79" MaxPinNum="2"/>
   </metadata>
  <path d="M 777.38 766.74 L 746.64 766.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="v6300" d="M 836.14 729.38 L 836.14 784.51" stroke-width="1" zvalue="105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@0" LinkObjectIDznd="77" MaxPinNum="2"/>
   </metadata>
  <path d="M 836.14 729.38 L 836.14 784.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="v6300" d="M 746.64 775.5 L 684.1 775.5 L 684.1 746.59" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 746.64 775.5 L 684.1 775.5 L 684.1 746.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="v6300" d="M 684.1 773.14 L 684.1 801.71" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86" LinkObjectIDznd="301@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 684.1 773.14 L 684.1 801.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="v6300" d="M 1369.03 744.52 L 1369.03 837.92" stroke-width="1" zvalue="123"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@1" LinkObjectIDznd="115@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1369.03 744.52 L 1369.03 837.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="v6300" d="M 1458.62 838.6 L 1458.61 820.9" stroke-width="1" zvalue="124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="110@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1458.62 838.6 L 1458.61 820.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="v6300" d="M 1458.61 798.63 L 1458.61 781.36 L 1369.03 781.36" stroke-width="1" zvalue="125"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="108" MaxPinNum="2"/>
   </metadata>
  <path d="M 1458.61 798.63 L 1458.61 781.36 L 1369.03 781.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="v6300" d="M 1305 836.46 L 1304.97 823.98" stroke-width="1" zvalue="131"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="103@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1305 836.46 L 1304.97 823.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="v6300" d="M 1399.88 766.74 L 1369.14 766.74" stroke-width="1" zvalue="133"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="108" MaxPinNum="2"/>
   </metadata>
  <path d="M 1399.88 766.74 L 1369.14 766.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="v6300" d="M 1458.64 729.38 L 1458.64 784.51" stroke-width="1" zvalue="135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="106" MaxPinNum="2"/>
   </metadata>
  <path d="M 1458.64 729.38 L 1458.64 784.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="v6300" d="M 981.47 732.67 L 981.47 706.29" stroke-width="1" zvalue="147"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="332@0" LinkObjectIDznd="338@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 981.47 732.67 L 981.47 706.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="v6300" d="M 981.47 682.98 L 981.47 633.5" stroke-width="1" zvalue="148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="338@1" LinkObjectIDznd="45@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 981.47 682.98 L 981.47 633.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="v6300" d="M 1015.28 746.13 L 1015.28 728 L 981.47 728" stroke-width="1" zvalue="150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="331@0" LinkObjectIDznd="121" MaxPinNum="2"/>
   </metadata>
  <path d="M 1015.28 746.13 L 1015.28 728 L 981.47 728" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="v6300" d="M 1659.03 759.52 L 1659.03 796.64" stroke-width="1" zvalue="156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@1" LinkObjectIDznd="205@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1659.03 759.52 L 1659.03 796.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="v6300" d="M 1714.03 779.88 L 1659.14 779.88" stroke-width="1" zvalue="161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="131" MaxPinNum="2"/>
   </metadata>
  <path d="M 1714.03 779.88 L 1659.14 779.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv110" d="M 592.51 298.58 L 592.51 325.15" stroke-width="1" zvalue="176"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@1" LinkObjectIDznd="60@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 592.51 298.58 L 592.51 325.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv6" d="M 1204 462.89 L 1204 428.31" stroke-width="1" zvalue="182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="182@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1204 462.89 L 1204 428.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv6" d="M 1204.21 396.44 L 1204.21 364.73" stroke-width="1" zvalue="183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="187@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1204.21 396.44 L 1204.21 364.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv110" d="M 591.96 413.84 L 470 413.84 L 470 432.25" stroke-width="1" zvalue="190"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@2" LinkObjectIDznd="8@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 591.96 413.84 L 470 413.84 L 470 432.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="v6300" d="M 643.38 498.74 L 592.13 498.74" stroke-width="1" zvalue="192"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="313@0" LinkObjectIDznd="74" MaxPinNum="2"/>
   </metadata>
  <path d="M 643.38 498.74 L 592.13 498.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv110" d="M 592.48 348.99 L 592.48 396.09" stroke-width="1" zvalue="193"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@1" LinkObjectIDznd="38@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 592.48 348.99 L 592.48 396.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="v6300" d="M 592.13 459.52 L 592.13 529.12" stroke-width="1" zvalue="194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@1" LinkObjectIDznd="249@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 592.13 459.52 L 592.13 529.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv110" d="M 633.5 188.65 L 591.51 188.65" stroke-width="1" zvalue="197"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="43" MaxPinNum="2"/>
   </metadata>
  <path d="M 633.5 188.65 L 591.51 188.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv110" d="M 629.09 171.25 L 591.51 171.25" stroke-width="1" zvalue="198"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="43" MaxPinNum="2"/>
   </metadata>
  <path d="M 629.09 171.25 L 591.51 171.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="v6300" d="M 1304.97 801.71 L 1304.97 749.59" stroke-width="1" zvalue="217"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="7@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1304.97 801.71 L 1304.97 749.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="v6300" d="M 1369.03 786 L 1304.99 786" stroke-width="1" zvalue="218"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108" LinkObjectIDznd="33" MaxPinNum="2"/>
   </metadata>
  <path d="M 1369.03 786 L 1304.99 786" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv110" d="M 537.95 376.82 L 592.48 376.82" stroke-width="1" zvalue="259"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="73" MaxPinNum="2"/>
   </metadata>
  <path d="M 537.95 376.82 L 592.48 376.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="v6300" d="M 593.78 555.27 L 593.78 633.5" stroke-width="1" zvalue="260"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@1" LinkObjectIDznd="45@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 593.78 555.27 L 593.78 633.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="v6300" d="M 746.53 718.37 L 746.53 633.5" stroke-width="1" zvalue="261"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="45@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 746.53 718.37 L 746.53 633.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="v6300" d="M 1659.03 633.5 L 1659.03 733.37" stroke-width="1" zvalue="262"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@3" LinkObjectIDznd="195@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1659.03 633.5 L 1659.03 733.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="v6300" d="M 1369.03 718.37 L 1369.03 633.5" stroke-width="1" zvalue="263"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="45@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1369.03 718.37 L 1369.03 633.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="265">
   <use class="kv110" height="20" transform="rotate(90,524.755,256.139) scale(1.42779,1.64399) translate(-155.087,-93.8956)" width="10" x="517.6156229483202" xlink:href="#GroundDisconnector:地刀_0" y="239.6990959419589" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454788644866" ObjectName="110kV挖苦河二三级线16160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454788644866"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,524.755,256.139) scale(1.42779,1.64399) translate(-155.087,-93.8956)" width="10" x="517.6156229483202" y="239.6990959419589"/></g>
  <g id="155">
   <use class="kv110" height="20" transform="rotate(90,522.154,208.25) scale(1.45001,1.51513) translate(-159.802,-65.6521)" width="10" x="514.9041446910089" xlink:href="#GroundDisconnector:地刀_0" y="193.0987287957589" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454788513794" ObjectName="110kV挖苦河二三级线16167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454788513794"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,522.154,208.25) scale(1.45001,1.51513) translate(-159.802,-65.6521)" width="10" x="514.9041446910089" y="193.0987287957589"/></g>
  <g id="59">
   <use class="kv110" height="20" transform="rotate(90,524.199,376.75) scale(1.35001,1.41064) translate(-134.158,-105.567)" width="10" x="517.4490543157601" xlink:href="#GroundDisconnector:地刀_0" y="362.6436384205101" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454788972546" ObjectName="110kV挖苦河二三级线16117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454788972546"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,524.199,376.75) scale(1.35001,1.41064) translate(-134.158,-105.567)" width="10" x="517.4490543157601" y="362.6436384205101"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="45">
   <path class="v6300" d="M 481.25 633.5 L 1762.5 633.5" stroke-width="6" zvalue="54"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674421374979" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674421374979"/></metadata>
  <path d="M 481.25 633.5 L 1762.5 633.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="313">
   <use class="v6300" height="26" transform="rotate(90,655.75,498.769) scale(-1,-1) translate(-1311.5,-997.538)" width="12" x="649.7499999999999" xlink:href="#Accessory:避雷器1_0" y="485.7692307692308" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454788775938" ObjectName="#1发电机避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,655.75,498.769) scale(-1,-1) translate(-1311.5,-997.538)" width="12" x="649.7499999999999" y="485.7692307692308"/></g>
  <g id="276">
   <use class="v6300" height="23" transform="rotate(0,836.107,852.695) scale(1.92176,1.25332) translate(-394.121,-169.435)" width="15" x="821.6938093853412" xlink:href="#Accessory:PT1515_0" y="838.2818193095625" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454789496834" ObjectName="#1发电机励磁变PT"/>
   </metadata>
  <rect fill="white" height="23" opacity="0" stroke="white" transform="rotate(0,836.107,852.695) scale(1.92176,1.25332) translate(-394.121,-169.435)" width="15" x="821.6938093853412" y="838.2818193095625"/></g>
  <g id="253">
   <use class="v6300" height="30" transform="rotate(0,836.138,713.511) scale(1.48718,1.44283) translate(-266.599,-212.346)" width="30" x="813.8299638313063" xlink:href="#Accessory:RT1122_0" y="691.8690543701709" zvalue="79"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454789431298" ObjectName="#1发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,836.138,713.511) scale(1.48718,1.44283) translate(-266.599,-212.346)" width="30" x="813.8299638313063" y="691.8690543701709"/></g>
  <g id="298">
   <use class="v6300" height="30" transform="rotate(0,682.569,856.754) scale(1.3853,1.3853) translate(-184.065,-232.512)" width="30" x="661.7898202133692" xlink:href="#Accessory:PT789_0" y="835.9741270018703" zvalue="92"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454789234690" ObjectName="#1发电机机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,682.569,856.754) scale(1.3853,1.3853) translate(-184.065,-232.512)" width="30" x="661.7898202133692" y="835.9741270018703"/></g>
  <g id="71">
   <use class="v6300" height="26" transform="rotate(90,789.75,766.769) scale(-1,-1) translate(-1579.5,-1533.54)" width="12" x="783.7499999999999" xlink:href="#Accessory:避雷器1_0" y="753.7692307692307" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454789103618" ObjectName="#1发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,789.75,766.769) scale(-1,-1) translate(-1579.5,-1533.54)" width="12" x="783.7499999999999" y="753.7692307692307"/></g>
  <g id="114">
   <use class="v6300" height="23" transform="rotate(0,1458.62,851.898) scale(1.8155,1.18402) translate(-649.078,-130.285)" width="15" x="1445.008508352757" xlink:href="#Accessory:PT1515_0" y="838.2818193095626" zvalue="112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454790152194" ObjectName="#2发电机励磁变PT"/>
   </metadata>
  <rect fill="white" height="23" opacity="0" stroke="white" transform="rotate(0,1458.62,851.898) scale(1.8155,1.18402) translate(-649.078,-130.285)" width="15" x="1445.008508352757" y="838.2818193095626"/></g>
  <g id="113">
   <use class="v6300" height="30" transform="rotate(0,1458.64,713.511) scale(1.48718,1.44283) translate(-470.522,-212.346)" width="30" x="1436.329963831306" xlink:href="#Accessory:RT1122_0" y="691.8690543701709" zvalue="114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454790086658" ObjectName="#2发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1458.64,713.511) scale(1.48718,1.44283) translate(-470.522,-212.346)" width="30" x="1436.329963831306" y="691.8690543701709"/></g>
  <g id="104">
   <use class="v6300" height="30" transform="rotate(0,1305.07,856.754) scale(1.3853,1.3853) translate(-357.203,-232.512)" width="30" x="1284.289820213369" xlink:href="#Accessory:PT789_0" y="835.9741270018703" zvalue="127"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454789890050" ObjectName="#2发电机互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1305.07,856.754) scale(1.3853,1.3853) translate(-357.203,-232.512)" width="30" x="1284.289820213369" y="835.9741270018703"/></g>
  <g id="101">
   <use class="v6300" height="26" transform="rotate(90,1412.25,766.769) scale(-1,-1) translate(-2824.5,-1533.54)" width="12" x="1406.25" xlink:href="#Accessory:避雷器1_0" y="753.7692307692307" zvalue="132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454789758978" ObjectName="#2发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1412.25,766.769) scale(-1,-1) translate(-2824.5,-1533.54)" width="12" x="1406.25" y="753.7692307692307"/></g>
  <g id="332">
   <use class="v6300" height="30" transform="rotate(0,981.541,750.41) scale(1.38364,1.21068) translate(-266.394,-127.425)" width="30" x="960.786868217023" xlink:href="#Accessory:PT789_0" y="732.25" zvalue="143"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454790348802" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,981.541,750.41) scale(1.38364,1.21068) translate(-266.394,-127.425)" width="30" x="960.786868217023" y="732.25"/></g>
  <g id="331">
   <use class="v6300" height="26" transform="rotate(0,1015.25,758.5) scale(1,1) translate(0,0)" width="12" x="1009.25" xlink:href="#Accessory:避雷器1_0" y="745.5" zvalue="145"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454790283266" ObjectName="6.3kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1015.25,758.5) scale(1,1) translate(0,0)" width="12" x="1009.25" y="745.5"/></g>
  <g id="132">
   <use class="v6300" height="26" transform="rotate(0,1714,792.25) scale(1,1) translate(0,0)" width="12" x="1708" xlink:href="#Accessory:避雷器1_0" y="779.25" zvalue="163"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454790610946" ObjectName="10kVⅡ段母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1714,792.25) scale(1,1) translate(0,0)" width="12" x="1708" y="779.25"/></g>
  <g id="49">
   <use class="kv110" height="30" transform="rotate(0,651,197) scale(-1,1) translate(-1302,0)" width="35" x="633.5" xlink:href="#Accessory:线路PT99_0" y="182" zvalue="195"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449581219846" ObjectName="110kV挖苦河二三级设备1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,651,197) scale(-1,1) translate(-1302,0)" width="35" x="633.5" y="182"/></g>
  <g id="50">
   <use class="kv110" height="20" transform="rotate(270,650.75,171.25) scale(1.525,2.475) translate(-218.779,-87.3081)" width="20" x="635.5" xlink:href="#Accessory:线路PT3_0" y="146.5" zvalue="196"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449581285382" ObjectName="110kV挖苦河二三级线PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,650.75,171.25) scale(1.525,2.475) translate(-218.779,-87.3081)" width="20" x="635.5" y="146.5"/></g>
  <g id="6">
   <use class="v6300" height="26" transform="rotate(0,684,736) scale(1,1) translate(0,0)" width="12" x="678" xlink:href="#Accessory:设备233_0" y="723" zvalue="213"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450102296581" ObjectName="#1发电机小电阻电容接地"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,684,736) scale(1,1) translate(0,0)" width="12" x="678" y="723"/></g>
  <g id="7">
   <use class="v6300" height="26" transform="rotate(0,1304,739) scale(1,1) translate(0,0)" width="12" x="1298" xlink:href="#Accessory:设备233_0" y="726" zvalue="215"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450101248005" ObjectName="#2发电机小电阻电容接地"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1304,739) scale(1,1) translate(0,0)" width="12" x="1298" y="726"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v6300" height="30" transform="rotate(0,746.641,859.061) scale(1.43307,1.43307) translate(-219.135,-253.108)" width="30" x="725.1445868895569" xlink:href="#Generator:发电机_0" y="837.5652867370809" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454789562370" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192454789562370"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,746.641,859.061) scale(1.43307,1.43307) translate(-219.135,-253.108)" width="30" x="725.1445868895569" y="837.5652867370809"/></g>
  <g id="115">
   <use class="v6300" height="30" transform="rotate(0,1369.14,859.061) scale(1.43307,1.43307) translate(-407.251,-253.108)" width="30" x="1347.644586889557" xlink:href="#Generator:发电机_0" y="837.5652867370809" zvalue="110"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454790217730" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192454790217730"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1369.14,859.061) scale(1.43307,1.43307) translate(-407.251,-253.108)" width="30" x="1347.644586889557" y="837.5652867370809"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="205">
   <use class="v6300" height="30" transform="rotate(0,1658.75,821.25) scale(1.69643,1.70833) translate(-671.211,-329.893)" width="28" x="1635" xlink:href="#EnergyConsumer:站用变DY接地_0" y="795.625" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454790545410" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1658.75,821.25) scale(1.69643,1.70833) translate(-671.211,-329.893)" width="28" x="1635" y="795.625"/></g>
  <g id="137">
   <use class="kv6" height="30" transform="rotate(0,1203.84,487.5) scale(1.69643,1.70833) translate(-484.46,-191.509)" width="28" x="1180.094144700714" xlink:href="#EnergyConsumer:站用变DY接地_0" y="461.875" zvalue="174"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454790938626" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1203.84,487.5) scale(1.69643,1.70833) translate(-484.46,-191.509)" width="28" x="1180.094144700714" y="461.875"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="187">
   <use class="kv6" height="30" transform="rotate(0,1204.21,350.356) scale(3.59524,0.967949) translate(-860.18,11.1204)" width="7" x="1191.625" xlink:href="#ACLineSegment:线路_0" y="335.8365384615386" zvalue="170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454790807554" ObjectName="外接高河一级站10kV隔离变"/>
   <cge:TPSR_Ref TObjectID="6192454790807554_5066549684404225"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1204.21,350.356) scale(3.59524,0.967949) translate(-860.18,11.1204)" width="7" x="1191.625" y="335.8365384615386"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="38">
   <g id="380">
    <use class="kv110" height="34" transform="rotate(0,604.25,428.177) scale(2.04839,2.04839) translate(-293.012,-201.323)" width="31" x="572.5" xlink:href="#PowerTransformer2:两卷变12_0" y="393.35" zvalue="187"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874592813058" ObjectName="110"/>
    </metadata>
   </g>
   <g id="381">
    <use class="v6300" height="34" transform="rotate(0,604.25,428.177) scale(2.04839,2.04839) translate(-293.012,-201.323)" width="31" x="572.5" xlink:href="#PowerTransformer2:两卷变12_1" y="393.35" zvalue="187"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874422550532" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399533592578" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399533592578"/></metadata>
  <rect fill="white" height="34" opacity="0" stroke="white" transform="rotate(0,604.25,428.177) scale(2.04839,2.04839) translate(-293.012,-201.323)" width="31" x="572.5" y="393.35"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="118">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="118" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,599.514,43.75) scale(1,1) translate(0,0)" writing-mode="lr" x="599.05" xml:space="preserve" y="48.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136401481730" ObjectName="P"/>
   </metadata>
  </g>
  <g id="119">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="119" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,599.514,61.75) scale(1,1) translate(0,0)" writing-mode="lr" x="599.05" xml:space="preserve" y="66.42" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136401547266" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="120">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="120" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,599.514,79.25) scale(1,1) translate(0,0)" writing-mode="lr" x="599.05" xml:space="preserve" y="83.92" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136401612802" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="133">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="133" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,738.641,937.779) scale(1,1) translate(0,-2.05737e-13)" writing-mode="lr" x="738.09" xml:space="preserve" y="942.46" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136404889602" ObjectName="P"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="134" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1369.14,935.279) scale(1,1) translate(0,-2.04849e-13)" writing-mode="lr" x="1368.59" xml:space="preserve" y="939.98" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136406069250" ObjectName="P"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="136" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,738.641,961) scale(1,1) translate(0,-2.1056e-13)" writing-mode="lr" x="738.09" xml:space="preserve" y="965.7" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136404955138" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="138" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1369.14,956.5) scale(1,1) translate(0,-2.09894e-13)" writing-mode="lr" x="1368.59" xml:space="preserve" y="961.1799999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136406134786" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="140" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,738.641,982.5) scale(1,1) translate(0,0)" writing-mode="lr" x="738.09" xml:space="preserve" y="987.2" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136405020674" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="141" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1369.14,981.779) scale(1,1) translate(0,1.0747e-13)" writing-mode="lr" x="1368.59" xml:space="preserve" y="986.5" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136406200322" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,152.611,280.917) scale(1,1) translate(0,0)" writing-mode="lr" x="152.77" xml:space="preserve" y="285.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136403185666" ObjectName="F"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,330.222,281.917) scale(1,1) translate(0,0)" writing-mode="lr" x="330.38" xml:space="preserve" y="286.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136403251202" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,152.611,305.917) scale(1,1) translate(0,0)" writing-mode="lr" x="152.77" xml:space="preserve" y="310.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136403054594" ObjectName="F"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,330.222,306.917) scale(1,1) translate(0,0)" writing-mode="lr" x="330.38" xml:space="preserve" y="311.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136403120130" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,152.611,329.917) scale(1,1) translate(0,0)" writing-mode="lr" x="152.77" xml:space="preserve" y="334.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127211696135" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,332.611,328.917) scale(1,1) translate(0,0)" writing-mode="lr" x="332.77" xml:space="preserve" y="333.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127180566533" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="96" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,152.611,396.667) scale(1,1) translate(0,0)" writing-mode="lr" x="152.77" xml:space="preserve" y="401.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124274438148" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="83">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,152.611,376.667) scale(1,1) translate(0,0)" writing-mode="lr" x="152.77" xml:space="preserve" y="381.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136403709954" ObjectName="F"/>
   </metadata>
  </g>
  <g id="61">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,328.611,396.667) scale(1,1) translate(0,0)" writing-mode="lr" x="328.77" xml:space="preserve" y="401.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124274569222" ObjectName="雨量采集"/>
   </metadata>
  </g>
  <g id="25">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,328.611,420.667) scale(1,1) translate(0,0)" writing-mode="lr" x="328.77" xml:space="preserve" y="425.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124274700292" ObjectName="F"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="2" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,979.25,807) scale(1,1) translate(0,0)" writing-mode="lr" x="978.78" xml:space="preserve" y="811.78" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136403316738" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,979.25,832) scale(1,1) translate(0,0)" writing-mode="lr" x="978.78" xml:space="preserve" y="836.78" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136403382274" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,979.25,857) scale(1,1) translate(0,0)" writing-mode="lr" x="978.78" xml:space="preserve" y="861.78" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136403447810" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="5" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,551.25,652.5) scale(1,1) translate(0,0)" writing-mode="lr" x="550.78" xml:space="preserve" y="657.28" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136403578882" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="17" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,663.803,326.955) scale(1,1) translate(-1.37723e-13,-7.00228e-14)" writing-mode="lr" x="622.0599999999999" xml:space="preserve" y="331.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136409477122" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="24">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="24" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,663.803,346.177) scale(1,1) translate(-1.37723e-13,-7.42911e-14)" writing-mode="lr" x="622.0599999999999" xml:space="preserve" y="350.59" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136409542658" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="27" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,661.625,545.794) scale(1,1) translate(6.85285e-14,1.19016e-13)" writing-mode="lr" x="619.1" xml:space="preserve" y="550.22" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124273061892" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="47">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="47" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,661.625,566.5) scale(1,1) translate(6.85285e-14,1.23614e-13)" writing-mode="lr" x="619.1" xml:space="preserve" y="570.92" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124273127428" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="53" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,665.447,365.4) scale(1,1) translate(-1.38088e-13,-7.85594e-14)" writing-mode="lr" x="623.71" xml:space="preserve" y="369.81" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136409739266" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="54" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,661.625,587.206) scale(1,1) translate(6.85285e-14,1.28211e-13)" writing-mode="lr" x="619.1" xml:space="preserve" y="591.63" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124273192964" ObjectName="LIa"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="154">
   <use height="30" transform="rotate(0,327.673,207.107) scale(0.708333,0.665547) translate(130.549,99.0596)" width="30" x="317.05" xlink:href="#State:红绿圆(方形)_0" y="197.12" zvalue="232"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374885060612" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,327.673,207.107) scale(0.708333,0.665547) translate(130.549,99.0596)" width="30" x="317.05" y="197.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,232.048,207.107) scale(0.708333,0.665547) translate(91.174,99.0596)" width="30" x="221.42" xlink:href="#State:红绿圆(方形)_0" y="197.12" zvalue="233"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950080954372" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,232.048,207.107) scale(0.708333,0.665547) translate(91.174,99.0596)" width="30" x="221.42" y="197.12"/></g>
  <g id="9">
   <use height="30" transform="rotate(0,321.812,125.464) scale(1.22222,1.03092) translate(-48.5114,-3.29938)" width="90" x="266.81" xlink:href="#State:全站检修_0" y="110" zvalue="265"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549684404225" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,321.812,125.464) scale(1.22222,1.03092) translate(-48.5114,-3.29938)" width="90" x="266.81" y="110"/></g>
 </g>
 <g id="GroundClass">
  <g id="8">
   <use class="kv110" height="18" transform="rotate(0,470,441) scale(1,1) translate(0,0)" width="12" x="464" xlink:href="#Ground:大地_0" y="432" zvalue="257"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450122874885" ObjectName="#1主变接地"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,470,441) scale(1,1) translate(0,0)" width="12" x="464" y="432"/></g>
 </g>
</svg>