<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549582430210" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:接地变20201012_0" viewBox="0,0,23,35">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.05" x2="21.05" y1="24.5" y2="24.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="20.5" y1="17.5" y2="17.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.5" x2="21.5" y1="23.5" y2="23.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5" x2="20.5" y1="17.5" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="16.5" y1="17.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="7.5" y1="25.5" y2="29.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.2" x2="7.2" y1="24.25" y2="32.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.5" x2="22.5" y1="22.5" y2="22.5"/>
   <path d="M 6.25 29.75 L 8.25 29.75 L 7.25 31.75 L 6.25 29.75 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="7.07" cy="6.66" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.061718118722352" x2="7.061718118722352" y1="3.664465958746224" y2="6.263454058763156"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.655265608193801" x2="7.061718118722348" y1="8.862442158780071" y2="6.263454058763138"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.468170629250878" x2="7.061718118722331" y1="8.862442158780071" y2="6.263454058763138"/>
   <ellipse cx="7.07" cy="17.66" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.061718118722352" x2="7.061718118722352" y1="14.66446595874622" y2="17.26345405876316"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.655265608193801" x2="7.061718118722348" y1="19.86244215878007" y2="17.26345405876314"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.468170629250878" x2="7.061718118722331" y1="19.86244215878007" y2="17.26345405876314"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="Accessory:PT1111_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="33.75" xlink:href="#terminal" y="11.95"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="27.02377700368697" x2="19.18246971500333" y1="12.01581866675157" y2="12.01581866675157"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.4317302644139" x2="33.73333333333333" y1="12.01581866675157" y2="12.01581866675157"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.30947355115262" x2="23.30947355115262" y1="12.14512912951974" y2="19.34675167759026"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.21102695297582" x2="12.70307369224894" y1="12.09628971045682" y2="12.09628971045682"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.22373975336484" x2="19.22373975336484" y1="12.09628971045682" y2="12.09628971045682"/>
   <ellipse cx="23.29" cy="23.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="19.22373975336483" x2="19.22373975336483" y1="8.716505874834564" y2="15.55654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.36665011096792" x2="30.36665011096792" y1="12.09628971045682" y2="12.09628971045682"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="30.3666501109679" x2="30.3666501109679" y1="8.716505874834564" y2="15.55654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="16.12848687625285" x2="16.12848687625285" y1="8.716505874834558" y2="15.55654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="27.27139723385593" x2="27.27139723385593" y1="8.716505874834564" y2="15.55654458978453"/>
   <ellipse cx="25.79" cy="27.85" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.79" cy="27.85" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.96296296296296" x2="8.779897241477752" y1="12.08795620351516" y2="12.08795620351516"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="8.711988705963254" x2="8.711988705963254" y1="14.28440670580408" y2="10.00759086112023"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="7.461988705963254" x2="7.461988705963254" y1="13.53440670580408" y2="10.9242575277869"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.961988705963256" x2="5.961988705963256" y1="13.03440670580408" y2="11.59092419445357"/>
  </symbol>
  <symbol id="Ground:小电阻接地_0" viewBox="0,0,14,18">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <rect fill-opacity="0" height="8.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6.94,4.99) scale(1,1) translate(0,0)" width="6.08" x="3.9" y="0.9"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="7" x2="7" y1="12.33333333333334" y2="9"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9999999999999982" x2="12.91666666666667" y1="12.24453511141348" y2="12.24453511141348"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.916666666666666" x2="11.08333333333333" y1="14.65451817731685" y2="14.65451817731685"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.934588934424228" x2="8.898744398909104" y1="17.13116790988687" y2="17.13116790988687"/>
  </symbol>
  <symbol id="Accessory:10kV母线PT带消谐装置_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="17.56245852479325" xlink:href="#terminal" y="28.12373692455963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.61245852479333" x2="34.61245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.86245852479331" x2="30.86245852479331" y1="22.68299618381883" y2="19.84040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.6124585247933" x2="26.6124585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.61245852479332" x2="34.61245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666667" x2="31" y1="22.69225544307809" y2="22.69225544307809"/>
   <rect fill-opacity="0" height="3.55" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,30.81,15.38) scale(1,1) translate(0,0)" width="8.92" x="26.34" y="13.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.647721108666" x2="30.647721108666" y1="10.83674821859629" y2="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.71438777533266" x2="28.71438777533266" y1="7.51612768565195" y2="7.51612768565195"/>
   <ellipse cx="14.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.05048569403981" x2="22.05048569403981" y1="34.53084700683308" y2="34.53084700683308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.7608382776216" x2="28.48402243293775" y1="7.754208141873304" y2="7.754208141873304"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.01083827762157" x2="29.40068909960439" y1="6.50420814187332" y2="6.50420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.51083827762159" x2="30.06735576627107" y1="5.004208141873296" y2="5.004208141873296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.89410730945214" x2="23.0877735452272" y1="24.15299989806297" y2="21.53208583485582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58713830216066" x2="22.81883560169719" y1="21.54040359122622" y2="21.54040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51457106407813" x2="19.32090482830307" y1="24.05654513693459" y2="21.43563107372743"/>
   <ellipse cx="14.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.3644003886642" x2="22.3644003886642" y1="30.19213090500035" y2="30.19213090500035"/>
   <ellipse cx="21.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.19220382559271" x2="17.19220382559271" y1="23.07076893362116" y2="23.07076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.4422038255927" x2="16.4422038255927" y1="16.82076893362115" y2="16.82076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.92264294445647" x2="20.87303257583197" y1="16.99992879670395" y2="15.47611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.873032575832" x2="20.37063985073817" y1="15.47611741162252" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.87303257583199" x2="23.32581493230132" y1="15.47611741162254" y2="16.60543752773795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.94220382559271" x2="16.94220382559271" y1="16.32076893362115" y2="16.32076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="16.07581493230131" y1="22.72611741162255" y2="23.85543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="13.12063985073816" y1="22.72611741162255" y2="20.07298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583197" y1="24.24992879670398" y2="22.72611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="16.07581493230131" y1="15.47611741162254" y2="16.60543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="13.12063985073816" y1="15.47611741162253" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583196" y1="16.99992879670396" y2="15.47611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.59103363843511" x2="17.59103363843511" y1="25.15822158129307" y2="28.16666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:接地可调两卷变_0" viewBox="0,0,22,30">
   <use terminal-index="0" type="1" x="11.00731595793324" xlink:href="#terminal" y="1.08736282578875"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.02194787379973" x2="6.955871323769093" y1="7.640146319158664" y2="3.944602328551218"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.23333333333333" x2="11.01463191586648" y1="3.694602328551216" y2="7.640146319158664"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.00935070873343" x2="11.00935070873343" y1="7.654778235025146" y2="11.68728637061797"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="21.53783721993598" x2="20.53783721993598" y1="2.175582990397805" y2="5.175582990397805"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.5394375857338822" x2="21.53943758573388" y1="13.09064929126657" y2="2.173982624599901"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="18.53052126200274" x2="21.53052126200274" y1="1.175582990397805" y2="2.175582990397805"/>
   <ellipse cx="11.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="11" xlink:href="#terminal" y="7.7"/>
  </symbol>
  <symbol id="PowerTransformer2:接地可调两卷变_1" viewBox="0,0,22,30">
   <use terminal-index="1" type="1" x="11" xlink:href="#terminal" y="29.05121170553269"/>
   <path d="M 6.75 25.8333 L 15.8333 25.8333 L 11.0833 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:接地刀_0" viewBox="0,0,20,25">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="5.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="13" y1="14.33333333333333" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.927164662603633" x2="5.927164662603633" y1="21.23783949080951" y2="18.88156647584608"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.627658815132046" x2="9.601962883654533" y1="21.31657844392075" y2="21.31657844392075"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.411384725196287" x2="8.342862241256245" y1="23.11600124906283" y2="23.11600124906283"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.281031073506858" x2="7.626181375820934" y1="24.83429284309519" y2="24.83429284309519"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.6833333333333336" x2="5.927164662603635" y1="9.222919382464234" y2="18.88156647584608"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.83108493932905" x2="8.028087080656674" y1="9.101131074690123" y2="9.101131074690123"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.97259166003632" x2="5.97259166003632" y1="9.059451081670424" y2="6.027110967746054"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.57716466260364" x2="15.57716466260364" y1="21.12739518973012" y2="18.77112217476669"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.27765881513205" x2="19.25196288365454" y1="21.20613414284136" y2="21.20613414284136"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.06138472519629" x2="17.99286224125624" y1="23.00555694798344" y2="23.00555694798344"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.93103107350686" x2="17.27618137582093" y1="24.7238485420158" y2="24.7238485420158"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.33333333333333" x2="15.57716466260364" y1="9.112475081384845" y2="18.77112217476669"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.62259166003632" x2="15.62259166003632" y1="8.949006780591038" y2="0.08333333333333393"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.48108493932905" x2="17.67808708065667" y1="8.990686773610733" y2="8.990686773610733"/>
  </symbol>
  <symbol id="GroundDisconnector:接地刀_1" viewBox="0,0,20,25">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="5.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.916666666666667" x2="15.41666666666667" y1="14.33333333333333" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.927164662603633" x2="5.927164662603633" y1="21.23783949080951" y2="9.083333333333336"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.627658815132046" x2="9.601962883654533" y1="21.31657844392075" y2="21.31657844392075"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.411384725196287" x2="8.342862241256245" y1="23.11600124906283" y2="23.11600124906283"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.281031073506858" x2="7.626181375820934" y1="24.83429284309519" y2="24.83429284309519"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.83108493932905" x2="8.028087080656674" y1="9.101131074690123" y2="9.101131074690123"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.97259166003632" x2="5.97259166003632" y1="9.059451081670424" y2="6.027110967746054"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.57716466260364" x2="15.57716466260364" y1="21.12739518973012" y2="8.916666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.27765881513205" x2="19.25196288365454" y1="21.20613414284136" y2="21.20613414284136"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.06138472519629" x2="17.99286224125624" y1="23.00555694798344" y2="23.00555694798344"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.93103107350686" x2="17.27618137582093" y1="24.7238485420158" y2="24.7238485420158"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.62259166003632" x2="15.62259166003632" y1="8.949006780591038" y2="0.08333333333333393"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.48108493932905" x2="17.67808708065667" y1="8.990686773610733" y2="8.990686773610733"/>
  </symbol>
  <symbol id="GroundDisconnector:接地刀_2" viewBox="0,0,20,25">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="5.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.927164662603633" x2="5.927164662603633" y1="21.23783949080951" y2="18.88156647584608"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.627658815132046" x2="9.601962883654533" y1="21.31657844392075" y2="21.31657844392075"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.411384725196287" x2="8.342862241256245" y1="23.11600124906283" y2="23.11600124906283"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.281031073506858" x2="7.626181375820934" y1="24.83429284309519" y2="24.83429284309519"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.510497995936969" x2="3.266666666666667" y1="9.306252715797566" y2="18.96489980917941"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.83108493932905" x2="8.028087080656674" y1="9.101131074690123" y2="9.101131074690123"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.97259166003632" x2="5.97259166003632" y1="9.059451081670424" y2="6.027110967746054"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.57716466260364" x2="15.57716466260364" y1="21.12739518973012" y2="18.77112217476669"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.27765881513205" x2="19.25196288365454" y1="21.20613414284136" y2="21.20613414284136"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.06138472519629" x2="17.99286224125624" y1="23.00555694798344" y2="23.00555694798344"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.93103107350686" x2="17.27618137582093" y1="24.7238485420158" y2="24.7238485420158"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="8.660497995936966" y1="9.279141748051511" y2="18.93778884143336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.62259166003632" x2="15.62259166003632" y1="8.949006780591038" y2="0.08333333333333393"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.48108493932905" x2="17.67808708065667" y1="8.990686773610733" y2="8.990686773610733"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.0938313292703" x2="12.85" y1="9.056252715797566" y2="18.71489980917941"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13" x2="18.2438313292703" y1="9.029141748051511" y2="18.68778884143336"/>
  </symbol>
  <symbol id="Compensator:电容20200722_0" viewBox="0,0,25,50">
   <use terminal-index="0" type="0" x="15.16666666666667" xlink:href="#terminal" y="0.4166666666666643"/>
   <path d="M 15.3333 45.75 L 15.3333 48.75 L 1.33333 48.75 L 1.33333 0.0833333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25" x2="22.25" y1="37.91666666666667" y2="35.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25" x2="23.25" y1="32.25" y2="29.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.75" x2="23.75" y1="37.91666666666667" y2="37.91666666666667"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,22.25,31.42) scale(1,1) translate(0,0)" width="4" x="20.25" y="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.75" x2="22.75" y1="39.91666666666667" y2="39.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.25" x2="21.25" y1="32.25" y2="29.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.25" x2="23.25" y1="38.91666666666667" y2="38.91666666666667"/>
   <rect fill-opacity="0" height="4" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.33,32.33) scale(1,1) translate(0,0)" width="2" x="14.33" y="30.33"/>
   <path d="M 15.25 23.75 L 22.25 23.75 L 22.25 32.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.35833333333333" x2="15.35833333333333" y1="40.10833333333333" y2="43.62685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.321296296296296" x2="9.321296296296296" y1="41.38611111111112" y2="43.62685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.10833333333333" x2="7.290905947441217" y1="46.02500000000001" y2="46.02500000000001"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25" x2="9.333333333333332" y1="43.60833333333333" y2="43.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.35833333333333" x2="15.35833333333333" y1="43.69166666666667" y2="46.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.374239280774555" x2="7.374239280774555" y1="46.10833333333333" y2="44.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.19166666666666" x2="23.19166666666666" y1="44.66666666666667" y2="46.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.358333333333334" x2="9.358333333333334" y1="30.60833333333334" y2="28.40462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.35833333333333" x2="15.35833333333333" y1="21.5" y2="39.10833333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333332" x2="15.35833333333334" y1="28.35833333333333" y2="28.35833333333333"/>
   <path d="M 9.35833 34.2072 A 2.96392 1.81747 -180 0 1 9.35833 30.5723" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 9.35833 37.8421 A 2.96392 1.81747 -180 0 1 9.35833 34.2072" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 9.35833 41.3771 A 2.96392 1.81747 -180 0 1 9.35833 37.7421" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.85833333333333" x2="13.85833333333334" y1="40.10833333333333" y2="40.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.85833333333333" x2="13.85833333333334" y1="39.10833333333333" y2="39.10833333333333"/>
   <path d="M 22.5 14.7417 A 6.84167 7.10597 -270 1 0 15.394 21.5833" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 22.4547 14.749 L 15.3041 14.749 L 15.3041 0.416667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(0,255,127)" fill-opacity="1" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <rect height="12.17" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.75,25.42) scale(1,1) translate(0,0)" width="6" x="8.75" y="19.33"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="11.5" y2="37.41666666666667"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_1" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <rect fill-opacity="0" height="8" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(255,0,0)" fill-opacity="1" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="7.333333333333334" y2="41.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
   <rect height="12.17" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.75,25.42) scale(1,1) translate(0,0)" width="6" x="8.75" y="19.33"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_2" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.75" x2="14.75" y1="17" y2="32"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.65" x2="8.65" y1="17.1" y2="32.1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(0,255,127)" fill-opacity="1" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="32.35" y2="41.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.71666666666667" x2="11.71666666666667" y1="17.05" y2="7.466666666666663"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
  </symbol>
  <symbol id="Accessory:空挂线路_0" viewBox="0,0,11,13">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="-0.0833333333333357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="12.91666666666667" y2="0.2500000000000009"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="Accessory:附属接地变_0" viewBox="0,0,15,15">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="1.5"/>
   <ellipse cx="7.57" cy="7.91" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.561718118722353" x2="7.561718118722353" y1="4.914465958746223" y2="7.513454058763156"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.1552656081938" x2="7.561718118722348" y1="10.11244215878007" y2="7.513454058763136"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.96817062925088" x2="7.561718118722333" y1="10.11244215878007" y2="7.513454058763136"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="EnergyConsumer:接地变2020_0" viewBox="0,0,20,22">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="-3.15"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.916666666666668" x2="17.41666666666667" y1="21.91666666666666" y2="16.56666666666666"/>
   <ellipse cx="9.800000000000001" cy="8.390000000000001" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.859999999999999" cy="14.38" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.926184092940137" x2="9.926184092940137" y1="13.05346745835017" y2="14.72522626390733"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.60625558534406" x2="9.926184092940133" y1="16.39698506946448" y2="14.72522626390732"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.246112600536197" x2="9.926184092940126" y1="16.39698506946448" y2="14.72522626390732"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.51666666666667" x2="15.31648793565684" y1="19.98286447224305" y2="19.98286447224305"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.6766309204647" x2="16.15652368185881" y1="20.81874387502165" y2="20.81874387502165"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.83659517426274" x2="16.99655942806077" y1="21.65462327780023" y2="21.65462327780023"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.926184092940138" x2="17.41666666666667" y1="14.72522626390732" y2="14.72522626390732"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.41657730116176" x2="17.41657730116176" y1="14.75" y2="19.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.01978169934642" x2="10.01978169934642" y1="21.75" y2="18.56024096385542"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.926184092940137" x2="9.926184092940137" y1="5.803467458350164" y2="7.475226263907325"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.60625558534406" x2="9.926184092940133" y1="9.146985069464476" y2="7.475226263907315"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.246112600536197" x2="9.926184092940126" y1="9.146985069464476" y2="7.475226263907315"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变D-Y_0" viewBox="0,0,23,39">
   <use terminal-index="0" type="0" x="7.666666666666666" xlink:href="#terminal" y="0.4166666666666714"/>
   <path d="M 7.5 4.25 L 4.58333 10.1667 L 10.8333 10.1667 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.42034 32.6694 L 7.64848 37.3333 L 7.05673 32.6694 L 7.72567 32.7212 z" fill="rgb(170,170,127)" fill-opacity="1" stroke="rgb(170,170,127)" stroke-dasharray="6 2 2 2" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 6.20768 29.4824 L 9.29511 29.4824 L 7.7514 31.037 L 6.20768 29.4824" fill="none" stroke="rgb(170,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.597025948103791" x2="7.597025948103791" y1="2.04293756914176" y2="0.4882945839350796"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.597025948103783" x2="19.17487025948103" y1="29.63785055656028" y2="21.32051058570456"/>
   <ellipse cx="7.42" cy="8.6" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.5" cy="17.92" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.611718118722353" x2="7.611718118722353" y1="15.85874008086165" y2="18.45772818087859"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.2052656081938" x2="7.61171811872235" y1="21.05671628089551" y2="18.45772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.018170629250884" x2="7.611718118722337" y1="21.05671628089551" y2="18.45772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.41666666666666" x2="15.93279794298801" y1="26.63147854022231" y2="26.63147854022231"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.11989292193093" x2="17.22957168772375" y1="27.93097259023087" y2="27.93097259023087"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8231191771952" x2="18.52634543245948" y1="29.23046664023932" y2="29.23046664023932"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.611718118722351" x2="19.17487025948103" y1="18.45772818087858" y2="18.45772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.17473230482734" x2="19.17473230482734" y1="18.49624249591244" y2="26.39901100404639"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.756205920707614" x2="7.756205920707614" y1="32.74713652697363" y2="24.41980688231738"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="梁河三晟110kV总降变" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="60.94" y="331" zvalue="1486"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="248" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,97.375,343) scale(1,1) translate(0,0)" width="72.88" x="60.94" y="331" zvalue="1486"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,97.375,343) scale(1,1) translate(0,0)" writing-mode="lr" x="97.38" xml:space="preserve" y="347.5" zvalue="1486">信号一览</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="269.25" x="47.96" xlink:href="logo.png" y="37.64"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,182.589,67.6429) scale(1,1) translate(0,0)" writing-mode="lr" x="182.59" xml:space="preserve" y="71.14" zvalue="1487"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,184.298,67.3332) scale(1,1) translate(7.32351e-15,0)" writing-mode="lr" x="184.3" xml:space="preserve" y="76.33" zvalue="1488">梁河三晟110kV总降变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,749.548,343.758) scale(1,1) translate(0,0)" writing-mode="lr" x="749.55" xml:space="preserve" y="348.26" zvalue="3">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,955.437,714.714) scale(1,1) translate(0,0)" writing-mode="lr" x="955.4400000000001" xml:space="preserve" y="719.21" zvalue="5">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1050.5,86.8122) scale(1,1) translate(0,0)" writing-mode="lr" x="1050.5" xml:space="preserve" y="91.31" zvalue="51">110kV槟晟线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="175" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1076.48,261.645) scale(1,1) translate(0,0)" writing-mode="lr" x="1076.48" xml:space="preserve" y="266.14" zvalue="52">131</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1069.12,198.754) scale(1,1) translate(0,0)" writing-mode="lr" x="1069.12" xml:space="preserve" y="203.25" zvalue="54">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1021.23,176.67) scale(1,1) translate(0,0)" writing-mode="lr" x="1021.23" xml:space="preserve" y="181.17" zvalue="56">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1069.26,328.549) scale(1,1) translate(0,0)" writing-mode="lr" x="1069.26" xml:space="preserve" y="333.05" zvalue="59">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="171" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1022.23,239.561) scale(1,1) translate(0,0)" writing-mode="lr" x="1022.23" xml:space="preserve" y="244.06" zvalue="63">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="170" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1022.23,311.601) scale(1,1) translate(0,0)" writing-mode="lr" x="1022.23" xml:space="preserve" y="316.1" zvalue="65">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,449.503,845.207) scale(1,1) translate(0,0)" writing-mode="lr" x="449.5" xml:space="preserve" y="849.71" zvalue="138">17</text>
  <line fill="none" id="537" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="374" x2="374" y1="13" y2="1043" zvalue="163"/>
  <line fill="none" id="535" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.250000000000455" x2="374.25" y1="613.8704926140824" y2="613.8704926140824" zvalue="164"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="56.85709999999995" y1="433.25" y2="433.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="56.85709999999995" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="5" y1="433.25" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85709999999995" x2="56.85709999999995" y1="433.25" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85699999999997" x2="108.7141" y1="433.25" y2="433.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85699999999997" x2="108.7141" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85699999999997" x2="56.85699999999997" y1="433.25" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7141" x2="108.7141" y1="433.25" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7143" x2="160.5714" y1="433.25" y2="433.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7143" x2="160.5714" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7143" x2="108.7143" y1="433.25" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="160.5714" y1="433.25" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="212.4285" y1="433.25" y2="433.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="212.4285" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="160.5714" y1="433.25" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4285" x2="212.4285" y1="433.25" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4286" x2="264.2857" y1="433.25" y2="433.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4286" x2="264.2857" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4286" x2="212.4286" y1="433.25" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="264.2857" y1="433.25" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="316.1428000000001" y1="433.25" y2="433.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="316.1428000000001" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="264.2857" y1="433.25" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.1428000000001" x2="316.1428000000001" y1="433.25" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.143" x2="368.0001" y1="433.25" y2="433.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.143" x2="368.0001" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.143" x2="316.143" y1="433.25" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0001" x2="368.0001" y1="433.25" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="56.85709999999995" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="56.85709999999995" y1="492.5834" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="5" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85709999999995" x2="56.85709999999995" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85699999999997" x2="108.7141" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85699999999997" x2="108.7141" y1="492.5834" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85699999999997" x2="56.85699999999997" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7141" x2="108.7141" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7143" x2="160.5714" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7143" x2="160.5714" y1="492.5834" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7143" x2="108.7143" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="160.5714" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="212.4285" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="212.4285" y1="492.5834" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="160.5714" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4285" x2="212.4285" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4286" x2="264.2857" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4286" x2="264.2857" y1="492.5834" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4286" x2="212.4286" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="264.2857" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="316.1428000000001" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="316.1428000000001" y1="492.5834" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="264.2857" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.1428000000001" x2="316.1428000000001" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.143" x2="368.0001" y1="465.9167" y2="465.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.143" x2="368.0001" y1="492.5834" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.143" x2="316.143" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0001" x2="368.0001" y1="465.9167" y2="492.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="56.85709999999995" y1="492.5833" y2="492.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="56.85709999999995" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="5" y1="492.5833" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85709999999995" x2="56.85709999999995" y1="492.5833" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85699999999997" x2="108.7141" y1="492.5833" y2="492.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85699999999997" x2="108.7141" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85699999999997" x2="56.85699999999997" y1="492.5833" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7141" x2="108.7141" y1="492.5833" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7143" x2="160.5714" y1="492.5833" y2="492.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7143" x2="160.5714" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7143" x2="108.7143" y1="492.5833" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="160.5714" y1="492.5833" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="212.4285" y1="492.5833" y2="492.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="212.4285" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="160.5714" y1="492.5833" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4285" x2="212.4285" y1="492.5833" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4286" x2="264.2857" y1="492.5833" y2="492.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4286" x2="264.2857" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4286" x2="212.4286" y1="492.5833" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="264.2857" y1="492.5833" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="316.1428000000001" y1="492.5833" y2="492.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="316.1428000000001" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="264.2857" y1="492.5833" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.1428000000001" x2="316.1428000000001" y1="492.5833" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.143" x2="368.0001" y1="492.5833" y2="492.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.143" x2="368.0001" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.143" x2="316.143" y1="492.5833" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0001" x2="368.0001" y1="492.5833" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="56.85709999999995" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="56.85709999999995" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="5" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85709999999995" x2="56.85709999999995" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85699999999997" x2="108.7141" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85699999999997" x2="108.7141" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85699999999997" x2="56.85699999999997" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7141" x2="108.7141" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7143" x2="160.5714" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7143" x2="160.5714" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7143" x2="108.7143" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="160.5714" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="212.4285" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="212.4285" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="160.5714" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4285" x2="212.4285" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4286" x2="264.2857" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4286" x2="264.2857" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4286" x2="212.4286" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="264.2857" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="316.1428000000001" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="316.1428000000001" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="264.2857" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.1428000000001" x2="316.1428000000001" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.143" x2="368.0001" y1="519.25" y2="519.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.143" x2="368.0001" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.143" x2="316.143" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0001" x2="368.0001" y1="519.25" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="56.85709999999995" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="56.85709999999995" y1="572.5834" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="5" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85709999999995" x2="56.85709999999995" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85699999999997" x2="108.7141" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85699999999997" x2="108.7141" y1="572.5834" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85699999999997" x2="56.85699999999997" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7141" x2="108.7141" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7143" x2="160.5714" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7143" x2="160.5714" y1="572.5834" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7143" x2="108.7143" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="160.5714" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="212.4285" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="212.4285" y1="572.5834" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="160.5714" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4285" x2="212.4285" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4286" x2="264.2857" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4286" x2="264.2857" y1="572.5834" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4286" x2="212.4286" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="264.2857" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="316.1428000000001" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="316.1428000000001" y1="572.5834" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="264.2857" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.1428000000001" x2="316.1428000000001" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.143" x2="368.0001" y1="545.9167" y2="545.9167"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.143" x2="368.0001" y1="572.5834" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.143" x2="316.143" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0001" x2="368.0001" y1="545.9167" y2="572.5834"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="56.85709999999995" y1="572.5833" y2="572.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="56.85709999999995" y1="599.25" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="5" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85709999999995" x2="56.85709999999995" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85699999999997" x2="108.7141" y1="572.5833" y2="572.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85699999999997" x2="108.7141" y1="599.25" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.85699999999997" x2="56.85699999999997" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7141" x2="108.7141" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7143" x2="160.5714" y1="572.5833" y2="572.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7143" x2="160.5714" y1="599.25" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="108.7143" x2="108.7143" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="160.5714" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="212.4285" y1="572.5833" y2="572.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="212.4285" y1="599.25" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="160.5714" x2="160.5714" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4285" x2="212.4285" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4286" x2="264.2857" y1="572.5833" y2="572.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4286" x2="264.2857" y1="599.25" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.4286" x2="212.4286" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="264.2857" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="316.1428000000001" y1="572.5833" y2="572.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="316.1428000000001" y1="599.25" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="264.2857" x2="264.2857" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.1428000000001" x2="316.1428000000001" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.143" x2="368.0001" y1="572.5833" y2="572.5833"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.143" x2="368.0001" y1="599.25" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.143" x2="316.143" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0001" x2="368.0001" y1="572.5833" y2="599.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.25" x2="93.25" y1="927" y2="927"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.25" x2="93.25" y1="979.1632999999999" y2="979.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.25" x2="3.25" y1="927" y2="979.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.25" x2="93.25" y1="927" y2="979.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.25" x2="363.25" y1="927" y2="927"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.25" x2="363.25" y1="979.1632999999999" y2="979.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.25" x2="93.25" y1="927" y2="979.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.25" x2="363.25" y1="927" y2="979.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.25" x2="93.25" y1="979.16327" y2="979.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.25" x2="93.25" y1="1007.08167" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.25" x2="3.25" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.25" x2="93.25" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.25" x2="183.25" y1="979.16327" y2="979.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.25" x2="183.25" y1="1007.08167" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.25" x2="93.25" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.25" x2="183.25" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2500000000001" x2="273.2500000000001" y1="979.16327" y2="979.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2500000000001" x2="273.2500000000001" y1="1007.08167" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2500000000001" x2="183.2500000000001" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2500000000001" x2="273.2500000000001" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.25" x2="363.25" y1="979.16327" y2="979.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.25" x2="363.25" y1="1007.08167" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.25" x2="273.25" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.25" x2="363.25" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.25" x2="93.25" y1="1007.0816" y2="1007.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.25" x2="93.25" y1="1035" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.25" x2="3.25" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.25" x2="93.25" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.25" x2="183.25" y1="1007.0816" y2="1007.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.25" x2="183.25" y1="1035" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.25" x2="93.25" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.25" x2="183.25" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2500000000001" x2="273.2500000000001" y1="1007.0816" y2="1007.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2500000000001" x2="273.2500000000001" y1="1035" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2500000000001" x2="183.2500000000001" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2500000000001" x2="273.2500000000001" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.25" x2="363.25" y1="1007.0816" y2="1007.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.25" x2="363.25" y1="1035" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.25" x2="273.25" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.25" x2="363.25" y1="1007.0816" y2="1035"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="531" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,183.625,955.25) scale(1,1) translate(0,0)" writing-mode="lr" x="20.25" xml:space="preserve" y="961.25" zvalue="168">参考图号           梁河三晟110kV总降变 </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="528" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47,1022) scale(1,1) translate(0,0)" writing-mode="lr" x="47" xml:space="preserve" y="1028" zvalue="171">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="527" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229,1022) scale(1,1) translate(0,0)" writing-mode="lr" x="229" xml:space="preserve" y="1028" zvalue="172">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="526" stroke="rgb(255,255,255)" text-anchor="middle" x="186" xml:space="preserve" y="447.75" zvalue="173">35kV      Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="526" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="186" xml:space="preserve" y="463.75" zvalue="173">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="525" stroke="rgb(255,255,255)" text-anchor="middle" x="236.671875" xml:space="preserve" y="447.25" zvalue="174">35kV     Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="525" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="236.671875" xml:space="preserve" y="463.25" zvalue="174">母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="524" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,31,482.75) scale(1,1) translate(0,0)" writing-mode="lr" x="31" xml:space="preserve" y="487.25" zvalue="175">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="523" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,31,508.25) scale(1,1) translate(0,0)" writing-mode="lr" x="31" xml:space="preserve" y="512.75" zvalue="176">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="522" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,31,533.75) scale(1,1) translate(0,0)" writing-mode="lr" x="31" xml:space="preserve" y="538.25" zvalue="177">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="521" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,31,559.25) scale(1,1) translate(0,0)" writing-mode="lr" x="31" xml:space="preserve" y="563.75" zvalue="178">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="512" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,31,584.75) scale(1,1) translate(0,0)" writing-mode="lr" x="31" xml:space="preserve" y="589.25" zvalue="179">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="511" stroke="rgb(255,255,255)" text-anchor="middle" x="82" xml:space="preserve" y="447.75" zvalue="180">110kV   Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="511" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="82" xml:space="preserve" y="463.75" zvalue="180">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="510" stroke="rgb(255,255,255)" text-anchor="middle" x="291" xml:space="preserve" y="447.75" zvalue="181">10kV      Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="510" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="291" xml:space="preserve" y="463.75" zvalue="181">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="509" stroke="rgb(255,255,255)" text-anchor="middle" x="341.671875" xml:space="preserve" y="447.25" zvalue="182">10kV      Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="509" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="341.671875" xml:space="preserve" y="463.25" zvalue="182">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="508" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,212.399,342.5) scale(1,1) translate(0,0)" writing-mode="lr" x="212.4" xml:space="preserve" y="347" zvalue="183">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="506" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,300.399,342.5) scale(1,1) translate(0,0)" writing-mode="lr" x="300.4" xml:space="preserve" y="347" zvalue="184">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="504" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.25,635.25) scale(1,1) translate(0,0)" writing-mode="lr" x="73.25" xml:space="preserve" y="639.75" zvalue="186">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,444.321,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="444.32" xml:space="preserve" y="779.28" zvalue="471">032</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="217" stroke="rgb(255,255,255)" text-anchor="middle" x="136.671875" xml:space="preserve" y="446.25" zvalue="681">110kV     Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="217" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="136.671875" xml:space="preserve" y="462.25" zvalue="681">母</text>
  <line fill="none" id="213" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.7500000000004547" x2="368.75" y1="146.3704926140824" y2="146.3704926140824" zvalue="685"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.75" x2="183.75" y1="155.5" y2="155.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.75" x2="183.75" y1="181.5" y2="181.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.75" x2="2.75" y1="155.5" y2="181.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="183.75" y1="155.5" y2="181.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="364.75" y1="155.5" y2="155.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="364.75" y1="181.5" y2="181.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="183.75" y1="155.5" y2="181.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.75" x2="364.75" y1="155.5" y2="181.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.75" x2="183.75" y1="181.5" y2="181.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.75" x2="183.75" y1="205.75" y2="205.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.75" x2="2.75" y1="181.5" y2="205.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="183.75" y1="181.5" y2="205.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="364.75" y1="181.5" y2="181.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="364.75" y1="205.75" y2="205.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="183.75" y1="181.5" y2="205.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.75" x2="364.75" y1="181.5" y2="205.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.75" x2="183.75" y1="205.75" y2="205.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.75" x2="183.75" y1="230" y2="230"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.75" x2="2.75" y1="205.75" y2="230"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="183.75" y1="205.75" y2="230"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="364.75" y1="205.75" y2="205.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="364.75" y1="230" y2="230"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="183.75" y1="205.75" y2="230"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.75" x2="364.75" y1="205.75" y2="230"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.75" x2="183.75" y1="230" y2="230"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.75" x2="183.75" y1="252.75" y2="252.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.75" x2="2.75" y1="230" y2="252.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="183.75" y1="230" y2="252.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="364.75" y1="230" y2="230"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="364.75" y1="252.75" y2="252.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="183.75" y1="230" y2="252.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.75" x2="364.75" y1="230" y2="252.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.75" x2="183.75" y1="252.75" y2="252.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.75" x2="183.75" y1="275.5" y2="275.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.75" x2="2.75" y1="252.75" y2="275.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="183.75" y1="252.75" y2="275.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="364.75" y1="252.75" y2="252.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="364.75" y1="275.5" y2="275.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="183.75" y1="252.75" y2="275.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.75" x2="364.75" y1="252.75" y2="275.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.75" x2="183.75" y1="275.5" y2="275.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.75" x2="183.75" y1="298.25" y2="298.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.75" x2="2.75" y1="275.5" y2="298.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="183.75" y1="275.5" y2="298.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="364.75" y1="275.5" y2="275.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="364.75" y1="298.25" y2="298.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.75" x2="183.75" y1="275.5" y2="298.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.75" x2="364.75" y1="275.5" y2="298.25"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="211" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,40.75,169.5) scale(1,1) translate(0,0)" writing-mode="lr" x="40.75" xml:space="preserve" y="175" zvalue="687">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="210" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,220.75,169.5) scale(1,1) translate(0,0)" writing-mode="lr" x="220.75" xml:space="preserve" y="175" zvalue="688">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43.4375,194.75) scale(1,1) translate(0,0)" writing-mode="lr" x="43.44" xml:space="preserve" y="199.25" zvalue="689">110kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.5,194.5) scale(1,1) translate(0,0)" writing-mode="lr" x="226.5" xml:space="preserve" y="199" zvalue="690">110kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.9375,265.5) scale(1,1) translate(0,0)" writing-mode="lr" x="55.94" xml:space="preserve" y="270" zvalue="691">110kV#1主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="206" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,239.5,265) scale(1,1) translate(0,0)" writing-mode="lr" x="239.5" xml:space="preserve" y="269.5" zvalue="692">110kV#2主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="205" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.9375,288.5) scale(1,1) translate(0,0)" writing-mode="lr" x="57.94" xml:space="preserve" y="293" zvalue="693">110kV#1主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,238.25,288) scale(1,1) translate(0,0)" writing-mode="lr" x="238.25" xml:space="preserve" y="292.5" zvalue="694">110kV#2主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43.4375,219.75) scale(1,1) translate(0,0)" writing-mode="lr" x="43.44" xml:space="preserve" y="224.25" zvalue="695">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.5,219.5) scale(1,1) translate(0,0)" writing-mode="lr" x="226.5" xml:space="preserve" y="224" zvalue="696">35kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.6875,242.25) scale(1,1) translate(0,0)" writing-mode="lr" x="44.69" xml:space="preserve" y="246.75" zvalue="697">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.5,243.25) scale(1,1) translate(0,0)" writing-mode="lr" x="226.5" xml:space="preserve" y="247.75" zvalue="698">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="665" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,422.104,960.518) scale(1,1) translate(0,0)" writing-mode="lr" x="422.1" xml:space="preserve" y="965.02" zvalue="710">出线1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="677" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,521.503,845.207) scale(1,1) translate(0,0)" writing-mode="lr" x="521.5" xml:space="preserve" y="849.71" zvalue="720">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="676" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,516.321,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="516.3200000000001" xml:space="preserve" y="779.28" zvalue="723">033</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="692" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,593.503,845.207) scale(1,1) translate(0,0)" writing-mode="lr" x="593.5" xml:space="preserve" y="849.71" zvalue="736">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="691" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,588.321,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="588.3200000000001" xml:space="preserve" y="779.28" zvalue="739">034</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="707" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,661.503,845.207) scale(1,1) translate(0,0)" writing-mode="lr" x="661.5" xml:space="preserve" y="849.71" zvalue="752">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="706" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,656.321,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="656.3200000000001" xml:space="preserve" y="779.28" zvalue="755">035</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="724" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,729.503,845.207) scale(1,1) translate(0,0)" writing-mode="lr" x="729.5" xml:space="preserve" y="849.71" zvalue="768">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="723" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,724.321,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="724.3200000000001" xml:space="preserve" y="779.28" zvalue="771">036</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="740" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,793.503,845.207) scale(1,1) translate(0,0)" writing-mode="lr" x="793.5" xml:space="preserve" y="849.71" zvalue="784">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="739" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,788.321,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="788.3200000000001" xml:space="preserve" y="779.28" zvalue="787">037</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="755" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,861.503,845.207) scale(1,1) translate(0,0)" writing-mode="lr" x="861.5" xml:space="preserve" y="849.71" zvalue="800">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="754" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,856.321,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="856.3200000000001" xml:space="preserve" y="779.28" zvalue="803">038</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="770" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,929.503,845.207) scale(1,1) translate(0,0)" writing-mode="lr" x="929.5" xml:space="preserve" y="849.71" zvalue="816">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="769" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,924.321,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="924.3200000000001" xml:space="preserve" y="779.28" zvalue="819">039</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="788" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,997.503,845.207) scale(1,1) translate(0,0)" writing-mode="lr" x="997.5" xml:space="preserve" y="849.71" zvalue="832">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="786" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,992.321,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="992.3200000000001" xml:space="preserve" y="779.28" zvalue="835">041</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="805" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1065.5,845.207) scale(1,1) translate(0,0)" writing-mode="lr" x="1065.5" xml:space="preserve" y="849.71" zvalue="848">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="804" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1060.32,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1060.32" xml:space="preserve" y="779.28" zvalue="851">042</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1003" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1668,714.841) scale(1,1) translate(0,0)" writing-mode="lr" x="1668" xml:space="preserve" y="719.34" zvalue="864">10kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="867" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1205.5,845.207) scale(1,1) translate(0,0)" writing-mode="lr" x="1205.5" xml:space="preserve" y="849.71" zvalue="865">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="864" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1200.32,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1200.32" xml:space="preserve" y="779.28" zvalue="868">046</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="859" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1277.5,845.207) scale(1,1) translate(0,0)" writing-mode="lr" x="1277.5" xml:space="preserve" y="849.71" zvalue="880">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="858" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1272.32,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1272.32" xml:space="preserve" y="779.28" zvalue="883">047</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="856" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1349.5,845.207) scale(1,1) translate(0,0)" writing-mode="lr" x="1349.5" xml:space="preserve" y="849.71" zvalue="895">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="855" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1344.32,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1344.32" xml:space="preserve" y="779.28" zvalue="898">048</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="853" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1417.5,845.207) scale(1,1) translate(0,0)" writing-mode="lr" x="1417.5" xml:space="preserve" y="849.71" zvalue="910">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="852" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1412.72,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1412.72" xml:space="preserve" y="779.28" zvalue="913">049</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="850" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1485.5,845.207) scale(1,1) translate(0,0)" writing-mode="lr" x="1485.5" xml:space="preserve" y="849.71" zvalue="925">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="848" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1480.32,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1480.32" xml:space="preserve" y="779.28" zvalue="928">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="845" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1549.5,845.207) scale(1,1) translate(0,0)" writing-mode="lr" x="1549.5" xml:space="preserve" y="849.71" zvalue="940">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="842" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1544.32,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1544.32" xml:space="preserve" y="779.28" zvalue="943">052</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="838" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1618.5,844.207) scale(1,1) translate(0,0)" writing-mode="lr" x="1618.5" xml:space="preserve" y="848.71" zvalue="955">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="837" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1612.32,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1612.32" xml:space="preserve" y="779.28" zvalue="958">053</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="835" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1685.5,845.207) scale(1,1) translate(0,0)" writing-mode="lr" x="1685.5" xml:space="preserve" y="849.71" zvalue="970">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="834" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1680.32,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1680.32" xml:space="preserve" y="779.28" zvalue="973">054</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="828" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1753.5,845.207) scale(1,1) translate(0,0)" writing-mode="lr" x="1753.5" xml:space="preserve" y="849.71" zvalue="985">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="826" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1748.32,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1748.32" xml:space="preserve" y="779.28" zvalue="988">055</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="824" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1821.5,845.207) scale(1,1) translate(0,0)" writing-mode="lr" x="1821.5" xml:space="preserve" y="849.71" zvalue="1000">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="823" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1816.32,774.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1816.32" xml:space="preserve" y="779.28" zvalue="1003">056</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1019" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,481.706,684.996) scale(1,1) translate(0,0)" writing-mode="lr" x="481.71" xml:space="preserve" y="689.5" zvalue="1020">031</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1011" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,435.229,444.702) scale(1,1) translate(0,0)" writing-mode="lr" x="435.23" xml:space="preserve" y="449.2" zvalue="1022">#1接地变兼站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1020" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,415.507,615.164) scale(1,1) translate(0,0)" writing-mode="lr" x="415.51" xml:space="preserve" y="619.66" zvalue="1025">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1021" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,399.871,530.857) scale(1,1) translate(0,0)" writing-mode="lr" x="399.87" xml:space="preserve" y="535.36" zvalue="1028">0010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1023" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,588.353,684.996) scale(1,1) translate(0,0)" writing-mode="lr" x="588.35" xml:space="preserve" y="689.5" zvalue="1033">044</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1022" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,515.507,615.164) scale(1,1) translate(0,0)" writing-mode="lr" x="515.51" xml:space="preserve" y="619.66" zvalue="1036">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1035" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,547.45,566.857) scale(1,1) translate(0,0)" writing-mode="lr" x="547.45" xml:space="preserve" y="571.36" zvalue="1043">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1051" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,682.172,529.875) scale(1,1) translate(0,0)" writing-mode="lr" x="682.17" xml:space="preserve" y="534.38" zvalue="1064">10kVⅠ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1056" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,788.479,457.645) scale(1,1) translate(0,0)" writing-mode="lr" x="788.48" xml:space="preserve" y="462.14" zvalue="1066">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1055" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,837.123,394.754) scale(1,1) translate(0,0)" writing-mode="lr" x="837.12" xml:space="preserve" y="399.25" zvalue="1068">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1054" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,837.257,512.549) scale(1,1) translate(0,0)" writing-mode="lr" x="837.26" xml:space="preserve" y="517.05" zvalue="1072">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1053" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,774.232,435.561) scale(1,1) translate(0,0)" writing-mode="lr" x="774.23" xml:space="preserve" y="440.06" zvalue="1074">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1052" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,774.741,502.351) scale(1,1) translate(0,0)" writing-mode="lr" x="774.74" xml:space="preserve" y="506.85" zvalue="1076">60</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1075" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,773.901,585.75) scale(1,1) translate(0,0)" writing-mode="lr" x="773.9" xml:space="preserve" y="590.25" zvalue="1088">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1079" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,773.125,553.581) scale(1,1) translate(1.6942e-13,1.21032e-13)" writing-mode="lr" x="773.12" xml:space="preserve" y="558.08" zvalue="1091">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1085" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,783.464,673.746) scale(1,1) translate(-1.71196e-13,0)" writing-mode="lr" x="783.46" xml:space="preserve" y="678.25" zvalue="1094">001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,555.069,413.288) scale(1,1) translate(0,0)" writing-mode="lr" x="555.0700000000001" xml:space="preserve" y="417.79" zvalue="1101">#1电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,503.35,504.627) scale(1,1) translate(0,0)" writing-mode="lr" x="503.35" xml:space="preserve" y="509.13" zvalue="1102">04410</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,634.798,661.5) scale(1,1) translate(0,0)" writing-mode="lr" x="634.8" xml:space="preserve" y="666" zvalue="1108">0901</text>
  
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1745.71,684.996) scale(1,1) translate(0,0)" writing-mode="lr" x="1745.71" xml:space="preserve" y="689.5" zvalue="1131">057</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1675.51,615.164) scale(1,1) translate(0,0)" writing-mode="lr" x="1675.51" xml:space="preserve" y="619.66" zvalue="1133">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1707.45,566.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1707.45" xml:space="preserve" y="571.36" zvalue="1136">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1715.07,413.288) scale(1,1) translate(0,0)" writing-mode="lr" x="1715.07" xml:space="preserve" y="417.79" zvalue="1143">#2电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1661.85,504.627) scale(1,1) translate(0,0)" writing-mode="lr" x="1661.85" xml:space="preserve" y="509.13" zvalue="1145">05710</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1028.25,551) scale(1,1) translate(0,0)" writing-mode="lr" x="1028.25" xml:space="preserve" y="555.5" zvalue="1150">备用1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1812.25,523) scale(1,1) translate(0,0)" writing-mode="lr" x="1812.25" xml:space="preserve" y="527.5" zvalue="1160">备用2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,926,576.983) scale(1,1) translate(0,0)" writing-mode="lr" x="926" xml:space="preserve" y="581.48" zvalue="1163">1010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1592.67,529.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1592.67" xml:space="preserve" y="534.38" zvalue="1169">10kVⅡ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1539.9,661.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1539.9" xml:space="preserve" y="666" zvalue="1171">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1356.48,457.645) scale(1,1) translate(0,0)" writing-mode="lr" x="1356.48" xml:space="preserve" y="462.14" zvalue="1178">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1403.97,394.754) scale(1,1) translate(0,0)" writing-mode="lr" x="1403.97" xml:space="preserve" y="399.25" zvalue="1180">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1405.26,512.549) scale(1,1) translate(0,0)" writing-mode="lr" x="1405.26" xml:space="preserve" y="517.05" zvalue="1182">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1342.23,435.561) scale(1,1) translate(0,0)" writing-mode="lr" x="1342.23" xml:space="preserve" y="440.06" zvalue="1184">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1343.24,502.351) scale(1,1) translate(0,0)" writing-mode="lr" x="1343.24" xml:space="preserve" y="506.85" zvalue="1186">60</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1341.9,585.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1341.9" xml:space="preserve" y="590.25" zvalue="1194">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1343.12,553.581) scale(1,1) translate(2.9643e-13,1.21032e-13)" writing-mode="lr" x="1343.12" xml:space="preserve" y="558.08" zvalue="1196">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1351.96,675.746) scale(1,1) translate(-2.97096e-13,0)" writing-mode="lr" x="1351.96" xml:space="preserve" y="680.25" zvalue="1200">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1494,576.983) scale(1,1) translate(0,0)" writing-mode="lr" x="1494" xml:space="preserve" y="581.48" zvalue="1205">1020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1257.71,684.996) scale(1,1) translate(0,0)" writing-mode="lr" x="1257.71" xml:space="preserve" y="689.5" zvalue="1210">045</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1191.51,615.164) scale(1,1) translate(0,0)" writing-mode="lr" x="1191.51" xml:space="preserve" y="619.66" zvalue="1217">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1201.48,557.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1201.48" xml:space="preserve" y="562.36" zvalue="1220">0020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1272,588.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1272" xml:space="preserve" y="593" zvalue="1226">#2接地变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1014.12,419.754) scale(1,1) translate(2.21961e-13,0)" writing-mode="lr" x="1014.12" xml:space="preserve" y="424.25" zvalue="1232">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,954.232,404.316) scale(1,1) translate(0,3.51555e-13)" writing-mode="lr" x="954.23" xml:space="preserve" y="408.82" zvalue="1234">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,949.999,476.791) scale(1,1) translate(0,0)" writing-mode="lr" x="950" xml:space="preserve" y="481.29" zvalue="1236">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,964.201,524.125) scale(1,1) translate(0,0)" writing-mode="lr" x="964.2005050454998" xml:space="preserve" y="528.625" zvalue="1241">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,480.5,336.75) scale(1,1) translate(0,0)" writing-mode="lr" x="480.5" xml:space="preserve" y="341.25" zvalue="1245">施工电源变（兼#2站用变）</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,467.037,146.5) scale(1,1) translate(0,0)" writing-mode="lr" x="467.04" xml:space="preserve" y="151" zvalue="1254">T接10kV大和线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,487.848,960.518) scale(1,1) translate(0,0)" writing-mode="lr" x="487.85" xml:space="preserve" y="965.02" zvalue="1345">出线2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="254" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,560.848,960.518) scale(1,1) translate(0,0)" writing-mode="lr" x="560.85" xml:space="preserve" y="965.02" zvalue="1347">出线3</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,634.848,960.518) scale(1,1) translate(0,0)" writing-mode="lr" x="634.85" xml:space="preserve" y="965.02" zvalue="1349">出线4</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,701.848,960.518) scale(1,1) translate(0,0)" writing-mode="lr" x="701.85" xml:space="preserve" y="965.02" zvalue="1351">出线5</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,762.848,960.518) scale(1,1) translate(0,0)" writing-mode="lr" x="762.85" xml:space="preserve" y="965.02" zvalue="1353">出线6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,833.848,960.518) scale(1,1) translate(-3.62531e-13,0)" writing-mode="lr" x="833.85" xml:space="preserve" y="965.02" zvalue="1355">出线7</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,898.848,960.518) scale(1,1) translate(0,0)" writing-mode="lr" x="898.85" xml:space="preserve" y="965.02" zvalue="1357">出线8</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,968.848,960.518) scale(1,1) translate(0,0)" writing-mode="lr" x="968.85" xml:space="preserve" y="965.02" zvalue="1359">出线9</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="267" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1036.85,960.518) scale(1,1) translate(0,0)" writing-mode="lr" x="1036.85" xml:space="preserve" y="965.02" zvalue="1361">出线10</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="270" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1176.85,960.518) scale(1,1) translate(0,0)" writing-mode="lr" x="1176.85" xml:space="preserve" y="965.02" zvalue="1365">出线12</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="273" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1250.85,960.518) scale(1,1) translate(0,0)" writing-mode="lr" x="1250.85" xml:space="preserve" y="965.02" zvalue="1369">出线13</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="276" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1324.85,960.518) scale(1,1) translate(0,0)" writing-mode="lr" x="1324.85" xml:space="preserve" y="965.02" zvalue="1374">出线14</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="278" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1395.85,960.518) scale(1,1) translate(0,0)" writing-mode="lr" x="1395.85" xml:space="preserve" y="965.02" zvalue="1377">出线15</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="281" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1464.85,960.518) scale(1,1) translate(0,0)" writing-mode="lr" x="1464.85" xml:space="preserve" y="965.02" zvalue="1382">出线16</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1529.85,960.518) scale(1,1) translate(0,0)" writing-mode="lr" x="1529.85" xml:space="preserve" y="965.02" zvalue="1385">出线17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1597.85,960.518) scale(1,1) translate(0,0)" writing-mode="lr" x="1597.85" xml:space="preserve" y="965.02" zvalue="1388">出线18</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1668.85,960.518) scale(1,1) translate(0,0)" writing-mode="lr" x="1668.85" xml:space="preserve" y="965.02" zvalue="1391">出线19</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1735.85,960.518) scale(1,1) translate(0,0)" writing-mode="lr" x="1735.85" xml:space="preserve" y="965.02" zvalue="1394">出线20</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1803.85,960.518) scale(1,1) translate(0,0)" writing-mode="lr" x="1803.85" xml:space="preserve" y="965.02" zvalue="1397">出线21</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="215" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,774,609.5) scale(1,1) translate(0,0)" writing-mode="lr" x="774" xml:space="preserve" y="614" zvalue="1492">50MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="216" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1345,605.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1345" xml:space="preserve" y="610" zvalue="1494">50MVA</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="657">
   <path class="kv110" d="M 682.5 362.34 L 1572.5 362.34" stroke-width="6" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674235318276" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674235318276"/></metadata>
  <path d="M 682.5 362.34 L 1572.5 362.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="656">
   <path class="kv10" d="M 404.22 736.09 L 1090 736.09" stroke-width="6" zvalue="4"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674235252741" ObjectName="10kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674235252741"/></metadata>
  <path d="M 404.22 736.09 L 1090 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1002">
   <path class="kv10" d="M 1160.22 736.09 L 1846 736.09" stroke-width="6" zvalue="863"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674235383812" ObjectName="10kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674235383812"/></metadata>
  <path d="M 1160.22 736.09 L 1846 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="637">
   <use class="kv110" height="30" transform="rotate(0,1051.04,107.844) scale(1.98323,0.522926) translate(-517.633,91.2315)" width="7" x="1044.095748431563" xlink:href="#ACLineSegment:线路_0" y="99.99998993839381" zvalue="49"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449590853638" ObjectName="110kV槟晟线"/>
   <cge:TPSR_Ref TObjectID="6192449590853638_5066549582430210"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1051.04,107.844) scale(1.98323,0.522926) translate(-517.633,91.2315)" width="7" x="1044.095748431563" y="99.99998993839381"/></g>
 </g>
 <g id="BreakerClass">
  <g id="635">
   <use class="kv110" height="20" transform="rotate(0,1049.87,262.02) scale(1.5542,1.35421) translate(-371.595,-64.9925)" width="10" x="1042.103522042795" xlink:href="#Breaker:开关_0" y="248.4779758822602" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924478173190" ObjectName="110kV槟晟线131断路器"/>
   <cge:TPSR_Ref TObjectID="6473924478173190"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1049.87,262.02) scale(1.5542,1.35421) translate(-371.595,-64.9925)" width="10" x="1042.103522042795" y="248.4779758822602"/></g>
  <g id="312">
   <use class="kv10" height="20" transform="rotate(0,418.75,775.746) scale(1.64127,1.64127) translate(-160.406,-296.683)" width="10" x="410.5436507936508" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="470"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924478107654" ObjectName="出线1032断路器"/>
   <cge:TPSR_Ref TObjectID="6473924478107654"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,418.75,775.746) scale(1.64127,1.64127) translate(-160.406,-296.683)" width="10" x="410.5436507936508" y="759.3333401944903"/></g>
  <g id="687">
   <use class="kv10" height="20" transform="rotate(0,490.75,775.746) scale(1.64127,1.64127) translate(-188.537,-296.683)" width="10" x="482.5436507936508" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="722"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924478238726" ObjectName="出线2033断路器"/>
   <cge:TPSR_Ref TObjectID="6473924478238726"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,490.75,775.746) scale(1.64127,1.64127) translate(-188.537,-296.683)" width="10" x="482.5436507936508" y="759.3333401944903"/></g>
  <g id="702">
   <use class="kv10" height="20" transform="rotate(0,562.75,775.746) scale(1.64127,1.64127) translate(-216.669,-296.683)" width="10" x="554.5436507936508" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="738"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924478304262" ObjectName="出线3034断路器"/>
   <cge:TPSR_Ref TObjectID="6473924478304262"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,562.75,775.746) scale(1.64127,1.64127) translate(-216.669,-296.683)" width="10" x="554.5436507936508" y="759.3333401944903"/></g>
  <g id="719">
   <use class="kv10" height="20" transform="rotate(0,630.75,775.746) scale(1.64127,1.64127) translate(-243.238,-296.683)" width="10" x="622.5436507936508" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="754"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924478369797" ObjectName="出线4035断路器"/>
   <cge:TPSR_Ref TObjectID="6473924478369797"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,630.75,775.746) scale(1.64127,1.64127) translate(-243.238,-296.683)" width="10" x="622.5436507936508" y="759.3333401944903"/></g>
  <g id="734">
   <use class="kv10" height="20" transform="rotate(0,698.75,775.746) scale(1.64127,1.64127) translate(-269.806,-296.683)" width="10" x="690.5436507936508" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="770"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924478435333" ObjectName="出线5036断路器"/>
   <cge:TPSR_Ref TObjectID="6473924478435333"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,698.75,775.746) scale(1.64127,1.64127) translate(-269.806,-296.683)" width="10" x="690.5436507936508" y="759.3333401944903"/></g>
  <g id="750">
   <use class="kv10" height="20" transform="rotate(0,762.75,775.746) scale(1.64127,1.64127) translate(-294.812,-296.683)" width="10" x="754.5436507936508" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="786"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924478500869" ObjectName="出线6037断路器"/>
   <cge:TPSR_Ref TObjectID="6473924478500869"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,762.75,775.746) scale(1.64127,1.64127) translate(-294.812,-296.683)" width="10" x="754.5436507936508" y="759.3333401944903"/></g>
  <g id="765">
   <use class="kv10" height="20" transform="rotate(0,830.75,775.746) scale(1.64127,1.64127) translate(-321.381,-296.683)" width="10" x="822.5436507936508" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="802"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924478566405" ObjectName="出线7038断路器"/>
   <cge:TPSR_Ref TObjectID="6473924478566405"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,830.75,775.746) scale(1.64127,1.64127) translate(-321.381,-296.683)" width="10" x="822.5436507936508" y="759.3333401944903"/></g>
  <g id="780">
   <use class="kv10" height="20" transform="rotate(0,898.75,775.746) scale(1.64127,1.64127) translate(-347.949,-296.683)" width="10" x="890.5436507936508" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="818"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924478631941" ObjectName="出线8039断路器"/>
   <cge:TPSR_Ref TObjectID="6473924478631941"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,898.75,775.746) scale(1.64127,1.64127) translate(-347.949,-296.683)" width="10" x="890.5436507936508" y="759.3333401944903"/></g>
  <g id="800">
   <use class="kv10" height="20" transform="rotate(0,966.75,775.746) scale(1.64127,1.64127) translate(-374.518,-296.683)" width="10" x="958.5436507936508" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="834"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924478697477" ObjectName="出线9041断路器"/>
   <cge:TPSR_Ref TObjectID="6473924478697477"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,966.75,775.746) scale(1.64127,1.64127) translate(-374.518,-296.683)" width="10" x="958.5436507936508" y="759.3333401944903"/></g>
  <g id="817">
   <use class="kv10" height="20" transform="rotate(0,1034.75,775.746) scale(1.64127,1.64127) translate(-401.087,-296.683)" width="10" x="1026.543650793651" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="850"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924478763013" ObjectName="出线10042断路器"/>
   <cge:TPSR_Ref TObjectID="6473924478763013"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1034.75,775.746) scale(1.64127,1.64127) translate(-401.087,-296.683)" width="10" x="1026.543650793651" y="759.3333401944903"/></g>
  <g id="999">
   <use class="kv10" height="20" transform="rotate(0,1174.75,775.746) scale(1.64127,1.64127) translate(-455.787,-296.683)" width="10" x="1166.543650793651" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="867"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924479418373" ObjectName="出线12046断路器"/>
   <cge:TPSR_Ref TObjectID="6473924479418373"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1174.75,775.746) scale(1.64127,1.64127) translate(-455.787,-296.683)" width="10" x="1166.543650793651" y="759.3333401944903"/></g>
  <g id="987">
   <use class="kv10" height="20" transform="rotate(0,1246.75,775.746) scale(1.64127,1.64127) translate(-483.918,-296.683)" width="10" x="1238.543650793651" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="882"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924479352837" ObjectName="出线13047断路器"/>
   <cge:TPSR_Ref TObjectID="6473924479352837"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1246.75,775.746) scale(1.64127,1.64127) translate(-483.918,-296.683)" width="10" x="1238.543650793651" y="759.3333401944903"/></g>
  <g id="975">
   <use class="kv10" height="20" transform="rotate(0,1318.75,775.746) scale(1.64127,1.64127) translate(-512.05,-296.683)" width="10" x="1310.543650793651" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="897"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924479287301" ObjectName="出线14048断路器"/>
   <cge:TPSR_Ref TObjectID="6473924479287301"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1318.75,775.746) scale(1.64127,1.64127) translate(-512.05,-296.683)" width="10" x="1310.543650793651" y="759.3333401944903"/></g>
  <g id="963">
   <use class="kv10" height="20" transform="rotate(0,1387.15,775.746) scale(1.64127,1.64127) translate(-538.775,-296.683)" width="10" x="1378.944908735551" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="912"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924479221765" ObjectName="出线15049断路器"/>
   <cge:TPSR_Ref TObjectID="6473924479221765"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1387.15,775.746) scale(1.64127,1.64127) translate(-538.775,-296.683)" width="10" x="1378.944908735551" y="759.3333401944903"/></g>
  <g id="951">
   <use class="kv10" height="20" transform="rotate(0,1454.75,775.746) scale(1.64127,1.64127) translate(-565.187,-296.683)" width="10" x="1446.543650793651" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="927"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924479156229" ObjectName="出线16051断路器"/>
   <cge:TPSR_Ref TObjectID="6473924479156229"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1454.75,775.746) scale(1.64127,1.64127) translate(-565.187,-296.683)" width="10" x="1446.543650793651" y="759.3333401944903"/></g>
  <g id="936">
   <use class="kv10" height="20" transform="rotate(0,1518.75,775.746) scale(1.64127,1.64127) translate(-590.193,-296.683)" width="10" x="1510.543650793651" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="942"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924479090693" ObjectName="出线17052断路器"/>
   <cge:TPSR_Ref TObjectID="6473924479090693"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1518.75,775.746) scale(1.64127,1.64127) translate(-590.193,-296.683)" width="10" x="1510.543650793651" y="759.3333401944903"/></g>
  <g id="924">
   <use class="kv10" height="20" transform="rotate(0,1586.75,775.746) scale(1.64127,1.64127) translate(-616.762,-296.683)" width="10" x="1578.543650793651" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="957"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924479025157" ObjectName="出线18053断路器"/>
   <cge:TPSR_Ref TObjectID="6473924479025157"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1586.75,775.746) scale(1.64127,1.64127) translate(-616.762,-296.683)" width="10" x="1578.543650793651" y="759.3333401944903"/></g>
  <g id="912">
   <use class="kv10" height="20" transform="rotate(0,1654.75,775.746) scale(1.64127,1.64127) translate(-643.33,-296.683)" width="10" x="1646.543650793651" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="972"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924478959621" ObjectName="出线19054断路器"/>
   <cge:TPSR_Ref TObjectID="6473924478959621"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1654.75,775.746) scale(1.64127,1.64127) translate(-643.33,-296.683)" width="10" x="1646.543650793651" y="759.3333401944903"/></g>
  <g id="891">
   <use class="kv10" height="20" transform="rotate(0,1722.75,775.746) scale(1.64127,1.64127) translate(-669.899,-296.683)" width="10" x="1714.543650793651" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="987"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924478894085" ObjectName="出线20055断路器"/>
   <cge:TPSR_Ref TObjectID="6473924478894085"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1722.75,775.746) scale(1.64127,1.64127) translate(-669.899,-296.683)" width="10" x="1714.543650793651" y="759.3333401944903"/></g>
  <g id="878">
   <use class="kv10" height="20" transform="rotate(0,1790.75,775.746) scale(1.64127,1.64127) translate(-696.468,-296.683)" width="10" x="1782.543650793651" xlink:href="#Breaker:小车断路器_0" y="759.3333401944903" zvalue="1002"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924478828549" ObjectName="出线21056断路器"/>
   <cge:TPSR_Ref TObjectID="6473924478828549"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1790.75,775.746) scale(1.64127,1.64127) translate(-696.468,-296.683)" width="10" x="1782.543650793651" y="759.3333401944903"/></g>
  <g id="1008">
   <use class="kv10" height="20" transform="rotate(0,456.25,681.996) scale(1.64127,1.64127) translate(-175.058,-260.054)" width="10" x="448.0436507936508" xlink:href="#Breaker:小车断路器_0" y="665.5833401944903" zvalue="1019"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924479483909" ObjectName="#1接地变兼站用变031断路器"/>
   <cge:TPSR_Ref TObjectID="6473924479483909"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,456.25,681.996) scale(1.64127,1.64127) translate(-175.058,-260.054)" width="10" x="448.0436507936508" y="665.5833401944903"/></g>
  <g id="1026">
   <use class="kv10" height="20" transform="rotate(0,560.25,681.996) scale(1.64127,1.64127) translate(-215.692,-260.054)" width="10" x="552.0436507936508" xlink:href="#Breaker:小车断路器_0" y="665.5833401944903" zvalue="1032"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924479549445" ObjectName="#1电容器044断路器"/>
   <cge:TPSR_Ref TObjectID="6473924479549445"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,560.25,681.996) scale(1.64127,1.64127) translate(-215.692,-260.054)" width="10" x="552.0436507936508" y="665.5833401944903"/></g>
  <g id="1068">
   <use class="kv110" height="20" transform="rotate(0,817.875,454.02) scale(1.5542,1.35421) translate(-288.868,-115.212)" width="10" x="810.1035220427955" xlink:href="#Breaker:开关_0" y="440.4779758822602" zvalue="1065"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924479614981" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473924479614981"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,817.875,454.02) scale(1.5542,1.35421) translate(-288.868,-115.212)" width="10" x="810.1035220427955" y="440.4779758822602"/></g>
  <g id="1080">
   <use class="kv10" height="20" transform="rotate(0,818.151,674.746) scale(1.64127,1.64127) translate(-316.458,-257.221)" width="10" x="809.9449087355511" xlink:href="#Breaker:小车断路器_0" y="658.3333401944903" zvalue="1093"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924479680517" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924479680517"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,818.151,674.746) scale(1.64127,1.64127) translate(-316.458,-257.221)" width="10" x="809.9449087355511" y="658.3333401944903"/></g>
  <g id="26">
   <use class="kv10" height="20" transform="rotate(0,1004.25,681.996) scale(1.64127,1.64127) translate(-389.17,-260.054)" width="10" x="996.0436507936508" xlink:href="#Breaker:小车断路器_0" y="665.5833401944903" zvalue="1111"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924479746053" ObjectName="备用1断路器"/>
   <cge:TPSR_Ref TObjectID="6473924479746053"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1004.25,681.996) scale(1.64127,1.64127) translate(-389.17,-260.054)" width="10" x="996.0436507936508" y="665.5833401944903"/></g>
  <g id="44">
   <use class="kv10" height="20" transform="rotate(0,1720.25,681.996) scale(1.64127,1.64127) translate(-668.922,-260.054)" width="10" x="1712.043650793651" xlink:href="#Breaker:小车断路器_0" y="665.5833401944903" zvalue="1130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924479811589" ObjectName="#2电容器057断路器"/>
   <cge:TPSR_Ref TObjectID="6473924479811589"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1720.25,681.996) scale(1.64127,1.64127) translate(-668.922,-260.054)" width="10" x="1712.043650793651" y="665.5833401944903"/></g>
  <g id="57">
   <use class="kv10" height="20" transform="rotate(0,1812.25,681.996) scale(1.64127,1.64127) translate(-704.868,-260.054)" width="10" x="1804.043650793651" xlink:href="#Breaker:小车断路器_0" y="665.5833401944903" zvalue="1152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924479877125" ObjectName="备用2断路器"/>
   <cge:TPSR_Ref TObjectID="6473924479877125"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1812.25,681.996) scale(1.64127,1.64127) translate(-704.868,-260.054)" width="10" x="1804.043650793651" y="665.5833401944903"/></g>
  <g id="103">
   <use class="kv110" height="20" transform="rotate(0,1385.87,454.02) scale(1.5542,1.35421) translate(-491.407,-115.212)" width="10" x="1378.103522042795" xlink:href="#Breaker:开关_0" y="440.4779758822602" zvalue="1177"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924480008197" ObjectName="#2主变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473924480008197"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1385.87,454.02) scale(1.5542,1.35421) translate(-491.407,-115.212)" width="10" x="1378.103522042795" y="440.4779758822602"/></g>
  <g id="89">
   <use class="kv10" height="20" transform="rotate(0,1387.15,676.746) scale(1.64127,1.64127) translate(-538.775,-258.003)" width="10" x="1378.944908735551" xlink:href="#Breaker:小车断路器_0" y="660.3333401944903" zvalue="1198"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924479942661" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473924479942661"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1387.15,676.746) scale(1.64127,1.64127) translate(-538.775,-258.003)" width="10" x="1378.944908735551" y="660.3333401944903"/></g>
  <g id="118">
   <use class="kv10" height="20" transform="rotate(0,1232.25,681.996) scale(1.64127,1.64127) translate(-478.253,-260.054)" width="10" x="1224.043650793651" xlink:href="#Breaker:小车断路器_0" y="665.5833401944903" zvalue="1209"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924480073733" ObjectName="#2接地变045断路器"/>
   <cge:TPSR_Ref TObjectID="6473924480073733"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1232.25,681.996) scale(1.64127,1.64127) translate(-478.253,-260.054)" width="10" x="1224.043650793651" y="665.5833401944903"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="634">
   <use class="kv110" height="30" transform="rotate(0,1051.04,196.588) scale(-0.947693,0.6712) translate(-2160.48,91.3701)" width="15" x="1043.929351453262" xlink:href="#Disconnector:刀闸_0" y="186.5197927208583" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449590788102" ObjectName="110kV槟晟线1316隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449590788102"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1051.04,196.588) scale(-0.947693,0.6712) translate(-2160.48,91.3701)" width="15" x="1043.929351453262" y="186.5197927208583"/></g>
  <g id="628">
   <use class="kv110" height="30" transform="rotate(180,1051.22,327.857) scale(0.947693,-0.6712) translate(57.6291,-821.251)" width="15" x="1044.116158064038" xlink:href="#Disconnector:刀闸_0" y="317.788530492817" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449590591494" ObjectName="110kV槟晟线1311隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449590591494"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1051.22,327.857) scale(0.947693,-0.6712) translate(57.6291,-821.251)" width="15" x="1044.116158064038" y="317.788530492817"/></g>
  <g id="1015">
   <use class="kv10" height="30" transform="rotate(180,427.224,531.857) scale(0.947693,-0.6712) translate(23.1879,-1329.18)" width="15" x="420.1161580640381" xlink:href="#Disconnector:刀闸_0" y="521.788530492817" zvalue="1027"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449596555270" ObjectName="#1接地变兼站用变0010隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449596555270"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,427.224,531.857) scale(0.947693,-0.6712) translate(23.1879,-1329.18)" width="15" x="420.1161580640381" y="521.788530492817"/></g>
  <g id="1029">
   <use class="kv10" height="30" transform="rotate(180,560.308,567.857) scale(0.947693,-0.6712) translate(30.5334,-1418.82)" width="15" x="553.2002682062715" xlink:href="#Disconnector:刀闸_0" y="557.788530492817" zvalue="1042"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449596882950" ObjectName="#1电容器0446隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449596882950"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,560.308,567.857) scale(0.947693,-0.6712) translate(30.5334,-1418.82)" width="15" x="553.2002682062715" y="557.788530492817"/></g>
  <g id="1067">
   <use class="kv110" height="30" transform="rotate(0,819.037,392.588) scale(-0.947693,0.6712) translate(-1683.67,187.384)" width="15" x="811.9293514532625" xlink:href="#Disconnector:刀闸_0" y="382.5197927208583" zvalue="1067"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449597341701" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449597341701"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,819.037,392.588) scale(-0.947693,0.6712) translate(-1683.67,187.384)" width="15" x="811.9293514532625" y="382.5197927208583"/></g>
  <g id="1064">
   <use class="kv110" height="30" transform="rotate(180,819.224,511.857) scale(0.947693,-0.6712) translate(44.824,-1279.39)" width="15" x="812.1161580640381" xlink:href="#Disconnector:刀闸_0" y="501.788530492817" zvalue="1071"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449597276165" ObjectName="#1主变110kV侧1016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449597276165"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,819.224,511.857) scale(0.947693,-0.6712) translate(44.824,-1279.39)" width="15" x="812.1161580640381" y="501.788530492817"/></g>
  <g id="8">
   <use class="kv10" height="42" transform="rotate(0,671.704,665.625) scale(-1.39881,-1.39881) translate(-1145.92,-1133.1)" width="30" x="650.7217261904763" xlink:href="#Disconnector:手车刀闸带避雷器_0" y="636.25" zvalue="1107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449597800453" ObjectName="10kVⅠ母电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449597800453"/></metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,671.704,665.625) scale(-1.39881,-1.39881) translate(-1145.92,-1133.1)" width="30" x="650.7217261904763" y="636.25"/></g>
  <g id="41">
   <use class="kv10" height="30" transform="rotate(180,1720.31,567.857) scale(0.947693,-0.6712) translate(94.5586,-1418.82)" width="15" x="1713.200268206272" xlink:href="#Disconnector:刀闸_0" y="557.788530492817" zvalue="1135"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449598259205" ObjectName="#2电容器0576隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449598259205"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1720.31,567.857) scale(0.947693,-0.6712) translate(94.5586,-1418.82)" width="15" x="1713.200268206272" y="557.788530492817"/></g>
  <g id="70">
   <use class="kv10" height="42" transform="rotate(0,1582.2,665.625) scale(-1.39881,-1.39881) translate(-2707.33,-1133.1)" width="30" x="1561.221726190476" xlink:href="#Disconnector:手车刀闸带避雷器_0" y="636.25" zvalue="1170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449598980102" ObjectName="10kVⅡ母电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449598980102"/></metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1582.2,665.625) scale(-1.39881,-1.39881) translate(-2707.33,-1133.1)" width="30" x="1561.221726190476" y="636.25"/></g>
  <g id="102">
   <use class="kv110" height="30" transform="rotate(0,1385.88,392.588) scale(-0.947693,0.6712) translate(-2848.65,187.384)" width="15" x="1378.772980400387" xlink:href="#Disconnector:刀闸_0" y="382.5197927208583" zvalue="1179"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449599766533" ObjectName="#2主变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449599766533"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1385.88,392.588) scale(-0.947693,0.6712) translate(-2848.65,187.384)" width="15" x="1378.772980400387" y="382.5197927208583"/></g>
  <g id="101">
   <use class="kv110" height="30" transform="rotate(180,1387.22,511.857) scale(0.947693,-0.6712) translate(76.1743,-1279.39)" width="15" x="1380.116158064038" xlink:href="#Disconnector:刀闸_0" y="501.788530492817" zvalue="1181"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449599700997" ObjectName="#2主变110kV侧1026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449599700997"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1387.22,511.857) scale(0.947693,-0.6712) translate(76.1743,-1279.39)" width="15" x="1380.116158064038" y="501.788530492817"/></g>
  <g id="112">
   <use class="kv10" height="30" transform="rotate(180,1232.31,558.857) scale(0.947693,-0.6712) translate(67.6239,-1396.41)" width="15" x="1225.200268206272" xlink:href="#Disconnector:刀闸_0" y="548.788530492817" zvalue="1218"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449599897605" ObjectName="#2接地变0020隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449599897605"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1232.31,558.857) scale(0.947693,-0.6712) translate(67.6239,-1396.41)" width="15" x="1225.200268206272" y="548.788530492817"/></g>
  <g id="134">
   <use class="kv110" height="30" transform="rotate(0,984.037,417.588) scale(-0.947693,0.6712) translate(-2022.78,199.631)" width="15" x="976.9293514532625" xlink:href="#Disconnector:刀闸_0" y="407.5197927208583" zvalue="1231"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449600421893" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449600421893"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,984.037,417.588) scale(-0.947693,0.6712) translate(-2022.78,199.631)" width="15" x="976.9293514532625" y="407.5197927208583"/></g>
  <g id="148">
   <use class="kv10" height="30" transform="rotate(0,467,236) scale(1,1) translate(0,0)" width="15" x="459.5" xlink:href="#Disconnector:令克_0" y="221" zvalue="1248"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449600684037" ObjectName="施工电源变（兼#2站用变）刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449600684037"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,467,236) scale(1,1) translate(0,0)" width="15" x="459.5" y="221"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="631">
   <use class="kv110" height="20" transform="rotate(270,1016,162.669) scale(-1.24619,-1.0068) translate(-1830.05,-324.171)" width="10" x="1009.768227141073" xlink:href="#GroundDisconnector:地刀_0" y="152.6008256735037" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449590722566" ObjectName="110kV槟晟线13167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449590722566"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1016,162.669) scale(-1.24619,-1.0068) translate(-1830.05,-324.171)" width="10" x="1009.768227141073" y="152.6008256735037"/></g>
  <g id="625">
   <use class="kv110" height="20" transform="rotate(270,1017,225.56) scale(-1.24619,-1.0068) translate(-1831.86,-449.529)" width="10" x="1010.768227141073" xlink:href="#GroundDisconnector:地刀_0" y="215.4922963829677" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449590525958" ObjectName="110kV槟晟线13160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449590525958"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1017,225.56) scale(-1.24619,-1.0068) translate(-1831.86,-449.529)" width="10" x="1010.768227141073" y="215.4922963829677"/></g>
  <g id="624">
   <use class="kv110" height="20" transform="rotate(270,1017,297.6) scale(-1.24619,-1.0068) translate(-1831.86,-593.122)" width="10" x="1010.768227141073" xlink:href="#GroundDisconnector:地刀_0" y="287.532228536342" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449590394886" ObjectName="110kV槟晟线13117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449590394886"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1017,297.6) scale(-1.24619,-1.0068) translate(-1831.86,-593.122)" width="10" x="1010.768227141073" y="287.532228536342"/></g>
  <g id="566">
   <use class="kv10" height="20" transform="rotate(0,435.238,844.164) scale(-1.24619,1.0068) translate(-783.263,-5.63407)" width="10" x="429.0073475335558" xlink:href="#GroundDisconnector:地刀_0" y="834.0963545646036" zvalue="137"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449591050246" ObjectName="出线103217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449591050246"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,435.238,844.164) scale(-1.24619,1.0068) translate(-783.263,-5.63407)" width="10" x="429.0073475335558" y="834.0963545646036"/></g>
  <g id="689">
   <use class="kv10" height="20" transform="rotate(0,507.238,844.164) scale(-1.24619,1.0068) translate(-913.04,-5.63407)" width="10" x="501.0073475335558" xlink:href="#GroundDisconnector:地刀_0" y="834.0963545646036" zvalue="719"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449591443462" ObjectName="出线203317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449591443462"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,507.238,844.164) scale(-1.24619,1.0068) translate(-913.04,-5.63407)" width="10" x="501.0073475335558" y="834.0963545646036"/></g>
  <g id="704">
   <use class="kv10" height="20" transform="rotate(0,579.238,844.164) scale(-1.24619,1.0068) translate(-1042.82,-5.63407)" width="10" x="573.0073475335558" xlink:href="#GroundDisconnector:地刀_0" y="834.0963545646036" zvalue="735"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449591705606" ObjectName="出线303417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449591705606"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,579.238,844.164) scale(-1.24619,1.0068) translate(-1042.82,-5.63407)" width="10" x="573.0073475335558" y="834.0963545646036"/></g>
  <g id="721">
   <use class="kv10" height="20" transform="rotate(0,647.238,844.164) scale(-1.24619,1.0068) translate(-1165.38,-5.63407)" width="10" x="641.0073475335558" xlink:href="#GroundDisconnector:地刀_0" y="834.0963545646036" zvalue="751"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449591967750" ObjectName="出线403517接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449591967750"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,647.238,844.164) scale(-1.24619,1.0068) translate(-1165.38,-5.63407)" width="10" x="641.0073475335558" y="834.0963545646036"/></g>
  <g id="736">
   <use class="kv10" height="20" transform="rotate(0,715.238,844.164) scale(-1.24619,1.0068) translate(-1287.95,-5.63407)" width="10" x="709.0073475335558" xlink:href="#GroundDisconnector:地刀_0" y="834.0963545646036" zvalue="767"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449592229893" ObjectName="出线503617接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449592229893"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,715.238,844.164) scale(-1.24619,1.0068) translate(-1287.95,-5.63407)" width="10" x="709.0073475335558" y="834.0963545646036"/></g>
  <g id="752">
   <use class="kv10" height="20" transform="rotate(0,779.238,844.164) scale(-1.24619,1.0068) translate(-1403.31,-5.63407)" width="10" x="773.0073475335558" xlink:href="#GroundDisconnector:地刀_0" y="834.0963545646036" zvalue="783"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449592492038" ObjectName="出线603717接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449592492038"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,779.238,844.164) scale(-1.24619,1.0068) translate(-1403.31,-5.63407)" width="10" x="773.0073475335558" y="834.0963545646036"/></g>
  <g id="767">
   <use class="kv10" height="20" transform="rotate(0,847.238,844.164) scale(-1.24619,1.0068) translate(-1525.87,-5.63407)" width="10" x="841.0073475335558" xlink:href="#GroundDisconnector:地刀_0" y="834.0963545646036" zvalue="799"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449592754181" ObjectName="出线703817接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449592754181"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,847.238,844.164) scale(-1.24619,1.0068) translate(-1525.87,-5.63407)" width="10" x="841.0073475335558" y="834.0963545646036"/></g>
  <g id="782">
   <use class="kv10" height="20" transform="rotate(0,915.238,844.164) scale(-1.24619,1.0068) translate(-1648.44,-5.63407)" width="10" x="909.0073475335558" xlink:href="#GroundDisconnector:地刀_0" y="834.0963545646036" zvalue="815"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449593016325" ObjectName="出线803917接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449593016325"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,915.238,844.164) scale(-1.24619,1.0068) translate(-1648.44,-5.63407)" width="10" x="909.0073475335558" y="834.0963545646036"/></g>
  <g id="802">
   <use class="kv10" height="20" transform="rotate(0,983.238,844.164) scale(-1.24619,1.0068) translate(-1771,-5.63407)" width="10" x="977.0073475335558" xlink:href="#GroundDisconnector:地刀_0" y="834.0963545646036" zvalue="831"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449593278469" ObjectName="出线904117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449593278469"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,983.238,844.164) scale(-1.24619,1.0068) translate(-1771,-5.63407)" width="10" x="977.0073475335558" y="834.0963545646036"/></g>
  <g id="819">
   <use class="kv10" height="20" transform="rotate(0,1051.24,844.164) scale(-1.24619,1.0068) translate(-1893.57,-5.63407)" width="10" x="1045.007347533556" xlink:href="#GroundDisconnector:地刀_0" y="834.0963545646036" zvalue="847"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449593540614" ObjectName="出线1004217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449593540614"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1051.24,844.164) scale(-1.24619,1.0068) translate(-1893.57,-5.63407)" width="10" x="1045.007347533556" y="834.0963545646036"/></g>
  <g id="1001">
   <use class="kv10" height="20" transform="rotate(0,1191.24,844.164) scale(-1.24619,1.0068) translate(-2145.91,-5.63407)" width="10" x="1185.007347533556" xlink:href="#GroundDisconnector:地刀_0" y="834.0963545646036" zvalue="864"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449596162054" ObjectName="出线1204627接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449596162054"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1191.24,844.164) scale(-1.24619,1.0068) translate(-2145.91,-5.63407)" width="10" x="1185.007347533556" y="834.0963545646036"/></g>
  <g id="989">
   <use class="kv10" height="20" transform="rotate(0,1263.24,844.164) scale(-1.24619,1.0068) translate(-2275.69,-5.63407)" width="10" x="1257.007347533556" xlink:href="#GroundDisconnector:地刀_0" y="834.0963545646036" zvalue="879"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449595899910" ObjectName="出线1304727接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449595899910"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1263.24,844.164) scale(-1.24619,1.0068) translate(-2275.69,-5.63407)" width="10" x="1257.007347533556" y="834.0963545646036"/></g>
  <g id="977">
   <use class="kv10" height="20" transform="rotate(0,1335.24,844.164) scale(-1.24619,1.0068) translate(-2405.47,-5.63407)" width="10" x="1329.007347533556" xlink:href="#GroundDisconnector:地刀_0" y="834.0963545646036" zvalue="894"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449595637766" ObjectName="出线1404827接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449595637766"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1335.24,844.164) scale(-1.24619,1.0068) translate(-2405.47,-5.63407)" width="10" x="1329.007347533556" y="834.0963545646036"/></g>
  <g id="965">
   <use class="kv10" height="20" transform="rotate(0,1403.24,844.164) scale(-1.24619,1.0068) translate(-2528.03,-5.63407)" width="10" x="1397.007347533556" xlink:href="#GroundDisconnector:地刀_0" y="834.0963545646036" zvalue="909"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449595375622" ObjectName="出线1504927接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449595375622"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1403.24,844.164) scale(-1.24619,1.0068) translate(-2528.03,-5.63407)" width="10" x="1397.007347533556" y="834.0963545646036"/></g>
  <g id="953">
   <use class="kv10" height="20" transform="rotate(0,1471.24,844.164) scale(-1.24619,1.0068) translate(-2650.6,-5.63407)" width="10" x="1465.007347533556" xlink:href="#GroundDisconnector:地刀_0" y="834.0963545646036" zvalue="924"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449595113478" ObjectName="出线1605127接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449595113478"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1471.24,844.164) scale(-1.24619,1.0068) translate(-2650.6,-5.63407)" width="10" x="1465.007347533556" y="834.0963545646036"/></g>
  <g id="940">
   <use class="kv10" height="20" transform="rotate(0,1535.24,844.164) scale(-1.24619,1.0068) translate(-2765.96,-5.63407)" width="10" x="1529.007347533556" xlink:href="#GroundDisconnector:地刀_0" y="834.0963545646036" zvalue="939"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449594851334" ObjectName="出线1705227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449594851334"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1535.24,844.164) scale(-1.24619,1.0068) translate(-2765.96,-5.63407)" width="10" x="1529.007347533556" y="834.0963545646036"/></g>
  <g id="926">
   <use class="kv10" height="20" transform="rotate(0,1604.24,843.164) scale(-1.24619,1.0068) translate(-2890.33,-5.62732)" width="10" x="1598.007347533556" xlink:href="#GroundDisconnector:地刀_0" y="833.0963545646036" zvalue="954"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449594589190" ObjectName="出线1805327接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449594589190"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1604.24,843.164) scale(-1.24619,1.0068) translate(-2890.33,-5.62732)" width="10" x="1598.007347533556" y="833.0963545646036"/></g>
  <g id="914">
   <use class="kv10" height="20" transform="rotate(0,1671.24,844.164) scale(-1.24619,1.0068) translate(-3011.09,-5.63407)" width="10" x="1665.007347533556" xlink:href="#GroundDisconnector:地刀_0" y="834.0963545646036" zvalue="969"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449594327046" ObjectName="出线1905427接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449594327046"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1671.24,844.164) scale(-1.24619,1.0068) translate(-3011.09,-5.63407)" width="10" x="1665.007347533556" y="834.0963545646036"/></g>
  <g id="893">
   <use class="kv10" height="20" transform="rotate(0,1739.24,844.164) scale(-1.24619,1.0068) translate(-3133.66,-5.63407)" width="10" x="1733.007347533556" xlink:href="#GroundDisconnector:地刀_0" y="834.0963545646036" zvalue="984"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449594064902" ObjectName="出线2005527接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449594064902"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1739.24,844.164) scale(-1.24619,1.0068) translate(-3133.66,-5.63407)" width="10" x="1733.007347533556" y="834.0963545646036"/></g>
  <g id="880">
   <use class="kv10" height="20" transform="rotate(0,1807.24,844.164) scale(-1.24619,1.0068) translate(-3256.22,-5.63407)" width="10" x="1801.007347533556" xlink:href="#GroundDisconnector:地刀_0" y="834.0963545646036" zvalue="999"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449593802758" ObjectName="出线2105627接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449593802758"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1807.24,844.164) scale(-1.24619,1.0068) translate(-3256.22,-5.63407)" width="10" x="1801.007347533556" y="834.0963545646036"/></g>
  <g id="1013">
   <use class="kv10" height="20" transform="rotate(0,431.238,616.164) scale(-1.24619,-1.0068) translate(-776.054,-1228.1)" width="10" x="425.0073475335558" xlink:href="#GroundDisconnector:地刀_0" y="606.0963545646036" zvalue="1024"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449596489733" ObjectName="#1接地变兼站用变03117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449596489733"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,431.238,616.164) scale(-1.24619,-1.0068) translate(-776.054,-1228.1)" width="10" x="425.0073475335558" y="606.0963545646036"/></g>
  <g id="1025">
   <use class="kv10" height="20" transform="rotate(0,531.238,616.164) scale(-1.24619,-1.0068) translate(-956.298,-1228.1)" width="10" x="525.0073475335558" xlink:href="#GroundDisconnector:地刀_0" y="606.0963545646036" zvalue="1034"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449596751878" ObjectName="#1电容器04417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449596751878"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,531.238,616.164) scale(-1.24619,-1.0068) translate(-956.298,-1228.1)" width="10" x="525.0073475335558" y="606.0963545646036"/></g>
  <g id="1063">
   <use class="kv110" height="20" transform="rotate(270,768.999,421.56) scale(-1.24619,-1.0068) translate(-1384.85,-840.205)" width="10" x="762.7682271410732" xlink:href="#GroundDisconnector:地刀_0" y="411.4922963829677" zvalue="1073"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449597210629" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449597210629"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,768.999,421.56) scale(-1.24619,-1.0068) translate(-1384.85,-840.205)" width="10" x="762.7682271410732" y="411.4922963829677"/></g>
  <g id="1062">
   <use class="kv110" height="20" transform="rotate(270,771.749,488.35) scale(-1.24619,-1.0068) translate(-1389.81,-973.334)" width="10" x="765.5182271410732" xlink:href="#GroundDisconnector:地刀_0" y="478.282228536342" zvalue="1075"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449597079557" ObjectName="#1主变110kV侧10160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449597079557"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,771.749,488.35) scale(-1.24619,-1.0068) translate(-1389.81,-973.334)" width="10" x="765.5182271410732" y="478.282228536342"/></g>
  <g id="1077">
   <use class="kv110" height="20" transform="rotate(270,776.749,533.35) scale(-1.24619,-1.0068) translate(-1398.82,-1063.03)" width="10" x="770.5182271410732" xlink:href="#GroundDisconnector:地刀_0" y="523.2822285363419" zvalue="1090"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449597472773" ObjectName="#1主变110kV侧10167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449597472773"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,776.749,533.35) scale(-1.24619,-1.0068) translate(-1398.82,-1063.03)" width="10" x="770.5182271410732" y="523.2822285363419"/></g>
  <g id="448">
   <use class="kv10" height="25" transform="rotate(180,539.951,515.627) scale(1.25,-1.25) translate(-105.49,-925.004)" width="20" x="527.4509943938453" xlink:href="#GroundDisconnector:接地刀_0" y="500.0019829416365" zvalue="1101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449597669381" ObjectName="#1电容器04410接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449597669381"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(180,539.951,515.627) scale(1.25,-1.25) translate(-105.49,-925.004)" width="20" x="527.4509943938453" y="500.0019829416365"/></g>
  <g id="25">
   <use class="kv10" height="20" transform="rotate(0,975.238,616.164) scale(-1.24619,-1.0068) translate(-1756.59,-1228.1)" width="10" x="969.0073475335558" xlink:href="#GroundDisconnector:地刀_0" y="606.0963545646036" zvalue="1113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449597997061" ObjectName="备用1接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449597997061"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,975.238,616.164) scale(-1.24619,-1.0068) translate(-1756.59,-1228.1)" width="10" x="969.0073475335558" y="606.0963545646036"/></g>
  <g id="43">
   <use class="kv10" height="20" transform="rotate(0,1691.24,616.164) scale(-1.24619,-1.0068) translate(-3047.14,-1228.1)" width="10" x="1685.007347533556" xlink:href="#GroundDisconnector:地刀_0" y="606.0963545646036" zvalue="1132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449598455813" ObjectName="#2电容器05727接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449598455813"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1691.24,616.164) scale(-1.24619,-1.0068) translate(-3047.14,-1228.1)" width="10" x="1685.007347533556" y="606.0963545646036"/></g>
  <g id="34">
   <use class="kv10" height="25" transform="rotate(180,1699.95,515.627) scale(1.25,-1.25) translate(-337.49,-925.004)" width="20" x="1687.450994393845" xlink:href="#GroundDisconnector:接地刀_0" y="500.0019829416365" zvalue="1144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449598128133" ObjectName="#2电容器05710接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449598128133"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(180,1699.95,515.627) scale(1.25,-1.25) translate(-337.49,-925.004)" width="20" x="1687.450994393845" y="500.0019829416365"/></g>
  <g id="56">
   <use class="kv10" height="20" transform="rotate(0,1783.24,616.164) scale(-1.24619,-1.0068) translate(-3212.96,-1228.1)" width="10" x="1777.007347533556" xlink:href="#GroundDisconnector:地刀_0" y="606.0963545646036" zvalue="1153"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449598783494" ObjectName="备用2接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449598783494"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1783.24,616.164) scale(-1.24619,-1.0068) translate(-3212.96,-1228.1)" width="10" x="1777.007347533556" y="606.0963545646036"/></g>
  <g id="60">
   <use class="kv110" height="40" transform="rotate(0,895,577.983) scale(0.8,-1) translate(219.75,-1155.97)" width="40" x="879" xlink:href="#GroundDisconnector:中性点地刀12_0" y="557.9829446730681" zvalue="1162"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449598914566" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449598914566"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,895,577.983) scale(0.8,-1) translate(219.75,-1155.97)" width="40" x="879" y="557.9829446730681"/></g>
  <g id="100">
   <use class="kv110" height="20" transform="rotate(270,1337,421.56) scale(-1.24619,-1.0068) translate(-2408.64,-840.205)" width="10" x="1330.768227141073" xlink:href="#GroundDisconnector:地刀_0" y="411.4922963829677" zvalue="1183"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449599635461" ObjectName="#2主变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449599635461"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1337,421.56) scale(-1.24619,-1.0068) translate(-2408.64,-840.205)" width="10" x="1330.768227141073" y="411.4922963829677"/></g>
  <g id="99">
   <use class="kv110" height="20" transform="rotate(270,1339.75,488.35) scale(-1.24619,-1.0068) translate(-2413.6,-973.334)" width="10" x="1333.518227141073" xlink:href="#GroundDisconnector:地刀_0" y="478.282228536342" zvalue="1185"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449599504390" ObjectName="#2主变110kV侧10260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449599504390"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1339.75,488.35) scale(-1.24619,-1.0068) translate(-2413.6,-973.334)" width="10" x="1333.518227141073" y="478.282228536342"/></g>
  <g id="91">
   <use class="kv110" height="20" transform="rotate(270,1344.75,533.35) scale(-1.24619,-1.0068) translate(-2422.61,-1063.03)" width="10" x="1338.518227141073" xlink:href="#GroundDisconnector:地刀_0" y="523.2822285363419" zvalue="1195"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449599373318" ObjectName="#2主变110kV侧10267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449599373318"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1344.75,533.35) scale(-1.24619,-1.0068) translate(-2422.61,-1063.03)" width="10" x="1338.518227141073" y="523.2822285363419"/></g>
  <g id="84">
   <use class="kv110" height="40" transform="rotate(0,1463,577.983) scale(0.8,-1) translate(361.75,-1155.97)" width="40" x="1447" xlink:href="#GroundDisconnector:中性点地刀12_0" y="557.9829446730681" zvalue="1204"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449599176710" ObjectName="#2主变110kV侧1020中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449599176710"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1463,577.983) scale(0.8,-1) translate(361.75,-1155.97)" width="40" x="1447" y="557.9829446730681"/></g>
  <g id="114">
   <use class="kv10" height="20" transform="rotate(0,1207.24,616.164) scale(-1.24619,-1.0068) translate(-2174.75,-1228.1)" width="10" x="1201.007347533556" xlink:href="#GroundDisconnector:地刀_0" y="606.0963545646036" zvalue="1215"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449600028677" ObjectName="#2接地变04527接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449600028677"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1207.24,616.164) scale(-1.24619,-1.0068) translate(-2174.75,-1228.1)" width="10" x="1201.007347533556" y="606.0963545646036"/></g>
  <g id="133">
   <use class="kv110" height="20" transform="rotate(270,948.999,390.315) scale(-1.24619,-1.0068) translate(-1709.29,-777.925)" width="10" x="942.7682271410732" xlink:href="#GroundDisconnector:地刀_0" y="380.246507813326" zvalue="1233"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449600356357" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449600356357"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,948.999,390.315) scale(-1.24619,-1.0068) translate(-1709.29,-777.925)" width="10" x="942.7682271410732" y="380.246507813326"/></g>
  <g id="132">
   <use class="kv110" height="20" transform="rotate(270,949.999,450.56) scale(-1.24619,-1.0068) translate(-1711.09,-898.009)" width="10" x="943.7682271410732" xlink:href="#GroundDisconnector:地刀_0" y="440.4922963829677" zvalue="1235"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449600225285" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449600225285"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,949.999,450.56) scale(-1.24619,-1.0068) translate(-1711.09,-898.009)" width="10" x="943.7682271410732" y="440.4922963829677"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="630">
   <path class="kv110" d="M 1049.82 249.06 L 1049.82 206.48" stroke-width="1" zvalue="57"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="635@0" LinkObjectIDznd="634@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1049.82 249.06 L 1049.82 206.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="627">
   <path class="kv110" d="M 1050.95 186.85 L 1051.04 115.61" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="634@0" LinkObjectIDznd="637@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1050.95 186.85 L 1051.04 115.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="626">
   <path class="kv110" d="M 1024.28 142.69 L 1051.01 142.69" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1005@0" LinkObjectIDznd="627" MaxPinNum="2"/>
   </metadata>
  <path d="M 1024.28 142.69 L 1051.01 142.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="623">
   <path class="kv110" d="M 1051.17 337.75 L 1051.17 362.34" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="628@1" LinkObjectIDznd="657@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1051.17 337.75 L 1051.17 362.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="621">
   <path class="kv110" d="M 1049.98 274.95 L 1049.98 318.12" stroke-width="1" zvalue="67"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="635@1" LinkObjectIDznd="628@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1049.98 274.95 L 1049.98 318.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="503">
   <path class="kv110" d="M 1025.82 162.73 L 1050.98 162.73" stroke-width="1" zvalue="187"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="631@0" LinkObjectIDznd="627" MaxPinNum="2"/>
   </metadata>
  <path d="M 1025.82 162.73 L 1050.98 162.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="502">
   <path class="kv110" d="M 1026.82 225.62 L 1049.82 225.62" stroke-width="1" zvalue="188"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="625@0" LinkObjectIDznd="630" MaxPinNum="2"/>
   </metadata>
  <path d="M 1026.82 225.62 L 1049.82 225.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="501">
   <path class="kv110" d="M 1026.82 297.66 L 1049.98 297.66" stroke-width="1" zvalue="189"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="624@0" LinkObjectIDznd="621" MaxPinNum="2"/>
   </metadata>
  <path d="M 1026.82 297.66 L 1049.98 297.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="310">
   <path class="kv10" d="M 418.75 760.56 L 418.75 736.09" stroke-width="1" zvalue="472"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="312@0" LinkObjectIDznd="656@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 418.75 760.56 L 418.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="674">
   <path class="kv10" d="M 445.64 910.02 L 445.64 886.25 L 418.75 886.25" stroke-width="1" zvalue="717"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="660@0" LinkObjectIDznd="294" MaxPinNum="2"/>
   </metadata>
  <path d="M 445.64 910.02 L 445.64 886.25 L 418.75 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="686">
   <path class="kv10" d="M 490.75 760.56 L 490.75 736.09" stroke-width="1" zvalue="724"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="687@0" LinkObjectIDznd="656@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 490.75 760.56 L 490.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="680">
   <path class="kv10" d="M 507.18 834.35 L 507.18 819.75 L 490.75 819.75" stroke-width="1" zvalue="731"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="689@0" LinkObjectIDznd="164" MaxPinNum="2"/>
   </metadata>
  <path d="M 507.18 834.35 L 507.18 819.75 L 490.75 819.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="679">
   <path class="kv10" d="M 490.75 820 L 475.28 820 L 475.28 833.06" stroke-width="1" zvalue="732"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164" LinkObjectIDznd="688@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 490.75 820 L 475.28 820 L 475.28 833.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="678">
   <path class="kv10" d="M 517.64 910.02 L 517.64 886.25 L 490.75 886.25" stroke-width="1" zvalue="733"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="685@0" LinkObjectIDznd="164" MaxPinNum="2"/>
   </metadata>
  <path d="M 517.64 910.02 L 517.64 886.25 L 490.75 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="701">
   <path class="kv10" d="M 562.75 760.56 L 562.75 736.09" stroke-width="1" zvalue="740"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="702@0" LinkObjectIDznd="656@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 562.75 760.56 L 562.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="695">
   <path class="kv10" d="M 579.18 834.35 L 579.18 819.75 L 562.75 819.75" stroke-width="1" zvalue="747"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="704@0" LinkObjectIDznd="167" MaxPinNum="2"/>
   </metadata>
  <path d="M 579.18 834.35 L 579.18 819.75 L 562.75 819.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="694">
   <path class="kv10" d="M 562.75 820 L 547.28 820 L 547.28 833.06" stroke-width="1" zvalue="748"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="167" LinkObjectIDznd="703@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 562.75 820 L 547.28 820 L 547.28 833.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="693">
   <path class="kv10" d="M 589.64 910.02 L 589.64 886.25 L 562.75 886.25" stroke-width="1" zvalue="749"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="700@0" LinkObjectIDznd="167" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.64 910.02 L 589.64 886.25 L 562.75 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="718">
   <path class="kv10" d="M 630.75 760.56 L 630.75 736.09" stroke-width="1" zvalue="756"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="719@0" LinkObjectIDznd="656@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 630.75 760.56 L 630.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="710">
   <path class="kv10" d="M 647.18 834.35 L 647.18 819.75 L 630.75 819.75" stroke-width="1" zvalue="763"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="721@0" LinkObjectIDznd="177" MaxPinNum="2"/>
   </metadata>
  <path d="M 647.18 834.35 L 647.18 819.75 L 630.75 819.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="709">
   <path class="kv10" d="M 630.75 820 L 615.28 820 L 615.28 833.06" stroke-width="1" zvalue="764"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177" LinkObjectIDznd="720@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 630.75 820 L 615.28 820 L 615.28 833.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="708">
   <path class="kv10" d="M 657.64 910.02 L 657.64 886.25 L 630.75 886.25" stroke-width="1" zvalue="765"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="716@0" LinkObjectIDznd="177" MaxPinNum="2"/>
   </metadata>
  <path d="M 657.64 910.02 L 657.64 886.25 L 630.75 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="733">
   <path class="kv10" d="M 698.75 760.56 L 698.75 736.09" stroke-width="1" zvalue="772"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="734@0" LinkObjectIDznd="656@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 698.75 760.56 L 698.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="727">
   <path class="kv10" d="M 715.18 834.35 L 715.18 819.75 L 698.75 819.75" stroke-width="1" zvalue="779"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="736@0" LinkObjectIDznd="178" MaxPinNum="2"/>
   </metadata>
  <path d="M 715.18 834.35 L 715.18 819.75 L 698.75 819.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="726">
   <path class="kv10" d="M 698.75 820 L 683.28 820 L 683.28 833.06" stroke-width="1" zvalue="780"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178" LinkObjectIDznd="735@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 698.75 820 L 683.28 820 L 683.28 833.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="725">
   <path class="kv10" d="M 725.64 910.02 L 725.64 886.25 L 698.75 886.25" stroke-width="1" zvalue="781"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="732@0" LinkObjectIDznd="178" MaxPinNum="2"/>
   </metadata>
  <path d="M 725.64 910.02 L 725.64 886.25 L 698.75 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="749">
   <path class="kv10" d="M 762.75 760.56 L 762.75 736.09" stroke-width="1" zvalue="788"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="750@0" LinkObjectIDznd="656@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 762.75 760.56 L 762.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="743">
   <path class="kv10" d="M 779.18 834.35 L 779.18 819.75 L 762.75 819.75" stroke-width="1" zvalue="795"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="752@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 779.18 834.35 L 779.18 819.75 L 762.75 819.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="742">
   <path class="kv10" d="M 762.75 820 L 747.28 820 L 747.28 833.06" stroke-width="1" zvalue="796"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179" LinkObjectIDznd="751@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 762.75 820 L 747.28 820 L 747.28 833.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="741">
   <path class="kv10" d="M 789.64 910.02 L 789.64 886.25 L 762.75 886.25" stroke-width="1" zvalue="797"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="748@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 789.64 910.02 L 789.64 886.25 L 762.75 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="764">
   <path class="kv10" d="M 830.75 760.56 L 830.75 736.09" stroke-width="1" zvalue="804"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="765@0" LinkObjectIDznd="656@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 830.75 760.56 L 830.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="758">
   <path class="kv10" d="M 847.18 834.35 L 847.18 819.75 L 830.75 819.75" stroke-width="1" zvalue="811"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="767@0" LinkObjectIDznd="180" MaxPinNum="2"/>
   </metadata>
  <path d="M 847.18 834.35 L 847.18 819.75 L 830.75 819.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="757">
   <path class="kv10" d="M 830.75 820 L 815.28 820 L 815.28 833.06" stroke-width="1" zvalue="812"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180" LinkObjectIDznd="766@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 830.75 820 L 815.28 820 L 815.28 833.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="756">
   <path class="kv10" d="M 857.64 910.02 L 857.64 886.25 L 830.75 886.25" stroke-width="1" zvalue="813"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="763@0" LinkObjectIDznd="180" MaxPinNum="2"/>
   </metadata>
  <path d="M 857.64 910.02 L 857.64 886.25 L 830.75 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="779">
   <path class="kv10" d="M 898.75 760.56 L 898.75 736.09" stroke-width="1" zvalue="820"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="780@0" LinkObjectIDznd="656@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.75 760.56 L 898.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="773">
   <path class="kv10" d="M 915.18 834.35 L 915.18 819.75 L 898.75 819.75" stroke-width="1" zvalue="827"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="782@0" LinkObjectIDznd="181" MaxPinNum="2"/>
   </metadata>
  <path d="M 915.18 834.35 L 915.18 819.75 L 898.75 819.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="772">
   <path class="kv10" d="M 898.75 820 L 883.28 820 L 883.28 833.06" stroke-width="1" zvalue="828"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181" LinkObjectIDznd="781@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.75 820 L 883.28 820 L 883.28 833.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="771">
   <path class="kv10" d="M 925.64 910.02 L 925.64 886.25 L 898.75 886.25" stroke-width="1" zvalue="829"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="778@0" LinkObjectIDznd="181" MaxPinNum="2"/>
   </metadata>
  <path d="M 925.64 910.02 L 925.64 886.25 L 898.75 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="799">
   <path class="kv10" d="M 966.75 760.56 L 966.75 736.09" stroke-width="1" zvalue="836"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="800@0" LinkObjectIDznd="656@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.75 760.56 L 966.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="793">
   <path class="kv10" d="M 983.18 834.35 L 983.18 819.75 L 966.75 819.75" stroke-width="1" zvalue="843"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="802@0" LinkObjectIDznd="182" MaxPinNum="2"/>
   </metadata>
  <path d="M 983.18 834.35 L 983.18 819.75 L 966.75 819.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="792">
   <path class="kv10" d="M 966.75 820 L 951.28 820 L 951.28 833.06" stroke-width="1" zvalue="844"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182" LinkObjectIDznd="801@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.75 820 L 951.28 820 L 951.28 833.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="789">
   <path class="kv10" d="M 993.64 910.02 L 993.64 886.25 L 966.75 886.25" stroke-width="1" zvalue="845"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="798@0" LinkObjectIDznd="182" MaxPinNum="2"/>
   </metadata>
  <path d="M 993.64 910.02 L 993.64 886.25 L 966.75 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="816">
   <path class="kv10" d="M 1034.75 760.56 L 1034.75 736.09" stroke-width="1" zvalue="852"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="817@0" LinkObjectIDznd="656@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1034.75 760.56 L 1034.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="809">
   <path class="kv10" d="M 1051.18 834.35 L 1051.18 819.75 L 1034.75 819.75" stroke-width="1" zvalue="859"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="819@0" LinkObjectIDznd="183" MaxPinNum="2"/>
   </metadata>
  <path d="M 1051.18 834.35 L 1051.18 819.75 L 1034.75 819.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="807">
   <path class="kv10" d="M 1034.75 820 L 1019.28 820 L 1019.28 833.06" stroke-width="1" zvalue="860"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183" LinkObjectIDznd="818@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1034.75 820 L 1019.28 820 L 1019.28 833.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="806">
   <path class="kv10" d="M 1061.64 910.02 L 1061.64 886.25 L 1034.75 886.25" stroke-width="1" zvalue="861"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="814@0" LinkObjectIDznd="183" MaxPinNum="2"/>
   </metadata>
  <path d="M 1061.64 910.02 L 1061.64 886.25 L 1034.75 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="998">
   <path class="kv10" d="M 1174.75 760.56 L 1174.75 736.09" stroke-width="1" zvalue="869"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="999@0" LinkObjectIDznd="1002@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1174.75 760.56 L 1174.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="992">
   <path class="kv10" d="M 1191.18 834.35 L 1191.18 819.75 L 1174.75 819.75" stroke-width="1" zvalue="876"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1001@0" LinkObjectIDznd="299" MaxPinNum="2"/>
   </metadata>
  <path d="M 1191.18 834.35 L 1191.18 819.75 L 1174.75 819.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="991">
   <path class="kv10" d="M 1174.75 820 L 1159.28 820 L 1159.28 833.06" stroke-width="1" zvalue="877"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="299" LinkObjectIDznd="1000@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1174.75 820 L 1159.28 820 L 1159.28 833.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="990">
   <path class="kv10" d="M 1201.64 910.02 L 1201.64 886.25 L 1174.75 886.25" stroke-width="1" zvalue="878"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="997@0" LinkObjectIDznd="299" MaxPinNum="2"/>
   </metadata>
  <path d="M 1201.64 910.02 L 1201.64 886.25 L 1174.75 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="986">
   <path class="kv10" d="M 1246.75 760.56 L 1246.75 736.09" stroke-width="1" zvalue="884"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="987@0" LinkObjectIDznd="1002@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1246.75 760.56 L 1246.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="980">
   <path class="kv10" d="M 1263.18 834.35 L 1263.18 819.75 L 1246.75 819.75" stroke-width="1" zvalue="891"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="989@0" LinkObjectIDznd="185" MaxPinNum="2"/>
   </metadata>
  <path d="M 1263.18 834.35 L 1263.18 819.75 L 1246.75 819.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="979">
   <path class="kv10" d="M 1246.75 820 L 1231.28 820 L 1231.28 833.06" stroke-width="1" zvalue="892"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185" LinkObjectIDznd="988@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1246.75 820 L 1231.28 820 L 1231.28 833.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="978">
   <path class="kv10" d="M 1273.64 910.02 L 1273.64 886.25 L 1246.75 886.25" stroke-width="1" zvalue="893"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="985@0" LinkObjectIDznd="185" MaxPinNum="2"/>
   </metadata>
  <path d="M 1273.64 910.02 L 1273.64 886.25 L 1246.75 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="974">
   <path class="kv10" d="M 1318.75 760.56 L 1318.75 736.09" stroke-width="1" zvalue="899"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="975@0" LinkObjectIDznd="1002@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1318.75 760.56 L 1318.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="968">
   <path class="kv10" d="M 1335.18 834.35 L 1335.18 819.75 L 1318.75 819.75" stroke-width="1" zvalue="906"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="977@0" LinkObjectIDznd="186" MaxPinNum="2"/>
   </metadata>
  <path d="M 1335.18 834.35 L 1335.18 819.75 L 1318.75 819.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="967">
   <path class="kv10" d="M 1318.75 820 L 1303.28 820 L 1303.28 833.06" stroke-width="1" zvalue="907"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186" LinkObjectIDznd="976@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1318.75 820 L 1303.28 820 L 1303.28 833.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="966">
   <path class="kv10" d="M 1345.64 910.02 L 1345.64 886.25 L 1318.75 886.25" stroke-width="1" zvalue="908"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="973@0" LinkObjectIDznd="186" MaxPinNum="2"/>
   </metadata>
  <path d="M 1345.64 910.02 L 1345.64 886.25 L 1318.75 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="962">
   <path class="kv10" d="M 1387.15 760.56 L 1387.15 736.09" stroke-width="1" zvalue="914"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="963@0" LinkObjectIDznd="1002@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1387.15 760.56 L 1387.15 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="956">
   <path class="kv10" d="M 1403.18 834.35 L 1403.18 819.75 L 1387.15 819.75" stroke-width="1" zvalue="921"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="965@0" LinkObjectIDznd="187" MaxPinNum="2"/>
   </metadata>
  <path d="M 1403.18 834.35 L 1403.18 819.75 L 1387.15 819.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="955">
   <path class="kv10" d="M 1387.15 820 L 1371.28 820 L 1371.28 833.06" stroke-width="1" zvalue="922"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187" LinkObjectIDznd="964@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1387.15 820 L 1371.28 820 L 1371.28 833.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="954">
   <path class="kv10" d="M 1413.64 910.02 L 1413.64 886.25 L 1387.15 886.25" stroke-width="1" zvalue="923"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="961@0" LinkObjectIDznd="187" MaxPinNum="2"/>
   </metadata>
  <path d="M 1413.64 910.02 L 1413.64 886.25 L 1387.15 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="950">
   <path class="kv10" d="M 1454.75 760.56 L 1454.75 736.09" stroke-width="1" zvalue="929"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="951@0" LinkObjectIDznd="1002@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1454.75 760.56 L 1454.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="944">
   <path class="kv10" d="M 1471.18 834.35 L 1471.18 819.75 L 1454.75 819.75" stroke-width="1" zvalue="936"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="953@0" LinkObjectIDznd="188" MaxPinNum="2"/>
   </metadata>
  <path d="M 1471.18 834.35 L 1471.18 819.75 L 1454.75 819.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="943">
   <path class="kv10" d="M 1454.75 820 L 1439.28 820 L 1439.28 833.06" stroke-width="1" zvalue="937"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188" LinkObjectIDznd="952@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1454.75 820 L 1439.28 820 L 1439.28 833.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="941">
   <path class="kv10" d="M 1481.64 910.02 L 1481.64 886.25 L 1454.75 886.25" stroke-width="1" zvalue="938"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="949@0" LinkObjectIDznd="188" MaxPinNum="2"/>
   </metadata>
  <path d="M 1481.64 910.02 L 1481.64 886.25 L 1454.75 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="935">
   <path class="kv10" d="M 1518.75 760.56 L 1518.75 736.09" stroke-width="1" zvalue="944"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="936@0" LinkObjectIDznd="1002@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1518.75 760.56 L 1518.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="929">
   <path class="kv10" d="M 1535.18 834.35 L 1535.18 819.75 L 1518.75 819.75" stroke-width="1" zvalue="951"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="940@0" LinkObjectIDznd="189" MaxPinNum="2"/>
   </metadata>
  <path d="M 1535.18 834.35 L 1535.18 819.75 L 1518.75 819.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="928">
   <path class="kv10" d="M 1518.75 820 L 1503.28 820 L 1503.28 833.06" stroke-width="1" zvalue="952"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189" LinkObjectIDznd="938@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1518.75 820 L 1503.28 820 L 1503.28 833.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="927">
   <path class="kv10" d="M 1545.64 910.02 L 1545.64 886.25 L 1518.75 886.25" stroke-width="1" zvalue="953"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="934@0" LinkObjectIDznd="189" MaxPinNum="2"/>
   </metadata>
  <path d="M 1545.64 910.02 L 1545.64 886.25 L 1518.75 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="923">
   <path class="kv10" d="M 1586.75 760.56 L 1586.75 736.09" stroke-width="1" zvalue="959"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="924@0" LinkObjectIDznd="1002@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1586.75 760.56 L 1586.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="917">
   <path class="kv10" d="M 1604.18 833.35 L 1604.18 819.75 L 1586.75 819.75" stroke-width="1" zvalue="966"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="926@0" LinkObjectIDznd="190" MaxPinNum="2"/>
   </metadata>
  <path d="M 1604.18 833.35 L 1604.18 819.75 L 1586.75 819.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="916">
   <path class="kv10" d="M 1586.75 820 L 1571.28 820 L 1571.28 833.06" stroke-width="1" zvalue="967"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190" LinkObjectIDznd="925@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1586.75 820 L 1571.28 820 L 1571.28 833.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="915">
   <path class="kv10" d="M 1613.64 910.02 L 1613.64 886.25 L 1586.75 886.25" stroke-width="1" zvalue="968"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="922@0" LinkObjectIDznd="190" MaxPinNum="2"/>
   </metadata>
  <path d="M 1613.64 910.02 L 1613.64 886.25 L 1586.75 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="911">
   <path class="kv10" d="M 1654.75 760.56 L 1654.75 736.09" stroke-width="1" zvalue="974"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="912@0" LinkObjectIDznd="1002@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1654.75 760.56 L 1654.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="903">
   <path class="kv10" d="M 1671.18 834.35 L 1671.18 819.75 L 1654.75 819.75" stroke-width="1" zvalue="981"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="914@0" LinkObjectIDznd="191" MaxPinNum="2"/>
   </metadata>
  <path d="M 1671.18 834.35 L 1671.18 819.75 L 1654.75 819.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="902">
   <path class="kv10" d="M 1654.75 820 L 1639.28 820 L 1639.28 833.06" stroke-width="1" zvalue="982"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191" LinkObjectIDznd="913@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1654.75 820 L 1639.28 820 L 1639.28 833.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="899">
   <path class="kv10" d="M 1681.64 910.02 L 1681.64 886.25 L 1654.75 886.25" stroke-width="1" zvalue="983"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="910@0" LinkObjectIDznd="191" MaxPinNum="2"/>
   </metadata>
  <path d="M 1681.64 910.02 L 1681.64 886.25 L 1654.75 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="890">
   <path class="kv10" d="M 1722.75 760.56 L 1722.75 736.09" stroke-width="1" zvalue="989"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="891@0" LinkObjectIDznd="1002@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1722.75 760.56 L 1722.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="883">
   <path class="kv10" d="M 1739.18 834.35 L 1739.18 819.75 L 1722.75 819.75" stroke-width="1" zvalue="996"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="893@0" LinkObjectIDznd="192" MaxPinNum="2"/>
   </metadata>
  <path d="M 1739.18 834.35 L 1739.18 819.75 L 1722.75 819.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="882">
   <path class="kv10" d="M 1722.75 820 L 1707.28 820 L 1707.28 833.06" stroke-width="1" zvalue="997"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192" LinkObjectIDznd="892@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1722.75 820 L 1707.28 820 L 1707.28 833.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="881">
   <path class="kv10" d="M 1749.64 910.02 L 1749.64 886.25 L 1722.75 886.25" stroke-width="1" zvalue="998"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="889@0" LinkObjectIDznd="192" MaxPinNum="2"/>
   </metadata>
  <path d="M 1749.64 910.02 L 1749.64 886.25 L 1722.75 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="877">
   <path class="kv10" d="M 1790.75 760.56 L 1790.75 736.09" stroke-width="1" zvalue="1004"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="878@0" LinkObjectIDznd="1002@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1790.75 760.56 L 1790.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="870">
   <path class="kv10" d="M 1807.18 834.35 L 1807.18 819.75 L 1790.75 819.75" stroke-width="1" zvalue="1011"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="880@0" LinkObjectIDznd="193" MaxPinNum="2"/>
   </metadata>
  <path d="M 1807.18 834.35 L 1807.18 819.75 L 1790.75 819.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="869">
   <path class="kv10" d="M 1790.75 820 L 1775.28 820 L 1775.28 833.06" stroke-width="1" zvalue="1012"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193" LinkObjectIDznd="879@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1790.75 820 L 1775.28 820 L 1775.28 833.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="868">
   <path class="kv10" d="M 1817.64 910.02 L 1817.64 886.25 L 1790.75 886.25" stroke-width="1" zvalue="1013"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="876@0" LinkObjectIDznd="193" MaxPinNum="2"/>
   </metadata>
  <path d="M 1817.64 910.02 L 1817.64 886.25 L 1790.75 886.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1007">
   <path class="kv110" d="M 1084.06 131.94 L 1051.02 131.94" stroke-width="1" zvalue="1017"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1006@0" LinkObjectIDznd="627" MaxPinNum="2"/>
   </metadata>
  <path d="M 1084.06 131.94 L 1051.02 131.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1009">
   <path class="kv10" d="M 456.25 696.77 L 456.25 736.09" stroke-width="1" zvalue="1020"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1008@1" LinkObjectIDznd="656@10" MaxPinNum="2"/>
   </metadata>
  <path d="M 456.25 696.77 L 456.25 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1014">
   <path class="kv10" d="M 431.18 625.98 L 431.18 645 L 456.43 645" stroke-width="1" zvalue="1025"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1013@0" LinkObjectIDznd="162" MaxPinNum="2"/>
   </metadata>
  <path d="M 431.18 625.98 L 431.18 645 L 456.43 645" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1017">
   <path class="kv10" d="M 427.14 522.12 L 427.14 499.58" stroke-width="1" zvalue="1029"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1015@0" LinkObjectIDznd="1018@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 427.14 522.12 L 427.14 499.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1033">
   <path class="kv10" d="M 560.25 666.81 L 560.25 577.75" stroke-width="1" zvalue="1046"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1026@0" LinkObjectIDznd="1029@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 560.25 666.81 L 560.25 577.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1048">
   <path class="kv10" d="M 560.25 640 L 531.18 640 L 531.18 625.98" stroke-width="1" zvalue="1061"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1033" LinkObjectIDznd="1025@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 560.25 640 L 531.18 640 L 531.18 625.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1049">
   <path class="kv10" d="M 560.25 641.25 L 595.28 641.25 L 595.28 628" stroke-width="1" zvalue="1062"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1033" LinkObjectIDznd="1028@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 560.25 641.25 L 595.28 641.25 L 595.28 628" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1069">
   <path class="kv110" d="M 818.95 382.85 L 818.95 362.34" stroke-width="1" zvalue="1082"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1067@0" LinkObjectIDznd="657@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.95 382.85 L 818.95 362.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1070">
   <path class="kv110" d="M 818.98 402.48 L 818.98 441.06" stroke-width="1" zvalue="1083"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1067@1" LinkObjectIDznd="1068@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.98 402.48 L 818.98 441.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1071">
   <path class="kv110" d="M 778.82 421.62 L 818.98 421.62" stroke-width="1" zvalue="1084"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1063@0" LinkObjectIDznd="1070" MaxPinNum="2"/>
   </metadata>
  <path d="M 778.82 421.62 L 818.98 421.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1072">
   <path class="kv110" d="M 817.98 466.95 L 817.98 502.12" stroke-width="1" zvalue="1085"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1068@1" LinkObjectIDznd="1064@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 817.98 466.95 L 817.98 502.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1073">
   <path class="kv110" d="M 781.57 488.41 L 817.98 488.41" stroke-width="1" zvalue="1086"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1062@0" LinkObjectIDznd="1072" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.57 488.41 L 817.98 488.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1076">
   <path class="kv110" d="M 819.17 521.75 L 819.17 552.67" stroke-width="1" zvalue="1088"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1064@1" LinkObjectIDznd="1074@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 819.17 521.75 L 819.17 552.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1078">
   <path class="kv110" d="M 786.57 533.41 L 819.17 533.41" stroke-width="1" zvalue="1091"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1077@0" LinkObjectIDznd="1076" MaxPinNum="2"/>
   </metadata>
  <path d="M 786.57 533.41 L 819.17 533.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1081">
   <path class="kv10" d="M 819.15 608.6 L 819.15 659.56" stroke-width="1" zvalue="1094"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1074@1" LinkObjectIDznd="1080@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 819.15 608.6 L 819.15 659.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1082">
   <path class="kv10" d="M 818.15 689.52 L 818.15 736.09" stroke-width="1" zvalue="1095"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1080@1" LinkObjectIDznd="656@11" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.15 689.52 L 818.15 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1084">
   <path class="kv10" d="M 778.28 638.75 L 819.15 638.75" stroke-width="1" zvalue="1098"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1083@0" LinkObjectIDznd="1081" MaxPinNum="2"/>
   </metadata>
  <path d="M 778.28 638.75 L 819.15 638.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="kv10" d="M 560.22 558.12 L 560.22 498.88" stroke-width="1" zvalue="1103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1029@0" LinkObjectIDznd="452@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 560.22 558.12 L 560.22 498.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv10" d="M 544.95 506.88 L 544.95 498.88 L 560.22 498.88" stroke-width="1" zvalue="1105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="448@0" LinkObjectIDznd="2" MaxPinNum="2"/>
   </metadata>
  <path d="M 544.95 506.88 L 544.95 498.88 L 560.22 498.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv10" d="M 676.25 636.6 L 676.25 604.36" stroke-width="1" zvalue="1108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8@1" LinkObjectIDznd="1050@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 676.25 636.6 L 676.25 604.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv10" d="M 676.25 684.86 L 676.25 736.09" stroke-width="1" zvalue="1109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8@0" LinkObjectIDznd="656@12" MaxPinNum="2"/>
   </metadata>
  <path d="M 676.25 684.86 L 676.25 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv10" d="M 1004.25 666.81 L 1004.25 567.58" stroke-width="1" zvalue="1119"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@0" LinkObjectIDznd="46@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004.25 666.81 L 1004.25 567.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv10" d="M 1004.25 640 L 975.18 640 L 975.18 625.98" stroke-width="1" zvalue="1121"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21" LinkObjectIDznd="25@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004.25 640 L 975.18 640 L 975.18 625.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv10" d="M 1004.25 641.25 L 1039.28 641.25 L 1039.28 628" stroke-width="1" zvalue="1122"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21" LinkObjectIDznd="24@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004.25 641.25 L 1039.28 641.25 L 1039.28 628" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv10" d="M 1720.25 666.81 L 1720.25 577.75" stroke-width="1" zvalue="1138"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="41@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1720.25 666.81 L 1720.25 577.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv10" d="M 1720.25 640 L 1691.18 640 L 1691.18 625.98" stroke-width="1" zvalue="1140"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39" LinkObjectIDznd="43@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1720.25 640 L 1691.18 640 L 1691.18 625.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv10" d="M 1720.25 641.25 L 1755.28 641.25 L 1755.28 628" stroke-width="1" zvalue="1141"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39" LinkObjectIDznd="42@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1720.25 641.25 L 1755.28 641.25 L 1755.28 628" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv10" d="M 1720.22 558.12 L 1720.22 498.88" stroke-width="1" zvalue="1146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1720.22 558.12 L 1720.22 498.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="kv10" d="M 1704.95 506.88 L 1704.95 498.88 L 1720.22 498.88" stroke-width="1" zvalue="1147"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@0" LinkObjectIDznd="33" MaxPinNum="2"/>
   </metadata>
  <path d="M 1704.95 506.88 L 1704.95 498.88 L 1720.22 498.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv10" d="M 1004.25 696.77 L 1004.25 736.09" stroke-width="1" zvalue="1148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@1" LinkObjectIDznd="656@13" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004.25 696.77 L 1004.25 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv10" d="M 560.25 696.77 L 560.25 736.09" stroke-width="1" zvalue="1150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1026@1" LinkObjectIDznd="656@14" MaxPinNum="2"/>
   </metadata>
  <path d="M 560.25 696.77 L 560.25 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv10" d="M 1812.25 666.81 L 1812.25 559.58" stroke-width="1" zvalue="1155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="50@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1812.25 666.81 L 1812.25 559.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv10" d="M 1812.25 640 L 1783.18 640 L 1783.18 625.98" stroke-width="1" zvalue="1156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54" LinkObjectIDznd="56@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1812.25 640 L 1783.18 640 L 1783.18 625.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv10" d="M 1812.25 641.25 L 1847.28 641.25 L 1847.28 628" stroke-width="1" zvalue="1157"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54" LinkObjectIDznd="55@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1812.25 641.25 L 1847.28 641.25 L 1847.28 628" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv10" d="M 1812.25 696.77 L 1812.25 736.09" stroke-width="1" zvalue="1158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@1" LinkObjectIDznd="1002@10" MaxPinNum="2"/>
   </metadata>
  <path d="M 1812.25 696.77 L 1812.25 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv10" d="M 1720.25 696.77 L 1720.25 736.09" stroke-width="1" zvalue="1161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@1" LinkObjectIDznd="1002@11" MaxPinNum="2"/>
   </metadata>
  <path d="M 1720.25 696.77 L 1720.25 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv10" d="M 1586.75 636.6 L 1586.75 604.36" stroke-width="1" zvalue="1172"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@1" LinkObjectIDznd="71@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1586.75 636.6 L 1586.75 604.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv10" d="M 1586.75 684.86 L 1586.75 736.09" stroke-width="1" zvalue="1174"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="1002@12" MaxPinNum="2"/>
   </metadata>
  <path d="M 1586.75 684.86 L 1586.75 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv110" d="M 884.28 565.78 L 819.15 565.78" stroke-width="1" zvalue="1175"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="1074@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 884.28 565.78 L 819.15 565.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv110" d="M 1385.8 382.85 L 1385.8 362.34" stroke-width="1" zvalue="1187"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="657@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1385.8 382.85 L 1385.8 362.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv110" d="M 1385.82 402.48 L 1385.82 441.06" stroke-width="1" zvalue="1188"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@1" LinkObjectIDznd="103@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1385.82 402.48 L 1385.82 441.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv110" d="M 1346.82 421.62 L 1385.82 421.62" stroke-width="1" zvalue="1189"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@0" LinkObjectIDznd="97" MaxPinNum="2"/>
   </metadata>
  <path d="M 1346.82 421.62 L 1385.82 421.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv110" d="M 1385.98 466.95 L 1385.98 502.12" stroke-width="1" zvalue="1190"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@1" LinkObjectIDznd="101@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1385.98 466.95 L 1385.98 502.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv110" d="M 1349.57 488.41 L 1385.98 488.41" stroke-width="1" zvalue="1191"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="95" MaxPinNum="2"/>
   </metadata>
  <path d="M 1349.57 488.41 L 1385.98 488.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv110" d="M 1387.17 521.75 L 1387.17 552.67" stroke-width="1" zvalue="1193"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@1" LinkObjectIDznd="93@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1387.17 521.75 L 1387.17 552.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv110" d="M 1354.57 533.41 L 1387.17 533.41" stroke-width="1" zvalue="1197"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="92" MaxPinNum="2"/>
   </metadata>
  <path d="M 1354.57 533.41 L 1387.17 533.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv10" d="M 1387.15 608.6 L 1387.15 661.56" stroke-width="1" zvalue="1199"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@1" LinkObjectIDznd="89@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1387.15 608.6 L 1387.15 661.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv10" d="M 1346.28 638.75 L 1387.15 638.75" stroke-width="1" zvalue="1203"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="88" MaxPinNum="2"/>
   </metadata>
  <path d="M 1346.28 638.75 L 1387.15 638.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv110" d="M 1452.28 565.78 L 1387.15 565.78" stroke-width="1" zvalue="1206"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="93@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1452.28 565.78 L 1387.15 565.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv10" d="M 1387.15 691.52 L 1387.15 736.09" stroke-width="1" zvalue="1207"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@1" LinkObjectIDznd="1002@13" MaxPinNum="2"/>
   </metadata>
  <path d="M 1387.15 691.52 L 1387.15 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv10" d="M 1232.25 696.77 L 1232.25 736.09" stroke-width="1" zvalue="1211"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@1" LinkObjectIDznd="1002@14" MaxPinNum="2"/>
   </metadata>
  <path d="M 1232.25 696.77 L 1232.25 736.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 1232.22 549.12 L 1232.22 518.58" stroke-width="1" zvalue="1221"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="109@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1232.22 549.12 L 1232.22 518.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv10" d="M 1232.25 666.81 L 1232.25 612.48" stroke-width="1" zvalue="1227"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@0" LinkObjectIDznd="122@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1232.25 666.81 L 1232.25 612.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv10" d="M 1232.25 615.11 L 1232.25 568.75" stroke-width="1" zvalue="1228"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125" LinkObjectIDznd="112@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1232.25 615.11 L 1232.25 568.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv10" d="M 1207.18 625.98 L 1207.18 643 L 1232.25 643" stroke-width="1" zvalue="1229"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="125" MaxPinNum="2"/>
   </metadata>
  <path d="M 1207.18 625.98 L 1207.18 643 L 1232.25 643" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv110" d="M 983.95 407.85 L 983.95 362.34" stroke-width="1" zvalue="1238"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@0" LinkObjectIDznd="657@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 983.95 407.85 L 983.95 362.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv110" d="M 983.98 427.48 L 983.98 462.64" stroke-width="1" zvalue="1241"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@1" LinkObjectIDznd="136@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 983.98 427.48 L 983.98 462.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="kv110" d="M 958.82 390.38 L 983.95 390.38" stroke-width="1" zvalue="1242"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@0" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 958.82 390.38 L 983.95 390.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv110" d="M 959.82 450.62 L 983.98 450.62" stroke-width="1" zvalue="1243"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="137" MaxPinNum="2"/>
   </metadata>
  <path d="M 959.82 450.62 L 983.98 450.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv10" d="M 467.5 276.75 L 467.5 248.25" stroke-width="1" zvalue="1249"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@0" LinkObjectIDznd="148@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 467.5 276.75 L 467.5 248.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv10" d="M 467.08 222.75 L 467.04 175.79" stroke-width="1" zvalue="1250"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="148@0" LinkObjectIDznd="152@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 467.08 222.75 L 467.04 175.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv10" d="M 513.03 187.37 L 513.03 206.56 L 467.07 206.56" stroke-width="1" zvalue="1251"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="150" MaxPinNum="2"/>
   </metadata>
  <path d="M 513.03 187.37 L 513.03 206.56 L 467.07 206.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv10" d="M 456.43 601.69 L 456.43 666.81" stroke-width="1" zvalue="1262"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249" LinkObjectIDznd="1008@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 456.43 601.69 L 456.43 666.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv10" d="M 490.75 790.52 L 490.75 904.78" stroke-width="1" zvalue="1264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="687@1" LinkObjectIDznd="251@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 490.75 790.52 L 490.75 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv10" d="M 562.75 790.52 L 562.75 904.78" stroke-width="1" zvalue="1268"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="702@1" LinkObjectIDznd="253@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 562.75 790.52 L 562.75 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv10" d="M 630.75 790.52 L 630.75 904.78" stroke-width="1" zvalue="1272"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="719@1" LinkObjectIDznd="255@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 630.75 790.52 L 630.75 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv10" d="M 698.75 790.52 L 698.75 904.78" stroke-width="1" zvalue="1273"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="734@1" LinkObjectIDznd="257@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 698.75 790.52 L 698.75 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv10" d="M 762.75 790.52 L 762.75 904.78" stroke-width="1" zvalue="1274"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="750@1" LinkObjectIDznd="259@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 762.75 790.52 L 762.75 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv10" d="M 830.75 790.52 L 830.75 904.78" stroke-width="1" zvalue="1275"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="765@1" LinkObjectIDznd="261@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 830.75 790.52 L 830.75 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="kv10" d="M 898.75 790.52 L 898.75 904.78" stroke-width="1" zvalue="1276"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="780@1" LinkObjectIDznd="263@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.75 790.52 L 898.75 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv10" d="M 966.75 790.52 L 966.75 904.78" stroke-width="1" zvalue="1277"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="800@1" LinkObjectIDznd="265@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.75 790.52 L 966.75 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv10" d="M 1034.75 790.52 L 1034.75 904.78" stroke-width="1" zvalue="1278"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="817@1" LinkObjectIDznd="268@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1034.75 790.52 L 1034.75 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv10" d="M 1246.75 790.52 L 1246.75 904.78" stroke-width="1" zvalue="1280"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="987@1" LinkObjectIDznd="274@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1246.75 790.52 L 1246.75 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv10" d="M 1318.75 790.52 L 1318.75 904.78" stroke-width="1" zvalue="1281"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="975@1" LinkObjectIDznd="277@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1318.75 790.52 L 1318.75 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv10" d="M 1387.15 790.52 L 1387.15 904.78" stroke-width="1" zvalue="1282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="963@1" LinkObjectIDznd="279@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1387.15 790.52 L 1387.15 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv10" d="M 1454.75 790.52 L 1454.75 904.78" stroke-width="1" zvalue="1283"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="951@1" LinkObjectIDznd="282@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1454.75 790.52 L 1454.75 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv10" d="M 1518.75 790.52 L 1518.75 904.78" stroke-width="1" zvalue="1284"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="936@1" LinkObjectIDznd="284@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1518.75 790.52 L 1518.75 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv10" d="M 1586.75 790.52 L 1586.75 904.78" stroke-width="1" zvalue="1285"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="924@1" LinkObjectIDznd="286@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1586.75 790.52 L 1586.75 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="kv10" d="M 1654.75 790.52 L 1654.75 904.78" stroke-width="1" zvalue="1286"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="912@1" LinkObjectIDznd="288@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1654.75 790.52 L 1654.75 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="kv10" d="M 1722.75 790.52 L 1722.75 904.78" stroke-width="1" zvalue="1287"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="891@1" LinkObjectIDznd="290@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1722.75 790.52 L 1722.75 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv10" d="M 1790.75 790.52 L 1790.75 904.78" stroke-width="1" zvalue="1288"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="878@1" LinkObjectIDznd="292@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1790.75 790.52 L 1790.75 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="249">
   <path class="kv10" d="M 456.43 601.69 L 456.43 571 L 427.17 571 L 427.17 541.75" stroke-width="1" zvalue="1341"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="297" LinkObjectIDznd="1015@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 456.43 601.69 L 456.43 571 L 427.17 571 L 427.17 541.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="294">
   <path class="kv10" d="M 418.75 790.52 L 418.75 904.78" stroke-width="1" zvalue="1399"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="312@1" LinkObjectIDznd="664@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 418.75 790.52 L 418.75 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="297">
   <path class="kv10" d="M 456.43 601.69 L 456.43 601.69" stroke-width="1" zvalue="1402"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1010@0" LinkObjectIDznd="162" MaxPinNum="2"/>
   </metadata>
  <path d="M 456.43 601.69 L 456.43 601.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="299">
   <path class="kv10" d="M 1174.75 790.52 L 1174.75 904.78" stroke-width="1" zvalue="1404"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="999@1" LinkObjectIDznd="271@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1174.75 790.52 L 1174.75 904.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="300">
   <path class="kv10" d="M 435.18 834.35 L 435.18 817 L 418.75 817" stroke-width="1" zvalue="1405"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="566@0" LinkObjectIDznd="294" MaxPinNum="2"/>
   </metadata>
  <path d="M 435.18 834.35 L 435.18 817 L 418.75 817" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="301">
   <path class="kv10" d="M 399.28 837.06 L 399.28 817 L 418.75 817" stroke-width="1" zvalue="1406"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="562@0" LinkObjectIDznd="300" MaxPinNum="2"/>
   </metadata>
  <path d="M 399.28 837.06 L 399.28 817 L 418.75 817" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="562">
   <use class="kv10" height="26" transform="rotate(0,399.306,848.528) scale(-0.838049,0.927421) translate(-876.75,65.4614)" width="12" x="394.2781809263649" xlink:href="#Accessory:避雷器1_0" y="836.4718190736352" zvalue="139"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449590919174" ObjectName="出线1避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,399.306,848.528) scale(-0.838049,0.927421) translate(-876.75,65.4614)" width="12" x="394.2781809263649" y="836.4718190736352"/></g>
  <g id="660">
   <use class="kv10" height="35" transform="rotate(0,448.915,922.562) scale(0.727041,0.727041) translate(165.401,341.589)" width="23" x="440.5535714285713" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="705"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449591115782" ObjectName="出线1设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,448.915,922.562) scale(0.727041,0.727041) translate(165.401,341.589)" width="23" x="440.5535714285713" y="909.8392857142857"/></g>
  <g id="688">
   <use class="kv10" height="26" transform="rotate(0,475.306,844.528) scale(-0.838049,0.927421) translate(-1043.44,65.1484)" width="12" x="470.2781809263649" xlink:href="#Accessory:避雷器1_0" y="832.4718190736352" zvalue="721"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449591312390" ObjectName="出线2避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,475.306,844.528) scale(-0.838049,0.927421) translate(-1043.44,65.1484)" width="12" x="470.2781809263649" y="832.4718190736352"/></g>
  <g id="685">
   <use class="kv10" height="35" transform="rotate(0,520.915,922.562) scale(0.727041,0.727041) translate(192.432,341.589)" width="23" x="512.5535714285713" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="725"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449591246854" ObjectName="出线2设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,520.915,922.562) scale(0.727041,0.727041) translate(192.432,341.589)" width="23" x="512.5535714285713" y="909.8392857142857"/></g>
  <g id="703">
   <use class="kv10" height="26" transform="rotate(0,547.306,844.528) scale(-0.838049,0.927421) translate(-1201.35,65.1484)" width="12" x="542.2781809263649" xlink:href="#Accessory:避雷器1_0" y="832.4718190736352" zvalue="737"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449591574534" ObjectName="出线3避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,547.306,844.528) scale(-0.838049,0.927421) translate(-1201.35,65.1484)" width="12" x="542.2781809263649" y="832.4718190736352"/></g>
  <g id="700">
   <use class="kv10" height="35" transform="rotate(0,592.915,922.562) scale(0.727041,0.727041) translate(219.464,341.589)" width="23" x="584.5535714285713" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="741"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449591508998" ObjectName="出线3设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,592.915,922.562) scale(0.727041,0.727041) translate(219.464,341.589)" width="23" x="584.5535714285713" y="909.8392857142857"/></g>
  <g id="720">
   <use class="kv10" height="26" transform="rotate(0,615.306,844.528) scale(-0.838049,0.927421) translate(-1350.49,65.1484)" width="12" x="610.2781809263649" xlink:href="#Accessory:避雷器1_0" y="832.4718190736352" zvalue="753"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449591836678" ObjectName="出线4避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,615.306,844.528) scale(-0.838049,0.927421) translate(-1350.49,65.1484)" width="12" x="610.2781809263649" y="832.4718190736352"/></g>
  <g id="716">
   <use class="kv10" height="35" transform="rotate(0,660.915,922.562) scale(0.727041,0.727041) translate(244.994,341.589)" width="23" x="652.5535714285713" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="757"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449591771142" ObjectName="出线4设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,660.915,922.562) scale(0.727041,0.727041) translate(244.994,341.589)" width="23" x="652.5535714285713" y="909.8392857142857"/></g>
  <g id="735">
   <use class="kv10" height="26" transform="rotate(0,683.306,844.528) scale(-0.838049,0.927421) translate(-1499.63,65.1484)" width="12" x="678.2781809263649" xlink:href="#Accessory:避雷器1_0" y="832.4718190736352" zvalue="769"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449592098822" ObjectName="出线5避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,683.306,844.528) scale(-0.838049,0.927421) translate(-1499.63,65.1484)" width="12" x="678.2781809263649" y="832.4718190736352"/></g>
  <g id="732">
   <use class="kv10" height="35" transform="rotate(0,728.915,922.562) scale(0.727041,0.727041) translate(270.524,341.589)" width="23" x="720.5535714285713" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="773"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449592033286" ObjectName="出线5设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,728.915,922.562) scale(0.727041,0.727041) translate(270.524,341.589)" width="23" x="720.5535714285713" y="909.8392857142857"/></g>
  <g id="751">
   <use class="kv10" height="26" transform="rotate(0,747.306,844.528) scale(-0.838049,0.927421) translate(-1640,65.1484)" width="12" x="742.2781809263649" xlink:href="#Accessory:避雷器1_0" y="832.4718190736352" zvalue="785"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449592360966" ObjectName="出线6避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,747.306,844.528) scale(-0.838049,0.927421) translate(-1640,65.1484)" width="12" x="742.2781809263649" y="832.4718190736352"/></g>
  <g id="748">
   <use class="kv10" height="35" transform="rotate(0,792.915,922.562) scale(0.727041,0.727041) translate(294.552,341.589)" width="23" x="784.5535714285713" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="789"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449592295429" ObjectName="出线6设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,792.915,922.562) scale(0.727041,0.727041) translate(294.552,341.589)" width="23" x="784.5535714285713" y="909.8392857142857"/></g>
  <g id="766">
   <use class="kv10" height="26" transform="rotate(0,815.306,844.528) scale(-0.838049,0.927421) translate(-1789.14,65.1484)" width="12" x="810.2781809263649" xlink:href="#Accessory:避雷器1_0" y="832.4718190736352" zvalue="801"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449592623109" ObjectName="出线7避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,815.306,844.528) scale(-0.838049,0.927421) translate(-1789.14,65.1484)" width="12" x="810.2781809263649" y="832.4718190736352"/></g>
  <g id="763">
   <use class="kv10" height="35" transform="rotate(0,860.915,922.562) scale(0.727041,0.727041) translate(320.082,341.589)" width="23" x="852.5535714285713" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="805"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449592557574" ObjectName="出线7设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,860.915,922.562) scale(0.727041,0.727041) translate(320.082,341.589)" width="23" x="852.5535714285713" y="909.8392857142857"/></g>
  <g id="781">
   <use class="kv10" height="26" transform="rotate(0,883.306,844.528) scale(-0.838049,0.927421) translate(-1938.28,65.1484)" width="12" x="878.2781809263649" xlink:href="#Accessory:避雷器1_0" y="832.4718190736352" zvalue="817"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449592885253" ObjectName="出线8避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,883.306,844.528) scale(-0.838049,0.927421) translate(-1938.28,65.1484)" width="12" x="878.2781809263649" y="832.4718190736352"/></g>
  <g id="778">
   <use class="kv10" height="35" transform="rotate(0,928.915,922.562) scale(0.727041,0.727041) translate(345.611,341.589)" width="23" x="920.5535714285713" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="821"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449592819717" ObjectName="出线8设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,928.915,922.562) scale(0.727041,0.727041) translate(345.611,341.589)" width="23" x="920.5535714285713" y="909.8392857142857"/></g>
  <g id="801">
   <use class="kv10" height="26" transform="rotate(0,951.306,844.528) scale(-0.838049,0.927421) translate(-2087.42,65.1484)" width="12" x="946.2781809263649" xlink:href="#Accessory:避雷器1_0" y="832.4718190736352" zvalue="833"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449593147397" ObjectName="出线9避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,951.306,844.528) scale(-0.838049,0.927421) translate(-2087.42,65.1484)" width="12" x="946.2781809263649" y="832.4718190736352"/></g>
  <g id="798">
   <use class="kv10" height="35" transform="rotate(0,996.915,922.562) scale(0.727041,0.727041) translate(371.141,341.589)" width="23" x="988.5535714285713" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="837"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449593081861" ObjectName="出线9设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,996.915,922.562) scale(0.727041,0.727041) translate(371.141,341.589)" width="23" x="988.5535714285713" y="909.8392857142857"/></g>
  <g id="818">
   <use class="kv10" height="26" transform="rotate(0,1019.31,844.528) scale(-0.838049,0.927421) translate(-2236.56,65.1484)" width="12" x="1014.278180926365" xlink:href="#Accessory:避雷器1_0" y="832.4718190736352" zvalue="849"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449593409542" ObjectName="出线10避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1019.31,844.528) scale(-0.838049,0.927421) translate(-2236.56,65.1484)" width="12" x="1014.278180926365" y="832.4718190736352"/></g>
  <g id="814">
   <use class="kv10" height="35" transform="rotate(0,1064.91,922.562) scale(0.727041,0.727041) translate(396.671,341.589)" width="23" x="1056.553571428571" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="853"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449593344005" ObjectName="出线10设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1064.91,922.562) scale(0.727041,0.727041) translate(396.671,341.589)" width="23" x="1056.553571428571" y="909.8392857142857"/></g>
  <g id="1000">
   <use class="kv10" height="26" transform="rotate(0,1159.31,844.528) scale(-0.838049,0.927421) translate(-2543.62,65.1484)" width="12" x="1154.278180926365" xlink:href="#Accessory:避雷器1_0" y="832.4718190736352" zvalue="866"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449596030982" ObjectName="出线12避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1159.31,844.528) scale(-0.838049,0.927421) translate(-2543.62,65.1484)" width="12" x="1154.278180926365" y="832.4718190736352"/></g>
  <g id="997">
   <use class="kv10" height="35" transform="rotate(0,1204.91,922.562) scale(0.727041,0.727041) translate(449.232,341.589)" width="23" x="1196.553571428571" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="870"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449595965446" ObjectName="出线12设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1204.91,922.562) scale(0.727041,0.727041) translate(449.232,341.589)" width="23" x="1196.553571428571" y="909.8392857142857"/></g>
  <g id="988">
   <use class="kv10" height="26" transform="rotate(0,1231.31,844.528) scale(-0.838049,0.927421) translate(-2701.53,65.1484)" width="12" x="1226.278180926365" xlink:href="#Accessory:避雷器1_0" y="832.4718190736352" zvalue="881"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449595768838" ObjectName="出线13避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1231.31,844.528) scale(-0.838049,0.927421) translate(-2701.53,65.1484)" width="12" x="1226.278180926365" y="832.4718190736352"/></g>
  <g id="985">
   <use class="kv10" height="35" transform="rotate(0,1276.91,922.562) scale(0.727041,0.727041) translate(476.264,341.589)" width="23" x="1268.553571428571" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="885"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449595703302" ObjectName="出线13设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1276.91,922.562) scale(0.727041,0.727041) translate(476.264,341.589)" width="23" x="1268.553571428571" y="909.8392857142857"/></g>
  <g id="976">
   <use class="kv10" height="26" transform="rotate(0,1303.31,844.528) scale(-0.838049,0.927421) translate(-2859.45,65.1484)" width="12" x="1298.278180926365" xlink:href="#Accessory:避雷器1_0" y="832.4718190736352" zvalue="896"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449595506694" ObjectName="出线14避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1303.31,844.528) scale(-0.838049,0.927421) translate(-2859.45,65.1484)" width="12" x="1298.278180926365" y="832.4718190736352"/></g>
  <g id="973">
   <use class="kv10" height="35" transform="rotate(0,1348.91,922.562) scale(0.727041,0.727041) translate(503.296,341.589)" width="23" x="1340.553571428571" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="900"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449595441158" ObjectName="出线14设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1348.91,922.562) scale(0.727041,0.727041) translate(503.296,341.589)" width="23" x="1340.553571428571" y="909.8392857142857"/></g>
  <g id="964">
   <use class="kv10" height="26" transform="rotate(0,1371.31,844.528) scale(-0.838049,0.927421) translate(-3008.59,65.1484)" width="12" x="1366.278180926365" xlink:href="#Accessory:避雷器1_0" y="832.4718190736352" zvalue="911"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449595244550" ObjectName="出线15避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1371.31,844.528) scale(-0.838049,0.927421) translate(-3008.59,65.1484)" width="12" x="1366.278180926365" y="832.4718190736352"/></g>
  <g id="961">
   <use class="kv10" height="35" transform="rotate(0,1416.91,922.562) scale(0.727041,0.727041) translate(528.825,341.589)" width="23" x="1408.553571428571" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="915"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449595179014" ObjectName="出线15设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1416.91,922.562) scale(0.727041,0.727041) translate(528.825,341.589)" width="23" x="1408.553571428571" y="909.8392857142857"/></g>
  <g id="952">
   <use class="kv10" height="26" transform="rotate(0,1439.31,844.528) scale(-0.838049,0.927421) translate(-3157.73,65.1484)" width="12" x="1434.278180926365" xlink:href="#Accessory:避雷器1_0" y="832.4718190736352" zvalue="926"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449594982406" ObjectName="出线16避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1439.31,844.528) scale(-0.838049,0.927421) translate(-3157.73,65.1484)" width="12" x="1434.278180926365" y="832.4718190736352"/></g>
  <g id="949">
   <use class="kv10" height="35" transform="rotate(0,1484.91,922.562) scale(0.727041,0.727041) translate(554.355,341.589)" width="23" x="1476.553571428571" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="930"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449594916870" ObjectName="出线16设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1484.91,922.562) scale(0.727041,0.727041) translate(554.355,341.589)" width="23" x="1476.553571428571" y="909.8392857142857"/></g>
  <g id="938">
   <use class="kv10" height="26" transform="rotate(0,1503.31,844.528) scale(-0.838049,0.927421) translate(-3298.1,65.1484)" width="12" x="1498.278180926365" xlink:href="#Accessory:避雷器1_0" y="832.4718190736352" zvalue="941"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449594720262" ObjectName="出线17避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1503.31,844.528) scale(-0.838049,0.927421) translate(-3298.1,65.1484)" width="12" x="1498.278180926365" y="832.4718190736352"/></g>
  <g id="934">
   <use class="kv10" height="35" transform="rotate(0,1548.91,922.562) scale(0.727041,0.727041) translate(578.383,341.589)" width="23" x="1540.553571428571" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="945"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449594654726" ObjectName="出线17设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1548.91,922.562) scale(0.727041,0.727041) translate(578.383,341.589)" width="23" x="1540.553571428571" y="909.8392857142857"/></g>
  <g id="925">
   <use class="kv10" height="26" transform="rotate(0,1571.31,844.528) scale(-0.838049,0.927421) translate(-3447.24,65.1484)" width="12" x="1566.278180926365" xlink:href="#Accessory:避雷器1_0" y="832.4718190736352" zvalue="956"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449594458118" ObjectName="出线18避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1571.31,844.528) scale(-0.838049,0.927421) translate(-3447.24,65.1484)" width="12" x="1566.278180926365" y="832.4718190736352"/></g>
  <g id="922">
   <use class="kv10" height="35" transform="rotate(0,1616.91,922.562) scale(0.727041,0.727041) translate(603.913,341.589)" width="23" x="1608.553571428571" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="960"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449594392582" ObjectName="出线18设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1616.91,922.562) scale(0.727041,0.727041) translate(603.913,341.589)" width="23" x="1608.553571428571" y="909.8392857142857"/></g>
  <g id="913">
   <use class="kv10" height="26" transform="rotate(0,1639.31,844.528) scale(-0.838049,0.927421) translate(-3596.38,65.1484)" width="12" x="1634.278180926365" xlink:href="#Accessory:避雷器1_0" y="832.4718190736352" zvalue="971"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449594195974" ObjectName="出线19避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1639.31,844.528) scale(-0.838049,0.927421) translate(-3596.38,65.1484)" width="12" x="1634.278180926365" y="832.4718190736352"/></g>
  <g id="910">
   <use class="kv10" height="35" transform="rotate(0,1684.91,922.562) scale(0.727041,0.727041) translate(629.443,341.589)" width="23" x="1676.553571428571" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="975"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449594130438" ObjectName="出线19设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1684.91,922.562) scale(0.727041,0.727041) translate(629.443,341.589)" width="23" x="1676.553571428571" y="909.8392857142857"/></g>
  <g id="892">
   <use class="kv10" height="26" transform="rotate(0,1707.31,844.528) scale(-0.838049,0.927421) translate(-3745.52,65.1484)" width="12" x="1702.278180926365" xlink:href="#Accessory:避雷器1_0" y="832.4718190736352" zvalue="986"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449593933829" ObjectName="出线20避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1707.31,844.528) scale(-0.838049,0.927421) translate(-3745.52,65.1484)" width="12" x="1702.278180926365" y="832.4718190736352"/></g>
  <g id="889">
   <use class="kv10" height="35" transform="rotate(0,1752.91,922.562) scale(0.727041,0.727041) translate(654.973,341.589)" width="23" x="1744.553571428571" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="990"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449593868294" ObjectName="出线20设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1752.91,922.562) scale(0.727041,0.727041) translate(654.973,341.589)" width="23" x="1744.553571428571" y="909.8392857142857"/></g>
  <g id="879">
   <use class="kv10" height="26" transform="rotate(0,1775.31,844.528) scale(-0.838049,0.927421) translate(-3894.66,65.1484)" width="12" x="1770.278180926365" xlink:href="#Accessory:避雷器1_0" y="832.4718190736352" zvalue="1001"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449593671686" ObjectName="出线21避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1775.31,844.528) scale(-0.838049,0.927421) translate(-3894.66,65.1484)" width="12" x="1770.278180926365" y="832.4718190736352"/></g>
  <g id="876">
   <use class="kv10" height="35" transform="rotate(0,1820.91,922.562) scale(0.727041,0.727041) translate(680.503,341.589)" width="23" x="1812.553571428571" xlink:href="#Accessory:接地变20201012_0" y="909.8392857142857" zvalue="1005"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449593606150" ObjectName="出线21设备1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1820.91,922.562) scale(0.727041,0.727041) translate(680.503,341.589)" width="23" x="1812.553571428571" y="909.8392857142857"/></g>
  <g id="1005">
   <use class="kv110" height="20" transform="rotate(90,1010.94,142.688) scale(-1.90625,1.90625) translate(-1532.2,-58.7725)" width="20" x="991.875" xlink:href="#Accessory:线路PT3_0" y="123.625" zvalue="1015"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449596227590" ObjectName="110kV槟晟线PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1010.94,142.688) scale(-1.90625,1.90625) translate(-1532.2,-58.7725)" width="20" x="991.875" y="123.625"/></g>
  <g id="1006">
   <use class="kv110" height="40" transform="rotate(0,1101.25,142) scale(-1.25,1.25) translate(-1977.25,-23.4)" width="40" x="1076.25" xlink:href="#Accessory:PT1111_0" y="117" zvalue="1016"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449596293125" ObjectName="110kV槟晟线PT1"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1101.25,142) scale(-1.25,1.25) translate(-1977.25,-23.4)" width="40" x="1076.25" y="117"/></g>
  <g id="1028">
   <use class="kv10" height="26" transform="rotate(0,595.306,616.528) scale(-0.838049,-0.927421) translate(-1306.63,-1282.25)" width="12" x="590.2781809263649" xlink:href="#Accessory:避雷器1_0" y="604.4718190736352" zvalue="1040"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449596817414" ObjectName="#1电容器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,595.306,616.528) scale(-0.838049,-0.927421) translate(-1306.63,-1282.25)" width="12" x="590.2781809263649" y="604.4718190736352"/></g>
  <g id="1050">
   <use class="kv10" height="30" transform="rotate(0,683.672,579) scale(1.07143,1.75) translate(-44.3281,-236.893)" width="35" x="664.9219268440086" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="552.75" zvalue="1063"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449596948486" ObjectName="10kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,683.672,579) scale(1.07143,1.75) translate(-44.3281,-236.893)" width="35" x="664.9219268440086" y="552.75"/></g>
  <g id="1083">
   <use class="kv10" height="26" transform="rotate(90,766.806,638.778) scale(-0.838049,0.927421) translate(-1682.77,49.0466)" width="12" x="761.7781809263649" xlink:href="#Accessory:避雷器1_0" y="626.7218190736352" zvalue="1097"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449597538309" ObjectName="#1主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,766.806,638.778) scale(-0.838049,0.927421) translate(-1682.77,49.0466)" width="12" x="761.7781809263649" y="626.7218190736352"/></g>
  <g id="24">
   <use class="kv10" height="26" transform="rotate(0,1039.31,616.528) scale(-0.838049,-0.927421) translate(-2280.43,-1282.25)" width="12" x="1034.278180926365" xlink:href="#Accessory:避雷器1_0" y="604.4718190736352" zvalue="1115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449597865989" ObjectName="备用1避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1039.31,616.528) scale(-0.838049,-0.927421) translate(-2280.43,-1282.25)" width="12" x="1034.278180926365" y="604.4718190736352"/></g>
  <g id="42">
   <use class="kv10" height="26" transform="rotate(0,1755.31,616.528) scale(-0.838049,-0.927421) translate(-3850.79,-1282.25)" width="12" x="1750.278180926365" xlink:href="#Accessory:避雷器1_0" y="604.4718190736352" zvalue="1134"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449598324741" ObjectName="#2电容器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1755.31,616.528) scale(-0.838049,-0.927421) translate(-3850.79,-1282.25)" width="12" x="1750.278180926365" y="604.4718190736352"/></g>
  <g id="46">
   <use class="kv10" height="13" transform="rotate(0,1004.25,561) scale(1,-1) translate(0,-1122)" width="11" x="998.75" xlink:href="#Accessory:空挂线路_0" y="554.5" zvalue="1149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449598521349" ObjectName="备用1"/>
   </metadata>
  <rect fill="white" height="13" opacity="0" stroke="white" transform="rotate(0,1004.25,561) scale(1,-1) translate(0,-1122)" width="11" x="998.75" y="554.5"/></g>
  <g id="55">
   <use class="kv10" height="26" transform="rotate(0,1847.31,616.528) scale(-0.838049,-0.927421) translate(-4052.57,-1282.25)" width="12" x="1842.278180926365" xlink:href="#Accessory:避雷器1_0" y="604.4718190736352" zvalue="1154"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449598652421" ObjectName="备用2避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1847.31,616.528) scale(-0.838049,-0.927421) translate(-4052.57,-1282.25)" width="12" x="1842.278180926365" y="604.4718190736352"/></g>
  <g id="50">
   <use class="kv10" height="13" transform="rotate(0,1812.25,553) scale(1,-1) translate(0,-1106)" width="11" x="1806.75" xlink:href="#Accessory:空挂线路_0" y="546.5" zvalue="1159"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449598586885" ObjectName="备用2"/>
   </metadata>
  <rect fill="white" height="13" opacity="0" stroke="white" transform="rotate(0,1812.25,553) scale(1,-1) translate(0,-1106)" width="11" x="1806.75" y="546.5"/></g>
  <g id="71">
   <use class="kv10" height="30" transform="rotate(0,1594.17,579) scale(1.07143,1.75) translate(-105.028,-236.893)" width="35" x="1575.421926844008" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="552.75" zvalue="1168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449599045638" ObjectName="10kVⅡ母电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1594.17,579) scale(1.07143,1.75) translate(-105.028,-236.893)" width="35" x="1575.421926844008" y="552.75"/></g>
  <g id="86">
   <use class="kv10" height="26" transform="rotate(90,1334.81,638.778) scale(-0.838049,0.927421) translate(-2928.53,49.0466)" width="12" x="1329.778180926365" xlink:href="#Accessory:避雷器1_0" y="626.7218190736352" zvalue="1202"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449599242246" ObjectName="#2主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1334.81,638.778) scale(-0.838049,0.927421) translate(-2928.53,49.0466)" width="12" x="1329.778180926365" y="626.7218190736352"/></g>
  <g id="122">
   <use class="kv10" height="15" transform="rotate(0,1231.25,605.6) scale(1.43333,-1.14668) translate(-368.988,-1132.63)" width="15" x="1220.5" xlink:href="#Accessory:附属接地变_0" y="596.9999542236328" zvalue="1225"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449600094213" ObjectName="#2接地变"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1231.25,605.6) scale(1.43333,-1.14668) translate(-368.988,-1132.63)" width="15" x="1220.5" y="596.9999542236328"/></g>
  <g id="136">
   <use class="kv110" height="30" transform="rotate(0,991.401,488) scale(1.07143,-1.75) translate(-64.8434,-755.607)" width="35" x="972.6510100909994" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="461.75" zvalue="1240"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449600487429" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,991.401,488) scale(1.07143,-1.75) translate(-64.8434,-755.607)" width="35" x="972.6510100909994" y="461.75"/></g>
  <g id="142">
   <use class="kv10" height="35" transform="rotate(0,472,294) scale(1,1) translate(0,0)" width="23" x="460.5" xlink:href="#Accessory:接地变20201012_0" y="276.5" zvalue="1244"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449600552965" ObjectName="施工电源变（兼#2站用变）"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,472,294) scale(1,1) translate(0,0)" width="23" x="460.5" y="276.5"/></g>
  <g id="146">
   <use class="kv10" height="26" transform="rotate(0,513,175) scale(1,-1) translate(0,-350)" width="12" x="507" xlink:href="#Accessory:避雷器1_0" y="162" zvalue="1247"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449600618501" ObjectName="施工电源变（兼#2站用变）避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,513,175) scale(1,-1) translate(0,-350)" width="12" x="507" y="162"/></g>
  <g id="152">
   <use class="kv10" height="13" transform="rotate(0,467.037,167.844) scale(1.26205,-1.20675) translate(-95.5348,-305.587)" width="11" x="460.0957484315628" xlink:href="#Accessory:空挂线路_0" y="159.9999899383938" zvalue="1253"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449600749573" ObjectName="T接10kV大和线"/>
   </metadata>
  <rect fill="white" height="13" opacity="0" stroke="white" transform="rotate(0,467.037,167.844) scale(1.26205,-1.20675) translate(-95.5348,-305.587)" width="11" x="460.0957484315628" y="159.9999899383938"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="EnergyConsumerClass">
  <g id="664">
   <use class="kv10" height="39" transform="rotate(0,421.848,924.441) scale(1.03028,1.03028) translate(-12.0497,-26.5785)" width="23" x="410" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.350685072242" zvalue="709"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449591181318" ObjectName="出线1"/>
   <cge:TPSR_Ref TObjectID="6192449591181318"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,421.848,924.441) scale(1.03028,1.03028) translate(-12.0497,-26.5785)" width="23" x="410" y="904.350685072242"/></g>
  <g id="1010">
   <use class="kv10" height="22" transform="rotate(0,456.427,563.578) scale(2.55729,-2.69325) translate(-262.374,-754.207)" width="20" x="430.8541666666667" xlink:href="#EnergyConsumer:接地变2020_0" y="533.9521452505829" zvalue="1021"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449596358661" ObjectName="#1接地变兼站用变"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,456.427,563.578) scale(2.55729,-2.69325) translate(-262.374,-754.207)" width="20" x="430.8541666666667" y="533.9521452505829"/></g>
  <g id="251">
   <use class="kv10" height="39" transform="rotate(0,494.848,924.441) scale(1.03028,1.03028) translate(-14.1952,-26.5785)" width="23" x="483" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.3506851560809" zvalue="1344"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449600815109" ObjectName="出线2"/>
   <cge:TPSR_Ref TObjectID="6192449600815109"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,494.848,924.441) scale(1.03028,1.03028) translate(-14.1952,-26.5785)" width="23" x="483" y="904.3506851560809"/></g>
  <g id="253">
   <use class="kv10" height="39" transform="rotate(0,567.848,924.441) scale(1.03028,1.03028) translate(-16.3406,-26.5785)" width="23" x="556" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.3506851560809" zvalue="1346"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449600880645" ObjectName="出线3"/>
   <cge:TPSR_Ref TObjectID="6192449600880645"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,567.848,924.441) scale(1.03028,1.03028) translate(-16.3406,-26.5785)" width="23" x="556" y="904.3506851560809"/></g>
  <g id="255">
   <use class="kv10" height="39" transform="rotate(0,635.848,924.441) scale(1.03028,1.03028) translate(-18.3391,-26.5785)" width="23" x="624" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.3506851560809" zvalue="1348"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449600946181" ObjectName="出线4"/>
   <cge:TPSR_Ref TObjectID="6192449600946181"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,635.848,924.441) scale(1.03028,1.03028) translate(-18.3391,-26.5785)" width="23" x="624" y="904.3506851560809"/></g>
  <g id="257">
   <use class="kv10" height="39" transform="rotate(0,702.848,924.441) scale(1.03028,1.03028) translate(-20.3082,-26.5785)" width="23" x="691" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.3506851560809" zvalue="1350"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449601011717" ObjectName="出线5"/>
   <cge:TPSR_Ref TObjectID="6192449601011717"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,702.848,924.441) scale(1.03028,1.03028) translate(-20.3082,-26.5785)" width="23" x="691" y="904.3506851560809"/></g>
  <g id="259">
   <use class="kv10" height="39" transform="rotate(0,766.848,924.441) scale(1.03028,1.03028) translate(-22.1891,-26.5785)" width="23" x="755" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.3506851560809" zvalue="1352"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449601077253" ObjectName="出线6"/>
   <cge:TPSR_Ref TObjectID="6192449601077253"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,766.848,924.441) scale(1.03028,1.03028) translate(-22.1891,-26.5785)" width="23" x="755" y="904.3506851560809"/></g>
  <g id="261">
   <use class="kv10" height="39" transform="rotate(0,834.848,924.441) scale(1.03028,1.03028) translate(-24.1876,-26.5785)" width="23" x="823" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.3506850368716" zvalue="1354"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449601142790" ObjectName="出线7"/>
   <cge:TPSR_Ref TObjectID="6192449601142790"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,834.848,924.441) scale(1.03028,1.03028) translate(-24.1876,-26.5785)" width="23" x="823" y="904.3506850368716"/></g>
  <g id="263">
   <use class="kv10" height="39" transform="rotate(0,903.848,924.441) scale(1.03028,1.03028) translate(-26.2155,-26.5785)" width="23" x="892" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.3506851560809" zvalue="1356"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449601208326" ObjectName="出线8"/>
   <cge:TPSR_Ref TObjectID="6192449601208326"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,903.848,924.441) scale(1.03028,1.03028) translate(-26.2155,-26.5785)" width="23" x="892" y="904.3506851560809"/></g>
  <g id="265">
   <use class="kv10" height="39" transform="rotate(0,970.848,924.441) scale(1.03028,1.03028) translate(-28.1846,-26.5785)" width="23" x="959" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.350685072242" zvalue="1358"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449601273862" ObjectName="出线9"/>
   <cge:TPSR_Ref TObjectID="6192449601273862"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,970.848,924.441) scale(1.03028,1.03028) translate(-28.1846,-26.5785)" width="23" x="959" y="904.350685072242"/></g>
  <g id="268">
   <use class="kv10" height="39" transform="rotate(0,1038.85,924.441) scale(1.03028,1.03028) translate(-30.1831,-26.5785)" width="23" x="1027" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.3506850368716" zvalue="1360"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449601339398" ObjectName="出线10"/>
   <cge:TPSR_Ref TObjectID="6192449601339398"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,1038.85,924.441) scale(1.03028,1.03028) translate(-30.1831,-26.5785)" width="23" x="1027" y="904.3506850368716"/></g>
  <g id="271">
   <use class="kv10" height="39" transform="rotate(0,1178.85,924.441) scale(1.03028,1.03028) translate(-34.2977,-26.5785)" width="23" x="1167" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.3506850828986" zvalue="1364"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449601404934" ObjectName="出线12"/>
   <cge:TPSR_Ref TObjectID="6192449601404934"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,1178.85,924.441) scale(1.03028,1.03028) translate(-34.2977,-26.5785)" width="23" x="1167" y="904.3506850828986"/></g>
  <g id="274">
   <use class="kv10" height="39" transform="rotate(0,1250.7,924.441) scale(1.03028,1.03028) translate(-36.4093,-26.5785)" width="23" x="1238.851190476191" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.3506850232941" zvalue="1368"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449601470470" ObjectName="出线13"/>
   <cge:TPSR_Ref TObjectID="6192449601470470"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,1250.7,924.441) scale(1.03028,1.03028) translate(-36.4093,-26.5785)" width="23" x="1238.851190476191" y="904.3506850232941"/></g>
  <g id="277">
   <use class="kv10" height="39" transform="rotate(0,1322.7,924.441) scale(1.03028,1.03028) translate(-38.5254,-26.5785)" width="23" x="1310.851190476191" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.3506850232941" zvalue="1373"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449601536005" ObjectName="出线14"/>
   <cge:TPSR_Ref TObjectID="6192449601536005"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,1322.7,924.441) scale(1.03028,1.03028) translate(-38.5254,-26.5785)" width="23" x="1310.851190476191" y="904.3506850232941"/></g>
  <g id="279">
   <use class="kv10" height="39" transform="rotate(0,1390.7,924.441) scale(1.03028,1.03028) translate(-40.5239,-26.5785)" width="23" x="1378.851190476191" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.3506850828987" zvalue="1376"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449601601541" ObjectName="出线15"/>
   <cge:TPSR_Ref TObjectID="6192449601601541"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,1390.7,924.441) scale(1.03028,1.03028) translate(-40.5239,-26.5785)" width="23" x="1378.851190476191" y="904.3506850828987"/></g>
  <g id="282">
   <use class="kv10" height="39" transform="rotate(0,1459.7,924.441) scale(1.03028,1.03028) translate(-42.5518,-26.5785)" width="23" x="1447.851190476191" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.3506851425034" zvalue="1381"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449601667078" ObjectName="出线16"/>
   <cge:TPSR_Ref TObjectID="6192449601667078"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,1459.7,924.441) scale(1.03028,1.03028) translate(-42.5518,-26.5785)" width="23" x="1447.851190476191" y="904.3506851425034"/></g>
  <g id="284">
   <use class="kv10" height="39" transform="rotate(0,1522.7,924.441) scale(1.03028,1.03028) translate(-44.4033,-26.5785)" width="23" x="1510.85119047619" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.3506851425034" zvalue="1384"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449601732614" ObjectName="出线17"/>
   <cge:TPSR_Ref TObjectID="6192449601732614"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,1522.7,924.441) scale(1.03028,1.03028) translate(-44.4033,-26.5785)" width="23" x="1510.85119047619" y="904.3506851425034"/></g>
  <g id="286">
   <use class="kv10" height="39" transform="rotate(0,1590.7,924.441) scale(1.03028,1.03028) translate(-46.4018,-26.5785)" width="23" x="1578.85119047619" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.3506851425034" zvalue="1387"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449601798150" ObjectName="出线18"/>
   <cge:TPSR_Ref TObjectID="6192449601798150"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,1590.7,924.441) scale(1.03028,1.03028) translate(-46.4018,-26.5785)" width="23" x="1578.85119047619" y="904.3506851425034"/></g>
  <g id="288">
   <use class="kv10" height="39" transform="rotate(0,1660.7,924.441) scale(1.03028,1.03028) translate(-48.4591,-26.5785)" width="23" x="1648.85119047619" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.3506851425034" zvalue="1390"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449601863686" ObjectName="出线19"/>
   <cge:TPSR_Ref TObjectID="6192449601863686"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,1660.7,924.441) scale(1.03028,1.03028) translate(-48.4591,-26.5785)" width="23" x="1648.85119047619" y="904.3506851425034"/></g>
  <g id="290">
   <use class="kv10" height="39" transform="rotate(0,1727.7,924.441) scale(1.03028,1.03028) translate(-50.4282,-26.5785)" width="23" x="1715.85119047619" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.3506851425034" zvalue="1393"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449601929222" ObjectName="出线20"/>
   <cge:TPSR_Ref TObjectID="6192449601929222"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,1727.7,924.441) scale(1.03028,1.03028) translate(-50.4282,-26.5785)" width="23" x="1715.85119047619" y="904.3506851425034"/></g>
  <g id="292">
   <use class="kv10" height="39" transform="rotate(0,1795.7,924.441) scale(1.03028,1.03028) translate(-52.4267,-26.5785)" width="23" x="1783.85119047619" xlink:href="#EnergyConsumer:站用变D-Y_0" y="904.3506851425034" zvalue="1396"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449601994758" ObjectName="出线21"/>
   <cge:TPSR_Ref TObjectID="6192449601994758"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,1795.7,924.441) scale(1.03028,1.03028) translate(-52.4267,-26.5785)" width="23" x="1783.85119047619" y="904.3506851425034"/></g>
 </g>
 <g id="GroundClass">
  <g id="1018">
   <use class="kv10" height="18" transform="rotate(0,427.917,486.25) scale(1.66667,-1.66667) translate(-166.5,-772)" width="14" x="416.2500000000001" xlink:href="#Ground:小电阻接地_0" y="471.25" zvalue="1030"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449596620806" ObjectName="#1接地变兼站用变接地"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,427.917,486.25) scale(1.66667,-1.66667) translate(-166.5,-772)" width="14" x="416.2500000000001" y="471.25"/></g>
  <g id="109">
   <use class="kv10" height="18" transform="rotate(0,1232.22,505.25) scale(1.66667,-1.66667) translate(-488.223,-802.4)" width="14" x="1220.558112735427" xlink:href="#Ground:小电阻接地_0" y="490.25" zvalue="1222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449599832069" ObjectName="#2接地变接地"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1232.22,505.25) scale(1.66667,-1.66667) translate(-488.223,-802.4)" width="14" x="1220.558112735427" y="490.25"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="1074">
   <g id="10740">
    <use class="kv110" height="30" transform="rotate(0,819.151,580.5) scale(2,2) translate(-398.576,-275.25)" width="22" x="797.15" xlink:href="#PowerTransformer2:接地可调两卷变_0" y="550.5" zvalue="1087"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874423140356" ObjectName="110"/>
    </metadata>
   </g>
   <g id="10741">
    <use class="kv10" height="30" transform="rotate(0,819.151,580.5) scale(2,2) translate(-398.576,-275.25)" width="22" x="797.15" xlink:href="#PowerTransformer2:接地可调两卷变_1" y="550.5" zvalue="1087"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874423205892" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399443939332" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399443939332"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,819.151,580.5) scale(2,2) translate(-398.576,-275.25)" width="22" x="797.15" y="550.5"/></g>
  <g id="93">
   <g id="930">
    <use class="kv110" height="30" transform="rotate(0,1387.15,580.5) scale(2,2) translate(-682.576,-275.25)" width="22" x="1365.15" xlink:href="#PowerTransformer2:接地可调两卷变_0" y="550.5" zvalue="1192"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874423271428" ObjectName="110"/>
    </metadata>
   </g>
   <g id="931">
    <use class="kv10" height="30" transform="rotate(0,1387.15,580.5) scale(2,2) translate(-682.576,-275.25)" width="22" x="1365.15" xlink:href="#PowerTransformer2:接地可调两卷变_1" y="550.5" zvalue="1192"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874423336964" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399444004868" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399444004868"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1387.15,580.5) scale(2,2) translate(-682.576,-275.25)" width="22" x="1365.15" y="550.5"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="452">
   <use class="kv10" height="50" transform="rotate(0,555.069,467.235) scale(1.93326,-1.28713) translate(-256.288,-823.062)" width="25" x="530.9036750749517" xlink:href="#Compensator:电容20200722_0" y="435.056798228244" zvalue="1100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449597734917" ObjectName="#1电容器"/>
   <cge:TPSR_Ref TObjectID="6192449597734917"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,555.069,467.235) scale(1.93326,-1.28713) translate(-256.288,-823.062)" width="25" x="530.9036750749517" y="435.056798228244"/></g>
  <g id="35">
   <use class="kv10" height="50" transform="rotate(0,1715.07,467.235) scale(1.93326,-1.28713) translate(-816.265,-823.062)" width="25" x="1690.903675074951" xlink:href="#Compensator:电容20200722_0" y="435.056798228244" zvalue="1142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449598193669" ObjectName="#2电容器"/>
   <cge:TPSR_Ref TObjectID="6192449598193669"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1715.07,467.235) scale(1.93326,-1.28713) translate(-816.265,-823.062)" width="25" x="1690.903675074951" y="435.056798228244"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1060.22,707.295) scale(1,1) translate(0,-1.5423e-13)" writing-mode="lr" x="1059.75" xml:space="preserve" y="712" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124307206148" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,686.5,383.42) scale(1,1) translate(0,-8.23432e-14)" writing-mode="lr" x="686.03" xml:space="preserve" y="388.12" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124307730436" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="7" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1184.22,711.295) scale(1,1) translate(-2.52958e-13,-1.54896e-13)" writing-mode="lr" x="1183.75" xml:space="preserve" y="716.01" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124319133700" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="12" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1046.04,30) scale(1,1) translate(0,0)" writing-mode="lr" x="1045.57" xml:space="preserve" y="34.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124305960964" ObjectName="P"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="14" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1046.04,47.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1045.57" xml:space="preserve" y="52.19" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124306026500" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="15" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1046.04,64) scale(1,1) translate(0,0)" writing-mode="lr" x="1045.57" xml:space="preserve" y="68.69" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124306092036" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="16" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,417.848,980.266) scale(1,1) translate(0,0)" writing-mode="lr" x="417.38" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124308058116" ObjectName="P"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="17" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,493.848,980.266) scale(1,1) translate(0,0)" writing-mode="lr" x="493.38" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124330799108" ObjectName="P"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="20" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,565.848,980.266) scale(1,1) translate(0,0)" writing-mode="lr" x="565.38" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124331716612" ObjectName="P"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="22" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,634.848,980.266) scale(1,1) translate(0,0)" writing-mode="lr" x="634.38" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124332634119" ObjectName="P"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="23" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,700.848,980.266) scale(1,1) translate(0,0)" writing-mode="lr" x="700.38" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124333551620" ObjectName="P"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="38" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,764.848,980.266) scale(1,1) translate(0,0)" writing-mode="lr" x="764.38" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124334469124" ObjectName="P"/>
   </metadata>
  </g>
  <g id="40">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="40" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,832.848,980.266) scale(1,1) translate(-1.74937e-13,0)" writing-mode="lr" x="832.38" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124335386628" ObjectName="P"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="62" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,901.848,980.266) scale(1,1) translate(0,0)" writing-mode="lr" x="901.38" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124336304132" ObjectName="P"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="63" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,968.848,980.266) scale(1,1) translate(0,0)" writing-mode="lr" x="968.38" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124337221636" ObjectName="P"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="64" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1036.85,980.266) scale(1,1) translate(0,0)" writing-mode="lr" x="1036.38" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124338139140" ObjectName="P"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="65" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1168.85,980.266) scale(1,1) translate(3.74317e-13,0)" writing-mode="lr" x="1168.38" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124339056644" ObjectName="P"/>
   </metadata>
  </g>
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="68" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1244.7,980.266) scale(1,1) translate(0,0)" writing-mode="lr" x="1244.23" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124339974151" ObjectName="P"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="87" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1321.7,980.266) scale(1,1) translate(0,0)" writing-mode="lr" x="1321.23" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124340891652" ObjectName="P"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="107" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1388.7,980.266) scale(1,1) translate(0,0)" writing-mode="lr" x="1388.23" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124341809156" ObjectName="P"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="111" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1459.7,980.266) scale(1,1) translate(0,0)" writing-mode="lr" x="1459.23" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124342726660" ObjectName="P"/>
   </metadata>
  </g>
  <g id="113">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="113" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1530.7,980.266) scale(1,1) translate(0,0)" writing-mode="lr" x="1530.23" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124343644164" ObjectName="P"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="115" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1602.7,980.266) scale(1,1) translate(0,0)" writing-mode="lr" x="1602.23" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124344561668" ObjectName="P"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="116" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1672.7,980.266) scale(1,1) translate(0,0)" writing-mode="lr" x="1672.23" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124345479172" ObjectName="P"/>
   </metadata>
  </g>
  <g id="119">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="119" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1743.7,980.266) scale(1,1) translate(0,0)" writing-mode="lr" x="1743.23" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124346396676" ObjectName="P"/>
   </metadata>
  </g>
  <g id="120">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="120" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1811.7,980.266) scale(1,1) translate(0,0)" writing-mode="lr" x="1811.23" xml:space="preserve" y="984.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124347314180" ObjectName="P"/>
   </metadata>
  </g>
  <g id="121">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="121" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,416.848,999.766) scale(1,1) translate(0,0)" writing-mode="lr" x="416.38" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124308123652" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="124">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="124" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,494.848,999.766) scale(1,1) translate(0,0)" writing-mode="lr" x="494.38" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124330864644" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="130">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="130" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,567.848,999.766) scale(1,1) translate(0,0)" writing-mode="lr" x="567.38" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124331782148" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="131">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="131" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,635.848,999.766) scale(1,1) translate(0,0)" writing-mode="lr" x="635.38" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124332699655" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="144" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,702.848,999.766) scale(1,1) translate(0,0)" writing-mode="lr" x="702.38" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124333617156" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="145" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,766.848,999.766) scale(1,1) translate(0,0)" writing-mode="lr" x="766.38" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124334534660" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="154">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="154" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,834.848,999.766) scale(1,1) translate(-1.75382e-13,0)" writing-mode="lr" x="834.38" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124335452164" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="155">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="155" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,903.848,999.766) scale(1,1) translate(0,0)" writing-mode="lr" x="903.38" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124336369668" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="156">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="156" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,970.848,999.766) scale(1,1) translate(0,0)" writing-mode="lr" x="970.38" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124337287172" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="157">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="157" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1038.85,999.766) scale(1,1) translate(0,0)" writing-mode="lr" x="1038.38" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124338204676" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="158">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="158" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1170.85,999.766) scale(1,1) translate(3.74983e-13,0)" writing-mode="lr" x="1170.38" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124339122180" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="159">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="159" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1246.7,999.766) scale(1,1) translate(0,0)" writing-mode="lr" x="1246.23" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124340039684" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="160">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="160" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1322.7,999.766) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.23" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124340957188" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="161">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="161" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1390.7,999.766) scale(1,1) translate(0,0)" writing-mode="lr" x="1390.23" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124341874692" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="163">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="163" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1459.7,999.766) scale(1,1) translate(0,0)" writing-mode="lr" x="1459.23" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124342792196" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="165">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="165" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1530.7,999.766) scale(1,1) translate(0,0)" writing-mode="lr" x="1530.23" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124343709700" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="166">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="166" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1602.7,999.766) scale(1,1) translate(0,0)" writing-mode="lr" x="1602.23" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124344627204" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="168">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="168" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1672.7,999.766) scale(1,1) translate(0,0)" writing-mode="lr" x="1672.23" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124345544708" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="169">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="169" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1743.7,999.766) scale(1,1) translate(0,0)" writing-mode="lr" x="1743.23" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124346462212" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="184">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="184" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1811.7,999.766) scale(1,1) translate(0,0)" writing-mode="lr" x="1811.23" xml:space="preserve" y="1004.43" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124347379716" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="196">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="196" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,417.848,1014.27) scale(1,1) translate(0,0)" writing-mode="lr" x="417.38" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124308189188" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="197">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="197" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,494.848,1014.27) scale(1,1) translate(0,0)" writing-mode="lr" x="494.38" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124330930180" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="198">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="198" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,567.848,1014.27) scale(1,1) translate(0,0)" writing-mode="lr" x="567.38" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124331847684" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="199">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="199" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,635.848,1014.27) scale(1,1) translate(0,0)" writing-mode="lr" x="635.38" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124332765188" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="218">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="218" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,702.848,1014.27) scale(1,1) translate(0,0)" writing-mode="lr" x="702.38" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124333682692" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="219">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="219" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,766.848,1014.27) scale(1,1) translate(0,0)" writing-mode="lr" x="766.38" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124334600196" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="220">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="220" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,834.848,1014.27) scale(1,1) translate(-1.75382e-13,0)" writing-mode="lr" x="834.38" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124335517700" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="221">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="221" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,903.848,1014.27) scale(1,1) translate(0,0)" writing-mode="lr" x="903.38" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124336435204" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="222">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="222" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,970.848,1014.27) scale(1,1) translate(0,0)" writing-mode="lr" x="970.38" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124337352708" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="223">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="223" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1038.85,1014.27) scale(1,1) translate(0,0)" writing-mode="lr" x="1038.38" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124338270212" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="224">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="224" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1170.85,1014.27) scale(1,1) translate(3.74983e-13,0)" writing-mode="lr" x="1170.38" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124339187716" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="225">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="225" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1246.7,1014.27) scale(1,1) translate(0,0)" writing-mode="lr" x="1246.23" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124340105220" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="226">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="226" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1322.7,1014.27) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.23" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124341022724" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="227">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="227" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1390.7,1014.27) scale(1,1) translate(0,0)" writing-mode="lr" x="1390.23" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124341940228" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="228">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="228" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1459.7,1014.27) scale(1,1) translate(0,0)" writing-mode="lr" x="1459.23" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124342857732" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="229">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="229" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1530.7,1014.27) scale(1,1) translate(0,0)" writing-mode="lr" x="1530.23" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124343775236" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="230">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="230" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1602.7,1014.27) scale(1,1) translate(0,0)" writing-mode="lr" x="1602.23" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124344692740" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="231">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="231" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1676.7,1014.27) scale(1,1) translate(0,0)" writing-mode="lr" x="1676.23" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124345610244" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="232">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="232" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1743.7,1014.27) scale(1,1) translate(0,0)" writing-mode="lr" x="1743.23" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124346527748" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="233">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="233" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1811.7,1014.27) scale(1,1) translate(0,0)" writing-mode="lr" x="1811.23" xml:space="preserve" y="1018.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124347445252" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="234" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,873.151,431.25) scale(1,1) translate(0,0)" writing-mode="lr" x="872.6" xml:space="preserve" y="435.95" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124321624068" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="235">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="235" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1452.15,434.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1451.6" xml:space="preserve" y="439" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124327718916" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="236">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="236" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,873.151,450) scale(1,1) translate(0,0)" writing-mode="lr" x="872.6" xml:space="preserve" y="454.7" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124321689604" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="237">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="237" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1452.15,458) scale(1,1) translate(7.76685e-13,0)" writing-mode="lr" x="1451.6" xml:space="preserve" y="462.7" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124327784452" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="238">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="238" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,882.151,648.75) scale(1,1) translate(0,0)" writing-mode="lr" x="881.6" xml:space="preserve" y="653.45" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124321755140" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="239">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="239" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1455.15,648.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1454.6" xml:space="preserve" y="653.46" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124327849988" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="240" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,882.151,670.5) scale(1,1) translate(0,0)" writing-mode="lr" x="881.6" xml:space="preserve" y="675.2" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124321820676" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="241" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1455.15,669.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1454.6" xml:space="preserve" y="674.4400000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124327915524" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="242">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="242" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,873.151,468.75) scale(1,1) translate(0,0)" writing-mode="lr" x="872.6" xml:space="preserve" y="473.45" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124321886212" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="243">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="243" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1452.15,477.75) scale(1,1) translate(7.76685e-13,0)" writing-mode="lr" x="1451.6" xml:space="preserve" y="482.46" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124327981060" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="244" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,882.151,692.25) scale(1,1) translate(0,0)" writing-mode="lr" x="881.6" xml:space="preserve" y="696.95" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124322213892" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="245" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1455.15,691.75) scale(1,1) translate(7.7835e-13,0)" writing-mode="lr" x="1454.6" xml:space="preserve" y="696.4400000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124328308740" ObjectName="LIa"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="269">
   <use height="30" transform="rotate(0,333.768,342.5) scale(0.708333,0.665547) translate(133.059,167.098)" width="30" x="323.14" xlink:href="#State:红绿圆(方形)_0" y="332.52" zvalue="1490"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374885257220" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,333.768,342.5) scale(0.708333,0.665547) translate(133.059,167.098)" width="30" x="323.14" y="332.52"/></g>
  <g id="250">
   <use height="30" transform="rotate(0,251.143,342.5) scale(0.708333,0.665547) translate(99.0368,167.098)" width="30" x="240.52" xlink:href="#State:红绿圆(方形)_0" y="332.52" zvalue="1491"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,251.143,342.5) scale(0.708333,0.665547) translate(99.0368,167.098)" width="30" x="240.52" y="332.52"/></g>
 </g>
</svg>