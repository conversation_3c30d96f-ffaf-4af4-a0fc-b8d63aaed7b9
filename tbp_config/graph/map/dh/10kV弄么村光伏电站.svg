<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549594423298" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:杆或塔_0" viewBox="0,0,15,15">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="7.5"/>
   <ellipse cx="7.46" cy="7.68" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0.25" y2="25.75"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_0" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="3.333333333333332" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="24.08333333333333" y2="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="7" y1="19.5" y2="24.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_1" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="18.25" y2="32.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_2" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="4" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="10" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="25.83333333333333" y2="32.41666666666667"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2027.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id=":光伏模组_0" viewBox="0,0,80,80">
   <use terminal-index="0" type="0" x="23.86666666666666" xlink:href="#terminal" y="70.93333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="50.8" x2="57.2" y1="10.13333333333333" y2="10.13333333333333"/>
   <path d="M 36.7489 56.5767 L 38.0132 59.1405 L 39.2774 56.5767" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.6" x2="50.66666666666667" y1="21.33333333333333" y2="11.33333333333333"/>
   <path d="M 52.4 16.5333 L 49.2 16.5333 L 49.2 27.7333 L 52.4 27.7333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(170,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.2" x2="55.2" y1="32.53333333333333" y2="43.73333333333333"/>
   <path d="M 38 45.3333 L 38 35.7333 L 66.8 35.7333 L 66.8 30.9333" fill="none" stroke="rgb(170,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="73.59999999999999" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,40.13) scale(1,1) translate(0,0)" width="68.8" x="5.6" y="3.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="66.8" x2="66.8" y1="60.13333333333333" y2="64.93333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="5" x1="14" x2="33.2" y1="70.93333333333332" y2="70.93333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="62" x2="66.8" y1="60.13333333333333" y2="60.13333333333333"/>
   <path d="M 53.7333 10.4 L 53.7333 5.6 L 23.3333 5.6 L 23.7333 71.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="6 2" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.2" x2="55.2" y1="68.13333333333334" y2="79.20000000000002"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="23.6" y1="27.73333333333334" y2="27.73333333333334"/>
   <rect fill-opacity="0" height="6.77" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,37.91,50.21) scale(1,1) translate(0,0)" width="3.37" x="36.22" y="46.82"/>
   <path d="M 36.7489 61.0634 L 38.0132 63.6272 L 39.2774 61.0634" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="65.07886701035602" x2="68.00570387390415" y1="28.93345174664175" y2="28.93345174664175"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="37.9078029848685" x2="37.9078029848685" y1="55.40157383031868" y2="45.46666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.20202391636546" x2="14.96424980017045" y1="38.63150148536967" y2="35.9076739581179"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.20202391636535" x2="14.20202391636535" y1="27.59999999999994" y2="38.17753023082763"/>
   <rect fill-opacity="0" height="8.44" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.27,38.31) scale(1,1) translate(0,0)" width="5.34" x="11.6" y="34.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.20202391636544" x2="13.43979803256045" y1="38.63150148536967" y2="35.9076739581179"/>
   <ellipse cx="55.25" cy="50.35" fill-opacity="0" rx="6.61" ry="6.88" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="63.2906587876924" x2="70.00000000000001" y1="17.85532825265332" y2="17.85532825265332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="64.40888232307701" x2="68.88177646461541" y1="16.86538557734767" y2="16.86538557734767"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="65.40515432248043" x2="67.64160139324963" y1="15.89299537279264" y2="15.89299537279264"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.21175520726185" x2="14.21175520726185" y1="42.19334811824088" y2="44.59300008617765"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="66.58435362585561" x2="66.58435362585561" y1="17.91050011524912" y2="21.03483990886042"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="63.06666666666666" x2="66.60326121012066" y1="27.83642232472767" y2="21.07872108573694"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="66.5726562972967" x2="66.5726562972967" y1="28.99881464929833" y2="31.03687217788674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="52.85048888888888" x2="54.58955061728395" y1="51.30912086720867" y2="47.91573333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="58.06767407407408" x2="56.32861234567901" y1="51.30912086720867" y2="47.91573333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="52.85048888888888" x2="58.06767407407408" y1="51.30912086720867" y2="51.30912086720867"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.29388998943305" x2="12.96402824408577" y1="44.28768815766198" y2="44.28768815766198"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.83991873489108" x2="13.41799949862773" y1="44.96864503947492" y2="44.96864503947492"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.52213885671171" x2="13.73577937680711" y1="45.64960192128786" y2="45.64960192128786"/>
   <ellipse cx="55.29" cy="61.19" fill-opacity="0" rx="6.67" ry="6.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.25184255358918" x2="51.41184255358919" y1="60.85264603444045" y2="58.87258205947708"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.25184255358919" x2="55.25184255358919" y1="64.81277398436721" y2="60.85264603444048"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.2518425535892" x2="59.09184255358919" y1="60.85264603444045" y2="58.87258205947708"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="70.20141457174999" x2="63.35850922025583" y1="64.84471210629269" y2="64.84471210629269"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="68.86808123841665" x2="64.69184255358917" y1="66.84471210629269" y2="66.84471210629269"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="67.93474790508333" x2="65.62517588692251" y1="68.84471210629269" y2="68.84471210629269"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.06666666666667" x2="55.06666666666667" y1="32.26666666666668" y2="19.86666666666667"/>
   <rect fill-opacity="0" height="8.449999999999999" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,54.93,25.92) scale(1,1) translate(0,0)" width="4.27" x="52.8" y="21.69"/>
   <ellipse cx="37.99" cy="57.88" fill-opacity="0" rx="2.94" ry="2.98" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="37.99" cy="62.37" fill-opacity="0" rx="2.94" ry="2.98" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="54.24" cy="11.23" fill-opacity="0" rx="1.1" ry="1.1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV弄么村光伏电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="46.71" xlink:href="logo.png" y="41.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,195.714,73.4286) scale(1,1) translate(0,0)" writing-mode="lr" x="195.71" xml:space="preserve" y="76.93000000000001" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,198.214,71.1189) scale(1,1) translate(0,0)" writing-mode="lr" x="198.21" xml:space="preserve" y="80.12" zvalue="5">10kV弄么村光伏电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="73" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,87.2143,312.429) scale(1,1) translate(0,0)" width="97" x="38.71" y="300.43" zvalue="10"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,87.2143,312.429) scale(1,1) translate(0,0)" writing-mode="lr" x="87.20999999999999" xml:space="preserve" y="316.93" zvalue="10">全站公用</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="439.0952149344309" y2="439.0952149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="476.5852149344309" y2="476.5852149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="58.93664745163255" y1="439.0952149344309" y2="476.5852149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2813474516327" x2="107.2813474516327" y1="439.0952149344309" y2="476.5852149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="439.0952149344309" y2="439.0952149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="476.5852149344309" y2="476.5852149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="107.2816474516326" y1="439.0952149344309" y2="476.5852149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3899474516327" x2="169.3899474516327" y1="439.0952149344309" y2="476.5852149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="439.0952149344309" y2="439.0952149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="476.5852149344309" y2="476.5852149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="169.3894474516326" y1="439.0952149344309" y2="476.5852149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7142474516327" x2="232.7142474516327" y1="439.0952149344309" y2="476.5852149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="439.0952149344309" y2="439.0952149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="476.5852149344309" y2="476.5852149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="232.7141474516326" y1="439.0952149344309" y2="476.5852149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="439.0952149344309" y2="476.5852149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="439.0952149344309" y2="439.0952149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="476.5852149344309" y2="476.5852149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="439.0952149344309" y2="476.5852149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="356.9307474516327" x2="356.9307474516327" y1="439.0952149344309" y2="476.5852149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="476.5853149344309" y2="476.5853149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="500.7539149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="58.93664745163255" y1="476.5853149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2813474516327" x2="107.2813474516327" y1="476.5853149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="476.5853149344309" y2="476.5853149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="500.7539149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="107.2816474516326" y1="476.5853149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3899474516327" x2="169.3899474516327" y1="476.5853149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="476.5853149344309" y2="476.5853149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="500.7539149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="169.3894474516326" y1="476.5853149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7142474516327" x2="232.7142474516327" y1="476.5853149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="476.5853149344309" y2="476.5853149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="500.7539149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="232.7141474516326" y1="476.5853149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="476.5853149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="476.5853149344309" y2="476.5853149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="500.7539149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="476.5853149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="356.9307474516327" x2="356.9307474516327" y1="476.5853149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="500.7539149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="524.9225149344309" y2="524.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="58.93664745163255" y1="500.7539149344309" y2="524.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2813474516327" x2="107.2813474516327" y1="500.7539149344309" y2="524.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="500.7539149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="524.9225149344309" y2="524.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="107.2816474516326" y1="500.7539149344309" y2="524.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3899474516327" x2="169.3899474516327" y1="500.7539149344309" y2="524.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="500.7539149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="524.9225149344309" y2="524.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="169.3894474516326" y1="500.7539149344309" y2="524.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7142474516327" x2="232.7142474516327" y1="500.7539149344309" y2="524.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="500.7539149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="524.9225149344309" y2="524.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="232.7141474516326" y1="500.7539149344309" y2="524.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="500.7539149344309" y2="524.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="500.7539149344309" y2="500.7539149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="524.9225149344309" y2="524.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="500.7539149344309" y2="524.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="356.9307474516327" x2="356.9307474516327" y1="500.7539149344309" y2="524.9225149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="524.9225549344309" y2="524.9225549344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="549.0911549344308" y2="549.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="58.93664745163255" y1="524.9225549344309" y2="549.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2813474516327" x2="107.2813474516327" y1="524.9225549344309" y2="549.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="524.9225549344309" y2="524.9225549344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="549.0911549344308" y2="549.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="107.2816474516326" y1="524.9225549344309" y2="549.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3899474516327" x2="169.3899474516327" y1="524.9225549344309" y2="549.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="524.9225549344309" y2="524.9225549344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="549.0911549344308" y2="549.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="169.3894474516326" y1="524.9225549344309" y2="549.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7142474516327" x2="232.7142474516327" y1="524.9225549344309" y2="549.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="524.9225549344309" y2="524.9225549344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="549.0911549344308" y2="549.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="232.7141474516326" y1="524.9225549344309" y2="549.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="524.9225549344309" y2="549.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="524.9225549344309" y2="524.9225549344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="549.0911549344308" y2="549.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="524.9225549344309" y2="549.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="356.9307474516327" x2="356.9307474516327" y1="524.9225549344309" y2="549.0911549344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="549.0913149344309" y2="549.0913149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="573.2599149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="58.93664745163255" y1="549.0913149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2813474516327" x2="107.2813474516327" y1="549.0913149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="549.0913149344309" y2="549.0913149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="573.2599149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="107.2816474516326" y1="549.0913149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3899474516327" x2="169.3899474516327" y1="549.0913149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="549.0913149344309" y2="549.0913149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="573.2599149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="169.3894474516326" y1="549.0913149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7142474516327" x2="232.7142474516327" y1="549.0913149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="549.0913149344309" y2="549.0913149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="573.2599149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="232.7141474516326" y1="549.0913149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="549.0913149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="549.0913149344309" y2="549.0913149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="573.2599149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="549.0913149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="356.9307474516327" x2="356.9307474516327" y1="549.0913149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="573.2599149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="597.4285149344308" y2="597.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="58.93664745163255" y1="573.2599149344309" y2="597.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2813474516327" x2="107.2813474516327" y1="573.2599149344309" y2="597.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="573.2599149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="597.4285149344308" y2="597.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="107.2816474516326" y1="573.2599149344309" y2="597.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3899474516327" x2="169.3899474516327" y1="573.2599149344309" y2="597.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="573.2599149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="597.4285149344308" y2="597.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="169.3894474516326" y1="573.2599149344309" y2="597.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7142474516327" x2="232.7142474516327" y1="573.2599149344309" y2="597.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="573.2599149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="597.4285149344308" y2="597.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="232.7141474516326" y1="573.2599149344309" y2="597.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="573.2599149344309" y2="597.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="573.2599149344309" y2="573.2599149344309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="597.4285149344308" y2="597.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="573.2599149344309" y2="597.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="356.9307474516327" x2="356.9307474516327" y1="573.2599149344309" y2="597.4285149344308"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="157.4285714285714" y2="157.4285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="183.4285714285714" y2="183.4285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="7.714285714285779" y1="157.4285714285714" y2="183.4285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="157.4285714285714" y2="183.4285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="157.4285714285714" y2="157.4285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="183.4285714285714" y2="183.4285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="157.4285714285714" y2="183.4285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.7142857142858" x2="369.7142857142858" y1="157.4285714285714" y2="183.4285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="183.4285714285714" y2="183.4285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="207.6785714285714" y2="207.6785714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="7.714285714285779" y1="183.4285714285714" y2="207.6785714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="183.4285714285714" y2="207.6785714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="183.4285714285714" y2="183.4285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="207.6785714285714" y2="207.6785714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="183.4285714285714" y2="207.6785714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.7142857142858" x2="369.7142857142858" y1="183.4285714285714" y2="207.6785714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="207.6785714285714" y2="207.6785714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="230.4285714285714" y2="230.4285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="7.714285714285779" y1="207.6785714285714" y2="230.4285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="207.6785714285714" y2="230.4285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="207.6785714285714" y2="207.6785714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="230.4285714285714" y2="230.4285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="207.6785714285714" y2="230.4285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.7142857142858" x2="369.7142857142858" y1="207.6785714285714" y2="230.4285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="230.4285714285714" y2="230.4285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="253.1785714285714" y2="253.1785714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="7.714285714285779" y1="230.4285714285714" y2="253.1785714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="230.4285714285714" y2="253.1785714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="230.4285714285714" y2="230.4285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="253.1785714285714" y2="253.1785714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="230.4285714285714" y2="253.1785714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.7142857142858" x2="369.7142857142858" y1="230.4285714285714" y2="253.1785714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="253.1785714285714" y2="253.1785714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="275.9285714285714" y2="275.9285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="7.714285714285779" y1="253.1785714285714" y2="275.9285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="253.1785714285714" y2="275.9285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="253.1785714285714" y2="253.1785714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="275.9285714285714" y2="275.9285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="253.1785714285714" y2="275.9285714285714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.7142857142858" x2="369.7142857142858" y1="253.1785714285714" y2="275.9285714285714"/>
  <line fill="none" id="78" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="380.7142857142858" x2="380.7142857142858" y1="9.428571428571558" y2="1039.428571428572" zvalue="6"/>
  <line fill="none" id="76" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.714285714286461" x2="373.714285714286" y1="145.2990640426538" y2="145.2990640426538" zvalue="8"/>
  <line fill="none" id="74" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.714285714286461" x2="373.714285714286" y1="615.2990640426538" y2="615.2990640426538" zvalue="9"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.714285714285779" x2="96.71428571428578" y1="930.4285714285713" y2="930.4285714285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.714285714285779" x2="96.71428571428578" y1="969.5918714285714" y2="969.5918714285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.714285714285779" x2="6.714285714285779" y1="930.4285714285713" y2="969.5918714285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="96.71428571428578" y1="930.4285714285713" y2="969.5918714285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="366.7142857142858" y1="930.4285714285713" y2="930.4285714285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="366.7142857142858" y1="969.5918714285714" y2="969.5918714285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="96.71428571428578" y1="930.4285714285713" y2="969.5918714285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366.7142857142858" x2="366.7142857142858" y1="930.4285714285713" y2="969.5918714285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.714285714285779" x2="96.71428571428578" y1="969.5918414285713" y2="969.5918414285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.714285714285779" x2="96.71428571428578" y1="997.5102414285714" y2="997.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.714285714285779" x2="6.714285714285779" y1="969.5918414285713" y2="997.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="96.71428571428578" y1="969.5918414285713" y2="997.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="186.7142857142858" y1="969.5918414285713" y2="969.5918414285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="186.7142857142858" y1="997.5102414285714" y2="997.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="96.71428571428578" y1="969.5918414285713" y2="997.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.7142857142858" x2="186.7142857142858" y1="969.5918414285713" y2="997.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.7142857142859" x2="276.7142857142859" y1="969.5918414285713" y2="969.5918414285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.7142857142859" x2="276.7142857142859" y1="997.5102414285714" y2="997.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.7142857142859" x2="186.7142857142859" y1="969.5918414285713" y2="997.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.7142857142859" x2="276.7142857142859" y1="969.5918414285713" y2="997.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.7142857142858" x2="366.7142857142858" y1="969.5918414285713" y2="969.5918414285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.7142857142858" x2="366.7142857142858" y1="997.5102414285714" y2="997.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.7142857142858" x2="276.7142857142858" y1="969.5918414285713" y2="997.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366.7142857142858" x2="366.7142857142858" y1="969.5918414285713" y2="997.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.714285714285779" x2="96.71428571428578" y1="997.5101714285713" y2="997.5101714285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.714285714285779" x2="96.71428571428578" y1="1025.428571428571" y2="1025.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.714285714285779" x2="6.714285714285779" y1="997.5101714285713" y2="1025.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="96.71428571428578" y1="997.5101714285713" y2="1025.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="186.7142857142858" y1="997.5101714285713" y2="997.5101714285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="186.7142857142858" y1="1025.428571428571" y2="1025.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="96.71428571428578" y1="997.5101714285713" y2="1025.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.7142857142858" x2="186.7142857142858" y1="997.5101714285713" y2="1025.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.7142857142859" x2="276.7142857142859" y1="997.5101714285713" y2="997.5101714285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.7142857142859" x2="276.7142857142859" y1="1025.428571428571" y2="1025.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.7142857142859" x2="186.7142857142859" y1="997.5101714285713" y2="1025.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.7142857142859" x2="276.7142857142859" y1="997.5101714285713" y2="1025.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.7142857142858" x2="366.7142857142858" y1="997.5101714285713" y2="997.5101714285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.7142857142858" x2="366.7142857142858" y1="1025.428571428571" y2="1025.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.7142857142858" x2="276.7142857142858" y1="997.5101714285713" y2="1025.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366.7142857142858" x2="366.7142857142858" y1="997.5101714285713" y2="1025.428571428571"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.7143,950.429) scale(1,1) translate(0,0)" writing-mode="lr" x="51.71" xml:space="preserve" y="956.4299999999999" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.7143,984.429) scale(1,1) translate(0,0)" writing-mode="lr" x="48.71" xml:space="preserve" y="990.4299999999999" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230.714,984.429) scale(1,1) translate(0,0)" writing-mode="lr" x="230.71" xml:space="preserve" y="990.4299999999999" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.7143,1012.43) scale(1,1) translate(0,0)" writing-mode="lr" x="47.71" xml:space="preserve" y="1018.43" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229.714,1012.43) scale(1,1) translate(0,0)" writing-mode="lr" x="229.71" xml:space="preserve" y="1018.43" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" x="134.71875" xml:space="preserve" y="452.4375" zvalue="17">110kV  母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="134.71875" xml:space="preserve" y="468.4375" zvalue="17">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,72.2143,644.929) scale(1,1) translate(0,-2.77968e-13)" writing-mode="lr" x="72.21428571428578" xml:space="preserve" y="649.4285714285714" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,205.113,312.27) scale(1,1) translate(0,0)" writing-mode="lr" x="205.11" xml:space="preserve" y="316.77" zvalue="20">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,310.113,312.27) scale(1,1) translate(0,0)" writing-mode="lr" x="310.11" xml:space="preserve" y="316.77" zvalue="21">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.7143,487.929) scale(1,1) translate(0,0)" writing-mode="lr" x="83.71428571428578" xml:space="preserve" y="492.4285714285715" zvalue="22">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.7143,513.429) scale(1,1) translate(0,0)" writing-mode="lr" x="83.71428571428578" xml:space="preserve" y="517.9285714285716" zvalue="23">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.7143,538.929) scale(1,1) translate(0,0)" writing-mode="lr" x="83.71428571428578" xml:space="preserve" y="543.4285714285714" zvalue="24">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.7143,563.429) scale(1,1) translate(0,0)" writing-mode="lr" x="82.71428571428578" xml:space="preserve" y="567.9285714285714" zvalue="25">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.7143,589.929) scale(1,1) translate(0,0)" writing-mode="lr" x="83.71428571428578" xml:space="preserve" y="594.4285714285713" zvalue="26">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231.768,952.429) scale(1,1) translate(0,0)" writing-mode="lr" x="231.77" xml:space="preserve" y="958.4299999999999" zvalue="27">NYCGF-01-2018</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.7143,171.429) scale(1,1) translate(0,0)" writing-mode="lr" x="45.71" xml:space="preserve" y="176.93" zvalue="28">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225.714,171.429) scale(1,1) translate(0,0)" writing-mode="lr" x="225.71" xml:space="preserve" y="176.93" zvalue="29">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,517.089,535.929) scale(1,1) translate(0,0)" writing-mode="lr" x="517.09" xml:space="preserve" y="540.4299999999999" zvalue="32">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,822.464,818.84) scale(1,1) translate(0,0)" writing-mode="lr" x="822.46" xml:space="preserve" y="823.34" zvalue="33">10kV#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1060.71,815.804) scale(1,1) translate(0,0)" writing-mode="lr" x="1060.71" xml:space="preserve" y="820.3" zvalue="35">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,630.839,677.179) scale(1,1) translate(0,0)" writing-mode="lr" x="630.84" xml:space="preserve" y="681.6799999999999" zvalue="38">013</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,851.464,488.429) scale(1,1) translate(0,0)" writing-mode="lr" x="851.46" xml:space="preserve" y="492.93" zvalue="41">011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,850.839,645.929) scale(1,1) translate(0,0)" writing-mode="lr" x="850.84" xml:space="preserve" y="650.4299999999999" zvalue="43">0121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1021.84,650.929) scale(1,1) translate(-2.23563e-13,0)" writing-mode="lr" x="1021.84" xml:space="preserve" y="655.4299999999999" zvalue="47">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,792.464,346.679) scale(1,1) translate(0,0)" writing-mode="lr" x="792.46" xml:space="preserve" y="351.18" zvalue="51">A012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,848.964,289.929) scale(1,1) translate(0,0)" writing-mode="lr" x="848.96" xml:space="preserve" y="294.43" zvalue="53">A01</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,669.714,742.491) scale(1,1) translate(0,0)" writing-mode="lr" x="669.71" xml:space="preserve" y="746.99" zvalue="56">01367</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1449.46,257.054) scale(1,1) translate(0,0)" writing-mode="lr" x="1449.46" xml:space="preserve" y="261.55" zvalue="60">#2箱变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1723.21,262.054) scale(1,1) translate(0,0)" writing-mode="lr" x="1723.21" xml:space="preserve" y="266.55" zvalue="62">#1箱变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,799.31,188.038) scale(1,1) translate(0,0)" writing-mode="lr" x="799.3099999999999" xml:space="preserve" y="192.54" zvalue="65">#1杆</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,791.839,144.929) scale(1,1) translate(0,0)" writing-mode="lr" x="791.84" xml:space="preserve" y="149.43" zvalue="68">A0R1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,824.884,50.7843) scale(1,1) translate(0,0)" writing-mode="lr" x="824.88" xml:space="preserve" y="55.28" zvalue="76">#04塔</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,706.25,374.25) scale(1,1) translate(0,0)" writing-mode="lr" x="706.25" xml:space="preserve" y="378.75" zvalue="84">10kV弄么村光伏电站线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1090.62,69.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1090.63" xml:space="preserve" y="73.75" zvalue="85">110kV梁河变/10kV梁茂线#21+1杆T梁川建材支线#04塔</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,990.714,1020.14) scale(1,1) translate(0,0)" writing-mode="lr" x="990.71" xml:space="preserve" y="1024.64" zvalue="86">10kV光伏集成线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" x="1478.1640625" xml:space="preserve" y="492.0424107142858" zvalue="88">#2光阵(弄么</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1478.1640625" xml:space="preserve" y="508.0424107142858" zvalue="88">村)0.357MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1725.68,497.036) scale(1,1) translate(1.85419e-13,0)" writing-mode="lr" x="1725.678571428572" xml:space="preserve" y="501.5357142857142" zvalue="90">#1光阵(水箐村)0.5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1532.5,450.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1532.5" xml:space="preserve" y="455" zvalue="93">400kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1479,451) scale(1,1) translate(0,0)" writing-mode="lr" x="1479" xml:space="preserve" y="455.5" zvalue="94">0.38kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1812.5,450.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1812.5" xml:space="preserve" y="455" zvalue="96">500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1754,453) scale(1,1) translate(0,0)" writing-mode="lr" x="1754" xml:space="preserve" y="457.5" zvalue="98">0.38kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,793.125,226.75) scale(1,1) translate(0,0)" writing-mode="lr" x="793.13" xml:space="preserve" y="231.25" zvalue="101">A011</text>
 </g>
 <g id="ButtonClass">
  <g href="500kV德宏变_全站公用.svg"><rect fill-opacity="0" height="24" width="97" x="38.71" y="300.43" zvalue="10"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="44">
   <path class="kv10" d="M 508.21 558.18 L 1259.46 558.18" stroke-width="6" zvalue="30"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674257862660" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674257862660"/></metadata>
  <path d="M 508.21 558.18 L 1259.46 558.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="41">
   <use class="kv10" height="30" transform="rotate(0,822.589,771.09) scale(2.63393,2.65241) translate(-487.409,-455.591)" width="28" x="785.7142857142858" xlink:href="#EnergyConsumer:站用变DY接地_0" y="731.3035714285713" zvalue="31"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450096136197" ObjectName="10kV#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,822.589,771.09) scale(2.63393,2.65241) translate(-487.409,-455.591)" width="28" x="785.7142857142858" y="731.3035714285713"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="40">
   <use class="kv10" height="42" transform="rotate(0,1060.71,775.679) scale(1.25,-1.25) translate(-208.393,-1390.97)" width="30" x="1041.964285714286" xlink:href="#Accessory:4卷PT带容断器_0" y="749.4285714285716" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450096070661" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1060.71,775.679) scale(1.25,-1.25) translate(-208.393,-1390.97)" width="30" x="1041.964285714286" y="749.4285714285716"/></g>
  <g id="63">
   <use class="kv10" height="26" transform="rotate(0,549.464,794.429) scale(1.25,1.25) translate(-108.393,-155.636)" width="12" x="541.9642857142858" xlink:href="#Accessory:避雷器1_0" y="778.1785714285716" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450095677445" ObjectName="013侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,549.464,794.429) scale(1.25,1.25) translate(-108.393,-155.636)" width="12" x="541.9642857142858" y="778.1785714285716"/></g>
  <g id="65">
   <use class="kv10" height="26" transform="rotate(0,780.714,415.679) scale(1.25,-1.25) translate(-154.643,-744.971)" width="12" x="773.2142857142858" xlink:href="#Accessory:避雷器1_0" y="399.4285714285715" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450095611909" ObjectName="011侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,780.714,415.679) scale(1.25,-1.25) translate(-154.643,-744.971)" width="12" x="773.2142857142858" y="399.4285714285715"/></g>
  <g id="75">
   <use class="kv10" height="15" transform="rotate(0,823.31,189.621) scale(1.25,1.25) translate(-162.787,-36.0492)" width="15" x="813.9354395604398" xlink:href="#Accessory:杆或塔_0" y="180.2458791208792" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450095415301" ObjectName="#1杆"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,823.31,189.621) scale(1.25,1.25) translate(-162.787,-36.0492)" width="15" x="813.9354395604398" y="180.2458791208792"/></g>
  <g id="55">
   <use class="kv10" height="15" transform="rotate(0,823.551,65.1593) scale(1.25,1.25) translate(-162.835,-11.1569)" width="15" x="814.1758241758243" xlink:href="#Accessory:杆或塔_0" y="55.7843406593409" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450095284229" ObjectName="#04塔"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,823.551,65.1593) scale(1.25,1.25) translate(-162.835,-11.1569)" width="15" x="814.1758241758243" y="55.7843406593409"/></g>
 </g>
 <g id="BreakerClass">
  <g id="39">
   <use class="kv10" height="20" transform="rotate(0,601.964,678.179) scale(2.75,2.75) translate(-374.318,-414.068)" width="10" x="588.2142857142858" xlink:href="#Breaker:小车断路器_0" y="650.6785714285716" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924567367685" ObjectName="013"/>
   <cge:TPSR_Ref TObjectID="6473924567367685"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,601.964,678.179) scale(2.75,2.75) translate(-374.318,-414.068)" width="10" x="588.2142857142858" y="650.6785714285716"/></g>
  <g id="37">
   <use class="kv10" height="20" transform="rotate(0,823.214,489.429) scale(2.75,2.75) translate(-515.114,-293.955)" width="10" x="809.464285716217" xlink:href="#Breaker:小车断路器_0" y="461.9285714285715" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924567302149" ObjectName="011"/>
   <cge:TPSR_Ref TObjectID="6473924567302149"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,823.214,489.429) scale(2.75,2.75) translate(-515.114,-293.955)" width="10" x="809.464285716217" y="461.9285714285715"/></g>
  <g id="52">
   <use class="kv10" height="20" transform="rotate(0,823.214,290.929) scale(1.875,1.6875) translate(-379.792,-111.651)" width="10" x="813.8392857142858" xlink:href="#Breaker:开关_0" y="274.0535714285715" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924567236614" ObjectName="A01"/>
   <cge:TPSR_Ref TObjectID="6473924567236614"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,823.214,290.929) scale(1.875,1.6875) translate(-379.792,-111.651)" width="10" x="813.8392857142858" y="274.0535714285715"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="38">
   <path class="kv10" d="M 601.96 652.74 L 601.96 558.18" stroke-width="1" zvalue="37"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@0" LinkObjectIDznd="44@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 601.96 652.74 L 601.96 558.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv10" d="M 823.21 514.18 L 823.21 558.18" stroke-width="1" zvalue="40"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@1" LinkObjectIDznd="44@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.21 514.18 L 823.21 558.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv10" d="M 822.83 732.89 L 822.83 667.3" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="43@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 822.83 732.89 L 822.83 667.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv10" d="M 823.21 626.93 L 823.21 558.18" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@1" LinkObjectIDznd="36" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.21 626.93 L 823.21 558.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv10" d="M 1048.81 750.21 L 1048.81 668.14" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="46@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1048.81 750.21 L 1048.81 668.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv10" d="M 1049.47 635.77 L 1049.47 558.18" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@1" LinkObjectIDznd="44@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1049.47 635.77 L 1049.47 558.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv10" d="M 823.34 307.04 L 823.32 334.38" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@1" LinkObjectIDznd="50@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.34 307.04 L 823.32 334.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv10" d="M 1402.29 441.64 L 1418.21 441.64 L 1418.21 524.43 L 1661.96 524.43 L 1661.96 442.06 L 1681.04 442.06" stroke-width="1" zvalue="63"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21" LinkObjectIDznd="71@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1402.29 441.64 L 1418.21 441.64 L 1418.21 524.43 L 1661.96 524.43 L 1661.96 442.06 L 1681.04 442.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv10" d="M 823.21 463.99 L 823.29 361.19" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="50@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.21 463.99 L 823.29 361.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv10" d="M 823.31 189.62 L 823.31 208.24" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@0" LinkObjectIDznd="93" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.31 189.62 L 823.31 208.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv10" d="M 823.32 129.37 L 823.32 65.16" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="24" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.32 129.37 L 823.32 65.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv10" d="M 823.36 120.55 L 823.32 120.55" stroke-width="1" zvalue="73"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="26" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.36 120.55 L 823.32 120.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv115" d="M 710.33 65.16 L 930.33 65.16" stroke-width="1" zvalue="74"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 710.33 65.16 L 930.33 65.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv10" d="M 823.32 65.16 L 823.32 65.16" stroke-width="1" zvalue="77"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26" LinkObjectIDznd="24" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.32 65.16 L 823.32 65.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv10" d="M 1402.29 442.06 L 1402.29 994.43 L 601.96 994.43 L 601.96 702.93" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="39@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1402.29 442.06 L 1402.29 994.43 L 601.96 994.43 L 601.96 702.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv10" d="M 657.53 760.68 L 601.96 760.68" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 657.53 760.68 L 601.96 760.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv10" d="M 549.51 778.97 L 549.51 759.43 L 601.96 759.43" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 549.51 778.97 L 549.51 759.43 L 601.96 759.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv10" d="M 780.76 431.14 L 780.76 443.18 L 823.23 443.18" stroke-width="1" zvalue="82"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="28" MaxPinNum="2"/>
   </metadata>
  <path d="M 780.76 431.14 L 780.76 443.18 L 823.23 443.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv10" d="M 823.55 65.16 L 823.32 65.16" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="26" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.55 65.16 L 823.32 65.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv10" d="M 823.15 274.78 L 823.08 241.26" stroke-width="1" zvalue="101"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="91@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.15 274.78 L 823.08 241.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv10" d="M 823.11 214.45 L 823.11 161.24" stroke-width="1" zvalue="102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="32@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.11 214.45 L 823.11 161.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="43">
   <use class="kv10" height="33" transform="rotate(0,823.214,646.929) scale(1.25,-1.25) translate(-162.893,-1160.35)" width="14" x="814.4642857162169" xlink:href="#Disconnector:手车隔离开关13_0" y="626.3035714285716" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450096005125" ObjectName="10kV#1站用变0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450096005125"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,823.214,646.929) scale(1.25,-1.25) translate(-162.893,-1160.35)" width="14" x="814.4642857162169" y="626.3035714285716"/></g>
  <g id="46">
   <use class="kv10" height="26" transform="rotate(0,1049.46,651.929) scale(1.25,1.25) translate(-208.393,-127.136)" width="12" x="1041.964285714286" xlink:href="#Disconnector:小车隔刀熔断器_0" y="635.6785714285716" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450095939589" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450095939589"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1049.46,651.929) scale(1.25,1.25) translate(-208.393,-127.136)" width="12" x="1041.964285714286" y="635.6785714285716"/></g>
  <g id="50">
   <use class="kv10" height="30" transform="rotate(0,823.214,347.679) scale(1.25,0.916667) translate(-162.768,30.3571)" width="15" x="813.8392857142858" xlink:href="#Disconnector:刀闸_0" y="333.9285714285715" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450095874053" ObjectName="A012"/>
   <cge:TPSR_Ref TObjectID="6192450095874053"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,823.214,347.679) scale(1.25,0.916667) translate(-162.768,30.3571)" width="15" x="813.8392857142858" y="333.9285714285715"/></g>
  <g id="32">
   <use class="kv10" height="30" transform="rotate(0,823.214,145.929) scale(1.25,1.25) translate(-162.768,-25.4357)" width="15" x="813.8392857142858" xlink:href="#Disconnector:令克_0" y="127.1785714285715" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450095349765" ObjectName="A0R1"/>
   <cge:TPSR_Ref TObjectID="6192450095349765"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,823.214,145.929) scale(1.25,1.25) translate(-162.768,-25.4357)" width="15" x="813.8392857142858" y="127.1785714285715"/></g>
  <g id="91">
   <use class="kv10" height="30" transform="rotate(0,823,227.75) scale(1.25,0.916667) translate(-162.725,19.4545)" width="15" x="813.6253977340432" xlink:href="#Disconnector:刀闸_0" y="214" zvalue="100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450307358725" ObjectName="A011"/>
   <cge:TPSR_Ref TObjectID="6192450307358725"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,823,227.75) scale(1.25,0.916667) translate(-162.725,19.4545)" width="15" x="813.6253977340432" y="214"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="61">
   <use class="kv10" height="20" transform="rotate(270,669.714,760.741) scale(1.25,1.25) translate(-132.693,-149.648)" width="10" x="663.4642857142858" xlink:href="#GroundDisconnector:地刀_0" y="748.2410714285716" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450095808517" ObjectName="01367"/>
   <cge:TPSR_Ref TObjectID="6192450095808517"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,669.714,760.741) scale(1.25,1.25) translate(-132.693,-149.648)" width="10" x="663.4642857142858" y="748.2410714285716"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="95">
   <text Format="f5.1" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="95" prefix="Ia:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,938.714,305.554) scale(1,1) translate(0,0)" writing-mode="lr" x="883.61" xml:space="preserve" y="309.91" zvalue="1">Ia:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127047135236" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="97" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,937.714,278.554) scale(1,1) translate(0,2.02879e-13)" writing-mode="lr" x="889.3200000000001" xml:space="preserve" y="282.91" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127047528452" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="22" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,936.5,251.5) scale(1,1) translate(0,0)" writing-mode="lr" x="888.1" xml:space="preserve" y="255.86" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127062536198" ObjectName="GEN_PSUM"/>
   </metadata>
  </g>
 </g>
</svg>