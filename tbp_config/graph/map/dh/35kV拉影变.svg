<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549681782785" height="1045" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1045" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1734.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2314.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Compensator:10kV电容器_0" viewBox="0,0,24,40">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="4.699999999999999"/>
   <path d="M 12 20.1 L 17.85 20.1 L 17.85 24.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="4.749074074074073" y1="35.35833333333333" y2="35.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.72129629629629" x2="4.72129629629629" y1="33.13611111111111" y2="35.37685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="24.85833333333333" y2="30.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.258333333333324" x2="0.258333333333324" y1="22.10833333333333" y2="33.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.75833333333333" x2="4.75833333333333" y1="22.35833333333333" y2="20.15462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="31.85833333333333" y2="35.37685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="20.04351851851851" y2="24.775"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.024239280774554" x2="4.024239280774554" y1="39.35833333333333" y2="37.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.786111111111114" x2="12.00833333333334" y1="20.10833333333333" y2="20.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="35.35833333333333" y2="39.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.84166666666666" x2="4.024239280774546" y1="39.35833333333333" y2="39.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.84166666666667" x2="19.84166666666667" y1="37.35833333333333" y2="39.35833333333333"/>
   <path d="M 4.75833 25.9572 A 2.96392 1.81747 180 0 1 4.75833 22.3223" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 4.75833 29.5921 A 2.96392 1.81747 180 0 1 4.75833 25.9572" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 4.75833 33.1271 A 2.96392 1.81747 180 0 1 4.75833 29.4921" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="30.85833333333333" y2="30.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="31.85833333333333" y2="31.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="12" y1="4.5" y2="7.166666666666668"/>
   <path d="M 7.26667 12.1 A 4.91667 4.75 -450 1 0 12.0167 7.18333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 7.33333 12.0833 L 12 12.1667 L 12 20.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510778" x2="17.14262023217246" y1="27.75922956383932" y2="26.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="29.36667381975841" y2="30.33114037330986"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.50700202690252" x2="18.32283029297954" y1="31.0857461490052" y2="31.0857461490052"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.28450340888154" x2="18.47116270499357" y1="30.7156109584975" y2="30.7156109584975"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.98783858485353" x2="18.76782752902158" y1="30.34547576798984" y2="30.34547576798984"/>
   <rect fill-opacity="0" height="4.29" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,17.83,27.22) scale(1,1) translate(0,0)" width="2.33" x="16.67" y="25.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="24.56666666666668" y2="25.08015580397416"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510781" x2="18.53565505804314" y1="27.75922956383932" y2="26.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="25.08015580397418" y2="27.73243882624067"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Accessory:线路PT带避雷器0904_0" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="12.91666666666667" y1="23.75" y2="23.75"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,11) scale(1,1) translate(0,0)" width="6" x="7" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.988213005145578" x2="9.988213005145578" y1="1.029523490692871" y2="18.75"/>
   <ellipse cx="9.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="12.91666666666667" y1="32.91666666666667" y2="32.91666666666667"/>
  </symbol>
  <symbol id="Accessory:母线电压互感器11_0" viewBox="0,0,45,48">
   <use terminal-index="0" type="0" x="25.25" xlink:href="#terminal" y="47.78457520218115"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.5,30.95) scale(1,-1) translate(0,-1323.05)" width="6" x="10.5" y="23.95"/>
   <path d="M 8.64167 11.4096 L 4.64167 11.4096 L 6.50709 7.52624 L 7.67376 9.52624 L 8.64167 11.4096" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="13.5" y1="19.78457520218114" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.25" x2="25.25" y1="40.5" y2="47.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="37.25" x2="13.58333333333333" y1="40.5" y2="40.5"/>
   <ellipse cx="13.56" cy="5.42" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799663" x2="13.56620079799663" y1="3.155388266900378" y2="5.82957386674455"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799661" x2="10.88240159599323" y1="5.829573866744575" y2="7.166666666666657"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799662" x2="16.25" y1="5.829573866744575" y2="7.166666666666657"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,36.96,25.94) scale(1,-1) translate(0,-1122.72)" width="7.58" x="33.17" y="17.87"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="34.01790853551448" y2="24.11790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="40.43457520218114" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.7861111111111" x2="39.3" y1="11.60124186884782" y2="11.60124186884782"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.82777777777777" x2="39.93888888888888" y1="12.98282081621623" y2="12.98282081621623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.55" x2="41.21666666666666" y1="14.36439976358464" y2="14.36439976358464"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="34.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="18.01790853551448" y2="14.41790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="40.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <ellipse cx="13.59" cy="15.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.51" cy="10.09" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.67" cy="10.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490864" x2="10.91228003290526" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="13.59607923490865" y1="12.74178419246191" y2="15.41596979230608"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="23.19654510357869" y1="10.49930312563944" y2="11.83639592556152"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="20.51274590157532" y1="7.825117525795246" y2="10.49930312563942"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="16.27987843691203" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5127459015753" x2="17.82894669957193" y1="10.49930312563944" y2="11.83639592556152"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id="EnergyConsumer:站用变无融断_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <path d="M 10 9 L 10 0" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="26.83333333333333" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="10.8868007916835" y2="12.55855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="14.23031840279781" y2="12.55855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV拉影变" InitShowingPlane="" fill="rgb(0,0,0)" height="1045" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="63.02" xlink:href="logo.png" y="33.07"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="347" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,187.868,74.3565) scale(1,1) translate(-1.39935e-14,0)" writing-mode="lr" x="187.87" xml:space="preserve" y="77.86" zvalue="1303"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,216.714,73.2221) scale(1,1) translate(0,0)" writing-mode="lr" x="216.71" xml:space="preserve" y="82.22" zvalue="1304">35kV拉影变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="182" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,292.438,393.75) scale(1,1) translate(0,0)" width="72.88" x="256" y="381.75" zvalue="1885"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,292.438,393.75) scale(1,1) translate(0,0)" writing-mode="lr" x="292.44" xml:space="preserve" y="398.25" zvalue="1885">小电流接地</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="78" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,88.625,312.75) scale(1,1) translate(0,0)" width="72.88" x="52.19" y="300.75" zvalue="1886"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,88.625,312.75) scale(1,1) translate(0,0)" writing-mode="lr" x="88.63" xml:space="preserve" y="317.25" zvalue="1886">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="80" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,190.531,393.75) scale(1,1) translate(0,0)" width="72.88" x="154.09" y="381.75" zvalue="1887"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,190.531,393.75) scale(1,1) translate(0,0)" writing-mode="lr" x="190.53" xml:space="preserve" y="398.25" zvalue="1887">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="44" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,88.625,393.75) scale(1,1) translate(0,0)" width="72.88" x="52.19" y="381.75" zvalue="1888"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,88.625,393.75) scale(1,1) translate(0,0)" writing-mode="lr" x="88.63" xml:space="preserve" y="398.25" zvalue="1888">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="43" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,88.625,353.25) scale(1,1) translate(0,0)" width="72.88" x="52.19" y="341.25" zvalue="1889"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,88.625,353.25) scale(1,1) translate(0,0)" writing-mode="lr" x="88.63" xml:space="preserve" y="357.75" zvalue="1889">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,750.52,333.702) scale(1,1) translate(0,0)" writing-mode="lr" x="750.52" xml:space="preserve" y="338.2" zvalue="7">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,845.173,146.433) scale(1,1) translate(0,0)" writing-mode="lr" x="845.17" xml:space="preserve" y="150.93" zvalue="60">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1078.34,94) scale(1,1) translate(0,0)" writing-mode="lr" x="1078.34" xml:space="preserve" y="98.5" zvalue="299">35kV章拉线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1094.75,248.11) scale(1,1) translate(0,0)" writing-mode="lr" x="1094.75" xml:space="preserve" y="252.61" zvalue="300">361</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1084.91,185.22) scale(1,1) translate(0,0)" writing-mode="lr" x="1084.91" xml:space="preserve" y="189.72" zvalue="302">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1030.95,160.003) scale(1,1) translate(0,0)" writing-mode="lr" x="1030.95" xml:space="preserve" y="164.5" zvalue="304">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1087.26,313.237) scale(1,1) translate(0,0)" writing-mode="lr" x="1087.26" xml:space="preserve" y="317.74" zvalue="307">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1125.18,283.337) scale(1,1) translate(0,0)" writing-mode="lr" x="1125.18" xml:space="preserve" y="287.84" zvalue="316">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,896.174,244.904) scale(1,1) translate(0,0)" writing-mode="lr" x="896.17" xml:space="preserve" y="249.4" zvalue="354">7</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1123.4,212.448) scale(1,1) translate(0,0)" writing-mode="lr" x="1123.4" xml:space="preserve" y="216.95" zvalue="554">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="307" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,869.673,277.294) scale(1,1) translate(0,0)" writing-mode="lr" x="869.67" xml:space="preserve" y="281.79" zvalue="568">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="311" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1014.83,604.451) scale(1,1) translate(0,0)" writing-mode="lr" x="1014.83" xml:space="preserve" y="608.95" zvalue="1175">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="310" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1012.77,442.307) scale(1,1) translate(2.21842e-13,0)" writing-mode="lr" x="1012.77" xml:space="preserve" y="446.81" zvalue="1177">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="309" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1010.81,383.874) scale(1,1) translate(0,0)" writing-mode="lr" x="1010.81" xml:space="preserve" y="388.37" zvalue="1179">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="308" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,930.093,499.163) scale(1,1) translate(0,5.40859e-14)" writing-mode="lr" x="930.0934915309139" xml:space="preserve" y="505.162878036499" zvalue="1185">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="303" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1004.4,646.022) scale(1,1) translate(0,5.66234e-13)" writing-mode="lr" x="1004.4" xml:space="preserve" y="650.52" zvalue="1189">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,693.319,669.333) scale(1,1) translate(0,0)" writing-mode="lr" x="693.3200000000001" xml:space="preserve" y="673.83" zvalue="1200">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,833.295,655.013) scale(1,1) translate(3.63396e-13,0)" writing-mode="lr" x="833.3" xml:space="preserve" y="659.51" zvalue="1205">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,800.151,547.417) scale(1,1) translate(0,-1.19219e-13)" writing-mode="lr" x="800.15" xml:space="preserve" y="551.92" zvalue="1209">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,790.318,830.808) scale(1,1) translate(0,0)" writing-mode="lr" x="790.3200000000001" xml:space="preserve" y="835.3099999999999" zvalue="1226">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,803.085,772.451) scale(1,1) translate(0,0)" writing-mode="lr" x="803.09" xml:space="preserve" y="776.95" zvalue="1228">061</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,790.586,721.363) scale(1,1) translate(0,0)" writing-mode="lr" x="790.59" xml:space="preserve" y="725.86" zvalue="1232">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,780.625,957.673) scale(1,1) translate(0,0)" writing-mode="lr" x="780.62" xml:space="preserve" y="962.17" zvalue="1236">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,847.076,804.231) scale(1,1) translate(0,0)" writing-mode="lr" x="847.08" xml:space="preserve" y="808.73" zvalue="1242">60</text>
  <line fill="none" id="344" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.71428571428635" x2="383.7142857142859" y1="151.5133497569396" y2="151.5133497569396" zvalue="1306"/>
  <line fill="none" id="343" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384.7142857142859" x2="384.7142857142859" y1="9.642857142857224" y2="1039.642857142857" zvalue="1307"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428589" x2="198.7142857142859" y1="163.6428571428572" y2="163.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428589" x2="198.7142857142859" y1="189.6428571428572" y2="189.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428589" x2="17.71428571428589" y1="163.6428571428572" y2="189.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="198.7142857142859" y1="163.6428571428572" y2="189.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="379.7142857142859" y1="163.6428571428572" y2="163.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="379.7142857142859" y1="189.6428571428572" y2="189.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="198.7142857142859" y1="163.6428571428572" y2="189.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.7142857142859" x2="379.7142857142859" y1="163.6428571428572" y2="189.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428589" x2="198.7142857142859" y1="189.6428571428572" y2="189.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428589" x2="198.7142857142859" y1="213.8928571428572" y2="213.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428589" x2="17.71428571428589" y1="189.6428571428572" y2="213.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="198.7142857142859" y1="189.6428571428572" y2="213.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="379.7142857142859" y1="189.6428571428572" y2="189.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="379.7142857142859" y1="213.8928571428572" y2="213.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="198.7142857142859" y1="189.6428571428572" y2="213.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.7142857142859" x2="379.7142857142859" y1="189.6428571428572" y2="213.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428589" x2="198.7142857142859" y1="213.8928571428572" y2="213.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428589" x2="198.7142857142859" y1="236.6428571428572" y2="236.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428589" x2="17.71428571428589" y1="213.8928571428572" y2="236.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="198.7142857142859" y1="213.8928571428572" y2="236.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="379.7142857142859" y1="213.8928571428572" y2="213.8928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="379.7142857142859" y1="236.6428571428572" y2="236.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="198.7142857142859" y1="213.8928571428572" y2="236.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.7142857142859" x2="379.7142857142859" y1="213.8928571428572" y2="236.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428589" x2="198.7142857142859" y1="236.6428571428572" y2="236.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428589" x2="198.7142857142859" y1="259.3928571428572" y2="259.3928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428589" x2="17.71428571428589" y1="236.6428571428572" y2="259.3928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="198.7142857142859" y1="236.6428571428572" y2="259.3928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="379.7142857142859" y1="236.6428571428572" y2="236.6428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="379.7142857142859" y1="259.3928571428572" y2="259.3928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="198.7142857142859" y1="236.6428571428572" y2="259.3928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.7142857142859" x2="379.7142857142859" y1="236.6428571428572" y2="259.3928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428589" x2="198.7142857142859" y1="259.3928571428572" y2="259.3928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428589" x2="198.7142857142859" y1="282.1428571428572" y2="282.1428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.71428571428589" x2="17.71428571428589" y1="259.3928571428572" y2="282.1428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="198.7142857142859" y1="259.3928571428572" y2="282.1428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="379.7142857142859" y1="259.3928571428572" y2="259.3928571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="379.7142857142859" y1="282.1428571428572" y2="282.1428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.7142857142859" x2="198.7142857142859" y1="259.3928571428572" y2="282.1428571428572"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.7142857142859" x2="379.7142857142859" y1="259.3928571428572" y2="282.1428571428572"/>
  <line fill="none" id="341" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.60317460317538" x2="382.6031746031749" y1="628.1800164236063" y2="628.1800164236063" zvalue="1309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.38123174603174" x2="103.1557317460317" y1="425.833373015873" y2="425.833373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.38123174603174" x2="103.1557317460317" y1="457.4807730158731" y2="457.4807730158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.38123174603174" x2="57.38123174603174" y1="425.833373015873" y2="457.4807730158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1557317460317" x2="103.1557317460317" y1="425.833373015873" y2="457.4807730158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1552317460317" x2="174.2220317460318" y1="425.833373015873" y2="425.833373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1552317460317" x2="174.2220317460318" y1="457.4807730158731" y2="457.4807730158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1552317460317" x2="103.1552317460317" y1="425.833373015873" y2="457.4807730158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2220317460318" x2="174.2220317460318" y1="425.833373015873" y2="457.4807730158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2222317460318" x2="227.5555317460318" y1="425.833373015873" y2="425.833373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2222317460318" x2="227.5555317460318" y1="457.4807730158731" y2="457.4807730158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2222317460318" x2="174.2222317460318" y1="425.833373015873" y2="457.4807730158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5555317460318" x2="227.5555317460318" y1="425.833373015873" y2="457.4807730158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5554317460318" x2="293.1111317460318" y1="425.833373015873" y2="425.833373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5554317460318" x2="293.1111317460318" y1="457.4807730158731" y2="457.4807730158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5554317460318" x2="227.5554317460318" y1="425.833373015873" y2="457.4807730158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="293.1111317460318" y1="425.833373015873" y2="457.4807730158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="351.9175317460317" y1="425.833373015873" y2="425.833373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="351.9175317460317" y1="457.4807730158731" y2="457.4807730158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="293.1111317460318" y1="425.833373015873" y2="457.4807730158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="351.9175317460317" x2="351.9175317460317" y1="425.833373015873" y2="457.4807730158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.38123174603174" x2="103.1557317460317" y1="457.480673015873" y2="457.480673015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.38123174603174" x2="103.1557317460317" y1="482.160073015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.38123174603174" x2="57.38123174603174" y1="457.480673015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1557317460317" x2="103.1557317460317" y1="457.480673015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1552317460317" x2="174.2220317460318" y1="457.480673015873" y2="457.480673015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1552317460317" x2="174.2220317460318" y1="482.160073015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1552317460317" x2="103.1552317460317" y1="457.480673015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2220317460318" x2="174.2220317460318" y1="457.480673015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2222317460318" x2="227.5555317460318" y1="457.480673015873" y2="457.480673015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2222317460318" x2="227.5555317460318" y1="482.160073015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2222317460318" x2="174.2222317460318" y1="457.480673015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5555317460318" x2="227.5555317460318" y1="457.480673015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5554317460318" x2="293.1111317460318" y1="457.480673015873" y2="457.480673015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5554317460318" x2="293.1111317460318" y1="482.160073015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5554317460318" x2="227.5554317460318" y1="457.480673015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="293.1111317460318" y1="457.480673015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="351.9175317460317" y1="457.480673015873" y2="457.480673015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="351.9175317460317" y1="482.160073015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="293.1111317460318" y1="457.480673015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="351.9175317460317" x2="351.9175317460317" y1="457.480673015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.38123174603174" x2="103.1557317460317" y1="482.160073015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.38123174603174" x2="103.1557317460317" y1="506.839473015873" y2="506.839473015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.38123174603174" x2="57.38123174603174" y1="482.160073015873" y2="506.839473015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1557317460317" x2="103.1557317460317" y1="482.160073015873" y2="506.839473015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1552317460317" x2="174.2220317460318" y1="482.160073015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1552317460317" x2="174.2220317460318" y1="506.839473015873" y2="506.839473015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1552317460317" x2="103.1552317460317" y1="482.160073015873" y2="506.839473015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2220317460318" x2="174.2220317460318" y1="482.160073015873" y2="506.839473015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2222317460318" x2="227.5555317460318" y1="482.160073015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2222317460318" x2="227.5555317460318" y1="506.839473015873" y2="506.839473015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2222317460318" x2="174.2222317460318" y1="482.160073015873" y2="506.839473015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5555317460318" x2="227.5555317460318" y1="482.160073015873" y2="506.839473015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5554317460318" x2="293.1111317460318" y1="482.160073015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5554317460318" x2="293.1111317460318" y1="506.839473015873" y2="506.839473015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5554317460318" x2="227.5554317460318" y1="482.160073015873" y2="506.839473015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="293.1111317460318" y1="482.160073015873" y2="506.839473015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="351.9175317460317" y1="482.160073015873" y2="482.160073015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="351.9175317460317" y1="506.839473015873" y2="506.839473015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="293.1111317460318" y1="482.160073015873" y2="506.839473015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="351.9175317460317" x2="351.9175317460317" y1="482.160073015873" y2="506.839473015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.38123174603174" x2="103.1557317460317" y1="506.839513015873" y2="506.839513015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.38123174603174" x2="103.1557317460317" y1="531.5189130158731" y2="531.5189130158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.38123174603174" x2="57.38123174603174" y1="506.839513015873" y2="531.5189130158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1557317460317" x2="103.1557317460317" y1="506.839513015873" y2="531.5189130158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1552317460317" x2="174.2220317460318" y1="506.839513015873" y2="506.839513015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1552317460317" x2="174.2220317460318" y1="531.5189130158731" y2="531.5189130158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1552317460317" x2="103.1552317460317" y1="506.839513015873" y2="531.5189130158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2220317460318" x2="174.2220317460318" y1="506.839513015873" y2="531.5189130158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2222317460318" x2="227.5555317460318" y1="506.839513015873" y2="506.839513015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2222317460318" x2="227.5555317460318" y1="531.5189130158731" y2="531.5189130158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2222317460318" x2="174.2222317460318" y1="506.839513015873" y2="531.5189130158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5555317460318" x2="227.5555317460318" y1="506.839513015873" y2="531.5189130158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5554317460318" x2="293.1111317460318" y1="506.839513015873" y2="506.839513015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5554317460318" x2="293.1111317460318" y1="531.5189130158731" y2="531.5189130158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5554317460318" x2="227.5554317460318" y1="506.839513015873" y2="531.5189130158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="293.1111317460318" y1="506.839513015873" y2="531.5189130158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="351.9175317460317" y1="506.839513015873" y2="506.839513015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="351.9175317460317" y1="531.5189130158731" y2="531.5189130158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="293.1111317460318" y1="506.839513015873" y2="531.5189130158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="351.9175317460317" x2="351.9175317460317" y1="506.839513015873" y2="531.5189130158731"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.38123174603174" x2="103.1557317460317" y1="531.518973015873" y2="531.518973015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.38123174603174" x2="103.1557317460317" y1="556.198373015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.38123174603174" x2="57.38123174603174" y1="531.518973015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1557317460317" x2="103.1557317460317" y1="531.518973015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1552317460317" x2="174.2220317460318" y1="531.518973015873" y2="531.518973015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1552317460317" x2="174.2220317460318" y1="556.198373015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1552317460317" x2="103.1552317460317" y1="531.518973015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2220317460318" x2="174.2220317460318" y1="531.518973015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2222317460318" x2="227.5555317460318" y1="531.518973015873" y2="531.518973015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2222317460318" x2="227.5555317460318" y1="556.198373015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2222317460318" x2="174.2222317460318" y1="531.518973015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5555317460318" x2="227.5555317460318" y1="531.518973015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5554317460318" x2="293.1111317460318" y1="531.518973015873" y2="531.518973015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5554317460318" x2="293.1111317460318" y1="556.198373015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5554317460318" x2="227.5554317460318" y1="531.518973015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="293.1111317460318" y1="531.518973015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="351.9175317460317" y1="531.518973015873" y2="531.518973015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="351.9175317460317" y1="556.198373015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="293.1111317460318" y1="531.518973015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="351.9175317460317" x2="351.9175317460317" y1="531.518973015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.38123174603174" x2="103.1557317460317" y1="556.198373015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.38123174603174" x2="103.1557317460317" y1="580.877773015873" y2="580.877773015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.38123174603174" x2="57.38123174603174" y1="556.198373015873" y2="580.877773015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1557317460317" x2="103.1557317460317" y1="556.198373015873" y2="580.877773015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1552317460317" x2="174.2220317460318" y1="556.198373015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1552317460317" x2="174.2220317460318" y1="580.877773015873" y2="580.877773015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.1552317460317" x2="103.1552317460317" y1="556.198373015873" y2="580.877773015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2220317460318" x2="174.2220317460318" y1="556.198373015873" y2="580.877773015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2222317460318" x2="227.5555317460318" y1="556.198373015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2222317460318" x2="227.5555317460318" y1="580.877773015873" y2="580.877773015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="174.2222317460318" x2="174.2222317460318" y1="556.198373015873" y2="580.877773015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5555317460318" x2="227.5555317460318" y1="556.198373015873" y2="580.877773015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5554317460318" x2="293.1111317460318" y1="556.198373015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5554317460318" x2="293.1111317460318" y1="580.877773015873" y2="580.877773015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.5554317460318" x2="227.5554317460318" y1="556.198373015873" y2="580.877773015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="293.1111317460318" y1="556.198373015873" y2="580.877773015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="351.9175317460317" y1="556.198373015873" y2="556.198373015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="351.9175317460317" y1="580.877773015873" y2="580.877773015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="293.1111317460318" x2="293.1111317460318" y1="556.198373015873" y2="580.877773015873"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="351.9175317460317" x2="351.9175317460317" y1="556.198373015873" y2="580.877773015873"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.71428571428589" x2="106.7142857142859" y1="936.6428571428572" y2="936.6428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.71428571428589" x2="106.7142857142859" y1="975.8061571428573" y2="975.8061571428573"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.71428571428589" x2="16.71428571428589" y1="936.6428571428572" y2="975.8061571428573"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="106.7142857142859" y1="936.6428571428572" y2="975.8061571428573"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="376.7142857142859" y1="936.6428571428572" y2="936.6428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="376.7142857142859" y1="975.8061571428573" y2="975.8061571428573"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="106.7142857142859" y1="936.6428571428572" y2="975.8061571428573"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="376.7142857142859" x2="376.7142857142859" y1="936.6428571428572" y2="975.8061571428573"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.71428571428589" x2="106.7142857142859" y1="975.8061271428572" y2="975.8061271428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.71428571428589" x2="106.7142857142859" y1="1003.724527142857" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.71428571428589" x2="16.71428571428589" y1="975.8061271428572" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="106.7142857142859" y1="975.8061271428572" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="196.7142857142859" y1="975.8061271428572" y2="975.8061271428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="196.7142857142859" y1="1003.724527142857" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="106.7142857142859" y1="975.8061271428572" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.7142857142859" x2="196.7142857142859" y1="975.8061271428572" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.714285714286" x2="286.714285714286" y1="975.8061271428572" y2="975.8061271428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.714285714286" x2="286.714285714286" y1="1003.724527142857" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.714285714286" x2="196.714285714286" y1="975.8061271428572" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.714285714286" x2="286.714285714286" y1="975.8061271428572" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.7142857142859" x2="376.7142857142859" y1="975.8061271428572" y2="975.8061271428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.7142857142859" x2="376.7142857142859" y1="1003.724527142857" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.7142857142859" x2="286.7142857142859" y1="975.8061271428572" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="376.7142857142859" x2="376.7142857142859" y1="975.8061271428572" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.71428571428589" x2="106.7142857142859" y1="1003.724457142857" y2="1003.724457142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.71428571428589" x2="106.7142857142859" y1="1031.642857142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.71428571428589" x2="16.71428571428589" y1="1003.724457142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="106.7142857142859" y1="1003.724457142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="196.7142857142859" y1="1003.724457142857" y2="1003.724457142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="196.7142857142859" y1="1031.642857142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="106.7142857142859" y1="1003.724457142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.7142857142859" x2="196.7142857142859" y1="1003.724457142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.714285714286" x2="286.714285714286" y1="1003.724457142857" y2="1003.724457142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.714285714286" x2="286.714285714286" y1="1031.642857142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.714285714286" x2="196.714285714286" y1="1003.724457142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.714285714286" x2="286.714285714286" y1="1003.724457142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.7142857142859" x2="376.7142857142859" y1="1003.724457142857" y2="1003.724457142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.7142857142859" x2="376.7142857142859" y1="1031.642857142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.7142857142859" x2="286.7142857142859" y1="1003.724457142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="376.7142857142859" x2="376.7142857142859" y1="1003.724457142857" y2="1031.642857142857"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="337" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,61.7143,956.643) scale(1,1) translate(0,0)" writing-mode="lr" x="61.71" xml:space="preserve" y="962.64" zvalue="1313">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="336" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.7143,990.643) scale(1,1) translate(0,0)" writing-mode="lr" x="58.71" xml:space="preserve" y="996.64" zvalue="1314">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="335" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.714,990.643) scale(1,1) translate(0,0)" writing-mode="lr" x="240.71" xml:space="preserve" y="996.64" zvalue="1315">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="334" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.7143,1018.64) scale(1,1) translate(0,0)" writing-mode="lr" x="57.71" xml:space="preserve" y="1024.64" zvalue="1316">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="333" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,239.714,1018.64) scale(1,1) translate(0,0)" writing-mode="lr" x="239.71" xml:space="preserve" y="1024.64" zvalue="1317">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="329" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,140.677,442.254) scale(1,1) translate(1.98198e-13,0)" writing-mode="lr" x="140.6766008649555" xml:space="preserve" y="446.7539682539683" zvalue="1318">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="327" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.2143,651.143) scale(1,1) translate(0,0)" writing-mode="lr" x="82.21428571428589" xml:space="preserve" y="655.6428571428572" zvalue="1320">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="326" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,215.113,318.484) scale(1,1) translate(0,0)" writing-mode="lr" x="215.11" xml:space="preserve" y="322.98" zvalue="1321">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="317" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,320.113,318.484) scale(1,1) translate(0,0)" writing-mode="lr" x="320.11" xml:space="preserve" y="322.98" zvalue="1322">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="316" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,262.045,443.365) scale(1,1) translate(0,0)" writing-mode="lr" x="262.044653009801" xml:space="preserve" y="447.8650793650794" zvalue="1323">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.7143,474.393) scale(1,1) translate(0,0)" writing-mode="lr" x="85.71428571428589" xml:space="preserve" y="478.8928571428572" zvalue="1325">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.7143,499.893) scale(1,1) translate(0,-1.07779e-13)" writing-mode="lr" x="85.71428571428589" xml:space="preserve" y="504.3928571428572" zvalue="1326">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="300" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.7143,522.893) scale(1,1) translate(0,0)" writing-mode="lr" x="85.71428571428589" xml:space="preserve" y="527.3928571428572" zvalue="1327">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="299" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.7143,545.893) scale(1,1) translate(0,5.89965e-14)" writing-mode="lr" x="85.71428571428589" xml:space="preserve" y="550.3928571428572" zvalue="1328">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="298" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.7143,572.893) scale(1,1) translate(0,1.23988e-13)" writing-mode="lr" x="85.71428571428589" xml:space="preserve" y="577.3928571428572" zvalue="1329">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="297" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,223.495,957.532) scale(1,1) translate(0,0)" writing-mode="lr" x="223.5" xml:space="preserve" y="963.53" zvalue="1330">LaYing-01-2015</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="295" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,331.768,990.643) scale(1,1) translate(0,0)" writing-mode="lr" x="331.77" xml:space="preserve" y="996.64" zvalue="1332">20201014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.7143,177.643) scale(1,1) translate(0,0)" writing-mode="lr" x="55.71" xml:space="preserve" y="183.14" zvalue="1333">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="290" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.714,177.643) scale(1,1) translate(0,0)" writing-mode="lr" x="235.71" xml:space="preserve" y="183.14" zvalue="1334">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.4018,201.893) scale(1,1) translate(0,0)" writing-mode="lr" x="59.4" xml:space="preserve" y="206.39" zvalue="1335">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.9018,249.643) scale(1,1) translate(0,0)" writing-mode="lr" x="62.9" xml:space="preserve" y="255.14" zvalue="1336">#1主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,244.464,249.143) scale(1,1) translate(0,0)" writing-mode="lr" x="244.46" xml:space="preserve" y="254.64" zvalue="1337">#2主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="286" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.9018,272.643) scale(1,1) translate(0,-3.49244e-13)" writing-mode="lr" x="62.9" xml:space="preserve" y="278.14" zvalue="1338">#1主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,244.464,272.143) scale(1,1) translate(0,-3.48578e-13)" writing-mode="lr" x="244.46" xml:space="preserve" y="277.64" zvalue="1339">#2主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.4018,225.893) scale(1,1) translate(0,0)" writing-mode="lr" x="60.4" xml:space="preserve" y="230.39" zvalue="1340">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1050.93,116.306) scale(1,1) translate(0,0)" writing-mode="lr" x="1050.93" xml:space="preserve" y="120.81" zvalue="1372">9</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1215.43,161.635) scale(1,1) translate(0,0)" writing-mode="lr" x="1215.43" xml:space="preserve" y="166.14" zvalue="1441">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,845.52,860.231) scale(1,1) translate(0,0)" writing-mode="lr" x="845.52" xml:space="preserve" y="864.73" zvalue="1444">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="375" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,932.904,912.5) scale(1,1) translate(0,0)" writing-mode="lr" x="932.9" xml:space="preserve" y="917" zvalue="1486">10kV曼拉线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,947.602,829.113) scale(1,1) translate(0,0)" writing-mode="lr" x="947.6" xml:space="preserve" y="833.61" zvalue="1486">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,954.814,767.701) scale(1,1) translate(0,0)" writing-mode="lr" x="954.8099999999999" xml:space="preserve" y="772.2" zvalue="1489">062</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,945.774,724.113) scale(1,1) translate(0,0)" writing-mode="lr" x="945.77" xml:space="preserve" y="728.61" zvalue="1492">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="467" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,833.413,744.62) scale(1,1) translate(0,0)" writing-mode="lr" x="833.41" xml:space="preserve" y="749.12" zvalue="1567">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="484" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,159.5,991) scale(1,1) translate(0,0)" writing-mode="lr" x="159.5" xml:space="preserve" y="997" zvalue="1574">李艳</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,930.416,406.611) scale(1,1) translate(0,0)" writing-mode="lr" x="930.42" xml:space="preserve" y="411.11" zvalue="1585">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,925,521.163) scale(1,1) translate(0,0)" writing-mode="lr" x="924.9995238762392" xml:space="preserve" y="527.162878036499" zvalue="1612">3150kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1358.34,96.8889) scale(1,1) translate(0,0)" writing-mode="lr" x="1358.34" xml:space="preserve" y="101.39" zvalue="1630">35kV拉祥线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1374.75,256.11) scale(1,1) translate(0,0)" writing-mode="lr" x="1374.75" xml:space="preserve" y="260.61" zvalue="1631">362</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1366.02,194.331) scale(1,1) translate(0,0)" writing-mode="lr" x="1366.02" xml:space="preserve" y="198.83" zvalue="1633">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1312.06,164.003) scale(1,1) translate(0,0)" writing-mode="lr" x="1312.06" xml:space="preserve" y="168.5" zvalue="1635">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1369.48,313.904) scale(1,1) translate(0,0)" writing-mode="lr" x="1369.48" xml:space="preserve" y="318.4" zvalue="1638">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1405.62,282.226) scale(1,1) translate(0,0)" writing-mode="lr" x="1405.62" xml:space="preserve" y="286.73" zvalue="1644">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1406.73,217.559) scale(1,1) translate(0,0)" writing-mode="lr" x="1406.73" xml:space="preserve" y="222.06" zvalue="1647">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1330.93,119.195) scale(1,1) translate(0,0)" writing-mode="lr" x="1330.93" xml:space="preserve" y="123.7" zvalue="1650">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,889.484,314.673) scale(1,1) translate(7.86468e-13,0)" writing-mode="lr" x="889.48" xml:space="preserve" y="319.17" zvalue="1663">0</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1288.83,604.451) scale(1,1) translate(0,0)" writing-mode="lr" x="1288.83" xml:space="preserve" y="608.95" zvalue="1669">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1286.77,442.307) scale(1,1) translate(7.06706e-13,0)" writing-mode="lr" x="1286.77" xml:space="preserve" y="446.81" zvalue="1671">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1284.81,383.874) scale(1,1) translate(0,0)" writing-mode="lr" x="1284.81" xml:space="preserve" y="388.37" zvalue="1673">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1201.87,499.163) scale(1,1) translate(-5.21304e-13,-1.08172e-13)" writing-mode="lr" x="1201.871269308692" xml:space="preserve" y="505.1628780461321" zvalue="1677">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1278.4,646.022) scale(1,1) translate(0,5.66234e-13)" writing-mode="lr" x="1278.4" xml:space="preserve" y="650.52" zvalue="1679">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1204.42,406.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1204.42" xml:space="preserve" y="411.11" zvalue="1685">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1202.33,521.163) scale(1,1) translate(-5.17956e-13,0)" writing-mode="lr" x="1202.332857209573" xml:space="preserve" y="527.1628782580597" zvalue="1686">3150kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1077.6,829.113) scale(1,1) translate(1.9071e-12,0)" writing-mode="lr" x="1077.6" xml:space="preserve" y="833.61" zvalue="1691">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1062.9,912.5) scale(1,1) translate(1.12344e-13,0)" writing-mode="lr" x="1062.9" xml:space="preserve" y="917" zvalue="1692">10kV拉影街面线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1084.81,767.701) scale(1,1) translate(0,0)" writing-mode="lr" x="1084.81" xml:space="preserve" y="772.2" zvalue="1694">063</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1075.77,724.113) scale(1,1) translate(0,0)" writing-mode="lr" x="1075.77" xml:space="preserve" y="728.61" zvalue="1697">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1207.6,829.113) scale(1,1) translate(0,0)" writing-mode="lr" x="1207.6" xml:space="preserve" y="833.61" zvalue="1704">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1192.9,912.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1192.9" xml:space="preserve" y="917" zvalue="1705">10kV迈扎央线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1214.81,767.701) scale(1,1) translate(0,0)" writing-mode="lr" x="1214.81" xml:space="preserve" y="772.2" zvalue="1707">064</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1205.77,724.113) scale(1,1) translate(6.8131e-12,0)" writing-mode="lr" x="1205.77" xml:space="preserve" y="728.61" zvalue="1710">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="175" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1337.6,829.113) scale(1,1) translate(0,0)" writing-mode="lr" x="1337.6" xml:space="preserve" y="833.61" zvalue="1717">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1322.9,912.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.9" xml:space="preserve" y="917" zvalue="1718">10kV洋人街老迈扎央线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1344.81,767.701) scale(1,1) translate(0,0)" writing-mode="lr" x="1344.81" xml:space="preserve" y="772.2" zvalue="1720">065</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1335.77,724.113) scale(1,1) translate(0,0)" writing-mode="lr" x="1335.77" xml:space="preserve" y="728.61" zvalue="1723">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1466.32,830.808) scale(1,1) translate(0,0)" writing-mode="lr" x="1466.32" xml:space="preserve" y="835.3099999999999" zvalue="1730">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1479.09,772.451) scale(1,1) translate(0,0)" writing-mode="lr" x="1479.09" xml:space="preserve" y="776.95" zvalue="1732">066</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1466.59,721.363) scale(1,1) translate(0,0)" writing-mode="lr" x="1466.59" xml:space="preserve" y="725.86" zvalue="1735">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1521.52,860.231) scale(1,1) translate(0,0)" writing-mode="lr" x="1521.52" xml:space="preserve" y="864.73" zvalue="1745">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="191" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1509.41,744.62) scale(1,1) translate(0,0)" writing-mode="lr" x="1509.41" xml:space="preserve" y="749.12" zvalue="1748">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1452.56,906.333) scale(1,1) translate(-2.87582e-12,0)" writing-mode="lr" x="1452.56" xml:space="preserve" y="910.83" zvalue="1752">备用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="230" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1596.32,830.808) scale(1,1) translate(0,0)" writing-mode="lr" x="1596.32" xml:space="preserve" y="835.3099999999999" zvalue="1754">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1609.09,772.451) scale(1,1) translate(0,0)" writing-mode="lr" x="1609.09" xml:space="preserve" y="776.95" zvalue="1756">067</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="224" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1596.59,721.363) scale(1,1) translate(0,0)" writing-mode="lr" x="1596.59" xml:space="preserve" y="725.86" zvalue="1759">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="223" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1651.52,860.231) scale(1,1) translate(0,0)" writing-mode="lr" x="1651.52" xml:space="preserve" y="864.73" zvalue="1764">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="222" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1639.41,744.62) scale(1,1) translate(0,0)" writing-mode="lr" x="1639.41" xml:space="preserve" y="749.12" zvalue="1767">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1582.56,906.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1582.56" xml:space="preserve" y="910.83" zvalue="1770">备用</text>
 </g>
 <g id="ButtonClass">
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="256" y="381.75" zvalue="1885"/></g>
  <g href="全站公用20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="52.19" y="300.75" zvalue="1886"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="154.09" y="381.75" zvalue="1887"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="52.19" y="381.75" zvalue="1888"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="52.19" y="341.25" zvalue="1889"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="128">
   <path class="kv35" d="M 725.67 345.59 L 1483.11 345.59" stroke-width="6" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674410430467" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674410430467"/></metadata>
  <path d="M 725.67 345.59 L 1483.11 345.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="330">
   <path class="kv10" d="M 668.67 682 L 1694.22 682" stroke-width="6" zvalue="1199"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674410496003" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674410496003"/></metadata>
  <path d="M 668.67 682 L 1694.22 682" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="218">
   <use class="kv35" height="30" transform="rotate(0,848.559,180.654) scale(-1.25,-1.25) translate(-1523.66,-321.427)" width="30" x="829.8086446218178" xlink:href="#Accessory:避雷器PT带熔断器_0" y="161.9039533263671" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454319407106" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,848.559,180.654) scale(-1.25,-1.25) translate(-1523.66,-321.427)" width="30" x="829.8086446218178" y="161.9039533263671"/></g>
  <g id="15">
   <use class="kv35" height="26" transform="rotate(270,1104.57,136.19) scale(-0.838049,0.927421) translate(-2423.57,9.71459)" width="12" x="1099.543239071856" xlink:href="#Accessory:避雷器1_0" y="124.1339168478893" zvalue="312"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454321373186" ObjectName="35kV章拉线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1104.57,136.19) scale(-0.838049,0.927421) translate(-2423.57,9.71459)" width="12" x="1099.543239071856" y="124.1339168478893"/></g>
  <g id="100">
   <use class="kv10" height="48" transform="rotate(180,806.151,590.028) scale(-1.24444,-1.16667) translate(-1448.45,-1091.77)" width="45" x="778.1511111111112" xlink:href="#Accessory:母线电压互感器11_0" y="562.0277777777778" zvalue="1208"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454320390146" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(180,806.151,590.028) scale(-1.24444,-1.16667) translate(-1448.45,-1091.77)" width="45" x="778.1511111111112" y="562.0277777777778"/></g>
  <g id="130">
   <use class="kv35" height="40" transform="rotate(90,1018.35,136.194) scale(-1,1) translate(-2036.7,0)" width="20" x="1008.350226310201" xlink:href="#Accessory:线路PT带避雷器0904_0" y="116.1944444444445" zvalue="1375"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454321438722" ObjectName="35kV章拉线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1018.35,136.194) scale(-1,1) translate(-2036.7,0)" width="20" x="1008.350226310201" y="116.1944444444445"/></g>
  <g id="115">
   <use class="kv35" height="26" transform="rotate(270,1384.57,139.079) scale(-0.838049,0.927421) translate(-3037.68,9.94067)" width="12" x="1379.543239071856" xlink:href="#Accessory:避雷器1_0" y="127.0228057367782" zvalue="1641"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454322028546" ObjectName="35kV拉祥线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1384.57,139.079) scale(-0.838049,0.927421) translate(-3037.68,9.94067)" width="12" x="1379.543239071856" y="127.0228057367782"/></g>
  <g id="93">
   <use class="kv35" height="40" transform="rotate(90,1298.35,139.083) scale(-1,1) translate(-2596.7,0)" width="20" x="1288.350226310201" xlink:href="#Accessory:线路PT带避雷器0904_0" y="119.0833333333334" zvalue="1651"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454321635330" ObjectName="35kV拉祥线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1298.35,139.083) scale(-1,1) translate(-2596.7,0)" width="20" x="1288.350226310201" y="119.0833333333334"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="70">
   <use class="kv35" height="30" transform="rotate(0,1071.63,111.532) scale(1.98323,0.522926) translate(-527.842,94.5959)" width="7" x="1064.686704179698" xlink:href="#ACLineSegment:线路_0" y="103.687764596597" zvalue="297"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249326288898" ObjectName="35kV章拉线"/>
   <cge:TPSR_Ref TObjectID="8444249326288898_5066549681782785"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1071.63,111.532) scale(1.98323,0.522926) translate(-527.842,94.5959)" width="7" x="1064.686704179698" y="103.687764596597"/></g>
 </g>
 <g id="BreakerClass">
  <g id="69">
   <use class="kv35" height="20" transform="rotate(0,1071.71,250.708) scale(1.5542,1.35421) translate(-379.38,-62.0336)" width="10" x="1063.934034534565" xlink:href="#Breaker:开关_0" y="237.165750510661" zvalue="298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925156864003" ObjectName="35kV章拉线361断路器"/>
   <cge:TPSR_Ref TObjectID="6473925156864003"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1071.71,250.708) scale(1.5542,1.35421) translate(-379.38,-62.0336)" width="10" x="1063.934034534565" y="237.165750510661"/></g>
  <g id="325">
   <use class="kv10" height="20" transform="rotate(180,986.496,600.451) scale(1.5542,1.35421) translate(-348.995,-153.513)" width="10" x="978.7245763550374" xlink:href="#Breaker:开关_0" y="586.9090909090909" zvalue="1174"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925156995075" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925156995075"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,986.496,600.451) scale(1.5542,1.35421) translate(-348.995,-153.513)" width="10" x="978.7245763550374" y="586.9090909090909"/></g>
  <g id="324">
   <use class="kv35" height="20" transform="rotate(180,987.359,444.154) scale(1.5542,1.35421) translate(-349.303,-112.632)" width="10" x="979.5882603069475" xlink:href="#Breaker:开关_0" y="430.6115014070385" zvalue="1176"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925156929539" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925156929539"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,987.359,444.154) scale(1.5542,1.35421) translate(-349.303,-112.632)" width="10" x="979.5882603069475" y="430.6115014070385"/></g>
  <g id="176">
   <use class="kv10" height="20" transform="rotate(180,776.5,771.701) scale(1.5542,1.35421) translate(-274.115,-198.306)" width="10" x="768.7291508472352" xlink:href="#Breaker:开关_0" y="758.1592274655184" zvalue="1227"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925157060611" ObjectName="10kV1号电容器061断路器"/>
   <cge:TPSR_Ref TObjectID="6473925157060611"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,776.5,771.701) scale(1.5542,1.35421) translate(-274.115,-198.306)" width="10" x="768.7291508472352" y="758.1592274655184"/></g>
  <g id="391">
   <use class="kv10" height="20" transform="rotate(180,932.821,768.701) scale(1.5542,1.35421) translate(-329.856,-197.521)" width="10" x="925.0501807201683" xlink:href="#Breaker:开关_0" y="755.1592274655184" zvalue="1488"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925157126147" ObjectName="10kV曼拉线062断路器"/>
   <cge:TPSR_Ref TObjectID="6473925157126147"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,932.821,768.701) scale(1.5542,1.35421) translate(-329.856,-197.521)" width="10" x="925.0501807201683" y="755.1592274655184"/></g>
  <g id="145">
   <use class="kv35" height="20" transform="rotate(0,1351.71,257.597) scale(1.5542,1.35421) translate(-479.223,-63.8355)" width="10" x="1343.934034534565" xlink:href="#Breaker:开关_0" y="244.05463939955" zvalue="1629"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925157191683" ObjectName="35kV拉祥线362断路器"/>
   <cge:TPSR_Ref TObjectID="6473925157191683"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1351.71,257.597) scale(1.5542,1.35421) translate(-479.223,-63.8355)" width="10" x="1343.934034534565" y="244.05463939955"/></g>
  <g id="136">
   <use class="kv10" height="20" transform="rotate(180,1260.5,600.451) scale(1.5542,1.35421) translate(-446.699,-153.513)" width="10" x="1252.724576355037" xlink:href="#Breaker:开关_0" y="586.9090909090909" zvalue="1668"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925157322755" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473925157322755"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1260.5,600.451) scale(1.5542,1.35421) translate(-446.699,-153.513)" width="10" x="1252.724576355037" y="586.9090909090909"/></g>
  <g id="126">
   <use class="kv35" height="20" transform="rotate(180,1261.36,444.154) scale(1.5542,1.35421) translate(-447.007,-112.632)" width="10" x="1253.588260306948" xlink:href="#Breaker:开关_0" y="430.6115014070385" zvalue="1670"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925157257219" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925157257219"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1261.36,444.154) scale(1.5542,1.35421) translate(-447.007,-112.632)" width="10" x="1253.588260306948" y="430.6115014070385"/></g>
  <g id="152">
   <use class="kv10" height="20" transform="rotate(180,1062.82,768.701) scale(1.5542,1.35421) translate(-376.212,-197.521)" width="10" x="1055.050180720168" xlink:href="#Breaker:开关_0" y="755.1592274655184" zvalue="1693"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925157388291" ObjectName="10kV拉影街面线063断路器"/>
   <cge:TPSR_Ref TObjectID="6473925157388291"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1062.82,768.701) scale(1.5542,1.35421) translate(-376.212,-197.521)" width="10" x="1055.050180720168" y="755.1592274655184"/></g>
  <g id="167">
   <use class="kv10" height="20" transform="rotate(180,1192.82,768.701) scale(1.5542,1.35421) translate(-422.567,-197.521)" width="10" x="1185.050180720168" xlink:href="#Breaker:开关_0" y="755.1592274655184" zvalue="1706"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925157453827" ObjectName="10kV迈扎央线064断路器"/>
   <cge:TPSR_Ref TObjectID="6473925157453827"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1192.82,768.701) scale(1.5542,1.35421) translate(-422.567,-197.521)" width="10" x="1185.050180720168" y="755.1592274655184"/></g>
  <g id="184">
   <use class="kv10" height="20" transform="rotate(180,1322.82,768.701) scale(1.5542,1.35421) translate(-468.923,-197.521)" width="10" x="1315.050180720168" xlink:href="#Breaker:开关_0" y="755.1592274655184" zvalue="1719"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925157519363" ObjectName="10kV洋人街老迈扎央线065断路器"/>
   <cge:TPSR_Ref TObjectID="6473925157519363"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1322.82,768.701) scale(1.5542,1.35421) translate(-468.923,-197.521)" width="10" x="1315.050180720168" y="755.1592274655184"/></g>
  <g id="216">
   <use class="kv10" height="20" transform="rotate(180,1452.5,771.701) scale(1.5542,1.35421) translate(-515.164,-198.306)" width="10" x="1444.729150847235" xlink:href="#Breaker:开关_0" y="758.1592274655184" zvalue="1731"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925157584899" ObjectName="10kV5S备用线066断路器"/>
   <cge:TPSR_Ref TObjectID="6473925157584899"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1452.5,771.701) scale(1.5542,1.35421) translate(-515.164,-198.306)" width="10" x="1444.729150847235" y="758.1592274655184"/></g>
  <g id="244">
   <use class="kv10" height="20" transform="rotate(180,1582.5,771.701) scale(1.5542,1.35421) translate(-561.52,-198.306)" width="10" x="1574.729150847235" xlink:href="#Breaker:开关_0" y="758.1592274655184" zvalue="1755"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925157650435" ObjectName="10kV6S备用线067断路器"/>
   <cge:TPSR_Ref TObjectID="6473925157650435"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1582.5,771.701) scale(1.5542,1.35421) translate(-561.52,-198.306)" width="10" x="1574.729150847235" y="758.1592274655184"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="68">
   <use class="kv35" height="30" transform="rotate(0,1071.71,185.276) scale(0.947693,0.6712) translate(58.7599,85.8286)" width="15" x="1064.603492892157" xlink:href="#Disconnector:刀闸_0" y="175.2075673790615" zvalue="301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454319669250" ObjectName="35kV章拉线3616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454319669250"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1071.71,185.276) scale(0.947693,0.6712) translate(58.7599,85.8286)" width="15" x="1064.603492892157" y="175.2075673790615"/></g>
  <g id="20">
   <use class="kv35" height="30" transform="rotate(180,1071.89,312.544) scale(0.947693,-0.6712) translate(58.7698,-783.126)" width="15" x="1064.784133425996" xlink:href="#Disconnector:刀闸_0" y="302.4763051659212" zvalue="306"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454319472642" ObjectName="35kV章拉线3611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454319472642"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1071.89,312.544) scale(0.947693,-0.6712) translate(58.7698,-783.126)" width="15" x="1064.784133425996" y="302.4763051659212"/></g>
  <g id="304">
   <use class="kv35" height="30" transform="rotate(180,844.892,279.405) scale(0.947693,-0.6712) translate(46.2408,-700.615)" width="15" x="837.7841334259955" xlink:href="#Disconnector:刀闸_0" y="269.3374162770324" zvalue="567"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454320193538" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454320193538"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,844.892,279.405) scale(0.947693,-0.6712) translate(46.2408,-700.615)" width="15" x="837.7841334259955" y="269.3374162770324"/></g>
  <g id="323">
   <use class="kv35" height="30" transform="rotate(180,987.172,383.567) scale(-0.947693,-0.6712) translate(-2029.22,-959.964)" width="15" x="980.0647612382003" xlink:href="#Disconnector:刀闸_0" y="373.4991439940569" zvalue="1178"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454320324610" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454320324610"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,987.172,383.567) scale(-0.947693,-0.6712) translate(-2029.22,-959.964)" width="15" x="980.0647612382003" y="373.4991439940569"/></g>
  <g id="314">
   <use class="kv10" height="30" transform="rotate(0,986.122,643.689) scale(0.947693,0.6712) translate(54.0358,310.391)" width="15" x="979.0138817028078" xlink:href="#Disconnector:刀闸_0" y="633.6210562102442" zvalue="1188"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454320259074" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454320259074"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,986.122,643.689) scale(0.947693,0.6712) translate(54.0358,310.391)" width="15" x="979.0138817028078" y="633.6210562102442"/></g>
  <g id="107">
   <use class="kv10" height="30" transform="rotate(0,809.631,651.431) scale(0.947693,0.6712) translate(44.2946,314.183)" width="15" x="802.523601539605" xlink:href="#Disconnector:刀闸_0" y="641.3632955836687" zvalue="1204"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454320455682" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454320455682"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,809.631,651.431) scale(0.947693,0.6712) translate(44.2946,314.183)" width="15" x="802.523601539605" y="641.3632955836687"/></g>
  <g id="185">
   <use class="kv10" height="30" transform="rotate(0,776.5,826.891) scale(0.947693,0.6712) translate(42.4659,400.135)" width="15" x="769.3924507585768" xlink:href="#Disconnector:刀闸_0" y="816.8230897637047" zvalue="1225"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454320717826" ObjectName="10kV1号电容器0616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454320717826"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,776.5,826.891) scale(0.947693,0.6712) translate(42.4659,400.135)" width="15" x="769.3924507585768" y="816.8230897637047"/></g>
  <g id="170">
   <use class="kv10" height="30" transform="rotate(180,776.339,721.891) scale(-0.947693,-0.6712) translate(-1595.92,-1802.35)" width="15" x="769.230872376394" xlink:href="#Disconnector:刀闸_0" y="711.8230764122643" zvalue="1231"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454320652290" ObjectName="10kV1号电容器0611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454320652290"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,776.339,721.891) scale(-0.947693,-0.6712) translate(-1595.92,-1802.35)" width="15" x="769.230872376394" y="711.8230764122643"/></g>
  <g id="117">
   <use class="kv35" height="30" transform="rotate(270,1049.34,136.14) scale(-0.897331,-0.448665) translate(-2219.51,-447.842)" width="15" x="1042.608250669651" xlink:href="#Disconnector:刀闸_0" y="129.4095784849349" zvalue="1371"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454321504258" ObjectName="35kV章拉线3619隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454321504258"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1049.34,136.14) scale(-0.897331,-0.448665) translate(-2219.51,-447.842)" width="15" x="1042.608250669651" y="129.4095784849349"/></g>
  <g id="392">
   <use class="kv10" height="30" transform="rotate(0,932.821,827.891) scale(0.947693,0.6712) translate(51.0939,400.625)" width="15" x="925.7134806315099" xlink:href="#Disconnector:刀闸_0" y="817.8230897637047" zvalue="1485"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454321176578" ObjectName="10kV曼拉线0626隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454321176578"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,932.821,827.891) scale(0.947693,0.6712) translate(51.0939,400.625)" width="15" x="925.7134806315099" y="817.8230897637047"/></g>
  <g id="389">
   <use class="kv10" height="30" transform="rotate(180,932.66,722.891) scale(-0.947693,-0.6712) translate(-1917.19,-1804.84)" width="15" x="925.5519022493272" xlink:href="#Disconnector:刀闸_0" y="712.8230764122643" zvalue="1491"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454321111042" ObjectName="10kV曼拉线0621隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454321111042"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,932.66,722.891) scale(-0.947693,-0.6712) translate(-1917.19,-1804.84)" width="15" x="925.5519022493272" y="712.8230764122643"/></g>
  <g id="71">
   <use class="kv35" height="30" transform="rotate(90,1098.65,159.491) scale(1.11111,1.11111) translate(-109.031,-14.2824)" width="15" x="1090.313788651921" xlink:href="#Disconnector:令克_0" y="142.8238574184511" zvalue="1624"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454321569794" ObjectName="35kV1号站用变令克"/>
   <cge:TPSR_Ref TObjectID="6192454321569794"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1098.65,159.491) scale(1.11111,1.11111) translate(-109.031,-14.2824)" width="15" x="1090.313788651921" y="142.8238574184511"/></g>
  <g id="132">
   <use class="kv35" height="30" transform="rotate(0,1351.71,192.164) scale(0.947693,0.6712) translate(74.2142,89.2032)" width="15" x="1344.603492892157" xlink:href="#Disconnector:刀闸_0" y="182.0964562679504" zvalue="1632"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454322290690" ObjectName="35kV拉祥线3626隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454322290690"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1351.71,192.164) scale(0.947693,0.6712) translate(74.2142,89.2032)" width="15" x="1344.603492892157" y="182.0964562679504"/></g>
  <g id="119">
   <use class="kv35" height="30" transform="rotate(180,1351.89,315.433) scale(0.947693,-0.6712) translate(74.2242,-790.319)" width="15" x="1344.784133425996" xlink:href="#Disconnector:刀闸_0" y="305.3651940548102" zvalue="1637"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454322094082" ObjectName="35kV拉祥线3621隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454322094082"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1351.89,315.433) scale(0.947693,-0.6712) translate(74.2242,-790.319)" width="15" x="1344.784133425996" y="305.3651940548102"/></g>
  <g id="95">
   <use class="kv35" height="30" transform="rotate(270,1329.34,139.028) scale(-0.897331,-0.448665) translate(-2811.54,-457.17)" width="15" x="1322.608250669651" xlink:href="#Disconnector:刀闸_0" y="132.2984673738238" zvalue="1649"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454321700866" ObjectName="35kV拉祥线3629隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454321700866"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1329.34,139.028) scale(-0.897331,-0.448665) translate(-2811.54,-457.17)" width="15" x="1322.608250669651" y="132.2984673738238"/></g>
  <g id="120">
   <use class="kv35" height="30" transform="rotate(180,1261.17,383.567) scale(-0.947693,-0.6712) translate(-2592.35,-959.964)" width="15" x="1254.0647612382" xlink:href="#Disconnector:刀闸_0" y="373.4991439940569" zvalue="1672"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454322749442" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454322749442"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1261.17,383.567) scale(-0.947693,-0.6712) translate(-2592.35,-959.964)" width="15" x="1254.0647612382" y="373.4991439940569"/></g>
  <g id="85">
   <use class="kv10" height="30" transform="rotate(0,1260.12,643.689) scale(0.947693,0.6712) translate(69.159,310.391)" width="15" x="1253.013881702808" xlink:href="#Disconnector:刀闸_0" y="633.6210562102442" zvalue="1678"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454322683906" ObjectName="#2主变10kV侧0021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454322683906"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1260.12,643.689) scale(0.947693,0.6712) translate(69.159,310.391)" width="15" x="1253.013881702808" y="633.6210562102442"/></g>
  <g id="153">
   <use class="kv10" height="30" transform="rotate(0,1062.82,827.891) scale(0.947693,0.6712) translate(58.2692,400.625)" width="15" x="1055.71348063151" xlink:href="#Disconnector:刀闸_0" y="817.8230897637047" zvalue="1690"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454323077122" ObjectName="10kV拉影街面线0636隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454323077122"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1062.82,827.891) scale(0.947693,0.6712) translate(58.2692,400.625)" width="15" x="1055.71348063151" y="817.8230897637047"/></g>
  <g id="150">
   <use class="kv10" height="30" transform="rotate(180,1062.66,722.891) scale(-0.947693,-0.6712) translate(-2184.36,-1804.84)" width="15" x="1055.551902249327" xlink:href="#Disconnector:刀闸_0" y="712.8230764122643" zvalue="1696"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454323011586" ObjectName="10kV拉影街面线0631隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454323011586"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1062.66,722.891) scale(-0.947693,-0.6712) translate(-2184.36,-1804.84)" width="15" x="1055.551902249327" y="712.8230764122643"/></g>
  <g id="169">
   <use class="kv10" height="30" transform="rotate(0,1192.82,827.891) scale(0.947693,0.6712) translate(65.4444,400.625)" width="15" x="1185.71348063151" xlink:href="#Disconnector:刀闸_0" y="817.8230897637047" zvalue="1703"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454323273730" ObjectName="10kV迈扎央线0646隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454323273730"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1192.82,827.891) scale(0.947693,0.6712) translate(65.4444,400.625)" width="15" x="1185.71348063151" y="817.8230897637047"/></g>
  <g id="165">
   <use class="kv10" height="30" transform="rotate(180,1192.66,722.891) scale(-0.947693,-0.6712) translate(-2451.54,-1804.84)" width="15" x="1185.551902249327" xlink:href="#Disconnector:刀闸_0" y="712.8230764122643" zvalue="1709"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454323208194" ObjectName="10kV迈扎央线0641隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454323208194"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1192.66,722.891) scale(-0.947693,-0.6712) translate(-2451.54,-1804.84)" width="15" x="1185.551902249327" y="712.8230764122643"/></g>
  <g id="186">
   <use class="kv10" height="30" transform="rotate(0,1322.82,827.891) scale(0.947693,0.6712) translate(72.6197,400.625)" width="15" x="1315.71348063151" xlink:href="#Disconnector:刀闸_0" y="817.8230897637047" zvalue="1716"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454323470338" ObjectName="10kV洋人街老迈扎央线0656隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454323470338"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1322.82,827.891) scale(0.947693,0.6712) translate(72.6197,400.625)" width="15" x="1315.71348063151" y="817.8230897637047"/></g>
  <g id="181">
   <use class="kv10" height="30" transform="rotate(180,1322.66,722.891) scale(-0.947693,-0.6712) translate(-2718.71,-1804.84)" width="15" x="1315.551902249327" xlink:href="#Disconnector:刀闸_0" y="712.8230764122643" zvalue="1722"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454323404802" ObjectName="10kV洋人街老迈扎央线0651隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454323404802"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1322.66,722.891) scale(-0.947693,-0.6712) translate(-2718.71,-1804.84)" width="15" x="1315.551902249327" y="712.8230764122643"/></g>
  <g id="217">
   <use class="kv10" height="30" transform="rotate(0,1452.5,826.891) scale(0.947693,0.6712) translate(79.7772,400.135)" width="15" x="1445.392450758577" xlink:href="#Disconnector:刀闸_0" y="816.8230897637047" zvalue="1729"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454323863554" ObjectName="10kV5S备用线0666隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454323863554"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1452.5,826.891) scale(0.947693,0.6712) translate(79.7772,400.135)" width="15" x="1445.392450758577" y="816.8230897637047"/></g>
  <g id="214">
   <use class="kv10" height="30" transform="rotate(180,1452.34,721.891) scale(-0.947693,-0.6712) translate(-2985.23,-1802.35)" width="15" x="1445.230872376394" xlink:href="#Disconnector:刀闸_0" y="711.8230764122643" zvalue="1734"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454323798018" ObjectName="10kV5S备用线0661隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454323798018"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1452.34,721.891) scale(-0.947693,-0.6712) translate(-2985.23,-1802.35)" width="15" x="1445.230872376394" y="711.8230764122643"/></g>
  <g id="251">
   <use class="kv10" height="30" transform="rotate(0,1582.5,826.891) scale(0.947693,0.6712) translate(86.9524,400.135)" width="15" x="1575.392450758577" xlink:href="#Disconnector:刀闸_0" y="816.8230897637047" zvalue="1753"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454324387842" ObjectName="10kV6S备用线0676隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454324387842"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1582.5,826.891) scale(0.947693,0.6712) translate(86.9524,400.135)" width="15" x="1575.392450758577" y="816.8230897637047"/></g>
  <g id="239">
   <use class="kv10" height="30" transform="rotate(180,1582.34,721.891) scale(-0.947693,-0.6712) translate(-3252.41,-1802.35)" width="15" x="1575.230872376394" xlink:href="#Disconnector:刀闸_0" y="711.8230764122643" zvalue="1758"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454324322306" ObjectName="10kV6S备用线0671隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454324322306"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1582.34,721.891) scale(-0.947693,-0.6712) translate(-3252.41,-1802.35)" width="15" x="1575.230872376394" y="711.8230764122643"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="67">
   <use class="kv35" height="20" transform="rotate(270,1054.61,159.336) scale(-1.24619,-1.0068) translate(-1899.65,-317.527)" width="10" x="1048.379338252184" xlink:href="#GroundDisconnector:地刀_0" y="149.2676158048624" zvalue="303"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454319603714" ObjectName="35kV章拉线36167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454319603714"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1054.61,159.336) scale(-1.24619,-1.0068) translate(-1899.65,-317.527)" width="10" x="1048.379338252184" y="149.2676158048624"/></g>
  <g id="33">
   <use class="kv35" height="20" transform="rotate(90,1101.5,282.002) scale(1.24619,-1.0068) translate(-216.372,-562.032)" width="10" x="1095.268227141073" xlink:href="#GroundDisconnector:地刀_0" y="271.9342824715293" zvalue="315"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454319865858" ObjectName="35kV章拉线36117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454319865858"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1101.5,282.002) scale(1.24619,-1.0068) translate(-216.372,-562.032)" width="10" x="1095.268227141073" y="271.9342824715293"/></g>
  <g id="103">
   <use class="kv35" height="20" transform="rotate(90,876.373,244.682) scale(1.24619,-1.0068) translate(-171.898,-487.642)" width="10" x="870.1419645148105" xlink:href="#GroundDisconnector:地刀_0" y="234.6135754008221" zvalue="353"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454319996930" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454319996930"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,876.373,244.682) scale(1.24619,-1.0068) translate(-171.898,-487.642)" width="10" x="870.1419645148105" y="234.6135754008221"/></g>
  <g id="294">
   <use class="kv35" height="20" transform="rotate(90,1101.5,214.002) scale(1.24619,-1.0068) translate(-216.372,-426.491)" width="10" x="1095.268227141073" xlink:href="#GroundDisconnector:地刀_0" y="203.9342824715292" zvalue="553"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454320128002" ObjectName="35kV章拉线36160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454320128002"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1101.5,214.002) scale(1.24619,-1.0068) translate(-216.372,-426.491)" width="10" x="1095.268227141073" y="203.9342824715292"/></g>
  <g id="131">
   <use class="kv10" height="20" transform="rotate(90,822.901,802.731) scale(1.24619,-1.0068) translate(-161.335,-1599.97)" width="10" x="816.6705537030783" xlink:href="#GroundDisconnector:地刀_0" y="792.6629272741211" zvalue="1240"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454320586754" ObjectName="10kV1号电容器06160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454320586754"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,822.901,802.731) scale(1.24619,-1.0068) translate(-161.335,-1599.97)" width="10" x="816.6705537030783" y="792.6629272741211"/></g>
  <g id="110">
   <use class="kv10" height="20" transform="rotate(90,821.346,858.731) scale(1.24619,-1.0068) translate(-161.028,-1711.59)" width="10" x="815.1148477652921" xlink:href="#GroundDisconnector:地刀_0" y="848.6629272741211" zvalue="1443"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454320914434" ObjectName="10kV1号电容器06167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454320914434"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,821.346,858.731) scale(1.24619,-1.0068) translate(-161.028,-1711.59)" width="10" x="815.1148477652921" y="848.6629272741211"/></g>
  <g id="468">
   <use class="kv10" height="20" transform="rotate(90,811.39,745.509) scale(1.24619,-1.0068) translate(-159.061,-1485.91)" width="10" x="805.15880252485" xlink:href="#GroundDisconnector:地刀_0" y="735.4407050518987" zvalue="1566"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454322880514" ObjectName="10kV1号电容器06117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454322880514"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,811.39,745.509) scale(1.24619,-1.0068) translate(-159.061,-1485.91)" width="10" x="805.15880252485" y="735.4407050518987"/></g>
  <g id="22">
   <use class="kv35" height="20" transform="rotate(270,953.749,406.38) scale(-1.24619,-1.0068) translate(-1717.85,-809.948)" width="10" x="947.5182271410732" xlink:href="#GroundDisconnector:地刀_0" y="396.312282682325" zvalue="1584"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454321307650" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454321307650"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,953.749,406.38) scale(-1.24619,-1.0068) translate(-1717.85,-809.948)" width="10" x="947.5182271410732" y="396.312282682325"/></g>
  <g id="129">
   <use class="kv35" height="20" transform="rotate(270,1334.61,162.225) scale(-1.24619,-1.0068) translate(-2404.33,-323.285)" width="10" x="1328.379338252184" xlink:href="#GroundDisconnector:地刀_0" y="152.1565046937513" zvalue="1634"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454322225154" ObjectName="35kV拉祥线36267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454322225154"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1334.61,162.225) scale(-1.24619,-1.0068) translate(-2404.33,-323.285)" width="10" x="1328.379338252184" y="152.1565046937513"/></g>
  <g id="111">
   <use class="kv35" height="20" transform="rotate(90,1381.5,284.891) scale(1.24619,-1.0068) translate(-271.687,-567.79)" width="10" x="1375.268227141073" xlink:href="#GroundDisconnector:地刀_0" y="274.8231713604181" zvalue="1643"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454321963010" ObjectName="35kV拉祥线36217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454321963010"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1381.5,284.891) scale(1.24619,-1.0068) translate(-271.687,-567.79)" width="10" x="1375.268227141073" y="274.8231713604181"/></g>
  <g id="98">
   <use class="kv35" height="20" transform="rotate(90,1381.5,216.891) scale(1.24619,-1.0068) translate(-271.687,-432.249)" width="10" x="1375.268227141073" xlink:href="#GroundDisconnector:地刀_0" y="206.823171360418" zvalue="1646"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454321831938" ObjectName="35kV拉祥线36260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454321831938"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1381.5,216.891) scale(1.24619,-1.0068) translate(-271.687,-432.249)" width="10" x="1375.268227141073" y="206.823171360418"/></g>
  <g id="188">
   <use class="kv35" height="20" transform="rotate(90,869.484,315.793) scale(1.24619,-1.0068) translate(-170.537,-629.384)" width="10" x="863.2530756259215" xlink:href="#GroundDisconnector:地刀_0" y="305.7246865119333" zvalue="1662"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454322487298" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454322487298"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,869.484,315.793) scale(1.24619,-1.0068) translate(-170.537,-629.384)" width="10" x="863.2530756259215" y="305.7246865119333"/></g>
  <g id="41">
   <use class="kv35" height="20" transform="rotate(270,1227.75,406.38) scale(-1.24619,-1.0068) translate(-2211.72,-809.948)" width="10" x="1221.518227141073" xlink:href="#GroundDisconnector:地刀_0" y="396.312282682325" zvalue="1684"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454322618370" ObjectName="#2主变35kV侧30217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454322618370"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1227.75,406.38) scale(-1.24619,-1.0068) translate(-2211.72,-809.948)" width="10" x="1221.518227141073" y="396.312282682325"/></g>
  <g id="205">
   <use class="kv10" height="20" transform="rotate(90,1497.35,858.731) scale(1.24619,-1.0068) translate(-294.573,-1711.59)" width="10" x="1491.114847765292" xlink:href="#GroundDisconnector:地刀_0" y="848.6629272741211" zvalue="1744"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454323732482" ObjectName="10kV5S备用线06667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454323732482"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1497.35,858.731) scale(1.24619,-1.0068) translate(-294.573,-1711.59)" width="10" x="1491.114847765292" y="848.6629272741211"/></g>
  <g id="202">
   <use class="kv10" height="20" transform="rotate(90,1487.39,745.509) scale(1.24619,-1.0068) translate(-292.606,-1485.91)" width="10" x="1481.15880252485" xlink:href="#GroundDisconnector:地刀_0" y="735.4407050518987" zvalue="1747"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454323601410" ObjectName="10kV5S备用线06617接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454323601410"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1487.39,745.509) scale(1.24619,-1.0068) translate(-292.606,-1485.91)" width="10" x="1481.15880252485" y="735.4407050518987"/></g>
  <g id="235">
   <use class="kv10" height="20" transform="rotate(90,1627.35,858.731) scale(1.24619,-1.0068) translate(-320.255,-1711.59)" width="10" x="1621.114847765292" xlink:href="#GroundDisconnector:地刀_0" y="848.6629272741211" zvalue="1763"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454324256770" ObjectName="10kV6S备用线06767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454324256770"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1627.35,858.731) scale(1.24619,-1.0068) translate(-320.255,-1711.59)" width="10" x="1621.114847765292" y="848.6629272741211"/></g>
  <g id="233">
   <use class="kv10" height="20" transform="rotate(90,1617.39,745.509) scale(1.24619,-1.0068) translate(-318.288,-1485.91)" width="10" x="1611.15880252485" xlink:href="#GroundDisconnector:地刀_0" y="735.4407050518987" zvalue="1766"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454324125698" ObjectName="10kV6S备用线06717接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454324125698"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1617.39,745.509) scale(1.24619,-1.0068) translate(-318.288,-1485.91)" width="10" x="1611.15880252485" y="735.4407050518987"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="21">
   <path class="kv35" d="M 1071.65 237.75 L 1071.65 195.17" stroke-width="1" zvalue="305"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="68@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1071.65 237.75 L 1071.65 195.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv35" d="M 1071.79 175.54 L 1071.79 119.3" stroke-width="1" zvalue="308"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="70@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1071.79 175.54 L 1071.79 119.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv35" d="M 1071.81 263.64 L 1071.81 302.81" stroke-width="1" zvalue="310"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@1" LinkObjectIDznd="20@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1071.81 263.64 L 1071.81 302.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv35" d="M 1071.83 322.44 L 1071.83 345.59" stroke-width="1" zvalue="313"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@1" LinkObjectIDznd="128@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1071.83 322.44 L 1071.83 345.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv35" d="M 1091.68 282.06 L 1071.81 282.06" stroke-width="1" zvalue="317"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="17" MaxPinNum="2"/>
   </metadata>
  <path d="M 1091.68 282.06 L 1071.81 282.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv35" d="M 866.56 244.74 L 844.81 244.74" stroke-width="1" zvalue="355"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="305" MaxPinNum="2"/>
   </metadata>
  <path d="M 866.56 244.74 L 844.81 244.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="293">
   <path class="kv35" d="M 1091.68 214.06 L 1071.65 214.06" stroke-width="1" zvalue="555"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="294@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 1091.68 214.06 L 1071.65 214.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="305">
   <path class="kv35" d="M 844.81 198.07 L 844.81 269.67" stroke-width="1" zvalue="568"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@0" LinkObjectIDznd="304@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 844.81 198.07 L 844.81 269.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="322">
   <path class="kv35" d="M 987.26 373.83 L 987.26 345.59" stroke-width="1" zvalue="1180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="323@0" LinkObjectIDznd="128@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 987.26 373.83 L 987.26 345.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="321">
   <path class="kv35" d="M 987.26 431.22 L 987.23 393.46" stroke-width="1" zvalue="1181"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="324@1" LinkObjectIDznd="323@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 987.26 431.22 L 987.23 393.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="313">
   <path class="kv10" d="M 986.2 633.95 L 986.2 613.41" stroke-width="1" zvalue="1190"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="314@0" LinkObjectIDznd="325@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 986.2 633.95 L 986.2 613.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="kv10" d="M 986.18 653.58 L 986.18 682" stroke-width="1" zvalue="1191"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="314@1" LinkObjectIDznd="330@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 986.18 653.58 L 986.18 682" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv10" d="M 809.71 641.7 L 809.71 617.78" stroke-width="1" zvalue="1206"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="100@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.71 641.7 L 809.71 617.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv10" d="M 809.69 661.33 L 809.69 682" stroke-width="1" zvalue="1207"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@1" LinkObjectIDznd="330@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.69 661.33 L 809.69 682" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv10" d="M 776.55 784.66 L 776.58 817.16" stroke-width="1" zvalue="1230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@0" LinkObjectIDznd="185@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 776.55 784.66 L 776.58 817.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv10" d="M 776.4 731.79 L 776.4 758.77" stroke-width="1" zvalue="1233"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@1" LinkObjectIDznd="176@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 776.4 731.79 L 776.4 758.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="kv10" d="M 776.42 712.16 L 776.42 682" stroke-width="1" zvalue="1234"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="330@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 776.42 712.16 L 776.42 682" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv10" d="M 776.58 874.85 L 776.56 836.79" stroke-width="1" zvalue="1238"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="185@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 776.58 874.85 L 776.56 836.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="kv10" d="M 986.39 587.52 L 986.39 554.99" stroke-width="1" zvalue="1300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="325@1" LinkObjectIDznd="318@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 986.39 587.52 L 986.39 554.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="243">
   <path class="kv35" d="M 987.41 483.38 L 987.41 457.11" stroke-width="1" zvalue="1301"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="318@0" LinkObjectIDznd="324@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 987.41 483.38 L 987.41 457.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv10" d="M 813.09 802.79 L 776.57 802.79" stroke-width="1" zvalue="1441"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="171" MaxPinNum="2"/>
   </metadata>
  <path d="M 813.09 802.79 L 776.57 802.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv10" d="M 811.53 858.79 L 776.57 858.79" stroke-width="1" zvalue="1445"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="133" MaxPinNum="2"/>
   </metadata>
  <path d="M 811.53 858.79 L 776.57 858.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv35" d="M 844.83 289.3 L 844.83 345.59" stroke-width="1" zvalue="1446"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="304@1" LinkObjectIDznd="128@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 844.83 289.3 L 844.83 345.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="390">
   <path class="kv10" d="M 932.87 781.66 L 932.9 818.16" stroke-width="1" zvalue="1490"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="391@0" LinkObjectIDznd="392@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 932.87 781.66 L 932.9 818.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="388">
   <path class="kv10" d="M 932.72 732.79 L 932.72 755.77" stroke-width="1" zvalue="1493"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="389@1" LinkObjectIDznd="391@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 932.72 732.79 L 932.72 755.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="387">
   <path class="kv10" d="M 932.74 713.16 L 932.74 682" stroke-width="1" zvalue="1494"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="389@0" LinkObjectIDznd="330@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 932.74 713.16 L 932.74 682" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="386">
   <path class="kv10" d="M 932.9 877 L 932.88 837.79" stroke-width="1" zvalue="1495"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="381@0" LinkObjectIDznd="392@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 932.9 877 L 932.88 837.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv35" d="M 1093.1 136.22 L 1071.79 136.22" stroke-width="1" zvalue="1618"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 1093.1 136.22 L 1071.79 136.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv35" d="M 1055.85 136.22 L 1071.79 136.22" stroke-width="1" zvalue="1619"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 1055.85 136.22 L 1071.79 136.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv35" d="M 1042.72 136.19 L 1037.43 136.19" stroke-width="1" zvalue="1620"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@1" LinkObjectIDznd="130@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1042.72 136.19 L 1037.43 136.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv35" d="M 1064.43 159.4 L 1071.79 159.4" stroke-width="1" zvalue="1622"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 1064.43 159.4 L 1071.79 159.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv35" d="M 1126.37 159.1 L 1113.37 159.1" stroke-width="1" zvalue="1625"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@0" LinkObjectIDznd="71@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.37 159.1 L 1113.37 159.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv35" d="M 1085.04 159.4 L 1071.79 159.4" stroke-width="1" zvalue="1626"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@1" LinkObjectIDznd="65" MaxPinNum="2"/>
   </metadata>
  <path d="M 1085.04 159.4 L 1071.79 159.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv35" d="M 1351.65 244.64 L 1351.65 202.06" stroke-width="1" zvalue="1636"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="132@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1351.65 244.64 L 1351.65 202.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv35" d="M 1351.79 182.43 L 1351.79 122.19" stroke-width="1" zvalue="1639"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="155@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1351.79 182.43 L 1351.79 122.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="kv35" d="M 1351.81 270.53 L 1351.81 305.7" stroke-width="1" zvalue="1640"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@1" LinkObjectIDznd="119@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1351.81 270.53 L 1351.81 305.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv35" d="M 1351.83 325.33 L 1351.83 345.59" stroke-width="1" zvalue="1642"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@1" LinkObjectIDznd="128@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1351.83 325.33 L 1351.83 345.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv35" d="M 1371.68 284.95 L 1351.81 284.95" stroke-width="1" zvalue="1645"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="116" MaxPinNum="2"/>
   </metadata>
  <path d="M 1371.68 284.95 L 1351.81 284.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv35" d="M 1371.68 216.95 L 1351.65 216.95" stroke-width="1" zvalue="1648"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="127" MaxPinNum="2"/>
   </metadata>
  <path d="M 1371.68 216.95 L 1351.65 216.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv35" d="M 1373.1 139.11 L 1351.79 139.11" stroke-width="1" zvalue="1653"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="118" MaxPinNum="2"/>
   </metadata>
  <path d="M 1373.1 139.11 L 1351.79 139.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv35" d="M 1335.85 139.11 L 1351.79 139.11" stroke-width="1" zvalue="1654"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@0" LinkObjectIDznd="118" MaxPinNum="2"/>
   </metadata>
  <path d="M 1335.85 139.11 L 1351.79 139.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv35" d="M 1322.72 139.08 L 1317.43 139.08" stroke-width="1" zvalue="1655"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@1" LinkObjectIDznd="93@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.72 139.08 L 1317.43 139.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv35" d="M 1344.43 162.29 L 1351.79 162.29" stroke-width="1" zvalue="1656"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="118" MaxPinNum="2"/>
   </metadata>
  <path d="M 1344.43 162.29 L 1351.79 162.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv35" d="M 859.67 315.86 L 844.83 315.86" stroke-width="1" zvalue="1663"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@0" LinkObjectIDznd="113" MaxPinNum="2"/>
   </metadata>
  <path d="M 859.67 315.86 L 844.83 315.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv35" d="M 963.57 406.44 L 987.24 406.44" stroke-width="1" zvalue="1664"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@0" LinkObjectIDznd="321" MaxPinNum="2"/>
   </metadata>
  <path d="M 963.57 406.44 L 987.24 406.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv35" d="M 1261.26 373.83 L 1261.26 345.59" stroke-width="1" zvalue="1674"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="128@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1261.26 373.83 L 1261.26 345.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv35" d="M 1261.26 431.22 L 1261.23 393.46" stroke-width="1" zvalue="1675"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@1" LinkObjectIDznd="120@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1261.26 431.22 L 1261.23 393.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv10" d="M 1260.2 633.95 L 1260.2 613.41" stroke-width="1" zvalue="1680"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@0" LinkObjectIDznd="136@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1260.2 633.95 L 1260.2 613.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv10" d="M 1260.18 653.58 L 1260.18 682" stroke-width="1" zvalue="1681"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@1" LinkObjectIDznd="330@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1260.18 653.58 L 1260.18 682" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv10" d="M 1260.39 587.52 L 1260.39 554.99" stroke-width="1" zvalue="1682"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136@1" LinkObjectIDznd="86@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1260.39 587.52 L 1260.39 554.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv35" d="M 1261.41 483.38 L 1261.41 457.11" stroke-width="1" zvalue="1683"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="126@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1261.41 483.38 L 1261.41 457.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv35" d="M 1237.57 406.44 L 1261.24 406.44" stroke-width="1" zvalue="1687"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="87" MaxPinNum="2"/>
   </metadata>
  <path d="M 1237.57 406.44 L 1261.24 406.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv10" d="M 801.57 745.57 L 776.4 745.57" stroke-width="1" zvalue="1688"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="468@0" LinkObjectIDznd="168" MaxPinNum="2"/>
   </metadata>
  <path d="M 801.57 745.57 L 776.4 745.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv10" d="M 1062.87 781.66 L 1062.9 818.16" stroke-width="1" zvalue="1695"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="153@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.87 781.66 L 1062.9 818.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv10" d="M 1062.72 732.79 L 1062.72 755.77" stroke-width="1" zvalue="1698"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@1" LinkObjectIDznd="152@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.72 732.79 L 1062.72 755.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv10" d="M 1062.74 713.16 L 1062.74 682" stroke-width="1" zvalue="1699"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="330@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.74 713.16 L 1062.74 682" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv10" d="M 1062.9 877 L 1062.88 837.79" stroke-width="1" zvalue="1700"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="153@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.9 877 L 1062.88 837.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv10" d="M 1192.87 781.66 L 1192.9 818.16" stroke-width="1" zvalue="1708"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="167@0" LinkObjectIDznd="169@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1192.87 781.66 L 1192.9 818.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv10" d="M 1192.72 732.79 L 1192.72 755.77" stroke-width="1" zvalue="1711"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@1" LinkObjectIDznd="167@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1192.72 732.79 L 1192.72 755.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv10" d="M 1192.74 713.16 L 1192.74 682" stroke-width="1" zvalue="1712"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="330@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1192.74 713.16 L 1192.74 682" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv10" d="M 1192.9 877 L 1192.88 837.79" stroke-width="1" zvalue="1713"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="169@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1192.9 877 L 1192.88 837.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv10" d="M 1322.87 781.66 L 1322.9 818.16" stroke-width="1" zvalue="1721"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="186@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.87 781.66 L 1322.9 818.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv10" d="M 1322.72 732.79 L 1322.72 755.77" stroke-width="1" zvalue="1724"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@1" LinkObjectIDznd="184@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.72 732.79 L 1322.72 755.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv10" d="M 1322.74 713.16 L 1322.74 682" stroke-width="1" zvalue="1725"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="330@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.74 713.16 L 1322.74 682" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv10" d="M 1322.9 877 L 1322.88 837.79" stroke-width="1" zvalue="1726"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@0" LinkObjectIDznd="186@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.9 877 L 1322.88 837.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv10" d="M 1452.55 784.66 L 1452.58 817.16" stroke-width="1" zvalue="1733"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="216@0" LinkObjectIDznd="217@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1452.55 784.66 L 1452.58 817.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv10" d="M 1452.4 731.79 L 1452.4 758.77" stroke-width="1" zvalue="1736"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@1" LinkObjectIDznd="216@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1452.4 731.79 L 1452.4 758.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv10" d="M 1452.42 712.16 L 1452.42 682" stroke-width="1" zvalue="1737"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@0" LinkObjectIDznd="330@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1452.42 712.16 L 1452.42 682" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv10" d="M 1452.56 874.78 L 1452.56 836.79" stroke-width="1" zvalue="1740"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="217@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1452.56 874.78 L 1452.56 836.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="203">
   <path class="kv10" d="M 1487.53 858.79 L 1452.56 858.79" stroke-width="1" zvalue="1746"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="209" MaxPinNum="2"/>
   </metadata>
  <path d="M 1487.53 858.79 L 1452.56 858.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv10" d="M 1477.57 745.57 L 1452.4 745.57" stroke-width="1" zvalue="1749"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="213" MaxPinNum="2"/>
   </metadata>
  <path d="M 1477.57 745.57 L 1452.4 745.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv10" d="M 1582.55 784.66 L 1582.58 817.16" stroke-width="1" zvalue="1757"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="244@0" LinkObjectIDznd="251@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1582.55 784.66 L 1582.58 817.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="238">
   <path class="kv10" d="M 1582.4 731.79 L 1582.4 758.77" stroke-width="1" zvalue="1760"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="239@1" LinkObjectIDznd="244@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1582.4 731.79 L 1582.4 758.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="237">
   <path class="kv10" d="M 1582.42 712.16 L 1582.42 682" stroke-width="1" zvalue="1761"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="239@0" LinkObjectIDznd="330@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1582.42 712.16 L 1582.42 682" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="kv10" d="M 1582.56 874.78 L 1582.56 836.79" stroke-width="1" zvalue="1762"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@0" LinkObjectIDznd="251@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1582.56 874.78 L 1582.56 836.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="234">
   <path class="kv10" d="M 1617.53 858.79 L 1582.56 858.79" stroke-width="1" zvalue="1765"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@0" LinkObjectIDznd="236" MaxPinNum="2"/>
   </metadata>
  <path d="M 1617.53 858.79 L 1582.56 858.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="kv10" d="M 1607.57 745.57 L 1582.4 745.57" stroke-width="1" zvalue="1768"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@0" LinkObjectIDznd="238" MaxPinNum="2"/>
   </metadata>
  <path d="M 1607.57 745.57 L 1582.4 745.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="318">
   <g id="3180">
    <use class="kv35" height="60" transform="rotate(0,987.363,519.111) scale(1.20876,1.21296) translate(-166.345,-84.7528)" width="40" x="963.1900000000001" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="482.72" zvalue="1184"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874578788354" ObjectName="35"/>
    </metadata>
   </g>
   <g id="3181">
    <use class="kv10" height="60" transform="rotate(0,987.363,519.111) scale(1.20876,1.21296) translate(-166.345,-84.7528)" width="40" x="963.1900000000001" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="482.72" zvalue="1184"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874578853890" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399528349698" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399528349698"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,987.363,519.111) scale(1.20876,1.21296) translate(-166.345,-84.7528)" width="40" x="963.1900000000001" y="482.72"/></g>
  <g id="86">
   <g id="860">
    <use class="kv35" height="60" transform="rotate(0,1261.36,519.111) scale(1.20876,1.21296) translate(-213.666,-84.7528)" width="40" x="1237.19" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="482.72" zvalue="1676"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874578919426" ObjectName="35"/>
    </metadata>
   </g>
   <g id="861">
    <use class="kv10" height="60" transform="rotate(0,1261.36,519.111) scale(1.20876,1.21296) translate(-213.666,-84.7528)" width="40" x="1237.19" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="482.72" zvalue="1676"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874578984962" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399528415234" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399528415234"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1261.36,519.111) scale(1.20876,1.21296) translate(-213.666,-84.7528)" width="40" x="1237.19" y="482.72"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="158">
   <use class="kv10" height="40" transform="rotate(0,776.583,903.09) scale(-1.81597,1.84588) translate(-1194.43,-396.927)" width="24" x="754.7916666666669" xlink:href="#Compensator:10kV电容器_0" y="866.1728099342654" zvalue="1235"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454320979970" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192454320979970"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,776.583,903.09) scale(-1.81597,1.84588) translate(-1194.43,-396.927)" width="24" x="754.7916666666669" y="866.1728099342654"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="282">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="282" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,134.353,495.893) scale(1,1) translate(1.21006e-14,-1.08556e-13)" writing-mode="lr" x="134.46" xml:space="preserve" y="500.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134844411906" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="281">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="281" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,134.353,518.893) scale(1,1) translate(1.21006e-14,0)" writing-mode="lr" x="134.46" xml:space="preserve" y="523.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134844477442" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="280">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="280" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,134.353,541.893) scale(1,1) translate(1.21006e-14,5.9385e-14)" writing-mode="lr" x="134.46" xml:space="preserve" y="546.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134844542978" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="279">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="279" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,134.353,470.393) scale(1,1) translate(1.21006e-14,-1.02894e-13)" writing-mode="lr" x="134.46" xml:space="preserve" y="475.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134844674050" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="278">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="278" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,146.714,201.754) scale(1,1) translate(0,0)" writing-mode="lr" x="146.87" xml:space="preserve" y="208.03" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134844805122" ObjectName="F"/>
   </metadata>
  </g>
  <g id="277">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="277" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,261.756,495.893) scale(1,1) translate(2.62451e-14,1.08556e-13)" writing-mode="lr" x="261.87" xml:space="preserve" y="500.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134849589250" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="276">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="276" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,261.756,518.893) scale(1,1) translate(2.62451e-14,0)" writing-mode="lr" x="261.87" xml:space="preserve" y="523.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134849654786" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="275">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="275" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,261.756,541.893) scale(1,1) translate(2.62451e-14,5.9385e-14)" writing-mode="lr" x="261.87" xml:space="preserve" y="546.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134849720322" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="274">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="274" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,261.756,470.393) scale(1,1) translate(2.62451e-14,-1.02894e-13)" writing-mode="lr" x="261.87" xml:space="preserve" y="475.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134849851394" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="273">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="273" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,145.464,225.393) scale(1,1) translate(0,0)" writing-mode="lr" x="145.62" xml:space="preserve" y="231.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134849982466" ObjectName="F"/>
   </metadata>
  </g>
  <g id="250">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="250" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,146.714,248.837) scale(1,1) translate(0,0)" writing-mode="lr" x="146.87" xml:space="preserve" y="255.11" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134846967810" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="248">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="248" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,323.714,247.837) scale(1,1) translate(0,0)" writing-mode="lr" x="323.87" xml:space="preserve" y="254.11" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134855749634" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="246" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,134.353,568.893) scale(1,1) translate(1.21006e-14,-1.24765e-13)" writing-mode="lr" x="134.46" xml:space="preserve" y="573.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134844870658" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,261.756,568.893) scale(1,1) translate(2.62451e-14,-1.24765e-13)" writing-mode="lr" x="261.87" xml:space="preserve" y="573.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134850048002" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="241" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,146.714,177.143) scale(1,1) translate(0,0)" writing-mode="lr" x="146.87" xml:space="preserve" y="183.47" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134865580034" ObjectName=""/>
   </metadata>
  </g>
  <g id="229">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="229" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,323.714,177.143) scale(1,1) translate(0,0)" writing-mode="lr" x="323.87" xml:space="preserve" y="183.49" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134865645570" ObjectName=""/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1071.63,8.18776) scale(1,1) translate(1.13979e-13,0)" writing-mode="lr" x="1071.82" xml:space="preserve" y="13.1" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134845460482" ObjectName="P"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="2" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1351.63,12.0767) scale(1,1) translate(0,0)" writing-mode="lr" x="1351.82" xml:space="preserve" y="16.99" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134854242306" ObjectName="P"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="3" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1071.63,31.1878) scale(1,1) translate(1.13979e-13,0)" writing-mode="lr" x="1071.82" xml:space="preserve" y="36.1" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134845526018" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="5" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1351.63,35.0767) scale(1,1) translate(0,0)" writing-mode="lr" x="1351.82" xml:space="preserve" y="39.99" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134854307842" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="6" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1071.63,54.1878) scale(1,1) translate(1.13979e-13,0)" writing-mode="lr" x="1071.82" xml:space="preserve" y="59.1" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134845591554" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="7" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1352.74,55.1878) scale(1,1) translate(0,0)" writing-mode="lr" x="1352.93" xml:space="preserve" y="60.1" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134854373378" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="25">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="25" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,932.904,934.389) scale(1,1) translate(0,0)" writing-mode="lr" x="933.1" xml:space="preserve" y="939.3" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134852276226" ObjectName="P"/>
   </metadata>
  </g>
  <g id="26">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="26" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1062.9,934.389) scale(1,1) translate(1.1301e-13,0)" writing-mode="lr" x="1063.1" xml:space="preserve" y="939.3" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134858371074" ObjectName="P"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="27" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1192.9,934.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1193.1" xml:space="preserve" y="939.3" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134859812866" ObjectName="P"/>
   </metadata>
  </g>
  <g id="28">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="28" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1322.9,934.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1323.1" xml:space="preserve" y="939.3" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134861254658" ObjectName="P"/>
   </metadata>
  </g>
  <g id="49">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="49" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1452.56,933.278) scale(1,1) translate(-9.37622e-13,0)" writing-mode="lr" x="1452.75" xml:space="preserve" y="938.1900000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134863220738" ObjectName="P"/>
   </metadata>
  </g>
  <g id="51">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="51" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1582.56,933.278) scale(1,1) translate(0,0)" writing-mode="lr" x="1582.75" xml:space="preserve" y="938.1900000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134864138242" ObjectName="P"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="52" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,932.904,957.389) scale(1,1) translate(0,0)" writing-mode="lr" x="933.1" xml:space="preserve" y="962.3" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134852341762" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="54" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1062.9,957.389) scale(1,1) translate(1.1301e-13,0)" writing-mode="lr" x="1063.1" xml:space="preserve" y="962.3" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134858436610" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="55" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1192.9,957.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1193.1" xml:space="preserve" y="962.3" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134859878402" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="56" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1322.9,957.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1323.1" xml:space="preserve" y="962.3" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134861320194" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="57" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1452.56,956.278) scale(1,1) translate(-9.37622e-13,0)" writing-mode="lr" x="1452.75" xml:space="preserve" y="961.1900000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134863286274" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="58">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="58" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1582.56,956.278) scale(1,1) translate(0,0)" writing-mode="lr" x="1582.75" xml:space="preserve" y="961.1900000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134864203778" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="59">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="59" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,932.904,980.389) scale(1,1) translate(0,0)" writing-mode="lr" x="933.1" xml:space="preserve" y="985.3" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134852407298" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="134" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1062.9,980.389) scale(1,1) translate(1.1301e-13,0)" writing-mode="lr" x="1063.1" xml:space="preserve" y="985.3" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134858502146" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="196">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="196" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1192.9,980.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1193.1" xml:space="preserve" y="985.3" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134859943938" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="197">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="197" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1322.9,980.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1323.1" xml:space="preserve" y="985.3" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134861385730" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="207">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="207" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1452.56,979.278) scale(1,1) translate(-9.37622e-13,0)" writing-mode="lr" x="1452.75" xml:space="preserve" y="984.1900000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134863351810" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="208">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="208" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1582.56,979.278) scale(1,1) translate(0,0)" writing-mode="lr" x="1582.75" xml:space="preserve" y="984.1900000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134864269314" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="227">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="227" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,936.904,1008) scale(1,1) translate(0,0)" writing-mode="lr" x="937.1" xml:space="preserve" y="1012.91" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134852800514" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="254">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="254" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1066.9,1008) scale(1,1) translate(1.13454e-13,0)" writing-mode="lr" x="1067.1" xml:space="preserve" y="1012.91" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134858895362" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="255">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="255" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1196.9,1008) scale(1,1) translate(0,0)" writing-mode="lr" x="1197.1" xml:space="preserve" y="1012.91" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134860337154" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="256" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1326.9,1008) scale(1,1) translate(0,0)" writing-mode="lr" x="1327.1" xml:space="preserve" y="1012.91" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134861778946" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="257" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1456.56,1005.78) scale(1,1) translate(9.40287e-13,0)" writing-mode="lr" x="1456.75" xml:space="preserve" y="1010.69" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134863745026" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="258">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="258" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1586.56,1005.78) scale(1,1) translate(0,0)" writing-mode="lr" x="1586.75" xml:space="preserve" y="1010.69" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134864662530" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="259">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="259" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,776.583,975.508) scale(1,1) translate(0,0)" writing-mode="lr" x="776.78" xml:space="preserve" y="980.42" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134851555330" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="260">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="260" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,776.583,998.508) scale(1,1) translate(0,0)" writing-mode="lr" x="776.78" xml:space="preserve" y="1003.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134851620866" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="269">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="269" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1065.64,396.667) scale(1,1) translate(-2.26628e-13,0)" writing-mode="lr" x="1065.84" xml:space="preserve" y="401.58" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134846509058" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="270">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="270" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1343.86,398.889) scale(1,1) translate(0,-2.58719e-13)" writing-mode="lr" x="1344.06" xml:space="preserve" y="403.8" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134855290882" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="271">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="271" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1065.64,419.222) scale(1,1) translate(-2.26628e-13,0)" writing-mode="lr" x="1065.84" xml:space="preserve" y="424.13" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134846574594" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="272">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="272" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1343.86,425.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1344.06" xml:space="preserve" y="430.8" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134855356418" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="296">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="296" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1065.64,583.333) scale(1,1) translate(1.13314e-13,0)" writing-mode="lr" x="1065.84" xml:space="preserve" y="588.24" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134846640130" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="306">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="306" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1343.86,590) scale(1,1) translate(0,0)" writing-mode="lr" x="1344.06" xml:space="preserve" y="594.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134855421954" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="315">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="315" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1065.64,609.222) scale(1,1) translate(1.13314e-13,0)" writing-mode="lr" x="1065.84" xml:space="preserve" y="614.13" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134846705666" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="319">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="319" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1343.86,614.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1344.06" xml:space="preserve" y="619.6900000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134855487490" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,847.667,122.591) scale(1,1) translate(8.91139e-14,0)" writing-mode="lr" x="847.2" xml:space="preserve" y="127.37" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134844674050" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="340">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="340" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,797.667,523) scale(1,1) translate(0,0)" writing-mode="lr" x="797.2" xml:space="preserve" y="527.78" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134849851394" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,353.339,319.143) scale(0.708333,0.665547) translate(141.118,155.36)" width="30" x="342.71" xlink:href="#State:红绿圆(方形)_0" y="309.16" zvalue="1361"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374925168641" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,353.339,319.143) scale(0.708333,0.665547) translate(141.118,155.36)" width="30" x="342.71" y="309.16"/></g>
  <g id="1035">
   <use height="30" transform="rotate(0,257.714,319.143) scale(0.708333,0.665547) translate(101.743,155.36)" width="30" x="247.09" xlink:href="#State:红绿圆(方形)_0" y="309.16" zvalue="1362"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562954494672903" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,257.714,319.143) scale(0.708333,0.665547) translate(101.743,155.36)" width="30" x="247.09" y="309.16"/></g>
  <g id="830">
   <use height="30" transform="rotate(0,324.812,133.964) scale(1.27778,1.03333) translate(-58.1114,-3.82141)" width="90" x="267.31" xlink:href="#State:全站检修_0" y="118.46" zvalue="1896"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549681782785" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,324.812,133.964) scale(1.27778,1.03333) translate(-58.1114,-3.82141)" width="90" x="267.31" y="118.46"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="48">
   <use class="kv35" height="30" transform="rotate(90,1145.79,159.104) scale(-1.64694,-1.37245) translate(-1835.03,-269.444)" width="20" x="1129.320114894484" xlink:href="#EnergyConsumer:站用变无融断_0" y="138.5172757000018" zvalue="1440"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454320783362" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1145.79,159.104) scale(-1.64694,-1.37245) translate(-1835.03,-269.444)" width="20" x="1129.320114894484" y="138.5172757000018"/></g>
  <g id="381">
   <use class="kv10" height="30" transform="rotate(0,932.904,886) scale(0.416667,-0.666667) translate(1302.57,-2220)" width="12" x="930.4043632062665" xlink:href="#EnergyConsumer:负荷_0" y="876" zvalue="1502"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454321045506" ObjectName="10kV曼拉线"/>
   <cge:TPSR_Ref TObjectID="6192454321045506"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,932.904,886) scale(0.416667,-0.666667) translate(1302.57,-2220)" width="12" x="930.4043632062665" y="876"/></g>
  <g id="146">
   <use class="kv10" height="30" transform="rotate(0,1062.9,886) scale(0.416667,-0.666667) translate(1484.57,-2220)" width="12" x="1060.404363206266" xlink:href="#EnergyConsumer:负荷_0" y="876" zvalue="1701"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454322946050" ObjectName="10kV拉影街面线"/>
   <cge:TPSR_Ref TObjectID="6192454322946050"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1062.9,886) scale(0.416667,-0.666667) translate(1484.57,-2220)" width="12" x="1060.404363206266" y="876"/></g>
  <g id="161">
   <use class="kv10" height="30" transform="rotate(0,1192.9,886) scale(0.416667,-0.666667) translate(1666.57,-2220)" width="12" x="1190.404363206266" xlink:href="#EnergyConsumer:负荷_0" y="876" zvalue="1714"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454323142658" ObjectName="10kV迈扎央线"/>
   <cge:TPSR_Ref TObjectID="6192454323142658"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1192.9,886) scale(0.416667,-0.666667) translate(1666.57,-2220)" width="12" x="1190.404363206266" y="876"/></g>
  <g id="177">
   <use class="kv10" height="30" transform="rotate(0,1322.9,886) scale(0.416667,-0.666667) translate(1848.57,-2220)" width="12" x="1320.404363206266" xlink:href="#EnergyConsumer:负荷_0" y="876" zvalue="1727"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454323339266" ObjectName="10kV洋人街老迈扎央线"/>
   <cge:TPSR_Ref TObjectID="6192454323339266"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1322.9,886) scale(0.416667,-0.666667) translate(1848.57,-2220)" width="12" x="1320.404363206266" y="876"/></g>
  <g id="221">
   <use class="kv10" height="30" transform="rotate(0,1452.56,883.778) scale(0.416667,-0.666667) translate(2030.08,-2214.44)" width="12" x="1450.058112735427" xlink:href="#EnergyConsumer:负荷_0" y="873.7777777777778" zvalue="1751"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454323929090" ObjectName="10kV5S备用线"/>
   <cge:TPSR_Ref TObjectID="6192454323929090"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1452.56,883.778) scale(0.416667,-0.666667) translate(2030.08,-2214.44)" width="12" x="1450.058112735427" y="873.7777777777778"/></g>
  <g id="231">
   <use class="kv10" height="30" transform="rotate(0,1582.56,883.778) scale(0.416667,-0.666667) translate(2212.08,-2214.44)" width="12" x="1580.058112735427" xlink:href="#EnergyConsumer:负荷_0" y="873.7777777777778" zvalue="1769"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454323994626" ObjectName="10kV6S备用线"/>
   <cge:TPSR_Ref TObjectID="6192454323994626"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1582.56,883.778) scale(0.416667,-0.666667) translate(2212.08,-2214.44)" width="12" x="1580.058112735427" y="873.7777777777778"/></g>
 </g>
</svg>