<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549582495746" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_0" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.66666666666666" x2="27.83333333333334" y1="6.416666666666666" y2="22.08333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_1" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.5" x2="28.5" y1="3.833333333333332" y2="22"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_2" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.16666666666667" x2="35.5" y1="3.083333333333332" y2="24.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="35.5" x2="21.16666666666667" y1="3.166666666666664" y2="24.83333333333334"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:RT1122_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="22.91666666666667" y2="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="18.16666666666666" y2="20.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="18.16666666666666" y2="20.16666666666666"/>
   <path d="M 13 11 L 17 11 L 15 8 L 13 11 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="15.41666666666666" y2="18.16666666666666"/>
   <ellipse cx="14.95" cy="18.02" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.95" cy="9.92" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000004" y2="0.9166666666666679"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.89694619969018" y2="15.89694619969018"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.73157590710537" y2="17.73157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.48348701738202" y2="19.48348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.05" x2="5.05" y1="3.566666666666666" y2="15.81666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="4.416666666666668" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.249999999999998" y2="20.16666666666667"/>
  </symbol>
  <symbol id="Accessory:四卷带壁雷器母线PT_0" viewBox="0,0,40,35">
   <use terminal-index="0" type="0" x="22.10905664884498" xlink:href="#terminal" y="34.51612485684674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="28.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,30,14.36) scale(1,-1) translate(0,-930.43)" width="7" x="26.5" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="16.27740325661302" y2="15.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="7.166666666666671" y1="16.27740325661302" y2="12.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="12.27740325661302" y2="13.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="30.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="22" y1="28.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="14" y1="21.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="30" y1="28.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="30" y1="28.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.92943360505483" x2="29.92943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="13.81" cy="17.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.95921744067709" x2="27.68240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.12588410734375" x2="28.51573492932657" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="30.54255077401042" x2="29.09906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599325" x2="16.18240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="13.78240159599324" y1="15.23154965466559" y2="17.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="11.38240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <ellipse cx="20.23" cy="13.99" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.48" cy="10.41" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="17.79906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="20.19906826265991" y1="11.73154965466559" y2="14.2066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="22.59906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="8.148216321332258" y2="10.62329629003646"/>
   <ellipse cx="6.64" cy="14.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT1111_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="33.75" xlink:href="#terminal" y="11.95"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="27.02377700368697" x2="19.18246971500333" y1="12.01581866675157" y2="12.01581866675157"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.4317302644139" x2="33.73333333333333" y1="12.01581866675157" y2="12.01581866675157"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.30947355115262" x2="23.30947355115262" y1="12.14512912951974" y2="19.34675167759026"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.21102695297582" x2="12.70307369224894" y1="12.09628971045682" y2="12.09628971045682"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.22373975336484" x2="19.22373975336484" y1="12.09628971045682" y2="12.09628971045682"/>
   <ellipse cx="23.29" cy="23.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="19.22373975336483" x2="19.22373975336483" y1="8.716505874834564" y2="15.55654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.36665011096792" x2="30.36665011096792" y1="12.09628971045682" y2="12.09628971045682"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="30.3666501109679" x2="30.3666501109679" y1="8.716505874834564" y2="15.55654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="16.12848687625285" x2="16.12848687625285" y1="8.716505874834558" y2="15.55654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="27.27139723385593" x2="27.27139723385593" y1="8.716505874834564" y2="15.55654458978453"/>
   <ellipse cx="25.79" cy="27.85" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.79" cy="27.85" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.96296296296296" x2="8.779897241477752" y1="12.08795620351516" y2="12.08795620351516"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="8.711988705963254" x2="8.711988705963254" y1="14.28440670580408" y2="10.00759086112023"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="7.461988705963254" x2="7.461988705963254" y1="13.53440670580408" y2="10.9242575277869"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.961988705963256" x2="5.961988705963256" y1="13.03440670580408" y2="11.59092419445357"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Accessory:空挂线路_0" viewBox="0,0,11,13">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="-0.0833333333333357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="12.91666666666667" y2="0.2500000000000009"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:设备233_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.097643878606505" xlink:href="#terminal" y="23.59422303238503"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="4" y1="2" y2="4"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="8" y1="2" y2="4"/>
   <rect fill-opacity="0" height="12.18" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.04,12.51) scale(1,1) translate(0,0)" width="7.42" x="2.33" y="6.42"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.5" x2="8.611111111111114" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.5" x2="8.611111111111114" y1="22.41666666666666" y2="22.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="2.166666666666664" y2="6.599999999999997"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:PT6_0" viewBox="0,0,15,20">
   <use terminal-index="0" type="0" x="7.560000000000002" xlink:href="#terminal" y="0.42361930658363"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.160000000000005" x2="8.559999999999999" y1="15.48035578286757" y2="14.24281579851547"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.060000000000002" x2="8.459999999999996" y1="16.09912577504353" y2="17.33666575939564"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.459999999999999" x2="5.060000000000007" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000005" x2="9.859999999999998" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.859999999999999" x2="8.859999999999999" y1="14.65532912663279" y2="17.13040909533699"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000003" x2="7.460000000000003" y1="2.279929283111803" y2="4.755009251816007"/>
   <ellipse cx="7.5" cy="6.51" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.7" cy="13.44" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="Accessory:PT1515_0" viewBox="0,0,15,23">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="0.2666666666666639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="3.916666666666664" y2="0.2499999999999982"/>
   <ellipse cx="7.67" cy="16.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.57" cy="8.85" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.583333333333334" x2="7.583333333333334" y1="14.63888888888889" y2="16.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333334" x2="7.583333333333334" y1="19.25" y2="16.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333334" x2="7.583333333333334" y1="14.63888888888889" y2="16.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.749999999999996" x2="9.694444444444443" y1="9.549457994579948" y2="9.549457994579948"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.750000000000003" x2="7.064814814814818" y1="9.549457994579944" y2="7.083333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.694444444444443" x2="8.379629629629626" y1="9.549457994579944" y2="7.083333333333332"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_0" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.05" x2="7.05" y1="1.166666666666664" y2="26.33333333333334"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="10.66666666666667" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="26.5417774506975" y2="23.30486537657277"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="26.5417774506975" y2="23.30486537657277"/>
   <rect fill-opacity="0" height="6.25" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,8.63) scale(1,1) translate(0,0)" width="2.83" x="5.58" y="5.5"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_1" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.05" x2="7.05" y1="2.833333333333334" y2="14.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="2.617622152595477" y2="5.500000000000003"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="10.66666666666666" y1="2.617622152595477" y2="5.500000000000003"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="10.66666666666667" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="24.65357874079139" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="26.5417774506975" y2="23.30486537657277"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="24.65357874079139" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="26.5417774506975" y2="23.30486537657277"/>
   <rect fill-opacity="0" height="6.25" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,8.63) scale(1,1) translate(0,0)" width="2.83" x="5.58" y="5.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="7.049999999999999" y1="16" y2="18.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.133333333333333" x2="7.133333333333333" y1="18.16666666666667" y2="24.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_2" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.33333333333333" x2="3.333333333333333" y1="9.166666666666668" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.666666666666667" x2="10.83333333333333" y1="9.124593892873616" y2="18.41666666666667"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="12.5" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="26.56537356321841" y2="21.7550287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="26.56537356321841" y2="21.7550287356322"/>
  </symbol>
  <symbol id="Accessory:避雷器PT2_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15.03333333333333" xlink:href="#terminal" y="6.499999999999995"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.5" x2="8.5" y1="13.75" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="13.75" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="20" y1="6.500000000000004" y2="11.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.983333333333334" x2="20.03333333333333" y1="6.5" y2="6.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="14.25" y2="14.25"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,14.75) scale(1,1) translate(0,0)" width="4" x="8" y="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.5" x2="10.5" y1="22.25" y2="22.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="21.25" y2="21.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="20.16666666666667" y2="20.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="16.25" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="18.25" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="15.25" y2="15.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="6.5" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <ellipse cx="19.94" cy="14.01" fill-opacity="0" rx="2.9" ry="2.93" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.96" cy="17.99" fill-opacity="0" rx="2.9" ry="2.93" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="23.85" cy="16.02" fill-opacity="0" rx="2.9" ry="2.93" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.75143596735765" x2="20.00179595652019" y1="14.29408028774067" y2="13.58359976663723"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.23218974684688" x2="19.99892763614873" y1="14.2775979716802" y2="13.58359976663723"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.99998084753513" x2="19.99998084753513" y1="13.58534069714021" y2="12.64584868169305"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.0677745356137" x2="25.03146295417303" y1="16.00139048345364" y2="16.88180390923548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.06634037542797" x2="23.16916113548238" y1="16.00283749062492" y2="16.89828622529594"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.06764008309628" x2="24.06764008309628" y1="15.15116072288544" y2="16.00139048345364"/>
   <path d="M 19.9673 17.5905 L 18.9708 19.1728 L 20.9475 19.1728 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV挖苦河三级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="274.75" x="45.86" xlink:href="logo.png" y="39.71"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,183.232,69.7143) scale(1,1) translate(0,0)" writing-mode="lr" x="183.23" xml:space="preserve" y="73.20999999999999" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,179.107,69.4046) scale(1,1) translate(0,0)" writing-mode="lr" x="179.11" xml:space="preserve" y="78.40000000000001" zvalue="3">110kV挖苦河三级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="231" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,87.125,220) scale(1,1) translate(0,0)" width="72.88" x="50.69" y="208" zvalue="351"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,87.125,220) scale(1,1) translate(0,0)" writing-mode="lr" x="87.13" xml:space="preserve" y="224.5" zvalue="351">信号一览</text>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="379.857142857143" x2="379.857142857143" y1="7.714285714285779" y2="1037.714285714286" zvalue="4"/>
  <line fill="none" id="28" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.857142857143458" x2="372.857142857143" y1="143.5847783283682" y2="143.5847783283682" zvalue="6"/>
  <line fill="none" id="26" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.85714285714334" x2="381.8571428571429" y1="515.5847783283682" y2="515.5847783283682" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.857142857143003" x2="95.857142857143" y1="928.7142857142858" y2="928.7142857142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.857142857143003" x2="95.857142857143" y1="967.8775857142857" y2="967.8775857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.857142857143003" x2="5.857142857143003" y1="928.7142857142858" y2="967.8775857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.857142857143" x2="95.857142857143" y1="928.7142857142858" y2="967.8775857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.857142857143" x2="365.857142857143" y1="928.7142857142858" y2="928.7142857142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.857142857143" x2="365.857142857143" y1="967.8775857142857" y2="967.8775857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.857142857143" x2="95.857142857143" y1="928.7142857142858" y2="967.8775857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="365.857142857143" x2="365.857142857143" y1="928.7142857142858" y2="967.8775857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.857142857143003" x2="95.857142857143" y1="967.8775557142858" y2="967.8775557142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.857142857143003" x2="95.857142857143" y1="995.7959557142858" y2="995.7959557142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.857142857143003" x2="5.857142857143003" y1="967.8775557142858" y2="995.7959557142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.857142857143" x2="95.857142857143" y1="967.8775557142858" y2="995.7959557142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.857142857143" x2="185.857142857143" y1="967.8775557142858" y2="967.8775557142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.857142857143" x2="185.857142857143" y1="995.7959557142858" y2="995.7959557142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.857142857143" x2="95.857142857143" y1="967.8775557142858" y2="995.7959557142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.857142857143" x2="185.857142857143" y1="967.8775557142858" y2="995.7959557142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.8571428571431" x2="275.8571428571431" y1="967.8775557142858" y2="967.8775557142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.8571428571431" x2="275.8571428571431" y1="995.7959557142858" y2="995.7959557142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.8571428571431" x2="185.8571428571431" y1="967.8775557142858" y2="995.7959557142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.8571428571431" x2="275.8571428571431" y1="967.8775557142858" y2="995.7959557142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.857142857143" x2="365.857142857143" y1="967.8775557142858" y2="967.8775557142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.857142857143" x2="365.857142857143" y1="995.7959557142858" y2="995.7959557142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.857142857143" x2="275.857142857143" y1="967.8775557142858" y2="995.7959557142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="365.857142857143" x2="365.857142857143" y1="967.8775557142858" y2="995.7959557142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.857142857143003" x2="95.857142857143" y1="995.7958857142858" y2="995.7958857142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.857142857143003" x2="95.857142857143" y1="1023.714285714286" y2="1023.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.857142857143003" x2="5.857142857143003" y1="995.7958857142858" y2="1023.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.857142857143" x2="95.857142857143" y1="995.7958857142858" y2="1023.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.857142857143" x2="185.857142857143" y1="995.7958857142858" y2="995.7958857142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.857142857143" x2="185.857142857143" y1="1023.714285714286" y2="1023.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.857142857143" x2="95.857142857143" y1="995.7958857142858" y2="1023.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.857142857143" x2="185.857142857143" y1="995.7958857142858" y2="1023.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.8571428571431" x2="275.8571428571431" y1="995.7958857142858" y2="995.7958857142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.8571428571431" x2="275.8571428571431" y1="1023.714285714286" y2="1023.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.8571428571431" x2="185.8571428571431" y1="995.7958857142858" y2="1023.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.8571428571431" x2="275.8571428571431" y1="995.7958857142858" y2="1023.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.857142857143" x2="365.857142857143" y1="995.7958857142858" y2="995.7958857142858"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.857142857143" x2="365.857142857143" y1="1023.714285714286" y2="1023.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.857142857143" x2="275.857142857143" y1="995.7958857142858" y2="1023.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="365.857142857143" x2="365.857142857143" y1="995.7958857142858" y2="1023.714285714286"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50.8571,948.714) scale(1,1) translate(0,0)" writing-mode="lr" x="50.86" xml:space="preserve" y="954.71" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.8571,982.714) scale(1,1) translate(0,0)" writing-mode="lr" x="47.86" xml:space="preserve" y="988.71" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229.857,982.714) scale(1,1) translate(0,0)" writing-mode="lr" x="229.86" xml:space="preserve" y="988.71" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46.8571,1010.71) scale(1,1) translate(0,0)" writing-mode="lr" x="46.86" xml:space="preserve" y="1016.71" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.857,1010.71) scale(1,1) translate(0,0)" writing-mode="lr" x="228.86" xml:space="preserve" y="1016.71" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,71.3571,643.214) scale(1,1) translate(0,-2.77207e-13)" writing-mode="lr" x="71.357142857143" xml:space="preserve" y="647.7142857142858" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1700.23,637.885) scale(1,1) translate(1.10211e-12,-2.74814e-13)" writing-mode="lr" x="1700.23" xml:space="preserve" y="642.38" zvalue="35">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,836.114,957.414) scale(1,1) translate(1.78442e-13,-1.25421e-12)" writing-mode="lr" x="836.1136758572808" xml:space="preserve" y="961.9141058812409" zvalue="38">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,934.834,949.961) scale(1,1) translate(0,0)" writing-mode="lr" x="934.83" xml:space="preserve" y="954.46" zvalue="40">励磁变PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,920.905,737.378) scale(1,1) translate(0,0)" writing-mode="lr" x="920.91" xml:space="preserve" y="741.88" zvalue="42">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,810.847,783.918) scale(1,1) translate(-1.77124e-13,0)" writing-mode="lr" x="810.85" xml:space="preserve" y="788.42" zvalue="44">671</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,805.708,719.086) scale(1,1) translate(0,0)" writing-mode="lr" x="805.71" xml:space="preserve" y="723.59" zvalue="46">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,887.276,863.637) scale(1,1) translate(0,0)" writing-mode="lr" x="887.28" xml:space="preserve" y="868.14" zvalue="48">6912</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,765.373,946.042) scale(1,1) translate(0,0)" writing-mode="lr" x="765.37" xml:space="preserve" y="950.54" zvalue="55">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,730.458,868.208) scale(1,1) translate(0,0)" writing-mode="lr" x="730.46" xml:space="preserve" y="872.71" zvalue="57">6911</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,741.216,965.437) scale(1,1) translate(0,0)" writing-mode="lr" x="741.22" xml:space="preserve" y="969.9400000000001" zvalue="65">高屏切机第二轮</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1414.51,952.475) scale(1,1) translate(9.20613e-13,-1.24763e-12)" writing-mode="lr" x="1414.50761525122" xml:space="preserve" y="956.974711941847" zvalue="68">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1510.69,952.355) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.69" xml:space="preserve" y="956.86" zvalue="70">励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1495.3,736.439) scale(1,1) translate(0,0)" writing-mode="lr" x="1495.3" xml:space="preserve" y="740.9400000000001" zvalue="72">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1380.39,785.897) scale(1,1) translate(-3.03091e-13,0)" writing-mode="lr" x="1380.39" xml:space="preserve" y="790.4" zvalue="74">672</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1459.97,862.698) scale(1,1) translate(0,0)" writing-mode="lr" x="1459.97" xml:space="preserve" y="867.2" zvalue="78">6922</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1344.48,951.817) scale(1,1) translate(0,0)" writing-mode="lr" x="1344.48" xml:space="preserve" y="956.3200000000001" zvalue="85">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1300.06,867.268) scale(1,1) translate(0,0)" writing-mode="lr" x="1300.06" xml:space="preserve" y="871.77" zvalue="87">6921</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1513.07,969.355) scale(1,1) translate(0,0)" writing-mode="lr" x="1513.07" xml:space="preserve" y="973.86" zvalue="95">高压切机第一轮</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1672.82,897.013) scale(1,1) translate(0,0)" writing-mode="lr" x="1672.82" xml:space="preserve" y="901.51" zvalue="98">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1701.31,777.388) scale(1,1) translate(0,0)" writing-mode="lr" x="1701.31" xml:space="preserve" y="781.89" zvalue="100">673</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1137.17,746.987) scale(1,1) translate(0,0)" writing-mode="lr" x="1137.17" xml:space="preserve" y="751.49" zvalue="110">6901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,744.595,443.972) scale(1,1) translate(0,0)" writing-mode="lr" x="744.6" xml:space="preserve" y="448.47" zvalue="121">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,736.992,591.71) scale(1,1) translate(0,1.29101e-13)" writing-mode="lr" x="736.99" xml:space="preserve" y="596.21" zvalue="130">601</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,728.557,523.353) scale(1,1) translate(0,0)" writing-mode="lr" x="728.5599999999999" xml:space="preserve" y="527.85" zvalue="150">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,682.941,389.588) scale(1,1) translate(0,0)" writing-mode="lr" x="682.9400000000001" xml:space="preserve" y="394.09" zvalue="158">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,766.613,373.235) scale(1,1) translate(0,0)" writing-mode="lr" x="766.61" xml:space="preserve" y="377.74" zvalue="159">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1708.15,306.417) scale(1,1) translate(0,0)" writing-mode="lr" x="1708.15" xml:space="preserve" y="310.92" zvalue="161">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,797.618,214.645) scale(1,1) translate(0,0)" writing-mode="lr" x="797.62" xml:space="preserve" y="219.15" zvalue="169">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1202.42,247.551) scale(1,1) translate(0,0)" writing-mode="lr" x="1202.42" xml:space="preserve" y="252.05" zvalue="191">171</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1193.64,295.107) scale(1,1) translate(0,0)" writing-mode="lr" x="1193.64" xml:space="preserve" y="299.61" zvalue="192">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1195.31,187.107) scale(1,1) translate(0,0)" writing-mode="lr" x="1195.31" xml:space="preserve" y="191.61" zvalue="195">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1165.83,76.8846) scale(1,1) translate(0,0)" writing-mode="lr" x="1165.83" xml:space="preserve" y="81.38" zvalue="199">110kV挖苦河三级电站线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1230.14,282.551) scale(1,1) translate(0,0)" writing-mode="lr" x="1230.14" xml:space="preserve" y="287.05" zvalue="202">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1230.14,233.774) scale(1,1) translate(0,0)" writing-mode="lr" x="1230.14" xml:space="preserve" y="238.27" zvalue="204">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1230.14,179.329) scale(1,1) translate(0,0)" writing-mode="lr" x="1230.14" xml:space="preserve" y="183.83" zvalue="206">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1537.09,248.292) scale(1,1) translate(0,0)" writing-mode="lr" x="1537.09" xml:space="preserve" y="252.79" zvalue="249">172</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1528.31,295.847) scale(1,1) translate(0,0)" writing-mode="lr" x="1528.31" xml:space="preserve" y="300.35" zvalue="250">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1529.98,187.847) scale(1,1) translate(0,0)" writing-mode="lr" x="1529.98" xml:space="preserve" y="192.35" zvalue="253">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1500.5,77.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1500.5" xml:space="preserve" y="82.13" zvalue="257">110kV挖苦河二三级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1564.81,283.292) scale(1,1) translate(0,0)" writing-mode="lr" x="1564.81" xml:space="preserve" y="287.79" zvalue="260">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1564.81,234.514) scale(1,1) translate(0,0)" writing-mode="lr" x="1564.81" xml:space="preserve" y="239.01" zvalue="262">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1564.81,180.069) scale(1,1) translate(0,0)" writing-mode="lr" x="1564.81" xml:space="preserve" y="184.57" zvalue="264">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1468.13,374.325) scale(1,1) translate(0,0)" writing-mode="lr" x="1468.13" xml:space="preserve" y="378.83" zvalue="275">外接高河一级站10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1481.56,589.825) scale(1,1) translate(0,0)" writing-mode="lr" x="1481.56" xml:space="preserve" y="594.33" zvalue="277">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,823,286.999) scale(1,1) translate(0,0)" writing-mode="lr" x="823" xml:space="preserve" y="291.5" zvalue="289">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,828,181.999) scale(1,1) translate(0,0)" writing-mode="lr" x="828" xml:space="preserve" y="186.5" zvalue="291">10</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="192" y1="281.75" y2="281.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="192" y1="307.75" y2="307.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="11" y1="281.75" y2="307.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="192" y1="281.75" y2="307.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="373" y1="281.75" y2="281.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="373" y1="307.75" y2="307.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="192" y1="281.75" y2="307.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373" x2="373" y1="281.75" y2="307.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="192" y1="307.75" y2="307.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="192" y1="332" y2="332"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="11" y1="307.75" y2="332"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="192" y1="307.75" y2="332"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="373" y1="307.75" y2="307.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="373" y1="332" y2="332"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="192" y1="307.75" y2="332"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373" x2="373" y1="307.75" y2="332"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="192" y1="332" y2="332"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="192" y1="354.75" y2="354.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="11" y1="332" y2="354.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="192" y1="332" y2="354.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="373" y1="332" y2="332"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="373" y1="354.75" y2="354.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="192" y1="332" y2="354.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373" x2="373" y1="332" y2="354.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="192" y1="354.75" y2="354.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="192" y1="377.5" y2="377.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="11" y1="354.75" y2="377.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="192" y1="354.75" y2="377.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="373" y1="354.75" y2="354.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="373" y1="377.5" y2="377.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="192" y1="354.75" y2="377.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373" x2="373" y1="354.75" y2="377.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="192" y1="377.5" y2="377.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="192" y1="400.25" y2="400.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="11" y1="377.5" y2="400.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="192" y1="377.5" y2="400.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="373" y1="377.5" y2="377.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="373" y1="400.25" y2="400.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="192" y1="377.5" y2="400.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373" x2="373" y1="377.5" y2="400.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="192" y1="400.25" y2="400.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="192" y1="423" y2="423"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="11" y1="400.25" y2="423"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="192" y1="400.25" y2="423"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="373" y1="400.25" y2="400.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="373" y1="423" y2="423"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="192" y1="400.25" y2="423"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373" x2="373" y1="400.25" y2="423"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="192" y1="423" y2="423"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="192" y1="445.75" y2="445.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11" x2="11" y1="423" y2="445.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="192" y1="423" y2="445.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="373" y1="423" y2="423"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="373" y1="445.75" y2="445.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192" x2="192" y1="423" y2="445.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373" x2="373" y1="423" y2="445.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="241" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.899,220.591) scale(1,1) translate(0,0)" writing-mode="lr" x="202.9" xml:space="preserve" y="225.09" zvalue="339">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="239" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.899,220.591) scale(1,1) translate(0,0)" writing-mode="lr" x="307.9" xml:space="preserve" y="225.09" zvalue="340">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,65,294.75) scale(1,1) translate(0,0)" writing-mode="lr" x="22.5" xml:space="preserve" y="299.25" zvalue="341">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="237" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240.5,294.75) scale(1,1) translate(0,0)" writing-mode="lr" x="198" xml:space="preserve" y="299.25" zvalue="342">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.1875,368) scale(1,1) translate(0,0)" writing-mode="lr" x="63.19" xml:space="preserve" y="372.5" zvalue="343">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="235" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.1875,392) scale(1,1) translate(0,0)" writing-mode="lr" x="60.19" xml:space="preserve" y="396.5" zvalue="344">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="230" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59,320.75) scale(1,1) translate(0,0)" writing-mode="lr" x="16.5" xml:space="preserve" y="325.25" zvalue="352">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239.5,320.75) scale(1,1) translate(0,0)" writing-mode="lr" x="197" xml:space="preserve" y="325.25" zvalue="353">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="227" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.1875,414) scale(1,1) translate(0,0)" writing-mode="lr" x="60.19" xml:space="preserve" y="418.5" zvalue="356">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.188,413) scale(1,1) translate(0,0)" writing-mode="lr" x="228.19" xml:space="preserve" y="417.5" zvalue="357">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.1875,437) scale(1,1) translate(0,0)" writing-mode="lr" x="60.19" xml:space="preserve" y="441.5" zvalue="358">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59,342.5) scale(1,1) translate(0,0)" writing-mode="lr" x="16.5" xml:space="preserve" y="347" zvalue="360">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,342.75) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="347.25" zvalue="362">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="248" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,209,950) scale(1,1) translate(0,0)" writing-mode="lr" x="209" xml:space="preserve" y="956" zvalue="369">WaKuHe3-01-2022</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,841,977.5) scale(1,1) translate(0,0)" writing-mode="lr" x="841" xml:space="preserve" y="982" zvalue="383">6.3MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1416,970.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1416" xml:space="preserve" y="975" zvalue="384">11MW</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.69" y="208" zvalue="351"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="33">
   <path class="v6300" d="M 570 682.14 L 1735.71 682.14" stroke-width="6" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674235711493" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674235711493"/></metadata>
  <path d="M 570 682.14 L 1735.71 682.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv110" d="M 560.67 328.67 L 1720.67 328.67" stroke-width="6" zvalue="160"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674235777028" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674235777028"/></metadata>
  <path d="M 560.67 328.67 L 1720.67 328.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v6300" height="30" transform="rotate(0,835.158,913.228) scale(1.43307,1.43307) translate(-245.885,-269.477)" width="30" x="813.6623219195195" xlink:href="#Generator:发电机_0" y="891.7319534037476" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449610907654" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449610907654"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,835.158,913.228) scale(1.43307,1.43307) translate(-245.885,-269.477)" width="30" x="813.6623219195195" y="891.7319534037476"/></g>
  <g id="115">
   <use class="v6300" height="30" transform="rotate(0,1409.55,912.289) scale(1.43307,1.43307) translate(-419.464,-269.193)" width="30" x="1388.056261313459" xlink:href="#Generator:发电机_0" y="890.7925594643536" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449611497478" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449611497478"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1409.55,912.289) scale(1.43307,1.43307) translate(-419.464,-269.193)" width="30" x="1388.056261313459" y="890.7925594643536"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="276">
   <use class="v6300" height="20" transform="rotate(0,924.548,904.224) scale(1.5701,1.17758) translate(-331.426,-134.579)" width="15" x="912.7724992131582" xlink:href="#Accessory:PT6_0" y="892.4484859762294" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449610842118" ObjectName="#1发电机励磁变PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,924.548,904.224) scale(1.5701,1.17758) translate(-331.426,-134.579)" width="15" x="912.7724992131582" y="892.4484859762294"/></g>
  <g id="253">
   <use class="v6300" height="30" transform="rotate(0,924.655,767.678) scale(1.48718,1.44283) translate(-295.597,-228.97)" width="30" x="902.3476988612689" xlink:href="#Accessory:RT1122_0" y="746.0357210368377" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449610776582" ObjectName="#1发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,924.655,767.678) scale(1.48718,1.44283) translate(-295.597,-228.97)" width="30" x="902.3476988612689" y="746.0357210368377"/></g>
  <g id="298">
   <use class="v6300" height="30" transform="rotate(0,771.087,910.92) scale(1.3853,1.3853) translate(-208.685,-247.577)" width="30" x="750.3075552433319" xlink:href="#Accessory:PT789_0" y="890.1407936685371" zvalue="54"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449610579974" ObjectName="#1发电机机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,771.087,910.92) scale(1.3853,1.3853) translate(-208.685,-247.577)" width="30" x="750.3075552433319" y="890.1407936685371"/></g>
  <g id="71">
   <use class="v6300" height="26" transform="rotate(90,878.268,820.936) scale(-1,-1) translate(-1756.54,-1641.87)" width="12" x="872.2677350299624" xlink:href="#Accessory:避雷器1_0" y="807.9358974358972" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449610448902" ObjectName="#1发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,878.268,820.936) scale(-1,-1) translate(-1756.54,-1641.87)" width="12" x="872.2677350299624" y="807.9358974358972"/></g>
  <g id="114">
   <use class="v6300" height="23" transform="rotate(0,1499.04,905.255) scale(1.83273,1.19526) translate(-674.864,-145.637)" width="15" x="1485.290947693496" xlink:href="#Accessory:PT1515_0" y="891.5090920368355" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449611431942" ObjectName="#2发电机励磁变PT"/>
   </metadata>
  <rect fill="white" height="23" opacity="0" stroke="white" transform="rotate(0,1499.04,905.255) scale(1.83273,1.19526) translate(-674.864,-145.637)" width="15" x="1485.290947693496" y="891.5090920368355"/></g>
  <g id="113">
   <use class="v6300" height="30" transform="rotate(0,1499.05,766.739) scale(1.48718,1.44283) translate(-483.76,-228.682)" width="30" x="1476.741638255208" xlink:href="#Accessory:RT1122_0" y="745.0963270974438" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449611366406" ObjectName="#2发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1499.05,766.739) scale(1.48718,1.44283) translate(-483.76,-228.682)" width="30" x="1476.741638255208" y="745.0963270974438"/></g>
  <g id="104">
   <use class="v6300" height="30" transform="rotate(0,1344.48,910.981) scale(1.3853,1.3853) translate(-368.165,-247.594)" width="30" x="1323.701494637271" xlink:href="#Accessory:PT789_0" y="890.2013997291432" zvalue="84"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449611169798" ObjectName="#2发电机机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1344.48,910.981) scale(1.3853,1.3853) translate(-368.165,-247.594)" width="30" x="1323.701494637271" y="890.2013997291432"/></g>
  <g id="101">
   <use class="v6300" height="26" transform="rotate(90,1452.66,819.997) scale(-1,-1) translate(-2905.32,-1639.99)" width="12" x="1446.661674423902" xlink:href="#Accessory:避雷器1_0" y="806.9965034965035" zvalue="89"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449611038726" ObjectName="#2发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1452.66,819.997) scale(-1,-1) translate(-2905.32,-1639.99)" width="12" x="1446.661674423902" y="806.9965034965035"/></g>
  <g id="132">
   <use class="v6300" height="26" transform="rotate(0,1731.94,824.013) scale(1,1) translate(0,0)" width="12" x="1725.940789473684" xlink:href="#Accessory:避雷器1_0" y="811.0131578947369" zvalue="107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449611563014" ObjectName="10kVⅡ段母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1731.94,824.013) scale(1,1) translate(0,0)" width="12" x="1725.940789473684" y="811.0131578947369"/></g>
  <g id="88">
   <use class="v6300" height="30" transform="rotate(0,1107.44,822.012) scale(1.43455,1.72146) translate(-328.947,-333.683)" width="30" x="1085.922966288384" xlink:href="#Accessory:避雷器PT2_0" y="796.1904761904757" zvalue="116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449611825158" ObjectName="6901三卷PT8"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1107.44,822.012) scale(1.43455,1.72146) translate(-328.947,-333.683)" width="30" x="1085.922966288384" y="796.1904761904757"/></g>
  <g id="191">
   <use class="kv110" height="35" transform="rotate(0,773.155,114.06) scale(1.2292,1.2292) translate(-139.58,-17.257)" width="40" x="748.5714285714286" xlink:href="#Accessory:四卷带壁雷器母线PT_0" y="92.54945054945051" zvalue="242"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449612939270" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,773.155,114.06) scale(1.2292,1.2292) translate(-139.58,-17.257)" width="40" x="748.5714285714286" y="92.54945054945051"/></g>
  <g id="194">
   <use class="kv110" height="26" transform="rotate(90,1119.57,142) scale(0.714286,0.714286) translate(446.114,53.0857)" width="12" x="1115.285714285714" xlink:href="#Accessory:避雷器1_0" y="132.7142857142858" zvalue="243"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449613004806" ObjectName="110kV挖苦河三级电站线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1119.57,142) scale(0.714286,0.714286) translate(446.114,53.0857)" width="12" x="1115.285714285714" y="132.7142857142858"/></g>
  <g id="196">
   <use class="kv110" height="40" transform="rotate(0,1121.75,165.333) scale(0.666667,0.666667) translate(554.208,76)" width="40" x="1108.416666666667" xlink:href="#Accessory:PT1111_0" y="152.0000000000001" zvalue="244"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449613070342" ObjectName="110kV挖苦河三级电站线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1121.75,165.333) scale(0.666667,0.666667) translate(554.208,76)" width="40" x="1108.416666666667" y="152.0000000000001"/></g>
  <g id="208">
   <use class="kv110" height="26" transform="rotate(90,1454.24,142.74) scale(0.714286,0.714286) translate(579.981,53.3819)" width="12" x="1449.952825892992" xlink:href="#Accessory:避雷器1_0" y="133.4546703296705" zvalue="269"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449613201414" ObjectName="110kV挖苦河二三级线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1454.24,142.74) scale(0.714286,0.714286) translate(579.981,53.3819)" width="12" x="1449.952825892992" y="133.4546703296705"/></g>
  <g id="207">
   <use class="kv110" height="40" transform="rotate(0,1456.42,166.074) scale(0.666667,0.666667) translate(721.542,76.3702)" width="40" x="1443.083778273944" xlink:href="#Accessory:PT1111_0" y="152.7403846153846" zvalue="270"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449613135878" ObjectName="110kV挖苦河二三级线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1456.42,166.074) scale(0.666667,0.666667) translate(721.542,76.3702)" width="40" x="1443.083778273944" y="152.7403846153846"/></g>
  <g id="187">
   <use class="kv10" height="13" transform="rotate(0,1479.01,408.717) scale(2.28788,-2.23373) translate(-825.474,-583.673)" width="11" x="1466.430555555555" xlink:href="#Accessory:空挂线路_0" y="394.1976495726497" zvalue="274"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449613922310" ObjectName="外接高河一级站10kV母线"/>
   </metadata>
  <rect fill="white" height="13" opacity="0" stroke="white" transform="rotate(0,1479.01,408.717) scale(2.28788,-2.23373) translate(-825.474,-583.673)" width="11" x="1466.430555555555" y="394.1976495726497"/></g>
  <g id="25">
   <use class="v6300" height="26" transform="rotate(0,772,789) scale(1,1) translate(0,0)" width="12" x="766" xlink:href="#Accessory:设备233_0" y="776" zvalue="315"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450103410693" ObjectName="#1发电机小电阻电容接地"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,772,789) scale(1,1) translate(0,0)" width="12" x="766" y="776"/></g>
  <g id="6">
   <use class="v6300" height="26" transform="rotate(0,1345,789) scale(1,1) translate(0,0)" width="12" x="1339" xlink:href="#Accessory:设备233_0" y="776" zvalue="317"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450103345157" ObjectName="#2发电机小电阻电容接地"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1345,789) scale(1,1) translate(0,0)" width="12" x="1339" y="776"/></g>
 </g>
 <g id="BreakerClass">
  <g id="82">
   <use class="v6300" height="20" transform="rotate(0,835.052,785.791) scale(1.59229,1.43307) translate(-307.657,-233.132)" width="10" x="827.0906730926959" xlink:href="#Breaker:小车断路器_0" y="771.4606342005151" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924481187845" ObjectName="#1发电机671断路器"/>
   <cge:TPSR_Ref TObjectID="6473924481187845"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,835.052,785.791) scale(1.59229,1.43307) translate(-307.657,-233.132)" width="10" x="827.0906730926959" y="771.4606342005151"/></g>
  <g id="112">
   <use class="v6300" height="20" transform="rotate(0,1409.45,784.852) scale(1.59229,1.43307) translate(-521.318,-232.848)" width="10" x="1401.484612486636" xlink:href="#Breaker:小车断路器_0" y="770.5212402611212" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924481253381" ObjectName="#2发电机672断路器"/>
   <cge:TPSR_Ref TObjectID="6473924481253381"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1409.45,784.852) scale(1.59229,1.43307) translate(-521.318,-232.848)" width="10" x="1401.484612486636" y="770.5212402611212"/></g>
  <g id="195">
   <use class="v6300" height="20" transform="rotate(0,1676.98,778.388) scale(1.59229,1.43307) translate(-620.832,-230.894)" width="10" x="1669.013727536417" xlink:href="#Breaker:小车断路器_0" y="764.0571254285852" zvalue="99"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924481318917" ObjectName="#1站用变673断路器"/>
   <cge:TPSR_Ref TObjectID="6473924481318917"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1676.98,778.388) scale(1.59229,1.43307) translate(-620.832,-230.894)" width="10" x="1669.013727536417" y="764.0571254285852"/></g>
  <g id="267">
   <use class="kv110" height="20" transform="rotate(0,775.817,444.972) scale(1.22222,1.11111) translate(-139.947,-43.3861)" width="10" x="769.7063526104371" xlink:href="#Breaker:开关_0" y="433.8611325158013" zvalue="120"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924481449989" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473924481449989"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,775.817,444.972) scale(1.22222,1.11111) translate(-139.947,-43.3861)" width="10" x="769.7063526104371" y="433.8611325158013"/></g>
  <g id="249">
   <use class="v6300" height="20" transform="rotate(0,774.172,590.375) scale(1.59229,1.43307) translate(-285.012,-174.078)" width="10" x="766.2107960440205" xlink:href="#Breaker:小车断路器_0" y="576.0439889385385" zvalue="129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924481384453" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924481384453"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,774.172,590.375) scale(1.59229,1.43307) translate(-285.012,-174.078)" width="10" x="766.2107960440205" y="576.0439889385385"/></g>
  <g id="162">
   <use class="kv110" height="20" transform="rotate(0,1181.09,248.551) scale(1.22222,1.11111) translate(-213.632,-23.744)" width="10" x="1174.976495726496" xlink:href="#Breaker:开关_0" y="237.4401707282432" zvalue="189"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924481515525" ObjectName="110kV挖苦河三级电站线171断路器"/>
   <cge:TPSR_Ref TObjectID="6473924481515525"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1181.09,248.551) scale(1.22222,1.11111) translate(-213.632,-23.744)" width="10" x="1174.976495726496" y="237.4401707282432"/></g>
  <g id="223">
   <use class="kv110" height="20" transform="rotate(0,1515.75,249.292) scale(1.22222,1.11111) translate(-274.481,-23.8181)" width="10" x="1509.643607333773" xlink:href="#Breaker:开关_0" y="238.1805553436279" zvalue="247"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924481581061" ObjectName="110kV挖苦河二三级线172断路器"/>
   <cge:TPSR_Ref TObjectID="6473924481581061"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1515.75,249.292) scale(1.22222,1.11111) translate(-274.481,-23.8181)" width="10" x="1509.643607333773" y="238.1805553436279"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="81">
   <use class="v6300" height="25" transform="rotate(0,834.934,717.734) scale(0.353843,0.934146) translate(1510.14,49.7744)" width="45" x="826.9726687508099" xlink:href="#Disconnector:特殊刀闸_0" y="706.0568896056329" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449610711046" ObjectName="#1发电机6711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449610711046"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,834.934,717.734) scale(0.353843,0.934146) translate(1510.14,49.7744)" width="45" x="826.9726687508099" y="706.0568896056329"/></g>
  <g id="242">
   <use class="v6300" height="27" transform="rotate(0,924.625,863.823) scale(1.13735,0.86495) translate(-110.702,133.051)" width="14" x="916.66329709128" xlink:href="#Disconnector:带融断手车刀闸_0" y="852.1463221568496" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449610645510" ObjectName="#1发电机6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449610645510"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,924.625,863.823) scale(1.13735,0.86495) translate(-110.702,133.051)" width="14" x="916.66329709128" y="852.1463221568496"/></g>
  <g id="301">
   <use class="v6300" height="27" transform="rotate(0,770.987,866.9) scale(1.13735,0.86495) translate(-92.1473,133.531)" width="14" x="763.0256409522816" xlink:href="#Disconnector:带融断手车刀闸_0" y="855.2232452337727" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449610514438" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449610514438"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,770.987,866.9) scale(1.13735,0.86495) translate(-92.1473,133.531)" width="14" x="763.0256409522816" y="855.2232452337727"/></g>
  <g id="110">
   <use class="v6300" height="27" transform="rotate(0,1499.02,862.884) scale(1.13735,0.86495) translate(-180.069,132.904)" width="14" x="1491.05723648522" xlink:href="#Disconnector:带融断手车刀闸_0" y="851.2069282174557" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449611235334" ObjectName="#2发电机6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449611235334"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1499.02,862.884) scale(1.13735,0.86495) translate(-180.069,132.904)" width="14" x="1491.05723648522" y="851.2069282174557"/></g>
  <g id="103">
   <use class="v6300" height="27" transform="rotate(0,1345.38,865.961) scale(1.13735,0.86495) translate(-161.514,133.384)" width="14" x="1337.419580346221" xlink:href="#Disconnector:带融断手车刀闸_0" y="854.2838512943788" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449611104262" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449611104262"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1345.38,865.961) scale(1.13735,0.86495) translate(-161.514,133.384)" width="14" x="1337.419580346221" y="854.2838512943788"/></g>
  <g id="338">
   <use class="v6300" height="27" transform="rotate(0,1107.51,747.987) scale(-1.19048,-0.90535) translate(-2036.48,-1575.45)" width="14" x="1099.174276260702" xlink:href="#Disconnector:带融断手车刀闸_0" y="735.7646198830407" zvalue="109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449611759622" ObjectName="6.3kV母线电压互感器6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449611759622"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1107.51,747.987) scale(-1.19048,-0.90535) translate(-2036.48,-1575.45)" width="14" x="1099.174276260702" y="735.7646198830407"/></g>
  <g id="133">
   <use class="kv110" height="30" transform="rotate(0,775.231,369.118) scale(0.764706,0.772549) translate(236.768,105.262)" width="15" x="769.4953513705024" xlink:href="#Disconnector:刀闸_0" y="357.529411764706" zvalue="158"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449612218373" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449612218373"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,775.231,369.118) scale(0.764706,0.772549) translate(236.768,105.262)" width="15" x="769.4953513705024" y="357.529411764706"/></g>
  <g id="271">
   <use class="kv110" height="30" transform="rotate(0,775.395,215.645) scale(-1.11111,-0.814815) translate(-1472.42,-483.079)" width="15" x="767.0619658384567" xlink:href="#Disconnector:刀闸_0" y="203.4230767111493" zvalue="167"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449612283909" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449612283909"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,775.395,215.645) scale(-1.11111,-0.814815) translate(-1472.42,-483.079)" width="15" x="767.0619658384567" y="203.4230767111493"/></g>
  <g id="161">
   <use class="kv110" height="30" transform="rotate(0,1181.09,296.107) scale(-1.11111,-0.814815) translate(-2243.23,-662.288)" width="15" x="1172.754273504273" xlink:href="#Disconnector:刀闸_0" y="283.8846287360558" zvalue="190"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449612873734" ObjectName="110kV挖苦河三级电站线1711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449612873734"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1181.09,296.107) scale(-1.11111,-0.814815) translate(-2243.23,-662.288)" width="15" x="1172.754273504273" y="283.8846287360558"/></g>
  <g id="160">
   <use class="kv110" height="30" transform="rotate(0,1181.09,188.107) scale(-1.11111,-0.814815) translate(-2243.23,-421.743)" width="15" x="1172.754273530765" xlink:href="#Disconnector:刀闸_0" y="175.8846151726877" zvalue="193"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449612808198" ObjectName="110kV挖苦河三级电站线1716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449612808198"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1181.09,188.107) scale(-1.11111,-0.814815) translate(-2243.23,-421.743)" width="15" x="1172.754273530765" y="175.8846151726877"/></g>
  <g id="222">
   <use class="kv110" height="30" transform="rotate(0,1515.75,296.847) scale(-1.11111,-0.814815) translate(-2879.1,-663.938)" width="15" x="1507.421385111551" xlink:href="#Disconnector:刀闸_0" y="284.6250133514404" zvalue="248"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449613791238" ObjectName="110kV挖苦河二三级线1721隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449613791238"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1515.75,296.847) scale(-1.11111,-0.814815) translate(-2879.1,-663.938)" width="15" x="1507.421385111551" y="284.6250133514404"/></g>
  <g id="221">
   <use class="kv110" height="30" transform="rotate(0,1515.75,188.847) scale(-1.11111,-0.814815) translate(-2879.1,-423.392)" width="15" x="1507.421385138042" xlink:href="#Disconnector:刀闸_0" y="176.6249997880724" zvalue="251"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449613725702" ObjectName="110kV挖苦河二三级线1726隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449613725702"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1515.75,188.847) scale(-1.11111,-0.814815) translate(-2879.1,-423.392)" width="15" x="1507.421385138042" y="176.6249997880724"/></g>
  <g id="182">
   <use class="kv10" height="30" transform="rotate(0,1478.91,471.361) scale(1.25,1.25) translate(-293.907,-90.5222)" width="15" x="1469.534722222222" xlink:href="#Disconnector:令克_0" y="452.6111111111111" zvalue="273"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449613987846" ObjectName="外接高河一级站10kV隔离变隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449613987846"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1478.91,471.361) scale(1.25,1.25) translate(-293.907,-90.5222)" width="15" x="1469.534722222222" y="452.6111111111111"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="53">
   <path class="v6300" d="M 835.05 772.54 L 834.95 728.9" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="81@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 835.05 772.54 L 834.95 728.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="v6300" d="M 835.05 798.69 L 835.05 892.09" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@1" LinkObjectIDznd="850@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 835.05 798.69 L 835.05 892.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="v6300" d="M 924.64 892.95 L 924.62 875.07" stroke-width="1" zvalue="51"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="242@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 924.64 892.95 L 924.62 875.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="v6300" d="M 924.62 852.8 L 924.62 835.52 L 835.05 835.52" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="242@0" LinkObjectIDznd="52" MaxPinNum="2"/>
   </metadata>
  <path d="M 924.62 852.8 L 924.62 835.52 L 835.05 835.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="v6300" d="M 834.95 706.76 L 834.95 682.14" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@0" LinkObjectIDznd="33@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 834.95 706.76 L 834.95 682.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="v6300" d="M 771.02 890.63 L 770.99 878.14" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@0" LinkObjectIDznd="301@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 771.02 890.63 L 770.99 878.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="v6300" d="M 865.9 820.9 L 835.16 820.9" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="52" MaxPinNum="2"/>
   </metadata>
  <path d="M 865.9 820.9 L 835.16 820.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="v6300" d="M 924.66 783.55 L 924.66 835.52" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@0" LinkObjectIDznd="50" MaxPinNum="2"/>
   </metadata>
  <path d="M 924.66 783.55 L 924.66 835.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="v6300" d="M 835.16 829.67 L 772.1 829.67 L 772.1 799.59" stroke-width="1" zvalue="63"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52" LinkObjectIDznd="25@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 835.16 829.67 L 772.1 829.67 L 772.1 799.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="v6300" d="M 772.1 826.63 L 772.1 855.87" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45" LinkObjectIDznd="301@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.1 826.63 L 772.1 855.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="v6300" d="M 1409.45 797.75 L 1409.45 891.15" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@1" LinkObjectIDznd="115@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.45 797.75 L 1409.45 891.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="v6300" d="M 1499.04 891.83 L 1499.02 874.13" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="110@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1499.04 891.83 L 1499.02 874.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="v6300" d="M 1344.41 890.69 L 1344.41 877.21" stroke-width="1" zvalue="88"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="103@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1344.41 890.69 L 1344.41 877.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="v6300" d="M 1440.3 819.96 L 1409.55 819.96" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="72" MaxPinNum="2"/>
   </metadata>
  <path d="M 1440.3 819.96 L 1409.55 819.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="v6300" d="M 1409.55 836.73 L 1345.1 836.73 L 1345.1 799.59" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.55 836.73 L 1345.1 836.73 L 1345.1 799.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="v6300" d="M 1345.1 834.93 L 1345.1 854.93" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64" LinkObjectIDznd="103@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1345.1 834.93 L 1345.1 854.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="v6300" d="M 1676.98 791.29 L 1676.98 828.41" stroke-width="1" zvalue="101"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@1" LinkObjectIDznd="205@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1676.98 791.29 L 1676.98 828.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="v6300" d="M 1731.97 811.65 L 1677.08 811.65" stroke-width="1" zvalue="106"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="80" MaxPinNum="2"/>
   </metadata>
  <path d="M 1731.97 811.65 L 1677.08 811.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="v6300" d="M 1107.49 807.38 L 1107.51 759.53" stroke-width="1" zvalue="113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@0" LinkObjectIDznd="338@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1107.49 807.38 L 1107.51 759.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="v6300" d="M 1107.51 736.22 L 1107.51 682.14" stroke-width="1" zvalue="114"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="338@1" LinkObjectIDznd="33@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1107.51 736.22 L 1107.51 682.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv110" d="M 775.88 505.39 L 836.78 505.39 L 836.78 539.87" stroke-width="1" zvalue="151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@2" LinkObjectIDznd="125@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.88 505.39 L 836.78 505.39 L 836.78 539.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv110" d="M 775.9 455.58 L 775.9 484.81" stroke-width="1" zvalue="154"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@1" LinkObjectIDznd="121@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.9 455.58 L 775.9 484.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv110" d="M 775.78 434.34 L 775.78 380.51" stroke-width="1" zvalue="155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@0" LinkObjectIDznd="133@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.78 434.34 L 775.78 380.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv110" d="M 775.78 403.87 L 718.59 403.88" stroke-width="1" zvalue="156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128" LinkObjectIDznd="130@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.78 403.87 L 718.59 403.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv110" d="M 775.3 357.91 L 775.3 328.67" stroke-width="1" zvalue="159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@0" LinkObjectIDznd="136@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.3 357.91 L 775.3 328.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv110" d="M 775.33 203.63 L 775.33 134.98" stroke-width="1" zvalue="174"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@1" LinkObjectIDznd="191@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.33 203.63 L 775.33 134.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="kv110" d="M 814.68 164.99 L 775.33 164.99" stroke-width="1" zvalue="181"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@0" LinkObjectIDznd="108" MaxPinNum="2"/>
   </metadata>
  <path d="M 814.68 164.99 L 775.33 164.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv110" d="M 809.68 269.99 L 775.3 269.99" stroke-width="1" zvalue="183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@0" LinkObjectIDznd="189" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.68 269.99 L 775.3 269.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="kv110" d="M 1180.99 307.92 L 1180.99 328.67" stroke-width="1" zvalue="194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="136@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1180.99 307.92 L 1180.99 328.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv110" d="M 1181.02 284.09 L 1181.02 259.16" stroke-width="1" zvalue="196"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@1" LinkObjectIDznd="162@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1181.02 284.09 L 1181.02 259.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv110" d="M 1181.05 237.92 L 1180.99 199.92" stroke-width="1" zvalue="197"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162@0" LinkObjectIDznd="160@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1181.05 237.92 L 1180.99 199.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv110" d="M 1181.02 176.09 L 1181.02 135.66" stroke-width="1" zvalue="200"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160@1" LinkObjectIDznd="156@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1181.02 176.09 L 1181.02 135.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv110" d="M 1218.09 164.76 L 1181.02 164.76" stroke-width="1" zvalue="207"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="155" MaxPinNum="2"/>
   </metadata>
  <path d="M 1218.09 164.76 L 1181.02 164.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv110" d="M 1218.09 219.21 L 1181.02 219.21" stroke-width="1" zvalue="208"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@0" LinkObjectIDznd="157" MaxPinNum="2"/>
   </metadata>
  <path d="M 1218.09 219.21 L 1181.02 219.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv110" d="M 1218.09 267.99 L 1181.02 267.99" stroke-width="1" zvalue="209"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@0" LinkObjectIDznd="158" MaxPinNum="2"/>
   </metadata>
  <path d="M 1218.09 267.99 L 1181.02 267.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv110" d="M 1128.4 142.02 L 1181.02 142.02" stroke-width="1" zvalue="212"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@0" LinkObjectIDznd="155" MaxPinNum="2"/>
   </metadata>
  <path d="M 1128.4 142.02 L 1181.02 142.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv110" d="M 775.3 328.67 L 775.3 227.46" stroke-width="1" zvalue="241"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136@1" LinkObjectIDznd="271@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.3 328.67 L 775.3 227.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv110" d="M 1130.92 159.97 L 1181.02 159.97" stroke-width="1" zvalue="245"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="155" MaxPinNum="2"/>
   </metadata>
  <path d="M 1130.92 159.97 L 1181.02 159.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="220">
   <path class="kv110" d="M 1515.66 308.67 L 1515.66 328.67" stroke-width="1" zvalue="252"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="222@0" LinkObjectIDznd="136@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1515.66 308.67 L 1515.66 328.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="219">
   <path class="kv110" d="M 1515.69 284.83 L 1515.69 259.9" stroke-width="1" zvalue="254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="222@1" LinkObjectIDznd="223@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1515.69 284.83 L 1515.69 259.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="kv110" d="M 1515.71 238.66 L 1515.66 200.67" stroke-width="1" zvalue="255"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@0" LinkObjectIDznd="221@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1515.71 238.66 L 1515.66 200.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv110" d="M 1515.69 176.83 L 1515.69 136.4" stroke-width="1" zvalue="258"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@1" LinkObjectIDznd="217@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1515.69 176.83 L 1515.69 136.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="kv110" d="M 1552.76 165.5 L 1515.69 165.5" stroke-width="1" zvalue="265"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="213@0" LinkObjectIDznd="216" MaxPinNum="2"/>
   </metadata>
  <path d="M 1552.76 165.5 L 1515.69 165.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv110" d="M 1552.76 219.95 L 1515.69 219.95" stroke-width="1" zvalue="266"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@0" LinkObjectIDznd="218" MaxPinNum="2"/>
   </metadata>
  <path d="M 1552.76 219.95 L 1515.69 219.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="kv110" d="M 1552.76 268.73 L 1515.69 268.73" stroke-width="1" zvalue="267"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@0" LinkObjectIDznd="219" MaxPinNum="2"/>
   </metadata>
  <path d="M 1552.76 268.73 L 1515.69 268.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv110" d="M 1463.07 142.76 L 1515.69 142.76" stroke-width="1" zvalue="268"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@0" LinkObjectIDznd="216" MaxPinNum="2"/>
   </metadata>
  <path d="M 1463.07 142.76 L 1515.69 142.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="206">
   <path class="kv110" d="M 1465.58 160.71 L 1515.69 160.71" stroke-width="1" zvalue="271"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="216" MaxPinNum="2"/>
   </metadata>
  <path d="M 1465.58 160.71 L 1515.69 160.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv10" d="M 1478.81 521.26 L 1478.81 486.67" stroke-width="1" zvalue="278"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@0" LinkObjectIDznd="182@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1478.81 521.26 L 1478.81 486.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv10" d="M 1479.01 454.8 L 1479.01 423.42" stroke-width="1" zvalue="279"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="187@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1479.01 454.8 L 1479.01 423.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="v6300" d="M 775.85 556.19 L 775.85 577.12" stroke-width="1" zvalue="282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@1" LinkObjectIDznd="249@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.85 556.19 L 775.85 577.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="v6300" d="M 1499.02 851.86 L 1499.05 782.61" stroke-width="1" zvalue="318"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="113@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1499.02 851.86 L 1499.05 782.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="v6300" d="M 1409.45 836 L 1499.04 836" stroke-width="1" zvalue="319"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72" LinkObjectIDznd="7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.45 836 L 1499.04 836" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="v6300" d="M 774.17 603.27 L 774.17 682.14" stroke-width="1" zvalue="380"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@1" LinkObjectIDznd="33@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 774.17 603.27 L 774.17 682.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="v6300" d="M 1409.45 771.6 L 1409.45 682.14" stroke-width="1" zvalue="381"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="33@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.45 771.6 L 1409.45 682.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="v6300" d="M 1676.98 682.14 L 1676.98 765.13" stroke-width="1" zvalue="382"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@4" LinkObjectIDznd="195@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1676.98 682.14 L 1676.98 765.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="205">
   <use class="v6300" height="30" transform="rotate(0,1676.69,853.013) scale(1.69643,1.70833) translate(-678.576,-343.063)" width="28" x="1652.940789473684" xlink:href="#EnergyConsumer:站用变DY接地_0" y="827.3881578947369" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449611694086" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1676.69,853.013) scale(1.69643,1.70833) translate(-678.576,-343.063)" width="28" x="1652.940789473684" y="827.3881578947369"/></g>
  <g id="100">
   <use class="kv10" height="30" transform="rotate(0,1478.65,545.861) scale(1.69643,1.70833) translate(-597.275,-215.708)" width="28" x="1454.899700256269" xlink:href="#EnergyConsumer:站用变DY接地_0" y="520.2361111111111" zvalue="276"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449613856774" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1478.65,545.861) scale(1.69643,1.70833) translate(-597.275,-215.708)" width="28" x="1454.899700256269" y="520.2361111111111"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="121">
   <g id="1210">
    <use class="kv110" height="50" transform="rotate(0,775.851,520.431) scale(1.45098,1.45098) translate(-234.378,-150.481)" width="30" x="754.09" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="484.16" zvalue="149"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874423795716" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1211">
    <use class="v6300" height="50" transform="rotate(0,775.851,520.431) scale(1.45098,1.45098) translate(-234.378,-150.481)" width="30" x="754.09" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="484.16" zvalue="149"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874423861252" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399444070404" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399444070404"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,775.851,520.431) scale(1.45098,1.45098) translate(-234.378,-150.481)" width="30" x="754.09" y="484.16"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="125">
   <use class="kv110" height="20" transform="rotate(0,836.741,547.692) scale(0.823529,0.823529) translate(178.419,115.598)" width="10" x="832.6232114467408" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="539.456279809221" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449612021766" ObjectName="#1主变接地刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449612021766"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,836.741,547.692) scale(0.823529,0.823529) translate(178.419,115.598)" width="10" x="832.6232114467408" y="539.456279809221"/></g>
  <g id="130">
   <use class="kv110" height="20" transform="rotate(269,708.36,404.11) scale(1.07742,-1.07742) translate(-50.5123,-778.409)" width="10" x="702.9732567866679" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="393.3362396416005" zvalue="157"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449612152838" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449612152838"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(269,708.36,404.11) scale(1.07742,-1.07742) translate(-50.5123,-778.409)" width="10" x="702.9732567866679" y="393.3362396416005"/></g>
  <g id="154">
   <use class="kv110" height="30" transform="rotate(270,1228.7,267.996) scale(-0.925926,0.740741) translate(-2556.14,89.9096)" width="12" x="1223.143162393163" xlink:href="#GroundDisconnector:地刀12_0" y="256.8846204708782" zvalue="201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449612677125" ObjectName="110kV挖苦河三级电站线17117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449612677125"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1228.7,267.996) scale(-0.925926,0.740741) translate(-2556.14,89.9096)" width="12" x="1223.143162393163" y="256.8846204708782"/></g>
  <g id="153">
   <use class="kv110" height="30" transform="rotate(270,1228.7,219.218) scale(-0.925926,0.740741) translate(-2556.14,72.8374)" width="12" x="1223.143162393163" xlink:href="#GroundDisconnector:地刀12_0" y="208.10683739491" zvalue="203"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449612546054" ObjectName="110kV挖苦河三级电站线17160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449612546054"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1228.7,219.218) scale(-0.925926,0.740741) translate(-2556.14,72.8374)" width="12" x="1223.143162393163" y="208.10683739491"/></g>
  <g id="152">
   <use class="kv110" height="30" transform="rotate(270,1228.7,164.774) scale(-0.925926,0.740741) translate(-2556.14,53.7818)" width="12" x="1223.143162499126" xlink:href="#GroundDisconnector:地刀12_0" y="153.6623929504654" zvalue="205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449612414981" ObjectName="110kV挖苦河三级电站线17167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449612414981"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1228.7,164.774) scale(-0.925926,0.740741) translate(-2556.14,53.7818)" width="12" x="1223.143162499126" y="153.6623929504654"/></g>
  <g id="215">
   <use class="kv110" height="30" transform="rotate(270,1563.37,268.736) scale(-0.925926,0.740741) translate(-3252.25,90.1688)" width="12" x="1557.81027400044" xlink:href="#GroundDisconnector:地刀12_0" y="257.6250050862629" zvalue="259"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449613594630" ObjectName="110kV挖苦河二三级线17217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449613594630"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1563.37,268.736) scale(-0.925926,0.740741) translate(-3252.25,90.1688)" width="12" x="1557.81027400044" y="257.6250050862629"/></g>
  <g id="214">
   <use class="kv110" height="30" transform="rotate(270,1563.37,219.958) scale(-0.925926,0.740741) translate(-3252.25,73.0965)" width="12" x="1557.81027400044" xlink:href="#GroundDisconnector:地刀12_0" y="208.8472220102947" zvalue="261"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449613463558" ObjectName="110kV挖苦河二三级线17260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449613463558"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1563.37,219.958) scale(-0.925926,0.740741) translate(-3252.25,73.0965)" width="12" x="1557.81027400044" y="208.8472220102947"/></g>
  <g id="213">
   <use class="kv110" height="30" transform="rotate(270,1563.37,165.514) scale(-0.925926,0.740741) translate(-3252.25,54.041)" width="12" x="1557.810274106404" xlink:href="#GroundDisconnector:地刀12_0" y="154.4027775658501" zvalue="263"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449613332486" ObjectName="110kV挖苦河二三级线17267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449613332486"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1563.37,165.514) scale(-0.925926,0.740741) translate(-3252.25,54.041)" width="12" x="1557.810274106404" y="154.4027775658501"/></g>
  <g id="118">
   <use class="kv110" height="30" transform="rotate(90,824,270) scale(1,-1) translate(0,-540)" width="12" x="818" xlink:href="#GroundDisconnector:地刀12_0" y="255" zvalue="288"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449614118918" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449614118918"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,824,270) scale(1,-1) translate(0,-540)" width="12" x="818" y="255"/></g>
  <g id="123">
   <use class="kv110" height="30" transform="rotate(90,829,165) scale(1,-1) translate(0,-330)" width="12" x="823" xlink:href="#GroundDisconnector:地刀12_0" y="150" zvalue="290"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449614249990" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449614249990"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,829,165) scale(1,-1) translate(0,-330)" width="12" x="823" y="150"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="126">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="126" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1174.35,20.2756) scale(1,1) translate(-2.50767e-13,-2.57458e-14)" writing-mode="lr" x="1173.88" xml:space="preserve" y="24.94" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124388929540" ObjectName="P"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="145" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1510.13,16.7569) scale(1,1) translate(0,4.14792e-16)" writing-mode="lr" x="1509.66" xml:space="preserve" y="21.46" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124390371332" ObjectName="P"/>
   </metadata>
  </g>
  <g id="147">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="147" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1174.35,37.6645) scale(1,1) translate(0,-4.99515e-14)" writing-mode="lr" x="1173.88" xml:space="preserve" y="42.37" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124388995076" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="148" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1510.13,34.1458) scale(1,1) translate(0,-4.97442e-15)" writing-mode="lr" x="1509.66" xml:space="preserve" y="38.83" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124390436868" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="163">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="163" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1174.35,52.2756) scale(1,1) translate(0,8.95485e-14)" writing-mode="lr" x="1173.88" xml:space="preserve" y="56.97" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124389060615" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="164">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="164" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1510.13,53.7569) scale(1,1) translate(0,4.55962e-15)" writing-mode="lr" x="1509.66" xml:space="preserve" y="58.46" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124390502404" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="167">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="167" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,828.658,415.199) scale(1,1) translate(-1.73572e-13,2.234e-13)" writing-mode="lr" x="828.17" xml:space="preserve" y="419.9" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124386373636" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="168">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="168" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,828.658,437.477) scale(1,1) translate(-1.73572e-13,1.4146e-13)" writing-mode="lr" x="828.17" xml:space="preserve" y="442.18" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124386439172" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="169">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="169" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,828.658,576.131) scale(1,1) translate(-1.73572e-13,-1.25094e-13)" writing-mode="lr" x="828.17" xml:space="preserve" y="580.83" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124386504708" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="170">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="170" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,828.658,599.631) scale(1,1) translate(0,-1.30312e-13)" writing-mode="lr" x="828.17" xml:space="preserve" y="604.33" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124386570244" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="171">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="171" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,828.658,462.755) scale(1,1) translate(0,1.49879e-13)" writing-mode="lr" x="828.17" xml:space="preserve" y="467.46" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124386635780" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="172">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="172" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,828.658,623.242) scale(1,1) translate(0,-1.35555e-13)" writing-mode="lr" x="828.17" xml:space="preserve" y="627.9400000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124386963460" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="173">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="173" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,834.603,997.815) scale(1,1) translate(0,6.59049e-13)" writing-mode="lr" x="834.05" xml:space="preserve" y="1002.45" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124382375940" ObjectName="P"/>
   </metadata>
  </g>
  <g id="174">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="174" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1409.11,991.837) scale(1,1) translate(0,1.08481e-13)" writing-mode="lr" x="1408.56" xml:space="preserve" y="996.5700000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124383555588" ObjectName="P"/>
   </metadata>
  </g>
  <g id="175">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="175" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,835.603,1018.15) scale(1,1) translate(0,-6.70657e-13)" writing-mode="lr" x="835.05" xml:space="preserve" y="1022.83" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124382441476" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="176">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="176" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1409.11,1017) scale(1,1) translate(0,-2.00484e-12)" writing-mode="lr" x="1408.56" xml:space="preserve" y="1021.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124383621124" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="177">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="177" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,838.603,1037.65) scale(1,1) translate(8.72194e-14,-6.84244e-13)" writing-mode="lr" x="838.05" xml:space="preserve" y="1042.31" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124382507012" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="178">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="178" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1409.11,1041.06) scale(1,1) translate(0,1.14279e-13)" writing-mode="lr" x="1408.56" xml:space="preserve" y="1045.75" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124383686660" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="233">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="233" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,160.111,366.917) scale(1,1) translate(0,0)" writing-mode="lr" x="160.27" xml:space="preserve" y="371.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124388798468" ObjectName="F"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,160.111,294.917) scale(1,1) translate(0,0)" writing-mode="lr" x="160.27" xml:space="preserve" y="299.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124392534020" ObjectName="F"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.722,295.917) scale(1,1) translate(0,0)" writing-mode="lr" x="337.88" xml:space="preserve" y="300.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124392599556" ObjectName="F"/>
   </metadata>
  </g>
  <g id="247">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="247" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,160.111,390.139) scale(1,1) translate(0,0)" writing-mode="lr" x="160.27" xml:space="preserve" y="395.05" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124381720580" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,160.111,319.917) scale(1,1) translate(0,0)" writing-mode="lr" x="160.27" xml:space="preserve" y="324.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124392402948" ObjectName="F"/>
   </metadata>
  </g>
  <g id="228">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="228" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.722,320.917) scale(1,1) translate(0,0)" writing-mode="lr" x="337.88" xml:space="preserve" y="325.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124392468484" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,160.111,343.917) scale(1,1) translate(0,0)" writing-mode="lr" x="160.27" xml:space="preserve" y="348.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127184695301" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,340.111,342.917) scale(1,1) translate(0,0)" writing-mode="lr" x="340.27" xml:space="preserve" y="347.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127184629765" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="278">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="278" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,160.111,413.139) scale(1,1) translate(0,0)" writing-mode="lr" x="160.27" xml:space="preserve" y="418.05" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124392796164" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="280">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="280" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,160.111,433.139) scale(1,1) translate(0,0)" writing-mode="lr" x="160.27" xml:space="preserve" y="438.05" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124392861700" ObjectName="坝下水位"/>
   </metadata>
  </g>
  <g id="281">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="281" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,332.111,413.139) scale(1,1) translate(0,0)" writing-mode="lr" x="332.27" xml:space="preserve" y="418.05" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124392927236" ObjectName="雨量"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="2" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1105,868.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1104.53" xml:space="preserve" y="873.42" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124381327364" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,775.667,30.1667) scale(1,1) translate(0,-1.9688e-14)" writing-mode="lr" x="775.2" xml:space="preserve" y="34.94" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124388405252" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1105,897.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1104.53" xml:space="preserve" y="902.42" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124381392900" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="5" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,775.667,55.1667) scale(1,1) translate(0,0)" writing-mode="lr" x="775.2" xml:space="preserve" y="59.94" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124388470788" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="8" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1105,926.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1104.53" xml:space="preserve" y="931.42" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124381458436" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="9" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,775.667,80.1667) scale(1,1) translate(0,0)" writing-mode="lr" x="775.2" xml:space="preserve" y="84.94" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124388536324" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="10" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,592,713.143) scale(1,1) translate(0,0)" writing-mode="lr" x="591.53" xml:space="preserve" y="717.92" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124381589508" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="11" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,542.667,342.667) scale(1,1) translate(0,0)" writing-mode="lr" x="542.2" xml:space="preserve" y="347.44" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124388667396" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="232">
   <use height="30" transform="rotate(0,335.173,221.107) scale(0.708333,0.665547) translate(133.637,106.095)" width="30" x="324.55" xlink:href="#State:红绿圆(方形)_0" y="211.12" zvalue="349"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374885453828" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,335.173,221.107) scale(0.708333,0.665547) translate(133.637,106.095)" width="30" x="324.55" y="211.12"/></g>
  <g id="1">
   <use height="30" transform="rotate(0,242.048,221.107) scale(0.708333,0.665547) translate(95.2917,106.095)" width="30" x="231.42" xlink:href="#State:红绿圆(方形)_0" y="211.12" zvalue="371"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950129909768" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,242.048,221.107) scale(0.708333,0.665547) translate(95.2917,106.095)" width="30" x="231.42" y="211.12"/></g>
 </g>
</svg>